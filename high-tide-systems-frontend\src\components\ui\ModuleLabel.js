'use client';

import React from 'react';

/**
 * Componente de label que se adapta à cor do módulo
 * 
 * @param {Object} props - Propriedades do componente
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {string} props.htmlFor - ID do elemento associado
 * @param {React.ReactNode} props.children - Conteúdo do label
 * @param {React.ReactNode} props.icon - Ícone opcional
 * @param {boolean} props.required - Se o campo é obrigatório
 * @param {string} props.className - Classes adicionais
 */
const ModuleLabel = ({
  moduleColor = 'default',
  htmlFor,
  children,
  icon,
  required = false,
  className = '',
}) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      iconColor: 'text-gray-500 dark:text-gray-400',
    },
    people: {
      iconColor: 'text-module-people-icon dark:text-module-people-icon-dark',
    },
    scheduler: {
      iconColor: 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark',
    },
    admin: {
      iconColor: 'text-module-admin-icon dark:text-module-admin-icon-dark',
    },
    financial: {
      iconColor: 'text-module-financial-icon dark:text-module-financial-icon-dark',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;

  // Classes base para todos os labels
  const baseClasses = 'block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1';
  
  // Combinar todas as classes
  const labelClasses = `${baseClasses} ${className}`;

  return (
    <label htmlFor={htmlFor} className={labelClasses}>
      <div className="flex items-center gap-1.5">
        {icon && (
          <span className={`${colors.iconColor} w-4 h-4`}>
            {icon}
          </span>
        )}
        <span>{children}</span>
        {required && (
          <span className="text-red-500 ml-0.5">*</span>
        )}
      </div>
    </label>
  );
};

export default ModuleLabel;
