"use client";

import React, { useState, useEffect } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader from "@/components/ui/ModuleHeader";
import WorkingHoursGrid from "@/components/workingHours/workingHoursGrid";
import MultiSelect from "@/components/ui/multi-select";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import workingHoursService from "@/app/modules/scheduler/services/workingHoursService";
import { Clock, Filter, Shield } from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";
import { useToast } from "@/contexts/ToastContext"; // Importando o hook de toast
import ExportMenu from "@/components/ui/ExportMenu";

// Tutorial steps para a página de horários de trabalho
const workingHoursTutorialSteps = [
  {
    title: "Horá<PERSON>s de Trabalho",
    content: "Esta tela permite configurar os horários de disponibilidade dos profissionais para agendamentos.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Filtrar Profissionais",
    content: "Selecione um ou mais profissionais para visualizar ou configurar seus horários de trabalho.",
    selector: ".w-80",
    position: "bottom"
  },
  {
    title: "Grade de Horários",
    content: "Visualize e configure os horários de trabalho dos profissionais selecionados. Clique nas células para marcar/desmarcar horários disponíveis.",
    selector: "table",
    position: "top"
  },
  {
    title: "Legenda",
    content: "Entenda o significado das cores na grade de horários.",
    selector: ".flex.flex-wrap.gap-4",
    position: "top"
  },
  {
    title: "Salvar Alterações",
    content: "Após configurar os horários, clique em Salvar para aplicar as alterações.",
    selector: "button:contains('Salvar')",
    position: "bottom"
  }
];

const WorkingHoursPage = () => {
  const [selectedProviders, setSelectedProviders] = useState([]);
  const [providers, setProviders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedDay, setSelectedDay] = useState('1'); // Segunda por padrão

  // Function to load time grids for selected providers
  const loadTimeGridsForSelectedProviders = async (day) => {
    if (selectedProviders.length > 0) {
      try {
        setIsLoading(true);
        const data = await workingHoursService.getUsersTimeGrid(day);

        // Initialize the grids for each selected user
        const grids = {};
        const providerIds = selectedProviders.map(p => p.value);

        data.users.forEach(user => {
          if (providerIds.includes(user.userId)) {
            grids[user.userId] = [...user.timeGrid];
          }
        });

        setTimeGrids(grids);
        console.log("Time grids loaded:", grids);
      } catch (error) {
        console.error("Erro ao carregar grade de horários:", error);
        toast_error({
          title: "Erro ao carregar horários",
          message: error.message || "Ocorreu um erro ao carregar os horários"
        });
      } finally {
        setIsLoading(false);
      }
    }
  };
  const [timeGrids, setTimeGrids] = useState({});

  // Obtendo as permissões usando o hook
  const { can } = usePermissions();

  // Obtendo as funções de toast
  const { toast_error, toast_success, toast_warning } = useToast();

  // Verificando permissões específicas
  const canViewWorkingHours = can('scheduling.working-hours.view');
  const canManageWorkingHours = can('scheduling.working-hours.manage');

  useEffect(() => {
    const loadProviders = async () => {
      try {
        setIsLoading(true);
        const providersData = await appointmentService.getProviders();

        setProviders(providersData.map(provider => ({
          value: provider.id,
          label: provider.fullName
        })));
      } catch (error) {
        console.error('Erro ao carregar profissionais:', error);
        toast_error({
          title: "Erro ao carregar profissionais",
          message: error.message || "Ocorreu um erro ao carregar a lista de profissionais"
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Carrega profissionais apenas se o usuário tiver permissão para visualizar
    if (canViewWorkingHours) {
      loadProviders();
    } else {
      // Exibe toast de permissão negada
      toast_error({
        title: "Acesso negado",
        message: "Você não tem permissão para visualizar os horários de trabalho"
      });
    }
  }, [canViewWorkingHours, toast_error]);

  // Effect to reload time grids when selected day changes
  useEffect(() => {
    if (selectedProviders.length > 0) {
      loadTimeGridsForSelectedProviders(selectedDay);
    }
  }, [selectedDay]);

  const handleProviderFilterChange = async (selected) => {
    setSelectedProviders(selected);

    if (selected.length > 0) {
      await loadTimeGridsForSelectedProviders(selectedDay);
    } else {
      // Clear time grids if no providers are selected
      setTimeGrids({});
    }
  };

  // Função para exportar os horários de trabalho
  const handleExport = async (format) => {
    if (selectedProviders.length === 0) {
      toast_error({
        title: "Nenhum profissional selecionado",
        message: "Selecione pelo menos um profissional para exportar os horários"
      });
      return;
    }

    setIsExporting(true);

    // Mostrar toast informando que os dados da semana inteira estão sendo carregados
    toast_warning({
      title: "Preparando exportação",
      message: "Carregando dados da semana inteira para todos os profissionais selecionados..."
    });

    try {
      // Mapear os providers para o formato esperado pela função de exportação
      const providersForExport = selectedProviders.map(provider => {
        const providerObj = providers.find(p => p.value === provider.value);
        return {
          userId: provider.value,
          userName: provider.label || providerObj?.label || "Profissional"
        };
      });

      console.log("Profissionais para exportação:", providersForExport);

      // Exportar os dados da semana inteira
      await workingHoursService.exportWorkingHours(
        providersForExport,
        null, // Passamos null para forçar o serviço a buscar os dados atualizados
        selectedDay,
        format
      );

      toast_success({
        title: "Exportação concluída",
        message: "Os horários de trabalho da semana inteira foram exportados com sucesso"
      });
    } catch (error) {
      console.error("Erro ao exportar horários de trabalho:", error);
      toast_error({
        title: "Erro na exportação",
        message: error.message || "Ocorreu um erro ao exportar os horários de trabalho"
      });
    } finally {
      setIsExporting(false);
    }
  };

  // Se o usuário não tem permissão para visualizar, exibe mensagem de acesso negado
  if (!canViewWorkingHours) {
    return (
      <div className="bg-neutral-50 dark:bg-gray-800 p-6 rounded-lg border border-neutral-200 dark:border-gray-700 text-center">
        <Shield className="h-12 w-12 text-neutral-400 dark:text-gray-500 mx-auto mb-4" />
        <h2 className="text-lg font-medium text-neutral-700 dark:text-neutral-200 mb-2">
          Acesso Restrito
        </h2>
        <p className="text-neutral-600 dark:text-gray-300">
          Você não tem permissão para visualizar os horários de trabalho.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Título e botão de exportar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Clock size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Horários de Trabalho
        </h1>

        {/* Botão de exportar */}
        <div className="export-button">
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={false}
            underConstruction={selectedProviders.length === 0}
            className="text-purple-700 dark:text-purple-300"
          />
        </div>
      </div>

      {/* Cabeçalho da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description={canManageWorkingHours
          ? "Configure os horários de disponibilidade dos profissionais para agendamentos."
          : "Visualize os horários de disponibilidade dos profissionais para agendamentos."}
        moduleColor="scheduler"
        tutorialSteps={workingHoursTutorialSteps}
        tutorialName="working-hours-overview"
        filters={
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2 text-module-scheduler-text dark:text-gray-300">
              <Filter size={18} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
              <span className="font-medium">Filtrar por:</span>
            </div>

            <div className="w-80">
              <MultiSelect
                label=""
                value={selectedProviders}
                onChange={handleProviderFilterChange}
                options={providers}
                placeholder="Selecionar profissionais"
                loading={isLoading}
                moduleOverride="scheduler"
              />
            </div>
          </div>
        }
      />

      {/* Conteúdo principal */}
      <WorkingHoursGrid
        selectedProviders={selectedProviders}
        canManage={canManageWorkingHours}
        onDayChange={setSelectedDay}
        onTimeGridsChange={(grids) => {
          setTimeGrids(grids);
          console.log("Time grids updated:", grids);
        }}
      />

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default WorkingHoursPage;