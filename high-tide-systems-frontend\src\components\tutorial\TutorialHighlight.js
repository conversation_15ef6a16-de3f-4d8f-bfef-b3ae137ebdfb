"use client";

import React, { useState, useEffect, useRef } from 'react';

/**
 * Componente que cria um destaque visual em torno do elemento alvo para o tutorial
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.selector - Seletor CSS do elemento a ser destacado
 * @param {string} props.shape - Forma do destaque ('circle', 'rectangle', 'auto')
 * @param {number} props.padding - Espaço adicional ao redor do elemento em pixels
 * @param {boolean} props.pulsate - Se o destaque deve ter animação pulsante
 */
const TutorialHighlight = ({
  selector,
  shape = 'auto',
  padding = 10,
  pulsate = true,
  children
}) => {
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0, height: 0 });
  const [isVisible, setIsVisible] = useState(false);
  const highlightRef = useRef(null);
  const targetRef = useRef(null);

  // Calcula a posição do elemento alvo
  useEffect(() => {
    if (!selector) return;

    const calculatePosition = () => {
      // Tenta encontrar todos os elementos que correspondem ao seletor
      const elements = document.querySelectorAll(selector);

      if (!elements || elements.length === 0) {
        console.warn(`TutorialHighlight: Elemento com seletor "${selector}" não encontrado.`);
        setIsVisible(false);
        return;
      }

      console.log(`TutorialHighlight: Encontrados ${elements.length} elementos com seletor "${selector}"`);

      // Encontra o primeiro elemento visível
      let targetElement = null;
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const rect = element.getBoundingClientRect();

        // Verifica se o elemento está visível na tela
        if (rect.width > 0 && rect.height > 0 &&
            rect.top < window.innerHeight &&
            rect.left < window.innerWidth &&
            rect.bottom > 0 &&
            rect.right > 0) {

          // Verifica se o elemento não está oculto por CSS
          const style = window.getComputedStyle(element);
          if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
            targetElement = element;
            console.log(`TutorialHighlight: Elemento visível encontrado na posição ${i+1}`, rect);
            break;
          }
        }
      }

      // Se não encontrou nenhum elemento visível, usa o primeiro
      if (!targetElement && elements.length > 0) {
        targetElement = elements[0];
        console.log(`TutorialHighlight: Nenhum elemento visível encontrado, usando o primeiro elemento`);
      }

      if (!targetElement) {
        console.warn(`TutorialHighlight: Nenhum elemento visível encontrado com seletor "${selector}".`);
        setIsVisible(false);
        return;
      }

      // Armazenamos referência ao elemento para uso futuro
      targetRef.current = targetElement;

      const rect = targetElement.getBoundingClientRect();
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      // Determinar forma automática com base nas dimensões
      let finalShape = shape;
      if (shape === 'auto') {
        // Se o elemento for aproximadamente quadrado e pequeno, usar círculo
        const isSquarish = Math.abs(rect.width - rect.height) < Math.min(rect.width, rect.height) * 0.2;
        const isSmall = Math.max(rect.width, rect.height) < 100;
        finalShape = isSquarish && isSmall ? 'circle' : 'rectangle';
      }

      // Adicionar padding à posição
      let newPosition = {
        top: rect.top + scrollTop - padding,
        left: rect.left + scrollLeft - padding,
        width: rect.width + padding * 2,
        height: rect.height + padding * 2,
        shape: finalShape
      };

      setPosition(newPosition);
      setIsVisible(true);
    };

    // Calcular posição inicial
    calculatePosition();

    // Recalcular durante a rolagem da página
    const handleScroll = () => {
      requestAnimationFrame(calculatePosition);
    };

    // Recalcular em caso de redimensionamento
    window.addEventListener('resize', calculatePosition);
    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('resize', calculatePosition);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [selector, shape, padding]);

  if (!isVisible) return null;

  const borderRadius = position.shape === 'circle'
    ? '50%'
    : '8px';

  const highlightStyles = {
    position: 'absolute',
    top: `${position.top}px`,
    left: `${position.left}px`,
    width: `${position.width}px`,
    height: `${position.height}px`,
    borderRadius,
    boxShadow: '0 0 0 5000px rgba(0, 0, 0, 0.75)',
    zIndex: 9998,
    pointerEvents: 'none',
    animation: pulsate ? 'tutorial-highlight-pulse 2s infinite' : 'none',
  };

  return (
    <>
      {/* Estilo para animação */}
      <style jsx global>{`
        @keyframes tutorial-highlight-pulse {
          0% {
            box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.75), 0 0 0 4px rgba(255, 153, 51, 0.6);
          }
          50% {
            box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.75), 0 0 0 8px rgba(255, 153, 51, 0.8);
          }
          100% {
            box-shadow: 0 0 0 5000px rgba(0, 0, 0, 0.75), 0 0 0 4px rgba(255, 153, 51, 0.6);
          }
        }
      `}</style>

      {/* Elemento de destaque */}
      <div
        ref={highlightRef}
        className="tutorial-highlight"
        style={highlightStyles}
      />

      {/* Conteúdo filho (caixa de diálogo, etc.) */}
      {children}
    </>
  );
};

export default TutorialHighlight;