"use client";

import React, { useState, useEffect } from "react";
import {
  User,
  Heart,
  Mail,
  Phone,
  FileText
} from "lucide-react";
import { contactsService } from "@/app/modules/people/services/contactsService";
import MaskedInput from "@/components/common/MaskedInput";
import ModuleModal from "@/components/ui/ModuleModal";
import ModalButton from "@/components/ui/ModalButton";

const ContactFormModal = ({ isOpen, onClose, contact, personId, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: "",
    relationship: "",
    email: "",
    phone: "",
    notes: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (contact) {
      setFormData({
        name: contact.name || "",
        relationship: contact.relationship || "",
        email: contact.email || "",
        phone: contact.phone || "",
        notes: contact.notes || ""
      });
    } else {
      setFormData({
        name: "",
        relationship: "",
        email: "",
        phone: "",
        notes: ""
      });
    }
  }, [contact]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Nome é obrigatório";
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear the error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare data - remove formatting from phone number
      const cleanedData = {
        ...formData,
        phone: formData.phone ? formData.phone.replace(/\D/g, '') : ''
      };

      if (contact) {
        // Update existing contact
        await contactsService.updateContact(contact.id, cleanedData);
      } else {
        // Create new contact
        await contactsService.createContact({
          ...cleanedData,
          personId
        });
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving contact:", error);

      // Handle API validation errors
      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.path] = err.msg;
        });
        setErrors(apiErrors);
      } else {
        setErrors({
          submit: "Ocorreu um erro ao salvar o contato. Por favor, tente novamente."
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="people"
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="people"
        type="submit"
        form="contact-form"
        isLoading={isSubmitting}
      >
        {contact ? "Atualizar" : "Salvar"}
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={contact ? "Editar Contato" : "Adicionar Contato"}
      icon={<User size={22} />}
      moduleColor="people"
      size="md"
      animateExit={true}
      footer={modalFooter}
    >
      <form id="contact-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6">
          <div className="space-y-6">
            <div>
              <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4">
                <User className="w-4 h-4" />
                Informações do Contato
              </h4>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-gray-300 mb-1">
                Nome do Contato <span className="text-red-500 dark:text-red-400">*</span>
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <User size={18} className="text-neutral-400 dark:text-gray-500" />
                </div>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className={`w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 pl-10 text-sm transition-colors duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-gray-200 ${
                    errors.name
                      ? "border-red-300 dark:border-red-700 focus:ring-red-200 dark:focus:ring-red-900"
                      : ""
                  }`}
                  placeholder="Digite o nome completo"
                />
              </div>
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.name}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-gray-300 mb-1">
                Relacionamento
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Heart size={18} className="text-neutral-400 dark:text-gray-500" />
                </div>
                <input
                  type="text"
                  name="relationship"
                  value={formData.relationship}
                  onChange={handleChange}
                  className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 pl-10 text-sm transition-colors duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-gray-200"
                  placeholder="Ex: Pai, Mãe, Irmão, Amigo..."
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail size={18} className="text-neutral-400 dark:text-gray-500" />
                </div>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className={`w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 pl-10 text-sm transition-colors duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-gray-200 ${
                    errors.email
                      ? "border-red-300 dark:border-red-700 focus:ring-red-200 dark:focus:ring-red-900"
                      : ""
                  }`}
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.email}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-gray-300 mb-1">
                Telefone
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone size={18} className="text-neutral-400 dark:text-gray-500" />
                </div>
                <MaskedInput
                  type="phone"
                  value={formData.phone}
                  onChange={(e) =>
                    handleChange({
                      target: { name: "phone", value: e.target.value },
                    })
                  }
                  placeholder="(00) 00000-0000"
                  className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 pl-10 text-sm transition-colors duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-gray-200"
                  disabled={isSubmitting}
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-gray-300 mb-1">
                Observações
              </label>
              <div className="relative">
                <div className="absolute top-3 left-3 pointer-events-none">
                  <FileText size={18} className="text-neutral-400 dark:text-gray-500" />
                </div>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 pl-10 text-sm transition-colors duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-gray-200"
                  placeholder="Informações adicionais sobre este contato..."
                  rows={3}
                ></textarea>
              </div>
            </div>

            {errors.submit && (
              <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-300 rounded-lg text-sm">
                {errors.submit}
              </div>
            )}


          </div>
        </form>
    </ModuleModal>
  );
};

export default ContactFormModal;