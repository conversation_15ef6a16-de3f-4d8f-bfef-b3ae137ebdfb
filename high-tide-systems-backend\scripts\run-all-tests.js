// scripts/run-all-tests.js
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Função para encontrar todos os arquivos de teste
function findTestFiles(dir) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat && stat.isDirectory()) {
      // Recursivamente busca em subdiretórios
      results = results.concat(findTestFiles(filePath));
    } else if (file.endsWith('.js') && !file.includes('test-cache-service.js') && !file.includes('test-middleware.js')) {
      // Exclui os arquivos com erros de sintaxe
      results.push(filePath);
    }
  });
  
  return results;
}

// Função para executar um teste
function runTest(testFile) {
  return new Promise((resolve) => {
    console.log(`\n\n=== Executando teste: ${testFile} ===\n`);
    
    const testProcess = spawn('node', [testFile], { stdio: 'inherit' });
    
    testProcess.on('close', (code) => {
      if (code !== 0) {
        console.log(`\n❌ Teste falhou com código de saída ${code}: ${testFile}`);
      } else {
        console.log(`\n✅ Teste concluído com sucesso: ${testFile}`);
      }
      resolve();
    });
  });
}

// Função principal
async function runAllTests() {
  try {
    console.log('Iniciando execução de todos os testes...\n');
    
    const testsDir = path.join(__dirname, '..', 'tests');
    const testFiles = findTestFiles(testsDir);
    
    console.log(`Encontrados ${testFiles.length} arquivos de teste para executar.\n`);
    
    // Executa os testes sequencialmente
    for (const testFile of testFiles) {
      await runTest(testFile);
    }
    
    console.log('\n\n=== Todos os testes foram executados ===');
  } catch (error) {
    console.error('Erro ao executar os testes:', error);
    process.exit(1);
  }
}

// Executa a função principal
runAllTests();
