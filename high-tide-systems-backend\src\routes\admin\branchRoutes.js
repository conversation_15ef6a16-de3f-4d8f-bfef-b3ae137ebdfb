const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { BranchController, createBranchValidation } = require('../../controllers/branchController');

// All routes require authentication
router.use(authenticate);

// Branch routes
router.get('/', BranchController.list);
router.get('/:id', BranchController.get);
router.post('/', createBranchValidation, BranchController.create);
router.put('/:id', BranchController.update);
router.patch('/:id/status', BranchController.toggleStatus);
router.delete('/:id', BranchController.delete);

// Working hours routes
router.get('/:id/default-working-hours', BranchController.getDefaultWorkingHours);
router.post('/:id/apply-working-hours', BranchController.applyWorkingHoursToUsers);

module.exports = router;