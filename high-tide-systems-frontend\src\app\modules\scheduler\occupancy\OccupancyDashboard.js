"use client";

import React, { useState, useEffect } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Legend,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ComposedChart,
  Area
} from "recharts";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import {
  ModuleTable,
  ModuleSelect,
  ModuleDatePicker,
  ModuleFormGroup
} from "@/components/ui";
import ExportMenu from "@/components/ui/ExportMenu";
import {
  Users,
  Calendar,
  Clock,
  Calendar as CalendarIcon,
  TrendingUp,
  Search,
  Briefcase,
  MapPin,
  BarChart2,
  ChevronDown,
  ChevronUp,
  User,
  Filter,
  RefreshCcw,
  Loader,
  AlertTriangle
} from "lucide-react";
import { occupancyService } from '@/app/modules/scheduler/services/occupancyService';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import MultiSelect from '@/components/ui/multi-select';
import { APPOINTMENT_STATUS } from "@/app/modules/scheduler/calendar/utils/appointmentConstants";

const OccupancyDashboard = () => {
  // Estados para os dados
  const [occupancyData, setOccupancyData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isExporting, setIsExporting] = useState(false);
  const [error, setError] = useState(null);

  // Função auxiliar para criar datas seguras
  const createSafeDate = (dateValue) => {
    try {
      const date = new Date(dateValue);
      // Verificar se a data é válida
      if (isNaN(date.getTime())) {
        console.warn('Data inválida:', dateValue);
        return new Date(); // Retornar data atual se for inválida
      }
      return date;
    } catch (error) {
      console.error('Erro ao criar data:', error);
      return new Date(); // Retornar data atual em caso de erro
    }
  };

  // Função auxiliar para formatar data para input
  const formatDateForInput = (date) => {
    try {
      if (!date || isNaN(date.getTime())) return '';
      return date.toISOString().split('T')[0];
    } catch (error) {
      console.error('Erro ao formatar data para input:', error);
      return '';
    }
  };

  // Estados para filtros
  const [period, setPeriod] = useState('month');
  const [selectedDate, setSelectedDate] = useState(new Date());
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [selectedProviders, setSelectedProviders] = useState([]);
  const [selectedServiceTypes, setSelectedServiceTypes] = useState([]);
  const [selectedLocations, setSelectedLocations] = useState([]);

  // Estados para opções de filtro
  const [providers, setProviders] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [locations, setLocations] = useState([]);

  // Estado para detalhes expandidos
  const [expandedProvider, setExpandedProvider] = useState(null);
  const [expandedService, setExpandedService] = useState(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [providerDetails, setProviderDetails] = useState(null);
  const [serviceTypeComparison, setServiceTypeComparison] = useState(null);

  // Cores para gráficos
  const PROVIDER_COLORS = ["#3b82f6", "#60a5fa", "#93c5fd", "#bfdbfe", "#dbeafe"];
  const SERVICE_COLORS = ["#22c55e", "#4ade80", "#86efac", "#bbf7d0", "#dcfce7"];
  const LOCATION_COLORS = ["#f59e0b", "#fbbf24", "#fcd34d", "#fde68a", "#fef3c7"];
  const WEEKDAY_COLORS = ["#c084fc", "#a855f7", "#9333ea", "#7e22ce", "#6b21a8", "#581c87", "#4c1d95"];

  // Formatar números
  const formatNumber = (num) => {
    return num?.toLocaleString('pt-BR', { maximumFractionDigits: 1 }) || '0';
  };

  // Formatar horas
  const formatHours = (hours) => {
    return `${formatNumber(hours)}h`;
  };

  // Formatar porcentagem
  const formatPercentage = (value) => {
    return `${formatNumber(value)}%`;
  };

  // Calcular status de ocupação
  const getOccupancyStatus = (rate) => {
    if (rate >= 90) return { label: 'Crítica', color: '#ef4444', textColor: 'text-red-500 dark:text-red-400' };
    if (rate >= 75) return { label: 'Alta', color: '#f59e0b', textColor: 'text-amber-500 dark:text-amber-400' };
    if (rate >= 50) return { label: 'Média', color: '#10b981', textColor: 'text-emerald-500 dark:text-emerald-400' };
    return { label: 'Baixa', color: '#3b82f6', textColor: 'text-blue-500 dark:text-blue-400' };
  };

  // Carregar opções de filtro
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        // Carregar provedores
        const providersData = await appointmentService.getProviders();
        setProviders(providersData.map(provider => ({
          value: provider.id,
          label: provider.fullName
        })));

        // Carregar tipos de serviço
        const serviceTypesData = await appointmentService.getServiceTypes();
        setServiceTypes(serviceTypesData.map(serviceType => ({
          value: serviceType.id,
          label: serviceType.name
        })));

        // Carregar locais
        const locationsData = await appointmentService.getLocations();
        setLocations(locationsData.map(location => ({
          value: location.id,
          label: location.name
        })));
      } catch (error) {
        console.error("Erro ao carregar opções de filtro:", error);
        setError("Não foi possível carregar as opções de filtro. Por favor, tente novamente mais tarde.");
      }
    };

    loadFilterOptions();
  }, []);

  // Carregar dados de ocupação
  const loadOccupancyData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Preparar parâmetros de filtro
      const params = {
        period,
        providers: selectedProviders.map(p => p.value),
        serviceTypes: selectedServiceTypes.map(s => s.value),
        locations: selectedLocations.map(l => l.value)
      };

      // Adicionar parâmetros de data com base no período selecionado
      if (period === 'custom') {
        try {
          // Verificar se as datas são válidas
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error('Datas inválidas para o período personalizado');
          }

          // Para período personalizado, usar datas de início e fim explícitas
          params.startDate = startDate.toISOString();
          params.endDate = endDate.toISOString();
        } catch (dateError) {
          console.error('Erro com datas personalizadas:', dateError);
          setError('Datas inválidas. Por favor, selecione datas válidas para o período personalizado.');
          setIsLoading(false);
          return;
        }
      } else {
        try {
          // Verificar se a data de referência é válida
          if (isNaN(selectedDate.getTime())) {
            throw new Error('Data de referência inválida');
          }

          // Para períodos predefinidos, usar a data de referência
          params.date = selectedDate.toISOString();
        } catch (dateError) {
          console.error('Erro com data de referência:', dateError);
          setError('Data de referência inválida. Por favor, selecione uma data válida.');
          setIsLoading(false);
          return;
        }
      }

      console.log('Parâmetros de busca:', params);

      // Carregar dados
      const data = await occupancyService.getOccupancyData(params);
      setOccupancyData(data);

      // Resetar detalhes expandidos
      setExpandedProvider(null);
      setExpandedService(null);
      setProviderDetails(null);
      setServiceTypeComparison(null);
    } catch (error) {
      console.error("Erro ao carregar dados de ocupação:", error);
      setError("Não foi possível carregar os dados de ocupação. Por favor, tente novamente mais tarde.");
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar dados iniciais
  useEffect(() => {
    loadOccupancyData();
  }, []);

  // Carregar dados ao mudar filtros
  const handleApplyFilters = () => {
    loadOccupancyData();
  };

  // Função para exportar os dados de ocupação
  const handleExport = async (format) => {
    if (!occupancyData) return;

    setIsExporting(true);
    try {
      // Exportar os dados
      await occupancyService.exportOccupancyData(
        occupancyData,
        getPeriodLabel(),
        format
      );
    } catch (error) {
      console.error("Erro ao exportar dados de ocupação:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Carregar detalhes de um provedor
  const handleViewProviderDetails = async (providerId) => {
    if (expandedProvider === providerId) {
      // Fechar se já estiver aberto
      setExpandedProvider(null);
      setProviderDetails(null);
      return;
    }

    setExpandedProvider(providerId);
    setIsLoadingDetails(true);

    try {
      // Preparar parâmetros com base no período selecionado
      const params = { period };

      if (period === 'custom') {
        try {
          // Verificar se as datas são válidas
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error('Datas inválidas para o período personalizado');
          }

          params.startDate = startDate.toISOString();
          params.endDate = endDate.toISOString();
        } catch (dateError) {
          console.error('Erro com datas personalizadas:', dateError);
          setError('Datas inválidas. Por favor, selecione datas válidas para o período personalizado.');
          setIsLoadingDetails(false);
          return;
        }
      } else {
        try {
          // Verificar se a data de referência é válida
          if (isNaN(selectedDate.getTime())) {
            throw new Error('Data de referência inválida');
          }

          params.date = selectedDate.toISOString();
        } catch (dateError) {
          console.error('Erro com data de referência:', dateError);
          setError('Data de referência inválida. Por favor, selecione uma data válida.');
          setIsLoadingDetails(false);
          return;
        }
      }

      const details = await occupancyService.getProviderOccupancyDetails(providerId, params);

      setProviderDetails(details);
    } catch (error) {
      console.error(`Erro ao carregar detalhes do provedor ${providerId}:`, error);
      setError(`Não foi possível carregar os detalhes do provedor. Por favor, tente novamente mais tarde.`);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Carregar comparação de um tipo de serviço
  const handleViewServiceTypeComparison = async (serviceTypeId) => {
    if (expandedService === serviceTypeId) {
      // Fechar se já estiver aberto
      setExpandedService(null);
      setServiceTypeComparison(null);
      return;
    }

    setExpandedService(serviceTypeId);
    setIsLoadingDetails(true);

    try {
      // Preparar parâmetros com base no período selecionado
      const params = { period };

      if (period === 'custom') {
        try {
          // Verificar se as datas são válidas
          if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            throw new Error('Datas inválidas para o período personalizado');
          }

          params.startDate = startDate.toISOString();
          params.endDate = endDate.toISOString();
        } catch (dateError) {
          console.error('Erro com datas personalizadas:', dateError);
          setError('Datas inválidas. Por favor, selecione datas válidas para o período personalizado.');
          setIsLoadingDetails(false);
          return;
        }
      } else {
        try {
          // Verificar se a data de referência é válida
          if (isNaN(selectedDate.getTime())) {
            throw new Error('Data de referência inválida');
          }

          params.date = selectedDate.toISOString();
        } catch (dateError) {
          console.error('Erro com data de referência:', dateError);
          setError('Data de referência inválida. Por favor, selecione uma data válida.');
          setIsLoadingDetails(false);
          return;
        }
      }

      const comparison = await occupancyService.getServiceTypeProviderComparison(serviceTypeId, params);

      setServiceTypeComparison(comparison);
    } catch (error) {
      console.error(`Erro ao carregar comparação do serviço ${serviceTypeId}:`, error);
      setError(`Não foi possível carregar a comparação do serviço. Por favor, tente novamente mais tarde.`);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  // Obter rótulo de período para cabeçalho
  const getPeriodLabel = () => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ];

    // Função auxiliar para formatar data em formato brasileiro
    const formatDateBR = (date) => {
      try {
        if (!date || isNaN(date.getTime())) {
          return 'Data inválida';
        }
        return `${date.getDate()} de ${months[date.getMonth()]} de ${date.getFullYear()}`;
      } catch (error) {
        console.error('Erro ao formatar data:', error);
        return 'Data inválida';
      }
    };

    try {
      switch (period) {
        case 'day':
          return `Dia: ${formatDateBR(selectedDate)}`;
        case 'week':
          try {
            const weekStart = new Date(selectedDate);
            if (isNaN(weekStart.getTime())) {
              return 'Semana: Data inválida';
            }

            const day = weekStart.getDay();
            const diff = weekStart.getDate() - day + (day === 0 ? -6 : 1);
            weekStart.setDate(diff);

            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekEnd.getDate() + 6);

            return `Semana: ${weekStart.getDate()} a ${weekEnd.getDate()} de ${months[weekEnd.getMonth()]} de ${weekEnd.getFullYear()}`;
          } catch (error) {
            console.error('Erro ao calcular semana:', error);
            return 'Semana: Data inválida';
          }
        case 'month':
          try {
            if (isNaN(selectedDate.getTime())) {
              return 'Mês: Data inválida';
            }
            return `Mês: ${months[selectedDate.getMonth()]} de ${selectedDate.getFullYear()}`;
          } catch (error) {
            console.error('Erro ao formatar mês:', error);
            return 'Mês: Data inválida';
          }
        case 'year':
          try {
            if (isNaN(selectedDate.getTime())) {
              return 'Ano: Data inválida';
            }
            return `Ano: ${selectedDate.getFullYear()}`;
          } catch (error) {
            console.error('Erro ao formatar ano:', error);
            return 'Ano: Data inválida';
          }
        case 'custom':
          return `Período personalizado: ${formatDateBR(startDate)} até ${formatDateBR(endDate)}`;
        default:
          return 'Período selecionado';
      }
    } catch (error) {
      console.error('Erro ao gerar rótulo de período:', error);
      return 'Período selecionado';
    }
  };

  return (
    <div className="space-y-6">
      {/* Título e botão de exportar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Briefcase size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Análise de Ocupação
        </h1>

        {/* Botão de exportar */}
        <div className="export-button">
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || !occupancyData}
            className="text-purple-700 dark:text-purple-300"
          />
        </div>
      </div>

      {/* Filtros */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-scheduler-border dark:border-gray-700 shadow-lg dark:shadow-black/30 p-4">
        {period !== 'custom' ? (
          /* Layout para períodos padrão (dia, semana, mês, ano) */
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 mb-5">
            <ModuleFormGroup
              moduleColor="scheduler"
              label="Período"
              htmlFor="period"
              icon={<Calendar size={16} />}
            >
              <ModuleSelect
                moduleColor="scheduler"
                id="period"
                name="period"
                value={period}
                onChange={(e) => {
                      const newPeriod = e.target.value;
                      setPeriod(newPeriod);

                      // Se mudar para período personalizado, inicializar datas
                      if (newPeriod === 'custom') {
                        // Inicializar com o mês atual
                        const today = new Date();
                        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

                        setStartDate(firstDay);
                        setEndDate(lastDay);
                      }
                    }}
                  >
                    <option value="day">Dia</option>
                    <option value="week">Semana</option>
                    <option value="month">Mês</option>
                    <option value="year">Ano</option>
                    <option value="custom">Personalizado</option>
                  </ModuleSelect>
                </ModuleFormGroup>

                <ModuleFormGroup
                  moduleColor="scheduler"
                  label="Data de Referência"
                  htmlFor="referenceDate"
                  icon={<Calendar size={16} />}
                >
                  <ModuleDatePicker
                    moduleColor="scheduler"
                    id="referenceDate"
                    name="referenceDate"
                    value={formatDateForInput(selectedDate)}
                    onChange={(e) => {
                      try {
                        if (e.target.value) {
                          setSelectedDate(createSafeDate(e.target.value));
                        }
                      } catch (error) {
                        console.error('Erro ao definir data:', error);
                      }
                    }}
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Use "Aplicar Filtros" abaixo para atualizar os dados.
                  </p>
                </ModuleFormGroup>

                <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                  <ModuleFormGroup
                    moduleColor="scheduler"
                    label="Profissionais"
                    htmlFor="providers"
                    icon={<Users size={16} />}
                  >
                    <MultiSelect
                      value={selectedProviders}
                      onChange={setSelectedProviders}
                      options={providers}
                      placeholder="Todos"
                      moduleOverride="scheduler"
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup
                    moduleColor="scheduler"
                    label="Serviços"
                    htmlFor="services"
                    icon={<Briefcase size={16} />}
                  >
                    <MultiSelect
                      value={selectedServiceTypes}
                      onChange={setSelectedServiceTypes}
                      options={serviceTypes}
                      placeholder="Todos"
                      moduleOverride="scheduler"
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup
                    moduleColor="scheduler"
                    label="Locais"
                    htmlFor="locations"
                    icon={<MapPin size={16} />}
                  >
                    <MultiSelect
                      value={selectedLocations}
                      onChange={setSelectedLocations}
                      options={locations}
                      placeholder="Todos"
                      moduleOverride="scheduler"
                    />
                  </ModuleFormGroup>
                </div>
              </div>
            ) : (
              /* Layout para período personalizado - duas linhas */
              <div className="space-y-4 mb-5">
                {/* Primeira linha: apenas o seletor de período */}
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
                  <ModuleFormGroup
                    moduleColor="scheduler"
                    label="Período"
                    htmlFor="period"
                    icon={<Calendar size={16} />}
                  >
                    <ModuleSelect
                      moduleColor="scheduler"
                      id="period"
                      name="period"
                      value={period}
                      onChange={(e) => {
                        const newPeriod = e.target.value;
                        setPeriod(newPeriod);

                        // Se mudar para período personalizado, inicializar datas
                        if (newPeriod === 'custom') {
                          // Inicializar com o mês atual
                          const today = new Date();
                          const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
                          const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

                          setStartDate(firstDay);
                          setEndDate(lastDay);
                        }
                      }}
                    >
                      <option value="day">Dia</option>
                      <option value="week">Semana</option>
                      <option value="month">Mês</option>
                      <option value="year">Ano</option>
                      <option value="custom">Personalizado</option>
                    </ModuleSelect>
                  </ModuleFormGroup>

                  <div className="lg:col-span-3">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <ModuleFormGroup
                        moduleColor="scheduler"
                        label="Data Inicial"
                        htmlFor="startDate"
                        icon={<Calendar size={16} />}
                      >
                        <ModuleDatePicker
                          moduleColor="scheduler"
                          id="startDate"
                          name="startDate"
                          value={formatDateForInput(startDate)}
                          onChange={(e) => {
                            try {
                              if (e.target.value) {
                                setStartDate(createSafeDate(e.target.value));
                              }
                            } catch (error) {
                              console.error('Erro ao definir data inicial:', error);
                            }
                          }}
                        />
                      </ModuleFormGroup>

                      <ModuleFormGroup
                        moduleColor="scheduler"
                        label="Data Final"
                        htmlFor="endDate"
                        icon={<Calendar size={16} />}
                      >
                        <ModuleDatePicker
                          moduleColor="scheduler"
                          id="endDate"
                          name="endDate"
                          value={formatDateForInput(endDate)}
                          onChange={(e) => {
                            try {
                              if (e.target.value) {
                                setEndDate(createSafeDate(e.target.value));
                              }
                            } catch (error) {
                              console.error('Erro ao definir data final:', error);
                            }
                          }}
                        />
                      </ModuleFormGroup>
                    </div>
                  </div>
                </div>

                {/* Segunda linha: filtros de profissionais, serviços e locais */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <ModuleFormGroup
                    moduleColor="scheduler"
                    label="Profissionais"
                    htmlFor="providers"
                    icon={<Users size={16} />}
                  >
                    <MultiSelect
                      value={selectedProviders}
                      onChange={setSelectedProviders}
                      options={providers}
                      placeholder="Todos"
                      moduleOverride="scheduler"
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup
                    moduleColor="scheduler"
                    label="Serviços"
                    htmlFor="services"
                    icon={<Briefcase size={16} />}
                  >
                    <MultiSelect
                      value={selectedServiceTypes}
                      onChange={setSelectedServiceTypes}
                      options={serviceTypes}
                      placeholder="Todos"
                      moduleOverride="scheduler"
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup
                    moduleColor="scheduler"
                    label="Locais"
                    htmlFor="locations"
                    icon={<MapPin size={16} />}
                  >
                    <MultiSelect
                      value={selectedLocations}
                      onChange={setSelectedLocations}
                      options={locations}
                      placeholder="Todos"
                      moduleOverride="scheduler"
                    />
                  </ModuleFormGroup>
                </div>
              </div>
            )}

          <div className="flex justify-end mt-4">
            <FilterButton
              type="button"
              onClick={handleApplyFilters}
              moduleColor="scheduler"
              variant="primary"
              disabled={isLoading}
            >
              <div className="flex items-center gap-2">
                {isLoading ? (
                  <Loader className="h-4 w-4 animate-spin" />
                ) : (
                  <Filter className="h-4 w-4" />
                )}
                <span>Aplicar Filtros</span>
              </div>
            </FilterButton>
          </div>
      </div>

      {/* Conteúdo principal */}
      {isLoading ? (
        <div className="bg-white dark:bg-gray-800 p-10 rounded-xl shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700 flex flex-col items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-module-scheduler-icon dark:border-module-scheduler-icon-dark mb-4"></div>
          <p className="text-gray-700 dark:text-gray-300">Carregando dados de ocupação...</p>
        </div>
      ) : error ? (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-center text-error-500 dark:text-red-400 mb-2">
            <AlertTriangle className="h-10 w-10" />
          </div>
          <p className="text-center text-error-700 dark:text-red-300 mb-4">{error}</p>
          <div className="flex justify-center">
            <FilterButton
              type="button"
              onClick={loadOccupancyData}
              moduleColor="scheduler"
              variant="primary"
            >
              <div className="flex items-center gap-2">
                <RefreshCcw className="h-4 w-4" />
                <span>Tentar Novamente</span>
              </div>
            </FilterButton>
          </div>
        </div>
      ) : occupancyData ? (
        <>
          {/* Resumo de ocupação */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700"
          id="bordaTaxaOcupacao">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2 md:mb-0">
                Taxa de Ocupação
              </h2>
              <div className="text-right">
                <span className="inline-block text-gray-500 dark:text-gray-400">
                  {getPeriodLabel()}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              {/* Taxa de ocupação geral */}
              <div className="bg-gradient-to-br from-module-scheduler-bg to-module-scheduler-hover dark:from-module-scheduler-bg-dark dark:to-module-scheduler-hover-dark p-5 rounded-lg shadow-md dark:shadow-black/30">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-medium text-white">Taxa Geral</h3>
                  <div className="h-12 w-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="mt-3">
                  <span className="text-3xl font-bold text-white">
                    {formatPercentage(occupancyData.overallOccupancy)}
                  </span>
                </div>
                <div className="mt-1 text-sm text-white text-opacity-90">
                  Ocupação total dos horários disponíveis
                </div>
              </div>

              {/* Profissional mais ocupado */}
              {occupancyData.providerOccupancy.length > 0 && (
                <div className="bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 p-5 rounded-lg shadow-md dark:shadow-black/30">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-white">Profissional Mais Ocupado</h3>
                    <div className="h-12 w-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <User className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="mt-3">
                    <span className="text-3xl font-bold text-white">
                      {formatPercentage(occupancyData.providerOccupancy[0]?.occupancyRate || 0)}
                    </span>
                  </div>
                  <div className="mt-1 text-sm text-white text-opacity-90">
                    {occupancyData.providerOccupancy[0]?.providerName || 'Nenhum'}
                  </div>
                </div>
              )}

              {/* Serviço mais agendado */}
              {occupancyData.serviceTypeDistribution.length > 0 && (
                <div className="bg-gradient-to-br from-emerald-500 to-emerald-600 dark:from-emerald-600 dark:to-emerald-700 p-5 rounded-lg shadow-md dark:shadow-black/30">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-white">Serviço Mais Agendado</h3>
                    <div className="h-12 w-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <Briefcase className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="mt-3">
                    <span className="text-3xl font-bold text-white">
                      {occupancyData.serviceTypeDistribution[0]?.count || 0}
                    </span>
                  </div>
                  <div className="mt-1 text-sm text-white text-opacity-90 truncate">
                    {occupancyData.serviceTypeDistribution[0]?.serviceTypeName || 'Nenhum'}
                  </div>
                </div>
              )}

              {/* Local mais utilizado */}
              {occupancyData.locationOccupancy.length > 0 && (
                <div className="bg-gradient-to-br from-amber-500 to-amber-600 dark:from-amber-600 dark:to-amber-700 p-5 rounded-lg shadow-md dark:shadow-black/30">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-white">Local Mais Utilizado</h3>
                    <div className="h-12 w-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                      <MapPin className="h-6 w-6 text-white" />
                    </div>
                  </div>
                  <div className="mt-3">
                    <span className="text-3xl font-bold text-white">
                      {occupancyData.locationOccupancy[0]?.count || 0}
                    </span>
                  </div>
                  <div className="mt-1 text-sm text-white text-opacity-90 truncate">
                    {occupancyData.locationOccupancy[0]?.locationName || 'Nenhum'}
                  </div>
                </div>
              )}
            </div>

            {/* Distribuição por hora e dia da semana */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Distribuição por hora do dia */}
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm dark:shadow-black/20">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
                  Distribuição por Hora do Dia
                </h3>

                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={occupancyData.timeDistribution}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" className="dark:stroke-gray-600" />
                      <XAxis
                        dataKey="label"
                        tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                      />
                      <YAxis
                        tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                        tickFormatter={(value) => value}
                        label={{
                          value: 'Agendamentos',
                          angle: -90,
                          position: 'insideLeft',
                          style: { textAnchor: 'middle', fill: '#6b7280', className: "dark:fill-gray-400" }
                        }}
                      />
                      <Tooltip
                        formatter={(value) => [`${value} agendamentos`, 'Quantidade']}
                        labelFormatter={(label) => `Horário: ${label}`}
                      />
                      <Bar
                        dataKey="count"
                        fill="#8884d8"
                        name="Agendamentos"
                        barSize={window.innerWidth > 768 ? 20 : 10}
                      >
                        {occupancyData.timeDistribution.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={`rgba(136, 132, 216, ${0.3 + entry.percentageOfTotal / 100 * 0.7})`}
                          />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Distribuição por dia da semana */}
              <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm dark:shadow-black/20">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
                  <CalendarIcon className="h-5 w-5 mr-2 text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
                  Distribuição por Dia da Semana
                </h3>

                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart
                      cx="50%"
                      cy="50%"
                      outerRadius="70%"
                      data={occupancyData.weekdayDistribution}
                    >
                      <PolarGrid stroke="#e5e7eb" className="dark:stroke-gray-600" />
                      <PolarAngleAxis
                        dataKey="label"
                        tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                      />
                      <PolarRadiusAxis
                        angle={90}
                        domain={[0, 'auto']}
                        tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                      />
                      <Radar
                        name="Agendamentos"
                        dataKey="count"
                        stroke="#8884d8"
                        fill="#8884d8"
                        fillOpacity={0.5}
                      />
                      <Tooltip
                        formatter={(value) => [`${value} agendamentos`, 'Quantidade']}
                        labelFormatter={(label) => `Dia: ${label}`}
                      />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </div>

          {/* Detalhes por profissionais */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
              <Users className="h-5 w-5 mr-2 text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
              Ocupação por Profissional
            </h2>

            {occupancyData.providerOccupancy.length === 0 ? (
              <div className="py-10 text-center text-gray-500 dark:text-gray-400">
                <p>Nenhum dado de ocupação de profissionais disponível para o período selecionado.</p>
              </div>
            ) : (
              <div className="space-y-5">
                {/* Gráfico de ocupação */}
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm dark:shadow-black/20">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <ComposedChart
                        layout="vertical"
                        data={occupancyData.providerOccupancy
                          .slice(0, 10) // Limitar a 10 profissionais no gráfico
                          .map(p => ({
                            ...p,
                            occupancyColor: getOccupancyStatus(p.occupancyRate).color
                          }))}
                        margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid stroke="#e5e7eb" className="dark:stroke-gray-600" strokeDasharray="3 3" />
                        <XAxis
                          type="number"
                          tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                          domain={[0, 100]}
                          tickFormatter={(value) => `${value}%`}
                        />
                        <YAxis
                          dataKey="providerName"
                          type="category"
                          scale="band"
                          tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                          width={150}
                        />
                        <Tooltip
                          formatter={(value, name) => {
                            switch (name) {
                              case 'Taxa de Ocupação': return [`${formatPercentage(value)}`, name];
                              case 'Horas Disponíveis': return [`${formatHours(value)}`, name];
                              case 'Horas Ocupadas': return [`${formatHours(value)}`, name];
                              default: return [value, name];
                            }
                          }}
                        />
                        <Legend />
                        <Bar
                          dataKey="occupancyRate"
                          name="Taxa de Ocupação"
                          fill="#8884d8"
                          barSize={20}
                        >
                          {occupancyData.providerOccupancy.slice(0, 10).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.occupancyColor} />
                          ))}
                        </Bar>
                        <Area
                          type="monotone"
                          dataKey="totalAvailableHours"
                          name="Horas Disponíveis"
                          fill="#82ca9d"
                          stroke="#82ca9d"
                          fillOpacity={0.3}
                        />
                        <Line
                          type="monotone"
                          dataKey="totalBookedHours"
                          name="Horas Ocupadas"
                          stroke="#ff7300"
                          strokeWidth={2}
                        />
                      </ComposedChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Lista de profissionais */}
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Profissional
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Taxa de Ocupação
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Horas Disponíveis
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Horas Ocupadas
                        </th>
                        <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Agendamentos
                        </th>
                        <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Detalhes
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-800 dark:divide-gray-700">
                      {occupancyData.providerOccupancy.map((provider) => {
                        const occupancyStatus = getOccupancyStatus(provider.occupancyRate);
                        return (
                          <React.Fragment key={provider.providerId}>
                            <tr className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                {provider.providerName}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                <div className="flex items-center">
                                  <div className="w-16 bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 mr-2">
                                    <div
                                      className="h-2.5 rounded-full"
                                      style={{
                                        width: `${Math.min(provider.occupancyRate, 100)}%`,
                                        backgroundColor: occupancyStatus.color
                                      }}
                                    ></div>
                                  </div>
                                  <span className={`${occupancyStatus.textColor}`}>
                                    {formatPercentage(provider.occupancyRate)}
                                  </span>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                                {formatHours(provider.totalAvailableHours)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                                {formatHours(provider.totalBookedHours)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                                {provider.appointmentCount}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button
                                  onClick={() => handleViewProviderDetails(provider.providerId)}
                                  className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark hover:text-module-scheduler-hover dark:hover:text-module-scheduler-hover-dark flex items-center gap-1 ml-auto"
                                >
                                  {expandedProvider === provider.providerId ? (
                                    <>
                                      Ocultar Detalhes
                                      <ChevronUp className="h-4 w-4" />
                                    </>
                                  ) : (
                                    <>
                                      Ver Detalhes
                                      <ChevronDown className="h-4 w-4" />
                                    </>
                                  )}
                                </button>
                              </td>
                            </tr>

                            {/* Detalhes expandidos */}
                            {expandedProvider === provider.providerId && (
                              <tr>
                                <td colSpan={6} className="px-6 py-4 bg-gray-50 dark:bg-gray-700">
                                  {isLoadingDetails ? (
                                    <div className="py-6 flex justify-center">
                                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-module-scheduler-icon dark:border-module-scheduler-icon-dark"></div>
                                    </div>
                                  ) : !providerDetails ? (
                                    <div className="py-6 text-center text-gray-500 dark:text-gray-400">
                                      Não foi possível carregar os detalhes.
                                    </div>
                                  ) : (
                                    <div className="space-y-4">
                                      {/* Ocupação por dia da semana */}
                                      <div>
                                        <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                          Ocupação por Dia da Semana
                                        </h4>
                                        <div className="h-64">
                                          <ResponsiveContainer width="100%" height="100%">
                                            <BarChart
                                              data={providerDetails.weekdayOccupancy}
                                              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                            >
                                              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" className="dark:stroke-gray-600" />
                                              <XAxis
                                                dataKey="label"
                                                tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                                              />
                                              <YAxis
                                                yAxisId="left"
                                                orientation="left"
                                                tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                                                domain={[0, 100]}
                                                label={{
                                                  value: 'Ocupação (%)',
                                                  angle: -90,
                                                  position: 'insideLeft',
                                                  style: { textAnchor: 'middle', fill: '#6b7280', className: "dark:fill-gray-400" }
                                                }}
                                              />
                                              <YAxis
                                                yAxisId="right"
                                                orientation="right"
                                                tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                                                label={{
                                                  value: 'Horas',
                                                  angle: 90,
                                                  position: 'insideRight',
                                                  style: { textAnchor: 'middle', fill: '#6b7280', className: "dark:fill-gray-400" }
                                                }}
                                              />
                                              <Tooltip
                                                formatter={(value, name) => {
                                                  if (name === 'Taxa de Ocupação') return [`${formatPercentage(value)}`, name];
                                                  return [`${formatHours(value)}`, name];
                                                }}
                                              />
                                              <Legend />
                                              <Bar
                                                yAxisId="left"
                                                dataKey="occupancyRate"
                                                name="Taxa de Ocupação"
                                                fill="#8884d8"
                                              >
                                                {providerDetails.weekdayOccupancy.map((entry, index) => (
                                                  <Cell key={`cell-${index}`} fill={WEEKDAY_COLORS[index % WEEKDAY_COLORS.length]} />
                                                ))}
                                              </Bar>
                                              <Bar
                                                yAxisId="right"
                                                dataKey="availableHours"
                                                name="Horas Disponíveis"
                                                fill="#82ca9d"
                                              />
                                              <Bar
                                                yAxisId="right"
                                                dataKey="bookedHours"
                                                name="Horas Ocupadas"
                                                fill="#ff7300"
                                              />
                                            </BarChart>
                                          </ResponsiveContainer>
                                        </div>
                                      </div>

                                      {/* Informações adicionais - Grids */}
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {/* Tipos de serviço mais realizados */}
                                        <div>
                                          <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            Serviços Mais Realizados
                                          </h4>
                                          {providerDetails.topServices.length === 0 ? (
                                            <p className="text-gray-500 dark:text-gray-400 text-sm">
                                              Nenhum serviço registrado no período.
                                            </p>
                                          ) : (
                                            <div className="grid grid-cols-1 gap-2">
                                              {providerDetails.topServices.slice(0, 5).map((service, index) => (
                                                <div key={service.serviceTypeId} className="flex items-center gap-2">
                                                  <div
                                                    className="h-3 w-3 rounded-full"
                                                    style={{ backgroundColor: SERVICE_COLORS[index % SERVICE_COLORS.length] }}
                                                  ></div>
                                                  <span className="text-sm text-gray-700 dark:text-gray-300 flex-1 truncate" title={service.serviceTypeName}>
                                                    {service.serviceTypeName}
                                                  </span>
                                                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {service.count}
                                                  </span>
                                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                                    ({formatPercentage(service.percentageOfTotal)})
                                                  </span>
                                                </div>
                                              ))}
                                            </div>
                                          )}
                                        </div>

                                        {/* Locais mais utilizados */}
                                        <div>
                                          <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                            Locais Mais Utilizados
                                          </h4>
                                          {providerDetails.topLocations.length === 0 ? (
                                            <p className="text-gray-500 dark:text-gray-400 text-sm">
                                              Nenhum local registrado no período.
                                            </p>
                                          ) : (
                                            <div className="grid grid-cols-1 gap-2">
                                              {providerDetails.topLocations.slice(0, 5).map((location, index) => (
                                                <div key={location.locationId} className="flex items-center gap-2">
                                                  <div
                                                    className="h-3 w-3 rounded-full"
                                                    style={{ backgroundColor: LOCATION_COLORS[index % LOCATION_COLORS.length] }}
                                                  ></div>
                                                  <span className="text-sm text-gray-700 dark:text-gray-300 flex-1 truncate" title={location.locationName}>
                                                    {location.locationName}
                                                  </span>
                                                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                                    {location.count}
                                                  </span>
                                                  <span className="text-xs text-gray-500 dark:text-gray-400">
                                                    ({formatPercentage(location.percentageOfTotal)})
                                                  </span>
                                                </div>
                                              ))}
                                            </div>
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </td>
                              </tr>
                            )}
                          </React.Fragment>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>

          {/* Detalhes por tipo de serviço */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
              <Briefcase className="h-5 w-5 mr-2 text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
              Distribuição por Tipo de Serviço
            </h2>

            {occupancyData.serviceTypeDistribution.length === 0 ? (
              <div className="py-10 text-center text-gray-500 dark:text-gray-400">
                <p>Nenhum dado de serviços disponível para o período selecionado.</p>
              </div>
            ) : (
              <div className="space-y-5">
                {/* Gráfico de distribuição */}
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm dark:shadow-black/20">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={occupancyData.serviceTypeDistribution.slice(0, 5)}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={120}
                          label={({ name, percent }) => `${formatPercentage(percent * 100)}`}
                          dataKey="count"
                          nameKey="serviceTypeName"
                        >
                          {occupancyData.serviceTypeDistribution.slice(0, 5).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={SERVICE_COLORS[index % SERVICE_COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value) => [`${value} agendamentos`, 'Quantidade']}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Lista de serviços */}
                <ModuleTable
                  moduleColor="scheduler"
                  columns={[
                    { header: 'Tipo de Serviço', field: 'serviceTypeName', width: '25%' },
                    { header: 'Agendamentos', field: 'count', width: '15%', dataType: 'number' },
                    { header: '% do Total', field: 'percentageOfTotal', width: '15%', dataType: 'number' },
                    { header: 'Profissional com Maior Volume', field: 'topProvider', width: '30%' },
                    { header: 'Detalhes', field: 'actions', width: '15%', className: 'text-right', sortable: false }
                  ]}
                  data={occupancyData.serviceTypeDistribution}
                  emptyMessage="Nenhum tipo de serviço encontrado"
                  emptyIcon={<Briefcase size={24} />}
                  tableId="scheduler-service-types-distribution-table"
                  enableColumnToggle={true}
                  defaultSortField="count"
                  defaultSortDirection="desc"
                  renderRow={(service, index, moduleColors, visibleColumns) => (
                    <React.Fragment key={service.serviceTypeId}>
                      <tr className={moduleColors.hoverBg}>
                        {visibleColumns.includes('serviceTypeName') && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                            {service.serviceTypeName}
                          </td>
                        )}

                        {visibleColumns.includes('count') && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                            {service.count}
                          </td>
                        )}

                        {visibleColumns.includes('percentage') && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                            {formatPercentage(service.percentageOfTotal)}
                          </td>
                        )}

                        {visibleColumns.includes('topProvider') && (
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                            {service.topProvider ? (
                              <span className="inline-flex items-center">
                                {service.topProvider.providerName}
                                <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                                  ({service.topProvider.count})
                                </span>
                              </span>
                            ) : (
                              <span className="text-gray-400 dark:text-gray-500">
                                -
                              </span>
                            )}
                          </td>
                        )}

                        {visibleColumns.includes('actions') && (
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button
                              onClick={() => handleViewServiceTypeComparison(service.serviceTypeId)}
                              className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark hover:text-module-scheduler-hover dark:hover:text-module-scheduler-hover-dark flex items-center gap-1 ml-auto"
                            >
                              {expandedService === service.serviceTypeId ? (
                                <>
                                  Ocultar Detalhes
                                  <ChevronUp className="h-4 w-4" />
                                </>
                              ) : (
                                <>
                                  Ver Detalhes
                                  <ChevronDown className="h-4 w-4" />
                                </>
                              )}
                            </button>
                          </td>
                        )}
                      </tr>

                      {/* Detalhes expandidos */}
                      {expandedService === service.serviceTypeId && (
                        <tr>
                          <td colSpan={5} className="px-6 py-4 bg-gray-50 dark:bg-gray-700">
                            {isLoadingDetails ? (
                              <div className="py-6 flex justify-center">
                                <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-module-scheduler-icon dark:border-module-scheduler-icon-dark"></div>
                              </div>
                            ) : !serviceTypeComparison ? (
                              <div className="py-6 text-center text-gray-500 dark:text-gray-400">
                                Não foi possível carregar os detalhes.
                              </div>
                            ) : (
                              <div>
                                <h4 className="text-md font-semibold text-gray-700 dark:text-gray-300 mb-3">
                                  Comparação de Profissionais
                                </h4>

                                {serviceTypeComparison.providerComparison.length === 0 ? (
                                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                                    Nenhum profissional registrado para este serviço no período.
                                  </p>
                                ) : (
                                  <div className="h-64">
                                    <ResponsiveContainer width="100%" height="100%">
                                      <BarChart
                                        data={serviceTypeComparison.providerComparison}
                                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                                      >
                                        <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" className="dark:stroke-gray-600" />
                                        <XAxis
                                          dataKey="providerName"
                                          tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                                          angle={-45}
                                          textAnchor="end"
                                          height={60}
                                        />
                                        <YAxis
                                          yAxisId="left"
                                          orientation="left"
                                          tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                                          label={{
                                            value: 'Quantidade',
                                            angle: -90,
                                            position: 'insideLeft',
                                            style: { textAnchor: 'middle', fill: '#6b7280', className: "dark:fill-gray-400" }
                                          }}
                                        />
                                        <YAxis
                                          yAxisId="right"
                                          orientation="right"
                                          tick={{ fontSize: 12, fill: "#6b7280", className: "dark:fill-gray-400" }}
                                          label={{
                                            value: 'Duração (h)',
                                            angle: 90,
                                            position: 'insideRight',
                                            style: { textAnchor: 'middle', fill: '#6b7280', className: "dark:fill-gray-400" }
                                          }}
                                        />
                                        <Tooltip
                                          formatter={(value, name) => {
                                            if (name === 'Duração' || name === 'Duração Média')
                                              return [`${formatHours(value)}`, name];
                                            return [value, name];
                                          }}
                                        />
                                        <Legend />
                                        <Bar
                                          yAxisId="left"
                                          dataKey="count"
                                          name="Quantidade"
                                          fill="#8884d8"
                                        >
                                          {serviceTypeComparison.providerComparison.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={PROVIDER_COLORS[index % PROVIDER_COLORS.length]} />
                                          ))}
                                        </Bar>
                                        <Bar
                                          yAxisId="right"
                                          dataKey="duration"
                                          name="Duração"
                                          fill="#82ca9d"
                                        />
                                        <Line
                                          yAxisId="right"
                                          type="monotone"
                                          dataKey="averageDuration"
                                          name="Duração Média"
                                          stroke="#ff7300"
                                        />
                                      </BarChart>
                                    </ResponsiveContainer>
                                  </div>
                                )}
                              </div>
                            )}
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  )}
                />
              </div>
            )}
          </div>

          {/* Detalhes por local */}
          <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
            <h2 className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
              <MapPin className="h-5 w-5 mr-2 text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
              Distribuição por Local
            </h2>

            {occupancyData.locationOccupancy.length === 0 ? (
              <div className="py-10 text-center text-gray-500 dark:text-gray-400">
                <p>Nenhum dado de locais disponível para o período selecionado.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Gráfico de distribuição */}
                <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm dark:shadow-black/20">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={occupancyData.locationOccupancy.slice(0, 5)}
                          cx="50%"
                          cy="50%"
                          innerRadius={60}
                          outerRadius={90}
                          paddingAngle={5}
                          dataKey="count"
                          nameKey="locationName"
                        >
                          {occupancyData.locationOccupancy.slice(0, 5).map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={LOCATION_COLORS[index % LOCATION_COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value) => [`${value} agendamentos`, 'Quantidade']}
                        />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                </div>

                {/* Lista de locais */}
                <ModuleTable
                  moduleColor="scheduler"
                  columns={[
                    { header: 'Local', field: 'locationName', width: '50%' },
                    { header: 'Agendamentos', field: 'count', width: '25%', dataType: 'number' },
                    { header: '% do Total', field: 'percentageOfTotal', width: '25%', dataType: 'number' }
                  ]}
                  data={occupancyData.locationOccupancy}
                  emptyMessage="Nenhum local encontrado"
                  emptyIcon={<MapPin size={24} />}
                  tableId="scheduler-locations-occupancy-table"
                  enableColumnToggle={true}
                  defaultSortField="count"
                  defaultSortDirection="desc"
                  renderRow={(location, index, moduleColors, visibleColumns) => (
                    <tr key={location.locationId} className={moduleColors.hoverBg}>
                      {visibleColumns.includes('locationName') && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100 flex items-center">
                          <div
                            className="h-3 w-3 rounded-full mr-2"
                            style={{ backgroundColor: LOCATION_COLORS[index % LOCATION_COLORS.length] }}
                          ></div>
                          {location.locationName}
                        </td>
                      )}

                      {visibleColumns.includes('count') && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                          {location.count}
                        </td>
                      )}

                      {visibleColumns.includes('percentage') && (
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">
                          {formatPercentage(location.percentageOfTotal)}
                        </td>
                      )}
                    </tr>
                  )}
                />
              </div>
            )}
          </div>
        </>
      ) : (
        <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
          <div className="flex flex-col items-center justify-center py-10">
            <div className="h-16 w-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
              <CalendarIcon className="h-8 w-8 text-gray-400 dark:text-gray-500" />
            </div>
            <p className="mt-4 text-gray-500 dark:text-gray-400">
              Selecione um período e aplique os filtros para visualizar os dados de ocupação.
            </p>
            <button
              onClick={loadOccupancyData}
              className="mt-4 px-4 py-2 bg-module-scheduler-icon hover:bg-module-scheduler-hover text-white rounded-lg flex items-center gap-2 transition-colors shadow-md"
            >
              <Filter className="h-4 w-4" />
              Carregar Dados
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default OccupancyDashboard;