-- <PERSON><PERSON><PERSON><PERSON>
CREATE TYPE "TeachingType" AS ENUM ('DISCRETE_TRIAL_STRUCTURED', 'TASK_ANALYSIS', 'NATURALISTIC_TEACHING', 'DISCRETE_TRIAL_INTERSPERSED');

-- <PERSON>reateEnum
CREATE TYPE "CriteriaDegree" AS ENUM ('OMISSION', 'ERROR', 'MORE_INTRUSIVE', 'PARTIALLY_INTRUSIVE', 'LESS_INTRUSIVE', 'INDEPENDENT');

-- CreateTable
CREATE TABLE "StandardCriteria" (
    "id" TEXT NOT NULL,
    "teachingType" "TeachingType" NOT NULL,
    "acronym" TEXT NOT NULL,
    "degree" "CriteriaDegree" NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,

    CONSTRAINT "StandardCriteria_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "StandardCriteria_companyId_idx" ON "StandardCriteria"("companyId");

-- CreateIndex
CREATE INDEX "StandardCriteria_active_idx" ON "StandardCriteria"("active");

-- CreateIndex
CREATE INDEX "StandardCriteria_teachingType_idx" ON "StandardCriteria"("teachingType");

-- CreateIndex
CREATE INDEX "StandardCriteria_degree_idx" ON "StandardCriteria"("degree");

-- AddForeignKey
ALTER TABLE "StandardCriteria" ADD CONSTRAINT "StandardCriteria_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
