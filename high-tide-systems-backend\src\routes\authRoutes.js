const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const AuthController = require('../controllers/authController');
const { authenticate } = require('../middlewares/auth');

router.post('/register', [
  body('fullName').notEmpty().withMessage('Nome é obrigatório'),
  body('email').isEmail().withMessage('Email inválido'),
  body('password').isLength({ min: 6 }).withMessage('Senha deve ter no mínimo 6 caracteres'),
  body('module').optional().isIn(['ADMIN', 'FINANCIAL', 'HR', 'SCHEDULER', 'USER'])
    .withMessage('Role inválido')
], AuthController.register);

router.post('/login', [
  body('email').optional().isEmail().withMessage('<PERSON>ail inválido'),
  body('username').optional(),
  body('password').notEmpty().withMessage('Senha é obrigatória'),
  body('loginType').optional().isIn(['email', 'username']).withMessage('Tipo de login inválido')
], AuthController.login);

router.get('/me', authenticate, AuthController.me);

module.exports = router;