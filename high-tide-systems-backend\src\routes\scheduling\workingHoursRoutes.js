// routes/workingHoursRoutes.js

const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { WorkingHoursController, workingHoursValidation } = require('../../controllers/scheduling/workingHoursController');

// Rotas para gerenciamento básico de horários
router.get('/:userId', authenticate, WorkingHoursController.list);
router.post('/', authenticate, workingHoursValidation, WorkingHoursController.create);
router.put('/:id', authenticate, WorkingHoursController.update);
router.delete('/:id', authenticate, WorkingHoursController.delete);

// Rotas para gerenciamento de dias específicos
router.post('/:userId/:dayOfWeek', authenticate, WorkingHoursController.setDaySchedule);

// Rotas para grade de horários (granularidade de 1h)
router.get('/:userId/:dayOfWeek/grid', authenticate, WorkingHoursController.getDayGrid);
router.get('/grid/:dayOfWeek', authenticate, WorkingHoursController.getUsersTimeGrid);
router.put('/hourly-grid/:userId/:dayOfWeek', authenticate, WorkingHoursController.updateHourlyGrid);
router.put('/multiple-grids/:dayOfWeek', authenticate, WorkingHoursController.updateMultipleUsersGrid);

module.exports = router;