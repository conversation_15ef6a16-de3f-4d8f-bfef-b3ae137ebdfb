// prisma/seed-service-types.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Categorias de serviços de saúde com valores realistas
const healthServiceCategories = {
  // Serviços odontológicos
  dental: [
    { name: 'Consulta Odontológica', value: 150.00 },
    { name: 'Limpeza Dental', value: 180.00 },
    { name: 'Restauração Simples', value: 120.00 },
    { name: 'Restauração Complexa', value: 250.00 },
    { name: 'Tratamento de Canal', value: 800.00 },
    { name: 'Extração Simples', value: 180.00 },
    { name: 'Extração de Siso', value: 350.00 },
    { name: 'Clareamento Dental', value: 900.00 },
    { name: 'Aplicação de Flúor', value: 90.00 },
    { name: 'Radiografia Panorâmica', value: 120.00 },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', value: 1200.00 },
    { name: '<PERSON><PERSON><PERSON>', value: 3500.00 },
    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 2500.00 },
    { name: 'Manutenção <PERSON> Aparel<PERSON>', value: 150.00 },
    { name: 'Tratamento de Gengiva', value: 300.00 }
  ],
  
  // Serviços médicos gerais
  medical: [
    { name: 'Consulta Clínica', value: 250.00 },
    { name: 'Consulta Pediátrica', value: 280.00 },
    { name: 'Consulta Ginecológica', value: 300.00 },
    { name: 'Consulta Cardiológica', value: 350.00 },
    { name: 'Consulta Dermatológica', value: 320.00 },
    { name: 'Consulta Ortopédica', value: 300.00 },
    { name: 'Consulta Oftalmológica', value: 280.00 },
    { name: 'Consulta Neurológica', value: 380.00 },
    { name: 'Consulta Endocrinológica', value: 320.00 },
    { name: 'Consulta Urológica', value: 300.00 },
    { name: 'Exame de Sangue Básico', value: 120.00 },
    { name: 'Eletrocardiograma', value: 150.00 },
    { name: 'Ultrassonografia', value: 250.00 },
    { name: 'Raio-X', value: 180.00 },
    { name: 'Check-up Completo', value: 1200.00 }
  ],
  
  // Serviços psicológicos
  psychological: [
    { name: 'Sessão de Psicoterapia Individual', value: 200.00 },
    { name: 'Sessão de Psicoterapia em Casal', value: 280.00 },
    { name: 'Sessão de Psicoterapia Familiar', value: 320.00 },
    { name: 'Avaliação Psicológica', value: 450.00 },
    { name: 'Teste de Orientação Vocacional', value: 380.00 },
    { name: 'Terapia Cognitivo-Comportamental', value: 220.00 },
    { name: 'Terapia Infantil', value: 180.00 },
    { name: 'Terapia para Adolescentes', value: 200.00 },
    { name: 'Terapia para Ansiedade', value: 220.00 },
    { name: 'Terapia para Depressão', value: 220.00 },
    { name: 'Orientação Parental', value: 180.00 },
    { name: 'Coaching Psicológico', value: 250.00 },
    { name: 'Mindfulness e Meditação', value: 150.00 },
    { name: 'Grupo de Apoio', value: 120.00 },
    { name: 'Avaliação Neuropsicológica', value: 600.00 }
  ],
  
  // Serviços de fisioterapia
  physiotherapy: [
    { name: 'Avaliação Fisioterapêutica', value: 150.00 },
    { name: 'Fisioterapia Ortopédica', value: 120.00 },
    { name: 'Fisioterapia Neurológica', value: 150.00 },
    { name: 'Fisioterapia Respiratória', value: 130.00 },
    { name: 'Fisioterapia Esportiva', value: 140.00 },
    { name: 'Fisioterapia Geriátrica', value: 120.00 },
    { name: 'Fisioterapia Pediátrica', value: 130.00 },
    { name: 'RPG (Reeducação Postural Global)', value: 180.00 },
    { name: 'Pilates Terapêutico', value: 150.00 },
    { name: 'Acupuntura', value: 120.00 },
    { name: 'Drenagem Linfática', value: 140.00 },
    { name: 'Hidroterapia', value: 130.00 },
    { name: 'Terapia Manual', value: 120.00 },
    { name: 'Reabilitação Pós-Cirúrgica', value: 150.00 },
    { name: 'Bandagem Funcional', value: 80.00 }
  ],
  
  // Serviços para autismo e desenvolvimento
  autism: [
    { name: 'Avaliação Inicial TEA', value: 500.00 },
    { name: 'Terapia ABA (por sessão)', value: 180.00 },
    { name: 'Terapia Ocupacional', value: 150.00 },
    { name: 'Fonoaudiologia', value: 150.00 },
    { name: 'Psicomotricidade', value: 140.00 },
    { name: 'Musicoterapia', value: 130.00 },
    { name: 'Integração Sensorial', value: 160.00 },
    { name: 'Acompanhamento Psicopedagógico', value: 150.00 },
    { name: 'Orientação Familiar', value: 200.00 },
    { name: 'Terapia em Grupo', value: 120.00 },
    { name: 'Avaliação Neuropsicológica', value: 600.00 },
    { name: 'Programa de Comunicação Alternativa', value: 180.00 },
    { name: 'Treinamento de Habilidades Sociais', value: 150.00 },
    { name: 'Acompanhamento Escolar', value: 200.00 },
    { name: 'Plano Terapêutico Individualizado', value: 350.00 }
  ],
  
  // Serviços de nutrição
  nutrition: [
    { name: 'Consulta Nutricional', value: 180.00 },
    { name: 'Avaliação Antropométrica', value: 120.00 },
    { name: 'Plano Alimentar Personalizado', value: 250.00 },
    { name: 'Acompanhamento Nutricional', value: 150.00 },
    { name: 'Nutrição Esportiva', value: 200.00 },
    { name: 'Nutrição Clínica', value: 180.00 },
    { name: 'Nutrição Materno-Infantil', value: 180.00 },
    { name: 'Reeducação Alimentar', value: 160.00 },
    { name: 'Nutrição para Emagrecimento', value: 180.00 },
    { name: 'Nutrição para Hipertrofia', value: 200.00 },
    { name: 'Bioimpedância', value: 100.00 },
    { name: 'Orientação para Intolerâncias', value: 180.00 },
    { name: 'Nutrição para Diabéticos', value: 180.00 },
    { name: 'Nutrição para Idosos', value: 160.00 },
    { name: 'Nutrição Funcional', value: 220.00 }
  ]
};

// Mapeamento de indústrias para categorias de serviços
const industryToServiceCategories = {
  'Saúde': ['medical', 'physiotherapy', 'nutrition'],
  'Odontologia': ['dental'],
  'Psicologia': ['psychological'],
  'Fisioterapia': ['physiotherapy'],
  'Nutrição': ['nutrition'],
  'Terapia': ['psychological', 'physiotherapy', 'autism'],
  'Medicina': ['medical'],
  'Tecnologia': ['medical', 'psychological'], // Clínicas tecnológicas podem oferecer telemedicina
  'Educação': ['autism', 'psychological'], // Instituições educacionais com foco em desenvolvimento
  'Bem-estar': ['nutrition', 'physiotherapy'],
  'Serviços': ['medical', 'dental', 'psychological', 'physiotherapy', 'autism', 'nutrition'] // Empresas de serviços gerais podem oferecer diversos tipos
};

// Função para gerar um número aleatório entre min e max (inclusive)
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Função para embaralhar um array
function shuffleArray(array) {
  return [...array].sort(() => 0.5 - Math.random());
}

// Função principal
async function main() {
  console.log('Iniciando seed de tipos de serviço...');
  
  try {
    // Buscar todas as empresas ativas exceto a empresa de teste
    const companies = await prisma.company.findMany({
      where: {
        NOT: {
          id: '00000000-0000-0000-0000-000000000001' // Excluir a empresa de teste
        },
        active: true
      }
    });
    
    console.log(`Encontradas ${companies.length} empresas para adicionar tipos de serviço`);
    
    // Para cada empresa, criar entre 5 e 15 tipos de serviço
    for (const company of companies) {
      console.log(`\nProcessando empresa: ${company.name} (ID: ${company.id})`);
      
      // Determinar a indústria da empresa ou usar 'Serviços' como padrão
      const industry = company.industry || 'Serviços';
      console.log(`Indústria: ${industry}`);
      
      // Determinar as categorias de serviço com base na indústria
      const serviceCategories = industryToServiceCategories[industry] || ['medical', 'dental', 'psychological'];
      console.log(`Categorias de serviço selecionadas: ${serviceCategories.join(', ')}`);
      
      // Criar um pool de serviços possíveis para esta empresa
      let servicePool = [];
      for (const category of serviceCategories) {
        servicePool = [...servicePool, ...healthServiceCategories[category]];
      }
      
      // Embaralhar o pool de serviços
      servicePool = shuffleArray(servicePool);
      
      // Determinar quantos serviços criar (entre 5 e 15)
      const numServices = getRandomInt(5, 15);
      console.log(`Criando ${numServices} tipos de serviço para a empresa ${company.name}`);
      
      // Selecionar os serviços a serem criados
      const servicesToCreate = servicePool.slice(0, numServices);
      
      // Criar os serviços
      for (const service of servicesToCreate) {
        try {
          // Verificar se o serviço já existe para esta empresa
          const existingService = await prisma.serviceType.findFirst({
            where: {
              name: service.name,
              companyId: company.id
            }
          });
          
          if (existingService) {
            console.log(`Serviço "${service.name}" já existe para a empresa ${company.name}. Pulando...`);
            continue;
          }
          
          // Criar o serviço
          const createdService = await prisma.serviceType.create({
            data: {
              name: service.name,
              value: service.value,
              companyId: company.id
            }
          });
          
          console.log(`✅ Serviço criado: ${createdService.name} - R$ ${createdService.value}`);
        } catch (error) {
          console.error(`Erro ao criar serviço "${service.name}" para empresa ${company.name}:`, error);
        }
      }
    }
    
    console.log('\nSeed de tipos de serviço concluído com sucesso!');
  } catch (error) {
    console.error('Erro durante o seed de tipos de serviço:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
