// app/(auth)/login/page.js
'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Mail, Lock, Eye, EyeOff, HelpCircle, User } from 'lucide-react';
import Link from 'next/link';
import { APP_VERSION } from '@/config/appConfig';

export default function LoginPage() {
  const [loginType, setLoginType] = useState('email'); // 'email' ou 'username'
  const [email, setEmail] = useState('');
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const { login, loading } = useAuth();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    try {
      // Decide qual dado enviar com base no tipo de login selecionado
      const identifier = loginType === 'email' ? email : username;
      await login(identifier, password, loginType);
    } catch (err) {
      setError(err.message || 'Erro ao fazer login');
    }
  };

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-orange-50">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-orange-50 flex justify-items-center flex-col md:flex-row">
      <div className="w-full flex items-center justify-center p-6 bg-gradient-to-br from-orange-200 to-orange-600">
        <div className="bg-white rounded-2xl shadow-xl w-full max-w-md p-8">
          <div className="mb-6 relative">
            <img
              src="/logo_horizontal_sem_fundo.png"
              alt="High Tide Logo"
              className="max-w-full max-h-20"
            />
            <span className="absolute -bottom-1 right-3 text-xs text-gray-500 font-mono">{APP_VERSION}</span>
          </div>

          {error && (
            <div className="bg-red-50 text-red-500 p-3 rounded-lg text-sm mb-6">
              {error}
            </div>
          )}

          {/* Opções de login com abas */}
          <div className="flex mb-6 border-b border-gray-200">
            <button
              className={`flex-1 py-3 px-4 text-center font-medium text-sm transition-colors ${loginType === 'username'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-500 hover:text-gray-700'
                }`}
              onClick={() => setLoginType('username')}
            >
              <div className="flex items-center justify-center">
                <User size={16} className="mr-2" />
                <span>Entrar com Login</span>
              </div>
            </button>
            <button
              className={`flex-1 py-3 px-4 text-center font-medium text-sm transition-colors ${loginType === 'email'
                  ? 'border-b-2 border-orange-500 text-orange-600'
                  : 'text-gray-500 hover:text-gray-700'
                }`}
              onClick={() => setLoginType('email')}
            >
              <div className="flex items-center justify-center">
                <Mail size={16} className="mr-2" />
                <span>Entrar com Email</span>
              </div>
            </button>

          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Campo de Email (visível quando loginType === 'email') */}
            {loginType === 'email' && (
              <div>
                <label className="block text-sm font-medium text-gray-700" htmlFor="email">
                  Email
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    type="email"
                    required
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="block w-full text-gray-600 pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
            )}

            {/* Campo de Usuário (visível quando loginType === 'username') */}
            {loginType === 'username' && (
              <div>
                <label className="block text-sm font-medium text-gray-700" htmlFor="username">
                  Login
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                    <User className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="username"
                    type="text"
                    required
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    className="block w-full text-gray-600 pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Seu Login"
                  />
                </div>
              </div>
            )}

            {/* Campo de Senha (sempre visível) */}
            <div>
              <div className="flex justify-between">
                <label className="block text-sm font-medium text-gray-700" htmlFor="password">
                  Senha
                </label>
                <a href="/forgot-password" className="text-sm text-orange-500 hover:text-orange-600">
                  Esqueci a senha
                </a>
              </div>
              <div className="mt-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center">
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full text-gray-600 pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  placeholder="••••••••"
                />
                <button
                  type="button"
                  onClick={toggleShowPassword}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-gray-400" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
            </div>

            <button
              type="submit"
              className="w-full py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors"
            >
              Entrar
            </button>
          </form>

          <div className="mt-8 text-center">
            <Link
              href="/tutorial"
              className="text-sm text-gray-500 hover:text-orange-500 flex items-center justify-center"
            >
              <HelpCircle size={16} className="mr-1" />
              Precisa de ajuda? Acesse nossos tutoriais
            </Link>
          </div>

          <div className="mt-6 text-center text-xs text-gray-500">
            <p>
              Ao entrar, você concorda com nossos{' '}
              <a href="/terms" className="text-orange-500 hover:underline">
                Termos de Uso
              </a>{' '}
              e{' '}
              <a href="/privacy" className="text-orange-500 hover:underline">
                Política de Privacidade
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}