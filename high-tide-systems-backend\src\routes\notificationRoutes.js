const express = require('express');
const router = express.Router();
const { authenticate } = require('../middlewares/auth');
const { cacheMiddleware, clearCacheMiddleware } = require('../middlewares/cache');
const { NotificationController } = require('../controllers/notificationController');

// Configurar TTL para cache de notificações (1 minuto)
const NOTIFICATION_CACHE_TTL = 60;

// Rota pública para confirmar agendamento via link no email
router.get('/confirm/:token', NotificationController.confirmAppointment);

// Rota pública para cancelar agendamento via link no email
router.get('/cancel/:token', NotificationController.cancelAppointment);

// Rotas protegidas por autenticação

// Rota para contagem de notificações não lidas (com cache)
router.get('/count', authenticate, cacheMiddleware('notifications:count', NOTIFICATION_CACHE_TTL), NotificationController.getUnreadCount);

// Rota para listar notificações (sem cache, pois muda frequentemente)
router.get('/', authenticate, NotificationController.list);

// Rota para testar envio de emails
router.post('/test-email', authenticate, NotificationController.testEmail);

// Rota para enviar lembrete manualmente
router.post('/send-reminder/:id', authenticate, NotificationController.sendManualReminder);

// Rotas para marcar notificações como lidas (limpa cache)
router.put('/read/:id', authenticate, clearCacheMiddleware('notifications:*'), NotificationController.markAsRead);
router.put('/read-all', authenticate, clearCacheMiddleware('notifications:*'), NotificationController.markAllAsRead);

module.exports = router;