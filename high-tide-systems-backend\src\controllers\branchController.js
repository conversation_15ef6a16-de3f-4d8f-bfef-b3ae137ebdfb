const { validationResult } = require('express-validator');
const { body } = require('express-validator');
const prisma = require('../utils/prisma');
const BranchWorkingHoursHelper = require('../utils/branchWorkingHoursHelper');

// Validations
const createBranchValidation = [
  body('name').notEmpty().withMessage('Nome é obrigatório'),
  body('address').notEmpty().withMessage('Endereço é obrigatório'),
  body('companyId').notEmpty().withMessage('Empresa é obrigatória'),
  body('code').optional().isString().withMessage('Código deve ser uma string'),
  body('phone').optional().matches(/^\d{10,11}$/).withMessage('Telefone inválido'),
];

class BranchController {
  /**
   * Get default working hours for a branch
   */
  static async getDefaultWorkingHours(req, res) {
    try {
      const { id } = req.params;

      // Check if branch exists
      const branch = await prisma.branch.findUnique({
        where: { id }
      });

      if (!branch) {
        return res.status(404).json({ message: 'Unidade não encontrada' });
      }

      // Check if user has access to this branch
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && branch.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta unidade' });
      }

      // If branch has no default working hours, generate them
      const defaultWorkingHours = branch.defaultWorkingHours || BranchWorkingHoursHelper.generateDefaultWorkingHours();

      res.json({
        branchId: id,
        defaultWorkingHours
      });
    } catch (error) {
      console.error('Erro ao buscar horários de trabalho padrão:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Apply default working hours from a branch to all users in that branch
   */
  static async applyWorkingHoursToUsers(req, res) {
    try {
      const { id } = req.params;

      // Check if branch exists
      const branch = await prisma.branch.findUnique({
        where: { id }
      });

      if (!branch) {
        return res.status(404).json({ message: 'Unidade não encontrada' });
      }

      // Check if user has access to this branch
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && branch.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta unidade' });
      }

      // Check if branch has default working hours
      if (!branch.defaultWorkingHours) {
        return res.status(400).json({ message: 'Esta unidade não possui horários de trabalho padrão definidos' });
      }

      // Apply working hours to all users in the branch
      const result = await BranchWorkingHoursHelper.applyBranchWorkingHoursToAllUsers(id);

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'APPLY_WORKING_HOURS',
          entityType: 'Branch',
          entityId: id,
          details: { result },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: branch.companyId
        }
      });

      res.json(result);
    } catch (error) {
      console.error('Erro ao aplicar horários de trabalho aos usuários:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Creates a new branch
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        name,
        code,
        description,
        address,
        neighborhood,
        city,
        state,
        postalCode,
        phone,
        email,
        companyId,
        isHeadquarters
      } = req.body;

      // Check if company exists
      const company = await prisma.company.findUnique({
        where: { id: companyId }
      });

      if (!company) {
        return res.status(404).json({ message: 'Empresa não encontrada' });
      }

      // Check if code is already used in this company
      if (code) {
        const existingBranch = await prisma.branch.findFirst({
          where: {
            companyId,
            code
          }
        });

        if (existingBranch) {
          return res.status(400).json({ message: 'Código já utilizado em outra unidade desta empresa' });
        }
      }

      // If this is set as headquarters, update any existing headquarters
      if (isHeadquarters) {
        await prisma.branch.updateMany({
          where: {
            companyId,
            isHeadquarters: true
          },
          data: {
            isHeadquarters: false
          }
        });
      }

      // Generate default working hours if not provided
      const defaultWorkingHours = req.body.defaultWorkingHours || BranchWorkingHoursHelper.generateDefaultWorkingHours();

      const branch = await prisma.branch.create({
        data: {
          name,
          code,
          description,
          address,
          neighborhood,
          city,
          state,
          postalCode,
          phone,
          email,
          companyId,
          isHeadquarters: isHeadquarters || false,
          defaultWorkingHours
        }
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'CREATE',
          entityType: 'Branch',
          entityId: branch.id,
          details: { requestData: req.body },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: branch.companyId
        }
      });

      res.status(201).json(branch);
    } catch (error) {
      console.error('Erro ao criar unidade:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Updates a branch
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const {
        name,
        code,
        description,
        address,
        neighborhood,
        city,
        state,
        postalCode,
        phone,
        email,
        isHeadquarters
      } = req.body;

      // Check if branch exists
      const existingBranch = await prisma.branch.findUnique({
        where: { id }
      });

      if (!existingBranch) {
        return res.status(404).json({ message: 'Unidade não encontrada' });
      }

      // Check if code is already used in this company (if changed)
      if (code && code !== existingBranch.code) {
        const branchWithCode = await prisma.branch.findFirst({
          where: {
            companyId: existingBranch.companyId,
            code,
            id: { not: id }
          }
        });

        if (branchWithCode) {
          return res.status(400).json({ message: 'Código já utilizado em outra unidade desta empresa' });
        }
      }

      // If this is set as headquarters, update any existing headquarters
      if (isHeadquarters && !existingBranch.isHeadquarters) {
        await prisma.branch.updateMany({
          where: {
            companyId: existingBranch.companyId,
            isHeadquarters: true
          },
          data: {
            isHeadquarters: false
          }
        });
      }

      // Get defaultWorkingHours from request or keep existing
      const defaultWorkingHours = req.body.defaultWorkingHours !== undefined
        ? req.body.defaultWorkingHours
        : existingBranch.defaultWorkingHours;

      const branch = await prisma.branch.update({
        where: { id },
        data: {
          name,
          code,
          description,
          address,
          neighborhood,
          city,
          state,
          postalCode,
          phone,
          email,
          isHeadquarters: isHeadquarters !== undefined ? isHeadquarters : existingBranch.isHeadquarters,
          defaultWorkingHours
        }
      });

      // If working hours were updated, apply to all users in this branch
      if (req.body.defaultWorkingHours !== undefined && req.body.applyToUsers === true) {
        try {
          await BranchWorkingHoursHelper.applyBranchWorkingHoursToAllUsers(id);
        } catch (error) {
          console.error('Error applying working hours to users:', error);
          // We don't want to fail the whole request if this fails
        }
      }

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Branch',
          entityId: branch.id,
          details: { requestData: req.body },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: branch.companyId
        }
      });

      res.json(branch);
    } catch (error) {
      console.error('Erro ao atualizar unidade:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Lists branches with pagination and filters
   */
  static async list(req, res) {
    try {
      const { page = 1, limit = 10, search, active, companyId } = req.query;

      const where = {
        AND: [
          search
            ? {
                OR: [
                  { name: { contains: search, mode: 'insensitive' } },
                  { code: { contains: search, mode: 'insensitive' } },
                  { address: { contains: search, mode: 'insensitive' } },
                ],
              }
            : {},
          active !== undefined ? { active: active === 'true' } : {},
          companyId ? { companyId } : {},
          // If user is not system admin, limit to their company
          req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId
            ? { companyId: req.user.companyId }
            : {},
        ],
      };

      const [branches, total] = await Promise.all([
        prisma.branch.findMany({
          where,
          skip: (Number(page) - 1) * Number(limit),
          take: Number(limit),
          orderBy: { name: 'asc' },
          include: {
            company: {
              select: {
                id: true,
                name: true
              }
            },
            _count: {
              select: {
                locations: true
              }
            }
          }
        }),
        prisma.branch.count({ where }),
      ]);

      res.json({
        branches,
        total,
        pages: Math.ceil(total / Number(limit)),
      });
    } catch (error) {
      console.error('Erro ao listar unidades:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Gets a single branch by ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const branch = await prisma.branch.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          },
          locations: {
            where: {
              active: true,
              deletedAt: null
            },
            select: {
              id: true,
              name: true,
              address: true,
              phone: true,
              active: true
            }
          }
        }
      });

      if (!branch) {
        return res.status(404).json({ message: 'Unidade não encontrada' });
      }

      // Check if user has access to this branch
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && branch.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta unidade' });
      }

      res.json(branch);
    } catch (error) {
      console.error('Erro ao buscar unidade:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Toggles the active status of a branch
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      const branch = await prisma.branch.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true
            }
          }
        }
      });

      if (!branch) {
        return res.status(404).json({ message: 'Unidade não encontrada' });
      }

      // Check if user has access to this branch
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && branch.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta unidade' });
      }

      const updatedBranch = await prisma.branch.update({
        where: { id },
        data: { active: !branch.active },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'TOGGLE_STATUS',
          entityType: 'Branch',
          entityId: id,
          details: { newStatus: updatedBranch.active },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: branch.companyId
        }
      });

      res.json(updatedBranch);
    } catch (error) {
      console.error('Erro ao alterar status da unidade:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Soft deletes a branch
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      const branch = await prisma.branch.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              locations: {
                where: {
                  active: true,
                  deletedAt: null
                }
              }
            }
          }
        }
      });

      if (!branch) {
        return res.status(404).json({ message: 'Unidade não encontrada' });
      }

      // Check if user has access to this branch
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && branch.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta unidade' });
      }

      // Check if branch has active locations
      if (branch._count.locations > 0) {
        return res.status(400).json({
          message: 'Não é possível excluir uma unidade com locais ativos. Desative ou remova os locais primeiro.'
        });
      }

      await prisma.branch.update({
        where: { id },
        data: {
          active: false,
          deletedAt: new Date()
        }
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'DELETE',
          entityType: 'Branch',
          entityId: id,
          details: { softDelete: true },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: branch.companyId
        }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Erro ao deletar unidade:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  BranchController,
  createBranchValidation,
};