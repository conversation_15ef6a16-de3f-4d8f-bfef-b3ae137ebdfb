"use client";

import { api } from './api';

/**
 * Utilitário para gerenciar redirecionamentos de módulos com base nas preferências do usuário
 * e se é a primeira visita ao módulo.
 */

/**
 * Verifica se é a primeira visita do usuário ao módulo
 * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')
 * @returns {boolean} - Retorna true se for a primeira visita, false caso contrário
 */
export const isFirstVisit = (moduleId) => {
  if (typeof window === 'undefined') return true;

  try {
    const visitedModules = JSON.parse(localStorage.getItem('visitedModules') || '{}');
    return !visitedModules[moduleId];
  } catch (error) {
    console.error('Erro ao verificar primeira visita:', error);
    return true;
  }
};

/**
 * Marca um módulo como visitado
 * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')
 */
export const markModuleAsVisited = (moduleId) => {
  if (typeof window === 'undefined') return;

  try {
    const visitedModules = JSON.parse(localStorage.getItem('visitedModules') || '{}');
    visitedModules[moduleId] = true;
    localStorage.setItem('visitedModules', JSON.stringify(visitedModules));
  } catch (error) {
    console.error('Erro ao marcar módulo como visitado:', error);
  }
};

/**
 * Obtém a página inicial preferida do usuário para um módulo específico
 * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')
 * @returns {string} - Caminho da página inicial preferida
 */
export const getPreferredLandingPage = (moduleId) => {
  if (typeof window === 'undefined') return null;

  try {
    // Primeiro tenta obter do localStorage para evitar chamadas API desnecessárias
    const localPreferences = JSON.parse(localStorage.getItem('modulePreferences') || '{}');
    return localPreferences[moduleId] || null;
  } catch (error) {
    console.error('Erro ao obter página inicial preferida do localStorage:', error);
    return null;
  }
};

/**
 * Define a página inicial preferida do usuário para um módulo específico
 * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')
 * @param {string} pagePath - Caminho da página inicial preferida
 * @returns {Promise} - Promise que resolve quando a operação é concluída
 */
export const setPreferredLandingPage = async (moduleId, pagePath) => {
  if (typeof window === 'undefined') return;

  try {
    // Primeiro, atualiza o localStorage para feedback imediato
    const localPreferences = JSON.parse(localStorage.getItem('modulePreferences') || '{}');
    localPreferences[moduleId] = pagePath;
    localStorage.setItem('modulePreferences', JSON.stringify(localPreferences));

    // Depois, atualiza no backend
    try {
      // Obter as preferências atuais do backend
      const response = await api.get('/module-preferences');

      // Verificar o formato da resposta
      let serverPreferences;
      if (response.data && response.data.success && response.data.data) {
        // Formato com wrapper de sucesso
        serverPreferences = response.data.data;
      } else {
        // Formato direto
        serverPreferences = response.data || {};
      }

      console.log('Preferências extraídas da resposta (moduleRedirection):', serverPreferences);

      // Atualizar a preferência do módulo
      serverPreferences[moduleId] = pagePath;

      // Enviar as preferências atualizadas para o backend
      await api.put('/module-preferences', {
        modulePreferences: serverPreferences
      });

      console.log(`Preferência de módulo ${moduleId} atualizada com sucesso no backend`);
    } catch (apiError) {
      console.error('Erro ao atualizar preferências no backend:', apiError);
      // Não falhar a operação se o backend não estiver disponível
    }
  } catch (error) {
    console.error('Erro ao definir página inicial preferida:', error);
  }
};

/**
 * Obtém o caminho de redirecionamento para um módulo com base nas preferências do usuário
 * e se é a primeira visita
 * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')
 * @param {string} introductionPath - Caminho da página de introdução
 * @param {string} defaultPath - Caminho padrão para redirecionamento
 * @returns {string} - Caminho para redirecionamento
 */
export const getModuleRedirectionPath = (moduleId, introductionPath, defaultPath) => {
  // Se for a primeira visita, redireciona para a introdução
  if (isFirstVisit(moduleId)) {
    markModuleAsVisited(moduleId);
    return introductionPath;
  }

  // Verifica se há uma página inicial preferida
  const preferredPage = getPreferredLandingPage(moduleId);
  if (preferredPage) {
    return preferredPage;
  }

  // Caso contrário, usa o caminho padrão
  return defaultPath;
};

/**
 * Obtém as opções de páginas disponíveis para um módulo
 * @param {string} moduleId - ID do módulo
 * @returns {Array} - Array de objetos com as opções de páginas
 */
export const getModulePageOptions = (moduleId) => {
  switch (moduleId) {
    case 'admin':
      return [
        { value: '/dashboard/admin/introduction', label: 'Introdução' },
        { value: '/dashboard/admin/dashboard', label: 'Dashboard' },
        { value: '/dashboard/admin/users', label: 'Usuários' },
        { value: '/dashboard/admin/professions', label: 'Profissões' },
        { value: '/dashboard/admin/logs', label: 'Logs' },
        { value: '/dashboard/admin/settings', label: 'Configurações' }
      ];
    case 'scheduler':
      return [
        { value: '/dashboard/scheduler/introduction', label: 'Introdução' },
        { value: '/dashboard/scheduler/calendar', label: 'Calendário' },
        { value: '/dashboard/scheduler/working-hours', label: 'Horários de Trabalho' },
        { value: '/dashboard/scheduler/service-types', label: 'Tipos de Serviço' },
        { value: '/dashboard/scheduler/locations', label: 'Locais' },
        { value: '/dashboard/scheduler/appointments-report', label: 'Relatório' }
      ];
    case 'people':
      return [
        { value: '/dashboard/people/clients', label: 'Clientes' },
        { value: '/dashboard/people/persons', label: 'Pessoas' }
      ];
    case 'financial':
      return [
        { value: '/dashboard/financial/invoices', label: 'Faturas' },
        { value: '/dashboard/financial/payments', label: 'Pagamentos' }
      ];
    default:
      return [];
  }
};
