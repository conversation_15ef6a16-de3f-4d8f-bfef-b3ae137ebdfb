import React from 'react';
import { format, parseISO } from 'date-fns';
import { Clock, Calendar } from 'lucide-react';
import { ModuleInput, ModuleFormGroup } from '@/components/ui';

const AppointmentDates = ({ formData, setFormData }) => {
  // Função para formatar a data para exibição no input
  const formatDateForInput = (date) => {
    if (!date) return "";

    // Converter para objeto Date se for string ISO
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Formatar para o formato esperado pelo input datetime-local, usando o horário LOCAL
    const year = dateObj.getFullYear();
    const month = String(dateObj.getMonth() + 1).padStart(2, '0');
    const day = String(dateObj.getDate()).padStart(2, '0');
    const hours = String(dateObj.getHours()).padStart(2, '0');
    const minutes = String(dateObj.getMinutes()).padStart(2, '0');

    // Formato: YYYY-MM-DDTHH:MM (formato esperado pelo input datetime-local)
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  // Removido inputBaseClasses pois agora usamos o ModuleInput

  return (
    <div>
      <h3 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mb-2 flex items-center gap-2">
        <Clock className="w-4 h-4" />
        Período do Agendamento
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ModuleFormGroup
          moduleColor="scheduler"
          label="Data e Hora Inicial"
          htmlFor="startDate"
          icon={<Calendar size={16} />}
        >
          <ModuleInput
            moduleColor="scheduler"
            type="datetime-local"
            id="startDate"
            name="startDate"
            value={formatDateForInput(formData.startDate)}
            className="text-base py-2"
            onChange={(e) => {
              // Obter o valor do input (formato: "YYYY-MM-DDTHH:MM")
              const inputValue = e.target.value;

              // Criar um objeto Date a partir do valor do input (horário local)
              const localStartDate = new Date(inputValue);

              // SEMPRE calcular a data de término como 60 minutos depois da data de início
              const localEndDate = new Date(localStartDate.getTime() + 60 * 60 * 1000);
              console.log(`[DATE-CHANGE-DEBUG] FORÇANDO duração de 60 minutos ao alterar data de início`);
              console.log(`[DATE-CHANGE-DEBUG] Duração forçada (minutos): ${(localEndDate - localStartDate) / (60 * 1000)}`);

              console.log(`[DATE-CHANGE] Input: ${inputValue}`);
              console.log(`[DATE-CHANGE] Data local de início: ${localStartDate.toLocaleString()}`);
              console.log(`[DATE-CHANGE] Data local de término: ${localEndDate.toLocaleString()}`);

              // Converter para strings ISO para armazenamento interno
              // Importante: toISOString() converte para UTC, mas é apenas para armazenamento interno
              const startISOString = localStartDate.toISOString();
              const endISOString = localEndDate.toISOString();

              console.log(`[DATE-CHANGE] Start ISO (UTC): ${startISOString}`);
              console.log(`[DATE-CHANGE] End ISO (UTC): ${endISOString}`);

              setFormData({
                ...formData,
                startDate: startISOString,
                endDate: endISOString,
              });
            }}
            required
          />
        </ModuleFormGroup>

        <ModuleFormGroup
          moduleColor="scheduler"
          label="Data e Hora Final"
          htmlFor="endDate"
          icon={<Calendar size={16} />}
        >
          <ModuleInput
            moduleColor="scheduler"
            type="datetime-local"
            id="endDate"
            name="endDate"
            value={formatDateForInput(formData.endDate)}
            className="text-base py-2"
            onChange={(e) => {
              // Obter o valor do input (formato: "YYYY-MM-DDTHH:MM")
              const inputValue = e.target.value;

              // Criar um objeto Date a partir do valor do input (horário local)
              const localEndDate = new Date(inputValue);

              console.log(`[DATE-CHANGE] End Input: ${inputValue}`);
              console.log(`[DATE-CHANGE] Data local de término: ${localEndDate.toLocaleString()}`);

              // Converter para string ISO para armazenamento interno
              // Importante: toISOString() converte para UTC, mas é apenas para armazenamento interno
              const endISOString = localEndDate.toISOString();

              console.log(`[DATE-CHANGE] End ISO (UTC): ${endISOString}`);

              setFormData({ ...formData, endDate: endISOString });
            }}
            required
          />
        </ModuleFormGroup>
      </div>
    </div>
  );
};

export default AppointmentDates;