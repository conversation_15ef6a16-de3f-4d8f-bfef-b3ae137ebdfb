/**
 * Formata data e hora
 * @param {string} dateTime - Data ISO string
 * @returns {Object} - Objeto com data, hora e dia da semana formatados
 */
export const formatDateTime = (dateTime) => {
  const date = new Date(dateTime);
  return {
    date: date.toLocaleDateString('pt-BR'),
    time: date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
    weekday: date.toLocaleDateString('pt-BR', { weekday: 'short' }).replace('.', '')
  };
};

/**
 * Determina se a data é hoje, amanhã ou futura
 * @param {string} dateTime - Data ISO string
 * @returns {Object} - Objeto com texto e classe CSS
 */
export const getTimeFrame = (dateTime) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  const appointmentDate = new Date(dateTime);
  appointmentDate.setHours(0, 0, 0, 0);

  if (appointmentDate.getTime() === today.getTime()) {
    return { text: 'Hoje', class: 'bg-orange-500 text-white' };
  } else if (appointmentDate.getTime() === tomorrow.getTime()) {
    return { text: 'Amanhã', class: 'bg-orange-500 text-white' };
  } else {
    return { text: 'Em breve', class: 'bg-orange-500 text-white' };
  }
};

/**
 * Retorna a saudação baseada na hora do dia
 * @returns {string} - Saudação (Bom dia, Boa tarde, Boa noite)
 */
export const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour < 12) return 'Bom dia';
  if (hour < 18) return 'Boa tarde';
  return 'Boa noite';
};

/**
 * Formata a data atual com primeira letra maiúscula
 * @param {Date} date - Data a ser formatada (padrão: data atual)
 * @returns {string} - Data formatada
 */
export const formatDate = (date = new Date()) => {
  const formattedDate = date.toLocaleDateString('pt-BR', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });
  return formattedDate.charAt(0).toUpperCase() + formattedDate.slice(1);
};

/**
 * Retorna cores baseadas na hora do dia para combinar com o TimeIcon
 * @returns {Object} - Objeto com cores para borda, ícone e sombra
 */
export const getTimeBasedColors = () => {
  const hour = new Date().getHours();

  // Madrugada (0h às 5h) - Noite - Lua (Indigo)
  if (hour < 6) {
    return {
      border: 'border-indigo-300',
      text: 'text-indigo-600',
      shadow: 'shadow-indigo-100/50',
      boxShadow: '0 10px 15px -3px rgba(199, 210, 254, 0.3), 0 4px 6px -4px rgba(199, 210, 254, 0.4)'
    };
  }
  // Manhã (6h às 11h) - Sol da manhã (Amber)
  else if (hour < 12) {
    return {
      border: 'border-amber-300',
      text: 'text-amber-600',
      shadow: 'shadow-amber-100/50',
      boxShadow: '0 10px 15px -3px rgba(254, 215, 170, 0.3), 0 4px 6px -4px rgba(254, 215, 170, 0.4)'
    };
  }
  // Tarde (12h às 17h) - Sol da tarde (Orange)
  else if (hour < 18) {
    return {
      border: 'border-orange-300',
      text: 'text-orange-600',
      shadow: 'shadow-orange-100/50',
      boxShadow: '0 10px 15px -3px rgba(254, 215, 170, 0.3), 0 4px 6px -4px rgba(254, 215, 170, 0.4)'
    };
  }
  // Noite (18h às 23h) - Lua (Indigo)
  else {
    return {
      border: 'border-indigo-300',
      text: 'text-indigo-600',
      shadow: 'shadow-indigo-100/50',
      boxShadow: '0 10px 15px -3px rgba(199, 210, 254, 0.3), 0 4px 6px -4px rgba(199, 210, 254, 0.4)'
    };
  }
};

/**
 * Converte uma data UTC para o fuso horário local para exibição
 * @param {Date|string} utcDate - Data em UTC
 * @returns {Date} Data convertida para o fuso horário local
 */
export const utcToLocal = (utcDate) => {
  if (!utcDate) return null;

  // Criar uma nova data a partir da data UTC
  const date = new Date(utcDate);

  // Sempre aplicar a conversão de fuso horário, independentemente do formato da data
  // Isso garante que a data seja sempre exibida no fuso horário local do usuário
  const offsetMinutes = date.getTimezoneOffset();

  // Aplicar o offset para converter de UTC para o horário local
  // Multiplicamos por -1 porque getTimezoneOffset() retorna o valor inverso do que precisamos
  return new Date(date.getTime() - offsetMinutes * 60000);
};

/**
 * Formata o horário para exibição consistente
 * @param {Date|string} date - Data a ser formatada
 * @returns {string} Horário formatado (HH:MM)
 */
export const formatTime = (date) => {
  const d = new Date(date);
  return `${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`;
};

/**
 * Converte uma data local para UTC para envio ao servidor
 * @param {Date|string} localDate - Data no fuso horário local
 * @returns {Date} Data convertida para UTC
 */
export const localToUTC = (localDate) => {
  if (!localDate) return null;

  // Criar uma nova data a partir da data local
  const date = new Date(localDate);

  // Criar uma nova data UTC usando os componentes da data local
  // Isso garante que a data seja interpretada como UTC, sem ajustes automáticos de fuso horário
  const utcDate = new Date(Date.UTC(
    date.getFullYear(),
    date.getMonth(),
    date.getDate(),
    date.getHours(),
    date.getMinutes(),
    date.getSeconds()
  ));

  console.log(`[localToUTC] Local: ${date.toISOString()} -> UTC: ${utcDate.toISOString()}`);

  return utcDate;
};

/**
 * Verifica se uma data é válida
 * @param {Date|string} date - Data a ser verificada
 * @returns {boolean} - Verdadeiro se a data for válida
 */
export const isValidDate = (date) => {
  if (!date) return false;
  const d = new Date(date);
  return !isNaN(d.getTime());
};
