# Implementação de Cache Redis

Este documento descreve a implementação do Redis como sistema de cache na aplicação High Tide Systems.

## Áreas com Cache Implementado

### 1. Agendamentos
- **Arquivo**: `src/routes/scheduling/schedulingRoutes.js`
- **TTL**: 2 minutos (120 segundos)
- **Rotas com Cache**:
  - `GET /` - Listagem de agendamentos
  - `GET /dashboard` - Dashboard de agendamentos
  - `GET /:id` - Detalhes de um agendamento
- **Invalidação de Cache**:
  - Quando um agendamento é criado, atualizado ou excluído

### 2. Clientes
- **Arquivo**: `src/routes/person/clientRoutes.js`
- **TTL**: 5 minutos (300 segundos)
- **Rotas com Cache**:
  - `GET /` - Listagem de clientes
  - `GET /:id` - Perfil de um cliente
- **Invalidação de Cache**:
  - Quando um cliente é criado, atualizado, excluído ou tem seu status alterado

### 3. Relatórios
- **Arquivo**: `src/routes/reports/reportRoutes.js`
- **TTL**: 15 minutos (900 segundos)
- **Rotas com Cache**:
  - `GET /sales` - Relatório de vendas
  - `GET /performance` - Relatório de desempenho
  - `GET /customers` - Relatório de clientes
  - `GET /appointments` - Relatório de agendamentos

### 4. Configurações do Sistema
- **Arquivo**: `src/routes/admin/settingsRoutes.js`
- **TTL**: 1 hora (3600 segundos)
- **Rotas com Cache**:
  - `GET /` - Todas as configurações
  - `GET /general` - Configurações gerais
  - `GET /security` - Configurações de segurança
  - `GET /backup` - Configurações de backup
  - `GET /:key` - Configuração específica
- **Invalidação de Cache**:
  - Quando uma configuração é atualizada

### 5. Notificações
- **Arquivo**: `src/routes/notificationRoutes.js`
- **TTL**: 1 minuto (60 segundos)
- **Rotas com Cache**:
  - `GET /count` - Contagem de notificações não lidas
- **Invalidação de Cache**:
  - Quando notificações são marcadas como lidas

### 6. Tipos de Serviço
- **Arquivo**: `src/routes/scheduling/serviceTypeRoutes.js`
- **TTL**: 10 minutos (600 segundos)
- **Rotas com Cache**:
  - `GET /` - Listagem de tipos de serviço
  - `GET /:id` - Detalhes de um tipo de serviço
- **Invalidação de Cache**:
  - Quando um tipo de serviço é criado, atualizado ou excluído

### 7. Dashboard Administrativo
- **Arquivo**: `src/routes/admin/adminDashboardRoutes.js`
- **TTL**: 5 minutos (300 segundos)
- **Rotas com Cache**:
  - `GET /all` - Todos os dados do dashboard
  - `GET /stats` - Estatísticas
  - `GET /activity` - Dados de atividade
  - `GET /module-distribution` - Distribuição de módulos
  - `GET /active-users` - Usuários ativos
  - `GET /recent-activity` - Atividade recente

## Padrões de Chave de Cache

Os padrões de chave seguem uma convenção consistente:

- `{recurso}:{tipo}` - Para listagens (ex: `clients:list`)
- `{recurso}:{tipo}:{id}` - Para detalhes (ex: `clients:detail:123`)
- `{recurso}:*` - Para invalidação de cache (ex: `clients:*`)

## Testes

Os testes para o sistema de cache estão localizados em `tests/cache/`:

- `redis-basic.js` - Teste básico da conexão e operações do Redis
- `redis-simple.js` - Teste simplificado do Redis
- `middleware.js` - Teste do middleware de cache
- `cache-simple.js` - Teste do serviço de cache

## Próximos Passos

1. **Monitoramento de Cache**: Implementar métricas para monitorar a taxa de acertos/erros do cache
2. **Cache Warming**: Pré-carregar dados frequentemente acessados no cache
3. **Compressão de Cache**: Implementar compressão para respostas grandes
4. **Circuit Breaker**: Adicionar padrão de circuit breaker para falhas de conexão com o Redis
