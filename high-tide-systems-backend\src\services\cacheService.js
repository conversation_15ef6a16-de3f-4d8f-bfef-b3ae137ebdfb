// src/services/cacheService.js
let redis;
try {
  redis = require('redis');
} catch (error) {
  console.warn('Módulo Redis não encontrado, usando mock em memória');
  // Mock do Redis para desenvolvimento
  redis = {
    createClient: () => ({
      connect: async () => {},
      quit: async () => {},
      on: (event, callback) => {},
      set: async () => 'OK',
      get: async () => null,
      del: async () => 1,
      scan: async () => ({ cursor: 0, keys: [] })
    })
  };
}

class CacheService {
  constructor() {
    this.client = null;
    this.connected = false;
    this.defaultTTL = 3600; // 1 hora em segundos
  }

  /**
   * Inicializa a conexão com o Redis
   */
  async initialize() {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

      this.client = redis.createClient({
        url: redisUrl
      });

      // Configurar handlers de eventos
      this.client.on('error', (err) => {
        console.error('Erro no Redis:', err);
        this.connected = false;
      });

      this.client.on('connect', () => {
        console.log('Conectado ao Redis');
        this.connected = true;
      });

      this.client.on('reconnecting', () => {
        console.log('Reconectando ao Redis...');
      });

      // Conectar ao Redis
      await this.client.connect();

      return { success: true };
    } catch (error) {
      console.error('Erro ao inicializar o serviço de cache:', error);
      this.connected = false;
      return { success: false, error: error.message };
    }
  }

  /**
   * Fecha a conexão com o Redis
   */
  async close() {
    if (this.client) {
      await this.client.quit();
      this.connected = false;
      console.log('Conexão com Redis fechada');
    }
  }

  /**
   * Gera uma chave de cache baseada nos parâmetros
   * @param {string} prefix - Prefixo da chave (geralmente o nome do recurso)
   * @param {Object} params - Parâmetros para gerar a chave
   * @returns {string} - Chave formatada
   */
  generateKey(prefix, params = {}) {
    // Converter parâmetros em string ordenada para garantir consistência
    const paramsStr = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null)
      .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
      .map(([key, value]) => `${key}:${value}`)
      .join('|');

    return `${prefix}:${paramsStr || 'default'}`;
  }

  /**
   * Armazena um valor no cache
   * @param {string} key - Chave do cache
   * @param {any} value - Valor a ser armazenado
   * @param {number} ttl - Tempo de vida em segundos (opcional)
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async set(key, value, ttl = this.defaultTTL) {
    if (!this.connected || !this.client) return false;

    try {
      // Converter valor para JSON
      const valueStr = JSON.stringify(value);

      // Armazenar no Redis com TTL
      await this.client.set(key, valueStr, { EX: ttl });

      return true;
    } catch (error) {
      console.error(`Erro ao armazenar no cache (${key}):`, error);
      return false;
    }
  }

  /**
   * Recupera um valor do cache
   * @param {string} key - Chave do cache
   * @returns {Promise<any>} - Valor armazenado ou null se não encontrado
   */
  async get(key) {
    if (!this.connected || !this.client) return null;

    try {
      const value = await this.client.get(key);

      if (!value) return null;

      // Converter de volta para objeto
      return JSON.parse(value);
    } catch (error) {
      console.error(`Erro ao recuperar do cache (${key}):`, error);
      return null;
    }
  }

  /**
   * Remove um valor do cache
   * @param {string} key - Chave do cache
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async delete(key) {
    if (!this.connected || !this.client) return false;

    try {
      await this.client.del(key);
      return true;
    } catch (error) {
      console.error(`Erro ao remover do cache (${key}):`, error);
      return false;
    }
  }

  /**
   * Limpa o cache com base em um padrão de chave
   * @param {string} pattern - Padrão de chave (ex: "users:*")
   * @returns {Promise<boolean>} - Resultado da operação
   */
  async clear(pattern) {
    if (!this.connected || !this.client) return false;

    try {
      // Usar SCAN para encontrar chaves que correspondem ao padrão
      let cursor = 0;
      do {
        const result = await this.client.scan(cursor, {
          MATCH: pattern,
          COUNT: 100
        });

        cursor = result.cursor;

        // Se houver chaves, excluí-las
        if (result.keys.length > 0) {
          await this.client.del(result.keys);
        }
      } while (cursor !== 0);

      return true;
    } catch (error) {
      console.error(`Erro ao limpar cache (${pattern}):`, error);
      return false;
    }
  }

  /**
   * Recupera um valor do cache ou executa a função para obtê-lo
   * @param {string} key - Chave do cache
   * @param {Function} fn - Função para obter o valor se não estiver em cache
   * @param {number} ttl - Tempo de vida em segundos (opcional)
   * @returns {Promise<any>} - Valor do cache ou da função
   */
  async getOrSet(key, fn, ttl = this.defaultTTL) {
    // Tentar obter do cache primeiro
    const cachedValue = await this.get(key);

    if (cachedValue !== null) {
      return cachedValue;
    }

    try {
      // Executar função para obter o valor
      const value = await fn();

      // Armazenar no cache se o valor não for null ou undefined
      if (value !== null && value !== undefined) {
        await this.set(key, value, ttl);
      }

      return value;
    } catch (error) {
      console.error(`Erro ao executar função para cache (${key}):`, error);
      throw error;
    }
  }

  /**
   * Retorna o cliente Redis para uso em outros serviços
   * @returns {Object} - Cliente Redis
   */
  getClient() {
    return this.client;
  }
}

// Exportar instância única
module.exports = new CacheService();
