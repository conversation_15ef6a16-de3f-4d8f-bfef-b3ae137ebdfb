// tests/controllers/company-controller-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Dados para testes
const testCompany = {
  name: `Test Company ${Date.now()}`,
  tradingName: 'Test Trading Name',
  legalName: 'Test Legal Name Ltd',
  cnpj: `${Date.now()}`.substring(0, 14),
  phone: '1234567890',
  address: 'Test Address',
  city: 'Test City',
  state: 'TS',
  postalCode: '12345-678',
  website: 'https://example.com',
  primaryColor: '#FF0000',
  secondaryColor: '#00FF00',
  description: 'Test company description'
};

// Variáveis para armazenar IDs criados durante os testes
let companyId;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do controlador de empresas...');
  
  try {
    // Teste 1: Listar empresas
    console.log('\n1. Listando empresas...');
    const listResponse = await api.get('/companies');
    
    if (listResponse.status === 200) {
      console.log(`✅ ${listResponse.data.length} empresas encontradas`);
    } else {
      console.log('❌ Falha ao listar empresas');
    }
    
    // Teste 2: Criar uma nova empresa
    console.log('\n2. Criando empresa...');
    const createResponse = await api.post('/companies', testCompany);
    
    if (createResponse.status === 201) {
      console.log('✅ Empresa criada com sucesso!');
      companyId = createResponse.data.id;
      console.log(`ID da empresa: ${companyId}`);
    } else {
      console.log('❌ Falha ao criar empresa');
      return;
    }
    
    // Teste 3: Obter empresa por ID
    console.log('\n3. Obtendo empresa por ID...');
    const getResponse = await api.get(`/companies/${companyId}`);
    
    if (getResponse.status === 200) {
      console.log('✅ Empresa obtida com sucesso!');
      console.log(`Nome: ${getResponse.data.name}`);
      console.log(`CNPJ: ${getResponse.data.cnpj}`);
    } else {
      console.log('❌ Falha ao obter empresa');
    }
    
    // Teste 4: Atualizar empresa
    console.log('\n4. Atualizando empresa...');
    const updateResponse = await api.put(`/companies/${companyId}`, {
      name: `${testCompany.name} Updated`,
      description: 'Updated description'
    });
    
    if (updateResponse.status === 200) {
      console.log('✅ Empresa atualizada com sucesso!');
      console.log(`Novo nome: ${updateResponse.data.name}`);
    } else {
      console.log('❌ Falha ao atualizar empresa');
    }
    
    // Teste 5: Desativar empresa
    console.log('\n5. Desativando empresa...');
    const toggleStatusResponse = await api.patch(`/companies/${companyId}/status`, {
      active: false
    });
    
    if (toggleStatusResponse.status === 200) {
      console.log('✅ Status da empresa alterado com sucesso!');
      console.log(`Status: ${toggleStatusResponse.data.active ? 'Ativa' : 'Inativa'}`);
    } else {
      console.log('❌ Falha ao alterar status da empresa');
    }
    
    // Teste 6: Excluir empresa
    console.log('\n6. Excluindo empresa...');
    const deleteResponse = await api.delete(`/companies/${companyId}`);
    
    if (deleteResponse.status === 200) {
      console.log('✅ Empresa excluída com sucesso!');
    } else {
      console.log('❌ Falha ao excluir empresa');
    }
    
    console.log('\n✅ Todos os testes do controlador de empresas concluídos com sucesso!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
