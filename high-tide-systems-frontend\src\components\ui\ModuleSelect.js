'use client';

import React, { forwardRef, useState, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import CustomSelect from './CustomSelect';

/**
 * Componente de select que se adapta à cor do módulo
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {string} props.placeholder - Placeholder do select
 * @param {string} props.value - Valor do select
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {boolean} props.disabled - Se o select está desabilitado
 * @param {boolean} props.required - Se o select é obrigatório
 * @param {string} props.name - Nome do select
 * @param {string} props.id - ID do select
 * @param {boolean} props.error - Se o select tem erro
 * @param {string} props.errorMessage - Mensagem de erro
 * @param {React.ReactNode} props.children - Opções do select
 * @param {string} props.className - Classes adicionais
 */
const ModuleSelect = forwardRef(({
  moduleColor = 'default',
  placeholder,
  value,
  onChange,
  disabled = false,
  required = false,
  name,
  id,
  error = false,
  errorMessage,
  children,
  className = '',
  ...rest
}, ref) => {
  // Estado para controlar se o select está aberto
  const [isOpen, setIsOpen] = useState(false);

  // Detectar quando o select é aberto ou fechado
  useEffect(() => {
    const handleSelectChange = () => {
      setIsOpen(false);
    };

    const handleSelectClick = () => {
      setIsOpen(!isOpen);
    };

    const selectElement = ref?.current || document.getElementById(id);
    if (selectElement) {
      selectElement.addEventListener('change', handleSelectChange);
      selectElement.addEventListener('click', handleSelectClick);
      selectElement.addEventListener('blur', () => setIsOpen(false));
    }

    return () => {
      if (selectElement) {
        selectElement.removeEventListener('change', handleSelectChange);
        selectElement.removeEventListener('click', handleSelectClick);
        selectElement.removeEventListener('blur', () => setIsOpen(false));
      }
    };
  }, [ref, id, isOpen]);

  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      focusRing: 'focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 focus-visible:ring-primary-500 focus-visible:border-primary-500 dark:focus-visible:ring-primary-400 dark:focus-visible:border-primary-400',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:ring-red-500 focus-visible:border-red-500 dark:focus-visible:ring-red-400 dark:focus-visible:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-gray-400 dark:text-gray-500',
      hoverBg: 'hover:bg-primary-50 dark:hover:bg-primary-900/20',
      hoverText: 'hover:text-primary-700 dark:hover:text-primary-300',
    },
    people: {
      focusRing: 'focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:ring-module-people-border focus-visible:border-module-people-border dark:focus-visible:ring-module-people-border-dark dark:focus-visible:border-module-people-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:ring-red-500 focus-visible:border-red-500 dark:focus-visible:ring-red-400 dark:focus-visible:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-people-icon dark:text-module-people-icon-dark',
      hoverBg: 'hover:bg-module-people-bg/10 dark:hover:bg-module-people-bg-dark/20',
      hoverText: 'hover:text-module-people-text dark:hover:text-module-people-text-dark',
    },
    scheduler: {
      focusRing: 'focus:ring-module-scheduler-border focus:border-module-scheduler-border dark:focus:ring-module-scheduler-border-dark dark:focus:border-module-scheduler-border-dark focus-visible:ring-module-scheduler-border focus-visible:border-module-scheduler-border dark:focus-visible:ring-module-scheduler-border-dark dark:focus-visible:border-module-scheduler-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:ring-red-500 focus-visible:border-red-500 dark:focus-visible:ring-red-400 dark:focus-visible:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark',
      hoverBg: 'hover:bg-module-scheduler-bg/10 dark:hover:bg-module-scheduler-bg-dark/20',
      hoverText: 'hover:text-module-scheduler-text dark:hover:text-module-scheduler-text-dark',
    },
    admin: {
      focusRing: 'focus:ring-module-admin-border focus:border-module-admin-border dark:focus:ring-module-admin-border-dark dark:focus:border-module-admin-border-dark focus-visible:ring-module-admin-border focus-visible:border-module-admin-border dark:focus-visible:ring-module-admin-border-dark dark:focus-visible:border-module-admin-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:ring-red-500 focus-visible:border-red-500 dark:focus-visible:ring-red-400 dark:focus-visible:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-admin-icon dark:text-module-admin-icon-dark',
      hoverBg: 'hover:bg-module-admin-bg/10 dark:hover:bg-module-admin-bg-dark/20',
      hoverText: 'hover:text-module-admin-text dark:hover:text-module-admin-text-dark',
    },
    financial: {
      focusRing: 'focus:ring-module-financial-border focus:border-module-financial-border dark:focus:ring-module-financial-border-dark dark:focus:border-module-financial-border-dark focus-visible:ring-module-financial-border focus-visible:border-module-financial-border dark:focus-visible:ring-module-financial-border-dark dark:focus-visible:border-module-financial-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:ring-red-500 focus-visible:border-red-500 dark:focus-visible:ring-red-400 dark:focus-visible:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-financial-icon dark:text-module-financial-icon-dark',
      hoverBg: 'hover:bg-module-financial-bg/10 dark:hover:bg-module-financial-bg-dark/20',
      hoverText: 'hover:text-module-financial-text dark:hover:text-module-financial-text-dark',
    },
    abaplus: {
      focusRing: 'focus:ring-module-abaplus-border focus:border-module-abaplus-border dark:focus:ring-module-abaplus-border-dark dark:focus:border-module-abaplus-border-dark focus-visible:ring-module-abaplus-border focus-visible:border-module-abaplus-border dark:focus-visible:ring-module-abaplus-border-dark dark:focus-visible:border-module-abaplus-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:ring-red-500 focus-visible:border-red-500 dark:focus-visible:ring-red-400 dark:focus-visible:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-abaplus-icon dark:text-module-abaplus-icon-dark',
      hoverBg: 'hover:bg-module-abaplus-bg/10 dark:hover:bg-module-abaplus-bg-dark/20',
      hoverText: 'hover:text-module-abaplus-text dark:hover:text-module-abaplus-text-dark',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;

  // Classes base para todos os selects
  const baseClasses = 'w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 appearance-none pr-10 focus:outline-none focus-visible:outline-none';

  // Adicionar estilos para os itens do select no hover
  const optionHoverStyles = `
    option:hover {
      background-color: ${moduleColor === 'people' ? 'var(--module-people-bg)' :
                        moduleColor === 'scheduler' ? 'var(--module-scheduler-bg)' :
                        moduleColor === 'admin' ? 'var(--module-admin-bg)' :
                        moduleColor === 'financial' ? 'var(--module-financial-bg)' :
                        moduleColor === 'abaplus' ? 'var(--module-abaplus-bg)' : 'var(--primary-50)'} !important;
      color: ${moduleColor === 'people' ? 'var(--module-people-text)' :
              moduleColor === 'scheduler' ? 'var(--module-scheduler-text)' :
              moduleColor === 'admin' ? 'var(--module-admin-text)' :
              moduleColor === 'financial' ? 'var(--module-financial-text)' :
              moduleColor === 'abaplus' ? 'var(--module-abaplus-text)' : 'var(--primary-700)'} !important;
    }
  `;

  // Classes para o estado de foco
  const focusClasses = error ? colors.errorRing : colors.focusRing;

  // Classes para o estado de erro
  const errorClasses = error ? colors.errorBorder : '';

  // Combinar todas as classes
  const selectClasses = `${baseClasses} ${focusClasses} ${errorClasses} ${className}`;

  // Extrair as opções dos children (elementos option)
  const extractOptions = () => {
    const options = [];
    React.Children.forEach(children, child => {
      if (child && child.type === 'option') {
        options.push({
          value: child.props.value,
          label: child.props.children,
          disabled: child.props.disabled
        });
      }
    });
    return options;
  };

  // Verificar se devemos usar o select nativo ou o customizado
  const useNativeSelect = typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'test' || disabled;

  if (useNativeSelect) {
    // Usar o select nativo para testes ou quando desabilitado
    return (
      <div className="relative">
        <select
          ref={ref}
          value={value}
          onChange={onChange}
          disabled={disabled}
          required={required}
          name={name}
          id={id}
          className={selectClasses}
          {...rest}
        >
          {placeholder && (
            <option value="" disabled={required}>
              {placeholder}
            </option>
          )}
          {children}
        </select>

        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <ChevronDown className={`h-4 w-4 ${colors.iconColor}`} />
        </div>

        {error && errorMessage && (
          <p className="mt-1 text-xs text-red-600 dark:text-red-400">
            {errorMessage}
          </p>
        )}
      </div>
    );
  }

  // Usar o select customizado para melhor estilização
  return (
    <div className="relative">
      <CustomSelect
        ref={ref}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        name={name}
        id={id}
        moduleColor={moduleColor}
        error={error}
        placeholder={placeholder}
        options={extractOptions()}
        className={className}
      />

      {error && errorMessage && (
        <p className="mt-1 text-xs text-red-600 dark:text-red-400">
          {errorMessage}
        </p>
      )}
    </div>
  );
});

ModuleSelect.displayName = 'ModuleSelect';

export default ModuleSelect;
