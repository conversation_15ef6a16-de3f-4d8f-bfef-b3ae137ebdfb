// src/routes/aba/index.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const evaluationRoutes = require('./evaluationRoutes');
const levelRoutes = require('./levelRoutes');
const skillRoutes = require('./skillRoutes');
const scoreRoutes = require('./scoreRoutes');
const taskRoutes = require('./taskRoutes');
const standardCriteriaRoutes = require('./standardCriteriaRoutes');
const programRoutes = require('./programRoutes');
const curriculumFolderRoutes = require('./curriculumFolderRoutes');
const anamneseRoutes = require('./anamneseRoutes');
const evolucaoDiariaRoutes = require('./evolucaoDiariaRoutes');

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticate);

// Rota de status para verificar autenticação
router.get('/status', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'ABA+ module is running',
    user: {
      id: req.user.id,
      name: req.user.fullName,
      role: req.user.role
    }
  });
});

// Rotas de avaliações
router.use('/evaluations', evaluationRoutes);

// Rotas de níveis
router.use('/levels', levelRoutes);

// Rotas de habilidades
router.use('/skills', skillRoutes);

// Rotas de pontuações
router.use('/scores', scoreRoutes);

// Rotas de tarefas
router.use('/tasks', taskRoutes);

// Rotas de critérios padrão
router.use('/standard-criteria', standardCriteriaRoutes);

// Rotas de programas
router.use('/programs', programRoutes);

// Rotas de pastas curriculares
router.use('/curriculum-folders', curriculumFolderRoutes);

// Rotas de anamneses
router.use('/anamneses', anamneseRoutes);

// Rotas de evoluções diárias
router.use('/evolucoes-diarias', evolucaoDiariaRoutes);

module.exports = router;
