// hooks/useInsuranceLimits.js
"use client";

import { useState, useEffect } from 'react';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';

/**
 * Hook personalizado para gerenciar limites de convênios nos agendamentos
 */
const useInsuranceLimits = () => {
  const [limitsData, setLimitsData] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Verifica se um agendamento excederia os limites do convênio
   * @param {Object} appointmentData - Dados do agendamento
   * @returns {Object} Resultado da verificação
   */
  const checkInsuranceLimits = async (appointmentData) => {
    const { personId, insuranceId, serviceTypeId, startDate } = appointmentData;

    // Se algum dado essencial estiver faltando, retorna válido
    if (!personId || !insuranceId || !serviceTypeId || !startDate) {
      return { valid: true, message: "Sem verificação de limites" };
    }

    setIsLoading(true);
    setError(null);

    try {
      // Chama o serviço de agendamentos para validar os limites
      const response = await appointmentService.checkInsuranceLimits({
        personId,
        insuranceId,
        serviceTypeId,
        startDate: new Date(startDate)
      });

      setIsLoading(false);
      return response;
    } catch (err) {
      console.error("Erro ao verificar limites de convênio:", err);
      setError(err.response?.data?.message || "Erro ao verificar limites de convênio");
      setIsLoading(false);
      
      return {
        valid: false,
        message: err.response?.data?.message || "Erro ao verificar limites de convênio"
      };
    }
  };

  /**
   * Obtém estatísticas de uso atual dos limites de convênio
   * @param {Object} data - Dados de pessoa, convênio e tipo de serviço
   * @returns {Object} Estatísticas de uso
   */
  const getLimitsUsage = async (data) => {
    const { personId, insuranceId, serviceTypeId } = data;
    
    if (!personId || !insuranceId || !serviceTypeId) {
      return null;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await appointmentService.getInsuranceLimitsUsage({
        personId,
        insuranceId,
        serviceTypeId
      });
      
      setIsLoading(false);
      return response;
    } catch (err) {
      console.error("Erro ao obter uso de limites de convênio:", err);
      setError(err.response?.data?.message || "Erro ao obter uso de limites");
      setIsLoading(false);
      return null;
    }
  };

  return {
    checkInsuranceLimits,
    getLimitsUsage,
    isLoading,
    error,
    setError
  };
};

export default useInsuranceLimits;