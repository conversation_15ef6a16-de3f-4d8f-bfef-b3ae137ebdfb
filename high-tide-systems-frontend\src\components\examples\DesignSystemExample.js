'use client';

import React, { useState } from 'react';
import {
  Calendar,
  Users,
  DollarSign,
  Shield,
  Info,
  AlertCircle,
  CheckCircle,
  X,
  Plus,
  Search,
  Download,
  ChevronDown,
  ChevronRight,
  Settings
} from 'lucide-react';
import ModalButton from '@/components/ui/ModalButton';
import ModuleModal from '@/components/ui/ModuleModal';
import { ModuleSelect, ModuleFormGroup } from '@/components/ui';
import { useToast } from '@/contexts/ToastContext';

/**
 * Componente de exemplo que demonstra o sistema de design
 */
const DesignSystemExample = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [activeModule, setActiveModule] = useState('people');
  const { toast_success, toast_error, toast_warning, toast_info } = useToast();

  // Módulos disponíveis
  const modules = [
    { id: 'people', name: '<PERSON><PERSON>oa<PERSON>', icon: Users, color: 'people' },
    { id: 'scheduler', name: 'Agendamento', icon: Calendar, color: 'scheduler' },
    { id: 'admin', name: '<PERSON>ministra<PERSON>', icon: Shield, color: 'admin' },
    { id: 'financial', name: 'Financeiro', icon: DollarSign, color: 'financial' },
  ];

  // Função para mostrar toast
  const showToast = (type) => {
    const toastFunctions = {
      success: () => toast_success('Operação realizada com sucesso!'),
      error: () => toast_error('Ocorreu um erro na operação.'),
      warning: () => toast_warning('Atenção! Esta ação pode ter consequências.'),
      info: () => toast_info('Informação importante para o usuário.'),
    };

    if (toastFunctions[type]) {
      toastFunctions[type]();
    }
  };

  // Obter o módulo ativo
  const currentModule = modules.find(m => m.id === activeModule) || modules[0];
  const ModuleIcon = currentModule.icon;

  return (
    <div className="p-8 max-w-7xl mx-auto">
      <h1 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-white mb-8">
        Sistema de Design
      </h1>

      {/* Seleção de Módulo */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-neutral-800 dark:text-white mb-4">
          Selecione um Módulo
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4">
          {modules.map((module) => (
            <button
              key={module.id}
              onClick={() => setActiveModule(module.id)}
              className={`p-4 rounded-lg border transition-all ${
                activeModule === module.id
                  ? `bg-module-${module.color}-bg dark:bg-module-${module.color}-bg-dark border-module-${module.color}-border dark:border-module-${module.color}-border-dark`
                  : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
              }`}
            >
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full bg-module-${module.color}-bg/50 dark:bg-module-${module.color}-bg-dark/50`}>
                  <module.icon
                    size={20}
                    className={`text-module-${module.color}-icon dark:text-module-${module.color}-icon-dark`}
                  />
                </div>
                <span className={`font-medium ${
                  activeModule === module.id
                    ? `text-module-${module.color}-text dark:text-module-${module.color}-text-dark`
                    : 'text-neutral-800 dark:text-white'
                }`}>
                  {module.name}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Tipografia */}
      <section className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-semibold text-neutral-800 dark:text-white mb-4">
          Tipografia
        </h2>
        <div className="space-y-4">
          <div>
            <h1 className="text-3xl md:text-4xl font-bold text-neutral-900 dark:text-white">
              Título Principal (H1)
            </h1>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              text-3xl md:text-4xl font-bold
            </p>
          </div>
          <div>
            <h2 className="text-2xl md:text-3xl font-semibold text-neutral-800 dark:text-neutral-100">
              Título Secundário (H2)
            </h2>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              text-2xl md:text-3xl font-semibold
            </p>
          </div>
          <div>
            <h3 className="text-xl md:text-2xl font-semibold text-neutral-800 dark:text-neutral-100">
              Título Terciário (H3)
            </h3>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              text-xl md:text-2xl font-semibold
            </p>
          </div>
          <div>
            <h4 className="text-lg md:text-xl font-medium text-neutral-800 dark:text-neutral-100">
              Título Quaternário (H4)
            </h4>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              text-lg md:text-xl font-medium
            </p>
          </div>
          <div>
            <h5 className="text-base md:text-lg font-medium text-neutral-700 dark:text-neutral-200">
              Subtítulo (H5)
            </h5>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              text-base md:text-lg font-medium
            </p>
          </div>
          <div>
            <p className="text-base text-neutral-700 dark:text-neutral-300">
              Texto de parágrafo padrão. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl
              nunc quis nisl.
            </p>
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              text-base
            </p>
          </div>
          <div>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Texto pequeno para informações secundárias.
            </p>
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              text-sm
            </p>
          </div>
          <div>
            <p className="text-xs text-neutral-500 dark:text-neutral-500">
              Texto de legenda para metadados e notas de rodapé.
            </p>
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              text-xs
            </p>
          </div>
        </div>
      </section>

      {/* Cores */}
      <section className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-semibold text-neutral-800 dark:text-white mb-4">
          Cores do Módulo: {currentModule.name}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className={`p-4 rounded-lg bg-module-${currentModule.color}-bg dark:bg-module-${currentModule.color}-bg-dark border border-module-${currentModule.color}-border dark:border-module-${currentModule.color}-border-dark`}>
            <p className={`font-medium text-module-${currentModule.color}-text dark:text-module-${currentModule.color}-text-dark`}>
              Cor de Fundo e Borda
            </p>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mt-2">
              bg-module-{currentModule.color}-bg<br />
              border-module-{currentModule.color}-border
            </p>
          </div>
          <div className="p-4 rounded-lg bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
            <p className={`font-medium text-module-${currentModule.color}-icon dark:text-module-${currentModule.color}-icon-dark`}>
              Cor de Ícone
            </p>
            <div className="flex items-center gap-2 mt-2">
              <ModuleIcon size={24} className={`text-module-${currentModule.color}-icon dark:text-module-${currentModule.color}-icon-dark`} />
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                text-module-{currentModule.color}-icon
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Botões */}
      <section className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-semibold text-neutral-800 dark:text-white mb-4">
          Botões
        </h2>
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100 mb-3">
              Variantes
            </h3>
            <div className="flex flex-wrap gap-3">
              <ModalButton
                variant="primary"
                moduleColor={currentModule.color}
              >
                Primary
              </ModalButton>
              <ModalButton
                variant="secondary"
                moduleColor={currentModule.color}
              >
                Secondary
              </ModalButton>
              <ModalButton
                variant="danger"
              >
                Danger
              </ModalButton>
              <ModalButton
                variant="success"
              >
                Success
              </ModalButton>
              <ModalButton
                variant="warning"
              >
                Warning
              </ModalButton>
            </div>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100 mb-3">
              Estados
            </h3>
            <div className="flex flex-wrap gap-3">
              <ModalButton
                variant="primary"
                moduleColor={currentModule.color}
              >
                Normal
              </ModalButton>
              <ModalButton
                variant="primary"
                moduleColor={currentModule.color}
                isLoading
              >
                Carregando
              </ModalButton>
              <ModalButton
                variant="primary"
                moduleColor={currentModule.color}
                disabled
              >
                Desabilitado
              </ModalButton>
            </div>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100 mb-3">
              Com Ícones
            </h3>
            <div className="flex flex-wrap gap-3">
              <ModalButton
                variant="primary"
                moduleColor={currentModule.color}
                onClick={() => setIsModalOpen(true)}
              >
                <Plus size={16} />
                Abrir Modal
              </ModalButton>
              <ModalButton
                variant="secondary"
                moduleColor={currentModule.color}
              >
                <Search size={16} />
                Buscar
              </ModalButton>
              <ModalButton
                variant="secondary"
                moduleColor={currentModule.color}
              >
                <Download size={16} />
                Exportar
              </ModalButton>
            </div>
          </div>
        </div>
      </section>

      {/* Notificações */}
      <section className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-semibold text-neutral-800 dark:text-white mb-4">
          Notificações (Toast)
        </h2>
        <div className="flex flex-wrap gap-3">
          <ModalButton
            variant="success"
            onClick={() => showToast('success')}
          >
            <CheckCircle size={16} />
            Sucesso
          </ModalButton>
          <ModalButton
            variant="danger"
            onClick={() => showToast('error')}
          >
            <AlertCircle size={16} />
            Erro
          </ModalButton>
          <ModalButton
            variant="warning"
            onClick={() => showToast('warning')}
          >
            <AlertCircle size={16} />
            Aviso
          </ModalButton>
          <ModalButton
            variant="secondary"
            onClick={() => showToast('info')}
          >
            <Info size={16} />
            Informação
          </ModalButton>
        </div>
      </section>

      {/* Espaçamento */}
      <section className="mb-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-semibold text-neutral-800 dark:text-white mb-4">
          Espaçamento
        </h2>
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100 mb-3">
              Padding (p-*)
            </h3>
            <div className="flex flex-wrap gap-4">
              {[2, 4, 6, 8].map((size) => (
                <div
                  key={size}
                  className={`p-${size} bg-module-${currentModule.color}-bg/30 dark:bg-module-${currentModule.color}-bg-dark/30 rounded border border-dashed border-module-${currentModule.color}-border dark:border-module-${currentModule.color}-border-dark`}
                >
                  <div className={`h-10 w-20 flex items-center justify-center bg-module-${currentModule.color}-bg dark:bg-module-${currentModule.color}-bg-dark rounded`}>
                    <span className="text-sm font-medium">p-{size}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100 mb-3">
              Margin (m-*)
            </h3>
            <div className={`p-4 bg-module-${currentModule.color}-bg/30 dark:bg-module-${currentModule.color}-bg-dark/30 rounded border border-dashed border-module-${currentModule.color}-border dark:border-module-${currentModule.color}-border-dark`}>
              {[2, 4, 6, 8].map((size) => (
                <div
                  key={size}
                  className={`mb-${size} h-10 w-40 flex items-center justify-center bg-module-${currentModule.color}-bg dark:bg-module-${currentModule.color}-bg-dark rounded`}
                >
                  <span className="text-sm font-medium">mb-{size}</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-neutral-100 mb-3">
              Gap (gap-*)
            </h3>
            <div className={`p-4 bg-module-${currentModule.color}-bg/30 dark:bg-module-${currentModule.color}-bg-dark/30 rounded border border-dashed border-module-${currentModule.color}-border dark:border-module-${currentModule.color}-border-dark`}>
              {[2, 4, 6, 8].map((size) => (
                <div key={size} className="mb-6">
                  <p className="text-sm font-medium mb-2">gap-{size}</p>
                  <div className={`flex gap-${size}`}>
                    {[1, 2, 3].map((i) => (
                      <div
                        key={i}
                        className={`h-10 w-20 flex items-center justify-center bg-module-${currentModule.color}-bg dark:bg-module-${currentModule.color}-bg-dark rounded`}
                      >
                        <span className="text-sm">{i}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Modal de Exemplo */}
      <ModuleModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={`Modal de ${currentModule.name}`}
        icon={<ModuleIcon size={22} />}
        moduleColor={currentModule.color}
        size="md"
        footer={
          <div className="flex justify-end gap-3">
            <ModalButton
              variant="secondary"
              moduleColor={currentModule.color}
              onClick={() => setIsModalOpen(false)}
            >
              Cancelar
            </ModalButton>
            <ModalButton
              variant="primary"
              moduleColor={currentModule.color}
              onClick={() => {
                showToast('success');
                setIsModalOpen(false);
              }}
            >
              Confirmar
            </ModalButton>
          </div>
        }
      >
        <div className="p-6 space-y-4">
          <p className="text-neutral-700 dark:text-neutral-300">
            Este é um exemplo de modal usando o componente ModuleModal com o tema do módulo {currentModule.name}.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Campo de Exemplo
              </label>
              <input
                type="text"
                className="w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-module-scheduler-icon dark:focus:ring-module-scheduler-icon-dark bg-white dark:bg-neutral-800 dark:text-neutral-200"
                placeholder="Digite algo aqui"
              />
            </div>
            <div>
              <ModuleFormGroup
                moduleColor={currentModule.color}
                label="Seleção"
                htmlFor="example-select"
              >
                <ModuleSelect
                  moduleColor={currentModule.color}
                  id="example-select"
                  placeholder="Selecione uma opção"
                >
                  <option>Opção 1</option>
                  <option>Opção 2</option>
                  <option>Opção 3</option>
                </ModuleSelect>
              </ModuleFormGroup>
            </div>
          </div>
        </div>
      </ModuleModal>
    </div>
  );
};

export default DesignSystemExample;
