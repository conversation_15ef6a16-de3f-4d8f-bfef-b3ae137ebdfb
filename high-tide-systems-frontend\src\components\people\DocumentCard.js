"use client";

import React, { useState, useEffect } from "react";
import { FileText, Eye, Download, Trash, Calendar, ChevronDown, ChevronUp, Loader2, ExternalLink } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { documentsService } from "@/app/modules/people/services/documentsService";

const DocumentCard = ({ document, onDelete }) => {
  const [showPreview, setShowPreview] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Limpar a URL do preview quando o componente for desmontado ou o preview for fechado
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, []);

  const handleTogglePreview = async () => {
    if (showPreview) {
      setShowPreview(false);
      return;
    }

    if (!previewUrl) {
      setIsLoading(true);
      setError(null);
      
      try {
        setIsLoading(true);
        // Usar o serviço de documentos para obter a URL de preview
        const blob = await documentsService.getDocument(document.id);
        const url = URL.createObjectURL(blob);
        setPreviewUrl(url);
      } catch (err) {
        console.error("Erro ao carregar preview:", err);
        setError(err.message || "Não foi possível carregar a pré-visualização");
      } finally {
        setIsLoading(false);
      }
    }
    
    setShowPreview(true);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  const getDocumentTypeDisplay = (type) => {
    const typeMap = {
      "RG": "RG",
      "CPF": "CPF",
      "CNH": "Carteira de Motorista",
      "COMP_RESIDENCIA": "Comprovante de Residência",
      "CERTIDAO_NASCIMENTO": "Certidão de Nascimento",
      "CERTIDAO_CASAMENTO": "Certidão de Casamento",
      "CARTAO_VACINACAO": "Cartão de Vacinação",
      "PASSAPORTE": "Passaporte",
      "TITULO_ELEITOR": "Título de Eleitor",
      "CARTEIRA_TRABALHO": "Carteira de Trabalho",
      "OUTROS": "Outros"
    };
    
    return typeMap[type] || type;
  };

  const getDocumentIcon = (type) => {
    return <FileText className="h-6 w-6" />;
  };

  const getFileExtension = (filename) => {
    if (!filename) return "";
    return filename.split('.').pop().toLowerCase();
  };

  const getDocumentColorClass = (type) => {
    const colorMap = {
      "RG": "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",
      "CPF": "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",
      "CNH": "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",
      "COMP_RESIDENCIA": "bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300",
      "CERTIDAO_NASCIMENTO": "bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300",
      "CERTIDAO_CASAMENTO": "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300",
      "CARTAO_VACINACAO": "bg-teal-100 text-teal-800 dark:bg-teal-900/30 dark:text-teal-300", 
      "PASSAPORTE": "bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",
      "TITULO_ELEITOR": "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300",
      "CARTEIRA_TRABALHO": "bg-cyan-100 text-cyan-800 dark:bg-cyan-900/30 dark:text-cyan-300",
      "OUTROS": "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300"
    };
    
    return colorMap[type] || "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300";
  };
  
  const isImage = (mimeType) => {
    return mimeType && mimeType.startsWith('image/');
  };
  
  const isPDF = (mimeType) => {
    return mimeType === 'application/pdf';
  };
  
  const documentMimeType = document.mimeType || '';
  const extension = getFileExtension(document.filename).toLowerCase();
  const isImageExt = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'].includes(extension);
  const isPdfExt = extension === 'pdf';

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 overflow-hidden transition-all duration-300">
      <div className="p-4">
        <div className="flex items-start gap-3">
          <div className={`p-3 rounded-lg ${getDocumentColorClass(document.type)}`}>
            {getDocumentIcon(document.type)}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <div>
                <h4 className="text-neutral-800 dark:text-gray-100 font-medium truncate">{document.filename}</h4>
                <span className={`inline-block px-2 py-1 text-xs rounded-full mt-1 ${getDocumentColorClass(document.type)}`}>
                  {getDocumentTypeDisplay(document.type)}
                </span>
              </div>
              
              <div className="flex items-center gap-2 text-neutral-500 dark:text-gray-400 text-sm">
                <Calendar size={14} />
                <span>{formatDate(document.createdAt)}</span>
              </div>
            </div>
            
            <div className="flex flex-wrap gap-2 mt-3">
              <button
                onClick={handleTogglePreview}
                className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 dark:bg-blue-900/30 hover:bg-blue-200 dark:hover:bg-blue-800/40 text-blue-700 dark:text-blue-300 rounded-md text-sm transition-colors"
              >
                {showPreview ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                <span>{showPreview ? "Ocultar" : "Visualizar"}</span>
              </button>
              
              <button
                onClick={() => documentsService.downloadDocument(document.id)}
                className="inline-flex items-center gap-1 px-2 py-1 bg-neutral-100 dark:bg-gray-700 hover:bg-neutral-200 dark:hover:bg-gray-600 text-neutral-700 dark:text-gray-300 rounded-md text-sm transition-colors"
              >
                <Download size={14} />
                <span>Baixar</span>
              </button>
              
              <button
                onClick={() => documentsService.openDocument(document.id)}
                className="inline-flex items-center gap-1 px-2 py-1 bg-primary-100 dark:bg-primary-900/30 hover:bg-primary-200 dark:hover:bg-primary-800/40 text-primary-700 dark:text-primary-300 rounded-md text-sm transition-colors"
              >
                <ExternalLink size={14} />
                <span>Abrir</span>
              </button>
              
              <button
                onClick={() => onDelete(document.id)}
                className="inline-flex items-center gap-1 px-2 py-1 bg-red-100 dark:bg-red-900/30 hover:bg-red-200 dark:hover:bg-red-800/40 text-red-700 dark:text-red-300 rounded-md text-sm transition-colors"
              >
                <Trash size={14} />
                <span>Excluir</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      {/* Preview Section */}
      {showPreview && (
        <div className="border-t border-neutral-200 dark:border-gray-700 bg-neutral-50 dark:bg-gray-900">
          {isLoading ? (
            <div className="flex justify-center items-center py-16">
              <div className="flex flex-col items-center gap-2">
                <Loader2 size={32} className="text-primary-500 dark:text-primary-400 animate-spin" />
                <p className="text-neutral-600 dark:text-gray-300">Carregando documento...</p>
              </div>
            </div>
          ) : error ? (
            <div className="p-4 flex justify-center">
              <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 max-w-md">
                <p className="font-medium mb-2">Erro ao carregar documento</p>
                <p>{error}</p>
              </div>
            </div>
          ) : (
            <div className="p-4">
              {(isPDF(documentMimeType) || isPdfExt) ? (
                <div className="h-96 w-full border border-neutral-200 dark:border-gray-700 rounded-lg overflow-hidden">
                  <iframe 
                    src={`${previewUrl}#view=FitH`} 
                    className="w-full h-full" 
                    title={document.filename}
                  />
                </div>
              ) : (isImage(documentMimeType) || isImageExt) ? (
                <div className="flex justify-center">
                  <img 
                    src={previewUrl} 
                    alt={document.filename}
                    className="max-w-full max-h-80 object-contain border border-neutral-200 dark:border-gray-700 rounded-lg"
                  />
                </div>
              ) : (
                <div className="p-4 flex justify-center">
                  <div className="flex flex-col items-center gap-4 text-neutral-600 dark:text-gray-300 text-center">
                    <FileText size={48} className="text-neutral-400 dark:text-gray-500" />
                    <p>
                      Este tipo de documento não pode ser pré-visualizado diretamente.
                      Use os botões acima para baixar ou abrir o arquivo.
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default DocumentCard;