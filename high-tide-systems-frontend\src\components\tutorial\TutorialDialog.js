"use client";

import React, { useRef, useEffect, useState } from 'react';
import { ArrowLeft, ArrowRight, X } from 'lucide-react';

/**
 * Caixa de diálogo para exibir instruções do tutorial
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.title - <PERSON><PERSON><PERSON><PERSON> da etapa
 * @param {string|React.ReactNode} props.content - Conteúdo/descrição da etapa
 * @param {string} props.position - Posição do diálogo ('top', 'right', 'bottom', 'left', 'auto')
 * @param {string} props.targetSelector - Seletor do elemento alvo para posicionamento
 * @param {number} props.offsetX - Deslocamento horizontal da posição padrão
 * @param {number} props.offsetY - Deslocamento vertical da posição padrão
 * @param {number} props.currentStep - Número da etapa atual
 * @param {number} props.totalSteps - Número total de etapas
 * @param {function} props.onNext - Função chamada ao clicar em "Próximo"
 * @param {function} props.onPrev - Função chamada ao clicar em "Anterior"
 * @param {function} props.onClose - Função chamada ao clicar em "Fechar"
 * @param {boolean} props.isFirstStep - Se é a primeira etapa
 * @param {boolean} props.isLastStep - Se é a última etapa
 */
const TutorialDialog = ({
  title,
  content,
  position = 'auto',
  targetSelector,
  offsetX = 20,
  offsetY = 20,
  currentStep,
  totalSteps,
  onNext,
  onPrev,
  onClose,
  isFirstStep,
  isLastStep
}) => {
  const dialogRef = useRef(null);
  const [dialogPosition, setDialogPosition] = useState({ top: '50%', left: '50%', transform: 'translate(-50%, -50%)' });
  const [calculatedPosition, setCalculatedPosition] = useState(position);

  // Calcula a melhor posição para o diálogo com base no elemento alvo
  useEffect(() => {
    if (!targetSelector || !dialogRef.current) return;

    const calculatePosition = () => {
      // Tenta encontrar todos os elementos que correspondem ao seletor
      const elements = document.querySelectorAll(targetSelector);

      if (!elements || elements.length === 0) {
        console.warn(`TutorialDialog: Elemento com seletor "${targetSelector}" não encontrado para posicionar o diálogo.`);
        return;
      }

      console.log(`TutorialDialog: Encontrados ${elements.length} elementos com seletor "${targetSelector}"`);

      // Encontra o primeiro elemento visível
      let targetElement = null;
      for (let i = 0; i < elements.length; i++) {
        const element = elements[i];
        const rect = element.getBoundingClientRect();

        // Verifica se o elemento está visível na tela
        if (rect.width > 0 && rect.height > 0 &&
            rect.top < window.innerHeight &&
            rect.left < window.innerWidth &&
            rect.bottom > 0 &&
            rect.right > 0) {

          // Verifica se o elemento não está oculto por CSS
          const style = window.getComputedStyle(element);
          if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
            targetElement = element;
            console.log(`TutorialDialog: Elemento visível encontrado na posição ${i+1}`, rect);
            break;
          }
        }
      }

      // Se não encontrou nenhum elemento visível, usa o primeiro
      if (!targetElement && elements.length > 0) {
        targetElement = elements[0];
        console.log(`TutorialDialog: Nenhum elemento visível encontrado, usando o primeiro elemento`);
      }

      if (!targetElement) {
        console.warn(`TutorialDialog: Nenhum elemento visível encontrado com seletor "${targetSelector}" para posicionar o diálogo.`);
        return;
      }

      const targetRect = targetElement.getBoundingClientRect();
      const dialogRect = dialogRef.current.getBoundingClientRect();

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Auto-calcular a melhor posição se for 'auto'
      let bestPosition = position;
      if (position === 'auto') {
        // Calcular espaço disponível em cada direção
        const spaceAbove = targetRect.top;
        const spaceBelow = viewportHeight - targetRect.bottom;
        const spaceLeft = targetRect.left;
        const spaceRight = viewportWidth - targetRect.right;

        // Determinar a direção com mais espaço
        const maxSpace = Math.max(spaceAbove, spaceBelow, spaceLeft, spaceRight);

        if (maxSpace === spaceBelow) bestPosition = 'bottom';
        else if (maxSpace === spaceAbove) bestPosition = 'top';
        else if (maxSpace === spaceRight) bestPosition = 'right';
        else bestPosition = 'left';
      }

      setCalculatedPosition(bestPosition);

      let newPosition = {};

      // Calcular posição com base na direção escolhida
      switch (bestPosition) {
        case 'top':
          newPosition = {
            top: targetRect.top + scrollTop - dialogRect.height - offsetY,
            left: targetRect.left + scrollLeft + (targetRect.width / 2) - (dialogRect.width / 2),
            transform: 'none'
          };
          break;
        case 'right':
          newPosition = {
            top: targetRect.top + scrollTop + (targetRect.height / 2) - (dialogRect.height / 2),
            left: targetRect.right + scrollLeft + offsetX,
            transform: 'none'
          };
          break;
        case 'bottom':
          newPosition = {
            top: targetRect.bottom + scrollTop + offsetY,
            left: targetRect.left + scrollLeft + (targetRect.width / 2) - (dialogRect.width / 2),
            transform: 'none'
          };
          break;
        case 'left':
          newPosition = {
            top: targetRect.top + scrollTop + (targetRect.height / 2) - (dialogRect.height / 2),
            left: targetRect.left + scrollLeft - dialogRect.width - offsetX,
            transform: 'none'
          };
          break;
        default:
          // Posição centralizada como fallback
          newPosition = {
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          };
      }

      // Ajuste para garantir que o diálogo fique dentro da viewport
      if (newPosition.left < 20) newPosition.left = 20;
      if (newPosition.top < 20) newPosition.top = 20;
      if (newPosition.left + dialogRect.width > viewportWidth - 20) {
        newPosition.left = viewportWidth - dialogRect.width - 20;
      }
      if (newPosition.top + dialogRect.height > viewportHeight - 20) {
        newPosition.top = viewportHeight - dialogRect.height - 20;
      }

      setDialogPosition(newPosition);
    };

    // Pequeno atraso para garantir que o diálogo tenha sido renderizado com dimensões corretas
    const timer = setTimeout(calculatePosition, 300);

    // Recalcular em caso de redimensionamento da janela
    window.addEventListener('resize', calculatePosition);

    // Recalcular periodicamente para garantir que o diálogo esteja sempre posicionado corretamente
    const intervalTimer = setInterval(() => {
      calculatePosition();
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearInterval(intervalTimer);
      window.removeEventListener('resize', calculatePosition);
    };
  }, [targetSelector, position, offsetX, offsetY, dialogRef.current]);

  // Classe para adicionar seta direcional
  const getPositionClass = () => {
    switch (calculatedPosition) {
      case 'top': return 'tutorial-dialog-arrow-bottom';
      case 'right': return 'tutorial-dialog-arrow-left';
      case 'bottom': return 'tutorial-dialog-arrow-top';
      case 'left': return 'tutorial-dialog-arrow-right';
      default: return '';
    }
  };

  return (
    <>
      {/* Estilos para setas direcionais */}
      <style jsx global>{`
        .tutorial-dialog-arrow-top:after {
          content: '';
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translateX(-50%);
          border-width: 0 10px 10px 10px;
          border-style: solid;
          border-color: transparent transparent #ffffff transparent;
          filter: drop-shadow(0 -2px 2px rgba(0,0,0,0.1));
        }

        .tutorial-dialog-arrow-right:after {
          content: '';
          position: absolute;
          right: -10px;
          top: 50%;
          transform: translateY(-50%);
          border-width: 10px 0 10px 10px;
          border-style: solid;
          border-color: transparent transparent transparent #ffffff;
          filter: drop-shadow(2px 0 2px rgba(0,0,0,0.1));
        }

        .tutorial-dialog-arrow-bottom:after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          border-width: 10px 10px 0 10px;
          border-style: solid;
          border-color: #ffffff transparent transparent transparent;
          filter: drop-shadow(0 2px 2px rgba(0,0,0,0.1));
        }

        .tutorial-dialog-arrow-left:after {
          content: '';
          position: absolute;
          left: -10px;
          top: 50%;
          transform: translateY(-50%);
          border-width: 10px 10px 10px 0;
          border-style: solid;
          border-color: transparent #ffffff transparent transparent;
          filter: drop-shadow(-2px 0 2px rgba(0,0,0,0.1));
        }

        .dark .tutorial-dialog-arrow-top:after,
        .dark .tutorial-dialog-arrow-right:after,
        .dark .tutorial-dialog-arrow-bottom:after,
        .dark .tutorial-dialog-arrow-left:after {
          border-color: transparent;
        }

        .dark .tutorial-dialog-arrow-top:after {
          border-bottom-color: #1f2937;
        }

        .dark .tutorial-dialog-arrow-right:after {
          border-left-color: #1f2937;
        }

        .dark .tutorial-dialog-arrow-bottom:after {
          border-top-color: #1f2937;
        }

        .dark .tutorial-dialog-arrow-left:after {
          border-right-color: #1f2937;
        }
      `}</style>

      {/* Diálogo do tutorial */}
      <div
        ref={dialogRef}
        className={`fixed z-[9999] w-80 p-5 rounded-lg bg-white dark:bg-gray-800 shadow-xl dark:shadow-hard-dark ${getPositionClass()}`}
        style={{
          top: dialogPosition.top,
          left: dialogPosition.left,
          transform: dialogPosition.transform,
        }}
      >
        {/* Cabeçalho */}
        <div className="flex justify-between items-start mb-3">
          <h3 className="text-lg font-bold text-primary-600 dark:text-primary-400">
            {title}
          </h3>
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full transition-colors"
            aria-label="Fechar tutorial"
          >
            <X size={18} />
          </button>
        </div>

        {/* Conteúdo */}
        <div className="mb-4 text-gray-700 dark:text-gray-300">
          {content}
        </div>

        {/* Progresso */}
        <div className="w-full bg-gray-200 dark:bg-gray-700 h-1 rounded mb-3">
          <div
            className="bg-primary-500 dark:bg-primary-600 h-1 rounded"
            style={{ width: `${((currentStep + 1) / totalSteps) * 100}%` }}
          ></div>
        </div>

        {/* Rodapé com navegação */}
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Passo {currentStep + 1} de {totalSteps}
          </div>
          <div className="flex gap-2">
            {!isFirstStep && (
              <button
                onClick={onPrev}
                className="p-1.5 rounded-md border border-gray-300 dark:border-gray-600 text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center transition-colors"
                aria-label="Anterior"
              >
                <ArrowLeft size={16} />
              </button>
            )}
            <button
              onClick={onNext}
              className="px-3 py-1.5 rounded-md bg-primary-500 hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 text-white flex items-center gap-1.5 transition-colors"
              aria-label={isLastStep ? "Concluir" : "Próximo"}
            >
              {isLastStep ? "Concluir" : "Próximo"}
              {!isLastStep && <ArrowRight size={16} />}
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default TutorialDialog;