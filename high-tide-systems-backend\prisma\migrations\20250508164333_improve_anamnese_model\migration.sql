/*
  Warnings:

  - The `ageComoSeFosseSurdo` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `apresentaAtencaoDiminuida` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `apresentaPreferenciaIsolamento` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `balbucio` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `brincaAdequadamenteBrinquedo` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `enunciadoDuasPalavras` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `estereotipiasVocais` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `faltaExpressaoFacialAdequada` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `fazPedidoItensInteresse` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `frases` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `gestosElementares` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `naoSimbolicosConvencionais` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `olhaParaAlguemQueLheFala` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `olhaQuandoChamadoPeloNome` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `palavrasIsoladas` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `realizaImitacao` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `simbolicosRepresentacao` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - The `verbal` column on the `Anamnese` table would be dropped and recreated. This will lead to data loss if there is data in the column.

*/
-- CreateEnum
CREATE TYPE "SimNaoAsVezes" AS ENUM ('SIM', 'NAO', 'AS_VEZES');

-- AlterTable
ALTER TABLE "Anamnese" ADD COLUMN     "alergias" TEXT,
ADD COLUMN     "historicoFamiliar" TEXT,
ADD COLUMN     "historicoMedico" TEXT,
ADD COLUMN     "medicacoes" TEXT,
ADD COLUMN     "observacoesGerais" TEXT,
ADD COLUMN     "ultimaAtualizacaoPorId" TEXT,
ADD COLUMN     "versao" INTEGER NOT NULL DEFAULT 1,
DROP COLUMN "ageComoSeFosseSurdo",
ADD COLUMN     "ageComoSeFosseSurdo" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "apresentaAtencaoDiminuida",
ADD COLUMN     "apresentaAtencaoDiminuida" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "apresentaPreferenciaIsolamento",
ADD COLUMN     "apresentaPreferenciaIsolamento" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "balbucio",
ADD COLUMN     "balbucio" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "brincaAdequadamenteBrinquedo",
ADD COLUMN     "brincaAdequadamenteBrinquedo" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "enunciadoDuasPalavras",
ADD COLUMN     "enunciadoDuasPalavras" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "estereotipiasVocais",
ADD COLUMN     "estereotipiasVocais" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "faltaExpressaoFacialAdequada",
ADD COLUMN     "faltaExpressaoFacialAdequada" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "fazPedidoItensInteresse",
ADD COLUMN     "fazPedidoItensInteresse" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "frases",
ADD COLUMN     "frases" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "gestosElementares",
ADD COLUMN     "gestosElementares" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "naoSimbolicosConvencionais",
ADD COLUMN     "naoSimbolicosConvencionais" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "olhaParaAlguemQueLheFala",
ADD COLUMN     "olhaParaAlguemQueLheFala" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "olhaQuandoChamadoPeloNome",
ADD COLUMN     "olhaQuandoChamadoPeloNome" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "palavrasIsoladas",
ADD COLUMN     "palavrasIsoladas" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "realizaImitacao",
ADD COLUMN     "realizaImitacao" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "simbolicosRepresentacao",
ADD COLUMN     "simbolicosRepresentacao" "SimNaoAsVezes" DEFAULT 'NAO',
DROP COLUMN "verbal",
ADD COLUMN     "verbal" "SimNaoAsVezes" DEFAULT 'NAO';

-- CreateIndex
CREATE INDEX "Anamnese_ultimaAtualizacaoPorId_idx" ON "Anamnese"("ultimaAtualizacaoPorId");

-- CreateIndex
CREATE INDEX "Anamnese_versao_idx" ON "Anamnese"("versao");

-- AddForeignKey
ALTER TABLE "Anamnese" ADD CONSTRAINT "Anamnese_ultimaAtualizacaoPorId_fkey" FOREIGN KEY ("ultimaAtualizacaoPorId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
