// src/routes/aba/taskRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { TaskController, createTaskValidation, updateTaskValidation } = require('../../controllers/aba/taskController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para tarefas
router.post('/', createTaskValidation, TaskController.create);
router.get('/', TaskController.list);
router.get('/:id', TaskController.get);
router.put('/:id', updateTaskValidation, TaskController.update);
router.delete('/:id', TaskController.delete);

module.exports = router;
