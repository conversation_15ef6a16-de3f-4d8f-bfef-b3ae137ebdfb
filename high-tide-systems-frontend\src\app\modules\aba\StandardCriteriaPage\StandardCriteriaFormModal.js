"use client";

import React, { useState, useEffect } from "react";
import { Award } from "lucide-react";
import { ModuleModal, ModuleFormGroup, ModuleInput, ModuleSelect } from "@/components/ui";
import { useToast } from "@/contexts/ToastContext";
import { standardCriteriaService } from "../services/standardCriteriaService";

const StandardCriteriaFormModal = ({ isOpen, onClose, onSuccess, criterion }) => {
  const { toast_success, toast_error } = useToast();
  const [formData, setFormData] = useState({
    teachingType: "",
    acronym: "",
    degree: ""
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Carregar dados do critério se estiver editando
  useEffect(() => {
    if (criterion) {
      setFormData({
        teachingType: criterion.teachingType || "",
        acronym: criterion.acronym || "",
        degree: criterion.degree || ""
      });
    } else {
      // Reset form quando abrir para criar novo
      setFormData({
        teachingType: "",
        acronym: "",
        degree: ""
      });
    }
    setErrors({});
  }, [criterion, isOpen]);

  // Manipular mudanças nos campos do formulário
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Limpar erro do campo quando o usuário digitar
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  // Validar formulário
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.teachingType) {
      newErrors.teachingType = "Tipo de ensino é obrigatório";
    }
    
    if (!formData.acronym) {
      newErrors.acronym = "Sigla é obrigatória";
    }
    
    if (!formData.degree) {
      newErrors.degree = "Grau é obrigatório";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Enviar formulário
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      if (criterion) {
        // Atualizar critério existente
        await standardCriteriaService.updateStandardCriterion(criterion.id, formData);
        toast_success("Critério padrão atualizado com sucesso");
      } else {
        // Criar novo critério
        await standardCriteriaService.createStandardCriterion(formData);
        toast_success("Critério padrão criado com sucesso");
      }
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Erro ao salvar critério padrão:", error);
      toast_error(error.response?.data?.message || "Erro ao salvar critério padrão");
      
      // Verificar se há erros de validação retornados pela API
      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.param] = err.msg;
        });
        setErrors(apiErrors);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Opções para os selects
  const teachingTypeOptions = standardCriteriaService.getTeachingTypeOptions();
  const degreeOptions = standardCriteriaService.getDegreeOptions();

  // Rodapé do modal com botões
  const modalFooter = (
    <div className="flex justify-end gap-2">
      <button
        type="button"
        onClick={onClose}
        className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        disabled={isSubmitting}
      >
        Cancelar
      </button>
      <button
        type="submit"
        form="standard-criteria-form"
        className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={isSubmitting}
      >
        {isSubmitting ? "Salvando..." : criterion ? "Atualizar" : "Criar"}
      </button>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={criterion ? "Editar Critério Padrão" : "Novo Critério Padrão"}
      size="md"
      moduleColor="abaplus"
      icon={<Award size={20} />}
      footer={modalFooter}
    >
      <form id="standard-criteria-form" onSubmit={handleSubmit} className="p-6 space-y-6">
        <ModuleFormGroup label="Tipo de Ensino *" error={errors.teachingType}>
          <ModuleSelect
            name="teachingType"
            value={formData.teachingType}
            onChange={handleChange}
            moduleColor="abaplus"
            error={!!errors.teachingType}
          >
            <option value="">Selecione o tipo de ensino</option>
            {teachingTypeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </ModuleSelect>
        </ModuleFormGroup>

        <ModuleFormGroup label="Sigla *" error={errors.acronym}>
          <ModuleInput
            name="acronym"
            value={formData.acronym}
            onChange={handleChange}
            placeholder="Digite a sigla"
            moduleColor="abaplus"
            error={!!errors.acronym}
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Grau *" error={errors.degree}>
          <ModuleSelect
            name="degree"
            value={formData.degree}
            onChange={handleChange}
            moduleColor="abaplus"
            error={!!errors.degree}
          >
            <option value="">Selecione o grau</option>
            {degreeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </ModuleSelect>
        </ModuleFormGroup>
      </form>
    </ModuleModal>
  );
};

export default StandardCriteriaFormModal;
