import React, { useState, useEffect } from "react";
import { format as dateFormat } from "date-fns";
import {
  Search,
  Calendar,
  Filter,
  RefreshCw
} from "lucide-react";
import { FilterButton } from "@/components/ui/ModuleHeader";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import MultiSelect from "@/components/ui/multi-select";
import { useAuth } from '@/contexts/AuthContext';

const ClientReportFilters = ({
  filters,
  setFilters,
  onSearch,
  onExport,
  isLoading
}) => {
  // Estado para os dados dos selects
  const [persons, setPersons] = useState([]);
  const [locations, setLocations] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(false);
  const { user } = useAuth();

  // Opções de status
  const statusOptions = [
    { value: "PENDING", label: "Pendente" },
    { value: "CONFIRMED", label: "Confirmado" },
    { value: "CANCELLED", label: "Cancelado" },
    { value: "COMPLETED", label: "Concluído" },
    { value: "NO_SHOW", label: "Não Compareceu" }
  ];

  // Função para carregar apenas os dados relacionados aos agendamentos do cliente
  const loadClientRelatedData = async () => {
    try {
      setIsDataLoading(true);
      console.log("Loading client-related data for appointment filters...");

      // Primeiro, buscar todos os agendamentos do cliente
      const clientAppointments = await appointmentService.getAppointments();
      console.log(`Loaded ${clientAppointments?.appointments?.length || 0} client appointments`);

      if (!clientAppointments?.appointments?.length) {
        setIsDataLoading(false);
        return;
      }

      // Extrair IDs únicos de pessoas, locais e tipos de serviço dos agendamentos
      const personIds = new Set();
      const locationIds = new Set();
      const serviceTypeIds = new Set();

      clientAppointments.appointments.forEach(appointment => {
        // Extrair personId do relacionamento Person ou do campo personId
        if (appointment.Person && appointment.Person.length > 0) {
          appointment.Person.forEach(person => {
            if (person && person.id) personIds.add(person.id);
          });
        }

        if (appointment.locationId) locationIds.add(appointment.locationId);
        if (appointment.serviceTypeId) serviceTypeIds.add(appointment.serviceTypeId);
      });

      console.log("Extracted unique IDs:", {
        persons: Array.from(personIds),
        locations: Array.from(locationIds),
        serviceTypes: Array.from(serviceTypeIds)
      });

      // Buscar dados completos para cada tipo
      const [personsData, locationsData, serviceTypesData] = await Promise.all([
        appointmentService.getPersons(),
        appointmentService.getLocations(),
        appointmentService.getServiceTypes()
      ]);

      // Filtrar apenas os itens relacionados aos agendamentos do cliente
      const filteredPersons = (personsData || [])
        .filter(p => p && p.id && personIds.has(p.id))
        .map(person => ({
          value: person.id,
          label: person.fullName
        }));

      const filteredLocations = (locationsData || [])
        .filter(l => l && l.id && locationIds.has(l.id))
        .map(location => ({
          value: location.id,
          label: location.name
        }));

      const filteredServiceTypes = (serviceTypesData || [])
        .filter(s => s && s.id && serviceTypeIds.has(s.id))
        .map(serviceType => ({
          value: serviceType.id,
          label: serviceType.name
        }));

      console.log("Filtered data for client:", {
        persons: filteredPersons.length,
        locations: filteredLocations.length,
        serviceTypes: filteredServiceTypes.length
      });

      setPersons(filteredPersons);
      setLocations(filteredLocations);
      setServiceTypes(filteredServiceTypes);
    } catch (err) {
      console.error("Error loading client-related data:", err);
    } finally {
      setIsDataLoading(false);
    }
  };

  useEffect(() => {
    loadClientRelatedData();
  }, [user?.id]);

  // Atualizar filtros diretamente sem aplicar automaticamente
  const handleFilterChange = (newFilters) => {
    console.log("Filtros alterados:", JSON.stringify(newFilters, null, 2));
    setFilters(newFilters);
  };

  // Limpar todos os filtros sem aplicar automaticamente
  const handleClearFilters = () => {
    const clearedFilters = {
      search: "",
      startDate: null,
      endDate: null,
      status: [],
      providers: [], // Mantemos este campo vazio para compatibilidade
      persons: [],
      locations: [],
      serviceTypes: []
    };

    // Atualizar o estado dos filtros
    setFilters(clearedFilters);
  };



  return (
    <div className="space-y-4">
      {/* Barra de pesquisa e botões */}
      <div className="flex flex-col md:flex-row gap-3 md:items-center">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Buscar agendamentos..."
              value={filters.search}
              onChange={(e) => handleFilterChange({ ...filters, search: e.target.value })}
              className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <FilterButton
            type="button"
            onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            moduleColor="scheduler"
            variant="secondary"
          >
            <div className="flex items-center gap-2">
              <Filter size={16} />
              <span>Filtros</span>
              <span className="bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                {Object.entries(filters).filter(([key, value]) => {
                  if (key === 'search') return false;
                  if (Array.isArray(value)) return value.length > 0;
                  return value !== null && value !== '';
                }).length}
              </span>
            </div>
          </FilterButton>

          <FilterButton
            type="button"
            onClick={onSearch}
            moduleColor="scheduler"
            variant="primary"
            disabled={isLoading}
          >
            <div className="flex items-center gap-2">
              <Search size={16} />
              <span>{isLoading ? "Buscando..." : "Buscar"}</span>
            </div>
          </FilterButton>
        </div>
      </div>

      {/* Filtros avançados (expansíveis) */}
      {isFilterExpanded && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Período - Data Inicial
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="date"
                value={filters.startDate ? dateFormat(filters.startDate, 'yyyy-MM-dd') : ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  startDate: e.target.value ? new Date(e.target.value) : null
                })}
                className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Período - Data Final
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="date"
                value={filters.endDate ? dateFormat(filters.endDate, 'yyyy-MM-dd') : ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  endDate: e.target.value ? new Date(e.target.value) : null
                })}
                className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <MultiSelect
              options={statusOptions}
              value={filters.status || []}
              onChange={(selected) => {
                console.log("Status selecionados:", selected);
                handleFilterChange({ ...filters, status: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Pacientes
            </label>
            <MultiSelect
              options={persons}
              value={filters.persons || []}
              onChange={(selected) => {
                console.log("Pacientes selecionados:", selected);
                handleFilterChange({ ...filters, persons: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Locais
            </label>
            <MultiSelect
              options={locations}
              value={filters.locations || []}
              onChange={(selected) => {
                console.log("Locais selecionados:", selected);
                handleFilterChange({ ...filters, locations: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tipos de Serviço
            </label>
            <MultiSelect
              options={serviceTypes}
              value={filters.serviceTypes || []}
              onChange={(selected) => {
                console.log("Tipos de serviço selecionados:", selected);
                handleFilterChange({ ...filters, serviceTypes: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div className="flex items-end md:col-span-2 lg:col-span-3">
            <FilterButton
              type="button"
              onClick={handleClearFilters}
              moduleColor="scheduler"
              variant="secondary"
            >
              <div className="flex items-center gap-2">
                <RefreshCw size={16} />
                <span>Limpar Filtros</span>
              </div>
            </FilterButton>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientReportFilters;
