// src/utils/clientPermissions.js
/**
 * This file defines the permissions and access rules for client users
 */

// Define the modules that clients can access
export const CLIENT_MODULES = ['people', 'scheduler'];

// Define the specific permissions for clients
export const CLIENT_PERMISSIONS = [
  // People module permissions
  'people.persons.view',
  'people.persons.edit',
  'people.clients.view',
  'people.clients.edit',

  // Scheduler module permissions (view only)
  'scheduler.calendar.view',
  'scheduler.appointments.view',
  'scheduling.calendar.view',  // Added this permission for calendar viewing
];

/**
 * Check if a client has access to a specific module
 * @param {string} moduleId - The module ID to check
 * @returns {boolean} - Whether the client has access to the module
 */
export const clientHasModuleAccess = (moduleId) => {
  return CLIENT_MODULES.includes(moduleId);
};

/**
 * Check if a client has a specific permission
 * @param {string} permissionId - The permission ID to check
 * @returns {boolean} - Whether the client has the permission
 */
export const clientHasPermission = (permissionId) => {
  return CLIENT_PERMISSIONS.includes(permissionId);
};

/**
 * Filter data to only show what belongs to the client
 * @param {Array} data - The data to filter
 * @param {string} clientId - The client ID
 * @param {string} type - The type of data ('persons', 'appointments', etc.)
 * @returns {Array} - The filtered data
 */
export const filterClientData = (data, clientId, type) => {
  if (!data || !Array.isArray(data) || !clientId) return [];

  switch (type) {
    case 'persons':
      return data.filter(person => person.clientId === clientId);
    case 'appointments':
      return data.filter(appointment => appointment.clientId === clientId);
    default:
      return [];
  }
};
