import { api } from "@/utils/api";
import { extractData, extractEntity } from "@/utils/apiResponseAdapter";
import { exportService } from "@/app/services/exportService";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";

// Adaptador para processar os dados de limites de serviço
const processServiceLimits = (limits) => {
  if (!limits || !Array.isArray(limits)) {
    return [];
  }

  return limits.map(limit => {
    // Garantir que os campos estejam disponíveis independentemente do formato da resposta
    return {
      ...limit,
      // Usar os campos com letra maiúscula se disponíveis, caso contrário usar os campos com letra minúscula
      insurance: limit.insurance || {},
      serviceType: limit.serviceType || {},
      person: limit.person || {},
      // Adicionar referências normalizadas
      Insurance: limit.Insurance || limit.insurance || {},
      ServiceType: limit.ServiceType || limit.serviceType || {},
      Person: limit.Person || limit.person || {}
    };
  });
};

export const insuranceServiceLimitService = {
  // Obter todos os limites com suporte a filtros
  getAllLimits: async (filters = {}) => {
    try {
      const { search, personId, personIds, insuranceId, serviceTypeId, companyId, sortField, sortDirection } = filters;

      // Construir parâmetros de consulta
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (personId) params.append('personId', personId);
      if (insuranceId) params.append('insuranceId', insuranceId);
      if (serviceTypeId) params.append('serviceTypeId', serviceTypeId);
      if (companyId) params.append('companyId', companyId);
      if (sortField) params.append('sortField', sortField);
      if (sortDirection) params.append('sortDirection', sortDirection);

      // Adicionar personIds como parâmetros separados com notação de array
      if (personIds && personIds.length > 0) {
        // Garantir que personIds seja um array
        const personIdsArray = Array.isArray(personIds) ? personIds : [personIds];

        // Adicionar cada ID como um parâmetro separado
        personIdsArray.forEach((id) => {
          // Usar o mesmo nome de parâmetro para cada ID (sem índice)
          params.append('personIds', id);
        });

        console.log("Filtrando por múltiplos IDs de pacientes:", personIdsArray);
      } else {
        console.log("Nenhum filtro de paciente aplicado");
      }

      // Log para depuração dos parâmetros de ordenação
      if (sortField) {
        // Garantir que a direção seja uma string válida
        const validDirection = sortDirection && ['asc', 'desc'].includes(sortDirection.toLowerCase())
          ? sortDirection.toLowerCase()
          : 'asc';

        console.log(`Parâmetros de ordenação: campo=${sortField}, direção=${validDirection}`);

        // Atualizar o parâmetro de direção com o valor normalizado
        params.set('sortDirection', validDirection);
      }

      // Não enviamos parâmetros de paginação para o backend
      // para garantir que recebemos todos os limites de uma vez

      console.log(`Enviando requisição para: /insurance-service-limits?${params.toString()}`);
      const response = await api.get(`/insurance-service-limits?${params.toString()}`);

      console.log("Resposta da API:", response.data);

      // Usar o adaptador para extrair os dados de forma consistente
      // Primeiro tentamos extrair usando o formato padrão
      const extracted = extractData(response.data, 'limits', ['data']);

      // Se não houver limites no formato padrão, processamos o array diretamente
      if (extracted.limits && extracted.limits.length > 0) {
        return {
          limits: processServiceLimits(extracted.limits),
          total: extracted.total,
          pages: extracted.pages
        };
      } else {
        // Processar o array diretamente se a API retornar apenas um array
        const processedLimits = processServiceLimits(response.data);

        return {
          limits: processedLimits,
          total: processedLimits.length,
          pages: Math.ceil(processedLimits.length / 10)
        };
      }
    } catch (error) {
      console.error("Erro ao buscar limites de serviço:", error);
      throw error;
    }
  },

  // Obter limites para uma pessoa específica
  getLimitsByPerson: async (personId) => {
    try {
      const response = await api.get(`/insurance-service-limits/person/${personId}`);
      return processServiceLimits(response.data);
    } catch (error) {
      console.error("Erro ao buscar limites de serviço:", error);
      throw error;
    }
  },

  // Obter limites para uma combinação pessoa+convênio
  getLimitsByPersonInsurance: async (personId, insuranceId) => {
    try {
      const response = await api.get(`/insurance-service-limits/person/${personId}/insurance/${insuranceId}`);
      return processServiceLimits(response.data);
    } catch (error) {
      console.error("Erro ao buscar limites de serviço por convênio:", error);
      throw error;
    }
  },

  // Criar um novo limite
  createLimit: async (data) => {
    try {
      const response = await api.post('/insurance-service-limits', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar limite de serviço:", error);
      throw error;
    }
  },

  // Atualizar um limite existente
  updateLimit: async (id, data) => {
    try {
      const response = await api.put(`/insurance-service-limits/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar limite de serviço ${id}:`, error);
      throw error;
    }
  },

  // Excluir um limite
  deleteLimit: async (id) => {
    try {
      await api.delete(`/insurance-service-limits/${id}`);
      return true;
    } catch (error) {
      console.error(`Erro ao excluir limite de serviço ${id}:`, error);
      throw error;
    }
  },

  // Verificar se um agendamento está dentro dos limites
  checkAppointmentLimit: async (data) => {
    try {
      const response = await api.post('/insurance-service-limits/check', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao verificar limite de agendamento:", error);
      throw error;
    }
  },

  /**
   * Exporta a lista de limites de convênio com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, personIds, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx', 'pdf' ou 'image')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportInsuranceLimits: async (filters, exportFormat = "xlsx") => {
    try {
      // Obter os dados filtrados da API
      const response = await insuranceServiceLimitService.getAllLimits({
        ...filters,
        // Não precisamos de paginação para exportação
      });

      // Extrair os dados dos limites
      const limitsArray = response.limits || [];

      // Log para debug da estrutura dos dados
      console.log("Estrutura dos limites para exportação:", limitsArray);
      if (limitsArray.length > 0) {
        console.log("Exemplo do primeiro limite:", JSON.stringify(limitsArray[0], null, 2));
        console.log("Propriedades disponíveis:", Object.keys(limitsArray[0]));
      }

      // Se os limites não tiverem as informações completas, vamos tentar enriquecê-los
      // Verificar se temos objetos aninhados ou apenas IDs
      const needsEnrichment = limitsArray.length > 0 && (
        (!limitsArray[0].Person && !limitsArray[0].person && !limitsArray[0].personFullName) ||
        (!limitsArray[0].Insurance && !limitsArray[0].insurance && !limitsArray[0].insuranceName) ||
        (!limitsArray[0].ServiceType && !limitsArray[0].serviceType && !limitsArray[0].serviceTypeName)
      );

      // Se precisarmos enriquecer os dados, vamos buscar as informações faltantes
      if (needsEnrichment) {
        console.log("Dados incompletos detectados, buscando informações adicionais...");

        // Coletar todos os IDs únicos
        const personIds = [...new Set(limitsArray.filter(l => l.personId).map(l => l.personId))];
        const insuranceIds = [...new Set(limitsArray.filter(l => l.insuranceId).map(l => l.insuranceId))];
        const serviceTypeIds = [...new Set(limitsArray.filter(l => l.serviceTypeId).map(l => l.serviceTypeId))];

        console.log("IDs para enriquecimento:", { personIds, insuranceIds, serviceTypeIds });

        try {
          // Buscar informações de pacientes se necessário
          if (personIds.length > 0) {
            const personsResponse = await api.get('/persons', {
              params: { ids: personIds.join(',') }
            });
            const persons = personsResponse.data || [];

            // Criar um mapa de ID para objeto completo
            const personsMap = Array.isArray(persons)
              ? persons.reduce((map, person) => {
                  map[person.id] = person;
                  return map;
                }, {})
              : {};

            // Adicionar informações aos limites
            limitsArray.forEach(limit => {
              if (limit.personId && personsMap[limit.personId]) {
                limit.Person = personsMap[limit.personId];
              }
            });
          }

          // Buscar informações de convênios se necessário
          if (insuranceIds.length > 0) {
            const insurancesResponse = await api.get('/insurances', {
              params: { ids: insuranceIds.join(',') }
            });
            const insurances = insurancesResponse.data || [];

            // Criar um mapa de ID para objeto completo
            const insurancesMap = Array.isArray(insurances)
              ? insurances.reduce((map, insurance) => {
                  map[insurance.id] = insurance;
                  return map;
                }, {})
              : {};

            // Adicionar informações aos limites
            limitsArray.forEach(limit => {
              if (limit.insuranceId && insurancesMap[limit.insuranceId]) {
                limit.Insurance = insurancesMap[limit.insuranceId];
              }
            });
          }

          // Buscar informações de tipos de serviço se necessário
          if (serviceTypeIds.length > 0) {
            const serviceTypesResponse = await api.get('/service-types', {
              params: { ids: serviceTypeIds.join(',') }
            });
            const serviceTypes = serviceTypesResponse.data || [];

            // Criar um mapa de ID para objeto completo
            const serviceTypesMap = Array.isArray(serviceTypes)
              ? serviceTypes.reduce((map, serviceType) => {
                  map[serviceType.id] = serviceType;
                  return map;
                }, {})
              : {};

            // Adicionar informações aos limites
            limitsArray.forEach(limit => {
              if (limit.serviceTypeId && serviceTypesMap[limit.serviceTypeId]) {
                limit.ServiceType = serviceTypesMap[limit.serviceTypeId];
              }
            });
          }

          console.log("Dados enriquecidos com sucesso");
        } catch (error) {
          console.error("Erro ao enriquecer dados em lote:", error);

          // Tentar buscar individualmente para cada limite
          console.log("Tentando buscar dados individualmente...");

          // Criar funções para buscar entidades individuais
          const fetchPerson = async (id) => {
            try {
              const response = await api.get(`/persons/${id}`);
              return response.data;
            } catch (e) {
              console.error(`Erro ao buscar pessoa ${id}:`, e);
              return null;
            }
          };

          const fetchInsurance = async (id) => {
            try {
              const response = await api.get(`/insurances/${id}`);
              return response.data;
            } catch (e) {
              console.error(`Erro ao buscar convênio ${id}:`, e);
              return null;
            }
          };

          const fetchServiceType = async (id) => {
            try {
              const response = await api.get(`/service-types/${id}`);
              return response.data;
            } catch (e) {
              console.error(`Erro ao buscar tipo de serviço ${id}:`, e);
              return null;
            }
          };

          // Buscar dados para cada limite individualmente
          for (const limit of limitsArray) {
            // Buscar pessoa se necessário
            if (limit.personId && !limit.Person && !limit.person && !limit.personFullName) {
              const person = await fetchPerson(limit.personId);
              if (person) {
                limit.Person = person;
                console.log(`Pessoa ${limit.personId} encontrada individualmente`);
              }
            }

            // Buscar convênio se necessário
            if (limit.insuranceId && !limit.Insurance && !limit.insurance && !limit.insuranceName) {
              const insurance = await fetchInsurance(limit.insuranceId);
              if (insurance) {
                limit.Insurance = insurance;
                console.log(`Convênio ${limit.insuranceId} encontrado individualmente`);
              }
            }

            // Buscar tipo de serviço se necessário
            if (limit.serviceTypeId && !limit.ServiceType && !limit.serviceType && !limit.serviceTypeName) {
              const serviceType = await fetchServiceType(limit.serviceTypeId);
              if (serviceType) {
                limit.ServiceType = serviceType;
                console.log(`Tipo de serviço ${limit.serviceTypeId} encontrado individualmente`);
              }
            }
          }
        }
      }

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "patient", header: "Paciente" },
        { key: "insurance", header: "Convênio" },
        { key: "company", header: "Empresa" },
        { key: "serviceType", header: "Tipo de Serviço" },
        {
          key: "monthlyLimit",
          header: "Limite Mensal",
          format: (value) => value > 0 ? `${value}x por mês` : "Ilimitado"
        },
        { key: "notes", header: "Observações" },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = limitsArray.map(limit => {
        // Extrair informações do paciente
        let patientName = "N/A";
        if (limit.Person && limit.Person.fullName) {
          patientName = limit.Person.fullName;
        } else if (limit.person && limit.person.fullName) {
          patientName = limit.person.fullName;
        } else if (limit.personFullName) {
          patientName = limit.personFullName;
        } else if (limit.personId) {
          // Se tivermos apenas o ID, podemos tentar buscar o nome em outro lugar
          patientName = `ID: ${limit.personId}`;
        }

        // Extrair informações do convênio
        let insuranceName = "N/A";
        if (limit.Insurance && limit.Insurance.name) {
          insuranceName = limit.Insurance.name;
        } else if (limit.insurance && limit.insurance.name) {
          insuranceName = limit.insurance.name;
        } else if (limit.insuranceName) {
          insuranceName = limit.insuranceName;
        } else if (limit.insuranceId) {
          insuranceName = `ID: ${limit.insuranceId}`;
        }

        // Extrair informações do tipo de serviço
        let serviceTypeName = "N/A";
        if (limit.ServiceType && limit.ServiceType.name) {
          serviceTypeName = limit.ServiceType.name;
        } else if (limit.serviceType && limit.serviceType.name) {
          serviceTypeName = limit.serviceType.name;
        } else if (limit.serviceTypeName) {
          serviceTypeName = limit.serviceTypeName;
        } else if (limit.serviceTypeId) {
          serviceTypeName = `ID: ${limit.serviceTypeId}`;
        }

        // Extrair informações da empresa
        let companyName = "";
        if (limit.Insurance?.company?.name) {
          companyName = limit.Insurance.company.name;
        } else if (limit.insurance?.company?.name) {
          companyName = limit.insurance.company.name;
        }

        return {
          patient: patientName,
          insurance: insuranceName,
          company: companyName,
          serviceType: serviceTypeName,
          monthlyLimit: limit.monthlyLimit || 0,
          notes: limit.notes || "",
          createdAt: limit.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.personIds && filters.personIds.length > 0) {
        subtitleParts.push(`Pacientes específicos: ${filters.personIds.length} selecionados`);
      }
      if (filters.insuranceId) {
        subtitleParts.push(`Convênio: ${filters.insuranceName || filters.insuranceId}`);
      }
      if (filters.serviceTypeId) {
        subtitleParts.push(`Tipo de Serviço: ${filters.serviceTypeName || filters.serviceTypeId}`);
      }
      if (filters.companyId) {
        // Tentar encontrar o nome da empresa nos dados
        const companyName = limitsArray.find(l =>
          l.Insurance?.company?.id === filters.companyId ||
          l.insurance?.company?.id === filters.companyId
        )?.Insurance?.company?.name ||
        limitsArray.find(l =>
          l.Insurance?.company?.id === filters.companyId ||
          l.insurance?.company?.id === filters.companyId
        )?.insurance?.company?.name;

        subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "limites-convenio",
        columns,
        title: "Lista de Limites de Convênio",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar limites de convênio:", error);
      return false;
    }
  }
};

export default insuranceServiceLimitService;