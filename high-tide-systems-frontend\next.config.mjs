/** @type {import('next').NextConfig} */
const nextConfig = {
  // Habilitar output standalone para Docker
  output: 'standalone',
  
  // Manter a configuração experimental existente
  experimental: {
    turbo: {
      resolveAlias: {
        '@/*': './src/*'
      }
    },
  },
  
  // Configurar redirecionamentos para API se necessário
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/:path*`
      }
    ]
  },
}

export default nextConfig;