// scripts/migrate-professions.js
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Script para migrar os dados da coluna profession para o novo modelo Profession
 *
 * Este script deve ser executado após a migração do Prisma que cria as tabelas
 * Comando para executar: node scripts/migrate-professions.js
 */
async function main() {
  try {
    console.log('Iniciando migração de dados de profissões...');

    // 1. Buscar todos os usuários com profissão definida
    const users = await prisma.user.findMany({
      where: {
        profession: {
          not: null
        }
      },
      select: {
        id: true,
        profession: true,
        companyId: true
      }
    });

    console.log(`Encontrados ${users.length} usuários com profissão definida`);

    // 2. Agrupar usuários por profissão e empresa
    const professionsByCompany = {};

    users.forEach(user => {
      if (!user.profession || user.profession === 'Sem profissão') return;

      const key = `${user.profession}|${user.companyId || 'null'}`;
      if (!professionsByCompany[key]) {
        professionsByCompany[key] = {
          name: user.profession,
          companyId: user.companyId,
          users: []
        };
      }
      professionsByCompany[key].users.push(user.id);
    });

    console.log(`Agrupados em ${Object.keys(professionsByCompany).length} profissões distintas`);

    // 3. Criar as profissões no novo modelo
    for (const key of Object.keys(professionsByCompany)) {
      const { name, companyId, users } = professionsByCompany[key];

      console.log(`Criando profissão "${name}" para empresa ${companyId || 'global'}...`);

      // Verificar se a profissão já existe
      let profession = await prisma.profession.findFirst({
        where: {
          name,
          companyId
        }
      });

      // Se não existir, criar
      if (!profession) {
        profession = await prisma.profession.create({
          data: {
            name,
            companyId,
            description: `Migrado automaticamente da coluna profession`
          }
        });
        console.log(`Profissão "${name}" criada com ID ${profession.id}`);
      } else {
        console.log(`Profissão "${name}" já existe com ID ${profession.id}`);
      }

      // 4. Atualizar os usuários para usar a nova profissão
      console.log(`Atualizando ${users.length} usuários para usar a profissão "${name}"...`);

      for (const userId of users) {
        await prisma.user.update({
          where: { id: userId },
          data: { professionId: profession.id }
        });
      }

      console.log(`Usuários atualizados com sucesso para a profissão "${name}"`);
    }

    console.log('Migração de dados de profissões concluída com sucesso!');
  } catch (error) {
    console.error('Erro durante a migração de profissões:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
