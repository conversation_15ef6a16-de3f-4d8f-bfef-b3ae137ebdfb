// tests/controllers/aba-controller-test.js
require('dotenv').config();
const axios = require('axios');

// Configuração do cliente API
const api = axios.create({
  baseURL: process.env.API_URL || 'http://localhost:5000',
  timeout: 10000,
});

// Token de autenticação (substitua por um token válido)
let token = '';

// Função para fazer login e obter token
async function login() {
  try {
    const response = await api.post('/auth/login', {
      email: '<EMAIL>',
      password: 'Super@123',
    });
    
    token = response.data.token;
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    console.log('✅ Login realizado com sucesso');
  } catch (error) {
    console.error('❌ Erro ao fazer login:', error.message);
    process.exit(1);
  }
}

// Função principal para testar as rotas do módulo ABA+
async function testAbaModule() {
  console.log('\n=== TESTE DO MÓDULO ABA+ ===\n');
  
  try {
    // Fazer login primeiro
    await login();
    
    // Teste 1: Verificar status do módulo
    console.log('\n1. Verificando status do módulo ABA+...');
    const statusResponse = await api.get('/aba/status');
    
    if (statusResponse.status === 200) {
      console.log(`✅ Módulo ABA+ está funcionando. Usuário: ${statusResponse.data.user.name}`);
    } else {
      console.log('❌ Falha ao verificar status do módulo ABA+');
    }
    
    // Teste 2: Criar uma avaliação
    console.log('\n2. Criando uma avaliação...');
    const evaluationData = {
      type: 'SKILL_ACQUISITION',
      name: 'Avaliação de Teste',
      observations: 'Criada para teste da API'
    };
    
    const createEvaluationResponse = await api.post('/aba/evaluations', evaluationData);
    
    if (createEvaluationResponse.status === 201) {
      console.log('✅ Avaliação criada com sucesso');
      const evaluationId = createEvaluationResponse.data.id;
      
      // Teste 3: Buscar a avaliação criada
      console.log(`\n3. Buscando avaliação com ID ${evaluationId}...`);
      const getEvaluationResponse = await api.get(`/aba/evaluations/${evaluationId}`);
      
      if (getEvaluationResponse.status === 200) {
        console.log('✅ Avaliação encontrada:', getEvaluationResponse.data.name);
      } else {
        console.log('❌ Falha ao buscar avaliação');
      }
      
      // Teste 4: Criar um nível para a avaliação
      console.log('\n4. Criando um nível para a avaliação...');
      const levelData = {
        order: 1,
        description: 'Nível Básico',
        ageRange: '3-5 anos',
        evaluationId
      };
      
      const createLevelResponse = await api.post('/aba/levels', levelData);
      
      if (createLevelResponse.status === 201) {
        console.log('✅ Nível criado com sucesso');
        const levelId = createLevelResponse.data.id;
        
        // Teste 5: Buscar o nível criado
        console.log(`\n5. Buscando nível com ID ${levelId}...`);
        const getLevelResponse = await api.get(`/aba/levels/${levelId}`);
        
        if (getLevelResponse.status === 200) {
          console.log('✅ Nível encontrado:', getLevelResponse.data.description);
        } else {
          console.log('❌ Falha ao buscar nível');
        }
      } else {
        console.log('❌ Falha ao criar nível');
      }
      
      // Teste 6: Criar uma habilidade
      console.log('\n6. Criando uma habilidade...');
      const skillData = {
        code: 'HAB-001',
        order: 1,
        description: 'Habilidade de Teste'
      };
      
      const createSkillResponse = await api.post('/aba/skills', skillData);
      
      if (createSkillResponse.status === 201) {
        console.log('✅ Habilidade criada com sucesso');
        const skillId = createSkillResponse.data.id;
        
        // Teste 7: Adicionar a habilidade à avaliação
        console.log('\n7. Adicionando habilidade à avaliação...');
        const addSkillResponse = await api.post(`/aba/skills/evaluations/${evaluationId}/skills/${skillId}`);
        
        if (addSkillResponse.status === 201) {
          console.log('✅ Habilidade adicionada à avaliação com sucesso');
        } else {
          console.log('❌ Falha ao adicionar habilidade à avaliação');
        }
      } else {
        console.log('❌ Falha ao criar habilidade');
      }
      
      // Teste 8: Criar uma pontuação para a avaliação
      console.log('\n8. Criando uma pontuação para a avaliação...');
      const scoreData = {
        type: 'ALWAYS',
        value: '100',
        description: 'Pontuação de Teste',
        evaluationId
      };
      
      const createScoreResponse = await api.post('/aba/scores', scoreData);
      
      if (createScoreResponse.status === 201) {
        console.log('✅ Pontuação criada com sucesso');
        const scoreId = createScoreResponse.data.id;
        
        // Teste 9: Buscar a pontuação criada
        console.log(`\n9. Buscando pontuação com ID ${scoreId}...`);
        const getScoreResponse = await api.get(`/aba/scores/${scoreId}`);
        
        if (getScoreResponse.status === 200) {
          console.log('✅ Pontuação encontrada:', getScoreResponse.data.type);
        } else {
          console.log('❌ Falha ao buscar pontuação');
        }
      } else {
        console.log('❌ Falha ao criar pontuação');
      }
      
      // Teste 10: Listar todas as avaliações
      console.log('\n10. Listando todas as avaliações...');
      const listEvaluationsResponse = await api.get('/aba/evaluations');
      
      if (listEvaluationsResponse.status === 200) {
        console.log(`✅ ${listEvaluationsResponse.data.items.length} avaliações encontradas`);
      } else {
        console.log('❌ Falha ao listar avaliações');
      }
      
    } else {
      console.log('❌ Falha ao criar avaliação');
    }
    
    console.log('\n=== TESTES CONCLUÍDOS ===\n');
    
  } catch (error) {
    console.error('Erro durante os testes:', error.response?.data || error.message);
  }
}

// Executar os testes
testAbaModule();
