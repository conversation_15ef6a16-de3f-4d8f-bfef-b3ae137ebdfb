'use client';

import React, { useState, useEffect } from 'react';
import { Search, RefreshCw, Filter } from 'lucide-react';
import { FilterButton } from '@/components/ui/ModuleHeader';
import MultiSelect from '@/components/ui/multi-select';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { useAuth } from '@/contexts/AuthContext';

export const ClientCalendarFilters = ({ filters = {}, onFiltersChange, onSearch }) => {
  const [providers, setProviders] = useState([]); // Novo estado para profissionais
  const [persons, setPersons] = useState([]);
  const [locations, setLocations] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);
  const [error, setError] = useState(null);
  const { user } = useAuth();

  // Função para carregar apenas os dados relacionados aos agendamentos do cliente
  const loadClientRelatedData = async () => {
    try {
      setError(null);
      setIsLoading(true);
      console.log("Loading client-related data for calendar filters...");

      // Primeiro, buscar todos os agendamentos do cliente
      const clientAppointments = await appointmentService.getAppointments();
      console.log(`Loaded ${clientAppointments?.appointments?.length || 0} client appointments`);

      if (!clientAppointments?.appointments?.length) {
        setIsLoading(false);
        return;
      }

      // Extrair IDs únicos de pessoas, locais, tipos de serviço e profissionais dos agendamentos
      const personIds = new Set();
      const locationIds = new Set();
      const serviceTypeIds = new Set();
      const providerIds = new Set();

      clientAppointments.appointments.forEach(appointment => {
        // Extrair personId do relacionamento Person ou do campo personId
        if (appointment.Person && appointment.Person.length > 0) {
          appointment.Person.forEach(person => {
            if (person && person.id) personIds.add(person.id);
          });
        }

        if (appointment.locationId) locationIds.add(appointment.locationId);
        if (appointment.serviceTypeId) serviceTypeIds.add(appointment.serviceTypeId);
        if (appointment.userId) providerIds.add(appointment.userId);
      });

      console.log("Extracted unique IDs:", {
        persons: Array.from(personIds),
        locations: Array.from(locationIds),
        serviceTypes: Array.from(serviceTypeIds),
        providers: Array.from(providerIds)
      });

      // Buscar dados completos para cada tipo
      const [personsData, locationsData, serviceTypesData, providersData] = await Promise.all([
        appointmentService.getPersons(),
        appointmentService.getLocations(),
        appointmentService.getServiceTypes(),
        appointmentService.getProviders()
      ]);

      // Filtrar apenas os itens relacionados aos agendamentos do cliente
      const filteredPersons = (personsData || [])
        .filter(p => p && p.id && personIds.has(p.id))
        .map(person => ({
          value: person.id,
          label: person.fullName
        }));

      const filteredLocations = (locationsData || [])
        .filter(l => l && l.id && locationIds.has(l.id))
        .map(location => ({
          value: location.id,
          label: location.name
        }));

      const filteredServiceTypes = (serviceTypesData || [])
        .filter(s => s && s.id && serviceTypeIds.has(s.id))
        .map(serviceType => ({
          value: serviceType.id,
          label: serviceType.name
        }));

      // Filtrar profissionais relacionados aos agendamentos do cliente
      const filteredProviders = (providersData || [])
        .filter(p => p && p.id && providerIds.has(p.id))
        .map(provider => ({
          value: provider.id,
          label: provider.fullName
        }));

      console.log("Filtered data for client:", {
        persons: filteredPersons.length,
        locations: filteredLocations.length,
        serviceTypes: filteredServiceTypes.length,
        providers: filteredProviders.length
      });

      setPersons(filteredPersons);
      setLocations(filteredLocations);
      setServiceTypes(filteredServiceTypes);
      setProviders(filteredProviders);
    } catch (err) {
      console.error("Error loading client-related data:", err);
      setError("Erro ao carregar dados para os filtros. Por favor, tente novamente.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadClientRelatedData();
  }, [user?.id]);

  useEffect(() => {
    const hasActiveFilters =
      filters.providers?.length > 0 ||
      filters.persons?.length > 0 ||
      filters.locations?.length > 0 ||
      filters.serviceTypes?.length > 0;
    setHasChanges(hasActiveFilters);
  }, [filters]);

  const handleFilterChange = (newFilters) => {
    onFiltersChange(newFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      providers: [],
      persons: [],
      locations: [],
      serviceTypes: []
    };
    onFiltersChange(clearedFilters);
    // Dispara a busca imediatamente após limpar os filtros
    onSearch(clearedFilters);
  };

  return (
    <div className="rounded-lg">
      {error && (
        <div className="p-3 mb-4 bg-error-50 dark:bg-error-900/20 text-error-700 dark:text-error-300 rounded-lg border border-error-200 dark:border-error-700">
          {error}
        </div>
      )}

      <div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <MultiSelect
            label="Profissionais"
            value={filters.providers || []}
            onChange={(value) => handleFilterChange({ ...filters, providers: value })}
            options={providers}
            placeholder="Selecione os profissionais"
            loading={isLoading}
            moduleOverride="scheduler"
          />

          <MultiSelect
            label="Pacientes"
            value={filters.persons || []}
            onChange={(value) => handleFilterChange({ ...filters, persons: value })}
            options={persons}
            placeholder="Selecione os pacientes"
            loading={isLoading}
            moduleOverride="scheduler"
          />

          <MultiSelect
            label="Locais"
            value={filters.locations || []}
            onChange={(value) => handleFilterChange({ ...filters, locations: value })}
            options={locations}
            placeholder="Selecione os locais"
            loading={isLoading}
            moduleOverride="scheduler"
          />

          <MultiSelect
            label="Tipos de Serviço"
            value={filters.serviceTypes || []}
            onChange={(value) => handleFilterChange({ ...filters, serviceTypes: value })}
            options={serviceTypes}
            placeholder="Selecione os tipos"
            loading={isLoading}
            moduleOverride="scheduler"
          />
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end items-center gap-2">
        <FilterButton
          type="button"
          onClick={handleClearFilters}
          moduleColor="scheduler"
          variant="secondary"
        >
          <div className="flex items-center gap-2">
            <RefreshCw size={16} />
            <span>Limpar Filtros</span>
          </div>
        </FilterButton>

        <FilterButton
          type="button"
          onClick={() => onSearch(filters)}
          moduleColor="scheduler"
          variant="primary"
          disabled={!hasChanges}
          className={hasChanges ? '' : 'opacity-50 cursor-not-allowed'}
        >
          <div className="flex items-center gap-2">
            <Search size={16} />
            <span>Buscar</span>
          </div>
        </FilterButton>
      </div>
    </div>
  );
};
