-- CreateEnum
CREATE TYPE "ProgramStatus" AS ENUM ('unallocated', 'inTraining', 'completed');

-- CreateTable
CREATE TABLE "CurriculumFolderProgram" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "protocol" TEXT,
    "skill" TEXT,
    "milestone" TEXT,
    "teachingType" TEXT,
    "targetsPerSession" INTEGER DEFAULT 1,
    "attemptsPerTarget" INTEGER DEFAULT 1,
    "teachingProcedure" TEXT DEFAULT '',
    "instruction" TEXT DEFAULT '',
    "objective" TEXT DEFAULT '',
    "promptStep" TEXT DEFAULT '',
    "correctionProcedure" TEXT DEFAULT '',
    "learningCriteria" TEXT DEFAULT '',
    "materials" TEXT DEFAULT '',
    "notes" TEXT DEFAULT '',
    "status" "ProgramStatus" NOT NULL DEFAULT 'unallocated',
    "originalProgramId" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "curriculumFolderId" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "CurriculumFolderProgram_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_curriculumFolderId_idx" ON "CurriculumFolderProgram"("curriculumFolderId");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_companyId_idx" ON "CurriculumFolderProgram"("companyId");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_active_idx" ON "CurriculumFolderProgram"("active");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_status_idx" ON "CurriculumFolderProgram"("status");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_createdById_idx" ON "CurriculumFolderProgram"("createdById");

-- CreateIndex
CREATE INDEX "CurriculumFolderProgram_originalProgramId_idx" ON "CurriculumFolderProgram"("originalProgramId");

-- AddForeignKey
ALTER TABLE "CurriculumFolderProgram" ADD CONSTRAINT "CurriculumFolderProgram_curriculumFolderId_fkey" FOREIGN KEY ("curriculumFolderId") REFERENCES "CurriculumFolder"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolderProgram" ADD CONSTRAINT "CurriculumFolderProgram_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolderProgram" ADD CONSTRAINT "CurriculumFolderProgram_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
