const { PrismaClient } = require('@prisma/client');
const axios = require('axios');
const prisma = new PrismaClient();

async function main() {
  console.log('Iniciando atualização de endereços dos usuários...');

  // Buscar todos os usuários
  const users = await prisma.user.findMany({
    where: {
      role: 'EMPLOYEE',
      active: true
    }
  });

  console.log(`Encontrados ${users.length} usuários para atualizar.`);

  // Lista de CEPs válidos de diferentes regiões do Brasil
  const cepList = [
    // São Paulo
    '01310-200', '04538-132', '05402-000', '02012-021', '03318-000',
    '04571-010', '01311-000', '05305-006', '04547-130', '01452-001',
    // Rio de Janeiro
    '22031-001', '20021-130', '22250-040', '20040-002', '22410-003',
    '20050-090', '22790-703', '20230-010', '22631-004', '20211-340',
    // Belo Horizonte
    '30130-100', '30112-010', '31270-901', '30140-072', '30310-000',
    '30180-090', '30180-100', '30130-110', '31035-560', '30130-170',
    // Brasília
    '70070-120', '70297-400', '70200-002', '70680-500', '70770-100',
    '70910-900', '70040-020', '70673-410', '70343-902', '70310-500',
    // Salvador
    '40026-010', '41820-000', '40140-130', '40170-110', '41950-275',
    '40140-460', '40243-045', '40280-000', '40050-410', '40301-110',
    // Porto Alegre
    '90010-282', '90619-900', '90480-000', '90110-150', '90570-020',
    '90040-191', '90050-170', '90035-001', '90130-120', '90160-070',
    // Recife
    '50030-150', '50070-120', '50030-310', '50080-000', '50090-000',
    '50110-160', '50030-230', '50050-380', '50080-090', '50100-010',
    // Curitiba
    '80010-010', '80240-000', '80530-000', '80420-170', '80610-905',
    '80215-010', '80520-590', '80320-300', '80810-110', '80030-110',
    // Fortaleza
    '60175-047', '60115-282', '60160-230', '60125-001', '60811-341',
    '60165-082', '60150-161', '60135-420', '60115-170', '60130-001',
    // Manaus
    '69050-001', '69010-060', '69020-110', '69005-040', '69028-140',
    '69010-150', '69010-070', '69010-140', '69020-200', '69010-110'
  ];

  // Lista de complementos para adicionar ao endereço
  const complementos = [
    'Apto 101', 'Apto 202', 'Apto 303', 'Apto 404', 'Apto 505',
    'Apto 606', 'Apto 707', 'Apto 808', 'Apto 909', 'Apto 1010',
    'Casa 1', 'Casa 2', 'Casa 3', 'Casa 4', 'Casa 5',
    'Bloco A', 'Bloco B', 'Bloco C', 'Bloco D', 'Bloco E',
    'Sala 101', 'Sala 202', 'Sala 303', 'Sala 404', 'Sala 505',
    'Fundos', 'Térreo', 'Sobrado', 'Cobertura', 'Loja 1',
    'Loja 2', 'Conj. 101', 'Conj. 202', 'Conj. 303', 'Conj. 404'
  ];

  // Função para buscar endereço pelo CEP usando a API ViaCEP
  async function getAddressByCep(cep) {
    try {
      const cleanCep = cep.replace(/\D/g, '');
      const response = await axios.get(`https://viacep.com.br/ws/${cleanCep}/json/`);
      
      if (response.data.erro) {
        throw new Error('CEP não encontrado');
      }
      
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar CEP ${cep}:`, error.message);
      return null;
    }
  }

  // Função para obter um item aleatório de uma lista
  function getRandomItem(list) {
    return list[Math.floor(Math.random() * list.length)];
  }

  // Atualizar cada usuário com um endereço aleatório
  for (const user of users) {
    try {
      // Selecionar um CEP aleatório
      const cep = getRandomItem(cepList);
      
      // Buscar o endereço pelo CEP
      const addressData = await getAddressByCep(cep);
      
      if (!addressData) {
        console.log(`Não foi possível obter endereço para o usuário ${user.fullName} com o CEP ${cep}. Pulando...`);
        continue;
      }
      
      // Selecionar um complemento aleatório
      const complemento = getRandomItem(complementos);
      
      // Montar o endereço completo
      const fullAddress = `${addressData.logradouro}, ${Math.floor(Math.random() * 1000) + 1} - ${complemento}`;
      
      // Atualizar o usuário
      await prisma.user.update({
        where: { id: user.id },
        data: {
          address: fullAddress,
          neighborhood: addressData.bairro,
          city: addressData.localidade,
          state: addressData.uf,
          postalCode: addressData.cep
        }
      });
      
      console.log(`Usuário atualizado: ${user.fullName} - Endereço: ${fullAddress}, ${addressData.bairro}, ${addressData.localidade}/${addressData.uf}, CEP: ${addressData.cep}`);
    } catch (error) {
      console.error(`Erro ao atualizar usuário ${user.fullName}:`, error);
    }
  }

  console.log('\nAtualização de endereços dos usuários concluída com sucesso!');
}

main()
  .catch((e) => {
    console.error('Erro durante a atualização de endereços:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
