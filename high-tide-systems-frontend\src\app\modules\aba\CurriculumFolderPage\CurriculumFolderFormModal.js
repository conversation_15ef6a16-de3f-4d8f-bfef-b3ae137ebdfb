"use client";

import React, { useState, useEffect } from "react";
import { FileText, User, Check } from "lucide-react";
import {
  ModuleModal,
  ModuleInput,
  ModuleSelect,
  ModuleFormGroup
} from "@/components/ui";
import { useToast } from "@/contexts/ToastContext";
import curriculumFolderService from "../services/curriculumFolderService";
import { personsService } from "@/app/modules/people/services/personsService";

const CurriculumFolderFormModal = ({ isOpen, onClose, folder }) => {
  const { toast_success, toast_error } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [persons, setPersons] = useState([]);
  const [isLoadingPersons, setIsLoadingPersons] = useState(false);

  // Estado do formulário
  const [formData, setFormData] = useState({
    name: "",
    personId: "",
    shareWithParents: false,
    shareWithSchools: false
  });

  // Carregar dados do formulário se estiver editando
  useEffect(() => {
    if (folder) {
      setFormData({
        name: folder.name || "",
        personId: folder.personId || "",
        shareWithParents: folder.shareWithParents || false,
        shareWithSchools: folder.shareWithSchools || false
      });
    }
  }, [folder]);

  // Carregar lista de pacientes
  useEffect(() => {
    const loadPersons = async () => {
      setIsLoadingPersons(true);
      try {
        const response = await personsService.getPersons({
          limit: 100,
          active: true
        });

        const personsArray = response?.persons || response?.people || [];

        // Ordenar por nome
        const sortedPersons = [...personsArray].sort((a, b) =>
          a.fullName.toLowerCase().localeCompare(b.fullName.toLowerCase())
        );

        setPersons(sortedPersons);
      } catch (error) {
        console.error("Erro ao carregar pacientes:", error);
        toast_error({
          title: "Erro",
          message: "Não foi possível carregar a lista de aprendizes."
        });
      } finally {
        setIsLoadingPersons(false);
      }
    };

    loadPersons();
  }, [toast_error]);

  // Handler para mudanças nos campos do formulário
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }));
  };

  // Handler para submissão do formulário
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validação básica
    if (!formData.name.trim()) {
      toast_error({
        title: "Erro de Validação",
        message: "O nome da pasta é obrigatório."
      });
      return;
    }

    if (!formData.personId) {
      toast_error({
        title: "Erro de Validação",
        message: "É necessário selecionar um aprendiz."
      });
      return;
    }

    setIsSubmitting(true);

    try {
      if (folder) {
        // Atualizar pasta existente
        await curriculumFolderService.updateCurriculumFolder(folder.id, formData);
        toast_success({
          title: "Sucesso",
          message: "Pasta curricular atualizada com sucesso."
        });
      } else {
        // Criar nova pasta
        await curriculumFolderService.createCurriculumFolder(formData);
        toast_success({
          title: "Sucesso",
          message: "Pasta curricular criada com sucesso."
        });
      }

      // Fechar modal e atualizar lista
      onClose(true);
    } catch (error) {
      console.error("Erro ao salvar pasta curricular:", error);
      toast_error({
        title: "Erro",
        message: `Não foi possível ${folder ? "atualizar" : "criar"} a pasta curricular.`
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Renderizar o footer do modal
  const renderFooter = () => (
    <div className="flex justify-end gap-2">
      <button
        type="button"
        onClick={() => onClose()}
        className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        disabled={isSubmitting}
      >
        Cancelar
      </button>
      <button
        type="submit"
        form="curriculum-folder-form"
        className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <>
            <span className="animate-spin">
              <svg className="w-4 h-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            <span>Salvando...</span>
          </>
        ) : (
          <>
            <Check size={16} />
            <span>Salvar</span>
          </>
        )}
      </button>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={folder ? "Editar Pasta Curricular" : "Nova Pasta Curricular"}
      size="md"
      moduleColor="abaplus"
      icon={<FileText size={20} />}
      footer={renderFooter()}
    >
      <form id="curriculum-folder-form" onSubmit={handleSubmit} className="p-6 space-y-6">
        {/* Nome da Pasta */}
        <ModuleFormGroup
          moduleColor="abaplus"
          label="Nome da Pasta"
          htmlFor="name"
          required
        >
          <ModuleInput
            moduleColor="abaplus"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Digite o nome da pasta curricular"
            required
          />
        </ModuleFormGroup>

        {/* Aprendiz (Paciente) */}
        <ModuleFormGroup
          moduleColor="abaplus"
          label="Aprendiz"
          htmlFor="personId"
          icon={<User size={16} />}
          required
        >
          <ModuleSelect
            moduleColor="abaplus"
            id="personId"
            name="personId"
            value={formData.personId}
            onChange={handleChange}
            disabled={isLoadingPersons || isSubmitting}
            required
            placeholder="Selecione um aprendiz"
          >
            <option value="">Selecione um aprendiz</option>
            {persons.map((person) => (
              <option key={person.id} value={person.id}>
                {person.fullName}
              </option>
            ))}
          </ModuleSelect>
        </ModuleFormGroup>

        {/* Opções de Compartilhamento */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Disponibilizar para:</h3>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="shareWithParents"
              name="shareWithParents"
              checked={formData.shareWithParents}
              onChange={handleChange}
              className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-teal-600 dark:text-teal-500 focus:ring-teal-500 dark:focus:ring-teal-400"
            />
            <label htmlFor="shareWithParents" className="text-sm text-gray-700 dark:text-gray-300">
              Responsáveis
            </label>
          </div>

          <div className="flex items-center gap-2">
            <input
              type="checkbox"
              id="shareWithSchools"
              name="shareWithSchools"
              checked={formData.shareWithSchools}
              onChange={handleChange}
              className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-teal-600 dark:text-teal-500 focus:ring-teal-500 dark:focus:ring-teal-400"
            />
            <label htmlFor="shareWithSchools" className="text-sm text-gray-700 dark:text-gray-300">
              Escolas
            </label>
          </div>
        </div>
      </form>
    </ModuleModal>
  );
};

export default CurriculumFolderFormModal;
