const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Iniciando seed de profissões...');

  // Buscar 3 empresas aleatórias das 5 que foram criadas anteriormente
  const companies = await prisma.company.findMany({
    where: {
      NOT: {
        id: '00000000-0000-0000-0000-000000000001' // Excluir a empresa de teste
      }
    },
    take: 5
  });

  // Selecionar 3 empresas aleatoriamente
  const selectedCompanies = companies
    .sort(() => 0.5 - Math.random()) // Embaralhar array
    .slice(0, 3); // Pegar as primeiras 3

  console.log(`Selecionadas ${selectedCompanies.length} empresas para adicionar profissões:`);
  selectedCompanies.forEach(company => {
    console.log(`- ${company.name} (ID: ${company.id})`);
  });

  // Definir grupos de profissões para área da saúde
  const healthGroups = [
    {
      name: '<PERSON><PERSON>',
      description: 'Profissionais médicos de diversas especialidades',
      professions: [
        { name: 'Médico Clínico Geral', description: 'Atendimento médico geral e diagnóstico inicial' },
        { name: 'Médico Cardiologista', description: 'Especialista em doenças cardiovasculares' },
        { name: 'Médico Pediatra', description: 'Especialista em saúde infantil' },
        { name: 'Médico Ortopedista', description: 'Especialista em sistema músculo-esquelético' },
        { name: 'Médico Neurologista', description: 'Especialista em sistema nervoso' }
      ]
    },
    {
      name: 'Odontologia',
      description: 'Profissionais de saúde bucal',
      professions: [
        { name: 'Dentista Clínico Geral', description: 'Atendimento odontológico geral' },
        { name: 'Dentista Ortodontista', description: 'Especialista em correção dentária' },
        { name: 'Dentista Endodontista', description: 'Especialista em tratamento de canal' },
        { name: 'Dentista Periodontista', description: 'Especialista em tratamento de gengivas' }
      ]
    },
    {
      name: 'Terapias',
      description: 'Profissionais de terapias e reabilitação',
      professions: [
        { name: 'Fisioterapeuta', description: 'Reabilitação física e motora' },
        { name: 'Terapeuta Ocupacional', description: 'Reabilitação para atividades diárias' },
        { name: 'Fonoaudiólogo', description: 'Especialista em comunicação e linguagem' },
        { name: 'Psicólogo', description: 'Saúde mental e comportamental' }
      ]
    },
    {
      name: 'Enfermagem',
      description: 'Profissionais de enfermagem e cuidados',
      professions: [
        { name: 'Enfermeiro', description: 'Cuidados de enfermagem geral' },
        { name: 'Técnico de Enfermagem', description: 'Suporte em procedimentos de enfermagem' },
        { name: 'Auxiliar de Enfermagem', description: 'Apoio em cuidados básicos de saúde' }
      ]
    }
  ];

  // Definir grupos de profissões para área administrativa
  const adminGroups = [
    {
      name: 'Recursos Humanos',
      description: 'Profissionais de gestão de pessoas',
      professions: [
        { name: 'Analista de RH', description: 'Gestão de processos de recursos humanos' },
        { name: 'Recrutador', description: 'Especialista em seleção de pessoal' },
        { name: 'Gestor de Treinamento', description: 'Responsável por capacitação e desenvolvimento' },
        { name: 'Analista de Departamento Pessoal', description: 'Gestão de folha de pagamento e benefícios' }
      ]
    },
    {
      name: 'Financeiro',
      description: 'Profissionais de gestão financeira',
      professions: [
        { name: 'Contador', description: 'Responsável pela contabilidade' },
        { name: 'Analista Financeiro', description: 'Análise e planejamento financeiro' },
        { name: 'Tesoureiro', description: 'Gestão de fluxo de caixa' },
        { name: 'Auditor', description: 'Verificação de conformidade financeira' }
      ]
    },
    {
      name: 'Administrativo',
      description: 'Profissionais de suporte administrativo',
      professions: [
        { name: 'Assistente Administrativo', description: 'Suporte em rotinas administrativas' },
        { name: 'Secretário(a)', description: 'Gestão de agenda e comunicações' },
        { name: 'Recepcionista', description: 'Atendimento inicial e direcionamento' }
      ]
    },
    {
      name: 'Tecnologia',
      description: 'Profissionais de tecnologia e sistemas',
      professions: [
        { name: 'Analista de Sistemas', description: 'Desenvolvimento e manutenção de sistemas' },
        { name: 'Suporte Técnico', description: 'Atendimento a problemas de TI' },
        { name: 'Administrador de Rede', description: 'Gestão da infraestrutura de rede' }
      ]
    }
  ];

  // Para cada empresa selecionada, criar os grupos e profissões
  for (const company of selectedCompanies) {
    console.log(`\nCriando grupos e profissões para ${company.name}...`);
    
    // Criar grupos de saúde e suas profissões
    for (const groupData of healthGroups) {
      // Verificar se o grupo já existe
      let group = await prisma.professionGroup.findFirst({
        where: {
          name: groupData.name,
          companyId: company.id
        }
      });

      // Se não existir, criar o grupo
      if (!group) {
        group = await prisma.professionGroup.create({
          data: {
            name: groupData.name,
            description: groupData.description,
            companyId: company.id
          }
        });
        console.log(`Grupo criado: ${group.name} (ID: ${group.id})`);
      } else {
        console.log(`Grupo ${group.name} já existe para esta empresa. Pulando criação...`);
      }

      // Criar as profissões deste grupo
      for (const professionData of groupData.professions) {
        // Verificar se a profissão já existe
        const existingProfession = await prisma.profession.findFirst({
          where: {
            name: professionData.name,
            companyId: company.id
          }
        });

        // Se não existir, criar a profissão
        if (!existingProfession) {
          const profession = await prisma.profession.create({
            data: {
              name: professionData.name,
              description: professionData.description,
              groupId: group.id,
              companyId: company.id
            }
          });
          console.log(`  Profissão criada: ${profession.name}`);
        } else {
          console.log(`  Profissão ${professionData.name} já existe para esta empresa. Pulando criação...`);
        }
      }
    }

    // Criar grupos administrativos e suas profissões
    for (const groupData of adminGroups) {
      // Verificar se o grupo já existe
      let group = await prisma.professionGroup.findFirst({
        where: {
          name: groupData.name,
          companyId: company.id
        }
      });

      // Se não existir, criar o grupo
      if (!group) {
        group = await prisma.professionGroup.create({
          data: {
            name: groupData.name,
            description: groupData.description,
            companyId: company.id
          }
        });
        console.log(`Grupo criado: ${group.name} (ID: ${group.id})`);
      } else {
        console.log(`Grupo ${group.name} já existe para esta empresa. Pulando criação...`);
      }

      // Criar as profissões deste grupo
      for (const professionData of groupData.professions) {
        // Verificar se a profissão já existe
        const existingProfession = await prisma.profession.findFirst({
          where: {
            name: professionData.name,
            companyId: company.id
          }
        });

        // Se não existir, criar a profissão
        if (!existingProfession) {
          const profession = await prisma.profession.create({
            data: {
              name: professionData.name,
              description: professionData.description,
              groupId: group.id,
              companyId: company.id
            }
          });
          console.log(`  Profissão criada: ${profession.name}`);
        } else {
          console.log(`  Profissão ${professionData.name} já existe para esta empresa. Pulando criação...`);
        }
      }
    }
  }

  console.log('\nSeed de profissões concluído com sucesso!');
}

main()
  .catch((e) => {
    console.error('Erro durante o seed de profissões:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
