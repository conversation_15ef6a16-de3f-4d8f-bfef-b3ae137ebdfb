// src/controllers/aba/levelController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createLevelValidation = [
  body("order").isInt({ min: 1 }).withMessage("Ordem deve ser um número inteiro positivo"),
  body("description").notEmpty().withMessage("Descrição do nível é obrigatória"),
  body("ageRange").optional(),
  body("evaluationId").notEmpty().withMessage("ID da avaliação é obrigatório"),
];

const updateLevelValidation = [
  body("order").optional().isInt({ min: 1 }).withMessage("Ordem deve ser um número inteiro positivo"),
  body("description").optional().notEmpty().withMessage("Descrição do nível é obrigatória"),
  body("ageRange").optional(),
];

class LevelController {
  /**
   * Cria um novo nível
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { order, description, ageRange, evaluationId } = req.body;

      // Verificar se a avaliação existe e pertence à empresa do usuário
      const evaluation = await prisma.evaluation.findUnique({
        where: { id: evaluationId },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para adicionar níveis a esta avaliação" });
      }

      // Criar nível
      const level = await prisma.level.create({
        data: {
          order,
          description,
          ageRange,
          evaluationId,
        },
      });

      res.status(201).json(level);
    } catch (error) {
      console.error("Erro ao criar nível:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todos os níveis com filtros e paginação
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search = "",
        evaluationId,
      } = req.query;

      // Construir filtros
      const where = {};

      // Filtro por avaliação
      if (evaluationId) {
        where.evaluationId = evaluationId;

        // Verificar se a avaliação pertence à empresa do usuário
        const evaluation = await prisma.evaluation.findUnique({
          where: { id: evaluationId },
        });

        if (!evaluation) {
          return res.status(404).json({ message: "Avaliação não encontrada" });
        }

        if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
          return res.status(403).json({ message: "Você não tem permissão para visualizar níveis desta avaliação" });
        }
      } else {
        // Se não for filtrado por avaliação, filtrar por empresa
        where.evaluation = {
          companyId: req.user.companyId,
          deletedAt: null,
        };
      }

      // Filtro de busca
      if (search) {
        where.OR = [
          { description: { contains: search, mode: "insensitive" } },
          { ageRange: { contains: search, mode: "insensitive" } },
        ];
      }

      // Contar total de registros
      const total = await prisma.level.count({ where });

      // Buscar níveis com paginação
      const levels = await prisma.level.findMany({
        where,
        include: {
          evaluation: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
        orderBy: [
          { evaluationId: "asc" },
          { order: "asc" },
        ],
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar resposta
      const response = formatSuccessResponse(
        levels,
        "levels",
        total,
        Math.ceil(total / Number(limit))
      );

      res.json(response);
    } catch (error) {
      console.error("Erro ao listar níveis:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém um nível específico pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const level = await prisma.level.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              id: true,
              name: true,
              type: true,
              companyId: true,
            },
          },
        },
      });

      if (!level) {
        return res.status(404).json({ message: "Nível não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && level.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para visualizar este nível" });
      }

      res.json(level);
    } catch (error) {
      console.error("Erro ao buscar nível:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza um nível existente
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Verificar se o nível existe
      const existingLevel = await prisma.level.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              companyId: true,
            },
          },
        },
      });

      if (!existingLevel) {
        return res.status(404).json({ message: "Nível não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingLevel.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para atualizar este nível" });
      }

      const { order, description, ageRange } = req.body;

      // Atualizar nível
      const updatedLevel = await prisma.level.update({
        where: { id },
        data: {
          order: order !== undefined ? order : undefined,
          description: description !== undefined ? description : undefined,
          ageRange: ageRange !== undefined ? ageRange : undefined,
          updatedAt: new Date(),
        },
      });

      res.json(updatedLevel);
    } catch (error) {
      console.error("Erro ao atualizar nível:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove um nível
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o nível existe
      const level = await prisma.level.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              companyId: true,
            },
          },
        },
      });

      if (!level) {
        return res.status(404).json({ message: "Nível não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && level.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para remover este nível" });
      }

      // Remover nível
      await prisma.level.delete({
        where: { id },
      });

      res.json({ message: "Nível removido com sucesso" });
    } catch (error) {
      console.error("Erro ao remover nível:", error);
      
      // Verificar se o erro é de restrição de chave estrangeira
      if (error.code === 'P2003') {
        return res.status(400).json({ 
          message: "Não é possível remover este nível porque ele está sendo usado em tarefas/testes" 
        });
      }
      
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  LevelController,
  createLevelValidation,
  updateLevelValidation,
};
