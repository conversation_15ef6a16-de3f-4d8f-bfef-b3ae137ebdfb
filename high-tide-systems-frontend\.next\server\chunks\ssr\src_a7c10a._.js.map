{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/clientsService.js"], "sourcesContent": ["// src/app/modules/people/services/clientsService.js\r\nimport { exportService } from \"@/app/services/exportService\";\r\nimport { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { extractData, extractEntity } from \"@/utils/apiResponseAdapter\";\r\n\r\nexport const clientsService = {\r\n  // Get clients with optional filters\r\n  getClients: async (filters = {}) => {\r\n    // Ensure page is a number and at least 1\r\n    const page = parseInt(filters.page, 10) || 1;\r\n    const limit = parseInt(filters.limit, 10) || 10;\r\n    const {\r\n      search = \"\",\r\n      clientIds,\r\n      active,\r\n      companyId,\r\n      include_persons,\r\n      sortField = 'fullName',  // Default sort by fullName (backend controller will handle this)\r\n      sortDirection = 'asc'    // Default sort direction\r\n    } = filters;\r\n\r\n    try {\r\n      // Construir parâmetros para a API\r\n      const params = {\r\n        page,\r\n        limit,\r\n        search: search || undefined,\r\n        active: active === undefined ? undefined : active,\r\n        companyId: companyId || undefined,\r\n        include_persons: include_persons || undefined,\r\n        sortField,\r\n        sortDirection\r\n      };\r\n\r\n      // Adicionar clientIds como parâmetros separados com notação de array\r\n      if (clientIds && clientIds.length > 0) {\r\n        // Garantir que clientIds seja um array\r\n        const clientIdsArray = Array.isArray(clientIds) ? clientIds : [clientIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        clientIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          params[`clientIds[${index}]`] = id;\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de clientes:\", clientIdsArray);\r\n      }\r\n\r\n      const response = await api.get(\"/clients\", { params });\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      return extractData(response.data, 'clients', ['data']);\r\n    } catch (error) {\r\n      console.error(\"Error fetching clients:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get a single client by ID\r\n  getClient: async (id) => {\r\n    try {\r\n      const response = await api.get(`/clients/${id}`);\r\n      // Usar o adaptador para extrair a entidade\r\n      return extractEntity(response.data);\r\n    } catch (error) {\r\n      console.error(`Error fetching client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create a new client\r\n  createClient: async (clientData) => {\r\n    try {\r\n      // Ajustar payload para incluir dados da pessoa\r\n      const response = await api.post(\"/clients\", clientData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating client:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update an existing client\r\n  updateClient: async (id, clientData) => {\r\n    try {\r\n      const response = await api.put(`/clients/${id}`, clientData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error updating client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Toggle client active status\r\n  toggleClientStatus: async (id) => {\r\n    try {\r\n      const response = await api.patch(`/clients/${id}/status`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error toggling status for client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a client (soft delete)\r\n  deleteClient: async (id) => {\r\n    try {\r\n      const response = await api.delete(`/clients/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error deleting client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get persons associated with a client\r\n  getClientPersons: async (clientId) => {\r\n    try {\r\n      const response = await api.get(`/clients/${clientId}/persons`);\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      const { persons } = extractData(response.data, 'persons', ['people']);\r\n      return persons;\r\n    } catch (error) {\r\n      console.error(`Error fetching persons for client ${clientId}:`, error);\r\n      return [];\r\n    }\r\n  },\r\n  /**\r\n * Exporta a lista de clientes com os filtros aplicados\r\n * @param {Object} filters - Filtros atuais (busca, status, etc)\r\n * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n */\r\n  exportClients: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await clientsService.getClients({\r\n        ...filters,\r\n        limit: 1000, // Aumentamos o limite para exportar mais dados\r\n      });\r\n\r\n      // Extrair os dados dos clientes usando o adaptador\r\n      const { clients } = extractData(response, 'clients', ['data']);\r\n      const data = clients;\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"fullName\", header: \"Cliente\" },\r\n        { key: \"login\", header: \"Login\" },\r\n        { key: \"email\", header: \"Email\" },\r\n        {\r\n          key: \"personsCount\",\r\n          header: \"Nº de Pessoas\",\r\n          format: (_, item) => item.persons ? item.persons.length : 0\r\n        },\r\n        {\r\n          key: \"active\",\r\n          header: \"Status\",\r\n          format: (value) => value ? \"Ativo\" : \"Inativo\",\r\n          align: \"center\",\r\n          width: 20\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = data.map(client => {\r\n        // Obter o nome completo do titular ou usar o login como fallback\r\n        const fullName = client.persons && client.persons[0] && client.persons[0].fullName\r\n          ? client.persons[0].fullName\r\n          : client.login;\r\n\r\n        return {\r\n          fullName: fullName,\r\n          login: client.login || \"\",\r\n          email: client.email || \"\",\r\n          personsCount: client.persons ? client.persons.length : 0,\r\n          active: client.active,\r\n          createdAt: client.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.clientIds && filters.clientIds.length > 0) {\r\n        subtitleParts.push(`Clientes específicos: ${filters.clientIds.length} selecionados`);\r\n      }\r\n      if (filters.active !== undefined) {\r\n        subtitleParts.push(`Status: ${filters.active ? \"Ativos\" : \"Inativos\"}`);\r\n      }\r\n      if (filters.companyId) {\r\n        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"clientes\",\r\n        columns,\r\n        title: \"Lista de Clientes\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar clientes:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\nexport default clientsService;"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AACpD;AACA;AAGA;AAFA;AACA;;;;;;AAGO,MAAM,iBAAiB;IAC5B,oCAAoC;IACpC,YAAY,OAAO,UAAU,CAAC,CAAC;QAC7B,yCAAyC;QACzC,MAAM,OAAO,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC3C,MAAM,QAAQ,SAAS,QAAQ,KAAK,EAAE,OAAO;QAC7C,MAAM,EACJ,SAAS,EAAE,EACX,SAAS,EACT,MAAM,EACN,SAAS,EACT,eAAe,EACf,YAAY,UAAU,EACtB,gBAAgB,MAAS,yBAAyB;QAA7B,EACtB,GAAG;QAEJ,IAAI;YACF,kCAAkC;YAClC,MAAM,SAAS;gBACb;gBACA;gBACA,QAAQ,UAAU;gBAClB,QAAQ,WAAW,YAAY,YAAY;gBAC3C,WAAW,aAAa;gBACxB,iBAAiB,mBAAmB;gBACpC;gBACA;YACF;YAEA,qEAAqE;YACrE,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,uCAAuC;gBACvC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,YAAY;oBAAC;iBAAU;gBAEzE,+CAA+C;gBAC/C,eAAe,OAAO,CAAC,CAAC,IAAI;oBAC1B,yDAAyD;oBACzD,MAAM,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG;gBAClC;gBAEA,QAAQ,GAAG,CAAC,4CAA4C;YAC1D;YAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,YAAY;gBAAE;YAAO;YAEpD,8DAA8D;YAC9D,OAAO,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,WAAW;gBAAC;aAAO;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;YAC/C,2CAA2C;YAC3C,OAAO,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,cAAc,OAAO;QACnB,IAAI;YACF,+CAA+C;YAC/C,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;YAC5C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,cAAc,OAAO,IAAI;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE;YACjD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC,EAAE;YACzD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,kBAAkB,OAAO;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC;YAC7D,8DAA8D;YAC9D,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,WAAW;gBAAC;aAAS;YACpE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAChE,OAAO,EAAE;QACX;IACF;IACA;;;;;CAKD,GACC,eAAe,OAAO,SAAS,eAAe,MAAM;QAClD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,eAAe,UAAU,CAAC;gBAC/C,GAAG,OAAO;gBACV,OAAO;YACT;YAEA,mDAAmD;YACnD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,UAAU,WAAW;gBAAC;aAAO;YAC7D,MAAM,OAAO;YAEb,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAY,QAAQ;gBAAU;gBACrC;oBAAE,KAAK;oBAAS,QAAQ;gBAAQ;gBAChC;oBAAE,KAAK;oBAAS,QAAQ;gBAAQ;gBAChC;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,GAAG,OAAS,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,MAAM,GAAG;gBAC5D;gBACA;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,QAAQ,UAAU;oBACrC,OAAO;oBACP,OAAO;gBACT;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;gBAC5B,iEAAiE;gBACjE,MAAM,WAAW,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC9E,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC1B,OAAO,KAAK;gBAEhB,OAAO;oBACL,UAAU;oBACV,OAAO,OAAO,KAAK,IAAI;oBACvB,OAAO,OAAO,KAAK,IAAI;oBACvB,cAAc,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,MAAM,GAAG;oBACvD,QAAQ,OAAO,MAAM;oBACrB,WAAW,OAAO,SAAS,IAAI;gBACjC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,cAAc,IAAI,CAAC,CAAC,sBAAsB,EAAE,QAAQ,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;YACrF;YACA,IAAI,QAAQ,MAAM,KAAK,WAAW;gBAChC,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,GAAG,WAAW,YAAY;YACxE;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,WAAW,IAAI,QAAQ,SAAS,EAAE;YAC3E;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,uIAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;AACF;uCAEe"}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 237, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/common/services/cepService.js"], "sourcesContent": ["// src/app/modules/common/services/cepService.js\r\nimport { api } from \"@/utils/api\";\r\n\r\nexport const cepService = {\r\n  /**\r\n   * Busca informações de endereço a partir de um CEP\r\n   * @param {string} cep - CEP a ser consultado (pode conter máscara)\r\n   * @returns {Promise<Object>} - Dados do endereço\r\n   */\r\n  searchByCep: async (cep) => {\r\n    try {\r\n      // Remove caracteres não numéricos\r\n      const cleanCep = cep.replace(/\\D/g, '');\r\n\r\n      // Valida o CEP (deve ter 8 dígitos)\r\n      if (cleanCep.length !== 8) {\r\n        throw new Error('CEP inválido. O CEP deve conter 8 dígitos.');\r\n      }\r\n\r\n      const response = await api.get(`/cep/${cleanCep}`);\r\n      console.log('Resposta da API de CEP:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Erro ao buscar CEP:', error);\r\n\r\n      // Formata a mensagem de erro\r\n      const errorMessage = error.response?.data?.message ||\r\n                          error.message ||\r\n                          'Erro ao buscar CEP';\r\n\r\n      throw new Error(errorMessage);\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;AAChD;;AAEO,MAAM,aAAa;IACxB;;;;GAIC,GACD,aAAa,OAAO;QAClB,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;YAEpC,oCAAoC;YACpC,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU;YACjD,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;YACpD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YAErC,6BAA6B;YAC7B,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;YAEpB,MAAM,IAAI,MAAM;QAClB;IACF;AACF"}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/hooks/useCep.js"], "sourcesContent": ["// src/hooks/useCep.js\r\nimport { useState } from 'react';\r\nimport { cepService } from '@/app/modules/common/services/cepService';\r\n\r\n/**\r\n * Hook para busca de CEP e preenchimento automático de endereço\r\n */\r\nexport function useCep() {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n\r\n  /**\r\n   * Busca endereço a partir do CEP e atualiza o formulário\r\n   * @param {string} cep - CEP a ser consultado\r\n   * @param {Function} setFormData - Função para atualizar o estado do formulário\r\n   * @param {Object} fieldMapping - Mapeamento dos campos do endereço para os campos do formulário\r\n   * @returns {Promise<Object|null>} - Dados do endereço ou null em caso de erro\r\n   */\r\n  const searchAddressByCep = async (cep, setFormData, fieldMapping = {}) => {\r\n    // Limpa erro anterior\r\n    setError(null);\r\n\r\n    // Verifica se o CEP tem pelo menos 8 dígitos (considerando possível máscara)\r\n    const cleanCep = cep.replace(/\\D/g, '');\r\n    if (cleanCep.length !== 8) {\r\n      return null;\r\n    }\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      const addressData = await cepService.searchByCep(cep);\r\n      console.log('Dados do endereço recebidos:', addressData);\r\n\r\n      // Atualiza o formulário com os dados do endereço\r\n      setFormData(prevData => {\r\n        console.log('Estado atual do formulário antes da atualização:', prevData);\r\n\r\n        // Cria um objeto com os campos mapeados\r\n        const updatedFields = {};\r\n\r\n        // Mapeamento padrão se não for fornecido\r\n        const defaultMapping = {\r\n          logradouro: 'address',\r\n          bairro: 'neighborhood',\r\n          localidade: 'city',\r\n          uf: 'state',\r\n          cep: 'postalCode'\r\n        };\r\n\r\n        // Usa o mapeamento fornecido ou o padrão\r\n        const mapping = { ...defaultMapping, ...fieldMapping };\r\n        console.log('Mapeamento de campos:', mapping);\r\n\r\n        // Para cada campo retornado pela API, mapeia para o campo correspondente no formulário\r\n        Object.keys(addressData).forEach(apiField => {\r\n          const formField = mapping[apiField];\r\n          console.log(`Mapeando ${apiField} para ${formField}`);\r\n\r\n          if (formField && addressData[apiField]) {\r\n            // Suporte para campos aninhados (ex: \"person.address\")\r\n            if (formField.includes('.')) {\r\n              const [parent, child] = formField.split('.');\r\n              console.log(`Campo aninhado: ${parent}.${child} = ${addressData[apiField]}`);\r\n              // Garantir que todos os campos aninhados existentes sejam preservados\r\n              if (!updatedFields[parent]) {\r\n                updatedFields[parent] = { ...prevData[parent] };\r\n                console.log(`Criando objeto para ${parent} com valores existentes:`, updatedFields[parent]);\r\n              }\r\n\r\n              // Atualizar o campo específico\r\n              updatedFields[parent][child] = addressData[apiField];\r\n              console.log(`Atualizado ${parent}.${child} para ${addressData[apiField]}. Objeto atual:`, updatedFields[parent]);\r\n            } else {\r\n              console.log(`Campo simples: ${formField} = ${addressData[apiField]}`);\r\n\r\n              // Verificar se o valor é diferente do atual antes de atualizar\r\n              if (prevData[formField] !== addressData[apiField]) {\r\n                console.log(`Valor diferente do atual: ${prevData[formField]} -> ${addressData[apiField]}`);\r\n                updatedFields[formField] = addressData[apiField];\r\n              } else {\r\n                console.log(`Valor igual ao atual, não será atualizado: ${prevData[formField]}`);\r\n              }\r\n            }\r\n          }\r\n        });\r\n\r\n        console.log('Campos atualizados:', updatedFields);\r\n\r\n        // Cria o novo estado do formulário\r\n        const newState = { ...prevData, ...updatedFields };\r\n        console.log('Novo estado do formulário:', newState);\r\n\r\n        // Log específico para os campos de endereço\r\n        console.log('Valores dos campos de endereço atualizados:');\r\n        console.log('address:', newState.address);\r\n        console.log('neighborhood:', newState.neighborhood);\r\n        console.log('city:', newState.city);\r\n        console.log('state:', newState.state);\r\n        console.log('postalCode:', newState.postalCode);\r\n\r\n        return newState;\r\n      });\r\n\r\n      return addressData;\r\n    } catch (err) {\r\n      setError(err.message);\r\n      return null;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return {\r\n    searchAddressByCep,\r\n    isLoading,\r\n    error\r\n  };\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;;;AAKO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC;;;;;;GAMC,GACD,MAAM,qBAAqB,OAAO,KAAK,aAAa,eAAe,CAAC,CAAC;QACnE,sBAAsB;QACtB,SAAS;QAET,6EAA6E;QAC7E,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;QACpC,IAAI,SAAS,MAAM,KAAK,GAAG;YACzB,OAAO;QACT;QAEA,aAAa;QAEb,IAAI;YACF,MAAM,cAAc,MAAM,yJAAA,CAAA,aAAU,CAAC,WAAW,CAAC;YACjD,QAAQ,GAAG,CAAC,gCAAgC;YAE5C,iDAAiD;YACjD,YAAY,CAAA;gBACV,QAAQ,GAAG,CAAC,oDAAoD;gBAEhE,wCAAwC;gBACxC,MAAM,gBAAgB,CAAC;gBAEvB,yCAAyC;gBACzC,MAAM,iBAAiB;oBACrB,YAAY;oBACZ,QAAQ;oBACR,YAAY;oBACZ,IAAI;oBACJ,KAAK;gBACP;gBAEA,yCAAyC;gBACzC,MAAM,UAAU;oBAAE,GAAG,cAAc;oBAAE,GAAG,YAAY;gBAAC;gBACrD,QAAQ,GAAG,CAAC,yBAAyB;gBAErC,uFAAuF;gBACvF,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,CAAA;oBAC/B,MAAM,YAAY,OAAO,CAAC,SAAS;oBACnC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,MAAM,EAAE,WAAW;oBAEpD,IAAI,aAAa,WAAW,CAAC,SAAS,EAAE;wBACtC,uDAAuD;wBACvD,IAAI,UAAU,QAAQ,CAAC,MAAM;4BAC3B,MAAM,CAAC,QAAQ,MAAM,GAAG,UAAU,KAAK,CAAC;4BACxC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,EAAE,WAAW,CAAC,SAAS,EAAE;4BAC3E,sEAAsE;4BACtE,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE;gCAC1B,aAAa,CAAC,OAAO,GAAG;oCAAE,GAAG,QAAQ,CAAC,OAAO;gCAAC;gCAC9C,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,wBAAwB,CAAC,EAAE,aAAa,CAAC,OAAO;4BAC5F;4BAEA,+BAA+B;4BAC/B,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC,SAAS;4BACpD,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE,MAAM,MAAM,EAAE,WAAW,CAAC,SAAS,CAAC,eAAe,CAAC,EAAE,aAAa,CAAC,OAAO;wBACjH,OAAO;4BACL,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,GAAG,EAAE,WAAW,CAAC,SAAS,EAAE;4BAEpE,+DAA+D;4BAC/D,IAAI,QAAQ,CAAC,UAAU,KAAK,WAAW,CAAC,SAAS,EAAE;gCACjD,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,WAAW,CAAC,SAAS,EAAE;gCAC1F,aAAa,CAAC,UAAU,GAAG,WAAW,CAAC,SAAS;4BAClD,OAAO;gCACL,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,QAAQ,CAAC,UAAU,EAAE;4BACjF;wBACF;oBACF;gBACF;gBAEA,QAAQ,GAAG,CAAC,uBAAuB;gBAEnC,mCAAmC;gBACnC,MAAM,WAAW;oBAAE,GAAG,QAAQ;oBAAE,GAAG,aAAa;gBAAC;gBACjD,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,4CAA4C;gBAC5C,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,YAAY,SAAS,OAAO;gBACxC,QAAQ,GAAG,CAAC,iBAAiB,SAAS,YAAY;gBAClD,QAAQ,GAAG,CAAC,SAAS,SAAS,IAAI;gBAClC,QAAQ,GAAG,CAAC,UAAU,SAAS,KAAK;gBACpC,QAAQ,GAAG,CAAC,eAAe,SAAS,UAAU;gBAE9C,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,KAAK;YACZ,SAAS,IAAI,OAAO;YACpB,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF"}}, {"offset": {"line": 382, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/common/MaskedInput.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\n\r\n/**\r\n * Componente genérico para campos de entrada com máscara\r\n * \r\n * @param {Object} props - Propriedades do componente\r\n * @param {string} props.type - Tipo de máscara: 'cpf', 'cnpj', 'phone', 'cep', ou 'custom'\r\n * @param {string} props.value - Valor atual do campo\r\n * @param {Function} props.onChange - Função chamada quando o valor muda\r\n * @param {string} props.name - Nome do campo\r\n * @param {string} props.id - ID do campo\r\n * @param {string} props.placeholder - Texto de placeholder\r\n * @param {string} props.className - Classes CSS adicionais\r\n * @param {boolean} props.disabled - Se o campo está desabilitado\r\n * @param {string} props.customMask - Máscara personalizada (quando type='custom')\r\n * @param {Object} props.inputProps - Propriedades adicionais para o input\r\n */\r\nconst MaskedInput = ({\r\n  type = 'text',\r\n  value = '',\r\n  onChange,\r\n  name,\r\n  id,\r\n  placeholder,\r\n  className = '',\r\n  disabled = false,\r\n  customMask,\r\n  ...inputProps\r\n}) => {\r\n  // Estado interno para controlar o valor formatado\r\n  const [inputValue, setInputValue] = useState('');\r\n\r\n  // Atualiza o estado interno quando o valor externo muda\r\n  useEffect(() => {\r\n    if (value !== undefined) {\r\n      setInputValue(formatValue(value, type, customMask));\r\n    }\r\n  }, [value, type, customMask]);\r\n\r\n  // Função para aplicar a máscara ao valor\r\n  const applyMask = (value, mask) => {\r\n    let maskedValue = '';\r\n    let valueIndex = 0;\r\n\r\n    for (let i = 0; i < mask.length && valueIndex < value.length; i++) {\r\n      const maskChar = mask[i];\r\n      const valueChar = value[valueIndex];\r\n\r\n      if (maskChar === '#') {\r\n        // Apenas dígitos\r\n        if (/\\d/.test(valueChar)) {\r\n          maskedValue += valueChar;\r\n          valueIndex++;\r\n        } else {\r\n          valueIndex++;\r\n          i--;\r\n        }\r\n      } else if (maskChar === 'A') {\r\n        // Apenas letras\r\n        if (/[a-zA-Z]/.test(valueChar)) {\r\n          maskedValue += valueChar;\r\n          valueIndex++;\r\n        } else {\r\n          valueIndex++;\r\n          i--;\r\n        }\r\n      } else if (maskChar === 'S') {\r\n        // Letras ou dígitos\r\n        if (/[a-zA-Z0-9]/.test(valueChar)) {\r\n          maskedValue += valueChar;\r\n          valueIndex++;\r\n        } else {\r\n          valueIndex++;\r\n          i--;\r\n        }\r\n      } else {\r\n        // Caracteres especiais da máscara\r\n        maskedValue += maskChar;\r\n        \r\n        // Se o caractere do valor for igual ao caractere da máscara, avança\r\n        if (valueChar === maskChar) {\r\n          valueIndex++;\r\n        }\r\n      }\r\n    }\r\n\r\n    return maskedValue;\r\n  };\r\n\r\n  // Função para obter a máscara com base no tipo\r\n  const getMask = (type) => {\r\n    switch (type) {\r\n      case 'cpf':\r\n        return '###.###.###-##';\r\n      case 'cnpj':\r\n        return '##.###.###/####-##';\r\n      case 'phone':\r\n        return '(##) #####-####';\r\n      case 'cep':\r\n        return '#####-###';\r\n      case 'custom':\r\n        return customMask || '';\r\n      default:\r\n        return '';\r\n    }\r\n  };\r\n\r\n  // Função para formatar o valor com base no tipo\r\n  const formatValue = (value, type, customMask) => {\r\n    if (!value) return '';\r\n    \r\n    // Remove caracteres não numéricos para tipos numéricos\r\n    let cleanValue = value;\r\n    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {\r\n      cleanValue = value.replace(/\\D/g, '');\r\n    }\r\n    \r\n    const mask = getMask(type);\r\n    return applyMask(cleanValue, mask);\r\n  };\r\n\r\n  // Função para remover a máscara e obter apenas os dígitos\r\n  const unformatValue = (value) => {\r\n    if (!value) return '';\r\n    \r\n    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {\r\n      return value.replace(/\\D/g, '');\r\n    }\r\n    \r\n    return value;\r\n  };\r\n\r\n  // Manipulador de mudança de valor\r\n  const handleChange = (e) => {\r\n    const newValue = e.target.value;\r\n    const formattedValue = formatValue(newValue, type, customMask);\r\n    \r\n    setInputValue(formattedValue);\r\n    \r\n    if (onChange) {\r\n      // Cria um evento sintético com o valor formatado\r\n      const syntheticEvent = {\r\n        ...e,\r\n        target: {\r\n          ...e.target,\r\n          name: name,\r\n          value: formattedValue,\r\n          rawValue: unformatValue(formattedValue)\r\n        }\r\n      };\r\n      \r\n      onChange(syntheticEvent);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <input\r\n      type=\"text\"\r\n      id={id}\r\n      name={name}\r\n      value={inputValue}\r\n      onChange={handleChange}\r\n      placeholder={placeholder}\r\n      className={className}\r\n      disabled={disabled}\r\n      {...inputProps}\r\n    />\r\n  );\r\n};\r\n\r\nexport default MaskedInput;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA;;;;;;;;;;;;;;CAcC,GACD,MAAM,cAAc,CAAC,EACnB,OAAO,MAAM,EACb,QAAQ,EAAE,EACV,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,WAAW,EACX,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,UAAU,EACV,GAAG,YACJ;IACC,kDAAkD;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,cAAc,YAAY,OAAO,MAAM;QACzC;IACF,GAAG;QAAC;QAAO;QAAM;KAAW;IAE5B,yCAAyC;IACzC,MAAM,YAAY,CAAC,OAAO;QACxB,IAAI,cAAc;QAClB,IAAI,aAAa;QAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,IAAI,aAAa,MAAM,MAAM,EAAE,IAAK;YACjE,MAAM,WAAW,IAAI,CAAC,EAAE;YACxB,MAAM,YAAY,KAAK,CAAC,WAAW;YAEnC,IAAI,aAAa,KAAK;gBACpB,iBAAiB;gBACjB,IAAI,KAAK,IAAI,CAAC,YAAY;oBACxB,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,gBAAgB;gBAChB,IAAI,WAAW,IAAI,CAAC,YAAY;oBAC9B,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO,IAAI,aAAa,KAAK;gBAC3B,oBAAoB;gBACpB,IAAI,cAAc,IAAI,CAAC,YAAY;oBACjC,eAAe;oBACf;gBACF,OAAO;oBACL;oBACA;gBACF;YACF,OAAO;gBACL,kCAAkC;gBAClC,eAAe;gBAEf,oEAAoE;gBACpE,IAAI,cAAc,UAAU;oBAC1B;gBACF;YACF;QACF;QAEA,OAAO;IACT;IAEA,+CAA+C;IAC/C,MAAM,UAAU,CAAC;QACf,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,cAAc;YACvB;gBACE,OAAO;QACX;IACF;IAEA,gDAAgD;IAChD,MAAM,cAAc,CAAC,OAAO,MAAM;QAChC,IAAI,CAAC,OAAO,OAAO;QAEnB,uDAAuD;QACvD,IAAI,aAAa;QACjB,IAAI;YAAC;YAAO;YAAQ;YAAS;SAAM,CAAC,QAAQ,CAAC,OAAO;YAClD,aAAa,MAAM,OAAO,CAAC,OAAO;QACpC;QAEA,MAAM,OAAO,QAAQ;QACrB,OAAO,UAAU,YAAY;IAC/B;IAEA,0DAA0D;IAC1D,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI;YAAC;YAAO;YAAQ;YAAS;SAAM,CAAC,QAAQ,CAAC,OAAO;YAClD,OAAO,MAAM,OAAO,CAAC,OAAO;QAC9B;QAEA,OAAO;IACT;IAEA,kCAAkC;IAClC,MAAM,eAAe,CAAC;QACpB,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK;QAC/B,MAAM,iBAAiB,YAAY,UAAU,MAAM;QAEnD,cAAc;QAEd,IAAI,UAAU;YACZ,iDAAiD;YACjD,MAAM,iBAAiB;gBACrB,GAAG,CAAC;gBACJ,QAAQ;oBACN,GAAG,EAAE,MAAM;oBACX,MAAM;oBACN,OAAO;oBACP,UAAU,cAAc;gBAC1B;YACF;YAEA,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QACC,MAAK;QACL,IAAI;QACJ,MAAM;QACN,OAAO;QACP,UAAU;QACV,aAAa;QACb,WAAW;QACX,UAAU;QACT,GAAG,UAAU;;;;;;AAGpB;uCAEe"}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/common/AddressForm.js"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\nimport { MapPin, Search, Loader2 } from 'lucide-react';\r\nimport { useCep } from '@/hooks/useCep';\r\nimport MaskedInput from './MaskedInput';\r\nimport { ModuleInput, ModuleMaskedInput } from '@/components/ui';\r\n\r\n/**\r\n * Componente reutilizável para formulários de endereço com busca automática por CEP\r\n *\r\n * @param {Object} props - Propriedades do componente\r\n * @param {Object} props.formData - Dados do formulário\r\n * @param {Function} props.setFormData - Função para atualizar os dados do formulário\r\n * @param {Object} props.errors - Erros de validação\r\n * @param {boolean} props.isLoading - Indica se o formulário está em carregamento\r\n * @param {Object} props.fieldMapping - Mapeamento dos campos do endereço para os campos do formulário\r\n * @param {string} props.prefix - Prefixo para campos aninhados (ex: \"person.\")\r\n * @param {Object} props.classes - Classes CSS personalizadas\r\n * @param {string} props.moduleColor - Cor do módulo (default, people, scheduler, admin, financial)\r\n */\r\nconst AddressForm = ({\r\n  formData,\r\n  setFormData,\r\n  errors = {},\r\n  isLoading = false,\r\n  fieldMapping = {},\r\n  prefix = '',\r\n  classes = {},\r\n  moduleColor = 'default'\r\n}) => {\r\n  // Hook para busca de CEP\r\n  const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = useCep();\r\n\r\n  // Classes CSS padrão\r\n  const defaultClasses = {\r\n    label: 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',\r\n    input: 'w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white',\r\n    error: 'text-sm text-red-600 dark:text-red-400 mt-1',\r\n    iconContainer: 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none',\r\n    button: 'absolute inset-y-0 right-0 px-3 flex items-center bg-primary-500 hover:bg-primary-600 text-white rounded-r-md transition-colors'\r\n  };\r\n\r\n  // Mescla as classes padrão com as classes personalizadas\r\n  const mergedClasses = {\r\n    label: classes.label || defaultClasses.label,\r\n    input: classes.input || defaultClasses.input,\r\n    error: classes.error || defaultClasses.error,\r\n    iconContainer: classes.iconContainer || defaultClasses.iconContainer,\r\n    button: classes.button || defaultClasses.button\r\n  };\r\n\r\n  // Mapeamento padrão dos campos\r\n  const defaultMapping = {\r\n    cep: `${prefix}postalCode`,\r\n    logradouro: `${prefix}address`,\r\n    bairro: `${prefix}neighborhood`,\r\n    localidade: `${prefix}city`,\r\n    uf: `${prefix}state`\r\n  };\r\n\r\n  // Log para debug\r\n  console.log('Prefix:', prefix);\r\n  console.log('Default mapping:', defaultMapping);\r\n\r\n  // Mescla o mapeamento padrão com o mapeamento personalizado\r\n  const mergedMapping = { ...defaultMapping, ...fieldMapping };\r\n  console.log('Mapeamento mesclado:', mergedMapping);\r\n\r\n  // Função para lidar com a mudança de valores nos campos\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    console.log(`Alterando campo: ${name} para valor: ${value}`);\r\n\r\n    // Suporte para campos aninhados (ex: \"person.address\")\r\n    if (name.includes('.')) {\r\n      const [parent, child] = name.split('.');\r\n      console.log(`Campo aninhado: ${parent}.${child}. Estado atual:`, formData[parent]);\r\n\r\n      const newFormData = {\r\n        ...formData,\r\n        [parent]: {\r\n          ...formData[parent],\r\n          [child]: value\r\n        }\r\n      };\r\n\r\n      console.log('Novo estado do formulário:', newFormData);\r\n      setFormData(newFormData);\r\n    } else {\r\n      const newFormData = {\r\n        ...formData,\r\n        [name]: value\r\n      };\r\n\r\n      console.log('Novo estado do formulário:', newFormData);\r\n      setFormData(newFormData);\r\n    }\r\n  };\r\n\r\n  // Função para buscar endereço pelo CEP\r\n  const handleCepSearch = async () => {\r\n    // Obtém o valor do CEP do campo correspondente\r\n    const cepField = mergedMapping.cep;\r\n    let cepValue;\r\n\r\n    if (cepField.includes('.')) {\r\n      const [parent, child] = cepField.split('.');\r\n      cepValue = formData[parent]?.[child];\r\n    } else {\r\n      cepValue = formData[cepField];\r\n    }\r\n\r\n    console.log('Buscando endereço para o CEP:', cepValue);\r\n\r\n    if (cepValue) {\r\n      try {\r\n        const result = await searchAddressByCep(cepValue, setFormData, mergedMapping);\r\n        console.log('Resultado da busca de CEP:', result);\r\n\r\n        // Forçar a atualização do formulário com os dados recebidos\r\n        if (result) {\r\n          console.log('Dados de endereço recebidos com sucesso, atualizando formulário');\r\n\r\n          // Criar um objeto com os campos mapeados\r\n          const updatedFields = {};\r\n\r\n          // Para cada campo retornado pela API, mapeia para o campo correspondente no formulário\r\n          Object.keys(result).forEach(apiField => {\r\n            const formField = mergedMapping[apiField];\r\n            if (formField && result[apiField]) {\r\n              updatedFields[formField] = result[apiField];\r\n            }\r\n          });\r\n\r\n          console.log('Campos a serem atualizados:', updatedFields);\r\n\r\n          // Atualizar o formulário com os dados do endereço\r\n          setFormData(prevData => ({\r\n            ...prevData,\r\n            ...updatedFields\r\n          }));\r\n        }\r\n      } catch (error) {\r\n        console.error('Erro ao buscar CEP:', error);\r\n      }\r\n    }\r\n  };\r\n\r\n  // Efeito para buscar endereço automaticamente quando o CEP for preenchido completamente\r\n  useEffect(() => {\r\n    const cepField = mergedMapping.cep;\r\n    let cepValue;\r\n\r\n    if (cepField.includes('.')) {\r\n      const [parent, child] = cepField.split('.');\r\n      cepValue = formData[parent]?.[child];\r\n    } else {\r\n      cepValue = formData[cepField];\r\n    }\r\n\r\n    // Formatar o CEP se necessário\r\n    if (cepValue && !cepValue.includes('-') && cepValue.replace(/\\D/g, '').length === 8) {\r\n      // Formatar o CEP no formato 00000-000\r\n      const cleanCep = cepValue.replace(/\\D/g, '');\r\n      const formattedCep = cleanCep.replace(/(\\d{5})(\\d{3})/, '$1-$2');\r\n\r\n      // Atualizar o formData com o CEP formatado\r\n      if (cepField.includes('.')) {\r\n        const [parent, child] = cepField.split('.');\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          [parent]: {\r\n            ...prev[parent],\r\n            [child]: formattedCep\r\n          }\r\n        }));\r\n      } else {\r\n        setFormData(prev => ({\r\n          ...prev,\r\n          [cepField]: formattedCep\r\n        }));\r\n      }\r\n    }\r\n\r\n    // Se o CEP tiver 8 dígitos (sem contar a máscara), busca o endereço\r\n    if (cepValue && cepValue.replace(/\\D/g, '').length === 8) {\r\n      handleCepSearch();\r\n    }\r\n  }, [formData[mergedMapping.cep]]);\r\n\r\n  // Função para obter o valor de um campo, considerando campos aninhados\r\n  const getFieldValue = (fieldName) => {\r\n    console.log(`Obtendo valor para o campo: ${fieldName}`);\r\n\r\n    if (fieldName.includes('.')) {\r\n      const [parent, child] = fieldName.split('.');\r\n      console.log(`Campo aninhado: ${parent}.${child}, valor:`, formData[parent]?.[child]);\r\n      return formData[parent]?.[child] || '';\r\n    }\r\n\r\n    console.log(`Campo simples: ${fieldName}, valor:`, formData[fieldName]);\r\n    return formData[fieldName] || '';\r\n  };\r\n\r\n  // Função para verificar se um campo tem erro\r\n  const hasError = (fieldName) => {\r\n    return errors[fieldName] ? true : false;\r\n  };\r\n\r\n  // Função para obter a mensagem de erro de um campo\r\n  const getErrorMessage = (fieldName) => {\r\n    return errors[fieldName] || '';\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {/* Seção de Endereço */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n        {/* CEP com botão de busca */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.cep}>\r\n            CEP\r\n          </label>\r\n          <div className=\"relative\">\r\n            <div className={mergedClasses.iconContainer}>\r\n              <MapPin className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n            </div>\r\n            {moduleColor ? (\r\n              <div className=\"relative\">\r\n                <ModuleMaskedInput\r\n                  moduleColor={moduleColor}\r\n                  mask=\"99999-999\"\r\n                  replacement={{ 9: /[0-9]/ }}\r\n                  id={mergedMapping.cep}\r\n                  name={mergedMapping.cep}\r\n                  value={getFieldValue(mergedMapping.cep)}\r\n                  onChange={handleChange}\r\n                  placeholder=\"00000-000\"\r\n                  className=\"pl-10 pr-12\"\r\n                  disabled={isLoading || isCepLoading}\r\n                  error={hasError(mergedMapping.cep)}\r\n                />\r\n              </div>\r\n            ) : (\r\n              <MaskedInput\r\n                type=\"cep\"\r\n                id={mergedMapping.cep}\r\n                name={mergedMapping.cep}\r\n                value={getFieldValue(mergedMapping.cep)}\r\n                onChange={handleChange}\r\n                placeholder=\"00000-000\"\r\n                className={`${mergedClasses.input} pl-10 pr-12 ${hasError(mergedMapping.cep) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n                disabled={isLoading || isCepLoading}\r\n              />\r\n            )}\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleCepSearch}\r\n              className={mergedClasses.button}\r\n              disabled={isLoading || isCepLoading}\r\n              aria-label=\"Buscar CEP\"\r\n            >\r\n              {isCepLoading ? (\r\n                <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n              ) : (\r\n                <Search className=\"h-5 w-5\" />\r\n              )}\r\n            </button>\r\n          </div>\r\n          {hasError(mergedMapping.cep) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.cep)}</p>\r\n          )}\r\n          {cepError && (\r\n            <p className={mergedClasses.error}>{cepError}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Estado */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.uf}>\r\n            Estado\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.uf}\r\n              name={mergedMapping.uf}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.uf)}\r\n              onChange={handleChange}\r\n              placeholder=\"UF\"\r\n              disabled={isLoading || isCepLoading}\r\n              maxLength={2}\r\n              error={hasError(mergedMapping.uf)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.uf}\r\n              name={mergedMapping.uf}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.uf)}\r\n              onChange={handleChange}\r\n              placeholder=\"UF\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.uf) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n              maxLength={2}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.uf) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.uf)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Cidade */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.localidade}>\r\n            Cidade\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.localidade}\r\n              name={mergedMapping.localidade}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.localidade)}\r\n              onChange={handleChange}\r\n              placeholder=\"Cidade\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.localidade)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.localidade}\r\n              name={mergedMapping.localidade}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.localidade)}\r\n              onChange={handleChange}\r\n              placeholder=\"Cidade\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.localidade) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.localidade) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.localidade)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Bairro */}\r\n        <div>\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.bairro}>\r\n            Bairro\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.bairro}\r\n              name={mergedMapping.bairro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.bairro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Bairro\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.bairro)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.bairro}\r\n              name={mergedMapping.bairro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.bairro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Bairro\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.bairro) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.bairro) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.bairro)}</p>\r\n          )}\r\n        </div>\r\n\r\n        {/* Endereço (logradouro) */}\r\n        <div className=\"md:col-span-2\">\r\n          <label className={mergedClasses.label} htmlFor={mergedMapping.logradouro}>\r\n            Logradouro\r\n          </label>\r\n          {moduleColor ? (\r\n            <ModuleInput\r\n              moduleColor={moduleColor}\r\n              id={mergedMapping.logradouro}\r\n              name={mergedMapping.logradouro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.logradouro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Rua, Avenida, etc.\"\r\n              disabled={isLoading || isCepLoading}\r\n              error={hasError(mergedMapping.logradouro)}\r\n            />\r\n          ) : (\r\n            <input\r\n              id={mergedMapping.logradouro}\r\n              name={mergedMapping.logradouro}\r\n              type=\"text\"\r\n              value={getFieldValue(mergedMapping.logradouro)}\r\n              onChange={handleChange}\r\n              placeholder=\"Rua, Avenida, etc.\"\r\n              className={`${mergedClasses.input} ${hasError(mergedMapping.logradouro) ? 'border-red-500 dark:border-red-700' : ''}`}\r\n              disabled={isLoading || isCepLoading}\r\n            />\r\n          )}\r\n          {hasError(mergedMapping.logradouro) && (\r\n            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.logradouro)}</p>\r\n          )}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default AddressForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAHA;AAGA;AAHA;AAAA;AAGA;AANA;;;;;;;AAQA;;;;;;;;;;;;CAYC,GACD,MAAM,cAAc,CAAC,EACnB,QAAQ,EACR,WAAW,EACX,SAAS,CAAC,CAAC,EACX,YAAY,KAAK,EACjB,eAAe,CAAC,CAAC,EACjB,SAAS,EAAE,EACX,UAAU,CAAC,CAAC,EACZ,cAAc,SAAS,EACxB;IACC,yBAAyB;IACzB,MAAM,EAAE,kBAAkB,EAAE,WAAW,YAAY,EAAE,OAAO,QAAQ,EAAE,GAAG,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD;IAE9E,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,OAAO;QACP,OAAO;QACP,OAAO;QACP,eAAe;QACf,QAAQ;IACV;IAEA,yDAAyD;IACzD,MAAM,gBAAgB;QACpB,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,OAAO,QAAQ,KAAK,IAAI,eAAe,KAAK;QAC5C,eAAe,QAAQ,aAAa,IAAI,eAAe,aAAa;QACpE,QAAQ,QAAQ,MAAM,IAAI,eAAe,MAAM;IACjD;IAEA,+BAA+B;IAC/B,MAAM,iBAAiB;QACrB,KAAK,GAAG,OAAO,UAAU,CAAC;QAC1B,YAAY,GAAG,OAAO,OAAO,CAAC;QAC9B,QAAQ,GAAG,OAAO,YAAY,CAAC;QAC/B,YAAY,GAAG,OAAO,IAAI,CAAC;QAC3B,IAAI,GAAG,OAAO,KAAK,CAAC;IACtB;IAEA,iBAAiB;IACjB,QAAQ,GAAG,CAAC,WAAW;IACvB,QAAQ,GAAG,CAAC,oBAAoB;IAEhC,4DAA4D;IAC5D,MAAM,gBAAgB;QAAE,GAAG,cAAc;QAAE,GAAG,YAAY;IAAC;IAC3D,QAAQ,GAAG,CAAC,wBAAwB;IAEpC,wDAAwD;IACxD,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,KAAK,aAAa,EAAE,OAAO;QAE3D,uDAAuD;QACvD,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,MAAM,CAAC,QAAQ,MAAM,GAAG,KAAK,KAAK,CAAC;YACnC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,MAAM,eAAe,CAAC,EAAE,QAAQ,CAAC,OAAO;YAEjF,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,CAAC,OAAO,EAAE;oBACR,GAAG,QAAQ,CAAC,OAAO;oBACnB,CAAC,MAAM,EAAE;gBACX;YACF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,YAAY;QACd,OAAO;YACL,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX,CAAC,KAAK,EAAE;YACV;YAEA,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,YAAY;QACd;IACF;IAEA,uCAAuC;IACvC,MAAM,kBAAkB;QACtB,+CAA+C;QAC/C,MAAM,WAAW,cAAc,GAAG;QAClC,IAAI;QAEJ,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;YACvC,WAAW,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;QACtC,OAAO;YACL,WAAW,QAAQ,CAAC,SAAS;QAC/B;QAEA,QAAQ,GAAG,CAAC,iCAAiC;QAE7C,IAAI,UAAU;YACZ,IAAI;gBACF,MAAM,SAAS,MAAM,mBAAmB,UAAU,aAAa;gBAC/D,QAAQ,GAAG,CAAC,8BAA8B;gBAE1C,4DAA4D;gBAC5D,IAAI,QAAQ;oBACV,QAAQ,GAAG,CAAC;oBAEZ,yCAAyC;oBACzC,MAAM,gBAAgB,CAAC;oBAEvB,uFAAuF;oBACvF,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,CAAA;wBAC1B,MAAM,YAAY,aAAa,CAAC,SAAS;wBACzC,IAAI,aAAa,MAAM,CAAC,SAAS,EAAE;4BACjC,aAAa,CAAC,UAAU,GAAG,MAAM,CAAC,SAAS;wBAC7C;oBACF;oBAEA,QAAQ,GAAG,CAAC,+BAA+B;oBAE3C,kDAAkD;oBAClD,YAAY,CAAA,WAAY,CAAC;4BACvB,GAAG,QAAQ;4BACX,GAAG,aAAa;wBAClB,CAAC;gBACH;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;YACvC;QACF;IACF;IAEA,wFAAwF;IACxF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,cAAc,GAAG;QAClC,IAAI;QAEJ,IAAI,SAAS,QAAQ,CAAC,MAAM;YAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;YACvC,WAAW,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;QACtC,OAAO;YACL,WAAW,QAAQ,CAAC,SAAS;QAC/B;QAEA,+BAA+B;QAC/B,IAAI,YAAY,CAAC,SAAS,QAAQ,CAAC,QAAQ,SAAS,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;YACnF,sCAAsC;YACtC,MAAM,WAAW,SAAS,OAAO,CAAC,OAAO;YACzC,MAAM,eAAe,SAAS,OAAO,CAAC,kBAAkB;YAExD,2CAA2C;YAC3C,IAAI,SAAS,QAAQ,CAAC,MAAM;gBAC1B,MAAM,CAAC,QAAQ,MAAM,GAAG,SAAS,KAAK,CAAC;gBACvC,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,OAAO,EAAE;4BACR,GAAG,IAAI,CAAC,OAAO;4BACf,CAAC,MAAM,EAAE;wBACX;oBACF,CAAC;YACH,OAAO;gBACL,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,CAAC,SAAS,EAAE;oBACd,CAAC;YACH;QACF;QAEA,oEAAoE;QACpE,IAAI,YAAY,SAAS,OAAO,CAAC,OAAO,IAAI,MAAM,KAAK,GAAG;YACxD;QACF;IACF,GAAG;QAAC,QAAQ,CAAC,cAAc,GAAG,CAAC;KAAC;IAEhC,uEAAuE;IACvE,MAAM,gBAAgB,CAAC;QACrB,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,WAAW;QAEtD,IAAI,UAAU,QAAQ,CAAC,MAAM;YAC3B,MAAM,CAAC,QAAQ,MAAM,GAAG,UAAU,KAAK,CAAC;YACxC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,OAAO,CAAC,EAAE,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM;YACnF,OAAO,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,IAAI;QACtC;QAEA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,QAAQ,CAAC,EAAE,QAAQ,CAAC,UAAU;QACtE,OAAO,QAAQ,CAAC,UAAU,IAAI;IAChC;IAEA,6CAA6C;IAC7C,MAAM,WAAW,CAAC;QAChB,OAAO,MAAM,CAAC,UAAU,GAAG,OAAO;IACpC;IAEA,mDAAmD;IACnD,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,CAAC,UAAU,IAAI;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBAEb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,GAAG;sCAAE;;;;;;sCAGnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,cAAc,aAAa;8CACzC,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;gCAEnB,4BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,4LAAA,CAAA,oBAAiB;wCAChB,aAAa;wCACb,MAAK;wCACL,aAAa;4CAAE,GAAG;wCAAQ;wCAC1B,IAAI,cAAc,GAAG;wCACrB,MAAM,cAAc,GAAG;wCACvB,OAAO,cAAc,cAAc,GAAG;wCACtC,UAAU;wCACV,aAAY;wCACZ,WAAU;wCACV,UAAU,aAAa;wCACvB,OAAO,SAAS,cAAc,GAAG;;;;;;;;;;yDAIrC,8OAAC,0IAAA,CAAA,UAAW;oCACV,MAAK;oCACL,IAAI,cAAc,GAAG;oCACrB,MAAM,cAAc,GAAG;oCACvB,OAAO,cAAc,cAAc,GAAG;oCACtC,UAAU;oCACV,aAAY;oCACZ,WAAW,GAAG,cAAc,KAAK,CAAC,aAAa,EAAE,SAAS,cAAc,GAAG,IAAI,uCAAuC,IAAI;oCAC1H,UAAU,aAAa;;;;;;8CAG3B,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAW,cAAc,MAAM;oCAC/B,UAAU,aAAa;oCACvB,cAAW;8CAEV,6BACC,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAEnB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAIvB,SAAS,cAAc,GAAG,mBACzB,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,GAAG;;;;;;wBAEtE,0BACC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG;;;;;;;;;;;;8BAKxC,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,EAAE;sCAAE;;;;;;wBAGjE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,EAAE;4BACpB,MAAM,cAAc,EAAE;4BACtB,MAAK;4BACL,OAAO,cAAc,cAAc,EAAE;4BACrC,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,WAAW;4BACX,OAAO,SAAS,cAAc,EAAE;;;;;iDAGlC,8OAAC;4BACC,IAAI,cAAc,EAAE;4BACpB,MAAM,cAAc,EAAE;4BACtB,MAAK;4BACL,OAAO,cAAc,cAAc,EAAE;4BACrC,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,EAAE,IAAI,uCAAuC,IAAI;4BAC7G,UAAU,aAAa;4BACvB,WAAW;;;;;;wBAGd,SAAS,cAAc,EAAE,mBACxB,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,EAAE;;;;;;;;;;;;8BAKxE,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,UAAU;sCAAE;;;;;;wBAGzE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,UAAU;;;;;iDAG1C,8OAAC;4BACC,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,UAAU,IAAI,uCAAuC,IAAI;4BACrH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,UAAU,mBAChC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,UAAU;;;;;;;;;;;;8BAKhF,8OAAC;;sCACC,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,MAAM;sCAAE;;;;;;wBAGrE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,MAAM;4BACxB,MAAM,cAAc,MAAM;4BAC1B,MAAK;4BACL,OAAO,cAAc,cAAc,MAAM;4BACzC,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,MAAM;;;;;iDAGtC,8OAAC;4BACC,IAAI,cAAc,MAAM;4BACxB,MAAM,cAAc,MAAM;4BAC1B,MAAK;4BACL,OAAO,cAAc,cAAc,MAAM;4BACzC,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,MAAM,IAAI,uCAAuC,IAAI;4BACjH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,MAAM,mBAC5B,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,MAAM;;;;;;;;;;;;8BAK5E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAW,cAAc,KAAK;4BAAE,SAAS,cAAc,UAAU;sCAAE;;;;;;wBAGzE,4BACC,8OAAC,gLAAA,CAAA,cAAW;4BACV,aAAa;4BACb,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,UAAU,aAAa;4BACvB,OAAO,SAAS,cAAc,UAAU;;;;;iDAG1C,8OAAC;4BACC,IAAI,cAAc,UAAU;4BAC5B,MAAM,cAAc,UAAU;4BAC9B,MAAK;4BACL,OAAO,cAAc,cAAc,UAAU;4BAC7C,UAAU;4BACV,aAAY;4BACZ,WAAW,GAAG,cAAc,KAAK,CAAC,CAAC,EAAE,SAAS,cAAc,UAAU,IAAI,uCAAuC,IAAI;4BACrH,UAAU,aAAa;;;;;;wBAG1B,SAAS,cAAc,UAAU,mBAChC,8OAAC;4BAAE,WAAW,cAAc,KAAK;sCAAG,gBAAgB,cAAc,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMxF;uCAEe"}}, {"offset": {"line": 1110, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/people/ClientFormModal.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { User, Mail, Lock, Eye, EyeOff, CreditCard, Phone, Calendar, MapPin, FileText, Loader2, AlertCircle } from \"lucide-react\";\r\nimport { ModuleModal, ModalButton, ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup } from \"@/components/ui\";\r\nimport { clientsService } from \"@/app/modules/people/services/clientsService\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { format } from \"date-fns\";\r\nimport AddressForm from \"@/components/common/AddressForm\";\r\nimport MaskedInput from \"@/components/common/MaskedInput\";\r\n\r\nconst ClientFormModal = ({ isOpen, onClose, client, onSuccess }) => {\r\n  const [formData, setFormData] = useState({\r\n    login: \"\",\r\n    email: \"\",\r\n    password: \"\",\r\n    confirmPassword: \"\",\r\n    person: {\r\n      id: \"\",\r\n      fullName: \"\",\r\n      cpf: \"\",\r\n      birthDate: \"\",\r\n      phone: \"\",\r\n      email: \"\",\r\n      gender: \"\",\r\n      address: \"\",\r\n      neighborhood: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      postalCode: \"\",\r\n      notes: \"\",\r\n    }\r\n  });\r\n\r\n  const [errors, setErrors] = useState({});\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [editMode, setEditMode] = useState(false);\r\n\r\n  // Load client data when editing\r\n  useEffect(() => {\r\n    if (client && isOpen) {\r\n      setEditMode(true);\r\n\r\n      // Format birthDate, if it exists\r\n      let birthDateFormatted = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].birthDate) {\r\n        try {\r\n          birthDateFormatted = format(new Date(client.persons[0].birthDate), \"yyyy-MM-dd\");\r\n        } catch (e) {\r\n          console.error(\"Error formatting date:\", e);\r\n        }\r\n      }\r\n\r\n      // Formatar CPF\r\n      let formattedCpf = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].cpf) {\r\n        const cleanCpf = client.persons[0].cpf.replace(/\\D/g, \"\");\r\n        formattedCpf = cleanCpf.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\r\n      }\r\n\r\n      // Formatar telefone\r\n      let formattedPhone = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].phone) {\r\n        const cleanPhone = client.persons[0].phone.replace(/\\D/g, \"\");\r\n        formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n      }\r\n\r\n      // Formatar CEP\r\n      let formattedPostalCode = \"\";\r\n      if (client.persons && client.persons[0] && client.persons[0].postalCode) {\r\n        const cleanPostalCode = client.persons[0].postalCode.replace(/\\D/g, \"\");\r\n        formattedPostalCode = cleanPostalCode.replace(/(\\d{5})(\\d{3})/, \"$1-$2\");\r\n      }\r\n\r\n      setFormData({\r\n        login: client.login || \"\",\r\n        email: client.email || \"\",\r\n        password: \"\",\r\n        confirmPassword: \"\",\r\n        person: {\r\n          id: client.persons && client.persons[0] ? client.persons[0].id || \"\" : \"\",\r\n          fullName: client.persons && client.persons[0] ? client.persons[0].fullName || \"\" : \"\",\r\n          cpf: formattedCpf || \"\",\r\n          birthDate: birthDateFormatted,\r\n          phone: formattedPhone || \"\",\r\n          email: client.persons && client.persons[0] ? client.persons[0].email || \"\" : \"\",\r\n          gender: client.persons && client.persons[0] ? client.persons[0].gender || \"\" : \"\",\r\n          address: client.persons && client.persons[0] ? client.persons[0].address || \"\" : \"\",\r\n          neighborhood: client.persons && client.persons[0] ? client.persons[0].neighborhood || \"\" : \"\",\r\n          city: client.persons && client.persons[0] ? client.persons[0].city || \"\" : \"\",\r\n          state: client.persons && client.persons[0] ? client.persons[0].state || \"\" : \"\",\r\n          postalCode: formattedPostalCode || \"\",\r\n          notes: client.persons && client.persons[0] ? client.persons[0].notes || \"\" : \"\",\r\n        }\r\n      });\r\n    } else if (isOpen) {\r\n      // Reset form when opening for new client\r\n      setEditMode(false);\r\n      resetForm();\r\n    }\r\n  }, [client, isOpen]);\r\n\r\n  const resetForm = () => {\r\n    setFormData({\r\n      login: \"\",\r\n      email: \"\",\r\n      password: \"\",\r\n      confirmPassword: \"\",\r\n      person: {\r\n        id: \"\",\r\n        fullName: \"\",\r\n        cpf: \"\",\r\n        birthDate: \"\",\r\n        phone: \"\",\r\n        email: \"\",\r\n        gender: \"\",\r\n        address: \"\",\r\n        neighborhood: \"\",\r\n        city: \"\",\r\n        state: \"\",\r\n        postalCode: \"\",\r\n        notes: \"\",\r\n      }\r\n    });\r\n    setErrors({});\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    // Client validation\r\n    if (!formData.login) {\r\n      newErrors.login = \"Login é obrigatório\";\r\n    }\r\n\r\n    if (!formData.email) {\r\n      newErrors.email = \"Email é obrigatório\";\r\n    } else if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      newErrors.email = \"Email inválido\";\r\n    }\r\n\r\n    // Password is required only for new clients\r\n    if (!editMode) {\r\n      if (!formData.password) {\r\n        newErrors.password = \"Senha é obrigatória\";\r\n      } else if (formData.password.length < 6) {\r\n        newErrors.password = \"Senha deve ter no mínimo 6 caracteres\";\r\n      }\r\n\r\n      if (formData.password !== formData.confirmPassword) {\r\n        newErrors.confirmPassword = \"Senhas não conferem\";\r\n      }\r\n    } else if (formData.password && formData.password.length < 6) {\r\n      // If editing and entering a password, it must be at least 6 characters\r\n      newErrors.password = \"Senha deve ter no mínimo 6 caracteres\";\r\n    } else if (formData.password && formData.password !== formData.confirmPassword) {\r\n      // If entering a password during edit, it must be confirmed\r\n      newErrors.confirmPassword = \"Senhas não conferem\";\r\n    }\r\n\r\n    // Person validation\r\n    if (!formData.person.fullName) {\r\n      newErrors[\"person.fullName\"] = \"Nome completo é obrigatório\";\r\n    }\r\n\r\n    if (formData.person.cpf) {\r\n      const cleanCpf = formData.person.cpf.replace(/\\D/g, \"\");\r\n      if (cleanCpf.length !== 11) {\r\n        newErrors[\"person.cpf\"] = \"CPF deve ter 11 dígitos\";\r\n      }\r\n    }\r\n\r\n    if (formData.person.email && !/\\S+@\\S+\\.\\S+/.test(formData.person.email)) {\r\n      newErrors[\"person.email\"] = \"Email inválido\";\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n\r\n    // Check if it's a person field or a client field\r\n    if (name.startsWith(\"person.\")) {\r\n      const personField = name.replace(\"person.\", \"\");\r\n      setFormData((prev) => ({\r\n        ...prev,\r\n        person: {\r\n          ...prev.person,\r\n          [personField]: value\r\n        }\r\n      }));\r\n    } else {\r\n      setFormData((prev) => ({ ...prev, [name]: value }));\r\n    }\r\n\r\n    // Clear error when user starts typing\r\n    if (errors[name]) {\r\n      setErrors((prev) => ({ ...prev, [name]: undefined }));\r\n    }\r\n  };\r\n\r\n  const togglePasswordVisibility = () => {\r\n    setShowPassword(!showPassword);\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateForm()) return;\r\n\r\n    setIsLoading(true);\r\n\r\n    try {\r\n      // Prepare person data\r\n      const personData = {\r\n        fullName: formData.person.fullName,\r\n        cpf: formData.person.cpf ? formData.person.cpf.replace(/\\D/g, \"\") : undefined,\r\n        birthDate: formData.person.birthDate || undefined,\r\n        phone: formData.person.phone ? formData.person.phone.replace(/\\D/g, \"\") : undefined,\r\n        email: formData.person.email || undefined,\r\n        gender: formData.person.gender || undefined,\r\n        address: formData.person.address || undefined,\r\n        neighborhood: formData.person.neighborhood || undefined,\r\n        city: formData.person.city || undefined,\r\n        state: formData.person.state || undefined,\r\n        postalCode: formData.person.postalCode || undefined,\r\n        notes: formData.person.notes || undefined,\r\n      };\r\n\r\n      console.log('Dados da pessoa a serem enviados:', personData);\r\n\r\n      if (editMode) {\r\n        // Update existing client account\r\n        const clientPayload = {\r\n          email: formData.email,\r\n        };\r\n\r\n        // Add password only if it's set\r\n        if (formData.password) {\r\n          clientPayload.password = formData.password;\r\n        }\r\n\r\n        await clientsService.updateClient(client.id, clientPayload);\r\n\r\n        // Update the associated person if we have a person ID\r\n        if (formData.person.id) {\r\n          try {\r\n            // Use personsService to update the person\r\n            await personsService.updatePerson(formData.person.id, personData);\r\n          } catch (personError) {\r\n            console.error(\"Erro ao atualizar pessoa:\", personError);\r\n            throw personError;\r\n          }\r\n        }\r\n      } else {\r\n        // Create new client with person\r\n        const payload = {\r\n          login: formData.login,\r\n          email: formData.email,\r\n          password: formData.password,\r\n          person: personData\r\n        };\r\n\r\n        await clientsService.createClient(payload);\r\n      }\r\n\r\n      onSuccess();\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar cliente:\", error);\r\n\r\n      // Handle API validation errors\r\n      if (error.response?.data?.errors) {\r\n        const apiErrors = {};\r\n        error.response.data.errors.forEach(err => {\r\n          apiErrors[err.param] = err.msg;\r\n        });\r\n        setErrors(apiErrors);\r\n      } else {\r\n        setErrors({\r\n          submit: error.response?.data?.message || \"Erro ao salvar cliente\"\r\n        });\r\n      }\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Não precisamos mais dessas classes, pois usaremos os componentes de módulo\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-end gap-3\">\r\n      <ModalButton\r\n        variant=\"secondary\"\r\n        moduleColor=\"people\"\r\n        onClick={onClose}\r\n        disabled={isLoading}\r\n      >\r\n        Cancelar\r\n      </ModalButton>\r\n\r\n      <ModalButton\r\n        variant=\"primary\"\r\n        moduleColor=\"people\"\r\n        type=\"submit\"\r\n        form=\"client-form\"\r\n        isLoading={isLoading}\r\n      >\r\n        {editMode ? \"Atualizar\" : \"Salvar\"}\r\n      </ModalButton>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={editMode ? \"Editar Cliente\" : \"Novo Cliente\"}\r\n      icon={<User size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"lg\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n      <form id=\"client-form\" onSubmit={handleSubmit} className=\"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6\">\r\n            {errors.submit && (\r\n              <div className=\"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2\">\r\n                <AlertCircle size={16} />\r\n                <span>{errors.submit}</span>\r\n              </div>\r\n            )}\r\n\r\n            <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-6 mt-2\">\r\n              <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                <User className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                Dados da Conta\r\n              </h3>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-10\">\r\n              {/* Login */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Login *\"\r\n                htmlFor=\"login\"\r\n                icon={<User size={16} />}\r\n                error={errors.login}\r\n                errorMessage={errors.login}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  id=\"login\"\r\n                  name=\"login\"\r\n                  value={formData.login}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Username\"\r\n                  disabled={isLoading || editMode} // Login remains non-editable for existing clients\r\n                  error={!!errors.login}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Email */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Email *\"\r\n                htmlFor=\"email\"\r\n                icon={<Mail size={16} />}\r\n                error={errors.email}\r\n                errorMessage={errors.email}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  placeholder=\"<EMAIL>\"\r\n                  disabled={isLoading}\r\n                  error={!!errors.email}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Password */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label={`Senha ${!editMode ? \"*\" : \"\"}`}\r\n                htmlFor=\"password\"\r\n                icon={<Lock size={16} />}\r\n                error={errors.password}\r\n                errorMessage={errors.password}\r\n                helpText={editMode ? \"Deixe em branco para manter a senha atual\" : \"Mínimo de 6 caracteres\"}\r\n              >\r\n                <div className=\"relative\">\r\n                  <ModuleInput\r\n                    moduleColor=\"people\"\r\n                    type={showPassword ? \"text\" : \"password\"}\r\n                    id=\"password\"\r\n                    name=\"password\"\r\n                    value={formData.password}\r\n                    onChange={handleChange}\r\n                    placeholder={editMode ? \"••••••••\" : \"Senha\"}\r\n                    required={!editMode}\r\n                    disabled={isLoading}\r\n                    error={!!errors.password}\r\n                    className=\"pr-10\"\r\n                  />\r\n                  <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={togglePasswordVisibility}\r\n                      className=\"text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-400\"\r\n                    >\r\n                      {showPassword ? (\r\n                        <EyeOff className=\"h-5 w-5\" />\r\n                      ) : (\r\n                        <Eye className=\"h-5 w-5\" />\r\n                      )}\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Confirm Password */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label={`Confirmar Senha ${!editMode ? \"*\" : \"\"}`}\r\n                htmlFor=\"confirmPassword\"\r\n                icon={<Lock size={16} />}\r\n                error={errors.confirmPassword}\r\n                errorMessage={errors.confirmPassword}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type={showPassword ? \"text\" : \"password\"}\r\n                  id=\"confirmPassword\"\r\n                  name=\"confirmPassword\"\r\n                  value={formData.confirmPassword}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Confirme a senha\"\r\n                  required={!editMode}\r\n                  disabled={isLoading}\r\n                  error={!!errors.confirmPassword}\r\n                />\r\n              </ModuleFormGroup>\r\n            </div>\r\n\r\n            <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-6 mt-4\">\r\n              <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                <User className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                Dados do Titular\r\n              </h3>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              {/* Full name */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Nome completo *\"\r\n                htmlFor=\"person.fullName\"\r\n                icon={<User size={16} />}\r\n                error={errors[\"person.fullName\"]}\r\n                errorMessage={errors[\"person.fullName\"]}\r\n                className=\"md:col-span-2\"\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  id=\"person.fullName\"\r\n                  name=\"person.fullName\"\r\n                  value={formData.person.fullName}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Nome completo\"\r\n                  disabled={isLoading}\r\n                  error={!!errors[\"person.fullName\"]}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* CPF */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"CPF\"\r\n                htmlFor=\"person.cpf\"\r\n                icon={<CreditCard size={16} />}\r\n                error={errors[\"person.cpf\"]}\r\n                errorMessage={errors[\"person.cpf\"]}\r\n              >\r\n                <div className=\"relative\">\r\n                  <MaskedInput\r\n                    type=\"cpf\"\r\n                    value={formData.person.cpf}\r\n                    onChange={(e) =>\r\n                      handleChange({\r\n                        target: { name: \"person.cpf\", value: e.target.value },\r\n                      })\r\n                    }\r\n                    placeholder=\"000.000.000-00\"\r\n                    className={`w-full rounded-md border ${errors[\"person.cpf\"] ? \"border-red-300 dark:border-red-700\" : \"border-neutral-300 dark:border-neutral-600\"} px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none`}\r\n                    disabled={isLoading}\r\n                  />\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Birth date */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Data de Nascimento\"\r\n                htmlFor=\"person.birthDate\"\r\n                icon={<Calendar size={16} />}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"date\"\r\n                  id=\"person.birthDate\"\r\n                  name=\"person.birthDate\"\r\n                  value={formData.person.birthDate}\r\n                  onChange={handleChange}\r\n                  disabled={isLoading}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Gender */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Gênero\"\r\n                htmlFor=\"person.gender\"\r\n                icon={<User size={16} />}\r\n              >\r\n                <ModuleSelect\r\n                  moduleColor=\"people\"\r\n                  id=\"person.gender\"\r\n                  name=\"person.gender\"\r\n                  value={formData.person.gender}\r\n                  onChange={handleChange}\r\n                  disabled={isLoading}\r\n                >\r\n                  <option value=\"\">Selecione</option>\r\n                  <option value=\"M\">Masculino</option>\r\n                  <option value=\"F\">Feminino</option>\r\n                  <option value=\"O\">Outro</option>\r\n                </ModuleSelect>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Person Email */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Email da Pessoa\"\r\n                htmlFor=\"person.email\"\r\n                icon={<Mail size={16} />}\r\n                error={errors[\"person.email\"]}\r\n                errorMessage={errors[\"person.email\"]}\r\n                helpText=\"Deixe em branco para usar o mesmo email da conta\"\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"email\"\r\n                  id=\"person.email\"\r\n                  name=\"person.email\"\r\n                  value={formData.person.email}\r\n                  onChange={handleChange}\r\n                  placeholder=\"<EMAIL>\"\r\n                  disabled={isLoading}\r\n                  error={!!errors[\"person.email\"]}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              {/* Phone */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Telefone\"\r\n                htmlFor=\"person.phone\"\r\n                icon={<Phone size={16} />}\r\n              >\r\n                <div className=\"relative\">\r\n                  <MaskedInput\r\n                    type=\"phone\"\r\n                    value={formData.person.phone}\r\n                    onChange={(e) =>\r\n                      handleChange({\r\n                        target: { name: \"person.phone\", value: e.target.value },\r\n                      })\r\n                    }\r\n                    placeholder=\"(00) 00000-0000\"\r\n                    className=\"w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none\"\r\n                    disabled={isLoading}\r\n                  />\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              {/* Address */}\r\n              <div className=\"md:col-span-2\">\r\n              <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-6 mt-4\">\r\n                <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                  <MapPin className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                  Endereço\r\n                </h3>\r\n              </div>\r\n                {console.log('Estado atual do formulário antes de renderizar AddressForm:', formData)}\r\n                <AddressForm\r\n                  formData={formData}\r\n                  setFormData={(newFormData) => {\r\n                    console.log('Atualizando formData no ClientFormModal:', newFormData);\r\n                    setFormData(newFormData);\r\n                  }}\r\n                  errors={errors}\r\n                  isLoading={isLoading}\r\n                  prefix=\"person.\"\r\n                  fieldMapping={{\r\n                    // Mapeamento personalizado para os campos da API ViaCEP\r\n                    logradouro: \"person.address\",\r\n                    bairro: \"person.neighborhood\",\r\n                    localidade: \"person.city\",\r\n                    uf: \"person.state\",\r\n                    cep: \"person.postalCode\"\r\n                  }}\r\n                  classes={{\r\n                    label: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\r\n                    input: \"block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:ring-2 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\",\r\n                    error: \"mt-1 text-xs text-red-600 dark:text-red-400\",\r\n                    iconContainer: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\r\n                  }}\r\n                />\r\n              </div>\r\n\r\n              {/* Notes */}\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Observações\"\r\n                htmlFor=\"person.notes\"\r\n                icon={<FileText size={16} />}\r\n                className=\"md:col-span-2\"\r\n              >\r\n                <ModuleTextarea\r\n                  moduleColor=\"people\"\r\n                  id=\"person.notes\"\r\n                  name=\"person.notes\"\r\n                  value={formData.person.notes}\r\n                  onChange={handleChange}\r\n                  placeholder=\"Observações adicionais\"\r\n                  rows={3}\r\n                  disabled={isLoading}\r\n                />\r\n              </ModuleFormGroup>\r\n            </div>\r\n      </form>\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default ClientFormModal;"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AAFA;AAHA;AAAA;AADA;AAAA;AACA;AAAA;AADA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AADA;AAAA;AAAA;AACA;AAJA;;;;;;;;;;AAWA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,OAAO;QACP,OAAO;QACP,UAAU;QACV,iBAAiB;QACjB,QAAQ;YACN,IAAI;YACJ,UAAU;YACV,KAAK;YACL,WAAW;YACX,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,cAAc;YACd,MAAM;YACN,OAAO;YACP,YAAY;YACZ,OAAO;QACT;IACF;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,QAAQ;YACpB,YAAY;YAEZ,iCAAiC;YACjC,IAAI,qBAAqB;YACzB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE;gBACtE,IAAI;oBACF,qBAAqB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,OAAO,CAAC,EAAE,CAAC,SAAS,GAAG;gBACrE,EAAE,OAAO,GAAG;oBACV,QAAQ,KAAK,CAAC,0BAA0B;gBAC1C;YACF;YAEA,eAAe;YACf,IAAI,eAAe;YACnB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE;gBAChE,MAAM,WAAW,OAAO,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO;gBACtD,eAAe,SAAS,OAAO,CAAC,gCAAgC;YAClE;YAEA,oBAAoB;YACpB,IAAI,iBAAiB;YACrB,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;gBAClE,MAAM,aAAa,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;gBAC1D,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;YAC/D;YAEA,eAAe;YACf,IAAI,sBAAsB;YAC1B,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,UAAU,EAAE;gBACvE,MAAM,kBAAkB,OAAO,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO;gBACpE,sBAAsB,gBAAgB,OAAO,CAAC,kBAAkB;YAClE;YAEA,YAAY;gBACV,OAAO,OAAO,KAAK,IAAI;gBACvB,OAAO,OAAO,KAAK,IAAI;gBACvB,UAAU;gBACV,iBAAiB;gBACjB,QAAQ;oBACN,IAAI,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,EAAE,IAAI,KAAK;oBACvE,UAAU,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,IAAI,KAAK;oBACnF,KAAK,gBAAgB;oBACrB,WAAW;oBACX,OAAO,kBAAkB;oBACzB,OAAO,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK;oBAC7E,QAAQ,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,IAAI,KAAK;oBAC/E,SAAS,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,OAAO,IAAI,KAAK;oBACjF,cAAc,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,YAAY,IAAI,KAAK;oBAC3F,MAAM,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI,KAAK;oBAC3E,OAAO,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK;oBAC7E,YAAY,uBAAuB;oBACnC,OAAO,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,GAAG,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK;gBAC/E;YACF;QACF,OAAO,IAAI,QAAQ;YACjB,yCAAyC;YACzC,YAAY;YACZ;QACF;IACF,GAAG;QAAC;QAAQ;KAAO;IAEnB,MAAM,YAAY;QAChB,YAAY;YACV,OAAO;YACP,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,QAAQ;gBACN,IAAI;gBACJ,UAAU;gBACV,KAAK;gBACL,WAAW;gBACX,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,cAAc;gBACd,MAAM;gBACN,OAAO;gBACP,YAAY;gBACZ,OAAO;YACT;QACF;QACA,UAAU,CAAC;IACb;IAEA,MAAM,eAAe;QACnB,MAAM,YAAY,CAAC;QAEnB,oBAAoB;QACpB,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,KAAK,EAAE;YACnB,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC/C,UAAU,KAAK,GAAG;QACpB;QAEA,4CAA4C;QAC5C,IAAI,CAAC,UAAU;YACb,IAAI,CAAC,SAAS,QAAQ,EAAE;gBACtB,UAAU,QAAQ,GAAG;YACvB,OAAO,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;gBACvC,UAAU,QAAQ,GAAG;YACvB;YAEA,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;gBAClD,UAAU,eAAe,GAAG;YAC9B;QACF,OAAO,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC5D,uEAAuE;YACvE,UAAU,QAAQ,GAAG;QACvB,OAAO,IAAI,SAAS,QAAQ,IAAI,SAAS,QAAQ,KAAK,SAAS,eAAe,EAAE;YAC9E,2DAA2D;YAC3D,UAAU,eAAe,GAAG;QAC9B;QAEA,oBAAoB;QACpB,IAAI,CAAC,SAAS,MAAM,CAAC,QAAQ,EAAE;YAC7B,SAAS,CAAC,kBAAkB,GAAG;QACjC;QAEA,IAAI,SAAS,MAAM,CAAC,GAAG,EAAE;YACvB,MAAM,WAAW,SAAS,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO;YACpD,IAAI,SAAS,MAAM,KAAK,IAAI;gBAC1B,SAAS,CAAC,aAAa,GAAG;YAC5B;QACF;QAEA,IAAI,SAAS,MAAM,CAAC,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,MAAM,CAAC,KAAK,GAAG;YACxE,SAAS,CAAC,eAAe,GAAG;QAC9B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAEhC,iDAAiD;QACjD,IAAI,KAAK,UAAU,CAAC,YAAY;YAC9B,MAAM,cAAc,KAAK,OAAO,CAAC,WAAW;YAC5C,YAAY,CAAC,OAAS,CAAC;oBACrB,GAAG,IAAI;oBACP,QAAQ;wBACN,GAAG,KAAK,MAAM;wBACd,CAAC,YAAY,EAAE;oBACjB;gBACF,CAAC;QACH,OAAO;YACL,YAAY,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAM,CAAC;QACnD;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACrD;IACF;IAEA,MAAM,2BAA2B;QAC/B,gBAAgB,CAAC;IACnB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;QAErB,aAAa;QAEb,IAAI;YACF,sBAAsB;YACtB,MAAM,aAAa;gBACjB,UAAU,SAAS,MAAM,CAAC,QAAQ;gBAClC,KAAK,SAAS,MAAM,CAAC,GAAG,GAAG,SAAS,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,MAAM;gBACpE,WAAW,SAAS,MAAM,CAAC,SAAS,IAAI;gBACxC,OAAO,SAAS,MAAM,CAAC,KAAK,GAAG,SAAS,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;gBAC1E,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;gBAChC,QAAQ,SAAS,MAAM,CAAC,MAAM,IAAI;gBAClC,SAAS,SAAS,MAAM,CAAC,OAAO,IAAI;gBACpC,cAAc,SAAS,MAAM,CAAC,YAAY,IAAI;gBAC9C,MAAM,SAAS,MAAM,CAAC,IAAI,IAAI;gBAC9B,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;gBAChC,YAAY,SAAS,MAAM,CAAC,UAAU,IAAI;gBAC1C,OAAO,SAAS,MAAM,CAAC,KAAK,IAAI;YAClC;YAEA,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,IAAI,UAAU;gBACZ,iCAAiC;gBACjC,MAAM,gBAAgB;oBACpB,OAAO,SAAS,KAAK;gBACvB;gBAEA,gCAAgC;gBAChC,IAAI,SAAS,QAAQ,EAAE;oBACrB,cAAc,QAAQ,GAAG,SAAS,QAAQ;gBAC5C;gBAEA,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;gBAE7C,sDAAsD;gBACtD,IAAI,SAAS,MAAM,CAAC,EAAE,EAAE;oBACtB,IAAI;wBACF,0CAA0C;wBAC1C,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE;oBACxD,EAAE,OAAO,aAAa;wBACpB,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,MAAM;oBACR;gBACF;YACF,OAAO;gBACL,gCAAgC;gBAChC,MAAM,UAAU;oBACd,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,UAAU,SAAS,QAAQ;oBAC3B,QAAQ;gBACV;gBAEA,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YACpC;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YAEzC,+BAA+B;YAC/B,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;gBAChC,MAAM,YAAY,CAAC;gBACnB,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBACjC,SAAS,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG;gBAChC;gBACA,UAAU;YACZ,OAAO;gBACL,UAAU;oBACR,QAAQ,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC3C;YACF;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,6EAA6E;IAE7E,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,SAAS;gBACT,UAAU;0BACX;;;;;;0BAID,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,MAAK;gBACL,WAAW;0BAEV,WAAW,cAAc;;;;;;;;;;;;IAKhC,qBACE,8OAAC,gLAAA,CAAA,cAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,WAAW,mBAAmB;QACrC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;kBAER,cAAA,8OAAC;YAAK,IAAG;YAAc,UAAU;YAAc,WAAU;;gBAClD,OAAO,MAAM,kBACZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,MAAM;;;;;;sCACnB,8OAAC;sCAAM,OAAO,MAAM;;;;;;;;;;;;8BAIxB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAsE;;;;;;;;;;;;8BAK1F,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,KAAK;4BACnB,cAAc,OAAO,KAAK;sCAE1B,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,aAAY;gCACZ,UAAU,aAAa;gCACvB,OAAO,CAAC,CAAC,OAAO,KAAK;;;;;;;;;;;sCAKzB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,KAAK;4BACnB,cAAc,OAAO,KAAK;sCAE1B,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,aAAY;gCACZ,UAAU;gCACV,OAAO,CAAC,CAAC,OAAO,KAAK;;;;;;;;;;;sCAKzB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAO,CAAC,MAAM,EAAE,CAAC,WAAW,MAAM,IAAI;4BACtC,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,QAAQ;4BACtB,cAAc,OAAO,QAAQ;4BAC7B,UAAU,WAAW,8CAA8C;sCAEnE,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAM,eAAe,SAAS;wCAC9B,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,aAAa,WAAW,aAAa;wCACrC,UAAU,CAAC;wCACX,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,QAAQ;wCACxB,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDAET,6BACC,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;qEAElB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAO,CAAC,gBAAgB,EAAE,CAAC,WAAW,MAAM,IAAI;4BAChD,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,OAAO,eAAe;4BAC7B,cAAc,OAAO,eAAe;sCAEpC,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAM,eAAe,SAAS;gCAC9B,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,eAAe;gCAC/B,UAAU;gCACV,aAAY;gCACZ,UAAU,CAAC;gCACX,UAAU;gCACV,OAAO,CAAC,CAAC,OAAO,eAAe;;;;;;;;;;;;;;;;;8BAKrC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAsE;;;;;;;;;;;;8BAK1F,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,MAAM,CAAC,kBAAkB;4BAChC,cAAc,MAAM,CAAC,kBAAkB;4BACvC,WAAU;sCAEV,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,QAAQ;gCAC/B,UAAU;gCACV,aAAY;gCACZ,UAAU;gCACV,OAAO,CAAC,CAAC,MAAM,CAAC,kBAAkB;;;;;;;;;;;sCAKtC,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;4BACxB,OAAO,MAAM,CAAC,aAAa;4BAC3B,cAAc,MAAM,CAAC,aAAa;sCAElC,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0IAAA,CAAA,UAAW;oCACV,MAAK;oCACL,OAAO,SAAS,MAAM,CAAC,GAAG;oCAC1B,UAAU,CAAC,IACT,aAAa;4CACX,QAAQ;gDAAE,MAAM;gDAAc,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACtD;oCAEF,aAAY;oCACZ,WAAW,CAAC,yBAAyB,EAAE,MAAM,CAAC,aAAa,GAAG,uCAAuC,6CAA6C,2GAA2G,CAAC;oCAC9P,UAAU;;;;;;;;;;;;;;;;sCAMhB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;sCAEtB,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,SAAS;gCAChC,UAAU;gCACV,UAAU;;;;;;;;;;;sCAKd,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;sCAElB,cAAA,8OAAC,kLAAA,CAAA,eAAY;gCACX,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,MAAM;gCAC7B,UAAU;gCACV,UAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,8OAAC;wCAAO,OAAM;kDAAI;;;;;;kDAClB,8OAAC;wCAAO,OAAM;kDAAI;;;;;;kDAClB,8OAAC;wCAAO,OAAM;kDAAI;;;;;;;;;;;;;;;;;sCAKtB,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;4BAClB,OAAO,MAAM,CAAC,eAAe;4BAC7B,cAAc,MAAM,CAAC,eAAe;4BACpC,UAAS;sCAET,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,KAAK;gCAC5B,UAAU;gCACV,aAAY;gCACZ,UAAU;gCACV,OAAO,CAAC,CAAC,MAAM,CAAC,eAAe;;;;;;;;;;;sCAKnC,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;sCAEnB,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0IAAA,CAAA,UAAW;oCACV,MAAK;oCACL,OAAO,SAAS,MAAM,CAAC,KAAK;oCAC5B,UAAU,CAAC,IACT,aAAa;4CACX,QAAQ;gDAAE,MAAM;gDAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;4CAAC;wCACxD;oCAEF,aAAY;oCACZ,WAAU;oCACV,UAAU;;;;;;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAU;;8CACf,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAsE;;;;;;;;;;;;gCAIzF,QAAQ,GAAG,CAAC,+DAA+D;8CAC5E,8OAAC,0IAAA,CAAA,UAAW;oCACV,UAAU;oCACV,aAAa,CAAC;wCACZ,QAAQ,GAAG,CAAC,4CAA4C;wCACxD,YAAY;oCACd;oCACA,QAAQ;oCACR,WAAW;oCACX,QAAO;oCACP,cAAc;wCACZ,wDAAwD;wCACxD,YAAY;wCACZ,QAAQ;wCACR,YAAY;wCACZ,IAAI;wCACJ,KAAK;oCACP;oCACA,SAAS;wCACP,OAAO;wCACP,OAAO;wCACP,OAAO;wCACP,eAAe;oCACjB;;;;;;;;;;;;sCAKJ,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;4BACtB,WAAU;sCAEV,cAAA,8OAAC,sLAAA,CAAA,iBAAc;gCACb,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,MAAM,CAAC,KAAK;gCAC5B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5B;uCAEe"}}, {"offset": {"line": 2102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2108, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/forms/ProfileImageUpload.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Upload, X, User } from 'lucide-react';\nimport { useToast } from '@/contexts/ToastContext';\nimport { personsService } from '@/app/modules/people/services/personsService';\nimport api from '@/utils/api';\n\nconst ProfileImageUpload = ({\n  personId,\n  onImageUploaded,\n  initialImageUrl = null,\n  deferUpload = false,\n  uploadRef = null,\n  showPreviewOnly = false,\n  previewFile = null\n}) => {\n  const [imageUrl, setImageUrl] = useState(initialImageUrl);\n  const [isUploading, setIsUploading] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(null);\n  const [previewUrl, setPreviewUrl] = useState(null);\n  const fileInputRef = useRef(null);\n  const { toast_success, toast_error } = useToast();\n\n  // Atualizar a URL da imagem quando o initialImageUrl mudar\n  useEffect(() => {\n    console.log('initialImageUrl mudou:', initialImageUrl);\n    setImageUrl(initialImageUrl);\n  }, [initialImageUrl]);\n\n  // Atualizar a pré-visualização quando o arquivo de pré-visualização mudar\n  useEffect(() => {\n    if (previewFile) {\n      console.log('Arquivo de pré-visualização recebido:', previewFile.name);\n      // Criar URL de pré-visualização para o arquivo\n      const url = URL.createObjectURL(previewFile);\n      setPreviewUrl(url);\n      setSelectedFile(previewFile);\n    }\n  }, [previewFile]);\n\n  // Limpar a URL de pré-visualização quando o componente for desmontado\n  useEffect(() => {\n    return () => {\n      if (previewUrl) {\n        URL.revokeObjectURL(previewUrl);\n      }\n    };\n  }, [previewUrl]);\n\n  // Expor o método de upload para o componente pai\n  useEffect(() => {\n    if (uploadRef) {\n      uploadRef.current = {\n        uploadSelectedImage: async () => {\n          console.log('Método uploadSelectedImage chamado');\n          console.log('Arquivo selecionado:', selectedFile ? selectedFile.name : 'Nenhum');\n          console.log('ID da pessoa:', personId);\n\n          if (selectedFile && personId) {\n            console.log('Iniciando upload do arquivo selecionado');\n            const result = await uploadImage(selectedFile);\n            console.log('Resultado do upload:', result);\n            return result;\n          }\n          console.log('Nenhum arquivo para upload ou ID da pessoa ausente');\n          return null;\n        },\n        hasSelectedFile: () => {\n          const hasFile = !!selectedFile;\n          console.log('Verificando se há arquivo selecionado:', hasFile);\n          return hasFile;\n        }\n      };\n    }\n  }, [selectedFile, personId, uploadRef]);\n\n  const handleUploadClick = () => {\n    fileInputRef.current.click();\n  };\n\n  const handleFileChange = (e) => {\n    console.log('Arquivo selecionado');\n    const file = e.target.files[0];\n    if (!file) {\n      console.log('Nenhum arquivo selecionado');\n      return;\n    }\n    console.log('Arquivo:', file.name, file.type, file.size);\n\n    // Validar tipo de arquivo\n    if (!file.type.startsWith('image/')) {\n      toast_error({\n        title: 'Erro',\n        message: 'Por favor, selecione uma imagem válida'\n      });\n      return;\n    }\n\n    // Validar tamanho do arquivo (2MB)\n    if (file.size > 2 * 1024 * 1024) {\n      toast_error({\n        title: 'Erro',\n        message: 'A imagem deve ter no máximo 2MB'\n      });\n      return;\n    }\n\n    // Armazenar o arquivo selecionado\n    setSelectedFile(file);\n\n    // Criar URL de pré-visualização\n    if (previewUrl) {\n      URL.revokeObjectURL(previewUrl);\n    }\n    const newPreviewUrl = URL.createObjectURL(file);\n    setPreviewUrl(newPreviewUrl);\n\n    // Se não estiver adiando o upload, fazer o upload imediatamente\n    if (!deferUpload && personId) {\n      uploadImage(file);\n    } else {\n      // Notificar o componente pai sobre a mudança de arquivo\n      if (onImageUploaded) {\n        onImageUploaded(null, file);\n      }\n    }\n  };\n\n  // Método para fazer o upload da imagem\n  const uploadImage = async (file) => {\n    if (!file || !personId) {\n      console.error('Upload cancelado: arquivo ou personId ausente', { file: !!file, personId });\n      return null;\n    }\n\n    setIsUploading(true);\n    console.log('Iniciando upload de imagem para pessoa ID:', personId);\n    console.log('Arquivo a ser enviado:', file.name, file.type, file.size);\n\n    try {\n      console.log('Chamando serviço de upload de imagem');\n      const response = await personsService.uploadProfileImage(personId, file);\n      console.log('Upload de imagem concluído com sucesso');\n      console.log('Resposta completa:', JSON.stringify(response));\n\n      // Atualizar URL da imagem com timestamp para evitar cache\n      const timestamp = new Date().getTime();\n\n      // Usar a URL completa retornada pelo servidor\n      const newImageUrl = response.fullImageUrl ? `${response.fullImageUrl}?t=${timestamp}` : null;\n      console.log('Nova URL da imagem:', newImageUrl);\n\n      setImageUrl(newImageUrl);\n      setSelectedFile(null); // Limpar o arquivo selecionado após o upload\n\n      if (onImageUploaded) {\n        onImageUploaded(newImageUrl);\n      }\n\n      toast_success({\n        title: 'Sucesso',\n        message: 'Imagem de perfil atualizada com sucesso'\n      });\n\n      return newImageUrl;\n    } catch (error) {\n      console.error('Erro ao fazer upload da imagem:', error);\n      toast_error({\n        title: 'Erro',\n        message: 'Erro ao fazer upload da imagem. Tente novamente.'\n      });\n      return null;\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const handleRemoveImage = async () => {\n    if (!imageUrl && !previewUrl && !selectedFile) return;\n\n    if (!confirm('Tem certeza que deseja remover a imagem de perfil?')) {\n      return;\n    }\n\n    // Se estiver apenas em pré-visualização (ainda não foi feito upload)\n    if (previewUrl && !imageUrl) {\n      // Limpar a pré-visualização\n      URL.revokeObjectURL(previewUrl);\n      setPreviewUrl(null);\n      setSelectedFile(null);\n\n      if (onImageUploaded) {\n        onImageUploaded(null, null);\n      }\n\n      return;\n    }\n\n    setIsUploading(true);\n\n    try {\n      // Enviar uma imagem vazia para remover a atual\n      // Criar um arquivo vazio (1x1 pixel transparente)\n      const emptyBlob = new Blob([''], { type: 'image/png' });\n      await personsService.uploadProfileImage(personId, new File([emptyBlob], 'empty.png', { type: 'image/png' }));\n\n      setImageUrl(null);\n      setPreviewUrl(null);\n      setSelectedFile(null);\n\n      if (onImageUploaded) {\n        onImageUploaded(null, null);\n      }\n\n      toast_success({\n        title: 'Sucesso',\n        message: 'Imagem de perfil removida com sucesso'\n      });\n    } catch (error) {\n      console.error('Erro ao remover a imagem:', error);\n      toast_error({\n        title: 'Erro',\n        message: 'Erro ao remover a imagem. Tente novamente.'\n      });\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col items-center w-full\">\n      <div className=\"relative mb-4\">\n        <div\n          className={`w-32 h-32 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center ${!showPreviewOnly ? 'cursor-pointer' : ''}`}\n          onClick={!showPreviewOnly ? handleUploadClick : undefined}\n        >\n          {isUploading ? (\n            <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white\"></div>\n            </div>\n          ) : null}\n\n          {imageUrl ? (\n            <>\n              <img\n                src={imageUrl}\n                alt=\"Foto de perfil\"\n                className=\"w-full h-full object-cover\"\n                onError={(e) => {\n                  console.error('Erro ao carregar imagem:', imageUrl);\n                  e.target.onerror = null;\n                  e.target.src = '';\n                  e.target.classList.add('hidden');\n                  setImageUrl(null);\n                }}\n              />\n              {/* Exibir URL da imagem para depuração */}\n              <div className=\"hidden\">{imageUrl}</div>\n            </>\n          ) : previewUrl ? (\n            <img\n              src={previewUrl}\n              alt=\"Pré-visualização\"\n              className=\"w-full h-full object-cover\"\n            />\n          ) : (\n            <User className=\"w-16 h-16 text-gray-400 dark:text-gray-500\" />\n          )}\n        </div>\n\n        {/* Botão de remoção absoluto - apenas se não estiver no modo de pré-visualização */}\n        {(imageUrl || previewUrl) && !showPreviewOnly && (\n          <button\n            type=\"button\"\n            onClick={handleRemoveImage}\n            className=\"absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 transition-colors\"\n            disabled={isUploading}\n          >\n            <X className=\"w-4 h-4\" />\n          </button>\n        )}\n      </div>\n\n      <input\n        type=\"file\"\n        ref={fileInputRef}\n        onChange={handleFileChange}\n        accept=\"image/*\"\n        className=\"hidden\"\n      />\n\n      {/* Botões de ação - apenas se não estiver no modo de pré-visualização */}\n      {!showPreviewOnly && (\n        <button\n          type=\"button\"\n          onClick={handleUploadClick}\n          className=\"flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50\"\n          disabled={isUploading}\n        >\n          <Upload className=\"w-4 h-4\" />\n          {imageUrl || previewUrl ? 'Alterar foto' : 'Adicionar foto'}\n        </button>\n      )}\n\n      {/* Mensagens de status - apenas se não estiver no modo de pré-visualização */}\n      {!showPreviewOnly && (\n        <div className=\"h-12 flex items-center justify-center\">\n          {isUploading && (\n            <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Enviando imagem...\n            </p>\n          )}\n\n          {deferUpload && selectedFile && !isUploading && (\n            <p className=\"text-sm text-green-500 dark:text-green-400 text-center\">\n              Imagem selecionada\n          </p>\n        )}\n      </div>\n      )}\n\n      <p className=\"text-xs text-gray-500 dark:text-gray-400\">\n        Tamanho máximo: 2MB\n      </p>\n    </div>\n  );\n};\n\nexport default ProfileImageUpload;\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AAHA;AAAA;AAAA;;;;;;;AAKA,MAAM,qBAAqB,CAAC,EAC1B,QAAQ,EACR,eAAe,EACf,kBAAkB,IAAI,EACtB,cAAc,KAAK,EACnB,YAAY,IAAI,EAChB,kBAAkB,KAAK,EACvB,cAAc,IAAI,EACnB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAE9C,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,0BAA0B;QACtC,YAAY;IACd,GAAG;QAAC;KAAgB;IAEpB,0EAA0E;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa;YACf,QAAQ,GAAG,CAAC,yCAAyC,YAAY,IAAI;YACrE,+CAA+C;YAC/C,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,cAAc;YACd,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAY;IAEhB,sEAAsE;IACtE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,YAAY;gBACd,IAAI,eAAe,CAAC;YACtB;QACF;IACF,GAAG;QAAC;KAAW;IAEf,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,UAAU,OAAO,GAAG;gBAClB,qBAAqB;oBACnB,QAAQ,GAAG,CAAC;oBACZ,QAAQ,GAAG,CAAC,wBAAwB,eAAe,aAAa,IAAI,GAAG;oBACvE,QAAQ,GAAG,CAAC,iBAAiB;oBAE7B,IAAI,gBAAgB,UAAU;wBAC5B,QAAQ,GAAG,CAAC;wBACZ,MAAM,SAAS,MAAM,YAAY;wBACjC,QAAQ,GAAG,CAAC,wBAAwB;wBACpC,OAAO;oBACT;oBACA,QAAQ,GAAG,CAAC;oBACZ,OAAO;gBACT;gBACA,iBAAiB;oBACf,MAAM,UAAU,CAAC,CAAC;oBAClB,QAAQ,GAAG,CAAC,0CAA0C;oBACtD,OAAO;gBACT;YACF;QACF;IACF,GAAG;QAAC;QAAc;QAAU;KAAU;IAEtC,MAAM,oBAAoB;QACxB,aAAa,OAAO,CAAC,KAAK;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC;YACZ;QACF;QACA,QAAQ,GAAG,CAAC,YAAY,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAEvD,0BAA0B;QAC1B,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;YACnC,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;YACA;QACF;QAEA,mCAAmC;QACnC,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;YAC/B,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;YACA;QACF;QAEA,kCAAkC;QAClC,gBAAgB;QAEhB,gCAAgC;QAChC,IAAI,YAAY;YACd,IAAI,eAAe,CAAC;QACtB;QACA,MAAM,gBAAgB,IAAI,eAAe,CAAC;QAC1C,cAAc;QAEd,gEAAgE;QAChE,IAAI,CAAC,eAAe,UAAU;YAC5B,YAAY;QACd,OAAO;YACL,wDAAwD;YACxD,IAAI,iBAAiB;gBACnB,gBAAgB,MAAM;YACxB;QACF;IACF;IAEA,uCAAuC;IACvC,MAAM,cAAc,OAAO;QACzB,IAAI,CAAC,QAAQ,CAAC,UAAU;YACtB,QAAQ,KAAK,CAAC,iDAAiD;gBAAE,MAAM,CAAC,CAAC;gBAAM;YAAS;YACxF,OAAO;QACT;QAEA,eAAe;QACf,QAAQ,GAAG,CAAC,8CAA8C;QAC1D,QAAQ,GAAG,CAAC,0BAA0B,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI;QAErE,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU;YACnE,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAEjD,0DAA0D;YAC1D,MAAM,YAAY,IAAI,OAAO,OAAO;YAEpC,8CAA8C;YAC9C,MAAM,cAAc,SAAS,YAAY,GAAG,GAAG,SAAS,YAAY,CAAC,GAAG,EAAE,WAAW,GAAG;YACxF,QAAQ,GAAG,CAAC,uBAAuB;YAEnC,YAAY;YACZ,gBAAgB,OAAO,6CAA6C;YAEpE,IAAI,iBAAiB;gBACnB,gBAAgB;YAClB;YAEA,cAAc;gBACZ,OAAO;gBACP,SAAS;YACX;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;YACA,OAAO;QACT,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc;QAE/C,IAAI,CAAC,QAAQ,uDAAuD;YAClE;QACF;QAEA,qEAAqE;QACrE,IAAI,cAAc,CAAC,UAAU;YAC3B,4BAA4B;YAC5B,IAAI,eAAe,CAAC;YACpB,cAAc;YACd,gBAAgB;YAEhB,IAAI,iBAAiB;gBACnB,gBAAgB,MAAM;YACxB;YAEA;QACF;QAEA,eAAe;QAEf,IAAI;YACF,+CAA+C;YAC/C,kDAAkD;YAClD,MAAM,YAAY,IAAI,KAAK;gBAAC;aAAG,EAAE;gBAAE,MAAM;YAAY;YACrD,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU,IAAI,KAAK;gBAAC;aAAU,EAAE,aAAa;gBAAE,MAAM;YAAY;YAEzG,YAAY;YACZ,cAAc;YACd,gBAAgB;YAEhB,IAAI,iBAAiB;gBACnB,gBAAgB,MAAM;YACxB;YAEA,cAAc;gBACZ,OAAO;gBACP,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,YAAY;gBACV,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,WAAW,CAAC,qGAAqG,EAAE,CAAC,kBAAkB,mBAAmB,IAAI;wBAC7J,SAAS,CAAC,kBAAkB,oBAAoB;;4BAE/C,4BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;;;;;;;;;uCAEf;4BAEH,yBACC;;kDACE,8OAAC;wCACC,KAAK;wCACL,KAAI;wCACJ,WAAU;wCACV,SAAS,CAAC;4CACR,QAAQ,KAAK,CAAC,4BAA4B;4CAC1C,EAAE,MAAM,CAAC,OAAO,GAAG;4CACnB,EAAE,MAAM,CAAC,GAAG,GAAG;4CACf,EAAE,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC;4CACvB,YAAY;wCACd;;;;;;kDAGF,8OAAC;wCAAI,WAAU;kDAAU;;;;;;;+CAEzB,2BACF,8OAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;qDAGZ,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;oBAKnB,CAAC,YAAY,UAAU,KAAK,CAAC,iCAC5B,8OAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;wBACV,UAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKnB,8OAAC;gBACC,MAAK;gBACL,KAAK;gBACL,UAAU;gBACV,QAAO;gBACP,WAAU;;;;;;YAIX,CAAC,iCACA,8OAAC;gBACC,MAAK;gBACL,SAAS;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBACjB,YAAY,aAAa,iBAAiB;;;;;;;YAK9C,CAAC,iCACA,8OAAC;gBAAI,WAAU;;oBACZ,6BACC,8OAAC;wBAAE,WAAU;kCAA2C;;;;;;oBAKzD,eAAe,gBAAgB,CAAC,6BAC/B,8OAAC;wBAAE,WAAU;kCAAyD;;;;;;;;;;;;0BAO5E,8OAAC;gBAAE,WAAU;0BAA2C;;;;;;;;;;;;AAK9D;uCAEe"}}, {"offset": {"line": 2495, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2501, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/people/PersonInfoTab.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { User, Mail, Phone, CreditCard, Calendar, MapPin, FileText, Users } from \"lucide-react\";\r\nimport { InputMask } from \"@react-input/mask\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { clientsService } from \"@/app/modules/people/services/clientsService\";\r\nimport ProfileImageUpload from \"@/components/forms/ProfileImageUpload\";\r\nimport AddressForm from \"@/components/common/AddressForm\";\r\nimport { ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup, ModuleMaskedInput } from \"@/components/ui\";\r\n\r\nconst PersonInfoTab = ({\r\n  formData,\r\n  setFormData,\r\n  errors,\r\n  isLoading,\r\n  handleChange,\r\n  onSubmit,\r\n  personId,\r\n  profileImageUploadRef,\r\n  isCreating,\r\n  onSetTempProfileImage,\r\n  tempProfileImage\r\n}) => {\r\n  const [clients, setClients] = useState([]);\r\n  const [isLoadingClients, setIsLoadingClients] = useState(false);\r\n  const [selectedImageFile, setSelectedImageFile] = useState(null);\r\n  const [selectedClient, setSelectedClient] = useState(null);\r\n  const [isLoadingClientData, setIsLoadingClientData] = useState(false);\r\n\r\n  // Fetch clients for dropdown\r\n  useEffect(() => {\r\n    fetchClients();\r\n  }, []);\r\n\r\n  // Fetch client data when clientId changes\r\n  useEffect(() => {\r\n    if (formData.clientId) {\r\n      fetchClientData(formData.clientId);\r\n    } else {\r\n      setSelectedClient(null);\r\n    }\r\n  }, [formData.clientId]);\r\n\r\n  const fetchClients = async () => {\r\n    setIsLoadingClients(true);\r\n    try {\r\n      const clientsList = await personsService.getClientsForSelect();\r\n      setClients(clientsList);\r\n    } catch (error) {\r\n      console.error(\"Error fetching clients:\", error);\r\n    } finally {\r\n      setIsLoadingClients(false);\r\n    }\r\n  };\r\n\r\n  const fetchClientData = async (clientId) => {\r\n    if (!clientId) return;\r\n\r\n    setIsLoadingClientData(true);\r\n    try {\r\n      const clientData = await clientsService.getClient(clientId);\r\n      setSelectedClient(clientData);\r\n\r\n      // If checkboxes are checked, update the form data with client info\r\n      if (formData.useClientEmail && clientData.email) {\r\n        handleChange({\r\n          target: { name: \"email\", value: clientData.email }\r\n        });\r\n      }\r\n\r\n      // For phone, we need to get it from the client's person\r\n      if (formData.useClientPhone && clientData.persons && clientData.persons.length > 0) {\r\n        const personPhone = clientData.persons[0].phone;\r\n        if (personPhone) {\r\n          // Format phone number\r\n          const cleanPhone = personPhone.replace(/\\D/g, \"\");\r\n          const formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n\r\n          handleChange({\r\n            target: { name: \"phone\", value: formattedPhone }\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching client data:\", error);\r\n    } finally {\r\n      setIsLoadingClientData(false);\r\n    }\r\n  };\r\n\r\n  // Não precisamos mais dessas classes, pois usaremos os componentes de módulo\r\n\r\n  // Simplificar o onSubmit para apenas chamar o onSubmit do componente pai\r\n  const handleSubmit = async () => {\r\n    console.log('Chamando onSubmit para salvar os dados da pessoa');\r\n    await onSubmit();\r\n  };\r\n\r\n  return (\r\n    <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>\r\n      <div className=\"flex flex-col md:flex-row gap-8 mb-6\">\r\n        {/* Upload de foto de perfil */}\r\n        <div className=\"flex-shrink-0 w-[200px] min-w-[200px]\">\r\n          {/* Exibir o componente de upload de imagem tanto para edição quanto para criação */}\r\n          <ProfileImageUpload\r\n            personId={personId}\r\n            initialImageUrl={formData.profileImageFullUrl}\r\n            deferUpload={true}\r\n            uploadRef={profileImageUploadRef}\r\n            onImageUploaded={(url, file) => {\r\n              console.log('Imagem selecionada ou URL recebida:', url ? 'URL' : 'Arquivo');\r\n\r\n              if (isCreating) {\r\n                // Se estiver criando uma nova pessoa, armazenar o arquivo para upload posterior\r\n                console.log('Armazenando arquivo de imagem temporário para nova pessoa');\r\n                onSetTempProfileImage && onSetTempProfileImage(file);\r\n              } else if (url) {\r\n                // Se estiver editando uma pessoa existente e receber uma URL\r\n                console.log('Nova URL da imagem recebida:', url);\r\n                handleChange({\r\n                  target: {\r\n                    name: 'profileImageFullUrl',\r\n                    value: url\r\n                  }\r\n                });\r\n              }\r\n\r\n              // Armazenar o arquivo selecionado para referência\r\n              setSelectedImageFile(file);\r\n              console.log('Arquivo de imagem armazenado:', file ? file.name : 'Nenhum');\r\n            }}\r\n            showPreviewOnly={isCreating}\r\n            previewFile={tempProfileImage}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex-grow\">\r\n          <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-3 mt-2\">\r\n            <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n              <User className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n              Dados Pessoais\r\n            </h3>\r\n          </div>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-3\">\r\n            {/* Nome completo */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Nome completo *\"\r\n              htmlFor=\"fullName\"\r\n              icon={<User size={16} />}\r\n              error={errors.fullName}\r\n              errorMessage={errors.fullName}\r\n              className=\"md:col-span-2\"\r\n            >\r\n              <ModuleInput\r\n                moduleColor=\"people\"\r\n                type=\"text\"\r\n                id=\"fullName\"\r\n                name=\"fullName\"\r\n                value={formData.fullName}\r\n                onChange={handleChange}\r\n                placeholder=\"Nome completo\"\r\n                disabled={isLoading}\r\n                error={!!errors.fullName}\r\n              />\r\n            </ModuleFormGroup>\r\n\r\n            {/* CPF */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"CPF\"\r\n              htmlFor=\"cpf\"\r\n              icon={<CreditCard size={16} />}\r\n              error={errors.cpf}\r\n              errorMessage={errors.cpf}\r\n            >\r\n              <ModuleMaskedInput\r\n                moduleColor=\"people\"\r\n                mask=\"999.999.999-99\"\r\n                replacement={{ 9: /[0-9]/ }}\r\n                value={formData.cpf}\r\n                onChange={(e) =>\r\n                  handleChange({\r\n                    target: { name: \"cpf\", value: e.target.value },\r\n                  })\r\n                }\r\n                placeholder=\"000.000.000-00\"\r\n                disabled={isLoading}\r\n                error={!!errors.cpf}\r\n              />\r\n            </ModuleFormGroup>\r\n\r\n            {/* Data de nascimento */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Data de Nascimento\"\r\n              htmlFor=\"birthDate\"\r\n              icon={<Calendar size={16} />}\r\n            >\r\n              <ModuleInput\r\n                moduleColor=\"people\"\r\n                type=\"date\"\r\n                id=\"birthDate\"\r\n                name=\"birthDate\"\r\n                value={formData.birthDate}\r\n                onChange={handleChange}\r\n                disabled={isLoading}\r\n              />\r\n            </ModuleFormGroup>\r\n\r\n            {/* Gênero */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Gênero\"\r\n              htmlFor=\"gender\"\r\n              icon={<User size={16} />}\r\n            >\r\n              <ModuleSelect\r\n                moduleColor=\"people\"\r\n                id=\"gender\"\r\n                name=\"gender\"\r\n                value={formData.gender}\r\n                onChange={handleChange}\r\n                disabled={isLoading}\r\n              >\r\n                <option value=\"\">Selecione</option>\r\n                <option value=\"M\">Masculino</option>\r\n                <option value=\"F\">Feminino</option>\r\n                <option value=\"O\">Outro</option>\r\n              </ModuleSelect>\r\n            </ModuleFormGroup>\r\n\r\n            {/* Email */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Email\"\r\n              htmlFor=\"email\"\r\n              icon={<Mail size={16} />}\r\n              error={errors.email}\r\n              errorMessage={errors.email}\r\n            >\r\n              <div className=\"space-y-2\">\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"email\"\r\n                  id=\"email\"\r\n                  name=\"email\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  placeholder=\"<EMAIL>\"\r\n                  disabled={isLoading || formData.useClientEmail}\r\n                  error={!!errors.email}\r\n                />\r\n                {formData.clientId && (\r\n                  <div className=\"flex items-center mt-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"useClientEmail\"\r\n                      name=\"useClientEmail\"\r\n                      checked={formData.useClientEmail}\r\n                      onChange={(e) => {\r\n                        const isChecked = e.target.checked;\r\n                        handleChange({\r\n                          target: { name: \"useClientEmail\", value: isChecked }\r\n                        });\r\n\r\n                        // If checked and we have client data, update email\r\n                        if (isChecked && selectedClient?.email) {\r\n                          handleChange({\r\n                            target: { name: \"email\", value: selectedClient.email }\r\n                          });\r\n                        }\r\n                      }}\r\n                      disabled={isLoading || isLoadingClientData || !selectedClient}\r\n                      className=\"h-4 w-4 text-module-people-primary border-gray-300 rounded focus:ring-module-people-primary dark:focus:ring-module-people-primary-dark\"\r\n                    />\r\n                    <label htmlFor=\"useClientEmail\" className=\"ml-2 text-sm text-gray-600 dark:text-gray-300\">\r\n                      Usar email do cliente\r\n                    </label>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </ModuleFormGroup>\r\n\r\n            {/* Telefone */}\r\n            <ModuleFormGroup\r\n              moduleColor=\"people\"\r\n              label=\"Telefone\"\r\n              htmlFor=\"phone\"\r\n              icon={<Phone size={16} />}\r\n            >\r\n              <div className=\"space-y-2\">\r\n                <ModuleMaskedInput\r\n                  moduleColor=\"people\"\r\n                  mask=\"(99) 99999-9999\"\r\n                  replacement={{ 9: /[0-9]/ }}\r\n                  value={formData.phone}\r\n                  onChange={(e) =>\r\n                    handleChange({\r\n                      target: { name: \"phone\", value: e.target.value },\r\n                    })\r\n                  }\r\n                  placeholder=\"(00) 00000-0000\"\r\n                  disabled={isLoading || formData.useClientPhone}\r\n                />\r\n                {formData.clientId && (\r\n                  <div className=\"flex items-center mt-2\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      id=\"useClientPhone\"\r\n                      name=\"useClientPhone\"\r\n                      checked={formData.useClientPhone}\r\n                      onChange={(e) => {\r\n                        const isChecked = e.target.checked;\r\n                        handleChange({\r\n                          target: { name: \"useClientPhone\", value: isChecked }\r\n                        });\r\n\r\n                        // If checked and we have client data with persons, update phone\r\n                        if (isChecked && selectedClient?.persons && selectedClient.persons.length > 0) {\r\n                          const personPhone = selectedClient.persons[0].phone;\r\n                          if (personPhone) {\r\n                            // Format phone number\r\n                            const cleanPhone = personPhone.replace(/\\D/g, \"\");\r\n                            const formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n\r\n                            handleChange({\r\n                              target: { name: \"phone\", value: formattedPhone }\r\n                            });\r\n                          }\r\n                        }\r\n                      }}\r\n                      disabled={isLoading || isLoadingClientData || !selectedClient || !selectedClient.persons || selectedClient.persons.length === 0}\r\n                      className=\"h-4 w-4 text-module-people-primary border-gray-300 rounded focus:ring-module-people-primary dark:focus:ring-module-people-primary-dark\"\r\n                    />\r\n                    <label htmlFor=\"useClientPhone\" className=\"ml-2 text-sm text-gray-600 dark:text-gray-300\">\r\n                      Usar telefone do cliente\r\n                    </label>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </ModuleFormGroup>\r\n\r\n            {/* Endereço */}\r\n            <div className=\"md:col-span-2\">\r\n              <div className=\"border-b border-neutral-200 dark:border-neutral-700 pb-2 mb-3 mt-4\">\r\n                <h3 className=\"text-base font-medium text-neutral-800 dark:text-neutral-200 flex items-center gap-2\">\r\n                  <MapPin className=\"w-5 h-5 text-module-people-icon dark:text-module-people-icon-dark\" />\r\n                  Endereço\r\n                </h3>\r\n              </div>\r\n              <AddressForm\r\n                formData={formData}\r\n                setFormData={setFormData}\r\n                errors={errors}\r\n                isLoading={isLoading}\r\n                fieldMapping={{\r\n                  // Mapeamento personalizado para os campos da API ViaCEP\r\n                  logradouro: \"address\",\r\n                  bairro: \"neighborhood\",\r\n                  localidade: \"city\",\r\n                  uf: \"state\",\r\n                  cep: \"postalCode\"\r\n                }}\r\n                moduleColor=\"people\"\r\n                classes={{\r\n                  label: \"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\",\r\n                  input: \"block w-full pl-10 pr-3 py-2 border border-neutral-300 dark:border-neutral-600 rounded-lg focus:outline-none focus:ring-1 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-people-border focus-visible:!border-module-people-border dark:focus-visible:!ring-module-people-border-dark dark:focus-visible:!border-module-people-border-dark bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\",\r\n                  error: \"mt-1 text-xs text-red-600 dark:text-red-400\",\r\n                  iconContainer: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\"\r\n                }}\r\n              />\r\n        </div>\r\n\r\n        {/* Cliente */}\r\n        <ModuleFormGroup\r\n          moduleColor=\"people\"\r\n          label=\"Cliente\"\r\n          htmlFor=\"clientId\"\r\n          icon={<Users size={16} />}\r\n        >\r\n          <ModuleSelect\r\n            moduleColor=\"people\"\r\n            id=\"clientId\"\r\n            name=\"clientId\"\r\n            value={formData.clientId}\r\n            onChange={handleChange}\r\n            disabled={isLoading || isLoadingClients}\r\n          >\r\n            <option value=\"\">Selecione um cliente</option>\r\n            {isLoadingClients ? (\r\n              <option disabled>Carregando...</option>\r\n            ) : (\r\n              clients.map((client) => (\r\n                <option key={client.value} value={client.value}>\r\n                  {client.label}\r\n                </option>\r\n              ))\r\n            )}\r\n          </ModuleSelect>\r\n        </ModuleFormGroup>\r\n\r\n        {/* Relacionamento (somente se clientId estiver preenchido) */}\r\n        {formData.clientId && (\r\n          <ModuleFormGroup\r\n            moduleColor=\"people\"\r\n            label=\"Relacionamento *\"\r\n            htmlFor=\"relationship\"\r\n            icon={<User size={16} />}\r\n            error={errors.relationship}\r\n            errorMessage={errors.relationship}\r\n          >\r\n            <ModuleSelect\r\n              moduleColor=\"people\"\r\n              id=\"relationship\"\r\n              name=\"relationship\"\r\n              value={formData.relationship}\r\n              onChange={handleChange}\r\n              disabled={isLoading}\r\n              error={!!errors.relationship}\r\n            >\r\n              <option value=\"\">Selecione</option>\r\n              <option value=\"Titular\">Titular</option>\r\n              <option value=\"Cônjuge\">Cônjuge</option>\r\n              <option value=\"Filho\">Filho</option>\r\n              <option value=\"Filha\">Filha</option>\r\n              <option value=\"Pai\">Pai</option>\r\n              <option value=\"Mãe\">Mãe</option>\r\n              <option value=\"Outro\">Outro</option>\r\n            </ModuleSelect>\r\n          </ModuleFormGroup>\r\n        )}\r\n\r\n        {/* Observações */}\r\n        <ModuleFormGroup\r\n          moduleColor=\"people\"\r\n          label=\"Observações\"\r\n          htmlFor=\"notes\"\r\n          icon={<FileText size={16} />}\r\n          className=\"md:col-span-2\"\r\n        >\r\n          <ModuleTextarea\r\n            moduleColor=\"people\"\r\n            id=\"notes\"\r\n            name=\"notes\"\r\n            value={formData.notes}\r\n            onChange={handleChange}\r\n            placeholder=\"Observações adicionais\"\r\n            rows={3}\r\n            disabled={isLoading}\r\n          />\r\n        </ModuleFormGroup>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  );\r\n};\r\n\r\nexport default PersonInfoTab;"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AACA;AACA;AANA;AAMA;AAAA;AANA;AAMA;AANA;AAMA;AANA;AAAA;AAAA;AAAA;AAAA;AAMA;AATA;;;;;;;;;;AAWA,MAAM,gBAAgB,CAAC,EACrB,QAAQ,EACR,WAAW,EACX,MAAM,EACN,SAAS,EACT,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,qBAAqB,EACrB,UAAU,EACV,qBAAqB,EACrB,gBAAgB,EACjB;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/D,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,QAAQ,EAAE;YACrB,gBAAgB,SAAS,QAAQ;QACnC,OAAO;YACL,kBAAkB;QACpB;IACF,GAAG;QAAC,SAAS,QAAQ;KAAC;IAEtB,MAAM,eAAe;QACnB,oBAAoB;QACpB,IAAI;YACF,MAAM,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,mBAAmB;YAC5D,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,UAAU;QAEf,uBAAuB;QACvB,IAAI;YACF,MAAM,aAAa,MAAM,6JAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;YAClD,kBAAkB;YAElB,mEAAmE;YACnE,IAAI,SAAS,cAAc,IAAI,WAAW,KAAK,EAAE;gBAC/C,aAAa;oBACX,QAAQ;wBAAE,MAAM;wBAAS,OAAO,WAAW,KAAK;oBAAC;gBACnD;YACF;YAEA,wDAAwD;YACxD,IAAI,SAAS,cAAc,IAAI,WAAW,OAAO,IAAI,WAAW,OAAO,CAAC,MAAM,GAAG,GAAG;gBAClF,MAAM,cAAc,WAAW,OAAO,CAAC,EAAE,CAAC,KAAK;gBAC/C,IAAI,aAAa;oBACf,sBAAsB;oBACtB,MAAM,aAAa,YAAY,OAAO,CAAC,OAAO;oBAC9C,MAAM,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;oBAEnE,aAAa;wBACX,QAAQ;4BAAE,MAAM;4BAAS,OAAO;wBAAe;oBACjD;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,6EAA6E;IAE7E,yEAAyE;IACzE,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,qBACE,8OAAC;QAAK,UAAU,CAAC;YAAQ,EAAE,cAAc;YAAI;QAAgB;kBAC3D,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC,gJAAA,CAAA,UAAkB;wBACjB,UAAU;wBACV,iBAAiB,SAAS,mBAAmB;wBAC7C,aAAa;wBACb,WAAW;wBACX,iBAAiB,CAAC,KAAK;4BACrB,QAAQ,GAAG,CAAC,uCAAuC,MAAM,QAAQ;4BAEjE,IAAI,YAAY;gCACd,gFAAgF;gCAChF,QAAQ,GAAG,CAAC;gCACZ,yBAAyB,sBAAsB;4BACjD,OAAO,IAAI,KAAK;gCACd,6DAA6D;gCAC7D,QAAQ,GAAG,CAAC,gCAAgC;gCAC5C,aAAa;oCACX,QAAQ;wCACN,MAAM;wCACN,OAAO;oCACT;gCACF;4BACF;4BAEA,kDAAkD;4BAClD,qBAAqB;4BACrB,QAAQ,GAAG,CAAC,iCAAiC,OAAO,KAAK,IAAI,GAAG;wBAClE;wBACA,iBAAiB;wBACjB,aAAa;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAsE;;;;;;;;;;;;sCAK1F,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAClB,OAAO,OAAO,QAAQ;oCACtB,cAAc,OAAO,QAAQ;oCAC7B,WAAU;8CAEV,cAAA,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,aAAY;wCACZ,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,QAAQ;;;;;;;;;;;8CAK5B,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;wCAAC,MAAM;;;;;;oCACxB,OAAO,OAAO,GAAG;oCACjB,cAAc,OAAO,GAAG;8CAExB,cAAA,8OAAC,4LAAA,CAAA,oBAAiB;wCAChB,aAAY;wCACZ,MAAK;wCACL,aAAa;4CAAE,GAAG;wCAAQ;wCAC1B,OAAO,SAAS,GAAG;wCACnB,UAAU,CAAC,IACT,aAAa;gDACX,QAAQ;oDAAE,MAAM;oDAAO,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC/C;wCAEF,aAAY;wCACZ,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,GAAG;;;;;;;;;;;8CAKvB,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;8CAEtB,cAAA,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,SAAS;wCACzB,UAAU;wCACV,UAAU;;;;;;;;;;;8CAKd,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;8CAElB,cAAA,8OAAC,kLAAA,CAAA,eAAY;wCACX,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,MAAM;wCACtB,UAAU;wCACV,UAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAI;;;;;;0DAClB,8OAAC;gDAAO,OAAM;0DAAI;;;;;;0DAClB,8OAAC;gDAAO,OAAM;0DAAI;;;;;;;;;;;;;;;;;8CAKtB,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAClB,OAAO,OAAO,KAAK;oCACnB,cAAc,OAAO,KAAK;8CAE1B,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gLAAA,CAAA,cAAW;gDACV,aAAY;gDACZ,MAAK;gDACL,IAAG;gDACH,MAAK;gDACL,OAAO,SAAS,KAAK;gDACrB,UAAU;gDACV,aAAY;gDACZ,UAAU,aAAa,SAAS,cAAc;gDAC9C,OAAO,CAAC,CAAC,OAAO,KAAK;;;;;;4CAEtB,SAAS,QAAQ,kBAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,SAAS,SAAS,cAAc;wDAChC,UAAU,CAAC;4DACT,MAAM,YAAY,EAAE,MAAM,CAAC,OAAO;4DAClC,aAAa;gEACX,QAAQ;oEAAE,MAAM;oEAAkB,OAAO;gEAAU;4DACrD;4DAEA,mDAAmD;4DACnD,IAAI,aAAa,gBAAgB,OAAO;gEACtC,aAAa;oEACX,QAAQ;wEAAE,MAAM;wEAAS,OAAO,eAAe,KAAK;oEAAC;gEACvD;4DACF;wDACF;wDACA,UAAU,aAAa,uBAAuB,CAAC;wDAC/C,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;wDAAiB,WAAU;kEAAgD;;;;;;;;;;;;;;;;;;;;;;;8CASlG,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;8CAEnB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4LAAA,CAAA,oBAAiB;gDAChB,aAAY;gDACZ,MAAK;gDACL,aAAa;oDAAE,GAAG;gDAAQ;gDAC1B,OAAO,SAAS,KAAK;gDACrB,UAAU,CAAC,IACT,aAAa;wDACX,QAAQ;4DAAE,MAAM;4DAAS,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAC;oDACjD;gDAEF,aAAY;gDACZ,UAAU,aAAa,SAAS,cAAc;;;;;;4CAE/C,SAAS,QAAQ,kBAChB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,MAAK;wDACL,SAAS,SAAS,cAAc;wDAChC,UAAU,CAAC;4DACT,MAAM,YAAY,EAAE,MAAM,CAAC,OAAO;4DAClC,aAAa;gEACX,QAAQ;oEAAE,MAAM;oEAAkB,OAAO;gEAAU;4DACrD;4DAEA,gEAAgE;4DAChE,IAAI,aAAa,gBAAgB,WAAW,eAAe,OAAO,CAAC,MAAM,GAAG,GAAG;gEAC7E,MAAM,cAAc,eAAe,OAAO,CAAC,EAAE,CAAC,KAAK;gEACnD,IAAI,aAAa;oEACf,sBAAsB;oEACtB,MAAM,aAAa,YAAY,OAAO,CAAC,OAAO;oEAC9C,MAAM,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;oEAEnE,aAAa;wEACX,QAAQ;4EAAE,MAAM;4EAAS,OAAO;wEAAe;oEACjD;gEACF;4DACF;wDACF;wDACA,UAAU,aAAa,uBAAuB,CAAC,kBAAkB,CAAC,eAAe,OAAO,IAAI,eAAe,OAAO,CAAC,MAAM,KAAK;wDAC9H,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;wDAAiB,WAAU;kEAAgD;;;;;;;;;;;;;;;;;;;;;;;8CASlG,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAsE;;;;;;;;;;;;sDAI5F,8OAAC,0IAAA,CAAA,UAAW;4CACV,UAAU;4CACV,aAAa;4CACb,QAAQ;4CACR,WAAW;4CACX,cAAc;gDACZ,wDAAwD;gDACxD,YAAY;gDACZ,QAAQ;gDACR,YAAY;gDACZ,IAAI;gDACJ,KAAK;4CACP;4CACA,aAAY;4CACZ,SAAS;gDACP,OAAO;gDACP,OAAO;gDACP,OAAO;gDACP,eAAe;4CACjB;;;;;;;;;;;;8CAKR,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;8CAEnB,cAAA,8OAAC,kLAAA,CAAA,eAAY;wCACX,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,QAAQ;wCACxB,UAAU;wCACV,UAAU,aAAa;;0DAEvB,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,iCACC,8OAAC;gDAAO,QAAQ;0DAAC;;;;;uDAEjB,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;oDAA0B,OAAO,OAAO,KAAK;8DAC3C,OAAO,KAAK;mDADF,OAAO,KAAK;;;;;;;;;;;;;;;;gCAShC,SAAS,QAAQ,kBAChB,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;oCAClB,OAAO,OAAO,YAAY;oCAC1B,cAAc,OAAO,YAAY;8CAEjC,cAAA,8OAAC,kLAAA,CAAA,eAAY;wCACX,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,YAAY;wCAC5B,UAAU;wCACV,UAAU;wCACV,OAAO,CAAC,CAAC,OAAO,YAAY;;0DAE5B,8OAAC;gDAAO,OAAM;0DAAG;;;;;;0DACjB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAU;;;;;;0DACxB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;0DACtB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;0DACpB,8OAAC;gDAAO,OAAM;0DAAQ;;;;;;;;;;;;;;;;;8CAM5B,8OAAC,wLAAA,CAAA,kBAAe;oCACd,aAAY;oCACZ,OAAM;oCACN,SAAQ;oCACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;oCACtB,WAAU;8CAEV,cAAA,8OAAC,sLAAA,CAAA,iBAAc;wCACb,aAAY;wCACZ,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,KAAK;wCACrB,UAAU;wCACV,aAAY;wCACZ,MAAM;wCACN,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;uCAEe"}}, {"offset": {"line": 3301, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3307, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/people/DocumentsTab.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { createPortal } from 'react-dom';\r\nimport {\r\n  FileText,\r\n  Upload,\r\n  Plus,\r\n  Loader2,\r\n  Trash,\r\n  Eye,\r\n  Download,\r\n  ChevronDown\r\n} from \"lucide-react\";\r\nimport { api } from \"@/utils/api\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\n\r\nconst DocumentsTab = ({ personId, onClose, isCreating, onAddTempDocument, tempDocuments = [] }) => {\r\n  const dropdownRef = useRef(null);\r\n  const buttonRef = useRef(null);\r\n  const fileInputRef = useRef(null);\r\n\r\n  const [documents, setDocuments] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [needsSave, setNeedsSave] = useState(!personId);\r\n  const [mounted, setMounted] = useState(false);\r\n  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });\r\n\r\n  const [dropdownOpen, setDropdownOpen] = useState(false);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [documentToDelete, setDocumentToDelete] = useState(null);\r\n\r\n  // Upload state\r\n  const [documentType, setDocumentType] = useState(\"\");\r\n  const [selectedFile, setSelectedFile] = useState(null);\r\n  const [uploadProgress, setUploadProgress] = useState(0);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [uploadError, setUploadError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    if (personId) {\r\n      loadDocuments();\r\n    } else if (isCreating && tempDocuments.length > 0) {\r\n      // Se estiver no modo de criação e houver documentos temporários, exibi-los\r\n      const formattedTempDocs = tempDocuments.map((doc, index) => ({\r\n        id: `temp-${index}`,\r\n        fileName: doc.file.name,\r\n        fileSize: doc.file.size,\r\n        fileType: doc.file.type,\r\n        documentType: doc.type,\r\n        createdAt: new Date().toISOString(),\r\n        isTemp: true\r\n      }));\r\n\r\n      setDocuments(formattedTempDocs);\r\n      setIsLoading(false);\r\n    } else {\r\n      setIsLoading(false);\r\n    }\r\n  }, [personId, isCreating, tempDocuments]);\r\n\r\n  // Montar o componente apenas no cliente\r\n  useEffect(() => {\r\n    setMounted(true);\r\n    return () => setMounted(false);\r\n  }, []);\r\n\r\n  // Calcular a posição do dropdown quando aberto\r\n  useEffect(() => {\r\n    if (dropdownOpen && buttonRef.current) {\r\n      const rect = buttonRef.current.getBoundingClientRect();\r\n      setDropdownPosition({\r\n        top: rect.bottom + window.scrollY,\r\n        right: window.innerWidth - rect.right,\r\n        width: Math.max(rect.width, 256) // Mínimo de 256px (w-64)\r\n      });\r\n    }\r\n  }, [dropdownOpen]);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (\r\n        buttonRef.current &&\r\n        !buttonRef.current.contains(event.target) &&\r\n        dropdownRef.current &&\r\n        !dropdownRef.current.contains(event.target)\r\n      ) {\r\n        setDropdownOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener(\"mousedown\", handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener(\"mousedown\", handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const loadDocuments = async () => {\r\n    if (!personId) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const response = await api.get(\"/documents\", {\r\n        params: {\r\n          targetId: personId,\r\n          targetType: \"person\"\r\n        }\r\n      });\r\n      setDocuments(response.data || []);\r\n    } catch (err) {\r\n      console.error(\"Error fetching documents:\", err);\r\n      setError(\"Não foi possível carregar os documentos.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleOpenUploadModal = (type) => {\r\n    setDocumentType(type);\r\n    setSelectedFile(null);\r\n    setUploadError(null);\r\n    setDropdownOpen(false);\r\n\r\n    // Trigger file input click\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.click();\r\n    }\r\n  };\r\n\r\n  const handleFileChange = (e) => {\r\n    if (e.target.files && e.target.files[0]) {\r\n      setSelectedFile(e.target.files[0]);\r\n      setUploadError(null);\r\n    }\r\n  };\r\n\r\n  const handleUploadDocument = async () => {\r\n    if (!selectedFile || !documentType) {\r\n      setUploadError(\"Selecione um arquivo e um tipo de documento\");\r\n      return;\r\n    }\r\n\r\n    setIsUploading(true);\r\n    setUploadProgress(0);\r\n    setUploadError(null);\r\n\r\n    // Se estiver no modo de criação, armazenar o documento temporariamente\r\n    if (isCreating) {\r\n      console.log('Adicionando documento temporário:', { file: selectedFile, type: documentType });\r\n\r\n      // Adicionar o documento à lista de documentos temporários\r\n      onAddTempDocument && onAddTempDocument({ file: selectedFile, type: documentType });\r\n\r\n      // Adicionar o documento à lista local para exibição\r\n      const newTempDoc = {\r\n        id: `temp-${Date.now()}`,\r\n        fileName: selectedFile.name,\r\n        fileSize: selectedFile.size,\r\n        fileType: selectedFile.type,\r\n        documentType: documentType,\r\n        createdAt: new Date().toISOString(),\r\n        isTemp: true\r\n      };\r\n\r\n      setDocuments(prev => [...prev, newTempDoc]);\r\n      setSelectedFile(null);\r\n      setDocumentType(\"\");\r\n      setIsUploading(false);\r\n      return;\r\n    }\r\n\r\n    // Se não estiver no modo de criação, fazer o upload normalmente\r\n    const formData = new FormData();\r\n    formData.append(\"documents\", selectedFile);\r\n    formData.append(\"types\", JSON.stringify([documentType]));\r\n\r\n    try {\r\n      await api.post(`/documents/upload?targetId=${personId}&targetType=person`, formData, {\r\n        headers: {\r\n          \"Content-Type\": \"multipart/form-data\",\r\n        },\r\n        onUploadProgress: (progressEvent) => {\r\n          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);\r\n          setUploadProgress(percentCompleted);\r\n        },\r\n      });\r\n\r\n      // Reload documents after successful upload\r\n      loadDocuments();\r\n      setSelectedFile(null);\r\n      setDocumentType(\"\");\r\n    } catch (err) {\r\n      console.error(\"Error uploading document:\", err);\r\n      setUploadError(err.response?.data?.message || \"Erro ao fazer upload do documento\");\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  };\r\n\r\n  const handleDeleteDocument = (documentId) => {\r\n    setDocumentToDelete(documentId);\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const confirmDeleteDocument = async () => {\r\n    if (!documentToDelete) return;\r\n\r\n    try {\r\n      await api.delete(`/documents/${documentToDelete}`);\r\n      setDocuments(documents.filter(doc => doc.id !== documentToDelete));\r\n      setConfirmDialogOpen(false);\r\n      setDocumentToDelete(null);\r\n    } catch (err) {\r\n      console.error(\"Error deleting document:\", err);\r\n      setError(\"Não foi possível excluir o documento.\");\r\n    }\r\n  };\r\n\r\n  const handleViewDocument = (documentId) => {\r\n    window.open(`/api/documents/${documentId}`, '_blank');\r\n  };\r\n\r\n  const documentTypes = [\r\n    { id: \"RG\", label: \"RG\" },\r\n    { id: \"CPF\", label: \"CPF\" },\r\n    { id: \"CNH\", label: \"Carteira de Motorista\" },\r\n    { id: \"COMP_RESIDENCIA\", label: \"Comprovante de Residência\" },\r\n    { id: \"CERTIDAO_NASCIMENTO\", label: \"Certidão de Nascimento\" },\r\n    { id: \"CERTIDAO_CASAMENTO\", label: \"Certidão de Casamento\" },\r\n    { id: \"CARTAO_VACINACAO\", label: \"Cartão de Vacinação\" },\r\n    { id: \"PASSAPORTE\", label: \"Passaporte\" },\r\n    { id: \"TITULO_ELEITOR\", label: \"Título de Eleitor\" },\r\n    { id: \"CARTEIRA_TRABALHO\", label: \"Carteira de Trabalho\" },\r\n    { id: \"OUTROS\", label: \"Outros\" }\r\n  ];\r\n\r\n  const getDocumentTypeDisplay = (type) => {\r\n    const found = documentTypes.find(docType => docType.id === type);\r\n    return found ? found.label : type;\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n\r\n    try {\r\n      return format(new Date(dateString), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return \"Data inválida\";\r\n    }\r\n  };\r\n\r\n  // Se não estiver no modo de criação e não tiver ID, exibir mensagem\r\n  if (!personId && !isCreating) {\r\n    return (\r\n      <div className=\"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <FileText size={24} />\r\n          <h3 className=\"text-lg font-semibold\">Documentos</h3>\r\n        </div>\r\n        <p className=\"text-center\">Salve os dados básicos da pessoa antes de adicionar documentos.</p>\r\n        <button\r\n          onClick={() => onClose()}\r\n          className=\"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          Voltar para Informações\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-white\">Documentos</h3>\r\n          {isLoading ? (\r\n            <Loader2 size={16} className=\"animate-spin text-neutral-400 dark:text-gray-500\" />\r\n          ) : (\r\n            <span className=\"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\">\r\n              {documents.length}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Dropdown menu for document type selection */}\r\n          <div className=\"relative\">\r\n            <button\r\n              ref={buttonRef}\r\n              onClick={() => setDropdownOpen(!dropdownOpen)}\r\n              className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n            >\r\n              <Upload size={16} />\r\n              <span>Adicionar</span>\r\n              <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />\r\n            </button>\r\n\r\n            {/* Dropdown - renderizado via portal para evitar problemas de overflow */}\r\n            {dropdownOpen && mounted && createPortal(\r\n              <div\r\n                ref={dropdownRef}\r\n                className=\"fixed z-[9999] w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700\"\r\n                style={{\r\n                  top: `${dropdownPosition.top}px`,\r\n                  right: `${dropdownPosition.right}px`,\r\n                  width: `${dropdownPosition.width}px`,\r\n                }}\r\n              >\r\n                <div className=\"p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600\">\r\n                  <h4 className=\"text-sm font-medium text-neutral-700 dark:text-gray-200\">Selecione o tipo de documento</h4>\r\n                </div>\r\n                <div className=\"max-h-96 overflow-y-auto\">\r\n                  {documentTypes.map(type => (\r\n                    <button\r\n                      key={type.id}\r\n                      onClick={() => handleOpenUploadModal(type.id)}\r\n                      className=\"w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors\"\r\n                    >\r\n                      <FileText size={14} className=\"text-neutral-400 dark:text-gray-500\" />\r\n                      <span>{type.label}</span>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n              </div>,\r\n              document.body\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Hidden file input */}\r\n      <input\r\n        type=\"file\"\r\n        ref={fileInputRef}\r\n        className=\"hidden\"\r\n        onChange={handleFileChange}\r\n        accept=\".pdf,.jpg,.jpeg,.png\"\r\n      />\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400\">\r\n          {error}\r\n          <button\r\n            onClick={loadDocuments}\r\n            className=\"ml-2 underline hover:no-underline\"\r\n          >\r\n            Tentar novamente\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* File upload preview */}\r\n      {selectedFile && (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm dark:shadow-md dark:shadow-black/20 p-4 border border-neutral-200 dark:border-gray-700\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <div className=\"p-2 rounded-full bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400\">\r\n                <FileText size={20} />\r\n              </div>\r\n              <div>\r\n                <p className=\"font-medium text-neutral-800 dark:text-gray-100\">{selectedFile.name}</p>\r\n                <p className=\"text-sm text-neutral-500 dark:text-gray-400\">\r\n                  {getDocumentTypeDisplay(documentType)} • {(selectedFile.size / 1024 / 1024).toFixed(2)} MB\r\n                </p>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"flex items-center gap-2\">\r\n              <button\r\n                onClick={() => setSelectedFile(null)}\r\n                className=\"p-2 text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors\"\r\n              >\r\n                <Trash size={16} />\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n          {uploadError && (\r\n            <div className=\"mt-3 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded text-sm\">\r\n              {uploadError}\r\n            </div>\r\n          )}\r\n\r\n          {isUploading && (\r\n            <div className=\"mt-3 space-y-1\">\r\n              <div className=\"h-2 w-full bg-neutral-200 dark:bg-gray-700 rounded-full overflow-hidden\">\r\n                <div\r\n                  className=\"h-full bg-primary-500 dark:bg-primary-600 rounded-full\"\r\n                  style={{ width: `${uploadProgress}%` }}\r\n                ></div>\r\n              </div>\r\n              <p className=\"text-right text-xs text-neutral-500 dark:text-gray-400\">\r\n                {uploadProgress}%\r\n              </p>\r\n            </div>\r\n          )}\r\n\r\n          <div className=\"mt-3 flex justify-end\">\r\n            <button\r\n              onClick={handleUploadDocument}\r\n              disabled={isUploading}\r\n              className=\"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2 disabled:opacity-50\"\r\n            >\r\n              {isUploading ? (\r\n                <>\r\n                  <Loader2 size={16} className=\"animate-spin\" />\r\n                  <span>Enviando...</span>\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Upload size={16} />\r\n                  <span>Enviar documento</span>\r\n                </>\r\n              )}\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Document list */}\r\n      {isLoading ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center\">\r\n          <div className=\"flex flex-col items-center\">\r\n            <Loader2 size={32} className=\"text-primary-500 dark:text-primary-400 animate-spin mb-4\" />\r\n            <p className=\"text-neutral-600 dark:text-gray-300\">Carregando documentos...</p>\r\n          </div>\r\n        </div>\r\n      ) : documents.length === 0 ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\">\r\n          <FileText size={48} className=\"text-neutral-300 dark:text-gray-600 mb-4\" />\r\n          <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\">Nenhum documento</h4>\r\n          <p className=\"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\">\r\n            Adicione documentos importantes como RG, CPF, comprovantes e outros para esta pessoa.\r\n          </p>\r\n          <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-2 max-w-2xl\">\r\n            {documentTypes.slice(0, 6).map(type => (\r\n              <button\r\n                key={type.id}\r\n                onClick={() => handleOpenUploadModal(type.id)}\r\n                className=\"flex flex-col items-center p-3 border border-neutral-200 dark:border-gray-700 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors\"\r\n              >\r\n                <span className=\"text-xs text-neutral-600 dark:text-gray-300 text-center\">{type.label}</span>\r\n                <Plus size={16} className=\"text-primary-500 dark:text-primary-400 mt-1\" />\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\">\r\n          <table className=\"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\">\r\n            <thead>\r\n              <tr className=\"bg-neutral-50 dark:bg-gray-900\">\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Tipo\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Arquivo\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Data de Upload\r\n                </th>\r\n                <th className=\"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Ações\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\">\r\n              {documents.map(document => (\r\n                <tr key={document.id} className={`hover:bg-neutral-50 dark:hover:bg-gray-700 ${document.isTemp ? 'bg-yellow-50 dark:bg-yellow-900/20' : ''}`}>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <span className=\"px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-700 text-neutral-800 dark:text-gray-200\">\r\n                      {document.isTemp ? document.documentType : getDocumentTypeDisplay(document.type)}\r\n                    </span>\r\n                    {document.isTemp && (\r\n                      <span className=\"ml-2 px-2 py-1 text-xs rounded-full bg-amber-100 dark:bg-amber-900/30 text-amber-800 dark:text-amber-300\">\r\n                        Pendente\r\n                      </span>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <FileText className=\"h-4 w-4 text-neutral-400 dark:text-gray-500 mr-2\" />\r\n                      <span className=\"text-neutral-700 dark:text-gray-200\">\r\n                        {document.isTemp ? document.fileName : document.filename}\r\n                      </span>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap text-neutral-500 dark:text-gray-400\">\r\n                    {formatDate(document.createdAt)}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap text-right\">\r\n                    <div className=\"flex justify-end\">\r\n                      {document.isTemp ? (\r\n                        <span className=\"text-xs text-amber-600 dark:text-amber-400\">\r\n                          Será salvo quando a pessoa for criada\r\n                        </span>\r\n                      ) : (\r\n                        <>\r\n                          <button\r\n                            onClick={() => handleViewDocument(document.id)}\r\n                            className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                            title=\"Visualizar\"\r\n                          >\r\n                            <Eye size={16} />\r\n                          </button>\r\n                          <button\r\n                            onClick={() => window.open(`/api/documents/${document.id}?download=true`, '_blank')}\r\n                            className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                            title=\"Baixar\"\r\n                          >\r\n                            <Download size={16} />\r\n                          </button>\r\n                          <button\r\n                            onClick={() => handleDeleteDocument(document.id)}\r\n                            className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                            title=\"Excluir\"\r\n                          >\r\n                            <Trash size={16} />\r\n                          </button>\r\n                        </>\r\n                      )}\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmDialogOpen}\r\n        onClose={() => {\r\n          setConfirmDialogOpen(false);\r\n          setDocumentToDelete(null);\r\n        }}\r\n        onConfirm={confirmDeleteDocument}\r\n        title=\"Excluir Documento\"\r\n        message=\"Tem certeza que deseja excluir este documento? Esta ação não pode ser desfeita.\"\r\n        variant=\"danger\"\r\n        confirmText=\"Excluir\"\r\n        cancelText=\"Cancelar\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default DocumentsTab;"], "names": [], "mappings": ";;;;AAEA;AACA;AAWA;AACA;AACA;AACA;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;;;;;AAmBA,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,EAAE;IAC5F,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAE5B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAG,OAAO;QAAG,OAAO;IAAE;IAEtF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,eAAe;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF,OAAO,IAAI,cAAc,cAAc,MAAM,GAAG,GAAG;YACjD,2EAA2E;YAC3E,MAAM,oBAAoB,cAAc,GAAG,CAAC,CAAC,KAAK,QAAU,CAAC;oBAC3D,IAAI,CAAC,KAAK,EAAE,OAAO;oBACnB,UAAU,IAAI,IAAI,CAAC,IAAI;oBACvB,UAAU,IAAI,IAAI,CAAC,IAAI;oBACvB,UAAU,IAAI,IAAI,CAAC,IAAI;oBACvB,cAAc,IAAI,IAAI;oBACtB,WAAW,IAAI,OAAO,WAAW;oBACjC,QAAQ;gBACV,CAAC;YAED,aAAa;YACb,aAAa;QACf,OAAO;YACL,aAAa;QACf;IACF,GAAG;QAAC;QAAU;QAAY;KAAc;IAExC,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QACX,OAAO,IAAM,WAAW;IAC1B,GAAG,EAAE;IAEL,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,gBAAgB,UAAU,OAAO,EAAE;YACrC,MAAM,OAAO,UAAU,OAAO,CAAC,qBAAqB;YACpD,oBAAoB;gBAClB,KAAK,KAAK,MAAM,GAAG,OAAO,OAAO;gBACjC,OAAO,OAAO,UAAU,GAAG,KAAK,KAAK;gBACrC,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,KAAK,yBAAyB;YAC5D;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACxC,YAAY,OAAO,IACnB,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAC1C;gBACA,gBAAgB;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,cAAc;gBAC3C,QAAQ;oBACN,UAAU;oBACV,YAAY;gBACd;YACF;YACA,aAAa,SAAS,IAAI,IAAI,EAAE;QAClC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,gBAAgB;QAEhB,2BAA2B;QAC3B,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK;QAC5B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;YACvC,gBAAgB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE;YACjC,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,gBAAgB,CAAC,cAAc;YAClC,eAAe;YACf;QACF;QAEA,eAAe;QACf,kBAAkB;QAClB,eAAe;QAEf,uEAAuE;QACvE,IAAI,YAAY;YACd,QAAQ,GAAG,CAAC,qCAAqC;gBAAE,MAAM;gBAAc,MAAM;YAAa;YAE1F,0DAA0D;YAC1D,qBAAqB,kBAAkB;gBAAE,MAAM;gBAAc,MAAM;YAAa;YAEhF,oDAAoD;YACpD,MAAM,aAAa;gBACjB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,UAAU,aAAa,IAAI;gBAC3B,UAAU,aAAa,IAAI;gBAC3B,UAAU,aAAa,IAAI;gBAC3B,cAAc;gBACd,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YAEA,aAAa,CAAA,OAAQ;uBAAI;oBAAM;iBAAW;YAC1C,gBAAgB;YAChB,gBAAgB;YAChB,eAAe;YACf;QACF;QAEA,gEAAgE;QAChE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,aAAa;QAC7B,SAAS,MAAM,CAAC,SAAS,KAAK,SAAS,CAAC;YAAC;SAAa;QAEtD,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,CAAC,2BAA2B,EAAE,SAAS,kBAAkB,CAAC,EAAE,UAAU;gBACnF,SAAS;oBACP,gBAAgB;gBAClB;gBACA,kBAAkB,CAAC;oBACjB,MAAM,mBAAmB,KAAK,KAAK,CAAC,AAAC,cAAc,MAAM,GAAG,MAAO,cAAc,KAAK;oBACtF,kBAAkB;gBACpB;YACF;YAEA,2CAA2C;YAC3C;YACA,gBAAgB;YAChB,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;QAChD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,qBAAqB;IACvB;IAEA,MAAM,wBAAwB;QAC5B,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,kBAAkB;YACjD,aAAa,UAAU,MAAM,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAChD,qBAAqB;YACrB,oBAAoB;QACtB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY,EAAE;IAC9C;IAEA,MAAM,gBAAgB;QACpB;YAAE,IAAI;YAAM,OAAO;QAAK;QACxB;YAAE,IAAI;YAAO,OAAO;QAAM;QAC1B;YAAE,IAAI;YAAO,OAAO;QAAwB;QAC5C;YAAE,IAAI;YAAmB,OAAO;QAA4B;QAC5D;YAAE,IAAI;YAAuB,OAAO;QAAyB;QAC7D;YAAE,IAAI;YAAsB,OAAO;QAAwB;QAC3D;YAAE,IAAI;YAAoB,OAAO;QAAsB;QACvD;YAAE,IAAI;YAAc,OAAO;QAAa;QACxC;YAAE,IAAI;YAAkB,OAAO;QAAoB;QACnD;YAAE,IAAI;YAAqB,OAAO;QAAuB;QACzD;YAAE,IAAI;YAAU,OAAO;QAAS;KACjC;IAED,MAAM,yBAAyB,CAAC;QAC9B,MAAM,QAAQ,cAAc,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,OAAO,QAAQ,MAAM,KAAK,GAAG;IAC/B;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI;YACF,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,cAAc;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,oEAAoE;IACpE,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,MAAM;;;;;;sCAChB,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,8OAAC;oBAAE,WAAU;8BAAc;;;;;;8BAC3B,8OAAC;oBACC,SAAS,IAAM;oBACf,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;4BACtE,0BACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;qDAE7B,8OAAC;gCAAK,WAAU;0CACb,UAAU,MAAM;;;;;;;;;;;;kCAKvB,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,KAAK;oCACL,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,MAAM;;;;;;sDACd,8OAAC;sDAAK;;;;;;sDACN,8OAAC,oNAAA,CAAA,cAAW;4CAAC,MAAM;4CAAI,WAAW,CAAC,+BAA+B,EAAE,eAAe,eAAe,IAAI;;;;;;;;;;;;gCAIvG,gBAAgB,yBAAW,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,gBACrC,8OAAC;oCACC,KAAK;oCACL,WAAU;oCACV,OAAO;wCACL,KAAK,GAAG,iBAAiB,GAAG,CAAC,EAAE,CAAC;wCAChC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;wCACpC,OAAO,GAAG,iBAAiB,KAAK,CAAC,EAAE,CAAC;oCACtC;;sDAEA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAG,WAAU;0DAA0D;;;;;;;;;;;sDAE1E,8OAAC;4CAAI,WAAU;sDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC;oDAEC,SAAS,IAAM,sBAAsB,KAAK,EAAE;oDAC5C,WAAU;;sEAEV,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,MAAM;4DAAI,WAAU;;;;;;sEAC9B,8OAAC;sEAAM,KAAK,KAAK;;;;;;;mDALZ,KAAK,EAAE;;;;;;;;;;;;;;;0CAUpB,SAAS,IAAI;;;;;;;;;;;;;;;;;;0BAOrB,8OAAC;gBACC,MAAK;gBACL,KAAK;gBACL,WAAU;gBACV,UAAU;gBACV,QAAO;;;;;;YAIR,uBACC,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAOJ,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;;;;;;kDAElB,8OAAC;;0DACC,8OAAC;gDAAE,WAAU;0DAAmD,aAAa,IAAI;;;;;;0DACjF,8OAAC;gDAAE,WAAU;;oDACV,uBAAuB;oDAAc;oDAAI,CAAC,aAAa,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK7F,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;oBAKlB,6BACC,8OAAC;wBAAI,WAAU;kCACZ;;;;;;oBAIJ,6BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,eAAe,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGzC,8OAAC;gCAAE,WAAU;;oCACV;oCAAe;;;;;;;;;;;;;kCAKtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,4BACC;;kDACE,8OAAC,iNAAA,CAAA,UAAO;wCAAC,MAAM;wCAAI,WAAU;;;;;;kDAC7B,8OAAC;kDAAK;;;;;;;6DAGR;;kDACE,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;;;;;;kDACd,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YASjB,0BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC7B,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;uBAGrD,UAAU,MAAM,KAAK,kBACvB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8MAAA,CAAA,WAAQ;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC9B,8OAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAC7E,8OAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAG7E,8OAAC;wBAAI,WAAU;kCACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,qBAC7B,8OAAC;gCAEC,SAAS,IAAM,sBAAsB,KAAK,EAAE;gCAC5C,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAA2D,KAAK,KAAK;;;;;;kDACrF,8OAAC,kMAAA,CAAA,OAAI;wCAAC,MAAM;wCAAI,WAAU;;;;;;;+BALrB,KAAK,EAAE;;;;;;;;;;;;;;;qCAWpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAwG;;;;;;;;;;;;;;;;;sCAK1H,8OAAC;4BAAM,WAAU;sCACd,UAAU,GAAG,CAAC,CAAA,0BACb,8OAAC;oCAAqB,WAAW,CAAC,2CAA2C,EAAE,UAAS,MAAM,GAAG,uCAAuC,IAAI;;sDAC1I,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DACb,UAAS,MAAM,GAAG,UAAS,YAAY,GAAG,uBAAuB,UAAS,IAAI;;;;;;gDAEhF,UAAS,MAAM,kBACd,8OAAC;oDAAK,WAAU;8DAA2G;;;;;;;;;;;;sDAK/H,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAK,WAAU;kEACb,UAAS,MAAM,GAAG,UAAS,QAAQ,GAAG,UAAS,QAAQ;;;;;;;;;;;;;;;;;sDAI9D,8OAAC;4CAAG,WAAU;sDACX,WAAW,UAAS,SAAS;;;;;;sDAEhC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;0DACZ,UAAS,MAAM,iBACd,8OAAC;oDAAK,WAAU;8DAA6C;;;;;yEAI7D;;sEACE,8OAAC;4DACC,SAAS,IAAM,mBAAmB,UAAS,EAAE;4DAC7C,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;gEAAC,MAAM;;;;;;;;;;;sEAEb,8OAAC;4DACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,UAAS,EAAE,CAAC,cAAc,CAAC,EAAE;4DAC1E,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,MAAM;;;;;;;;;;;sEAElB,8OAAC;4DACC,SAAS,IAAM,qBAAqB,UAAS,EAAE;4DAC/C,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;gEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;;mCAjDhB,UAAS,EAAE;;;;;;;;;;;;;;;;;;;;;0BA+D9B,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,oBAAoB;gBACtB;gBACA,WAAW;gBACX,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,aAAY;gBACZ,YAAW;;;;;;;;;;;;AAInB;uCAEe"}}, {"offset": {"line": 4354, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4360, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/contactsService.js"], "sourcesContent": ["// src/app/modules/people/services/contactsService.js\r\nimport { api } from \"@/utils/api\";\r\n\r\nexport const contactsService = {\r\n  // Get contacts for a person\r\n  getContactsByPerson: async (personId) => {\r\n    try {\r\n      const response = await api.get(\"/contacts\", {\r\n        params: {\r\n          personId\r\n        }\r\n      });\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching contacts for person ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get a single contact by ID\r\n  getContact: async (id) => {\r\n    try {\r\n      const response = await api.get(`/contacts/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching contact ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create a new contact\r\n  createContact: async (contactData) => {\r\n    try {\r\n      const response = await api.post(\"/contacts\", contactData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating contact:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update an existing contact\r\n  updateContact: async (id, contactData) => {\r\n    try {\r\n      const response = await api.put(`/contacts/${id}`, contactData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error updating contact ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a contact\r\n  deleteContact: async (id) => {\r\n    try {\r\n      await api.delete(`/contacts/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Error deleting contact ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Get relationship options for dropdowns\r\n  getRelationshipOptions: () => {\r\n    return [\r\n      { value: \"Pai\", label: \"Pai\" },\r\n      { value: \"Mãe\", label: \"Mãe\" },\r\n      { value: \"Filho(a)\", label: \"Filho(a)\" },\r\n      { value: \"Irmão(ã)\", label: \"Irmão(ã)\" },\r\n      { value: \"Cônjuge\", label: \"Cônjuge\" },\r\n      { value: \"Amigo(a)\", label: \"Amigo(a)\" },\r\n      { value: \"Colega\", label: \"Colega\" },\r\n      { value: \"Vizinho(a)\", label: \"Vizinho(a)\" },\r\n      { value: \"Responsável\", label: \"Responsável\" },\r\n      { value: \"Outro\", label: \"Outro\" }\r\n    ];\r\n  }\r\n};\r\n\r\nexport default contactsService;"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AACrD;;AAEO,MAAM,kBAAkB;IAC7B,4BAA4B;IAC5B,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,aAAa;gBAC1C,QAAQ;oBACN;gBACF;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC,EAAE;YACjE,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;YAChD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,aAAa;YAC7C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,eAAe,OAAO,IAAI;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,wBAAwB;QACtB,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAW,OAAO;YAAU;YACrC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAc,OAAO;YAAa;YAC3C;gBAAE,OAAO;gBAAe,OAAO;YAAc;YAC7C;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;AACF;uCAEe"}}, {"offset": {"line": 4469, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4475, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/people/ContactsTab.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  UserPlus,\r\n  Users,\r\n  Loader2,\r\n  Edit,\r\n  Trash,\r\n  Mail,\r\n  Phone,\r\n  FileText,\r\n  X,\r\n  User\r\n} from \"lucide-react\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport { contactsService } from \"@/app/modules/people/services/contactsService\";\r\nimport MaskedInput from \"@/components/common/MaskedInput\";\r\nimport { ModuleSelect, ModuleFormGroup } from \"@/components/ui\";\r\n\r\nconst ContactsTab = ({ personId, onClose, isCreating = false, onAddTempContact, tempContacts = [] }) => {\r\n  const [contacts, setContacts] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  const [contactFormOpen, setContactFormOpen] = useState(false);\r\n  const [selectedContact, setSelectedContact] = useState(null);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [contactToDelete, setContactToDelete] = useState(null);\r\n  const [notesModalOpen, setNotesModalOpen] = useState(false);\r\n  const [selectedNotes, setSelectedNotes] = useState({ name: \"\", notes: \"\" });\r\n\r\n  // Contact form state\r\n  const [formData, setFormData] = useState({\r\n    name: \"\",\r\n    relationship: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n    notes: \"\"\r\n  });\r\n  const [formErrors, setFormErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (personId) {\r\n      loadContacts();\r\n    }\r\n  }, [personId]);\r\n\r\n  const loadContacts = async () => {\r\n    if (!personId) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const data = await contactsService.getContactsByPerson(personId);\r\n      setContacts(data || []);\r\n    } catch (err) {\r\n      console.error(\"Error fetching contacts:\", err);\r\n      setError(\"Não foi possível carregar os contatos.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleOpenContactForm = (contact = null) => {\r\n    if (contact) {\r\n      setFormData({\r\n        name: contact.name || \"\",\r\n        relationship: contact.relationship || \"\",\r\n        email: contact.email || \"\",\r\n        phone: contact.phone || \"\",\r\n        notes: contact.notes || \"\"\r\n      });\r\n      setSelectedContact(contact);\r\n    } else {\r\n      setFormData({\r\n        name: \"\",\r\n        relationship: \"\",\r\n        email: \"\",\r\n        phone: \"\",\r\n        notes: \"\"\r\n      });\r\n      setSelectedContact(null);\r\n    }\r\n\r\n    setFormErrors({});\r\n    setContactFormOpen(true);\r\n  };\r\n\r\n  const handleCloseContactForm = () => {\r\n    setContactFormOpen(false);\r\n    setSelectedContact(null);\r\n    setFormData({\r\n      name: \"\",\r\n      relationship: \"\",\r\n      email: \"\",\r\n      phone: \"\",\r\n      notes: \"\"\r\n    });\r\n    setFormErrors({});\r\n  };\r\n\r\n  const handleDeleteContact = (contact) => {\r\n    setContactToDelete(contact.id);\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const handleViewNotes = (contact) => {\r\n    setSelectedNotes({\r\n      name: contact.name,\r\n      notes: contact.notes || \"\"\r\n    });\r\n    setNotesModalOpen(true);\r\n  };\r\n\r\n  const confirmDeleteContact = async () => {\r\n    if (!contactToDelete) return;\r\n\r\n    try {\r\n      await contactsService.deleteContact(contactToDelete);\r\n      setContacts(contacts.filter(c => c.id !== contactToDelete));\r\n      setConfirmDialogOpen(false);\r\n      setContactToDelete(null);\r\n    } catch (err) {\r\n      console.error(\"Error deleting contact:\", err);\r\n      setError(\"Não foi possível excluir o contato.\");\r\n    }\r\n  };\r\n\r\n  const handleFormChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({ ...prev, [name]: value }));\r\n\r\n    // Clear error when field is modified\r\n    if (formErrors[name]) {\r\n      setFormErrors(prev => ({ ...prev, [name]: undefined }));\r\n    }\r\n  };\r\n\r\n  const validateContactForm = () => {\r\n    const errors = {};\r\n\r\n    if (!formData.name.trim()) {\r\n      errors.name = \"Nome é obrigatório\";\r\n    }\r\n\r\n    if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      errors.email = \"Email inválido\";\r\n    }\r\n\r\n    setFormErrors(errors);\r\n    return Object.keys(errors).length === 0;\r\n  };\r\n\r\n  const handleSubmitContact = async (e) => {\r\n    e.preventDefault();\r\n\r\n    if (!validateContactForm()) {\r\n      return;\r\n    }\r\n\r\n    setIsSubmitting(true);\r\n\r\n    try {\r\n      const payload = {\r\n        personId: personId,\r\n        name: formData.name,\r\n        relationship: formData.relationship || null,\r\n        email: formData.email || null,\r\n        phone: formData.phone ? formData.phone.replace(/\\D/g, \"\") : null,\r\n        notes: formData.notes || null\r\n      };\r\n\r\n      if (isCreating) {\r\n        // Modo de criação - adicionar contato temporário\r\n        if (onAddTempContact) {\r\n          onAddTempContact({\r\n            id: `temp-${Date.now()}`,\r\n            ...payload\r\n          });\r\n        }\r\n        handleCloseContactForm();\r\n      } else {\r\n        // Modo de edição - salvar no banco de dados\r\n        if (selectedContact) {\r\n          // Update existing contact\r\n          await contactsService.updateContact(selectedContact.id, payload);\r\n        } else {\r\n          // Create new contact\r\n          await contactsService.createContact(payload);\r\n        }\r\n\r\n        // Reload contacts and close form\r\n        loadContacts();\r\n        handleCloseContactForm();\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Error saving contact:\", err);\r\n      setFormErrors({\r\n        submit: err.response?.data?.message || \"Erro ao salvar contato\"\r\n      });\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const formatPhone = (phone) => {\r\n    if (!phone) return \"N/A\";\r\n\r\n    // Phone format: (00) 00000-0000\r\n    const phoneNumbers = phone.replace(/\\D/g, '');\r\n    return phoneNumbers.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\r\n  };\r\n\r\n  // Se não estiver no modo de criação e não tiver personId, mostrar mensagem\r\n  if (!isCreating && !personId) {\r\n    return (\r\n      <div className=\"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Users size={24} />\r\n          <h3 className=\"text-lg font-semibold\">Contatos</h3>\r\n        </div>\r\n        <p className=\"text-center\">Salve os dados básicos da pessoa antes de adicionar contatos.</p>\r\n        <button\r\n          onClick={() => onClose()}\r\n          className=\"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          Voltar para Informações\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-white\">Contatos</h3>\r\n          {isCreating ? (\r\n            <span className=\"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\">\r\n              {tempContacts.length}\r\n            </span>\r\n          ) : isLoading ? (\r\n            <Loader2 size={16} className=\"animate-spin text-neutral-400 dark:text-gray-500\" />\r\n          ) : (\r\n            <span className=\"text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full\">\r\n              {contacts.length}\r\n            </span>\r\n          )}\r\n        </div>\r\n\r\n        <button\r\n          onClick={() => handleOpenContactForm()}\r\n          className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          <UserPlus size={16} />\r\n          <span>Adicionar Contato</span>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <div className=\"bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400\">\r\n          {error}\r\n          <button\r\n            onClick={loadContacts}\r\n            className=\"ml-2 underline hover:no-underline\"\r\n          >\r\n            Tentar novamente\r\n          </button>\r\n        </div>\r\n      )}\r\n\r\n      {/* Contacts list */}\r\n      {isCreating ? (\r\n        // Modo de criação - mostrar contatos temporários\r\n        tempContacts.length === 0 ? (\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\">\r\n            <Users size={48} className=\"text-neutral-300 dark:text-gray-600 mb-4\" />\r\n            <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\">Nenhum contato</h4>\r\n            <p className=\"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\">\r\n              Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de referência.\r\n            </p>\r\n            <button\r\n              onClick={() => handleOpenContactForm()}\r\n              className=\"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n            >\r\n              <UserPlus size={16} />\r\n              <span>Adicionar Contato</span>\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\">\r\n            <table className=\"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\">\r\n              <thead>\r\n                <tr className=\"bg-neutral-50 dark:bg-gray-900\">\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Nome\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Relacionamento\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Email\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Telefone\r\n                  </th>\r\n                  <th className=\"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                    Ações\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\">\r\n                {tempContacts.map(contact => (\r\n                  <tr key={contact.id} className=\"hover:bg-neutral-50 dark:hover:bg-gray-700\">\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      <div className=\"flex items-center\">\r\n                        <div className=\"h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium\">\r\n                          {contact.name.charAt(0).toUpperCase()}\r\n                        </div>\r\n                        <span className=\"ml-2 text-neutral-700 dark:text-gray-200\">{contact.name}</span>\r\n                      </div>\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      <span className=\"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300\">\r\n                        {contact.relationship || \"Não especificado\"}\r\n                      </span>\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      {contact.email ? (\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <Mail className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                          <span className=\"text-primary-600 dark:text-primary-400\">\r\n                            {contact.email}\r\n                          </span>\r\n                        </div>\r\n                      ) : (\r\n                        <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                      )}\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                      {contact.phone ? (\r\n                        <div className=\"flex items-center gap-1\">\r\n                          <Phone className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                          <span className=\"text-neutral-700 dark:text-gray-300\">\r\n                            {formatPhone(contact.phone)}\r\n                          </span>\r\n                        </div>\r\n                      ) : (\r\n                        <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                      )}\r\n                    </td>\r\n                    <td className=\"px-4 py-3 whitespace-nowrap text-right\">\r\n                      <div className=\"flex justify-end\">\r\n                        <button\r\n                          onClick={() => handleOpenContactForm(contact)}\r\n                          className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                          title=\"Editar\"\r\n                        >\r\n                          <Edit size={16} />\r\n                        </button>\r\n                        <button\r\n                          onClick={() => handleDeleteContact(contact)}\r\n                          className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                          title=\"Excluir\"\r\n                        >\r\n                          <Trash size={16} />\r\n                        </button>\r\n                      </div>\r\n                    </td>\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        )\r\n      ) : isLoading ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center\">\r\n          <div className=\"flex flex-col items-center\">\r\n            <Loader2 size={32} className=\"text-primary-500 dark:text-primary-400 animate-spin mb-4\" />\r\n            <p className=\"text-neutral-600 dark:text-gray-300\">Carregando contatos...</p>\r\n          </div>\r\n        </div>\r\n      ) : contacts.length === 0 ? (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center\">\r\n          <Users size={48} className=\"text-neutral-300 dark:text-gray-600 mb-4\" />\r\n          <h4 className=\"text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2\">Nenhum contato</h4>\r\n          <p className=\"text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center\">\r\n            Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de referência.\r\n          </p>\r\n          <button\r\n            onClick={() => handleOpenContactForm()}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n          >\r\n            <UserPlus size={16} />\r\n            <span>Adicionar Contato</span>\r\n          </button>\r\n        </div>\r\n      ) : (\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20\">\r\n          <table className=\"min-w-full divide-y divide-neutral-200 dark:divide-gray-700\">\r\n            <thead>\r\n              <tr className=\"bg-neutral-50 dark:bg-gray-900\">\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Nome\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Relacionamento\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Email\r\n                </th>\r\n                <th className=\"px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Telefone\r\n                </th>\r\n                <th className=\"px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                  Ações\r\n                </th>\r\n              </tr>\r\n            </thead>\r\n            <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700\">\r\n              {contacts.map(contact => (\r\n                <tr key={contact.id} className=\"hover:bg-neutral-50 dark:hover:bg-gray-700\">\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <div className=\"flex items-center\">\r\n                      <div className=\"h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium\">\r\n                        {contact.name.charAt(0).toUpperCase()}\r\n                      </div>\r\n                      <span className=\"ml-2 text-neutral-700 dark:text-gray-200\">{contact.name}</span>\r\n                    </div>\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    <span className=\"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300\">\r\n                      {contact.relationship || \"Não especificado\"}\r\n                    </span>\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    {contact.email ? (\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Mail className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                        <a href={`mailto:${contact.email}`} className=\"text-primary-600 dark:text-primary-400 hover:underline\">\r\n                          {contact.email}\r\n                        </a>\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap\">\r\n                    {contact.phone ? (\r\n                      <div className=\"flex items-center gap-1\">\r\n                        <Phone className=\"h-3 w-3 text-neutral-400 dark:text-gray-500\" />\r\n                        <a href={`tel:${contact.phone}`} className=\"text-neutral-700 dark:text-gray-300\">\r\n                          {formatPhone(contact.phone)}\r\n                        </a>\r\n                      </div>\r\n                    ) : (\r\n                      <span className=\"text-neutral-400 dark:text-gray-500\">Não informado</span>\r\n                    )}\r\n                  </td>\r\n                  <td className=\"px-4 py-3 whitespace-nowrap text-right\">\r\n                    <div className=\"flex justify-end\">\r\n                      <button\r\n                        onClick={() => handleOpenContactForm(contact)}\r\n                        className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                        title=\"Editar\"\r\n                      >\r\n                        <Edit size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleViewNotes(contact)}\r\n                        className={`p-1 ${contact.notes ? 'text-neutral-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400' : 'text-neutral-300 dark:text-gray-600 cursor-not-allowed'}`}\r\n                        title={contact.notes ? \"Ver Observações\" : \"Sem Observações\"}\r\n                        disabled={!contact.notes}\r\n                      >\r\n                        <FileText size={16} />\r\n                      </button>\r\n                      <button\r\n                        onClick={() => handleDeleteContact(contact)}\r\n                        className=\"p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                        title=\"Excluir\"\r\n                      >\r\n                        <Trash size={16} />\r\n                      </button>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              ))}\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      )}\r\n\r\n      {/* Contact Form Modal */}\r\n      {contactFormOpen && (\r\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n          <div className=\"fixed inset-0 bg-black/50\" onClick={handleCloseContactForm}></div>\r\n\r\n          <div className=\"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[60]\">\r\n            <div className=\"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\">\r\n              <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-gray-100\">\r\n                {selectedContact ? \"Editar Contato\" : \"Novo Contato\"}\r\n              </h3>\r\n              <button\r\n                onClick={handleCloseContactForm}\r\n                className=\"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\"\r\n              >\r\n                <X size={20} />\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"p-6\">\r\n              {formErrors.submit && (\r\n                <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded-lg text-sm\">\r\n                  {formErrors.submit}\r\n                </div>\r\n              )}\r\n\r\n              <form onSubmit={handleSubmitContact} className=\"space-y-4\">\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"name\">\r\n                    Nome *\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <User className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <input\r\n                      id=\"name\"\r\n                      name=\"name\"\r\n                      type=\"text\"\r\n                      value={formData.name}\r\n                      onChange={handleFormChange}\r\n                      className={`block w-full pl-10 pr-3 py-2 border ${formErrors.name ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'} rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}\r\n                      placeholder=\"Nome do contato\"\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                  {formErrors.name && <p className=\"mt-1 text-xs text-red-600 dark:text-red-400\">{formErrors.name}</p>}\r\n                </div>\r\n\r\n                <ModuleFormGroup\r\n                  moduleColor=\"people\"\r\n                  label=\"Relacionamento\"\r\n                  htmlFor=\"relationship\"\r\n                  icon={<Users size={16} />}\r\n                >\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    id=\"relationship\"\r\n                    name=\"relationship\"\r\n                    value={formData.relationship}\r\n                    onChange={handleFormChange}\r\n                    placeholder=\"Selecione o relacionamento\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    <option value=\"\">Selecione</option>\r\n                    <option value=\"Cônjuge\">Cônjuge</option>\r\n                    <option value=\"Filho/Filha\">Filho/Filha</option>\r\n                    <option value=\"Pai/Mãe\">Pai/Mãe</option>\r\n                    <option value=\"Irmão/Irmã\">Irmão/Irmã</option>\r\n                    <option value=\"Avô/Avó\">Avô/Avó</option>\r\n                    <option value=\"Tio/Tia\">Tio/Tia</option>\r\n                    <option value=\"Primo/Prima\">Primo/Prima</option>\r\n                    <option value=\"Amigo\">Amigo</option>\r\n                    <option value=\"Colega\">Colega</option>\r\n                    <option value=\"Outro\">Outro</option>\r\n                  </ModuleSelect>\r\n                </ModuleFormGroup>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"email\">\r\n                    Email\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <Mail className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <input\r\n                      id=\"email\"\r\n                      name=\"email\"\r\n                      type=\"email\"\r\n                      value={formData.email}\r\n                      onChange={handleFormChange}\r\n                      className={`block w-full pl-10 pr-3 py-2 border ${formErrors.email ? 'border-red-500 dark:border-red-700' : 'border-gray-300 dark:border-gray-600'} rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100`}\r\n                      placeholder=\"<EMAIL>\"\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                  {formErrors.email && <p className=\"mt-1 text-xs text-red-600 dark:text-red-400\">{formErrors.email}</p>}\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"phone\">\r\n                    Telefone\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\r\n                      <Phone className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <MaskedInput\r\n                      type=\"phone\"\r\n                      value={formData.phone}\r\n                      onChange={(e) =>\r\n                        handleFormChange({\r\n                          target: { name: \"phone\", value: e.target.value },\r\n                        })\r\n                      }\r\n                      placeholder=\"(00) 00000-0000\"\r\n                      className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\" htmlFor=\"notes\">\r\n                    Observações\r\n                  </label>\r\n                  <div className=\"relative\">\r\n                    <div className=\"absolute top-3 left-3 flex items-start pointer-events-none\">\r\n                      <FileText className=\"h-5 w-5 text-gray-400 dark:text-gray-500\" />\r\n                    </div>\r\n                    <textarea\r\n                      id=\"notes\"\r\n                      name=\"notes\"\r\n                      value={formData.notes}\r\n                      onChange={handleFormChange}\r\n                      className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none\"\r\n                      placeholder=\"Observações sobre o contato\"\r\n                      rows={3}\r\n                      disabled={isSubmitting}\r\n                    />\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex justify-end gap-3 pt-2\">\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={handleCloseContactForm}\r\n                    className=\"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    Cancelar\r\n                  </button>\r\n                  <button\r\n                    type=\"submit\"\r\n                    className=\"px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2\"\r\n                    disabled={isSubmitting}\r\n                  >\r\n                    {isSubmitting ? (\r\n                      <>\r\n                        <Loader2 size={16} className=\"animate-spin\" />\r\n                        <span>Salvando...</span>\r\n                      </>\r\n                    ) : (\r\n                      <span>Salvar</span>\r\n                    )}\r\n                  </button>\r\n                </div>\r\n              </form>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Notes Modal */}\r\n      {notesModalOpen && (\r\n        <div className=\"fixed inset-0 z-50 flex items-center justify-center overflow-y-auto\">\r\n          <div className=\"fixed inset-0 bg-black/50\" onClick={() => setNotesModalOpen(false)}></div>\r\n\r\n          <div className=\"relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[60]\">\r\n            <div className=\"flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700\">\r\n              <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-gray-100\">\r\n                Observações de {selectedNotes.name}\r\n              </h3>\r\n              <button\r\n                onClick={() => setNotesModalOpen(false)}\r\n                className=\"text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300\"\r\n              >\r\n                <X size={20} />\r\n              </button>\r\n            </div>\r\n\r\n            <div className=\"p-6\">\r\n              <div className=\"bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg border border-neutral-200 dark:border-gray-600 text-neutral-700 dark:text-gray-300 min-h-[100px] whitespace-pre-wrap\">\r\n                {selectedNotes.notes || \"Nenhuma observação disponível.\"}\r\n              </div>\r\n\r\n              <div className=\"mt-4 flex justify-end\">\r\n                <button\r\n                  onClick={() => setNotesModalOpen(false)}\r\n                  className=\"px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors\"\r\n                >\r\n                  Fechar\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmDialogOpen}\r\n        onClose={() => {\r\n          setConfirmDialogOpen(false);\r\n          setContactToDelete(null);\r\n        }}\r\n        onConfirm={confirmDeleteContact}\r\n        title=\"Excluir Contato\"\r\n        message=\"Tem certeza que deseja excluir este contato? Esta ação não pode ser desfeita.\"\r\n        variant=\"danger\"\r\n        confirmText=\"Excluir\"\r\n        cancelText=\"Cancelar\"\r\n      />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ContactsTab;"], "names": [], "mappings": ";;;;AAEA;AAaA;AACA;AACA;AACA;AAfA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAAA;AAlBA;;;;;;;;AAoBA,MAAM,cAAc,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,KAAK,EAAE,gBAAgB,EAAE,eAAe,EAAE,EAAE;IACjG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,MAAM;QAAI,OAAO;IAAG;IAEzE,qBAAqB;IACrB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,cAAc;QACd,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,OAAO,MAAM,8JAAA,CAAA,kBAAe,CAAC,mBAAmB,CAAC;YACvD,YAAY,QAAQ,EAAE;QACxB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,wBAAwB,CAAC,UAAU,IAAI;QAC3C,IAAI,SAAS;YACX,YAAY;gBACV,MAAM,QAAQ,IAAI,IAAI;gBACtB,cAAc,QAAQ,YAAY,IAAI;gBACtC,OAAO,QAAQ,KAAK,IAAI;gBACxB,OAAO,QAAQ,KAAK,IAAI;gBACxB,OAAO,QAAQ,KAAK,IAAI;YAC1B;YACA,mBAAmB;QACrB,OAAO;YACL,YAAY;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;gBACP,OAAO;gBACP,OAAO;YACT;YACA,mBAAmB;QACrB;QAEA,cAAc,CAAC;QACf,mBAAmB;IACrB;IAEA,MAAM,yBAAyB;QAC7B,mBAAmB;QACnB,mBAAmB;QACnB,YAAY;YACV,MAAM;YACN,cAAc;YACd,OAAO;YACP,OAAO;YACP,OAAO;QACT;QACA,cAAc,CAAC;IACjB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,mBAAmB,QAAQ,EAAE;QAC7B,qBAAqB;IACvB;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;YACf,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,KAAK,IAAI;QAC1B;QACA,kBAAkB;IACpB;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,8JAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;YACpC,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC1C,qBAAqB;YACrB,mBAAmB;QACrB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;QAE/C,qCAAqC;QACrC,IAAI,UAAU,CAAC,KAAK,EAAE;YACpB,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACvD;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,SAAS,CAAC;QAEhB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI;YACzB,OAAO,IAAI,GAAG;QAChB;QAEA,IAAI,SAAS,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1D,OAAO,KAAK,GAAG;QACjB;QAEA,cAAc;QACd,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAEhB,IAAI,CAAC,uBAAuB;YAC1B;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,MAAM,UAAU;gBACd,UAAU;gBACV,MAAM,SAAS,IAAI;gBACnB,cAAc,SAAS,YAAY,IAAI;gBACvC,OAAO,SAAS,KAAK,IAAI;gBACzB,OAAO,SAAS,KAAK,GAAG,SAAS,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;gBAC5D,OAAO,SAAS,KAAK,IAAI;YAC3B;YAEA,IAAI,YAAY;gBACd,iDAAiD;gBACjD,IAAI,kBAAkB;oBACpB,iBAAiB;wBACf,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;wBACxB,GAAG,OAAO;oBACZ;gBACF;gBACA;YACF,OAAO;gBACL,4CAA4C;gBAC5C,IAAI,iBAAiB;oBACnB,0BAA0B;oBAC1B,MAAM,8JAAA,CAAA,kBAAe,CAAC,aAAa,CAAC,gBAAgB,EAAE,EAAE;gBAC1D,OAAO;oBACL,qBAAqB;oBACrB,MAAM,8JAAA,CAAA,kBAAe,CAAC,aAAa,CAAC;gBACtC;gBAEA,iCAAiC;gBACjC;gBACA;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;YACvC,cAAc;gBACZ,QAAQ,IAAI,QAAQ,EAAE,MAAM,WAAW;YACzC;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO,OAAO;QAEnB,gCAAgC;QAChC,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO;QAC1C,OAAO,aAAa,OAAO,CAAC,yBAAyB;IACvD;IAEA,2EAA2E;IAC3E,IAAI,CAAC,cAAc,CAAC,UAAU;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oMAAA,CAAA,QAAK;4BAAC,MAAM;;;;;;sCACb,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,8OAAC;oBAAE,WAAU;8BAAc;;;;;;8BAC3B,8OAAC;oBACC,SAAS,IAAM;oBACf,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;4BACtE,2BACC,8OAAC;gCAAK,WAAU;0CACb,aAAa,MAAM;;;;;uCAEpB,0BACF,8OAAC,iNAAA,CAAA,UAAO;gCAAC,MAAM;gCAAI,WAAU;;;;;qDAE7B,8OAAC;gCAAK,WAAU;0CACb,SAAS,MAAM;;;;;;;;;;;;kCAKtB,8OAAC;wBACC,SAAS,IAAM;wBACf,WAAU;;0CAEV,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAKT,uBACC,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAOJ,aACC,iDAAiD;YACjD,aAAa,MAAM,KAAK,kBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC3B,8OAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAC7E,8OAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAG7E,8OAAC;wBACC,SAAS,IAAM;wBACf,WAAU;;0CAEV,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;qCAIV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAwG;;;;;;;;;;;;;;;;;sCAK1H,8OAAC;4BAAM,WAAU;sCACd,aAAa,GAAG,CAAC,CAAA,wBAChB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;kEAErC,8OAAC;wDAAK,WAAU;kEAA4C,QAAQ,IAAI;;;;;;;;;;;;;;;;;sDAG5E,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY,IAAI;;;;;;;;;;;sDAG7B,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEACb,QAAQ,KAAK;;;;;;;;;;;qEAIlB,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAK,WAAU;kEACb,YAAY,QAAQ,KAAK;;;;;;;;;;;qEAI9B,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;;;;;;kEAEd,8OAAC;wDACC,SAAS,IAAM,oBAAoB;wDACnC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;mCApDZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;uBA8D3B,0BACF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iNAAA,CAAA,UAAO;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCAC7B,8OAAC;4BAAE,WAAU;sCAAsC;;;;;;;;;;;;;;;;uBAGrD,SAAS,MAAM,KAAK,kBACtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCAC3B,8OAAC;wBAAG,WAAU;kCAA+D;;;;;;kCAC7E,8OAAC;wBAAE,WAAU;kCAAgE;;;;;;kCAG7E,8OAAC;wBACC,SAAS,IAAM;wBACf,WAAU;;0CAEV,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;0CAChB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;qCAIV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAM,WAAU;;sCACf,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAuG;;;;;;kDAGrH,8OAAC;wCAAG,WAAU;kDAAwG;;;;;;;;;;;;;;;;;sCAK1H,8OAAC;4BAAM,WAAU;sCACd,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;kEAErC,8OAAC;wDAAK,WAAU;kEAA4C,QAAQ,IAAI;;;;;;;;;;;;;;;;;sDAG5E,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY,IAAI;;;;;;;;;;;sDAG7B,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAE,MAAM,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;wDAAE,WAAU;kEAC3C,QAAQ,KAAK;;;;;;;;;;;qEAIlB,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,iBACZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,8OAAC;wDAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,KAAK,EAAE;wDAAE,WAAU;kEACxC,YAAY,QAAQ,KAAK;;;;;;;;;;;qEAI9B,8OAAC;gDAAK,WAAU;0DAAsC;;;;;;;;;;;sDAG1D,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,sBAAsB;wDACrC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;4DAAC,MAAM;;;;;;;;;;;kEAEd,8OAAC;wDACC,SAAS,IAAM,gBAAgB;wDAC/B,WAAW,CAAC,IAAI,EAAE,QAAQ,KAAK,GAAG,qFAAqF,0DAA0D;wDACjL,OAAO,QAAQ,KAAK,GAAG,oBAAoB;wDAC3C,UAAU,CAAC,QAAQ,KAAK;kEAExB,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,MAAM;;;;;;;;;;;kEAElB,8OAAC;wDACC,SAAS,IAAM,oBAAoB;wDACnC,WAAU;wDACV,OAAM;kEAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;mCA5DZ,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;YAwE5B,iCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA4B,SAAS;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,kBAAkB,mBAAmB;;;;;;kDAExC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;;oCACZ,WAAW,MAAM,kBAChB,8OAAC;wCAAI,WAAU;kDACZ,WAAW,MAAM;;;;;;kDAItB,8OAAC;wCAAK,UAAU;wCAAqB,WAAU;;0DAC7C,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAO;;;;;;kEAGlG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,WAAW,CAAC,oCAAoC,EAAE,WAAW,IAAI,GAAG,uCAAuC,uCAAuC,mIAAmI,CAAC;gEACtR,aAAY;gEACZ,UAAU;;;;;;;;;;;;oDAGb,WAAW,IAAI,kBAAI,8OAAC;wDAAE,WAAU;kEAA+C,WAAW,IAAI;;;;;;;;;;;;0DAGjG,8OAAC,wLAAA,CAAA,kBAAe;gDACd,aAAY;gDACZ,OAAM;gDACN,SAAQ;gDACR,oBAAM,8OAAC,oMAAA,CAAA,QAAK;oDAAC,MAAM;;;;;;0DAEnB,cAAA,8OAAC,kLAAA,CAAA,eAAY;oDACX,aAAY;oDACZ,IAAG;oDACH,MAAK;oDACL,OAAO,SAAS,YAAY;oDAC5B,UAAU;oDACV,aAAY;oDACZ,UAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAa;;;;;;sEAC3B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;sEACtB,8OAAC;4DAAO,OAAM;sEAAS;;;;;;sEACvB,8OAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;0DAI1B,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAQ;;;;;;kEAGnG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;0EAElB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAW,CAAC,oCAAoC,EAAE,WAAW,KAAK,GAAG,uCAAuC,uCAAuC,mIAAmI,CAAC;gEACvR,aAAY;gEACZ,UAAU;;;;;;;;;;;;oDAGb,WAAW,KAAK,kBAAI,8OAAC;wDAAE,WAAU;kEAA+C,WAAW,KAAK;;;;;;;;;;;;0DAGnG,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAQ;;;;;;kEAGnG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;;;;;;0EAEnB,8OAAC,0IAAA,CAAA,UAAW;gEACV,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IACT,iBAAiB;wEACf,QAAQ;4EAAE,MAAM;4EAAS,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC;oEACjD;gEAEF,aAAY;gEACZ,WAAU;gEACV,UAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;wDAAkE,SAAQ;kEAAQ;;;;;;kEAGnG,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;;;;;;0EAEtB,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,WAAU;gEACV,aAAY;gEACZ,MAAM;gEACN,UAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,SAAS;wDACT,WAAU;wDACV,UAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,UAAU;kEAET,6BACC;;8EACE,8OAAC,iNAAA,CAAA,UAAO;oEAAC,MAAM;oEAAI,WAAU;;;;;;8EAC7B,8OAAC;8EAAK;;;;;;;yFAGR,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAWrB,gCACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;wBAA4B,SAAS,IAAM,kBAAkB;;;;;;kCAE5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAA4D;4CACxD,cAAc,IAAI;;;;;;;kDAEpC,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,MAAM;;;;;;;;;;;;;;;;;0CAIb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ,cAAc,KAAK,IAAI;;;;;;kDAG1B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUX,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS;oBACP,qBAAqB;oBACrB,mBAAmB;gBACrB;gBACA,WAAW;gBACX,OAAM;gBACN,SAAQ;gBACR,SAAQ;gBACR,aAAY;gBACZ,YAAW;;;;;;;;;;;;AAInB;uCAEe"}}, {"offset": {"line": 6120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/insurancesService.js"], "sourcesContent": ["// app/modules/people/services/insurancesService.js\r\nimport { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { extractData, extractEntity } from \"@/utils/apiResponseAdapter\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\n\r\nexport const insurancesService = {\r\n  // Obter lista de convênios (com suporte a filtragem)\r\n  getInsurances: async ({ search, companyId, insuranceIds, page = 1, limit = 10 } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (search) params.append('search', search);\r\n      if (companyId) params.append('companyId', companyId);\r\n      if (page) params.append('page', page);\r\n      if (limit) params.append('limit', limit);\r\n\r\n      // Adicionar insuranceIds como parâmetros separados com notação de array\r\n      if (insuranceIds && insuranceIds.length > 0) {\r\n        // Garantir que insuranceIds seja um array\r\n        const insuranceIdsArray = Array.isArray(insuranceIds) ? insuranceIds : [insuranceIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        insuranceIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          params.append(`insuranceIds[${index}]`, id);\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de convênios:\", insuranceIdsArray);\r\n      }\r\n\r\n      const response = await api.get(`/insurances?${params.toString()}`);\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      // Primeiro tentamos extrair usando o formato padrão\r\n      const extracted = extractData(response.data, 'insurances', ['data']);\r\n\r\n      // Se não houver insurances no formato padrão, processamos o array diretamente\r\n      if (extracted.insurances && extracted.insurances.length > 0) {\r\n        return extracted;\r\n      } else {\r\n        // Processar o array diretamente se a API retornar apenas um array\r\n        const insurances = Array.isArray(response.data) ? response.data : [];\r\n        return {\r\n          insurances,\r\n          total: insurances.length,\r\n          pages: Math.ceil(insurances.length / limit)\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar convênios:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um convênio específico\r\n  getInsurance: async (id) => {\r\n    try {\r\n      const response = await api.get(`/insurances/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um convênio pelo ID (versão simplificada que não lança erro)\r\n  getInsuranceById: async (id) => {\r\n    try {\r\n      console.log(`Buscando convênio com ID: ${id}`);\r\n      const response = await api.get(`/insurances/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar convênio com ID ${id}:`, error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // Criar um novo convênio\r\n  createInsurance: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurances', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar convênio:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar um convênio existente\r\n  updateInsurance: async (id, data) => {\r\n    try {\r\n      const response = await api.put(`/insurances/${id}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir um convênio\r\n  deleteInsurance: async (id) => {\r\n    try {\r\n      await api.delete(`/insurances/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Adicionar um convênio a uma pessoa\r\n  addPersonInsurance: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurances/person', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao adicionar convênio à pessoa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Remover um convênio de uma pessoa\r\n  removePersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      await api.delete(`/insurances/person/${personId}/${insuranceId}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Erro ao remover convênio da pessoa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Listar convênios de uma pessoa - VERSÃO MELHORADA\r\n  listPersonInsurances: async (personId) => {\r\n    try {\r\n      console.log(`Buscando convênios para a pessoa ID: ${personId}`);\r\n      const response = await api.get(`/insurances/person/${personId}`);\r\n\r\n      // Detecta a estrutura retornada e normaliza\r\n      const data = response.data;\r\n\r\n      // Log para debug da estrutura de dados\r\n      console.log(`Resposta original da API (convenios pessoa ${personId}):`, data);\r\n\r\n      // Se for array, retorna diretamente\r\n      if (Array.isArray(data)) {\r\n        console.log(`Encontrados ${data.length} convênios no formato de array`);\r\n        return data;\r\n      }\r\n      // Se for objeto, procura por arrays\r\n      else if (data && typeof data === 'object') {\r\n        // Tenta encontrar um array dentro do objeto\r\n        const possibleArrayProps = ['personInsurances', 'insurances', 'data', 'items', 'results'];\r\n\r\n        // Primeiro procura nas propriedades comuns\r\n        for (const prop of possibleArrayProps) {\r\n          if (Array.isArray(data[prop])) {\r\n            console.log(`Encontrados ${data[prop].length} convênios na propriedade \"${prop}\"`);\r\n            return data[prop];\r\n          }\r\n        }\r\n\r\n        // Se não encontrou nas propriedades comuns, verifica todas as propriedades\r\n        for (const prop of Object.keys(data)) {\r\n          if (Array.isArray(data[prop])) {\r\n            console.log(`Encontrados ${data[prop].length} convênios na propriedade \"${prop}\"`);\r\n            return data[prop];\r\n          }\r\n        }\r\n\r\n        // Tenta extrair informações de convênios mesmo que não estejam em um array direto\r\n        if (data.insurance || data.insuranceId) {\r\n          console.log(`Encontrado um único convênio em formato não-array`);\r\n          const insurance = {\r\n            id: data.insuranceId || data.insurance?.id,\r\n            name: data.insuranceName || data.insurance?.name,\r\n            policyNumber: data.policyNumber,\r\n            validUntil: data.validUntil\r\n          };\r\n\r\n          return [insurance];\r\n        }\r\n      }\r\n\r\n      // Se não conseguir encontrar nenhum array, retorna array vazio\r\n      console.warn(\"Estrutura de resposta inesperada:\", data);\r\n      return [];\r\n    } catch (error) {\r\n      console.error(`Erro ao listar convênios da pessoa ${personId}:`, error);\r\n      // Em caso de erro, retorna array vazio em vez de lançar exceção para melhor UX\r\n      return [];\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de convênios com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  exportInsurances: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await insurancesService.getInsurances({\r\n        ...filters,\r\n        limit: 1000 // Aumentamos o limite para exportar mais dados\r\n      });\r\n\r\n      // Extrair os dados dos convênios\r\n      const insurancesArray = response.insurances || [];\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"name\", header: \"Nome\" },\r\n        {\r\n          key: \"companyName\",\r\n          header: \"Empresa\"\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = insurancesArray.map(insurance => {\r\n        return {\r\n          name: insurance.name || \"\",\r\n          companyName: insurance.company && insurance.company.name ? insurance.company.name : \"\",\r\n          createdAt: insurance.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.insuranceIds && filters.insuranceIds.length > 0) {\r\n        subtitleParts.push(`Convênios específicos: ${filters.insuranceIds.length} selecionados`);\r\n      }\r\n      if (filters.companyId) {\r\n        // Tentar encontrar o nome da empresa nos dados\r\n        const companyName = insurancesArray.find(i => i.company && i.company.id === filters.companyId)?.company?.name;\r\n        subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"convenios\",\r\n        columns,\r\n        title: \"Lista de Convênios\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar convênios:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  updatePersonInsurance: async (personId, insuranceId, data) => {\r\n    try {\r\n      const response = await api.put(`/insurances/person/${personId}/${insuranceId}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar convênio ${insuranceId} da pessoa ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter detalhes de um convênio específico de uma pessoa\r\n  getPersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      const response = await api.get(`/insurances/person/${personId}/${insuranceId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar detalhes do convênio ${insuranceId} da pessoa ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default insurancesService;"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AACnD;AAGA;AACA;AAHA;AACA;;;;;;AAIO,MAAM,oBAAoB;IAC/B,qDAAqD;IACrD,eAAe,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC;QAClF,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,MAAM,OAAO,MAAM,CAAC,QAAQ;YAChC,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAElC,wEAAwE;YACxE,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;gBAC3C,0CAA0C;gBAC1C,MAAM,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe;oBAAC;iBAAa;gBAErF,+CAA+C;gBAC/C,kBAAkB,OAAO,CAAC,CAAC,IAAI;oBAC7B,yDAAyD;oBACzD,OAAO,MAAM,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,EAAE;gBAC1C;gBAEA,QAAQ,GAAG,CAAC,6CAA6C;YAC3D;YAEA,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;YAEjE,8DAA8D;YAC9D,oDAAoD;YACpD,MAAM,YAAY,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,cAAc;gBAAC;aAAO;YAEnE,8EAA8E;YAC9E,IAAI,UAAU,UAAU,IAAI,UAAU,UAAU,CAAC,MAAM,GAAG,GAAG;gBAC3D,OAAO;YACT,OAAO;gBACL,kEAAkE;gBAClE,MAAM,aAAa,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,GAAG,EAAE;gBACpE,OAAO;oBACL;oBACA,OAAO,WAAW,MAAM;oBACxB,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;YAChD,MAAM;QACR;IACF;IAEA,qEAAqE;IACrE,kBAAkB,OAAO;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI;YAC7C,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,eAAe;YAC/C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,iBAAiB,OAAO,IAAI;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YACpD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,EAAE;YACnD,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YACpC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,EAAE;YACjD,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,sBAAsB;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,uBAAuB,OAAO,UAAU;QACtC,IAAI;YACF,MAAM,mHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa;YAChE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,sBAAsB,OAAO;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAC9D,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAE/D,4CAA4C;YAC5C,MAAM,OAAO,SAAS,IAAI;YAE1B,uCAAuC;YACvC,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,SAAS,EAAE,CAAC,EAAE;YAExE,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,MAAM,CAAC,8BAA8B,CAAC;gBACtE,OAAO;YACT,OAEK,IAAI,QAAQ,OAAO,SAAS,UAAU;gBACzC,4CAA4C;gBAC5C,MAAM,qBAAqB;oBAAC;oBAAoB;oBAAc;oBAAQ;oBAAS;iBAAU;gBAEzF,2CAA2C;gBAC3C,KAAK,MAAM,QAAQ,mBAAoB;oBACrC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;wBACjF,OAAO,IAAI,CAAC,KAAK;oBACnB;gBACF;gBAEA,2EAA2E;gBAC3E,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAO;oBACpC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;wBACjF,OAAO,IAAI,CAAC,KAAK;oBACnB;gBACF;gBAEA,kFAAkF;gBAClF,IAAI,KAAK,SAAS,IAAI,KAAK,WAAW,EAAE;oBACtC,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;oBAC/D,MAAM,YAAY;wBAChB,IAAI,KAAK,WAAW,IAAI,KAAK,SAAS,EAAE;wBACxC,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,EAAE;wBAC5C,cAAc,KAAK,YAAY;wBAC/B,YAAY,KAAK,UAAU;oBAC7B;oBAEA,OAAO;wBAAC;qBAAU;gBACpB;YACF;YAEA,+DAA+D;YAC/D,QAAQ,IAAI,CAAC,qCAAqC;YAClD,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC,EAAE;YACjE,+EAA+E;YAC/E,OAAO,EAAE;QACX;IACF;IAEA;;;;;GAKC,GACD,kBAAkB,OAAO,SAAS,eAAe,MAAM;QACrD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,kBAAkB,aAAa,CAAC;gBACrD,GAAG,OAAO;gBACV,OAAO,KAAK,+CAA+C;YAC7D;YAEA,iCAAiC;YACjC,MAAM,kBAAkB,SAAS,UAAU,IAAI,EAAE;YAEjD,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAQ,QAAQ;gBAAO;gBAC9B;oBACE,KAAK;oBACL,QAAQ;gBACV;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAA;gBACvC,OAAO;oBACL,MAAM,UAAU,IAAI,IAAI;oBACxB,aAAa,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,IAAI,GAAG,UAAU,OAAO,CAAC,IAAI,GAAG;oBACpF,WAAW,UAAU,SAAS,IAAI;gBACpC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC3D,cAAc,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;YACzF;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,+CAA+C;gBAC/C,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,EAAE,KAAK,QAAQ,SAAS,GAAG,SAAS;gBACzG,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,QAAQ,SAAS,EAAE;YACnE;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,uIAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,uBAAuB,OAAO,UAAU,aAAa;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa,EAAE;YAChF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,YAAY,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE;YAClF,MAAM;QACR;IACF;IAEA,yDAAyD;IACzD,oBAAoB,OAAO,UAAU;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,mHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa;YAC9E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,YAAY,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE;YAC3F,MAAM;QACR;IACF;AACF;uCAEe"}}, {"offset": {"line": 6408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/people/PersonInsuranceFormModal.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { AlertCircle, Calendar, CreditCard, FileText } from \"lucide-react\";\r\nimport { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from \"@/components/ui\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport { format } from \"date-fns\";\r\n\r\nconst PersonInsuranceFormModal = ({ isOpen, onClose, personId, personInsurance, onSuccess, isCreating = false, onAddTempInsurance }) => {\r\n  const [formData, setFormData] = useState({\r\n    personId: personId,\r\n    insuranceId: \"\",\r\n    policyNumber: \"\",\r\n    validUntil: \"\",\r\n    notes: \"\"\r\n  });\r\n\r\n  const [allInsurances, setAllInsurances] = useState([]);\r\n  const [availableInsurances, setAvailableInsurances] = useState([]);\r\n  const [personInsurances, setPersonInsurances] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const loadData = async () => {\r\n      setIsLoading(true);\r\n      setError(null);\r\n      try {\r\n        // Carregar todos os convênios disponíveis no sistema\r\n        const insurancesData = await insurancesService.getInsurances();\r\n        setAllInsurances(insurancesData);\r\n\r\n        if (!isCreating && personId) {\r\n          // Carregar convênios já associados a esta pessoa (apenas no modo de edição)\r\n          const personInsurancesData = await insurancesService.listPersonInsurances(personId);\r\n          setPersonInsurances(personInsurancesData);\r\n\r\n          // Filtrar convênios disponíveis (que ainda não estão associados à pessoa)\r\n          filterAvailableInsurances(insurancesData, personInsurancesData);\r\n        } else {\r\n          // No modo de criação, todos os convênios estão disponíveis\r\n          setAvailableInsurances(insurancesData);\r\n          setPersonInsurances([]);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Erro ao carregar dados:\", err);\r\n        setError(\"Erro ao carregar dados. Por favor, tente novamente.\");\r\n      } finally {\r\n        setIsLoading(false);\r\n      }\r\n    };\r\n\r\n    if (isOpen) {\r\n      loadData();\r\n    }\r\n  }, [isOpen, personId, isCreating]);\r\n\r\n  const filterAvailableInsurances = (allIns, personIns) => {\r\n    // Extrair IDs dos convênios da pessoa\r\n    const personInsuranceIds = personIns.map(pi => {\r\n      if (pi.insuranceId) return pi.insuranceId;\r\n      if (pi.insurance && pi.insurance.id) return pi.insurance.id;\r\n      return pi.id;\r\n    });\r\n\r\n    // Filtrar convênios não associados (para o modo de adição)\r\n    const available = allIns.filter(insurance =>\r\n      !personInsuranceIds.includes(insurance.id)\r\n    );\r\n\r\n    setAvailableInsurances(available);\r\n  };\r\n\r\n  // Preencher formulário quando editando\r\n  useEffect(() => {\r\n    if (personInsurance) {\r\n      let insuranceId = personInsurance.insuranceId ||\r\n                       (personInsurance.insurance ? personInsurance.insurance.id : personInsurance.id);\r\n\r\n      let validUntilFormatted = \"\";\r\n      try {\r\n        if (personInsurance.validUntil) {\r\n          validUntilFormatted = format(new Date(personInsurance.validUntil), \"yyyy-MM-dd\");\r\n        }\r\n      } catch (e) {\r\n        console.error(\"Error formatting date:\", e);\r\n      }\r\n\r\n      setFormData({\r\n        personId: personId,\r\n        insuranceId: insuranceId,\r\n        policyNumber: personInsurance.policyNumber || \"\",\r\n        validUntil: validUntilFormatted,\r\n        notes: personInsurance.notes || \"\"\r\n      });\r\n    } else {\r\n      setFormData({\r\n        personId: personId,\r\n        insuranceId: \"\",\r\n        policyNumber: \"\",\r\n        validUntil: \"\",\r\n        notes: \"\"\r\n      });\r\n    }\r\n  }, [personInsurance, personId]);\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    setFormData(prev => ({\r\n      ...prev,\r\n      [name]: value\r\n    }));\r\n  };\r\n\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    setIsSubmitting(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Converter validUntil para formato ISO se fornecido\r\n      const validUntilFormatted = formData.validUntil\r\n        ? new Date(formData.validUntil + 'T00:00:00Z').toISOString()\r\n        : null;\r\n\r\n      // Preparar o payload\r\n      const payload = {\r\n        personId: formData.personId,\r\n        insuranceId: formData.insuranceId,\r\n        policyNumber: formData.policyNumber,\r\n        validUntil: validUntilFormatted,\r\n        notes: formData.notes\r\n      };\r\n\r\n      if (isCreating) {\r\n        // Modo de criação - adicionar convênio temporário\r\n        if (onAddTempInsurance) {\r\n          // Adicionar informações do convênio selecionado\r\n          const selectedInsurance = allInsurances.find(ins => ins.id === formData.insuranceId);\r\n\r\n          onAddTempInsurance({\r\n            ...payload,\r\n            name: selectedInsurance?.name || \"Convênio\",\r\n            isTemp: true\r\n          });\r\n\r\n          // Exibir toast de sucesso\r\n          if (window.showToast) {\r\n            window.showToast({\r\n              type: \"success\",\r\n              message: \"Convênio adicionado temporariamente\"\r\n            });\r\n          }\r\n\r\n          // Fechar o modal\r\n          onSuccess();\r\n        }\r\n      } else if (personInsurance) {\r\n        // Modo de edição\r\n        await insurancesService.updatePersonInsurance(personId, formData.insuranceId, {\r\n          policyNumber: formData.policyNumber,\r\n          validUntil: validUntilFormatted,\r\n          notes: formData.notes\r\n        });\r\n\r\n        // Chamar callback de sucesso\r\n        onSuccess();\r\n\r\n        // Exibir toast de sucesso\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: \"success\",\r\n            message: \"Convênio atualizado com sucesso\"\r\n          });\r\n        }\r\n      } else {\r\n        // Modo de adição normal\r\n        await insurancesService.addPersonInsurance(payload);\r\n\r\n        // Chamar callback de sucesso\r\n        onSuccess();\r\n\r\n        // Exibir toast de sucesso\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: \"success\",\r\n            message: \"Convênio adicionado com sucesso\"\r\n          });\r\n        }\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Erro ao salvar convênio:\", err);\r\n      const errorMessage = err.response?.data?.message || \"Ocorreu um erro ao salvar o convênio.\";\r\n      setError(errorMessage);\r\n\r\n      // Exibir toast de erro se disponível\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: \"error\",\r\n          message: errorMessage\r\n        });\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // Função para buscar o nome de um convênio pelo ID\r\n  const getInsuranceName = (id) => {\r\n    const insurance = allInsurances.find(ins => ins.id === id);\r\n    return insurance ? insurance.name : \"Convênio\";\r\n  };\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-end gap-3\">\r\n      <ModalButton\r\n        variant=\"secondary\"\r\n        moduleColor=\"people\"\r\n        onClick={onClose}\r\n        disabled={isSubmitting}\r\n      >\r\n        Cancelar\r\n      </ModalButton>\r\n\r\n      <ModalButton\r\n        variant=\"primary\"\r\n        moduleColor=\"people\"\r\n        type=\"submit\"\r\n        form=\"person-insurance-form\"\r\n        isLoading={isSubmitting}\r\n        disabled={isSubmitting || (!personInsurance && !formData.insuranceId)}\r\n      >\r\n        {personInsurance ? \"Atualizar\" : \"Adicionar\"}\r\n      </ModalButton>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={personInsurance ? `Editar ${getInsuranceName(formData.insuranceId)}` : \"Adicionar Convênio\"}\r\n      icon={<CreditCard size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"md\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n      <form id=\"person-insurance-form\" onSubmit={handleSubmit} className=\"overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6\">\r\n          {error && (\r\n            <div className=\"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start gap-2\">\r\n              <AlertCircle size={16} className=\"mt-0.5\" />\r\n              <span>{error}</span>\r\n            </div>\r\n          )}\r\n\r\n          {isLoading ? (\r\n            <div className=\"flex items-center justify-center py-8\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500\"></div>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div>\r\n                <h4 className=\"text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4\">\r\n                  <CreditCard className=\"w-4 h-4\" />\r\n                  Informações do Convênio\r\n                </h4>\r\n              </div>\r\n\r\n              {!personInsurance && (\r\n                <ModuleFormGroup\r\n                  moduleColor=\"people\"\r\n                  label=\"Convênio\"\r\n                  htmlFor=\"insuranceId\"\r\n                  icon={<CreditCard size={16} />}\r\n                  required\r\n                  helpText={availableInsurances.length === 0 && !isLoading ? \"Todos os convênios já estão associados a esta pessoa.\" : \"\"}\r\n                >\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    id=\"insuranceId\"\r\n                    name=\"insuranceId\"\r\n                    value={formData.insuranceId}\r\n                    onChange={handleChange}\r\n                    required\r\n                    disabled={isSubmitting}\r\n                    placeholder=\"Selecione um convênio\"\r\n                  >\r\n                    {availableInsurances.length === 0 ? (\r\n                      <option disabled>Nenhum convênio disponível</option>\r\n                    ) : (\r\n                      availableInsurances.map(insurance => (\r\n                        <option key={insurance.id} value={insurance.id}>\r\n                          {insurance.name}\r\n                        </option>\r\n                      ))\r\n                    )}\r\n                  </ModuleSelect>\r\n                </ModuleFormGroup>\r\n              )}\r\n\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Número da Carteirinha\"\r\n                htmlFor=\"policyNumber\"\r\n                icon={<CreditCard size={16} />}\r\n              >\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  id=\"policyNumber\"\r\n                  name=\"policyNumber\"\r\n                  value={formData.policyNumber}\r\n                  onChange={handleChange}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Validade\"\r\n                htmlFor=\"validUntil\"\r\n                icon={<Calendar size={16} />}\r\n              >\r\n                <div className=\"relative\">\r\n                  <ModuleInput\r\n                    moduleColor=\"people\"\r\n                    type=\"date\"\r\n                    id=\"validUntil\"\r\n                    name=\"validUntil\"\r\n                    value={formData.validUntil}\r\n                    onChange={handleChange}\r\n                  />\r\n                  <Calendar className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 dark:text-neutral-500 pointer-events-none\" size={18} />\r\n                </div>\r\n              </ModuleFormGroup>\r\n\r\n              <ModuleFormGroup\r\n                moduleColor=\"people\"\r\n                label=\"Observações\"\r\n                htmlFor=\"notes\"\r\n                icon={<FileText size={16} />}\r\n              >\r\n                <ModuleTextarea\r\n                  moduleColor=\"people\"\r\n                  id=\"notes\"\r\n                  name=\"notes\"\r\n                  value={formData.notes}\r\n                  onChange={handleChange}\r\n                  rows={3}\r\n                />\r\n              </ModuleFormGroup>\r\n\r\n            </>\r\n          )}\r\n        </form>\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default PersonInsuranceFormModal;"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;AAFA;AAAA;AADA;AAAA;AACA;AAAA;AAAA;AADA;AAAA;AACA;;;;;;;AAIA,MAAM,2BAA2B,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,KAAK,EAAE,kBAAkB,EAAE;IACjI,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,aAAa;QACb,cAAc;QACd,YAAY;QACZ,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACjE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,aAAa;YACb,SAAS;YACT,IAAI;gBACF,qDAAqD;gBACrD,MAAM,iBAAiB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,aAAa;gBAC5D,iBAAiB;gBAEjB,IAAI,CAAC,cAAc,UAAU;oBAC3B,4EAA4E;oBAC5E,MAAM,uBAAuB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC;oBAC1E,oBAAoB;oBAEpB,0EAA0E;oBAC1E,0BAA0B,gBAAgB;gBAC5C,OAAO;oBACL,2DAA2D;oBAC3D,uBAAuB;oBACvB,oBAAoB,EAAE;gBACxB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,SAAS;YACX,SAAU;gBACR,aAAa;YACf;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAQ;QAAU;KAAW;IAEjC,MAAM,4BAA4B,CAAC,QAAQ;QACzC,sCAAsC;QACtC,MAAM,qBAAqB,UAAU,GAAG,CAAC,CAAA;YACvC,IAAI,GAAG,WAAW,EAAE,OAAO,GAAG,WAAW;YACzC,IAAI,GAAG,SAAS,IAAI,GAAG,SAAS,CAAC,EAAE,EAAE,OAAO,GAAG,SAAS,CAAC,EAAE;YAC3D,OAAO,GAAG,EAAE;QACd;QAEA,2DAA2D;QAC3D,MAAM,YAAY,OAAO,MAAM,CAAC,CAAA,YAC9B,CAAC,mBAAmB,QAAQ,CAAC,UAAU,EAAE;QAG3C,uBAAuB;IACzB;IAEA,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,IAAI,cAAc,gBAAgB,WAAW,IAC5B,CAAC,gBAAgB,SAAS,GAAG,gBAAgB,SAAS,CAAC,EAAE,GAAG,gBAAgB,EAAE;YAE/F,IAAI,sBAAsB;YAC1B,IAAI;gBACF,IAAI,gBAAgB,UAAU,EAAE;oBAC9B,sBAAsB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,gBAAgB,UAAU,GAAG;gBACrE;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;YAEA,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc,gBAAgB,YAAY,IAAI;gBAC9C,YAAY;gBACZ,OAAO,gBAAgB,KAAK,IAAI;YAClC;QACF,OAAO;YACL,YAAY;gBACV,UAAU;gBACV,aAAa;gBACb,cAAc;gBACd,YAAY;gBACZ,OAAO;YACT;QACF;IACF,GAAG;QAAC;QAAiB;KAAS;IAE9B,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAChB,SAAS;QAET,IAAI;YACF,qDAAqD;YACrD,MAAM,sBAAsB,SAAS,UAAU,GAC3C,IAAI,KAAK,SAAS,UAAU,GAAG,cAAc,WAAW,KACxD;YAEJ,qBAAqB;YACrB,MAAM,UAAU;gBACd,UAAU,SAAS,QAAQ;gBAC3B,aAAa,SAAS,WAAW;gBACjC,cAAc,SAAS,YAAY;gBACnC,YAAY;gBACZ,OAAO,SAAS,KAAK;YACvB;YAEA,IAAI,YAAY;gBACd,kDAAkD;gBAClD,IAAI,oBAAoB;oBACtB,gDAAgD;oBAChD,MAAM,oBAAoB,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,SAAS,WAAW;oBAEnF,mBAAmB;wBACjB,GAAG,OAAO;wBACV,MAAM,mBAAmB,QAAQ;wBACjC,QAAQ;oBACV;oBAEA,0BAA0B;oBAC1B,IAAI,OAAO,SAAS,EAAE;wBACpB,OAAO,SAAS,CAAC;4BACf,MAAM;4BACN,SAAS;wBACX;oBACF;oBAEA,iBAAiB;oBACjB;gBACF;YACF,OAAO,IAAI,iBAAiB;gBAC1B,iBAAiB;gBACjB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,qBAAqB,CAAC,UAAU,SAAS,WAAW,EAAE;oBAC5E,cAAc,SAAS,YAAY;oBACnC,YAAY;oBACZ,OAAO,SAAS,KAAK;gBACvB;gBAEA,6BAA6B;gBAC7B;gBAEA,0BAA0B;gBAC1B,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;YACF,OAAO;gBACL,wBAAwB;gBACxB,MAAM,gKAAA,CAAA,oBAAiB,CAAC,kBAAkB,CAAC;gBAE3C,6BAA6B;gBAC7B;gBAEA,0BAA0B;gBAC1B,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YACpD,SAAS;YAET,qCAAqC;YACrC,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS;gBACX;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,mDAAmD;IACnD,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY,cAAc,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QACvD,OAAO,YAAY,UAAU,IAAI,GAAG;IACtC;IAEA,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,SAAS;gBACT,UAAU;0BACX;;;;;;0BAID,8OAAC,gLAAA,CAAA,cAAW;gBACV,SAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,MAAK;gBACL,WAAW;gBACX,UAAU,gBAAiB,CAAC,mBAAmB,CAAC,SAAS,WAAW;0BAEnE,kBAAkB,cAAc;;;;;;;;;;;;IAKvC,qBACE,8OAAC,gLAAA,CAAA,cAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,kBAAkB,CAAC,OAAO,EAAE,iBAAiB,SAAS,WAAW,GAAG,GAAG;QAC9E,oBAAM,8OAAC,kNAAA,CAAA,aAAU;YAAC,MAAM;;;;;;QACxB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;kBAER,cAAA,8OAAC;YAAK,IAAG;YAAwB,UAAU;YAAc,WAAU;;gBAC9D,uBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oNAAA,CAAA,cAAW;4BAAC,MAAM;4BAAI,WAAU;;;;;;sCACjC,8OAAC;sCAAM;;;;;;;;;;;;gBAIV,0BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;yCAGjB;;sCACE,8OAAC;sCACC,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;wBAKrC,CAAC,iCACA,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;4BACxB,QAAQ;4BACR,UAAU,oBAAoB,MAAM,KAAK,KAAK,CAAC,YAAY,0DAA0D;sCAErH,cAAA,8OAAC,kLAAA,CAAA,eAAY;gCACX,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,QAAQ;gCACR,UAAU;gCACV,aAAY;0CAEX,oBAAoB,MAAM,KAAK,kBAC9B,8OAAC;oCAAO,QAAQ;8CAAC;;;;;2CAEjB,oBAAoB,GAAG,CAAC,CAAA,0BACtB,8OAAC;wCAA0B,OAAO,UAAU,EAAE;kDAC3C,UAAU,IAAI;uCADJ,UAAU,EAAE;;;;;;;;;;;;;;;sCASnC,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,kNAAA,CAAA,aAAU;gCAAC,MAAM;;;;;;sCAExB,cAAA,8OAAC,gLAAA,CAAA,cAAW;gCACV,aAAY;gCACZ,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,YAAY;gCAC5B,UAAU;;;;;;;;;;;sCAId,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;sCAEtB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gLAAA,CAAA,cAAW;wCACV,aAAY;wCACZ,MAAK;wCACL,IAAG;wCACH,MAAK;wCACL,OAAO,SAAS,UAAU;wCAC1B,UAAU;;;;;;kDAEZ,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;wCAAiH,MAAM;;;;;;;;;;;;;;;;;sCAI/I,8OAAC,wLAAA,CAAA,kBAAe;4BACd,aAAY;4BACZ,OAAM;4BACN,SAAQ;4BACR,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,MAAM;;;;;;sCAEtB,cAAA,8OAAC,sLAAA,CAAA,iBAAc;gCACb,aAAY;gCACZ,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,MAAM;;;;;;;;;;;;;;;;;;;;;;;;AASxB;uCAEe"}}, {"offset": {"line": 6899, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6905, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/people/PersonInsurancesTab.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\r\nimport { Plus, Edit, Trash, Calendar, CreditCard, AlertCircle } from \"lucide-react\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport PersonInsuranceFormModal from \"@/components/people/PersonInsuranceFormModal\";\r\n\r\nconst PersonInsurancesTab = ({ personId, onClose, isCreating = false, onAddTempInsurance, tempInsurances = [] }) => {\r\n  const [insurances, setInsurances] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n  const [formModalOpen, setFormModalOpen] = useState(false);\r\n  const [selectedInsurance, setSelectedInsurance] = useState(null);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [insuranceToDelete, setInsuranceToDelete] = useState(null);\r\n\r\n  useEffect(() => {\r\n    if (personId) {\r\n      loadInsurances();\r\n    } else if (isCreating) {\r\n      // Se estiver no modo de criação, não precisa carregar do servidor\r\n      setIsLoading(false);\r\n    }\r\n  }, [personId, isCreating]);\r\n\r\n  const loadInsurances = async () => {\r\n    if (!personId) return;\r\n\r\n    setIsLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await insurancesService.listPersonInsurances(personId);\r\n      setInsurances(data);\r\n    } catch (err) {\r\n      console.error(\"Erro ao carregar convênios:\", err);\r\n      setError(\"Não foi possível carregar os convênios da pessoa.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleAddInsurance = () => {\r\n    setSelectedInsurance(null);\r\n    setFormModalOpen(true);\r\n  };\r\n\r\n  // Função para adicionar um convênio temporário durante a criação\r\n  const handleAddTempInsurance = (insurance) => {\r\n    if (isCreating && onAddTempInsurance) {\r\n      onAddTempInsurance({\r\n        id: `temp-${Date.now()}`,\r\n        ...insurance,\r\n        isTemp: true\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleEditInsurance = (insurance) => {\r\n    setSelectedInsurance(insurance);\r\n    setFormModalOpen(true);\r\n  };\r\n\r\n  const handleDeleteInsurance = (insurance) => {\r\n    setInsuranceToDelete(insurance);\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    try {\r\n      await insurancesService.removePersonInsurance(personId, insuranceToDelete.insuranceId || insuranceToDelete.id);\r\n      loadInsurances();\r\n      setConfirmDialogOpen(false);\r\n      // Adicionar notificação de sucesso\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: \"success\",\r\n          message: `Convênio removido com sucesso`\r\n        });\r\n      }\r\n    } catch (err) {\r\n      console.error(\"Erro ao excluir convênio:\", err);\r\n      setError(\"Não foi possível excluir o convênio.\");\r\n      // Adicionar notificação de erro\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: \"error\",\r\n          message: `Erro ao remover convênio: ${err.response?.data?.message || \"Erro desconhecido\"}`\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n    try {\r\n      return format(new Date(dateString), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return \"Data inválida\";\r\n    }\r\n  };\r\n\r\n  const renderInsurancesList = () => {\r\n    // Determine which list of insurances to usar\r\n    const insuranceList = isCreating ? tempInsurances : insurances;\r\n\r\n    // Normalize insurance data structure as it could vary based on API response\r\n    const normalizedInsurances = insuranceList.map(ins => {\r\n      // If the insurance object is already normalized\r\n      if (ins.insurance) {\r\n        return {\r\n          id: ins.insurance.id,\r\n          name: ins.insurance.name,\r\n          policyNumber: ins.policyNumber,\r\n          validUntil: ins.validUntil,\r\n          notes: ins.notes,\r\n          // For deletion, we need the join record ID\r\n          insuranceId: ins.insurance.id\r\n        };\r\n      }\r\n      // If we directly get Insurance objects\r\n      else if (ins.id && ins.name) {\r\n        return {\r\n          id: ins.id,\r\n          name: ins.name,\r\n          policyNumber: ins.policyNumber,\r\n          validUntil: ins.validUntil,\r\n          notes: ins.notes,\r\n          insuranceId: ins.id\r\n        };\r\n      }\r\n      // If we get records from PersonInsurance with separate insurance properties\r\n      else if (ins.insuranceId) {\r\n        return {\r\n          id: ins.insuranceId,\r\n          name: ins.insuranceName || `Convênio ${ins.insuranceId}`,\r\n          policyNumber: ins.policyNumber,\r\n          validUntil: ins.validUntil,\r\n          notes: ins.notes,\r\n          insuranceId: ins.insuranceId\r\n        };\r\n      }\r\n      // Fallback for unexpected structure\r\n      return ins;\r\n    });\r\n\r\n    if (normalizedInsurances.length === 0) {\r\n      return (\r\n        <div className=\"bg-neutral-50 dark:bg-gray-700 p-6 rounded-lg text-center text-neutral-600 dark:text-neutral-300\">\r\n          <p>Nenhum convênio associado a esta pessoa.</p>\r\n        </div>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <div className=\"grid gap-4\">\r\n        {normalizedInsurances.map((insurance) => (\r\n          <div\r\n            key={insurance.id || insurance.insuranceId}\r\n            className=\"bg-white dark:bg-gray-700 border border-neutral-200 dark:border-gray-600 rounded-lg p-4 shadow-sm\"\r\n          >\r\n            <div className=\"flex flex-col md:flex-row md:justify-between md:items-center gap-4\">\r\n              <div className=\"space-y-1\">\r\n                <h4 className=\"font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2\">\r\n                  {insurance.name}\r\n                </h4>\r\n                <div className=\"flex flex-wrap items-center gap-x-4 gap-y-2 text-sm\">\r\n                  {insurance.policyNumber && (\r\n                    <span className=\"flex items-center gap-1 text-neutral-600 dark:text-neutral-300\">\r\n                      <CreditCard size={14} />\r\n                      <span>Carteirinha: {insurance.policyNumber}</span>\r\n                    </span>\r\n                  )}\r\n                  {insurance.validUntil && (\r\n                    <span className=\"flex items-center gap-1 text-neutral-600 dark:text-neutral-300\">\r\n                      <Calendar size={14} />\r\n                      <span>Validade: {formatDate(insurance.validUntil)}</span>\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                {insurance.notes && (\r\n                  <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">\r\n                    {insurance.notes}\r\n                  </p>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-center gap-2\">\r\n                <button\r\n                  onClick={() => handleEditInsurance(insurance)}\r\n                  className=\"p-2 hover:bg-neutral-100 dark:hover:bg-gray-600 rounded-lg text-neutral-600 dark:text-neutral-300\"\r\n                >\r\n                  <Edit size={18} />\r\n                </button>\r\n                <button\r\n                  onClick={() => handleDeleteInsurance(insurance)}\r\n                  className=\"p-2 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg text-red-600 dark:text-red-400\"\r\n                >\r\n                  <Trash size={18} />\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Verificar se há um ID de pessoa e não está no modo de criação\r\n  if (!personId && !isCreating) {\r\n    return (\r\n      <div className=\"p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <CreditCard size={24} />\r\n          <h3 className=\"text-lg font-semibold\">Convênios</h3>\r\n        </div>\r\n        <p className=\"text-center\">Salve os dados básicos da pessoa antes de adicionar convênios.</p>\r\n        <button\r\n          onClick={() => onClose()}\r\n          className=\"mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          Voltar para Informações\r\n        </button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-8\">\r\n        <div className=\"animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <div className=\"flex justify-between items-center\">\r\n        <h3 className=\"text-lg font-medium text-neutral-800 dark:text-neutral-200\">\r\n          Convênios Associados\r\n        </h3>\r\n        <button\r\n          onClick={handleAddInsurance}\r\n          className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n        >\r\n          <Plus size={16} />\r\n          <span>Adicionar Convênio</span>\r\n        </button>\r\n      </div>\r\n\r\n      {error && (\r\n        <div className=\"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start gap-2\">\r\n          <AlertCircle size={16} className=\"mt-0.5\" />\r\n          <span>{error}</span>\r\n        </div>\r\n      )}\r\n\r\n      {renderInsurancesList()}\r\n\r\n      {formModalOpen && (\r\n        <PersonInsuranceFormModal\r\n          isOpen={formModalOpen}\r\n          onClose={() => setFormModalOpen(false)}\r\n          personId={personId}\r\n          personInsurance={selectedInsurance}\r\n          isCreating={isCreating}\r\n          onAddTempInsurance={onAddTempInsurance}\r\n          onSuccess={() => {\r\n            if (!isCreating) {\r\n              loadInsurances();\r\n            }\r\n            setFormModalOpen(false);\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {confirmDialogOpen && (\r\n        <ConfirmationDialog\r\n          isOpen={confirmDialogOpen}\r\n          onClose={() => setConfirmDialogOpen(false)}\r\n          onConfirm={confirmDelete}\r\n          title=\"Remover Convênio\"\r\n          message={`Tem certeza que deseja remover o convênio ${insuranceToDelete?.name} desta pessoa?`}\r\n          variant=\"danger\"\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PersonInsurancesTab;"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;AACA;AAJA;AACA;AAFA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAOA,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,KAAK,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,EAAE;IAC7G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF,OAAO,IAAI,YAAY;YACrB,kEAAkE;YAClE,aAAa;QACf;IACF,GAAG;QAAC;QAAU;KAAW;IAEzB,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU;QAEf,aAAa;QACb,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,gKAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC;YAC1D,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;QACrB,iBAAiB;IACnB;IAEA,iEAAiE;IACjE,MAAM,yBAAyB,CAAC;QAC9B,IAAI,cAAc,oBAAoB;YACpC,mBAAmB;gBACjB,IAAI,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;gBACxB,GAAG,SAAS;gBACZ,QAAQ;YACV;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,qBAAqB;QACrB,iBAAiB;IACnB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,qBAAqB;QACrB,qBAAqB;IACvB;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,gKAAA,CAAA,oBAAiB,CAAC,qBAAqB,CAAC,UAAU,kBAAkB,WAAW,IAAI,kBAAkB,EAAE;YAC7G;YACA,qBAAqB;YACrB,mCAAmC;YACnC,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,CAAC,6BAA6B,CAAC;gBAC1C;YACF;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS;YACT,gCAAgC;YAChC,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,CAAC,0BAA0B,EAAE,IAAI,QAAQ,EAAE,MAAM,WAAW,qBAAqB;gBAC5F;YACF;QACF;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,IAAI;YACF,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,cAAc;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,uBAAuB;QAC3B,6CAA6C;QAC7C,MAAM,gBAAgB,aAAa,iBAAiB;QAEpD,4EAA4E;QAC5E,MAAM,uBAAuB,cAAc,GAAG,CAAC,CAAA;YAC7C,gDAAgD;YAChD,IAAI,IAAI,SAAS,EAAE;gBACjB,OAAO;oBACL,IAAI,IAAI,SAAS,CAAC,EAAE;oBACpB,MAAM,IAAI,SAAS,CAAC,IAAI;oBACxB,cAAc,IAAI,YAAY;oBAC9B,YAAY,IAAI,UAAU;oBAC1B,OAAO,IAAI,KAAK;oBAChB,2CAA2C;oBAC3C,aAAa,IAAI,SAAS,CAAC,EAAE;gBAC/B;YACF,OAEK,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE;gBAC3B,OAAO;oBACL,IAAI,IAAI,EAAE;oBACV,MAAM,IAAI,IAAI;oBACd,cAAc,IAAI,YAAY;oBAC9B,YAAY,IAAI,UAAU;oBAC1B,OAAO,IAAI,KAAK;oBAChB,aAAa,IAAI,EAAE;gBACrB;YACF,OAEK,IAAI,IAAI,WAAW,EAAE;gBACxB,OAAO;oBACL,IAAI,IAAI,WAAW;oBACnB,MAAM,IAAI,aAAa,IAAI,CAAC,SAAS,EAAE,IAAI,WAAW,EAAE;oBACxD,cAAc,IAAI,YAAY;oBAC9B,YAAY,IAAI,UAAU;oBAC1B,OAAO,IAAI,KAAK;oBAChB,aAAa,IAAI,WAAW;gBAC9B;YACF;YACA,oCAAoC;YACpC,OAAO;QACT;QAEA,IAAI,qBAAqB,MAAM,KAAK,GAAG;YACrC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;8BAAE;;;;;;;;;;;QAGT;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACZ,qBAAqB,GAAG,CAAC,CAAC,0BACzB,8OAAC;oBAEC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,UAAU,IAAI;;;;;;kDAEjB,8OAAC;wCAAI,WAAU;;4CACZ,UAAU,YAAY,kBACrB,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,kNAAA,CAAA,aAAU;wDAAC,MAAM;;;;;;kEAClB,8OAAC;;4DAAK;4DAAc,UAAU,YAAY;;;;;;;;;;;;;4CAG7C,UAAU,UAAU,kBACnB,8OAAC;gDAAK,WAAU;;kEACd,8OAAC,0MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;kEAChB,8OAAC;;4DAAK;4DAAW,WAAW,UAAU,UAAU;;;;;;;;;;;;;;;;;;;oCAIrD,UAAU,KAAK,kBACd,8OAAC;wCAAE,WAAU;kDACV,UAAU,KAAK;;;;;;;;;;;;0CAItB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,oBAAoB;wCACnC,WAAU;kDAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;;;;;;kDAEd,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;mBAvCd,UAAU,EAAE,IAAI,UAAU,WAAW;;;;;;;;;;IA+CpD;IAEA,gEAAgE;IAChE,IAAI,CAAC,YAAY,CAAC,YAAY;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kNAAA,CAAA,aAAU;4BAAC,MAAM;;;;;;sCAClB,8OAAC;4BAAG,WAAU;sCAAwB;;;;;;;;;;;;8BAExC,8OAAC;oBAAE,WAAU;8BAAc;;;;;;8BAC3B,8OAAC;oBACC,SAAS,IAAM;oBACf,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6D;;;;;;kCAG3E,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;0CACZ,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAIT,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,MAAM;wBAAI,WAAU;;;;;;kCACjC,8OAAC;kCAAM;;;;;;;;;;;;YAIV;YAEA,+BACC,8OAAC,uJAAA,CAAA,UAAwB;gBACvB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,UAAU;gBACV,iBAAiB;gBACjB,YAAY;gBACZ,oBAAoB;gBACpB,WAAW;oBACT,IAAI,CAAC,YAAY;wBACf;oBACF;oBACA,iBAAiB;gBACnB;;;;;;YAIH,mCACC,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,WAAW;gBACX,OAAM;gBACN,SAAS,CAAC,0CAA0C,EAAE,mBAAmB,KAAK,cAAc,CAAC;gBAC7F,SAAQ;;;;;;;;;;;;AAKlB;uCAEe"}}, {"offset": {"line": 7397, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 7403, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/components/people/PersonFormModal.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useRef } from \"react\";\r\nimport { Loader2, Shield, FileText, Users, CreditCard, Check, AlertCircle, User } from \"lucide-react\";\r\nimport ModuleModal from \"@/components/ui/ModuleModal\";\r\nimport ModalButton from \"@/components/ui/ModalButton\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { format } from \"date-fns\";\r\nimport PersonInfoTab from \"./PersonInfoTab\";\r\nimport DocumentsTab from \"./DocumentsTab\";\r\nimport ContactsTab from \"./ContactsTab\";\r\nimport PersonInsurancesTab from \"./PersonInsurancesTab\";\r\n\r\nconst PersonFormModal = ({ isOpen, onClose, person, onSuccess, initialClientId }) => {\r\n  const [activeTab, setActiveTab] = useState(\"info\"); // \"info\", \"documents\", \"contacts\", \"insurances\"\r\n  const [savedPersonId, setSavedPersonId] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [availableTabs, setAvailableTabs] = useState({\r\n    info: true,\r\n    documents: false,\r\n    contacts: false,\r\n    insurances: false\r\n  });\r\n  const [showSuccessMessage, setShowSuccessMessage] = useState(false);\r\n  const [tempDocuments, setTempDocuments] = useState([]);\r\n  const [tempContacts, setTempContacts] = useState([]);\r\n  const profileImageUploadRef = useRef(null);\r\n  const [tempInsurances, setTempInsurances] = useState([]);\r\n  const [tempProfileImage, setTempProfileImage] = useState(null);\r\n  const [formData, setFormData] = useState({\r\n    fullName: \"\",\r\n    cpf: \"\",\r\n    birthDate: \"\",\r\n    address: \"\",\r\n    neighborhood: \"\",\r\n    city: \"\",\r\n    state: \"\",\r\n    postalCode: \"\",\r\n    phone: \"\",\r\n    email: \"\",\r\n    gender: \"\",\r\n    notes: \"\",\r\n    clientId: \"\",\r\n    relationship: \"\",\r\n    useClientEmail: false,\r\n    useClientPhone: false,\r\n  });\r\n  const [errors, setErrors] = useState({});\r\n\r\n  // Fetch clients and initialize form data when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      console.log('Modal aberto, inicializando dados do formulário');\r\n      console.log('Dados da pessoa recebidos no modal:', person);\r\n      initializeFormData();\r\n    }\r\n  }, [isOpen, person, initialClientId]);\r\n\r\n  const initializeFormData = () => {\r\n    // Reset form data\r\n    let newFormData = {\r\n      fullName: \"\",\r\n      cpf: \"\",\r\n      birthDate: \"\",\r\n      address: \"\",\r\n      neighborhood: \"\",\r\n      city: \"\",\r\n      state: \"\",\r\n      postalCode: \"\",\r\n      phone: \"\",\r\n      email: \"\",\r\n      gender: \"\",\r\n      notes: \"\",\r\n      clientId: \"\",\r\n      relationship: \"\",\r\n      useClientEmail: false,\r\n      useClientPhone: false,\r\n    };\r\n\r\n    // If editing an existing person\r\n    if (person) {\r\n      let formattedCpf = \"\";\r\n      if (person.cpf) {\r\n        const cleanCpf = person.cpf.replace(/\\D/g, \"\");\r\n        formattedCpf = cleanCpf.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\r\n      }\r\n\r\n      // Formatar telefone\r\n      let formattedPhone = \"\";\r\n      if (person.phone) {\r\n        const cleanPhone = person.phone.replace(/\\D/g, \"\");\r\n        formattedPhone = cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n      }\r\n\r\n      let birthDateFormatted = \"\";\r\n      try {\r\n        if (person.birthDate) {\r\n          birthDateFormatted = format(new Date(person.birthDate), \"yyyy-MM-dd\");\r\n        }\r\n      } catch (e) {\r\n        console.error(\"Error formatting date:\", e);\r\n      }\r\n\r\n      // Formatar CEP\r\n      let formattedPostalCode = \"\";\r\n      if (person.postalCode) {\r\n        const cleanPostalCode = person.postalCode.replace(/\\D/g, \"\");\r\n        formattedPostalCode = cleanPostalCode.replace(/(\\d{5})(\\d{3})/, \"$1-$2\");\r\n      }\r\n\r\n      newFormData = {\r\n        fullName: person.fullName || \"\",\r\n        cpf: formattedCpf || \"\",\r\n        birthDate: birthDateFormatted,\r\n        address: person.address || \"\",\r\n        neighborhood: person.neighborhood || \"\",\r\n        city: person.city || \"\",\r\n        state: person.state || \"\",\r\n        postalCode: formattedPostalCode || \"\",\r\n        phone: formattedPhone || \"\",\r\n        email: person.email || \"\",\r\n        gender: person.gender || \"\",\r\n        notes: person.notes || \"\",\r\n        clientId: person.clientId || \"\",\r\n        relationship: person.relationship || \"\",\r\n        profileImageFullUrl: person.profileImageFullUrl || null,\r\n        useClientEmail: person.useClientEmail || false,\r\n        useClientPhone: person.useClientPhone || false,\r\n      };\r\n\r\n      // Log para depuração\r\n      console.log('Dados da pessoa carregados:', person);\r\n      console.log('URL da imagem de perfil:', person.profileImageFullUrl);\r\n\r\n      // Set saved person ID for document and contact tabs\r\n      setSavedPersonId(person.id);\r\n\r\n      // Enable all tabs when editing an existing person\r\n      setAvailableTabs({\r\n        info: true,\r\n        documents: true,\r\n        contacts: true,\r\n        insurances: true\r\n      });\r\n    }\r\n    // If creating a new person with a pre-selected client\r\n    else if (initialClientId) {\r\n      newFormData.clientId = initialClientId;\r\n      newFormData.relationship = \"Titular\";\r\n    }\r\n\r\n    setFormData(newFormData);\r\n    setErrors({});\r\n\r\n    // Default to info tab when opening modal\r\n    setActiveTab(\"info\");\r\n  };\r\n\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n\r\n    if (!formData.fullName) {\r\n      newErrors.fullName = \"Nome completo é obrigatório\";\r\n    }\r\n\r\n    if (formData.cpf) {\r\n      const cleanCpf = formData.cpf.replace(/\\D/g, \"\");\r\n      if (cleanCpf.length !== 11) {\r\n        newErrors.cpf = \"CPF deve ter 11 dígitos\";\r\n      }\r\n    }\r\n\r\n    // Only validate email format if an email is provided (it's not required)\r\n    if (formData.email && !/\\S+@\\S+\\.\\S+/.test(formData.email)) {\r\n      newErrors.email = \"Email inválido\";\r\n    }\r\n\r\n    if (formData.clientId && !formData.relationship) {\r\n      newErrors.relationship = \"Informe o relacionamento com o cliente\";\r\n    }\r\n\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSave = async (data) => {\r\n    console.log('Validando formulário antes de salvar');\r\n    if (!validateForm()) {\r\n      console.log('Validação falhou, não será possível salvar');\r\n      return false;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    console.log('Iniciando processo de salvamento dos dados');\r\n\r\n    try {\r\n      console.log('Preparando payload para envio');\r\n      const payload = {\r\n        fullName: data.fullName,\r\n        cpf: data.cpf ? data.cpf.replace(/\\D/g, \"\") : null,\r\n        birthDate: data.birthDate || null,\r\n        address: data.address || null,\r\n        neighborhood: data.neighborhood || null,\r\n        city: data.city || null,\r\n        state: data.state || null,\r\n        postalCode: data.postalCode || null,\r\n        phone: data.phone ? data.phone.replace(/\\D/g, \"\") : null,\r\n        email: data.email || null,\r\n        gender: data.gender || null,\r\n        notes: data.notes || null,\r\n        clientId: data.clientId || null,\r\n        relationship: data.relationship || null,\r\n        profileImageUrl: data.profileImageUrl || null,\r\n        useClientEmail: data.useClientEmail || false,\r\n        useClientPhone: data.useClientPhone || false,\r\n      };\r\n\r\n      console.log('Enviando dados para o backend:', payload);\r\n\r\n\r\n      let savedPerson;\r\n\r\n      if (person) {\r\n        // Update existing person\r\n        console.log(`Atualizando pessoa existente (ID: ${person.id})`);\r\n        savedPerson = await personsService.updatePerson(person.id, payload);\r\n        console.log('Pessoa atualizada com sucesso:', savedPerson);\r\n      } else {\r\n        // Create new person\r\n        console.log('Criando nova pessoa');\r\n        savedPerson = await personsService.createPerson(payload);\r\n        console.log('Nova pessoa criada com sucesso:', savedPerson);\r\n      }\r\n\r\n      // Set the ID so we can use it for documents and contacts tabs\r\n      if (savedPerson && savedPerson.id) {\r\n        console.log(`Definindo ID da pessoa salva: ${savedPerson.id}`);\r\n        setSavedPersonId(savedPerson.id);\r\n\r\n        // Processar dados temporários após salvar a pessoa\r\n        await processTemporaryData(savedPerson.id);\r\n      }\r\n\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Erro ao salvar pessoa:\", error);\r\n\r\n      // Handle API validation errors\r\n      if (error.response?.data?.errors) {\r\n        const apiErrors = {};\r\n        error.response.data.errors.forEach(err => {\r\n          apiErrors[err.param] = err.msg;\r\n        });\r\n        setErrors(apiErrors);\r\n      } else {\r\n        setErrors({\r\n          submit: error.response?.data?.message || \"Erro ao salvar pessoa\"\r\n        });\r\n      }\r\n\r\n      return false;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Método para processar os dados temporários após salvar a pessoa\r\n  const processTemporaryData = async (personId) => {\r\n    console.log('Processando dados temporários para a pessoa ID:', personId);\r\n\r\n    try {\r\n      // Processar foto de perfil\r\n      if (tempProfileImage) {\r\n        console.log('Processando foto de perfil temporária');\r\n        await personsService.uploadProfileImage(personId, tempProfileImage);\r\n        setTempProfileImage(null);\r\n      }\r\n\r\n      // Processar documentos temporários\r\n      if (tempDocuments.length > 0) {\r\n        console.log('Processando documentos temporários:', tempDocuments.length);\r\n        for (const doc of tempDocuments) {\r\n          await personsService.uploadDocument(personId, doc.file, doc.type);\r\n        }\r\n        setTempDocuments([]);\r\n      }\r\n\r\n      // Processar contatos temporários\r\n      if (tempContacts.length > 0) {\r\n        console.log('Processando contatos temporários:', tempContacts.length);\r\n        for (const contact of tempContacts) {\r\n          await personsService.createContact(personId, contact);\r\n        }\r\n        setTempContacts([]);\r\n      }\r\n\r\n      // Processar convênios temporários\r\n      if (tempInsurances.length > 0) {\r\n        console.log('Processando convênios temporários:', tempInsurances.length);\r\n        for (const insurance of tempInsurances) {\r\n          await personsService.addPersonInsurance(personId, insurance);\r\n        }\r\n        setTempInsurances([]);\r\n      }\r\n\r\n      console.log('Processamento de dados temporários concluído com sucesso');\r\n    } catch (error) {\r\n      console.error('Erro ao processar dados temporários:', error);\r\n      // Exibir mensagem de erro\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'error',\r\n          message: 'Erro ao processar alguns dados adicionais. Por favor, verifique e tente novamente.'\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (tab) => {\r\n    // Limpar mensagens de erro ao mudar de aba\r\n    setErrors({});\r\n    setShowSuccessMessage(false);\r\n\r\n    // Verificar se a aba está disponível\r\n    if (!availableTabs[tab]) {\r\n      // Se a aba não estiver disponível, mostrar mensagem\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'warning',\r\n          message: 'Complete a etapa atual antes de avançar para a próxima'\r\n        });\r\n      }\r\n      return;\r\n    }\r\n\r\n    // Permitir a navegação entre abas se estiverem disponíveis\r\n    setActiveTab(tab);\r\n\r\n    // Exibir uma mensagem informativa se a pessoa ainda não foi salva\r\n    if ((tab === \"documents\" || tab === \"contacts\" || tab === \"insurances\") && !savedPersonId && !person) {\r\n      console.log('Mudando para a aba', tab, 'sem ID de pessoa');\r\n    }\r\n  };\r\n\r\n  // Função para avançar para a próxima aba\r\n  const handleNextTab = async () => {\r\n    if (activeTab === \"documents\") {\r\n      // Avançar para a próxima aba - Contatos\r\n      setAvailableTabs(prev => ({\r\n        ...prev,\r\n        contacts: true\r\n      }));\r\n\r\n      setActiveTab(\"contacts\");\r\n\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'success',\r\n          message: tempDocuments.length > 0\r\n            ? `${tempDocuments.length} documento(s) adicionado(s) com sucesso`\r\n            : \"Você pode continuar sem adicionar documentos\"\r\n        });\r\n      }\r\n    } else if (activeTab === \"contacts\") {\r\n      // Avançar para a próxima aba - Convênios\r\n      setAvailableTabs(prev => ({\r\n        ...prev,\r\n        insurances: true\r\n      }));\r\n\r\n      setActiveTab(\"insurances\");\r\n\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'success',\r\n          message: tempContacts.length > 0\r\n            ? `${tempContacts.length} contato(s) adicionado(s) com sucesso`\r\n            : \"Você pode continuar sem adicionar contatos\"\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  const handlePersonInfoSubmit = async () => {\r\n    console.log('Iniciando salvamento dos dados da pessoa');\r\n\r\n    try {\r\n      // Verificar se há uma imagem para fazer upload\r\n      if (profileImageUploadRef.current && profileImageUploadRef.current.hasSelectedFile && profileImageUploadRef.current.hasSelectedFile()) {\r\n        console.log('Nova imagem detectada, fazendo upload antes de salvar');\r\n\r\n        // Obter o ID da pessoa (existente ou recém-criada)\r\n        const personId = person ? person.id : savedPersonId;\r\n\r\n        if (personId) {\r\n          try {\r\n            // Fazer o upload da imagem\r\n            const imageResponse = await profileImageUploadRef.current.uploadSelectedImage();\r\n            console.log('Upload concluído com sucesso:', imageResponse);\r\n\r\n            if (imageResponse && imageResponse.profileImageUrl) {\r\n              // Atualizar o formData com a nova URL da imagem\r\n              setFormData(prev => ({\r\n                ...prev,\r\n                profileImageUrl: imageResponse.profileImageUrl,\r\n                profileImageFullUrl: imageResponse.fullImageUrl\r\n              }));\r\n\r\n              console.log('FormData atualizado com a nova URL da imagem:', imageResponse.profileImageUrl);\r\n            }\r\n          } catch (error) {\r\n            console.error('Erro ao fazer upload da imagem:', error);\r\n          }\r\n        } else {\r\n          console.log('Pessoa ainda não tem ID, o upload será feito após salvar');\r\n        }\r\n      } else {\r\n        console.log('Nenhuma nova imagem para fazer upload');\r\n      }\r\n\r\n      // Se estiver editando uma pessoa existente\r\n      if (person || savedPersonId) {\r\n        // Salvar os dados da pessoa\r\n        const success = await handleSave(formData);\r\n        console.log('Resultado do salvamento:', success ? 'Sucesso' : 'Falha');\r\n\r\n        if (success) {\r\n          // Mostrar mensagem de sucesso\r\n          setShowSuccessMessage(true);\r\n\r\n          // Habilitar todas as abas após salvar com sucesso\r\n          setAvailableTabs({\r\n            info: true,\r\n            documents: true,\r\n            contacts: true,\r\n            insurances: true\r\n          });\r\n\r\n          // Exibir toast de sucesso\r\n          if (window.showToast) {\r\n            window.showToast({\r\n              type: 'success',\r\n              message: 'Informações salvas com sucesso. Agora você pode adicionar documentos, contatos e convênios.'\r\n            });\r\n          }\r\n\r\n          // Fechar o modal após salvar (apenas na edição)\r\n          onSuccess();\r\n        }\r\n      } else {\r\n        // Criando uma nova pessoa - fluxo de etapas\r\n        if (!validateForm()) return;\r\n\r\n        // Validar informações básicas e avançar para a próxima etapa\r\n        // Atualizar as abas disponíveis\r\n        setAvailableTabs(prev => ({\r\n          ...prev,\r\n          documents: true\r\n        }));\r\n\r\n        // Avançar para a próxima aba - Documentos\r\n        setActiveTab(\"documents\");\r\n\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: 'success',\r\n            message: 'Informações básicas validadas com sucesso'\r\n          });\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro durante o processo de salvamento:', error);\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'error',\r\n          message: error.response?.data?.message || 'Erro ao salvar informações'\r\n        });\r\n      }\r\n    }\r\n  };\r\n\r\n  // Função para finalizar e criar a pessoa\r\n  const handleFinish = async () => {\r\n    console.log('Finalizando e criando a pessoa');\r\n\r\n    try {\r\n      setIsLoading(true);\r\n\r\n      // Salvar os dados da pessoa\r\n      const success = await handleSave(formData);\r\n      console.log('Resultado do salvamento:', success ? 'Sucesso' : 'Falha');\r\n\r\n      if (success) {\r\n        // Exibir toast de sucesso\r\n        if (window.showToast) {\r\n          window.showToast({\r\n            type: 'success',\r\n            message: 'Paciente criado com sucesso!'\r\n          });\r\n        }\r\n\r\n        // Fechar o modal após criar a pessoa com sucesso\r\n        onSuccess();\r\n      }\r\n    } catch (error) {\r\n      console.error('Erro ao finalizar criação da pessoa:', error);\r\n      if (window.showToast) {\r\n        window.showToast({\r\n          type: 'error',\r\n          message: error.response?.data?.message || 'Erro ao criar paciente'\r\n        });\r\n      }\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleChange = (e) => {\r\n    const { name, value } = e.target;\r\n    console.log(`PersonFormModal.handleChange: Atualizando campo ${name} para ${value}`);\r\n\r\n    // Log do estado atual antes da atualização\r\n    console.log('Estado atual do formulário antes da atualização:', formData);\r\n\r\n    setFormData((prev) => {\r\n      const newState = { ...prev, [name]: value };\r\n      console.log('Novo estado do formulário após atualização:', newState);\r\n      return newState;\r\n    });\r\n\r\n    // Clear error when user starts typing\r\n    if (errors[name]) {\r\n      setErrors((prev) => ({ ...prev, [name]: undefined }));\r\n    }\r\n  };\r\n\r\n  // Componente de rodapé com botões\r\n  const modalFooter = (\r\n    <div className=\"flex justify-between items-center\">\r\n      <div>\r\n        {activeTab !== \"info\" && (\r\n          <ModalButton\r\n            variant=\"secondary\"\r\n            moduleColor=\"people\"\r\n            onClick={() => {\r\n              // Definir a aba anterior com base na aba atual\r\n              const prevTab = {\r\n                documents: \"info\",\r\n                contacts: \"documents\",\r\n                insurances: \"contacts\"\r\n              }[activeTab];\r\n              setActiveTab(prevTab);\r\n            }}\r\n            disabled={isLoading}\r\n          >\r\n            Voltar\r\n          </ModalButton>\r\n        )}\r\n      </div>\r\n      <div className=\"flex gap-3\">\r\n        <ModalButton\r\n          variant=\"secondary\"\r\n          moduleColor=\"people\"\r\n          onClick={onClose}\r\n          disabled={isLoading}\r\n        >\r\n          Cancelar\r\n        </ModalButton>\r\n\r\n        {person || savedPersonId ? (\r\n          // Usuário existente - botão Salvar\r\n          <ModalButton\r\n            variant=\"primary\"\r\n            moduleColor=\"people\"\r\n            onClick={handlePersonInfoSubmit}\r\n            isLoading={isLoading}\r\n          >\r\n            Salvar\r\n          </ModalButton>\r\n        ) : (\r\n          // Novo usuário - botões Continuar ou Criar\r\n          activeTab === \"info\" ? (\r\n            <ModalButton\r\n              variant=\"primary\"\r\n              moduleColor=\"people\"\r\n              onClick={handlePersonInfoSubmit}\r\n              isLoading={isLoading}\r\n            >\r\n              Continuar\r\n            </ModalButton>\r\n          ) : activeTab === \"insurances\" ? (\r\n            <ModalButton\r\n              variant=\"primary\"\r\n              moduleColor=\"people\"\r\n              onClick={handleFinish}\r\n              isLoading={isLoading}\r\n            >\r\n              Criar Paciente\r\n            </ModalButton>\r\n          ) : (\r\n            <ModalButton\r\n              variant=\"primary\"\r\n              moduleColor=\"people\"\r\n              onClick={handleNextTab}\r\n              isLoading={isLoading}\r\n            >\r\n              Continuar\r\n            </ModalButton>\r\n          )\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  return (\r\n    <ModuleModal\r\n      isOpen={isOpen}\r\n      onClose={onClose}\r\n      title={person ? \"Editar Pessoa\" : \"Novo Paciente\"}\r\n      icon={<User size={22} />}\r\n      moduleColor=\"people\"\r\n      size=\"lg\"\r\n      animateExit={true}\r\n      footer={modalFooter}\r\n    >\r\n\r\n        {/* Tabs */}\r\n        <div className=\"border-b border-neutral-200 dark:border-gray-700\">\r\n          <div className=\"flex\">\r\n            <button\r\n              onClick={() => handleTabChange(\"info\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"info\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                }`}\r\n            >\r\n              <User size={16} />\r\n              <span>Informações</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"documents\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"documents\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : availableTabs.documents\r\n                  ? \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                  : \"text-neutral-400 dark:text-gray-500 cursor-not-allowed\"\r\n                }`}\r\n            >\r\n              <FileText size={16} />\r\n              <span>Documentos</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"contacts\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"contacts\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : availableTabs.contacts\r\n                  ? \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                  : \"text-neutral-400 dark:text-gray-500 cursor-not-allowed\"\r\n                }`}\r\n            >\r\n              <Users size={16} />\r\n              <span>Contatos</span>\r\n            </button>\r\n            <button\r\n              onClick={() => handleTabChange(\"insurances\")}\r\n              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === \"insurances\"\r\n                ? \"border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400\"\r\n                : availableTabs.insurances\r\n                  ? \"text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700\"\r\n                  : \"text-neutral-400 dark:text-gray-500 cursor-not-allowed\"\r\n                }`}\r\n            >\r\n              <CreditCard size={16} />\r\n              <span>Convênios</span>\r\n            </button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"overflow-y-auto p-6\">\r\n          {errors.submit && (\r\n            <div className=\"mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded-lg\">\r\n              {errors.submit}\r\n            </div>\r\n          )}\r\n\r\n          {showSuccessMessage && (\r\n            <div className=\"mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/50 text-green-700 dark:text-green-400 rounded-lg flex items-center gap-2\">\r\n              <Check size={18} />\r\n              <span>Informações salvas com sucesso! Você pode continuar adicionando documentos, contatos e convênios.</span>\r\n            </div>\r\n          )}\r\n\r\n          {!savedPersonId && !person && activeTab !== \"info\" && (\r\n            <div className=\"mb-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex items-center gap-2\">\r\n              <AlertCircle size={18} />\r\n              <span>Salve as informações básicas antes de adicionar {activeTab === \"documents\" ? \"documentos\" : activeTab === \"contacts\" ? \"contatos\" : \"convênios\"}.</span>\r\n            </div>\r\n          )}\r\n\r\n          {activeTab === \"info\" && (\r\n            <PersonInfoTab\r\n              formData={formData}\r\n              setFormData={setFormData}\r\n              errors={errors}\r\n              isLoading={isLoading}\r\n              handleChange={handleChange}\r\n              onSubmit={handlePersonInfoSubmit}\r\n              personId={savedPersonId || person?.id}\r\n              profileImageUploadRef={profileImageUploadRef}\r\n              isCreating={!savedPersonId && !person}\r\n              onSetTempProfileImage={(file) => {\r\n                console.log('Definindo foto de perfil temporária:', file?.name);\r\n                setTempProfileImage(file);\r\n              }}\r\n              tempProfileImage={tempProfileImage}\r\n            />\r\n          )}\r\n\r\n          {activeTab === \"documents\" && (\r\n            <DocumentsTab\r\n              personId={savedPersonId || person?.id}\r\n              onClose={() => handleTabChange(\"info\")}\r\n              isCreating={!savedPersonId && !person}\r\n              onAddTempDocument={(doc) => {\r\n                console.log('Adicionando documento temporário:', doc);\r\n                setTempDocuments(prev => [...prev, doc]);\r\n              }}\r\n              tempDocuments={tempDocuments}\r\n            />\r\n          )}\r\n\r\n          {activeTab === \"contacts\" && (\r\n            <ContactsTab\r\n              personId={savedPersonId || person?.id}\r\n              onClose={() => handleTabChange(\"info\")}\r\n              isCreating={!savedPersonId && !person}\r\n              onAddTempContact={(contact) => {\r\n                console.log('Adicionando contato temporário:', contact);\r\n                setTempContacts(prev => [...prev, contact]);\r\n              }}\r\n              tempContacts={tempContacts}\r\n            />\r\n          )}\r\n\r\n          {activeTab === \"insurances\" && (\r\n            <PersonInsurancesTab\r\n              personId={savedPersonId || person?.id}\r\n              onClose={() => handleTabChange(\"info\")}\r\n              isCreating={!savedPersonId && !person}\r\n              onAddTempInsurance={(insurance) => {\r\n                console.log('Adicionando convênio temporário:', insurance);\r\n                setTempInsurances(prev => [...prev, insurance]);\r\n              }}\r\n              tempInsurances={tempInsurances}\r\n            />\r\n          )}\r\n        </div>\r\n\r\n    </ModuleModal>\r\n  );\r\n};\r\n\r\nexport default PersonFormModal;"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAJA;AAJA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;;;;;;AAaA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,gDAAgD;IACpG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,MAAM;QACN,WAAW;QACX,UAAU;QACV,YAAY;IACd;IACA,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,UAAU;QACV,KAAK;QACL,WAAW;QACX,SAAS;QACT,cAAc;QACd,MAAM;QACN,OAAO;QACP,YAAY;QACZ,OAAO;QACP,OAAO;QACP,QAAQ;QACR,OAAO;QACP,UAAU;QACV,cAAc;QACd,gBAAgB;QAChB,gBAAgB;IAClB;IACA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAEtC,0DAA0D;IAC1D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,uCAAuC;YACnD;QACF;IACF,GAAG;QAAC;QAAQ;QAAQ;KAAgB;IAEpC,MAAM,qBAAqB;QACzB,kBAAkB;QAClB,IAAI,cAAc;YAChB,UAAU;YACV,KAAK;YACL,WAAW;YACX,SAAS;YACT,cAAc;YACd,MAAM;YACN,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,QAAQ;YACR,OAAO;YACP,UAAU;YACV,cAAc;YACd,gBAAgB;YAChB,gBAAgB;QAClB;QAEA,gCAAgC;QAChC,IAAI,QAAQ;YACV,IAAI,eAAe;YACnB,IAAI,OAAO,GAAG,EAAE;gBACd,MAAM,WAAW,OAAO,GAAG,CAAC,OAAO,CAAC,OAAO;gBAC3C,eAAe,SAAS,OAAO,CAAC,gCAAgC;YAClE;YAEA,oBAAoB;YACpB,IAAI,iBAAiB;YACrB,IAAI,OAAO,KAAK,EAAE;gBAChB,MAAM,aAAa,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO;gBAC/C,iBAAiB,WAAW,OAAO,CAAC,yBAAyB;YAC/D;YAEA,IAAI,qBAAqB;YACzB,IAAI;gBACF,IAAI,OAAO,SAAS,EAAE;oBACpB,qBAAqB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,OAAO,SAAS,GAAG;gBAC1D;YACF,EAAE,OAAO,GAAG;gBACV,QAAQ,KAAK,CAAC,0BAA0B;YAC1C;YAEA,eAAe;YACf,IAAI,sBAAsB;YAC1B,IAAI,OAAO,UAAU,EAAE;gBACrB,MAAM,kBAAkB,OAAO,UAAU,CAAC,OAAO,CAAC,OAAO;gBACzD,sBAAsB,gBAAgB,OAAO,CAAC,kBAAkB;YAClE;YAEA,cAAc;gBACZ,UAAU,OAAO,QAAQ,IAAI;gBAC7B,KAAK,gBAAgB;gBACrB,WAAW;gBACX,SAAS,OAAO,OAAO,IAAI;gBAC3B,cAAc,OAAO,YAAY,IAAI;gBACrC,MAAM,OAAO,IAAI,IAAI;gBACrB,OAAO,OAAO,KAAK,IAAI;gBACvB,YAAY,uBAAuB;gBACnC,OAAO,kBAAkB;gBACzB,OAAO,OAAO,KAAK,IAAI;gBACvB,QAAQ,OAAO,MAAM,IAAI;gBACzB,OAAO,OAAO,KAAK,IAAI;gBACvB,UAAU,OAAO,QAAQ,IAAI;gBAC7B,cAAc,OAAO,YAAY,IAAI;gBACrC,qBAAqB,OAAO,mBAAmB,IAAI;gBACnD,gBAAgB,OAAO,cAAc,IAAI;gBACzC,gBAAgB,OAAO,cAAc,IAAI;YAC3C;YAEA,qBAAqB;YACrB,QAAQ,GAAG,CAAC,+BAA+B;YAC3C,QAAQ,GAAG,CAAC,4BAA4B,OAAO,mBAAmB;YAElE,oDAAoD;YACpD,iBAAiB,OAAO,EAAE;YAE1B,kDAAkD;YAClD,iBAAiB;gBACf,MAAM;gBACN,WAAW;gBACX,UAAU;gBACV,YAAY;YACd;QACF,OAEK,IAAI,iBAAiB;YACxB,YAAY,QAAQ,GAAG;YACvB,YAAY,YAAY,GAAG;QAC7B;QAEA,YAAY;QACZ,UAAU,CAAC;QAEX,yCAAyC;QACzC,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,MAAM,YAAY,CAAC;QAEnB,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,SAAS,GAAG,EAAE;YAChB,MAAM,WAAW,SAAS,GAAG,CAAC,OAAO,CAAC,OAAO;YAC7C,IAAI,SAAS,MAAM,KAAK,IAAI;gBAC1B,UAAU,GAAG,GAAG;YAClB;QACF;QAEA,yEAAyE;QACzE,IAAI,SAAS,KAAK,IAAI,CAAC,eAAe,IAAI,CAAC,SAAS,KAAK,GAAG;YAC1D,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,SAAS,QAAQ,IAAI,CAAC,SAAS,YAAY,EAAE;YAC/C,UAAU,YAAY,GAAG;QAC3B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa,OAAO;QACxB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,gBAAgB;YACnB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,aAAa;QACb,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,UAAU;gBACd,UAAU,KAAK,QAAQ;gBACvB,KAAK,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,OAAO,CAAC,OAAO,MAAM;gBAC9C,WAAW,KAAK,SAAS,IAAI;gBAC7B,SAAS,KAAK,OAAO,IAAI;gBACzB,cAAc,KAAK,YAAY,IAAI;gBACnC,MAAM,KAAK,IAAI,IAAI;gBACnB,OAAO,KAAK,KAAK,IAAI;gBACrB,YAAY,KAAK,UAAU,IAAI;gBAC/B,OAAO,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,MAAM;gBACpD,OAAO,KAAK,KAAK,IAAI;gBACrB,QAAQ,KAAK,MAAM,IAAI;gBACvB,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU,KAAK,QAAQ,IAAI;gBAC3B,cAAc,KAAK,YAAY,IAAI;gBACnC,iBAAiB,KAAK,eAAe,IAAI;gBACzC,gBAAgB,KAAK,cAAc,IAAI;gBACvC,gBAAgB,KAAK,cAAc,IAAI;YACzC;YAEA,QAAQ,GAAG,CAAC,kCAAkC;YAG9C,IAAI;YAEJ,IAAI,QAAQ;gBACV,yBAAyB;gBACzB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;gBAC7D,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,OAAO,EAAE,EAAE;gBAC3D,QAAQ,GAAG,CAAC,kCAAkC;YAChD,OAAO;gBACL,oBAAoB;gBACpB,QAAQ,GAAG,CAAC;gBACZ,cAAc,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;gBAChD,QAAQ,GAAG,CAAC,mCAAmC;YACjD;YAEA,8DAA8D;YAC9D,IAAI,eAAe,YAAY,EAAE,EAAE;gBACjC,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,YAAY,EAAE,EAAE;gBAC7D,iBAAiB,YAAY,EAAE;gBAE/B,mDAAmD;gBACnD,MAAM,qBAAqB,YAAY,EAAE;YAC3C;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YAExC,+BAA+B;YAC/B,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;gBAChC,MAAM,YAAY,CAAC;gBACnB,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;oBACjC,SAAS,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG;gBAChC;gBACA,UAAU;YACZ,OAAO;gBACL,UAAU;oBACR,QAAQ,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC3C;YACF;YAEA,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,kEAAkE;IAClE,MAAM,uBAAuB,OAAO;QAClC,QAAQ,GAAG,CAAC,mDAAmD;QAE/D,IAAI;YACF,2BAA2B;YAC3B,IAAI,kBAAkB;gBACpB,QAAQ,GAAG,CAAC;gBACZ,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU;gBAClD,oBAAoB;YACtB;YAEA,mCAAmC;YACnC,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,QAAQ,GAAG,CAAC,uCAAuC,cAAc,MAAM;gBACvE,KAAK,MAAM,OAAO,cAAe;oBAC/B,MAAM,6JAAA,CAAA,iBAAc,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,EAAE,IAAI,IAAI;gBAClE;gBACA,iBAAiB,EAAE;YACrB;YAEA,iCAAiC;YACjC,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B,QAAQ,GAAG,CAAC,qCAAqC,aAAa,MAAM;gBACpE,KAAK,MAAM,WAAW,aAAc;oBAClC,MAAM,6JAAA,CAAA,iBAAc,CAAC,aAAa,CAAC,UAAU;gBAC/C;gBACA,gBAAgB,EAAE;YACpB;YAEA,kCAAkC;YAClC,IAAI,eAAe,MAAM,GAAG,GAAG;gBAC7B,QAAQ,GAAG,CAAC,sCAAsC,eAAe,MAAM;gBACvE,KAAK,MAAM,aAAa,eAAgB;oBACtC,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,UAAU;gBACpD;gBACA,kBAAkB,EAAE;YACtB;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,0BAA0B;YAC1B,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS;gBACX;YACF;QACF;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,2CAA2C;QAC3C,UAAU,CAAC;QACX,sBAAsB;QAEtB,qCAAqC;QACrC,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE;YACvB,oDAAoD;YACpD,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS;gBACX;YACF;YACA;QACF;QAEA,2DAA2D;QAC3D,aAAa;QAEb,kEAAkE;QAClE,IAAI,CAAC,QAAQ,eAAe,QAAQ,cAAc,QAAQ,YAAY,KAAK,CAAC,iBAAiB,CAAC,QAAQ;YACpG,QAAQ,GAAG,CAAC,sBAAsB,KAAK;QACzC;IACF;IAEA,yCAAyC;IACzC,MAAM,gBAAgB;QACpB,IAAI,cAAc,aAAa;YAC7B,wCAAwC;YACxC,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,UAAU;gBACZ,CAAC;YAED,aAAa;YAEb,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,cAAc,MAAM,GAAG,IAC5B,GAAG,cAAc,MAAM,CAAC,uCAAuC,CAAC,GAChE;gBACN;YACF;QACF,OAAO,IAAI,cAAc,YAAY;YACnC,yCAAyC;YACzC,iBAAiB,CAAA,OAAQ,CAAC;oBACxB,GAAG,IAAI;oBACP,YAAY;gBACd,CAAC;YAED,aAAa;YAEb,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,aAAa,MAAM,GAAG,IAC3B,GAAG,aAAa,MAAM,CAAC,qCAAqC,CAAC,GAC7D;gBACN;YACF;QACF;IACF;IAEA,MAAM,yBAAyB;QAC7B,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,+CAA+C;YAC/C,IAAI,sBAAsB,OAAO,IAAI,sBAAsB,OAAO,CAAC,eAAe,IAAI,sBAAsB,OAAO,CAAC,eAAe,IAAI;gBACrI,QAAQ,GAAG,CAAC;gBAEZ,mDAAmD;gBACnD,MAAM,WAAW,SAAS,OAAO,EAAE,GAAG;gBAEtC,IAAI,UAAU;oBACZ,IAAI;wBACF,2BAA2B;wBAC3B,MAAM,gBAAgB,MAAM,sBAAsB,OAAO,CAAC,mBAAmB;wBAC7E,QAAQ,GAAG,CAAC,iCAAiC;wBAE7C,IAAI,iBAAiB,cAAc,eAAe,EAAE;4BAClD,gDAAgD;4BAChD,YAAY,CAAA,OAAQ,CAAC;oCACnB,GAAG,IAAI;oCACP,iBAAiB,cAAc,eAAe;oCAC9C,qBAAqB,cAAc,YAAY;gCACjD,CAAC;4BAED,QAAQ,GAAG,CAAC,iDAAiD,cAAc,eAAe;wBAC5F;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACnD;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,2CAA2C;YAC3C,IAAI,UAAU,eAAe;gBAC3B,4BAA4B;gBAC5B,MAAM,UAAU,MAAM,WAAW;gBACjC,QAAQ,GAAG,CAAC,4BAA4B,UAAU,YAAY;gBAE9D,IAAI,SAAS;oBACX,8BAA8B;oBAC9B,sBAAsB;oBAEtB,kDAAkD;oBAClD,iBAAiB;wBACf,MAAM;wBACN,WAAW;wBACX,UAAU;wBACV,YAAY;oBACd;oBAEA,0BAA0B;oBAC1B,IAAI,OAAO,SAAS,EAAE;wBACpB,OAAO,SAAS,CAAC;4BACf,MAAM;4BACN,SAAS;wBACX;oBACF;oBAEA,gDAAgD;oBAChD;gBACF;YACF,OAAO;gBACL,4CAA4C;gBAC5C,IAAI,CAAC,gBAAgB;gBAErB,6DAA6D;gBAC7D,gCAAgC;gBAChC,iBAAiB,CAAA,OAAQ,CAAC;wBACxB,GAAG,IAAI;wBACP,WAAW;oBACb,CAAC;gBAED,0CAA0C;gBAC1C,aAAa;gBAEb,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC5C;YACF;QACF;IACF;IAEA,yCAAyC;IACzC,MAAM,eAAe;QACnB,QAAQ,GAAG,CAAC;QAEZ,IAAI;YACF,aAAa;YAEb,4BAA4B;YAC5B,MAAM,UAAU,MAAM,WAAW;YACjC,QAAQ,GAAG,CAAC,4BAA4B,UAAU,YAAY;YAE9D,IAAI,SAAS;gBACX,0BAA0B;gBAC1B,IAAI,OAAO,SAAS,EAAE;oBACpB,OAAO,SAAS,CAAC;wBACf,MAAM;wBACN,SAAS;oBACX;gBACF;gBAEA,iDAAiD;gBACjD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,IAAI,OAAO,SAAS,EAAE;gBACpB,OAAO,SAAS,CAAC;oBACf,MAAM;oBACN,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW;gBAC5C;YACF;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,KAAK,MAAM,EAAE,OAAO;QAEnF,2CAA2C;QAC3C,QAAQ,GAAG,CAAC,oDAAoD;QAEhE,YAAY,CAAC;YACX,MAAM,WAAW;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM;YAC1C,QAAQ,GAAG,CAAC,+CAA+C;YAC3D,OAAO;QACT;QAEA,sCAAsC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE;YAChB,UAAU,CAAC,OAAS,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,KAAK,EAAE;gBAAU,CAAC;QACrD;IACF;IAEA,kCAAkC;IAClC,MAAM,4BACJ,8OAAC;QAAI,WAAU;;0BACb,8OAAC;0BACE,cAAc,wBACb,8OAAC,sIAAA,CAAA,UAAW;oBACV,SAAQ;oBACR,aAAY;oBACZ,SAAS;wBACP,+CAA+C;wBAC/C,MAAM,UAAU;4BACd,WAAW;4BACX,UAAU;4BACV,YAAY;wBACd,CAAC,CAAC,UAAU;wBACZ,aAAa;oBACf;oBACA,UAAU;8BACX;;;;;;;;;;;0BAKL,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,UAAU;kCACX;;;;;;oBAIA,UAAU,gBACT,mCAAmC;kCACnC,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;+BAID,2CAA2C;oBAC3C,cAAc,uBACZ,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;+BAGC,cAAc,6BAChB,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;6CAID,8OAAC,sIAAA,CAAA,UAAW;wBACV,SAAQ;wBACR,aAAY;wBACZ,SAAS;wBACT,WAAW;kCACZ;;;;;;;;;;;;;;;;;;IASX,qBACE,8OAAC,sIAAA,CAAA,UAAW;QACV,QAAQ;QACR,SAAS;QACT,OAAO,SAAS,kBAAkB;QAClC,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,MAAM;;;;;;QAClB,aAAY;QACZ,MAAK;QACL,aAAa;QACb,QAAQ;;0BAIN,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,SAChG,iGACA,+HACA;;8CAEJ,8OAAC,kMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;8CACZ,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,cAChG,iGACA,cAAc,SAAS,GACrB,gIACA,0DACF;;8CAEJ,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,MAAM;;;;;;8CAChB,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,aAChG,iGACA,cAAc,QAAQ,GACpB,gIACA,0DACF;;8CAEJ,8OAAC,oMAAA,CAAA,QAAK;oCAAC,MAAM;;;;;;8CACb,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BACC,SAAS,IAAM,gBAAgB;4BAC/B,WAAW,CAAC,wEAAwE,EAAE,cAAc,eAChG,iGACA,cAAc,UAAU,GACtB,gIACA,0DACF;;8CAEJ,8OAAC,kNAAA,CAAA,aAAU;oCAAC,MAAM;;;;;;8CAClB,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;;oBACZ,OAAO,MAAM,kBACZ,8OAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM;;;;;;oBAIjB,oCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oMAAA,CAAA,QAAK;gCAAC,MAAM;;;;;;0CACb,8OAAC;0CAAK;;;;;;;;;;;;oBAIT,CAAC,iBAAiB,CAAC,UAAU,cAAc,wBAC1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,MAAM;;;;;;0CACnB,8OAAC;;oCAAK;oCAAiD,cAAc,cAAc,eAAe,cAAc,aAAa,aAAa;oCAAY;;;;;;;;;;;;;oBAIzJ,cAAc,wBACb,8OAAC,4IAAA,CAAA,UAAa;wBACZ,UAAU;wBACV,aAAa;wBACb,QAAQ;wBACR,WAAW;wBACX,cAAc;wBACd,UAAU;wBACV,UAAU,iBAAiB,QAAQ;wBACnC,uBAAuB;wBACvB,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,uBAAuB,CAAC;4BACtB,QAAQ,GAAG,CAAC,wCAAwC,MAAM;4BAC1D,oBAAoB;wBACtB;wBACA,kBAAkB;;;;;;oBAIrB,cAAc,6BACb,8OAAC,2IAAA,CAAA,UAAY;wBACX,UAAU,iBAAiB,QAAQ;wBACnC,SAAS,IAAM,gBAAgB;wBAC/B,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,mBAAmB,CAAC;4BAClB,QAAQ,GAAG,CAAC,qCAAqC;4BACjD,iBAAiB,CAAA,OAAQ;uCAAI;oCAAM;iCAAI;wBACzC;wBACA,eAAe;;;;;;oBAIlB,cAAc,4BACb,8OAAC,0IAAA,CAAA,UAAW;wBACV,UAAU,iBAAiB,QAAQ;wBACnC,SAAS,IAAM,gBAAgB;wBAC/B,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,kBAAkB,CAAC;4BACjB,QAAQ,GAAG,CAAC,mCAAmC;4BAC/C,gBAAgB,CAAA,OAAQ;uCAAI;oCAAM;iCAAQ;wBAC5C;wBACA,cAAc;;;;;;oBAIjB,cAAc,8BACb,8OAAC,kJAAA,CAAA,UAAmB;wBAClB,UAAU,iBAAiB,QAAQ;wBACnC,SAAS,IAAM,gBAAgB;wBAC/B,YAAY,CAAC,iBAAiB,CAAC;wBAC/B,oBAAoB,CAAC;4BACnB,QAAQ,GAAG,CAAC,oCAAoC;4BAChD,kBAAkB,CAAA,OAAQ;uCAAI;oCAAM;iCAAU;wBAChD;wBACA,gBAAgB;;;;;;;;;;;;;;;;;;AAO9B;uCAEe"}}, {"offset": {"line": 8264, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8270, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/ClientsPage/ClientDetailsPage.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  ArrowLeft,\r\n  Edit,\r\n  Plus,\r\n  Trash,\r\n  Power,\r\n  User,\r\n  Mail,\r\n  Phone,\r\n  Calendar,\r\n  CreditCard,\r\n  MapPin,\r\n  UserPlus,\r\n  Shield,\r\n  CheckCircle,\r\n  XCircle,\r\n  RefreshCw,\r\n  Loader2,\r\n  Eye,\r\n} from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { clientsService } from \"@/app/modules/people/services/clientsService\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport ClientFormModal from \"@/components/people/ClientFormModal\";\r\nimport PersonFormModal from \"@/components/people/PersonFormModal\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport Link from \"next/link\";\r\n\r\nconst ClientDetailsPage = ({ clientId }) => {\r\n  const router = useRouter();\r\n  const [client, setClient] = useState(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  // Modals and dialogs state\r\n  const [clientFormOpen, setClientFormOpen] = useState(false);\r\n  const [personFormOpen, setPersonFormOpen] = useState(false);\r\n  const [selectedPerson, setSelectedPerson] = useState(null);\r\n  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);\r\n  const [confirmAction, setConfirmAction] = useState(null);\r\n\r\n  useEffect(() => {\r\n    loadClientData();\r\n  }, [clientId]);\r\n\r\n  const loadClientData = async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const data = await clientsService.getClient(clientId);\r\n      setClient(data);\r\n    } catch (err) {\r\n      console.error(\"Error fetching client details:\", err);\r\n      setError(\r\n        \"Não foi possível carregar os dados do cliente. Por favor, tente novamente.\"\r\n      );\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n\r\n    try {\r\n      return format(new Date(dateString), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return \"Data inválida\";\r\n    }\r\n  };\r\n\r\n  const formatCPF = (cpf) => {\r\n    if (!cpf) return \"N/A\";\r\n\r\n    // CPF format: 000.000.000-00\r\n    const cpfNumbers = cpf.replace(/\\D/g, \"\");\r\n    return cpfNumbers.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\r\n  };\r\n\r\n  const formatPhone = (phone) => {\r\n    if (!phone) return \"N/A\";\r\n\r\n    // Phone format: (00) 00000-0000\r\n    const phoneNumbers = phone.replace(/\\D/g, \"\");\r\n    return phoneNumbers.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n  };\r\n\r\n  const handleEditClient = () => {\r\n    setClientFormOpen(true);\r\n  };\r\n\r\n  const handleEditPerson = (person) => {\r\n    setSelectedPerson(person);\r\n    setPersonFormOpen(true);\r\n  };\r\n\r\n  const handleAddPerson = () => {\r\n    setSelectedPerson(null);\r\n    setPersonFormOpen(true);\r\n  };\r\n\r\n  const handleTogglePersonStatus = (person) => {\r\n    setSelectedPerson(person);\r\n    setConfirmAction({\r\n      type: \"toggle-person-status\",\r\n      title: `${person.active ? \"Desativar\" : \"Ativar\"} Pessoa`,\r\n      message: `Deseja ${person.active ? \"desativar\" : \"ativar\"} ${\r\n        person.fullName\r\n      }?`,\r\n    });\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const handleDeletePerson = (person) => {\r\n    setSelectedPerson(person);\r\n    setConfirmAction({\r\n      type: \"delete-person\",\r\n      title: \"Excluir Pessoa\",\r\n      message: `Deseja excluir permanentemente ${person.fullName}?`,\r\n      variant: \"danger\",\r\n    });\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const handleToggleClientStatus = () => {\r\n    setConfirmAction({\r\n      type: \"toggle-client-status\",\r\n      title: `${client.active ? \"Desativar\" : \"Ativar\"} Cliente`,\r\n      message: `Deseja ${client.active ? \"desativar\" : \"ativar\"} o cliente ${\r\n        client.login\r\n      }?`,\r\n    });\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const handleDeleteClient = () => {\r\n    setConfirmAction({\r\n      type: \"delete-client\",\r\n      title: \"Excluir Cliente\",\r\n      message: `Deseja excluir permanentemente o cliente ${client.login}?`,\r\n      variant: \"danger\",\r\n    });\r\n    setConfirmDialogOpen(true);\r\n  };\r\n\r\n  const confirmActionHandler = async () => {\r\n    try {\r\n      if (confirmAction.type === \"toggle-person-status\") {\r\n        await personsService.togglePersonStatus(selectedPerson.id);\r\n      } else if (confirmAction.type === \"delete-person\") {\r\n        await personsService.deletePerson(selectedPerson.id);\r\n      } else if (confirmAction.type === \"toggle-client-status\") {\r\n        await clientsService.toggleClientStatus(client.id);\r\n      } else if (confirmAction.type === \"delete-client\") {\r\n        await clientsService.deleteClient(client.id);\r\n        router.push(\"/dashboard/people/clients\");\r\n        return; // No need to reload data\r\n      }\r\n\r\n      // Reload data after action\r\n      loadClientData();\r\n    } catch (error) {\r\n      console.error(\"Error executing action:\", error);\r\n      setError(\"Ocorreu um erro ao executar esta ação.\");\r\n    }\r\n  };\r\n\r\n  const getGenderDisplay = (gender) => {\r\n    if (!gender) return \"Não informado\";\r\n\r\n    const genderMap = {\r\n      M: \"Masculino\",\r\n      F: \"Feminino\",\r\n      O: \"Outro\",\r\n    };\r\n\r\n    return genderMap[gender] || gender;\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center min-h-[300px]\">\r\n        <Loader2 className=\"h-8 w-8 animate-spin text-primary-500 dark:text-primary-400 mb-4\" />\r\n        <p className=\"text-neutral-600 dark:text-neutral-300\">Carregando dados do cliente...</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"bg-red-50 dark:bg-red-900/20 p-6 rounded-lg border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-300\">\r\n        <h3 className=\"text-lg font-medium mb-2\">Erro</h3>\r\n        <p>{error}</p>\r\n        <div className=\"mt-4\">\r\n          <button\r\n            onClick={() => router.push(\"/dashboard/people/clients\")}\r\n            className=\"px-4 py-2 bg-white dark:bg-gray-800 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2\"\r\n          >\r\n            <ArrowLeft size={18} />\r\n            <span>Voltar para lista de clientes</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!client) {\r\n    return (\r\n      <div className=\"bg-neutral-50 dark:bg-gray-800 p-6 rounded-lg border border-neutral-200 dark:border-gray-700 text-neutral-700 dark:text-neutral-300\">\r\n        <h3 className=\"text-lg font-medium mb-2\">Cliente não encontrado</h3>\r\n        <p>O cliente solicitado não foi encontrado.</p>\r\n        <div className=\"mt-4\">\r\n          <button\r\n            onClick={() => router.push(\"/dashboard/people/clients\")}\r\n            className=\"px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors flex items-center gap-2\"\r\n          >\r\n            <ArrowLeft size={18} />\r\n            <span>Voltar para lista de clientes</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex flex-col md:flex-row md:items-center md:justify-between gap-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <button\r\n            onClick={() => router.push(\"/dashboard/people/clients\")}\r\n            className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full\"\r\n          >\r\n            <ArrowLeft size={18} className=\"text-neutral-800 dark:text-neutral-200\" />\r\n          </button>\r\n          <h1 className=\"text-2xl font-bold text-neutral-800 dark:text-neutral-100\">\r\n            Detalhes do Cliente\r\n          </h1>\r\n        </div>\r\n\r\n        <div className=\"flex flex-wrap items-center gap-2\">\r\n          <button\r\n            onClick={handleEditClient}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n          >\r\n            <Edit size={16} />\r\n            <span>Editar Cliente</span>\r\n          </button>\r\n\r\n          <button\r\n            onClick={handleToggleClientStatus}\r\n            className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-colors ${\r\n              client.active\r\n                ? \"bg-amber-500 dark:bg-amber-600 text-white hover:bg-amber-600 dark:hover:bg-amber-700\"\r\n                : \"bg-green-500 dark:bg-green-600 text-white hover:bg-green-600 dark:hover:bg-green-700\"\r\n            }`}\r\n          >\r\n            <Power size={16} />\r\n            <span>{client.active ? \"Desativar\" : \"Ativar\"}</span>\r\n          </button>\r\n\r\n          <button\r\n            onClick={handleDeleteClient}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-red-500 dark:bg-red-600 text-white rounded-lg hover:bg-red-600 dark:hover:bg-red-700 transition-colors\"\r\n          >\r\n            <Trash size={16} />\r\n            <span>Excluir</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Client Info Card */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-lg dark:shadow-black/30 p-6 border border-neutral-100 dark:border-gray-700\">\r\n        <div className=\"flex flex-col md:flex-row md:items-center gap-6\">\r\n          <div className=\"flex-shrink-0 flex items-center justify-center w-20 h-20 bg-primary-100 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded-full\">\r\n            <User size={32} />\r\n          </div>\r\n\r\n          <div className=\"flex-1 space-y-4\">\r\n            <div>\r\n              <h2 className=\"text-2xl font-bold text-neutral-800 dark:text-neutral-100\">\r\n                {client.login}\r\n              </h2>\r\n              <div className=\"flex items-center mt-1\">\r\n                <span\r\n                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${\r\n                    client.active\r\n                      ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"\r\n                      : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {client.active ? (\r\n                    <>\r\n                      <CheckCircle size={12} />\r\n                      <span>Ativo</span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <XCircle size={12} />\r\n                      <span>Inativo</span>\r\n                    </>\r\n                  )}\r\n                </span>\r\n              </div>\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <div>\r\n                <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">Email</p>\r\n                <div className=\"flex items-center gap-2 mt-1\">\r\n                  <Mail className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                  <p className=\"text-neutral-800 dark:text-neutral-200\">{client.email}</p>\r\n                </div>\r\n              </div>\r\n\r\n              <div>\r\n                <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">Data de cadastro</p>\r\n                <div className=\"flex items-center gap-2 mt-1\">\r\n                  <Calendar className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                  <p className=\"text-neutral-800 dark:text-neutral-200\">\r\n                    {formatDate(client.createdAt)}\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Pessoas associadas */}\r\n      <div className=\"space-y-4\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h3 className=\"text-xl font-semibold text-neutral-800 dark:text-neutral-100\">Pessoas</h3>\r\n          <button\r\n            onClick={handleAddPerson}\r\n            className=\"flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n          >\r\n            <UserPlus size={16} />\r\n            <span>Adicionar Pessoa</span>\r\n          </button>\r\n        </div>\r\n\r\n        {client.persons && client.persons.length > 0 ? (\r\n          <div className=\"grid grid-cols-1 gap-4\">\r\n            {client.persons.map((person) => (\r\n              <div\r\n                key={person.id}\r\n                className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm dark:shadow-lg dark:shadow-black/30 p-6 border border-neutral-100 dark:border-gray-700\"\r\n              >\r\n                <div className=\"flex flex-col md:flex-row md:items-start gap-6\">\r\n                  <div className=\"flex-shrink-0 flex items-center justify-center w-16 h-16 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300 rounded-full relative\">\r\n                    <User size={24} />\r\n                    {person.relationship === \"Titular\" && (\r\n                      <div className=\"absolute -top-1 -right-1 bg-primary-500 dark:bg-primary-600 text-white p-1 rounded-full\">\r\n                        <Shield size={12} />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n\r\n                  <div className=\"flex-1\">\r\n                    <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4\">\r\n                      <div>\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <Link\r\n                            href={`/dashboard/people/persons/${person.id}`}\r\n                            className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-100 hover:text-primary-600 dark:hover:text-primary-400 hover:underline\"\r\n                          >\r\n                            {person.fullName}\r\n                          </Link>\r\n                          <span className=\"text-sm text-neutral-500 dark:text-neutral-400\">\r\n                            ({person.relationship || \"Sem relacionamento\"})\r\n                          </span>\r\n                        </div>\r\n\r\n                        <div className=\"flex items-center mt-1\">\r\n                          <span\r\n                            className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${\r\n                              person.active\r\n                                ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"\r\n                                : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"\r\n                            }`}\r\n                          >\r\n                            {person.active ? (\r\n                              <>\r\n                                <CheckCircle size={12} />\r\n                                <span>Ativo</span>\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <XCircle size={12} />\r\n                                <span>Inativo</span>\r\n                              </>\r\n                            )}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Link\r\n                          href={`/dashboard/people/persons/${person.id}`}\r\n                          className=\"p-2 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\r\n                          title=\"Visualizar\"\r\n                        >\r\n                          <Eye size={18} />\r\n                        </Link>\r\n                        <button\r\n                          onClick={() => handleEditPerson(person)}\r\n                          className=\"p-2 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors\"\r\n                          title=\"Editar\"\r\n                        >\r\n                          <Edit size={18} />\r\n                        </button>\r\n\r\n                        <button\r\n                          onClick={() => handleTogglePersonStatus(person)}\r\n                          className={`p-2 rounded-lg transition-colors ${\r\n                            person.active\r\n                              ? \"text-amber-500 hover:text-amber-600 hover:bg-amber-50 dark:hover:bg-amber-900/20 dark:text-amber-400 dark:hover:text-amber-300\"\r\n                              : \"text-green-500 hover:text-green-600 hover:bg-green-50 dark:hover:bg-green-900/20 dark:text-green-400 dark:hover:text-green-300\"\r\n                          }`}\r\n                          title={person.active ? \"Desativar\" : \"Ativar\"}\r\n                        >\r\n                          <Power size={18} />\r\n                        </button>\r\n\r\n                        <button\r\n                          onClick={() => handleDeletePerson(person)}\r\n                          className=\"p-2 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors\"\r\n                          title=\"Excluir\"\r\n                        >\r\n                          <Trash size={18} />\r\n                        </button>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n                      {person.cpf && (\r\n                        <div>\r\n                          <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">CPF</p>\r\n                          <div className=\"flex items-center gap-2 mt-1\">\r\n                            <CreditCard className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                            <p className=\"text-neutral-800 dark:text-neutral-200\">\r\n                              {formatCPF(person.cpf)}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {person.birthDate && (\r\n                        <div>\r\n                          <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">\r\n                            Data de Nascimento\r\n                          </p>\r\n                          <div className=\"flex items-center gap-2 mt-1\">\r\n                            <Calendar className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                            <p className=\"text-neutral-800 dark:text-neutral-200\">\r\n                              {formatDate(person.birthDate)}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {person.gender && (\r\n                        <div>\r\n                          <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">Gênero</p>\r\n                          <div className=\"flex items-center gap-2 mt-1\">\r\n                            <User className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                            <p className=\"text-neutral-800 dark:text-neutral-200\">\r\n                              {getGenderDisplay(person.gender)}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {person.email && (\r\n                        <div>\r\n                          <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">Email</p>\r\n                          <div className=\"flex items-center gap-2 mt-1\">\r\n                            <Mail className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                            <p className=\"text-neutral-800 dark:text-neutral-200\">{person.email}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {person.phone && (\r\n                        <div>\r\n                          <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">Telefone</p>\r\n                          <div className=\"flex items-center gap-2 mt-1\">\r\n                            <Phone className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                            <p className=\"text-neutral-800 dark:text-neutral-200\">\r\n                              {formatPhone(person.phone)}\r\n                            </p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {person.address && (\r\n                        <div className=\"md:col-span-2\">\r\n                          <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">Endereço</p>\r\n                          <div className=\"flex items-center gap-2 mt-1\">\r\n                            <MapPin className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500\" />\r\n                            <p className=\"text-neutral-800 dark:text-neutral-200\">{person.address}</p>\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                    {person.notes && (\r\n                      <div className=\"mt-4\">\r\n                        <p className=\"text-sm text-neutral-500 dark:text-neutral-400\">Observações</p>\r\n                        <p className=\"mt-1 text-neutral-700 dark:text-neutral-300 bg-neutral-50 dark:bg-gray-700 p-3 rounded-lg\">\r\n                          {person.notes}\r\n                        </p>\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        ) : (\r\n          <div className=\"bg-neutral-50 dark:bg-gray-700 p-6 rounded-lg border border-neutral-200 dark:border-gray-600 text-center\">\r\n            <p className=\"text-neutral-600 dark:text-neutral-300 mb-4\">\r\n              Este cliente não possui pessoas associadas.\r\n            </p>\r\n            <button\r\n              onClick={handleAddPerson}\r\n              className=\"inline-flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors\"\r\n            >\r\n              <UserPlus size={16} />\r\n              <span>Adicionar Pessoa</span>\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      {/* Modals */}\r\n      {clientFormOpen && (\r\n        <ClientFormModal\r\n          isOpen={clientFormOpen}\r\n          onClose={() => setClientFormOpen(false)}\r\n          client={client}\r\n          onSuccess={() => {\r\n            setClientFormOpen(false);\r\n            loadClientData();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {personFormOpen && (\r\n        <PersonFormModal\r\n          isOpen={personFormOpen}\r\n          onClose={() => setPersonFormOpen(false)}\r\n          person={selectedPerson}\r\n          initialClientId={clientId}\r\n          onSuccess={() => {\r\n            setPersonFormOpen(false);\r\n            loadClientData();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Confirmation Dialog */}\r\n      {confirmDialogOpen && confirmAction && (\r\n        <ConfirmationDialog\r\n          isOpen={confirmDialogOpen}\r\n          onClose={() => setConfirmDialogOpen(false)}\r\n          onConfirm={confirmActionHandler}\r\n          title={confirmAction.title}\r\n          message={confirmAction.message}\r\n          variant={confirmAction.variant || \"warning\"}\r\n          confirmText={confirmAction.confirmText || \"Confirmar\"}\r\n          cancelText={confirmAction.cancelText || \"Cancelar\"}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ClientDetailsPage;"], "names": [], "mappings": ";;;;AAEA;AAqBA;AAGA;AACA;AACA;AACA;AACA;AACA;AAPA;AACA;AAtBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;;;;;;;;;;AAiCA,MAAM,oBAAoB,CAAC,EAAE,QAAQ,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,2BAA2B;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB;QACrB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,OAAO,MAAM,6JAAA,CAAA,iBAAc,CAAC,SAAS,CAAC;YAC5C,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,SACE;QAEJ,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI;YACF,OAAO,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,cAAc;gBAAE,QAAQ,iJAAA,CAAA,OAAI;YAAC;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,IAAI,CAAC,KAAK,OAAO;QAEjB,6BAA6B;QAC7B,MAAM,aAAa,IAAI,OAAO,CAAC,OAAO;QACtC,OAAO,WAAW,OAAO,CAAC,gCAAgC;IAC5D;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO,OAAO;QAEnB,gCAAgC;QAChC,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO;QAC1C,OAAO,aAAa,OAAO,CAAC,yBAAyB;IACvD;IAEA,MAAM,mBAAmB;QACvB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,MAAM,kBAAkB;QACtB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,MAAM,2BAA2B,CAAC;QAChC,kBAAkB;QAClB,iBAAiB;YACf,MAAM;YACN,OAAO,GAAG,OAAO,MAAM,GAAG,cAAc,SAAS,OAAO,CAAC;YACzD,SAAS,CAAC,OAAO,EAAE,OAAO,MAAM,GAAG,cAAc,SAAS,CAAC,EACzD,OAAO,QAAQ,CAChB,CAAC,CAAC;QACL;QACA,qBAAqB;IACvB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS,CAAC,+BAA+B,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;YAC7D,SAAS;QACX;QACA,qBAAqB;IACvB;IAEA,MAAM,2BAA2B;QAC/B,iBAAiB;YACf,MAAM;YACN,OAAO,GAAG,OAAO,MAAM,GAAG,cAAc,SAAS,QAAQ,CAAC;YAC1D,SAAS,CAAC,OAAO,EAAE,OAAO,MAAM,GAAG,cAAc,SAAS,WAAW,EACnE,OAAO,KAAK,CACb,CAAC,CAAC;QACL;QACA,qBAAqB;IACvB;IAEA,MAAM,qBAAqB;QACzB,iBAAiB;YACf,MAAM;YACN,OAAO;YACP,SAAS,CAAC,yCAAyC,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC;YACpE,SAAS;QACX;QACA,qBAAqB;IACvB;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,IAAI,cAAc,IAAI,KAAK,wBAAwB;gBACjD,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,eAAe,EAAE;YAC3D,OAAO,IAAI,cAAc,IAAI,KAAK,iBAAiB;gBACjD,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,eAAe,EAAE;YACrD,OAAO,IAAI,cAAc,IAAI,KAAK,wBAAwB;gBACxD,MAAM,6JAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE;YACnD,OAAO,IAAI,cAAc,IAAI,KAAK,iBAAiB;gBACjD,MAAM,6JAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,OAAO,EAAE;gBAC3C,OAAO,IAAI,CAAC;gBACZ,QAAQ,yBAAyB;YACnC;YAEA,2BAA2B;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,SAAS;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,QAAQ,OAAO;QAEpB,MAAM,YAAY;YAChB,GAAG;YACH,GAAG;YACH,GAAG;QACL;QAEA,OAAO,SAAS,CAAC,OAAO,IAAI;IAC9B;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;8BACnB,8OAAC;oBAAE,WAAU;8BAAyC;;;;;;;;;;;;IAG5D;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2B;;;;;;8BACzC,8OAAC;8BAAG;;;;;;8BACJ,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;0CACjB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAA2B;;;;;;8BACzC,8OAAC;8BAAE;;;;;;8BACH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS,IAAM,OAAO,IAAI,CAAC;wBAC3B,WAAU;;0CAEV,8OAAC,gNAAA,CAAA,YAAS;gCAAC,MAAM;;;;;;0CACjB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;;;;;;IAKhB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;0CAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;0CAEjC,8OAAC;gCAAG,WAAU;0CAA4D;;;;;;;;;;;;kCAK5E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,2MAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,8OAAC;kDAAK;;;;;;;;;;;;0CAGR,8OAAC;gCACC,SAAS;gCACT,WAAW,CAAC,+DAA+D,EACzE,OAAO,MAAM,GACT,yFACA,wFACJ;;kDAEF,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;kDACb,8OAAC;kDAAM,OAAO,MAAM,GAAG,cAAc;;;;;;;;;;;;0CAGvC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,oMAAA,CAAA,QAAK;wCAAC,MAAM;;;;;;kDACb,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,MAAM;;;;;;;;;;;sCAGd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDACX,OAAO,KAAK;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAW,CAAC,6DAA6D,EACvE,OAAO,MAAM,GACT,yEACA,gEACJ;0DAED,OAAO,MAAM,iBACZ;;sEACE,8OAAC,2NAAA,CAAA,cAAW;4DAAC,MAAM;;;;;;sEACnB,8OAAC;sEAAK;;;;;;;iFAGR;;sEACE,8OAAC,4MAAA,CAAA,UAAO;4DAAC,MAAM;;;;;;sEACf,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;8CAOhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAiD;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAE,WAAU;sEAA0C,OAAO,KAAK;;;;;;;;;;;;;;;;;;sDAIvE,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAiD;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAE,WAAU;sEACV,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAC7E,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAIT,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,MAAM,GAAG,kBACzC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC,uBACnB,8OAAC;gCAEC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;;;;;;gDACX,OAAO,YAAY,KAAK,2BACvB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;wDAAC,MAAM;;;;;;;;;;;;;;;;;sDAKpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,4JAAA,CAAA,UAAI;4EACH,MAAM,CAAC,0BAA0B,EAAE,OAAO,EAAE,EAAE;4EAC9C,WAAU;sFAET,OAAO,QAAQ;;;;;;sFAElB,8OAAC;4EAAK,WAAU;;gFAAiD;gFAC7D,OAAO,YAAY,IAAI;gFAAqB;;;;;;;;;;;;;8EAIlD,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAW,CAAC,6DAA6D,EACvE,OAAO,MAAM,GACT,yEACA,gEACJ;kFAED,OAAO,MAAM,iBACZ;;8FACE,8OAAC,2NAAA,CAAA,cAAW;oFAAC,MAAM;;;;;;8FACnB,8OAAC;8FAAK;;;;;;;yGAGR;;8FACE,8OAAC,4MAAA,CAAA,UAAO;oFAAC,MAAM;;;;;;8FACf,8OAAC;8FAAK;;;;;;;;;;;;;;;;;;;;;;;;sEAOhB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,CAAC,0BAA0B,EAAE,OAAO,EAAE,EAAE;oEAC9C,WAAU;oEACV,OAAM;8EAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;wEAAC,MAAM;;;;;;;;;;;8EAEb,8OAAC;oEACC,SAAS,IAAM,iBAAiB;oEAChC,WAAU;oEACV,OAAM;8EAEN,cAAA,8OAAC,2MAAA,CAAA,OAAI;wEAAC,MAAM;;;;;;;;;;;8EAGd,8OAAC;oEACC,SAAS,IAAM,yBAAyB;oEACxC,WAAW,CAAC,iCAAiC,EAC3C,OAAO,MAAM,GACT,mIACA,kIACJ;oEACF,OAAO,OAAO,MAAM,GAAG,cAAc;8EAErC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,MAAM;;;;;;;;;;;8EAGf,8OAAC;oEACC,SAAS,IAAM,mBAAmB;oEAClC,WAAU;oEACV,OAAM;8EAEN,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;8DAKnB,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,GAAG,kBACT,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAiD;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kNAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;sFACtB,8OAAC;4EAAE,WAAU;sFACV,UAAU,OAAO,GAAG;;;;;;;;;;;;;;;;;;wDAM5B,OAAO,SAAS,kBACf,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAiD;;;;;;8EAG9D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;sFACpB,8OAAC;4EAAE,WAAU;sFACV,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;wDAMnC,OAAO,MAAM,kBACZ,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAiD;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAE,WAAU;sFACV,iBAAiB,OAAO,MAAM;;;;;;;;;;;;;;;;;;wDAMtC,OAAO,KAAK,kBACX,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAiD;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAE,WAAU;sFAA0C,OAAO,KAAK;;;;;;;;;;;;;;;;;;wDAKxE,OAAO,KAAK,kBACX,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAiD;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,8OAAC;4EAAE,WAAU;sFACV,YAAY,OAAO,KAAK;;;;;;;;;;;;;;;;;;wDAMhC,OAAO,OAAO,kBACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;8EAAiD;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;4EAAE,WAAU;sFAA0C,OAAO,OAAO;;;;;;;;;;;;;;;;;;;;;;;;gDAM5E,OAAO,KAAK,kBACX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAiD;;;;;;sEAC9D,8OAAC;4DAAE,WAAU;sEACV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;+BArKlB,OAAO,EAAE;;;;;;;;;6CA+KpB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAA8C;;;;;;0CAG3D,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,MAAM;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;YAOb,gCACC,8OAAC,8IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;gBACR,WAAW;oBACT,kBAAkB;oBAClB;gBACF;;;;;;YAIH,gCACC,8OAAC,8IAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;gBACR,iBAAiB;gBACjB,WAAW;oBACT,kBAAkB;oBAClB;gBACF;;;;;;YAKH,qBAAqB,+BACpB,8OAAC,6IAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,WAAW;gBACX,OAAO,cAAc,KAAK;gBAC1B,SAAS,cAAc,OAAO;gBAC9B,SAAS,cAAc,OAAO,IAAI;gBAClC,aAAa,cAAc,WAAW,IAAI;gBAC1C,YAAY,cAAc,UAAU,IAAI;;;;;;;;;;;;AAKlD;uCAEe"}}, {"offset": {"line": 9544, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 9550, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/dashboard/people/clients/%5Bid%5D/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useParams } from 'next/navigation';\r\nimport ClientDetailsPage from '@/app/modules/people/ClientsPage/ClientDetailsPage';\r\n\r\nexport default function ClientDetailsRoute() {\r\n  const params = useParams();\r\n  const clientId = params.id;\r\n  \r\n  return <ClientDetailsPage clientId={clientId} />;\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAE1B,qBAAO,8OAAC,mKAAA,CAAA,UAAiB;QAAC,UAAU;;;;;;AACtC"}}, {"offset": {"line": 9571, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}