'use client';

import React from 'react';
import { useChat } from '../../contexts/ChatContext';
import { X, ArrowLeft, Users } from 'lucide-react';

const ChatHeader = () => {
  const { 
    toggleChatPanel, 
    toggleChatModal, 
    isModalOpen, 
    activeConversation, 
    conversations, 
    setActiveConversation 
  } = useChat();

  // Encontrar a conversa ativa para mostrar o título
  const activeChat = activeConversation 
    ? conversations.find(c => c.id === activeConversation) 
    : null;

  const handleClose = () => {
    if (isModalOpen) {
      toggleChatModal();
    } else {
      toggleChatPanel();
    }
  };

  const handleBack = () => {
    // Voltar para a lista de conversas
    setActiveConversation(null);
  };

  return (
    <div className="chat-header bg-orange-500 text-white p-3 flex items-center justify-between rounded-t-lg">
      <div className="flex items-center">
        {activeConversation && (
          <button 
            className="back-button mr-2 p-1 hover:bg-orange-600 rounded-full transition-colors"
            onClick={handleBack}
            aria-label="Voltar para lista de conversas"
          >
            <ArrowLeft size={20} />
          </button>
        )}

        <div className="header-title font-medium">
          {activeChat ? (
            <div className="active-chat-info flex items-center">
              {activeChat.type === 'GROUP' ? (
                <div className="group-icon bg-orange-200 text-orange-700 p-1 rounded-full mr-2">
                  <Users size={20} />
                </div>
              ) : (
                <div className="user-avatar bg-orange-200 text-orange-700 w-8 h-8 rounded-full flex items-center justify-center mr-2">
                  {activeChat.participants && 
                    activeChat.participants.find(p => p.userId !== localStorage.getItem('userId'))?.user?.fullName?.charAt(0)}
                </div>
              )}
              <span className="truncate max-w-[200px]">
                {activeChat.type === 'GROUP' 
                  ? activeChat.title 
                  : activeChat.participants && 
                    activeChat.participants.find(p => p.userId !== localStorage.getItem('userId'))?.user?.fullName}
              </span>
            </div>
          ) : (
            <span>Conversas</span>
          )}
        </div>
      </div>

      <button 
        className="close-button p-1 hover:bg-orange-600 rounded-full transition-colors"
        onClick={handleClose}
        aria-label="Fechar chat"
      >
        <X size={20} />
      </button>
    </div>
  );
};

export default ChatHeader;
