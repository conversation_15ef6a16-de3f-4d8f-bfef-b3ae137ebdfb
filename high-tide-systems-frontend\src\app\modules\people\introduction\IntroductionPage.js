"use client";

import React, { useState } from "react";
import {
  Users,
  UserPlus,
  CreditCard,
  Shield,
  Info,
  Building,
  Play,
  Pause,
  ArrowRight,
  BarChart4,
  PieChart,
  LineChart,
  Activity,
  Mail,
  Phone,
  FileText,
  User,
  Calendar
} from "lucide-react";
import { ModuleHeader } from "@/components/ui";

const IntroductionPage = () => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Info size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Introdução
        </h1>
      </div>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-people-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-orange-600 to-orange-400 dark:from-orange-700 dark:to-orange-600 px-6 py-4">
          <div className="flex items-center">
            <Users className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo de Pessoas</h2>
          </div>
        </div>

        {/* Introduction text and video */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo de Pessoas do High Tide Systems. Este módulo é o centro de gerenciamento de clientes, pacientes e convênios,
                permitindo organizar todas as informações de forma eficiente.
                Aqui você encontrará todas as ferramentas necessárias para gerenciar pessoas e seus relacionamentos.
              </p>

              {/* Key features */}
              <div className="bg-orange-50 dark:bg-gray-700 rounded-lg p-4 border border-orange-200 dark:border-gray-600">
                <h3 className="font-semibold text-orange-800 dark:text-white mb-3 flex items-center">
                  <Activity className="mr-2 text-orange-600 dark:text-orange-300" size={18} />
                  Principais Recursos
                </h3>
                <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0">1</span>
                    <span>Cadastro completo de clientes e pacientes</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0">2</span>
                    <span>Gerenciamento de convênios e planos de saúde</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0">3</span>
                    <span>Controle de limites de serviços por convênio</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0">4</span>
                    <span>Relacionamento entre clientes e pacientes</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0">5</span>
                    <span>Gestão de documentos e informações de contato</span>
                  </li>
                </ul>
              </div>
            </div>

            <div>
              {/* Interactive demo/video placeholder */}
              <div className="bg-gradient-to-r from-orange-700 to-orange-500 dark:from-orange-800 dark:to-orange-600 rounded-lg overflow-hidden shadow-lg h-80 relative">
                {isVideoPlaying ? (
                  <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                    <button
                      onClick={() => setIsVideoPlaying(false)}
                      className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 rounded-full p-2 text-white transition-colors"
                    >
                      <Pause size={20} />
                    </button>

                    {/* Animation container */}
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="ai-video-content w-4/5 h-4/5 relative">
                        {/* People module overview animation */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center ai-slide" data-slide="1">
                          <div className="text-white text-xl font-bold mb-6">Módulo de Pessoas</div>
                          <div className="flex space-x-8 mb-8">
                            <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                              <div className="w-16 h-16 rounded-lg bg-orange-500/30 flex items-center justify-center mb-2">
                                <UserPlus size={32} className="text-orange-300" />
                              </div>
                              <span className="text-orange-200 text-sm">Clientes</span>
                            </div>
                            <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                              <div className="w-16 h-16 rounded-lg bg-orange-500/30 flex items-center justify-center mb-2">
                                <Users size={32} className="text-orange-300" />
                              </div>
                              <span className="text-orange-200 text-sm">Pacientes</span>
                            </div>
                            <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                              <div className="w-16 h-16 rounded-lg bg-orange-500/30 flex items-center justify-center mb-2">
                                <CreditCard size={32} className="text-orange-300" />
                              </div>
                              <span className="text-orange-200 text-sm">Convênios</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <style jsx>{`
                      .ai-video-content {
                        position: relative;
                        overflow: hidden;
                      }
                      .ai-slide {
                        animation: fadeIn 0.5s ease-in-out;
                      }
                      @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(10px); }
                        to { opacity: 1; transform: translateY(0); }
                      }
                    `}</style>
                  </div>
                ) : (
                  <div className="text-center p-6">
                    <button
                      onClick={() => setIsVideoPlaying(true)}
                      className="w-20 h-20 rounded-full bg-primary-500/20 flex items-center justify-center mx-auto mb-4 hover:bg-primary-500/30 transition-colors cursor-pointer"
                      aria-label="Iniciar demonstração"
                    >
                      <Play size={36} className="text-primary-500 ml-1" />
                    </button>
                    <p className="text-white text-sm mb-2">Clique para iniciar a demonstração interativa</p>
                    <p className="text-orange-200 text-xs">Visualize as principais funcionalidades do módulo</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-orange-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Clients section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <UserPlus className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Clientes</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie o cadastro de clientes no sistema. Mantenha informações completas
                      como dados pessoais, endereço, contatos e documentos.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro completo;
                        Gestão de documentos; Histórico de atendimentos; Relacionamento com pacientes.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <UserPlus size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Patients section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Users className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Pacientes</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Cadastre e gerencie pacientes, vinculando-os aos clientes titulares.
                      Mantenha um histórico completo de atendimentos e informações médicas.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de pacientes;
                        Vínculo com clientes; Histórico médico; Gestão de convênios.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <Users size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Insurances section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <CreditCard className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Convênios</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os convênios e planos de saúde disponíveis no sistema.
                      Configure informações como operadora, tipo de plano e cobertura.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de convênios;
                        Configuração de planos; Vinculação com pacientes; Gestão de coberturas.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <CreditCard size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Insurance Limits section */}
            <div className="bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Shield className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
                  <h3 className="font-semibold text-orange-800 dark:text-white">Limites de Convênio</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Configure os limites de serviços disponíveis para cada convênio.
                      Defina quantidades, valores e períodos de carência.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Configuração de limites;
                        Definição de carências; Controle de utilização; Alertas de limite.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
                      <Shield size={32} className="text-orange-500 dark:text-orange-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Getting started section */}
          <div className="mt-8 bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 p-6">
            <h3 className="text-lg font-semibold text-orange-800 dark:text-white mb-4 flex items-center">
              <Activity className="mr-2 text-orange-600 dark:text-orange-300" size={20} />
              Começando
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Para começar a utilizar o módulo de pessoas, recomendamos seguir estes passos:
            </p>
            <ol className="space-y-3 text-gray-600 dark:text-gray-300">
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">1</span>
                <span>Cadastre os <strong>clientes</strong> com informações completas, incluindo documentos e contatos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">2</span>
                <span>Registre os <strong>pacientes</strong> vinculados aos clientes, com seus dados pessoais e médicos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">3</span>
                <span>Configure os <strong>convênios</strong> disponíveis no sistema.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0">4</span>
                <span>Defina os <strong>limites de convênio</strong> para cada tipo de serviço oferecido.</span>
              </li>
            </ol>
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => window.location.href = '/dashboard/people/clients'}
                className="px-5 py-2.5 bg-orange-600 hover:bg-orange-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <UserPlus size={18} />
                Ir para Clientes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
