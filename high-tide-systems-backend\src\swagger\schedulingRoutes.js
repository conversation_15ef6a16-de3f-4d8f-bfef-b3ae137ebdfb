// src/swagger/schedulingRoutes.js

/**
 * @swagger
 * tags:
 *   name: Agendamentos
 *   description: Gerenciamento de agendamentos
 */

/**
 * @swagger
 * /schedulings:
 *   post:
 *     summary: Cria um novo agendamento
 *     description: |
 *       Cria um novo agendamento no sistema.
 *       O sistema verifica a disponibilidade do profissional antes de criar o agendamento.
 *       Após a criação, um email de confirmação é enviado ao cliente.
 *     tags: [Agendamentos]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - clientId
 *               - locationId
 *               - title
 *               - startDate
 *               - endDate
 *               - serviceTypeId
 *             properties:
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do profissional responsável
 *               clientId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do cliente
 *               creatorId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do usuário que está criando o agendamento (opcional, assume o usuário autenticado)
 *               locationId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do local
 *               title:
 *                 type: string
 *                 description: Título do agendamento
 *               description:
 *                 type: string
 *                 description: Descrição detalhada do agendamento (opcional)
 *               startDate:
 *                 type: string
 *                 format: date-time
 *                 description: Data e hora de início
 *               endDate:
 *                 type: string
 *                 format: date-time
 *                 description: Data e hora de término
 *               serviceTypeId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do tipo de serviço
 *               insuranceId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do convênio (opcional)
 *     responses:
 *       201:
 *         description: Agendamento criado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SchedulingResponse'
 *       400:
 *         description: Dados inválidos ou horário indisponível
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Horário indisponível"
 *                 reason:
 *                   type: string
 *                   example: "CONFLICT"
 *                 conflictData:
 *                   type: object
 *                   description: Dados do conflito (se houver)
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista agendamentos
 *     description: Retorna uma lista paginada de agendamentos. Permite filtrar por profissionais, clientes, locais e tipos de serviço.
 *     tags: [Agendamentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Número de itens por página
 *       - in: query
 *         name: providers
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             format: uuid
 *         description: IDs dos profissionais
 *         style: form
 *         explode: true
 *       - in: query
 *         name: clients
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             format: uuid
 *         description: IDs dos clientes
 *         style: form
 *         explode: true
 *       - in: query
 *         name: locations
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             format: uuid
 *         description: IDs dos locais
 *         style: form
 *         explode: true
 *       - in: query
 *         name: serviceTypes
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *             format: uuid
 *         description: IDs dos tipos de serviço
 *         style: form
 *         explode: true
 *     responses:
 *       200:
 *         description: Lista de agendamentos
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 schedulings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SchedulingResponse'
 *                 total:
 *                   type: integer
 *                   description: Total de registros
 *                 pages:
 *                   type: integer
 *                   description: Total de páginas
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /schedulings/{id}:
 *   put:
 *     summary: Atualiza um agendamento
 *     description: Atualiza os dados de um agendamento existente.
 *     tags: [Agendamentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do agendamento
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do profissional responsável
 *               clientId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do cliente
 *               creatorId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do usuário que está atualizando o agendamento
 *               locationId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do local
 *               title:
 *                 type: string
 *                 description: Título do agendamento
 *               description:
 *                 type: string
 *                 description: Descrição detalhada do agendamento
 *               startDate:
 *                 type: string
 *                 format: date-time
 *                 description: Data e hora de início
 *               endDate:
 *                 type: string
 *                 format: date-time
 *                 description: Data e hora de término
 *               serviceTypeId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do tipo de serviço
 *               insuranceId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do convênio
 *     responses:
 *       200:
 *         description: Agendamento atualizado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/SchedulingResponse'
 *       400:
 *         description: Dados inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Agendamento não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Agendamento não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove um agendamento
 *     description: Remove um agendamento do sistema.
 *     tags: [Agendamentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do agendamento
 *     responses:
 *       204:
 *         description: Agendamento removido com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Agendamento não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Agendamento não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */