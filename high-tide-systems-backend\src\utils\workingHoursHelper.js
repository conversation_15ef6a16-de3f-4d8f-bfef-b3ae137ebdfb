class WorkingHoursHelper {
  // Helper function to convert time strings to minutes
  static timeToMinutes(timeStr) {
    if (!timeStr) return null;
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  static generateDefaultWorkingHours(userId) {
    // <PERSON><PERSON> (1 = Segunda, 5 = Sexta)
    const workDays = [1, 2, 3, 4, 5];
    
    return workDays.map(dayOfWeek => ({
      userId,
      dayOfWeek,
      startTimeMinutes: this.timeToMinutes('08:00'),
      endTimeMinutes: this.timeToMinutes('18:00'),
      breakStartMinutes: this.timeToMinutes('12:00'),
      breakEndMinutes: this.timeToMinutes('13:00'),
      isActive: true
    }));
  }
}
  
module.exports = WorkingHoursHelper;