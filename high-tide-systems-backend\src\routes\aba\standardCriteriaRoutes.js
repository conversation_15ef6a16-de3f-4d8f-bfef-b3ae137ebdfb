// src/routes/aba/standardCriteriaRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { 
  StandardCriteriaController, 
  createStandardCriteriaValidation, 
  updateStandardCriteriaValidation 
} = require('../../controllers/aba/standardCriteriaController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para critérios padrão
router.post('/', createStandardCriteriaValidation, StandardCriteriaController.create);
router.get('/', StandardCriteriaController.list);
router.get('/:id', StandardCriteriaController.get);
router.put('/:id', updateStandardCriteriaValidation, StandardCriteriaController.update);
router.patch('/:id/status', StandardCriteriaController.toggleStatus);
router.delete('/:id', StandardCriteriaController.delete);

module.exports = router;
