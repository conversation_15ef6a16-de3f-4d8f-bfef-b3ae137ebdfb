"use client";

import React, { useState, useEffect } from "react";
import { Percent, Save } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleSelect, ModuleFormGroup } from "@/components/ui";

const ScoreFormModal = ({ isOpen, onClose, onSave, score }) => {
  const [formData, setFormData] = useState({
    type: "ALWAYS",
    value: "",
    description: ""
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Opções de tipo de pontuação
  const scoreTypeOptions = [
    { value: "ALWAYS", label: "Sempre (100%)" },
    { value: "FREQUENTLY", label: "Frequentemente (75%)" },
    { value: "SOMETIMES", label: "As vezes (50%)" },
    { value: "RARELY", label: "<PERSON><PERSON><PERSON> (25%)" },
    { value: "NEVER", label: "<PERSON><PERSON><PERSON> (0%)" },
    { value: "NOT_APPLICABLE", label: "Não se Aplica (-%)"}
  ];

  // Carregar dados da pontuação para edição
  useEffect(() => {
    if (score) {
      setFormData({
        type: score.type || "ALWAYS",
        value: score.value || "",
        description: score.description || ""
      });
    }
  }, [score]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value } = e.target;
    
    // Validação especial para o campo "value" (apenas números)
    if (name === "value" && value !== "") {
      const numericValue = value.replace(/[^0-9]/g, "");
      setFormData({ ...formData, [name]: numericValue });
    } else {
      setFormData({ ...formData, [name]: value });
    }
    
    // Limpar erro do campo quando ele for alterado
    if (errors[name]) {
      setErrors({ ...errors, [name]: null });
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.type) {
      newErrors.type = "O tipo de pontuação é obrigatório";
    }
    
    if (!formData.value && formData.type !== "NOT_APPLICABLE") {
      newErrors.value = "O valor é obrigatório";
    } else if (formData.value && isNaN(Number(formData.value))) {
      newErrors.value = "O valor deve ser um número";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      
      // Preparar dados para salvar
      const scoreData = {
        type: formData.type,
        value: formData.value,
        description: formData.description
      };
      
      onSave(scoreData);
    } catch (error) {
      console.error("Erro ao salvar pontuação:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para obter o label do tipo de pontuação
  const getScoreTypeLabel = (type) => {
    const option = scoreTypeOptions.find(opt => opt.value === type);
    return option ? option.label : type;
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={score ? "Editar Pontuação" : "Nova Pontuação"}
      size="md"
      moduleColor="abaplus"
      icon={<Percent size={20} />}
    >
      <div className="p-6 space-y-6">
        <ModuleFormGroup label="Tipo *" error={errors.type}>
          <ModuleSelect
            name="type"
            value={formData.type}
            onChange={handleChange}
            moduleColor="abaplus"
            error={!!errors.type}
          >
            {scoreTypeOptions.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </ModuleSelect>
        </ModuleFormGroup>

        <ModuleFormGroup 
          label="Valor *" 
          error={errors.value}
          helperText={formData.type === "NOT_APPLICABLE" ? "Não é necessário informar valor para este tipo" : ""}
        >
          <ModuleInput
            name="value"
            type="text"
            value={formData.value}
            onChange={handleChange}
            placeholder="Valor numérico"
            moduleColor="abaplus"
            error={!!errors.value}
            disabled={formData.type === "NOT_APPLICABLE"}
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Descrição">
          <ModuleInput
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Descrição da pontuação"
            moduleColor="abaplus"
          />
        </ModuleFormGroup>

        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Save size={18} />
                <span>Salvar</span>
              </>
            )}
          </button>
        </div>
      </div>
    </ModuleModal>
  );
};

export default ScoreFormModal;
