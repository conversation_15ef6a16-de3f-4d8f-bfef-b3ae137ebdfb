"use client";

import { useConstruction } from '@/contexts/ConstructionContext';

/**
 * Hook personalizado para facilitar o uso da funcionalidade "Em Construção"
 * 
 * @returns {Object} Objeto com funções para mostrar mensagens "Em Construção"
 */
export const useConstructionMessage = () => {
  const { showConstructionMessage } = useConstruction();

  /**
   * Função para adicionar a funcionalidade "Em Construção" a um elemento
   * @param {Object} options - Opções para a mensagem
   * @returns {Object} Objeto com props para adicionar ao elemento
   */
  const constructionProps = (options = {}) => {
    return {
      onClick: (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // Usar o elemento clicado como alvo para a mensagem
        const targetElement = e.currentTarget;
        const uniqueSelector = `construction-target-${Date.now()}`;
        
        // Adicionar um atributo temporário para identificar o elemento
        targetElement.setAttribute('data-construction-target', uniqueSelector);
        
        // Mostrar a mensagem
        showConstructionMessage({
          ...options,
          targetSelector: `[data-construction-target="${uniqueSelector}"]`
        });
        
        // Remover o atributo após um tempo
        setTimeout(() => {
          targetElement.removeAttribute('data-construction-target');
        }, 5000);
      }
    };
  };

  return {
    constructionProps,
    showConstructionMessage
  };
};

export default useConstructionMessage;
