"use client";

import React, { useState, useEffect } from "react";
import { X, Loader2, Shield, AlertT<PERSON>gle, Shield<PERSON>heck, UserCog } from "lucide-react";
import { api } from "@/utils/api";
import { useAuth } from "@/contexts/AuthContext";
import { userService } from "@/app/modules/admin/services/userService";

const RoleModal = ({ isOpen, onClose, user, onSuccess }) => {
  const { user: currentUser } = useAuth();
  const [selectedRole, setSelectedRole] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  // Configuração dos roles disponíveis
  const roles = [
    {
      id: "SYSTEM_ADMIN",
      name: "Administrador do Sistema",
      description: "Acesso completo a todas as funcionalidades e empresas",
      icon: ShieldCheck,
      color: "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800/50",
      requiresSystemAdmin: true
    },
    {
      id: "COMPANY_ADMIN",
      name: "Administrador da Empresa",
      description: "Acesso completo às funcionalidades dentro da empresa",
      icon: Shield,
      color: "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50",
      requiresSystemAdmin: false
    },
    {
      id: "EMPLOYEE",
      name: "Funcionário",
      description: "Acesso limitado às funcionalidades atribuídas",
      icon: UserCog,
      color: "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 border-green-200 dark:border-green-800/50",
      requiresSystemAdmin: false
    }
  ];

  useEffect(() => {
    if (user && isOpen) {
      setSelectedRole(user.role || "EMPLOYEE");
    }
  }, [user, isOpen]);

  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";
  const isCompanyAdmin = currentUser?.role === "COMPANY_ADMIN";

  const handleRoleChange = (roleId) => {
    setSelectedRole(roleId);
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError("");

    try {
      await userService.updateRole(user.id, selectedRole);

      onSuccess();
    } catch (error) {
      console.error("Erro ao atualizar role:", error);
      setError(error.response?.data?.message || "Erro ao atualizar função do usuário");
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 max-w-2xl w-full max-h-[90vh] flex flex-col z-[55]">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <div className="flex items-center gap-2">
            <UserCog className="h-5 w-5 text-primary-500 dark:text-primary-400" />
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">
              Alterar Função do Usuário
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        {/* Conteúdo */}
        <div className="overflow-y-auto p-6">
          {error && (
            <div className="mb-6 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 rounded-lg flex items-center gap-2">
              <AlertTriangle size={18} />
              <span>{error}</span>
            </div>
          )}

          <div className="mb-6">
            <h4 className="text-lg font-medium text-neutral-800 dark:text-white mb-1">
              {user?.fullName}
            </h4>
            <p className="text-sm text-neutral-600 dark:text-gray-300">
              Selecione a função deste usuário no sistema:
            </p>
          </div>

          <div className="space-y-4">
            {roles.map((role) => {
              const isSelected = selectedRole === role.id;
              const Icon = role.icon;
              const isDisabled = role.requiresSystemAdmin && !isSystemAdmin;

              return (
                <div
                  key={role.id}
                  className={`p-4 rounded-lg border ${
                    isSelected ? role.color : "border-neutral-200 dark:border-gray-700"
                  } ${isDisabled ? "opacity-70 cursor-not-allowed" : "cursor-pointer hover:border-primary-300 dark:hover:border-primary-700"}`}
                  onClick={() => {
                    if (!isDisabled) {
                      handleRoleChange(role.id);
                    }
                  }}
                >
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <input
                        type="radio"
                        checked={isSelected}
                        onChange={() => {}}
                        disabled={isDisabled}
                        className="h-5 w-5 rounded-full border-gray-300 dark:border-gray-600 text-primary-500 dark:text-primary-400 focus:ring-primary-500 dark:focus:ring-primary-400"
                      />
                    </div>

                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Icon className={`h-5 w-5 ${isSelected ? "" : "text-neutral-500 dark:text-gray-400"}`} />
                        <h5 className="font-medium text-neutral-800 dark:text-white">
                          {role.name}
                          {role.id === "SYSTEM_ADMIN" && (
                            <span className="ml-2 text-xs font-normal text-amber-600 dark:text-amber-500 bg-amber-50 dark:bg-amber-900/30 px-2 py-1 rounded">
                              Acesso Total
                            </span>
                          )}
                        </h5>
                      </div>
                      <p className="mt-1 text-sm text-neutral-600 dark:text-gray-300">
                        {role.description}
                      </p>

                      {role.requiresSystemAdmin && !isSystemAdmin && (
                        <div className="mt-2 text-xs text-amber-600 dark:text-amber-500 flex items-center gap-1">
                          <AlertTriangle size={12} />
                          <span>Apenas administradores do sistema podem conceder este acesso</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Alerta de restrições para não administradores do sistema */}
          {!isSystemAdmin && (
            <div className="mt-6 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-500 dark:text-amber-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h5 className="font-medium text-amber-800 dark:text-amber-300">Restrições de acesso</h5>
                  <p className="text-sm text-amber-700 dark:text-amber-400">
                    {isCompanyAdmin
                      ? "Como Administrador da Empresa, você só pode gerenciar usuários dentro da sua empresa e não pode criar Administradores do Sistema."
                      : "Você tem permissões limitadas para alterar funções de usuários. Algumas opções podem não estar disponíveis."}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-neutral-200 dark:border-gray-700 flex justify-end gap-3">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors flex items-center gap-2"
            disabled={isLoading || (selectedRole === user?.role)}
          >
            {isLoading ? (
              <>
                <Loader2 size={16} className="animate-spin" />
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <UserCog size={16} />
                <span>Salvar Alterações</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default RoleModal;