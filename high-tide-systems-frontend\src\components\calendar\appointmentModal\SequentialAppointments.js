import React from 'react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Copy, Clock } from 'lucide-react';

// Função auxiliar para garantir que temos um objeto Date
const ensureDate = (dateValue) => {
  if (!dateValue) return null;
  return dateValue instanceof Date ? dateValue : new Date(dateValue);
};

const SequentialAppointments = ({ formData, setFormData, lastAppointmentTime }) => {
  return (
    <div className="space-y-2">
      <h3 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2">
        <Copy className="w-4 h-4" />
        Agendamentos Sequenciais
      </h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm text-neutral-600 dark:text-neutral-400 mb-2">
            Quantidade de agendamentos (incluindo o original)
          </label>
          <div className="flex flex-col gap-3">
            <div className="flex justify-between items-center">
              <input
                type="range"
                min="1"
                max="5"
                step="1"
                value={formData.sequentialAppointments}
                onChange={(e) => setFormData({
                  ...formData,
                  sequentialAppointments: parseInt(e.target.value, 10)
                })}
                className="w-full h-2 bg-neutral-200 dark:bg-neutral-700 rounded-lg appearance-none cursor-pointer"
              />

              <span className="ml-4 px-3 py-1 bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200 font-medium rounded-full min-w-[40px] text-center">
                {formData.sequentialAppointments}x
              </span>
            </div>

            <div className="flex justify-between text-xs text-neutral-500 dark:text-neutral-400 px-1">
              <span>1x</span>
              <span>2x</span>
              <span>3x</span>
              <span>4x</span>
              <span>5x</span>
            </div>
          </div>
        </div>

        {formData.sequentialAppointments > 1 && formData.startDate && formData.endDate && (
          <div className="p-3 bg-neutral-50 dark:bg-gray-700 rounded-md text-sm text-neutral-700 dark:text-neutral-300">
            <p className="flex items-center gap-1">
              <Clock className="w-4 h-4 text-primary-500" />
              <span>
                Serão criados <strong>{formData.sequentialAppointments}</strong> agendamentos:
              </span>
            </p>
            <ul className="mt-1 ml-6 list-disc space-y-1">
              <li>
                Original: {format(ensureDate(formData.startDate), "HH:mm", { locale: ptBR })} - {format(ensureDate(formData.endDate), "HH:mm", { locale: ptBR })}
              </li>
              {formData.sequentialAppointments > 1 && (
                <li>
                  Último: {lastAppointmentTime
                    ? `${format(ensureDate(lastAppointmentTime.start), "HH:mm", { locale: ptBR })} - ${format(ensureDate(lastAppointmentTime.end), "HH:mm", { locale: ptBR })}`
                    : "Calculando..."}
                </li>
              )}
            </ul>
            <p className="mt-2 text-xs text-neutral-500 dark:text-neutral-400">
              Cada agendamento começa 1h após o término do anterior.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SequentialAppointments;