# Script de Cadastro de Localizações

Este script cadastra localizações para todas as empresas existentes no sistema, com nomes que fazem sentido com os serviços oferecidos.

## Características

- Cria entre 5 e 20 localizações para cada empresa
- Seleciona localizações específicas com base no segmento de atuação da empresa
- Distribui as localizações entre as unidades (branches) da empresa, quando existentes
- Verifica duplicidade para não criar localizações repetidas

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-locations-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed-locations.js
```

### Opção 3: Executando dentro do container Docker

```bash
docker exec -it high-tide-systems-api node prisma/seed-locations.js
```

## Categorias de Localizações

O script inclui localizações nas seguintes categorias:

1. **Odontológicas (dental)**
   - Consultórios odontológicos, salas de raio-X, salas de cirurgia, etc.

2. **Médicas (medical)**
   - Consultórios médicos, salas de exames, salas de especialidades, etc.

3. **Psicológicas (psychological)**
   - Consultórios psicológicos, salas de terapia individual e em grupo, etc.

4. **Fisioterapia (physiotherapy)**
   - Salas de fisioterapia, salas de RPG, salas de pilates, etc.

5. **Autismo e Desenvolvimento (autism)**
   - Salas de avaliação TEA, salas de terapia ABA, salas de fonoaudiologia, etc.

6. **Nutrição (nutrition)**
   - Consultórios nutricionais, salas de bioimpedância, cozinha experimental, etc.

## Mapeamento de Indústrias

O script mapeia o segmento de atuação da empresa para determinar quais categorias de localizações são mais adequadas:

- **Saúde**: localizações médicas, fisioterapia, nutrição
- **Odontologia**: localizações odontológicas
- **Psicologia**: localizações psicológicas
- **Fisioterapia**: localizações de fisioterapia
- **Nutrição**: localizações de nutrição
- **Terapia**: localizações psicológicas, fisioterapia, autismo
- **Medicina**: localizações médicas
- **Tecnologia**: localizações médicas, psicológicas (telemedicina)
- **Educação**: localizações para autismo, psicológicas
- **Bem-estar**: localizações de nutrição, fisioterapia
- **Serviços**: todos os tipos de localizações

## Observações

- O script verifica se a localização já existe para a empresa/unidade antes de criar uma nova
- Se a empresa tiver unidades (branches), as localizações serão distribuídas entre elas
- Se a empresa não tiver unidades, as localizações serão criadas diretamente para a empresa
- As localizações usam o endereço e telefone da unidade ou da empresa, conforme disponibilidade
