// src/routes/recurrenceRoutes.js

const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { RecurrenceController, createRecurrenceValidation } = require('../../controllers/scheduling/recurrenceController');

router.use(authenticate);

// Criar nova recorrência
router.post('/', createRecurrenceValidation, RecurrenceController.create);

// Listar recorrências
router.get('/', RecurrenceController.list);

// Atualizar agendamento de uma recorrência
router.put('/:id', RecurrenceController.update);

// Cancelar recorrência
router.delete('/:id', RecurrenceController.cancel);

module.exports = router;