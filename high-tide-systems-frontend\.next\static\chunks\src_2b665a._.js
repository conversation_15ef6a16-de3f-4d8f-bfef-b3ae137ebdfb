(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/src_2b665a._.js", {

"[project]/src/utils/moduleRedirection.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "getModulePageOptions": (()=>getModulePageOptions),
    "getModuleRedirectionPath": (()=>getModuleRedirectionPath),
    "getPreferredLandingPage": (()=>getPreferredLandingPage),
    "isFirstVisit": (()=>isFirstVisit),
    "markModuleAsVisited": (()=>markModuleAsVisited),
    "setPreferredLandingPage": (()=>setPreferredLandingPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-client] (ecmascript)");
"use client";
;
const isFirstVisit = (moduleId)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const visitedModules = JSON.parse(localStorage.getItem('visitedModules') || '{}');
        return !visitedModules[moduleId];
    } catch (error) {
        console.error('Erro ao verificar primeira visita:', error);
        return true;
    }
};
const markModuleAsVisited = (moduleId)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        const visitedModules = JSON.parse(localStorage.getItem('visitedModules') || '{}');
        visitedModules[moduleId] = true;
        localStorage.setItem('visitedModules', JSON.stringify(visitedModules));
    } catch (error) {
        console.error('Erro ao marcar módulo como visitado:', error);
    }
};
const getPreferredLandingPage = (moduleId)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        // Primeiro tenta obter do localStorage para evitar chamadas API desnecessárias
        const localPreferences = JSON.parse(localStorage.getItem('modulePreferences') || '{}');
        return localPreferences[moduleId] || null;
    } catch (error) {
        console.error('Erro ao obter página inicial preferida do localStorage:', error);
        return null;
    }
};
const setPreferredLandingPage = async (moduleId, pagePath)=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    try {
        // Primeiro, atualiza o localStorage para feedback imediato
        const localPreferences = JSON.parse(localStorage.getItem('modulePreferences') || '{}');
        localPreferences[moduleId] = pagePath;
        localStorage.setItem('modulePreferences', JSON.stringify(localPreferences));
        // Depois, atualiza no backend
        try {
            // Obter as preferências atuais do backend
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].get('/module-preferences');
            // Verificar o formato da resposta
            let serverPreferences;
            if (response.data && response.data.success && response.data.data) {
                // Formato com wrapper de sucesso
                serverPreferences = response.data.data;
            } else {
                // Formato direto
                serverPreferences = response.data || {};
            }
            console.log('Preferências extraídas da resposta (moduleRedirection):', serverPreferences);
            // Atualizar a preferência do módulo
            serverPreferences[moduleId] = pagePath;
            // Enviar as preferências atualizadas para o backend
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["api"].put('/module-preferences', {
                modulePreferences: serverPreferences
            });
            console.log(`Preferência de módulo ${moduleId} atualizada com sucesso no backend`);
        } catch (apiError) {
            console.error('Erro ao atualizar preferências no backend:', apiError);
        // Não falhar a operação se o backend não estiver disponível
        }
    } catch (error) {
        console.error('Erro ao definir página inicial preferida:', error);
    }
};
const getModuleRedirectionPath = (moduleId, introductionPath, defaultPath)=>{
    // Se for a primeira visita, redireciona para a introdução
    if (isFirstVisit(moduleId)) {
        markModuleAsVisited(moduleId);
        return introductionPath;
    }
    // Verifica se há uma página inicial preferida
    const preferredPage = getPreferredLandingPage(moduleId);
    if (preferredPage) {
        return preferredPage;
    }
    // Caso contrário, usa o caminho padrão
    return defaultPath;
};
const getModulePageOptions = (moduleId)=>{
    switch(moduleId){
        case 'admin':
            return [
                {
                    value: '/dashboard/admin/introduction',
                    label: 'Introdução'
                },
                {
                    value: '/dashboard/admin/dashboard',
                    label: 'Dashboard'
                },
                {
                    value: '/dashboard/admin/users',
                    label: 'Usuários'
                },
                {
                    value: '/dashboard/admin/professions',
                    label: 'Profissões'
                },
                {
                    value: '/dashboard/admin/logs',
                    label: 'Logs'
                },
                {
                    value: '/dashboard/admin/settings',
                    label: 'Configurações'
                }
            ];
        case 'scheduler':
            return [
                {
                    value: '/dashboard/scheduler/introduction',
                    label: 'Introdução'
                },
                {
                    value: '/dashboard/scheduler/calendar',
                    label: 'Calendário'
                },
                {
                    value: '/dashboard/scheduler/working-hours',
                    label: 'Horários de Trabalho'
                },
                {
                    value: '/dashboard/scheduler/service-types',
                    label: 'Tipos de Serviço'
                },
                {
                    value: '/dashboard/scheduler/locations',
                    label: 'Locais'
                },
                {
                    value: '/dashboard/scheduler/appointments-report',
                    label: 'Relatório'
                }
            ];
        case 'people':
            return [
                {
                    value: '/dashboard/people/clients',
                    label: 'Clientes'
                },
                {
                    value: '/dashboard/people/persons',
                    label: 'Pessoas'
                }
            ];
        case 'financial':
            return [
                {
                    value: '/dashboard/financial/invoices',
                    label: 'Faturas'
                },
                {
                    value: '/dashboard/financial/payments',
                    label: 'Pagamentos'
                }
            ];
        default:
            return [];
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/people/page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, k: __turbopack_refresh__, m: module, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>PeopleModuleRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$moduleRedirection$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/moduleRedirection.js [app-client] (ecmascript)");
;
var _s = __turbopack_refresh__.signature();
"use client";
;
;
;
function PeopleModuleRoute() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "PeopleModuleRoute.useEffect": ()=>{
            // Obtém o caminho de redirecionamento com base nas preferências do usuário
            const redirectPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$moduleRedirection$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getModuleRedirectionPath"])('people', '/dashboard/people/introduction', '/dashboard/people/clients');
            // Redireciona para o caminho determinado
            router.push(redirectPath);
        }
    }["PeopleModuleRoute.useEffect"], [
        router
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: "Carregando..."
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/people/page.js",
        lineNumber: 22,
        columnNumber: 10
    }, this);
}
_s(PeopleModuleRoute, "vQduR7x+OPXj6PSmJyFnf+hU7bg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = PeopleModuleRoute;
var _c;
__turbopack_refresh__.register(_c, "PeopleModuleRoute");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_refresh__.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/people/page.js [app-rsc] (ecmascript, Next.js server component, client modules)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),
}]);

//# sourceMappingURL=src_2b665a._.js.map