"use client";

import React, { useState, useEffect } from "react";
import {
  Award,
  Search,
  RefreshCw,
  Edit,
  Trash,
  Power,
  CheckCircle,
  XCircle,
  Plus
} from "lucide-react";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleSelect } from "@/components/ui";
import ModuleTable from "@/components/ui/ModuleTable";
import { standardCriteriaService } from "../services/standardCriteriaService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import StandardCriteriaFormModal from "./StandardCriteriaFormModal";

const StandardCriteriaPage = () => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();

  // Estados
  const [criteria, setCriteria] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });

  // Estados para filtros
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filters, setFilters] = useState({
    search: "",
    active: true
  });

  // Estados para modais
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [selectedCriterion, setSelectedCriterion] = useState(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [criterionToDelete, setCriterionToDelete] = useState(null);

  // Carregar critérios
  const loadCriteria = async () => {
    try {
      setIsLoading(true);
      const data = await standardCriteriaService.getStandardCriteria({
        page: pagination.page,
        limit: pagination.limit,
        search: filters.search,
        active: filters.active
      });

      setCriteria(data.items);
      setPagination({
        page: data.page,
        limit: data.limit,
        total: data.total,
        pages: data.pages
      });
    } catch (error) {
      console.error("Erro ao carregar critérios padrão:", error);
      toast_error("Não foi possível carregar os critérios padrão");
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar critérios ao montar o componente e quando os filtros mudarem
  useEffect(() => {
    loadCriteria();
  }, [pagination.page, pagination.limit, filters]);

  // Atualizar a lista
  const handleRefresh = () => {
    loadCriteria();
  };

  // Aplicar filtros
  const handleApplyFilters = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 })); // Voltar para a primeira página ao filtrar
  };

  // Limpar filtros
  const handleClearFilters = () => {
    setFilters({
      search: "",
      active: true
    });
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  // Manipular mudanças nos filtros
  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFilters(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  // Abrir modal de formulário para criar novo critério
  const handleOpenFormModal = () => {
    setSelectedCriterion(null);
    setIsFormModalOpen(true);
  };

  // Abrir modal de formulário para editar critério existente
  const handleEditCriterion = (criterion) => {
    setSelectedCriterion(criterion);
    setIsFormModalOpen(true);
  };

  // Abrir modal de confirmação para excluir critério
  const handleDeleteClick = (criterion) => {
    setCriterionToDelete(criterion);
    setIsDeleteModalOpen(true);
  };

  // Excluir critério
  const handleDeleteConfirm = async () => {
    if (!criterionToDelete) return;

    try {
      await standardCriteriaService.deleteStandardCriterion(criterionToDelete.id);
      toast_success("Critério padrão excluído com sucesso");
      loadCriteria();
    } catch (error) {
      console.error("Erro ao excluir critério padrão:", error);
      toast_error("Não foi possível excluir o critério padrão");
    } finally {
      setIsDeleteModalOpen(false);
      setCriterionToDelete(null);
    }
  };

  // Alternar status ativo/inativo
  const handleToggleStatus = async (criterion) => {
    try {
      await standardCriteriaService.toggleStandardCriterionStatus(criterion.id);
      toast_success(`Critério padrão ${criterion.active ? 'desativado' : 'ativado'} com sucesso`);
      loadCriteria();
    } catch (error) {
      console.error("Erro ao alternar status do critério padrão:", error);
      toast_error("Não foi possível alterar o status do critério padrão");
    }
  };

  // Manipular mudança de página
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  // Colunas da tabela
  const columns = [
    { id: "teachingType", header: "Tipo de Ensino", field: "teachingType" },
    { id: "acronym", header: "Sigla", field: "acronym" },
    { id: "degree", header: "Grau", field: "degree" },
    { id: "active", header: "Status", field: "active" },
    { id: "actions", header: "Ações", sortable: false }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Critérios Padrão"
        description="Gerencie os critérios padrão do módulo ABA+"
        moduleColor="abaplus"
      >
        <button
          onClick={handleRefresh}
          className="p-2 text-teal-600 hover:bg-teal-50 dark:text-teal-400 dark:hover:bg-teal-900/20 rounded-lg transition-colors"
          aria-label="Atualizar lista"
        >
          <RefreshCw size={20} />
        </button>
        <FilterButton
          isOpen={isFilterOpen}
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          moduleColor="abaplus"
        />
        <button
          onClick={handleOpenFormModal}
          className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
        >
          <Plus size={16} />
          Novo Critério
        </button>
      </ModuleHeader>

      {/* Filtros */}
      {isFilterOpen && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
          <form onSubmit={handleApplyFilters} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Buscar
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search size={16} className="text-gray-400" />
                  </div>
                  <ModuleInput
                    name="search"
                    value={filters.search}
                    onChange={handleFilterChange}
                    placeholder="Buscar por sigla"
                    moduleColor="abaplus"
                    className="pl-10"
                  />
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <ModuleSelect
                  name="active"
                  value={filters.active === "" ? "" : filters.active.toString()}
                  onChange={(e) => {
                    const value = e.target.value;
                    setFilters(prev => ({
                      ...prev,
                      active: value === "" ? "" : value === "true"
                    }));
                  }}
                  moduleColor="abaplus"
                >
                  <option value="">Todos</option>
                  <option value="true">Ativos</option>
                  <option value="false">Inativos</option>
                </ModuleSelect>
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <button
                type="button"
                onClick={handleClearFilters}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                Limpar
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors"
              >
                Aplicar Filtros
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Tabela de Critérios */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <ModuleTable
          columns={columns}
          data={criteria}
          isLoading={isLoading}
          currentPage={pagination.page}
          totalPages={pagination.pages}
          totalItems={pagination.total}
          onPageChange={handlePageChange}
          emptyMessage="Nenhum critério padrão encontrado"
          moduleColor="abaplus"
          renderRow={(criterion, index, moduleColors, visibleColumns) => (
            <tr key={criterion.id} className={moduleColors.hoverBg}>
              {visibleColumns.includes('teachingType') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {standardCriteriaService.getTeachingTypeLabel(criterion.teachingType)}
                  </div>
                </td>
              )}
              {visibleColumns.includes('acronym') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{criterion.acronym}</div>
                </td>
              )}
              {visibleColumns.includes('degree') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {standardCriteriaService.getDegreeLabel(criterion.degree)}
                  </div>
                </td>
              )}
              {visibleColumns.includes('active') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    criterion.active
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400'
                  }`}>
                    {criterion.active ? (
                      <>
                        <CheckCircle size={12} className="mr-1" />
                        Ativo
                      </>
                    ) : (
                      <>
                        <XCircle size={12} className="mr-1" />
                        Inativo
                      </>
                    )}
                  </span>
                </td>
              )}
              {visibleColumns.includes('actions') && (
                <td className="px-6 py-4 whitespace-nowrap text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <button
                      onClick={() => handleEditCriterion(criterion)}
                      className="p-1 text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20 rounded transition-colors"
                      aria-label="Editar"
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => handleToggleStatus(criterion)}
                      className={`p-1 ${
                        criterion.active
                          ? 'text-amber-600 hover:bg-amber-50 dark:text-amber-400 dark:hover:bg-amber-900/20'
                          : 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20'
                      } rounded transition-colors`}
                      aria-label={criterion.active ? "Desativar" : "Ativar"}
                    >
                      <Power size={18} />
                    </button>
                    <button
                      onClick={() => handleDeleteClick(criterion)}
                      className="p-1 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded transition-colors"
                      aria-label="Excluir"
                    >
                      <Trash size={18} />
                    </button>
                  </div>
                </td>
              )}
            </tr>
          )}
        />
      </div>

      {/* Modal de Formulário */}
      <StandardCriteriaFormModal
        isOpen={isFormModalOpen}
        onClose={() => setIsFormModalOpen(false)}
        onSuccess={handleRefresh}
        criterion={selectedCriterion}
      />

      {/* Modal de Confirmação de Exclusão */}
      <ConfirmationDialog
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Excluir Critério Padrão"
        message="Tem certeza que deseja excluir este critério padrão? Esta ação não pode ser desfeita."
        confirmButtonText="Excluir"
        cancelButtonText="Cancelar"
        type="danger"
      />
    </div>
  );
};

export default StandardCriteriaPage;
