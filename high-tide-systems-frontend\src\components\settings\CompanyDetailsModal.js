'use client';

import React, { useEffect, useState } from 'react';
import Modal from '@/components/ui/Modal';
import { Building, Globe, Mail, MapPin, Phone, X } from 'lucide-react';
import { api } from '@/utils/api';
import { companyLogoService } from '@/app/modules/admin/services/companyLogoService';

const CompanyDetailsModal = ({ isOpen, onClose, companyId }) => {
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (isOpen && companyId) {
      loadCompanyDetails();
    }
  }, [isOpen, companyId]);

  // Função para processar campos JSON que podem vir como string
  const parseJsonField = (jsonData, fieldName = 'campo') => {
    if (!jsonData) return {};

    // Se já for um objeto, retornar como está
    if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {
      return jsonData;
    }

    // Se for uma string, tentar fazer o parse
    if (typeof jsonData === 'string') {
      try {
        // Verificar se a string está vazia ou não é um JSON válido
        if (!jsonData.trim() || (!jsonData.startsWith('{') && !jsonData.startsWith('['))) {
          console.warn(`[CompanyDetailsModal] ${fieldName} não parece ser um JSON válido:`, jsonData);
          return {};
        }
        return JSON.parse(jsonData);
      } catch (error) {
        console.error(`[CompanyDetailsModal] Erro ao fazer parse do ${fieldName}:`, error);
        return {};
      }
    }

    return {};
  };

  // Alias para manter compatibilidade
  const parseSocialMedia = (data) => parseJsonField(data, 'socialMedia');
  const parseBusinessHours = (data) => parseJsonField(data, 'businessHours');

  const loadCompanyDetails = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('[CompanyDetailsModal] Buscando empresa com ID:', companyId);
      const response = await api.get(`/companies/${companyId}`);
      console.log('[CompanyDetailsModal] Resposta da API:', response.data);

      // Verificar se a empresa tem documentos/logo
      if (response.data.documents && response.data.documents.length > 0 && response.data.documents[0] && response.data.documents[0].path) {
        console.log('[CompanyDetailsModal] Documentos encontrados:', response.data.documents);
        console.log('[CompanyDetailsModal] Caminho do logo:', response.data.documents[0].path);

        // Tentar construir a URL do logo
        const logoUrl = companyLogoService.getCompanyLogoUrl(response.data.id, response.data.documents[0].path);
        console.log('[CompanyDetailsModal] URL do logo construída:', logoUrl);
      } else {
        console.log('[CompanyDetailsModal] Empresa não tem documentos/logo ou caminho inválido');
      }

      setCompany(response.data);
    } catch (error) {
      console.error('[CompanyDetailsModal] Erro ao carregar detalhes da empresa:', error);
      setError('Não foi possível carregar os detalhes da empresa. Tente novamente mais tarde.');
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Detalhes da Empresa">
      <div className="p-4">
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"></div>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500 dark:text-red-400">{error}</div>
        ) : company ? (
          <div className="space-y-6">
            <div className="flex flex-col md:flex-row gap-6">
              {/* Logo e informações principais */}
              <div className="flex-shrink-0 flex flex-col items-center">
                <div className="h-32 w-32 rounded-lg bg-neutral-100 dark:bg-gray-700 flex items-center justify-center overflow-hidden mb-4">
                  {company.documents && company.documents[0] ? (
                    <>
                      {/* Testar diferentes URLs para ver qual funciona */}
                      <img
                        src={company.documents && company.documents[0] && company.documents[0].path ? companyLogoService.getCompanyLogoUrl(company.id, company.documents[0].path) : ''}
                        alt={company.name}
                        className="h-full w-full object-contain"
                        onLoad={() => console.log('[CompanyDetailsModal] Imagem carregada com sucesso')}
                        onError={(e) => {
                          console.error('[CompanyDetailsModal] Erro ao carregar imagem:', e.target.src);
                          e.target.onerror = null;
                          e.target.style.display = 'none';
                          e.target.parentNode.innerHTML = `<div class="h-16 w-16 text-neutral-400 dark:text-gray-500"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg></div>`;
                        }}
                      />
                    </>
                  ) : (
                    <Building className="h-16 w-16 text-neutral-400 dark:text-gray-500" />
                  )}
                </div>
                <h3 className="text-lg font-medium text-neutral-800 dark:text-white text-center">
                  {company.name}
                </h3>
                {company.tradingName && (
                  <p className="text-sm text-neutral-500 dark:text-gray-400 text-center">
                    {company.tradingName}
                  </p>
                )}
                <div className="mt-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 text-xs rounded-full">
                  {company.active ? "Ativa" : "Inativa"}
                </div>
              </div>

              {/* Informações de contato */}
              <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5">
                    <Building className="h-4 w-4 text-neutral-500 dark:text-gray-400" />
                    Informações Básicas
                  </h4>
                  <div className="space-y-2">
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">CNPJ:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">
                        {company.cnpj ? company.cnpj.replace(/^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})$/, "$1.$2.$3/$4-$5") : "N/A"}
                      </span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Telefone:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{company.phone || "N/A"}</span>
                    </p>
                    {company.phone2 && (
                      <p className="text-sm flex justify-between">
                        <span className="text-neutral-500 dark:text-gray-400">Telefone 2:</span>
                        <span className="text-neutral-800 dark:text-gray-200 font-medium">{company.phone2}</span>
                      </p>
                    )}
                    {company.website && (
                      <p className="text-sm flex justify-between">
                        <span className="text-neutral-500 dark:text-gray-400">Website:</span>
                        <a
                          href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-primary-600 dark:text-primary-400 font-medium hover:underline"
                        >
                          {company.website}
                        </a>
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2 flex items-center gap-1.5">
                    <MapPin className="h-4 w-4 text-neutral-500 dark:text-gray-400" />
                    Endereço
                  </h4>
                  <div className="space-y-2">
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Endereço:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">{company.address || "N/A"}</span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">Cidade/Estado:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">
                        {company.city && company.state ? `${company.city}/${company.state}` : "N/A"}
                      </span>
                    </p>
                    <p className="text-sm flex justify-between">
                      <span className="text-neutral-500 dark:text-gray-400">CEP:</span>
                      <span className="text-neutral-800 dark:text-gray-200 font-medium">
                        {company.postalCode || "N/A"}
                      </span>
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {company.description && (
              <div>
                <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2">Descrição</h4>
                <p className="text-sm text-neutral-600 dark:text-gray-300 bg-neutral-50 dark:bg-gray-700 p-3 rounded-md">
                  {company.description}
                </p>
              </div>
            )}

            {company.socialMedia && (
              <div>
                <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2">Redes Sociais</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {Object.entries(parseSocialMedia(company.socialMedia) || {}).map(([key, value]) => (
                    value && (
                      <a
                        key={key}
                        href={value.startsWith('http') ? value : `https://${value}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-sm text-primary-600 dark:text-primary-400 hover:underline flex items-center gap-1"
                      >
                        <Globe className="h-4 w-4" />
                        {(() => {
                          // Mapear nomes de redes sociais para exibição
                          const socialNames = {
                            facebook: 'Facebook',
                            instagram: 'Instagram',
                            linkedin: 'LinkedIn',
                            twitter: 'Twitter',
                            youtube: 'YouTube',
                            website: 'Website',
                            email: 'Email'
                          };
                          return socialNames[key] || key.charAt(0).toUpperCase() + key.slice(1);
                        })()}
                      </a>
                    )
                  ))}
                </div>
              </div>
            )}

            {company.businessHours && (
              <div>
                <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-300 mb-2">Horário de Funcionamento</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                  {Object.entries(parseBusinessHours(company.businessHours) || {}).map(([day, hours]) => (
                    hours && (
                      <p key={day} className="text-sm flex justify-between">
                        <span className="text-neutral-500 dark:text-gray-400">
                          {day === 'monday' && 'Segunda-feira'}
                          {day === 'tuesday' && 'Terça-feira'}
                          {day === 'wednesday' && 'Quarta-feira'}
                          {day === 'thursday' && 'Quinta-feira'}
                          {day === 'friday' && 'Sexta-feira'}
                          {day === 'saturday' && 'Sábado'}
                          {day === 'sunday' && 'Domingo'}
                        </span>
                        <span className="text-neutral-800 dark:text-gray-200 font-medium">
                          {typeof hours === 'string'
                            ? hours
                            : (hours.start && hours.end)
                              ? `${hours.start} às ${hours.end}`
                              : JSON.stringify(hours)}
                        </span>
                      </p>
                    )
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 text-neutral-500 dark:text-neutral-400">
            Nenhuma informação disponível
          </div>
        )}
      </div>
      <div className="flex justify-end p-4 border-t border-neutral-200 dark:border-gray-700">
        <button
          onClick={onClose}
          className="px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors"
        >
          Fechar
        </button>
      </div>
    </Modal>
  );
};

export default CompanyDetailsModal;
