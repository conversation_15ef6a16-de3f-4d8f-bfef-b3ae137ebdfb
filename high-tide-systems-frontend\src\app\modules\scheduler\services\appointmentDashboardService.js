// app/modules/scheduler/services/appointmentDashboardService.js
import { api } from "@/utils/api";
import { appointmentService } from "./appointmentService";
import { exportService } from "@/app/services/exportService";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";

export const appointmentDashboardService = {
  /**
   * Get all dashboard data for appointments
   * @param {Object} params - Parameters for filtering dashboard data
   * @returns {Promise<Object>} Dashboard data
   */
  getAppointmentsDashboardData: async (params = {}) => {
    try {
      // Get dashboard data from API or compute it from existing data
      const response = await api.get("/schedulings/dashboard", { params });
      return response.data;
    } catch (error) {
      console.error("Error fetching appointments dashboard data:", error);

      // Fallback: Generate dashboard data from appointments if the API endpoint doesn't exist
      return appointmentDashboardService.generateDashboardDataFromAppointments(params);
    }
  },

  /**
   * Exporta os dados do dashboard de agendamentos
   * @param {Object} dashboardData - Dados do dashboard
   * @param {Object} params - Parâmetros usados para filtrar os dados
   * @param {string} exportFormat - Formato da exportação ('xlsx', 'pdf' ou 'image')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportDashboardData: async (dashboardData, params = {}, exportFormat = "xlsx") => {
    try {
      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Período para o subtítulo
      let periodText = 'Todos os períodos';
      if (params.period) {
        switch (params.period) {
          case '7days':
            periodText = 'Últimos 7 dias';
            break;
          case '30days':
            periodText = 'Últimos 30 dias';
            break;
          case '3months':
            periodText = 'Últimos 3 meses';
            break;
          case '1year':
            periodText = 'Último ano';
            break;
          case 'next7days':
            periodText = 'Próximos 7 dias';
            break;
          case 'next30days':
            periodText = 'Próximos 30 dias';
            break;
          default:
            if (params.startDate && params.endDate) {
              const start = dateFormat(new Date(params.startDate), "dd/MM/yyyy", { locale: ptBR });
              const end = dateFormat(new Date(params.endDate), "dd/MM/yyyy", { locale: ptBR });
              periodText = `${start} até ${end}`;
            } else {
              periodText = params.period;
            }
        }
      }

      // Subtítulo com timestamp e período
      const subtitle = `Exportado em: ${timestamp} | Período: ${periodText}`;

      // Definição das colunas para cada tabela
      const statsColumns = [
        { key: "metric", header: "Métrica" },
        { key: "value", header: "Valor", align: "right" }
      ];

      const patientsColumns = [
        { key: "name", header: "Paciente" },
        { key: "count", header: "Agendamentos", align: "right" }
      ];

      const servicesColumns = [
        { key: "name", header: "Serviço" },
        { key: "count", header: "Agendamentos", align: "right" }
      ];

      const locationsColumns = [
        { key: "name", header: "Localização" },
        { key: "count", header: "Agendamentos", align: "right" }
      ];

      const statusColumns = [
        { key: "name", header: "Status" },
        { key: "value", header: "Quantidade", align: "right" }
      ];

      const trendsColumns = [
        { key: "date", header: "Data", type: "date" },
        { key: "count", header: "Agendamentos", align: "right" }
      ];

      // Preparar os dados de estatísticas para exportação
      const statsData = [
        { metric: "Total de Agendamentos", value: dashboardData.stats.totalAppointments },
        { metric: "Concluídos", value: dashboardData.stats.completedAppointments },
        { metric: "Confirmados", value: dashboardData.stats.confirmedAppointments },
        { metric: "Pendentes", value: dashboardData.stats.pendingAppointments },
        { metric: "Cancelados", value: dashboardData.stats.cancelledAppointments },
        { metric: "Não Compareceu", value: dashboardData.stats.noShowAppointments }
      ];

      // Exportar os dados em múltiplas tabelas
      return await exportService.exportData(
        {
          estatisticas: statsData,
          pacientes: dashboardData.topPatients || [],
          servicos: dashboardData.topServices || [],
          localizacoes: dashboardData.topLocations || [],
          status: dashboardData.statusDistribution || [],
          tendencias: dashboardData.appointmentTrends || []
        },
        {
          format: exportFormat,
          filename: "dashboard-agendamentos",
          title: "Dashboard de Agendamentos",
          subtitle,
          multiTable: true,
          tables: [
            { name: "estatisticas", title: "Estatísticas Gerais", columns: statsColumns },
            { name: "pacientes", title: "Pacientes Mais Agendados", columns: patientsColumns },
            { name: "servicos", title: "Serviços Mais Agendados", columns: servicesColumns },
            { name: "localizacoes", title: "Localizações Mais Utilizadas", columns: locationsColumns },
            { name: "status", title: "Distribuição por Status", columns: statusColumns },
            { name: "tendencias", title: "Tendência de Agendamentos", columns: trendsColumns }
          ]
        }
      );
    } catch (error) {
      console.error("Erro ao exportar dados do dashboard de agendamentos:", error);
      return false;
    }
  },

  /**
   * Get upcoming recurring appointments
   * @returns {Promise<Array>} List of upcoming recurring appointments with details
   */
  getUpcomingRecurringAppointments: async () => {
    try {
      const response = await api.get("/schedulings/upcoming-recurring");
      return response.data;
    } catch (error) {
      console.error("Error fetching upcoming recurring appointments:", error);

      // Fallback: Try to get from regular appointments endpoint
      try {
        const allAppointments = await appointmentService.getAppointments({
          limit: 100,
          includeRecurring: true,
          upcoming: true
        });

        // Filter and process recurring appointments
        const recurringAppointments = (allAppointments.appointments || [])
          .filter(appointment => appointment.isRecurring || appointment.recurringId)
          .map(appointment => {
            // Enhance with additional details
            return {
              ...appointment,
              formattedStartDate: new Date(appointment.startDate).toLocaleDateString('pt-BR'),
              formattedStartTime: new Date(appointment.startDate).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
              formattedEndTime: new Date(appointment.endDate).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
              dayOfWeek: new Date(appointment.startDate).toLocaleDateString('pt-BR', { weekday: 'long' })
            };
          });

        return recurringAppointments;
      } catch (fallbackError) {
        console.error("Fallback error fetching recurring appointments:", fallbackError);
        return [];
      }
    }
  },

  /**
   * Get upcoming sequential appointments
   * @returns {Promise<Array>} List of upcoming sequential appointments with details
   */
  getUpcomingSequentialAppointments: async () => {
    try {
      const response = await api.get("/schedulings/upcoming-sequential");
      return response.data;
    } catch (error) {
      console.error("Error fetching upcoming sequential appointments:", error);

      // Fallback: Try to get from regular appointments endpoint
      try {
        const allAppointments = await appointmentService.getAppointments({
          limit: 100,
          includeSequential: true,
          upcoming: true
        });

        // Filter and process sequential appointments
        const sequentialAppointments = (allAppointments.appointments || [])
          .filter(appointment => appointment.isSequential || appointment.sequentialId)
          .map(appointment => {
            // Enhance with additional details
            return {
              ...appointment,
              formattedStartDate: new Date(appointment.startDate).toLocaleDateString('pt-BR'),
              formattedStartTime: new Date(appointment.startDate).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
              formattedEndTime: new Date(appointment.endDate).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' }),
              dayOfWeek: new Date(appointment.startDate).toLocaleDateString('pt-BR', { weekday: 'long' })
            };
          });

        return sequentialAppointments;
      } catch (fallbackError) {
        console.error("Fallback error fetching sequential appointments:", fallbackError);
        return [];
      }
    }
  },

  /**
   * Get top patients by appointment count
   * @param {string} period - Time period for filtering (7days, 30days, 3months, 1year)
   * @param {Object} params - Additional parameters
   * @returns {Promise<Array>} Top patients
   */
  getTopPatients: async (period = "30days", params = {}) => {
    try {
      const response = await api.get(`/schedulings/top-patients`, {
        params: { period, ...params }
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching top patients:", error);
      return [];
    }
  },

  /**
   * Get top services by appointment count
   * @param {string} period - Time period for filtering
   * @param {Object} params - Additional parameters
   * @returns {Promise<Array>} Top services
   */
  getTopServices: async (period = "30days", params = {}) => {
    try {
      const response = await api.get(`/schedulings/top-services`, {
        params: { period, ...params }
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching top services:", error);
      return [];
    }
  },

  /**
   * Get top locations by appointment count
   * @param {string} period - Time period for filtering
   * @param {Object} params - Additional parameters
   * @returns {Promise<Array>} Top locations
   */
  getTopLocations: async (period = "30days", params = {}) => {
    try {
      const response = await api.get(`/schedulings/top-locations`, {
        params: { period, ...params }
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching top locations:", error);
      return [];
    }
  },

  /**
   * Get appointment status distribution
   * @param {string} period - Time period for filtering
   * @param {Object} params - Additional parameters
   * @returns {Promise<Array>} Status distribution
   */
  getStatusDistribution: async (period = "30days", params = {}) => {
    try {
      const response = await api.get(`/schedulings/status-distribution`, {
        params: { period, ...params }
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching status distribution:", error);
      return [];
    }
  },

  /**
   * Get appointment trends over time
   * @param {string} period - Time period for filtering
   * @param {Object} params - Additional parameters
   * @returns {Promise<Array>} Trend data
   */
  getAppointmentTrends: async (period = "30days", params = {}) => {
    try {
      const response = await api.get(`/schedulings/trends`, {
        params: { period, ...params }
      });
      return response.data;
    } catch (error) {
      console.error("Error fetching appointment trends:", error);
      return [];
    }
  },

  /**
   * Generate dashboard data from appointments if dedicated API endpoints don't exist
   * This is a fallback method that processes raw appointment data
   * @param {Object} params - Parameters for filtering
   * @returns {Promise<Object>} Generated dashboard data
   */
  generateDashboardDataFromAppointments: async (params = {}) => {
    try {
      // Get all appointments with relevant data
      const response = await appointmentService.getAppointments({
        limit: 1000,  // Get a large number of appointments
        ...params
      });

      const appointments = response.appointments || [];

      if (appointments.length === 0) {
        return {
          stats: {
            totalAppointments: 0,
            completedAppointments: 0,
            cancelledAppointments: 0,
            noShowAppointments: 0,
            pendingAppointments: 0,
            confirmedAppointments: 0
          },
          topPatients: [],
          topServices: [],
          topLocations: [],
          statusDistribution: [],
          appointmentTrends: []
        };
      }

      // Calculate stats
      const stats = calculateAppointmentStats(appointments);

      // Generate top lists
      const topPatients = calculateTopPatients(appointments);
      const topServices = calculateTopServices(appointments);
      // Note: topLocations is now async
      const topLocations = await calculateTopLocations(appointments);

      // Generate distributions
      const statusDistribution = calculateStatusDistribution(appointments);

      // Generate trends (appointments per day/week)
      const appointmentTrends = calculateAppointmentTrends(appointments);

      return {
        stats,
        topPatients,
        topServices,
        topLocations,
        statusDistribution,
        appointmentTrends
      };
    } catch (error) {
      console.error("Error generating dashboard data:", error);
      return {
        stats: {
          totalAppointments: 0,
          completedAppointments: 0,
          cancelledAppointments: 0,
          noShowAppointments: 0,
          pendingAppointments: 0,
          confirmedAppointments: 0
        },
        topPatients: [],
        topServices: [],
        topLocations: [],
        statusDistribution: [],
        appointmentTrends: []
      };
    }
  }
};

/**
 * Calculate appointment statistics
 * @param {Array} appointments - List of appointments
 * @returns {Object} Statistics
 */
function calculateAppointmentStats(appointments) {
  // Count by status
  const completedAppointments = appointments.filter(a => a.status === 'COMPLETED').length;
  const cancelledAppointments = appointments.filter(a => a.status === 'CANCELLED').length;
  const noShowAppointments = appointments.filter(a => a.status === 'NO_SHOW').length;
  const pendingAppointments = appointments.filter(a => a.status === 'PENDING').length;
  const confirmedAppointments = appointments.filter(a => a.status === 'CONFIRMED').length;

  return {
    totalAppointments: appointments.length,
    completedAppointments,
    cancelledAppointments,
    noShowAppointments,
    pendingAppointments,
    confirmedAppointments
  };
}

/**
 * Calculate top patients by appointment count
 * @param {Array} appointments - List of appointments
 * @param {number} limit - Maximum number of patients to return
 * @returns {Array} Top patients
 */
function calculateTopPatients(appointments, limit = 5) {
  // Group by patient/person
  const patientCounts = {};

  appointments.forEach(appointment => {
    const personId = appointment.personId;
    const personName = appointment.personfullName || 'Unknown';

    if (personId) {
      if (!patientCounts[personId]) {
        patientCounts[personId] = {
          id: personId,
          name: personName,
          count: 0
        };
      }
      patientCounts[personId].count++;
    }
  });

  // Convert to array and sort
  return Object.values(patientCounts)
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
}

/**
 * Calculate top services by appointment count
 * @param {Array} appointments - List of appointments
 * @param {number} limit - Maximum number of services to return
 * @returns {Array} Top services
 */
function calculateTopServices(appointments, limit = 5) {
  // Group by service type
  const serviceCounts = {};

  appointments.forEach(appointment => {
    const serviceId = appointment.serviceTypeId;
    const serviceName = appointment.serviceTypefullName || 'Unknown';

    if (serviceId) {
      if (!serviceCounts[serviceId]) {
        serviceCounts[serviceId] = {
          id: serviceId,
          name: serviceName,
          count: 0
        };
      }
      serviceCounts[serviceId].count++;
    }
  });

  // Convert to array and sort
  return Object.values(serviceCounts)
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
}

/**
 * Calculate top locations by appointment count
 * @param {Array} appointments - List of appointments
 * @param {number} limit - Maximum number of locations to return
 * @returns {Promise<Array>} Top locations
 */
async function calculateTopLocations(appointments, limit = 5) {
  // Group by location
  const locationCounts = {};

  appointments.forEach(appointment => {
    const locationId = appointment.locationId;
    // We'll temporarily use a placeholder and then fetch real names
    const locationTemp = `Local ${locationId}`;

    if (locationId) {
      if (!locationCounts[locationId]) {
        locationCounts[locationId] = {
          id: locationId,
          name: locationTemp,
          count: 0
        };
      }
      locationCounts[locationId].count++;
    }
  });

  // Try to get actual location names from the API
  try {
    const { api } = await import('@/utils/api');
    const response = await api.get("/locations");

    if (response?.data?.locations) {
      const locations = response.data.locations;

      // Update location names with actual names
      Object.keys(locationCounts).forEach(locationId => {
        const location = locations.find(loc => loc.id === locationId);
        if (location) {
          locationCounts[locationId].name = location.name || location.description || `Local ${locationId}`;
        }
      });
    }
  } catch (error) {
    console.error("Error fetching location names:", error);
    // Continue with placeholder names if we can't get real names
  }

  // Convert to array and sort
  return Object.values(locationCounts)
    .sort((a, b) => b.count - a.count)
    .slice(0, limit);
}

/**
 * Calculate appointment status distribution
 * @param {Array} appointments - List of appointments
 * @returns {Array} Status distribution
 */
function calculateStatusDistribution(appointments) {
  // Count by status
  const statusCounts = {};
  const statusLabels = {
    'PENDING': 'Pendente',
    'CONFIRMED': 'Confirmado',
    'CANCELLED': 'Cancelado',
    'COMPLETED': 'Concluído',
    'NO_SHOW': 'Não Compareceu'
  };

  appointments.forEach(appointment => {
    const status = appointment.status || 'PENDING';

    if (!statusCounts[status]) {
      statusCounts[status] = {
        status,
        label: statusLabels[status] || status,
        count: 0
      };
    }
    statusCounts[status].count++;
  });

  // Convert to array
  return Object.values(statusCounts);
}

/**
 * Calculate appointment trends over time
 * @param {Array} appointments - List of appointments
 * @param {number} daysToInclude - Number of days to include in the trend
 * @returns {Array} Trend data
 */
function calculateAppointmentTrends(appointments, daysToInclude = 30) {
  // Group by day
  const today = new Date();
  const startDate = new Date();
  startDate.setDate(today.getDate() - daysToInclude);

  // Initialize days
  const days = {};
  for (let i = 0; i <= daysToInclude; i++) {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateString = date.toISOString().split('T')[0];
    days[dateString] = {
      date: dateString,
      count: 0
    };
  }

  // Count appointments per day
  appointments.forEach(appointment => {
    if (appointment.startDate) {
      const date = new Date(appointment.startDate);
      const dateString = date.toISOString().split('T')[0];

      if (days[dateString]) {
        days[dateString].count++;
      }
    }
  });

  // Convert to array and sort by date
  return Object.values(days).sort((a, b) => new Date(a.date) - new Date(b.date));
}