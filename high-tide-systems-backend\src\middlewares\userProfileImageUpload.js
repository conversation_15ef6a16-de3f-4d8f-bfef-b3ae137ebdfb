const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Caminho base para uploads
const UPLOAD_PATH = process.env.NODE_ENV === 'production'
  ? path.resolve("/tmp/uploads")
  : path.resolve("./uploads");

console.log('[userProfileImageUpload] Caminho base para uploads:', UPLOAD_PATH);

// Garante que o diretório de uploads existe
const ensureUploadDirectoryExists = () => {
  const directories = [
    UPLOAD_PATH,
    path.join(UPLOAD_PATH, "user-profile-images")
  ];

  directories.forEach(dir => {
    console.log(`[userProfileImageUpload] Verificando se o diretório existe: ${dir}`);
    if (!fs.existsSync(dir)) {
      console.log(`[userProfileImageUpload] Criando diretório: ${dir}`);
      fs.mkdirSync(dir, { recursive: true });
      console.log(`[userProfileImageUpload] Diretório criado com sucesso: ${dir}`);
    } else {
      console.log(`[userProfileImageUpload] Diretório já existe: ${dir}`);
    }
  });
};

// Cria os diretórios necessários
console.log('[userProfileImageUpload] Garantindo que os diretórios de upload existam...');
ensureUploadDirectoryExists();
console.log('[userProfileImageUpload] Diretórios de upload verificados/criados com sucesso');

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    console.log('[userProfileImageUpload] Configurando destino para upload de arquivo');
    console.log('[userProfileImageUpload] Req params:', req.params);
    console.log('[userProfileImageUpload] Req user:', req.user ? 'Presente' : 'Ausente');

    // Usar caminho absoluto para o diretório de uploads
    const profileImagesPath = path.join(UPLOAD_PATH, "user-profile-images");
    console.log('[userProfileImageUpload] Diretório de destino:', profileImagesPath);

    // Cria diretório específico para imagens de perfil se não existir
    try {
      if (!fs.existsSync(profileImagesPath)) {
        console.log('[userProfileImageUpload] Diretório não existe, criando...');
        fs.mkdirSync(profileImagesPath, { recursive: true });
        console.log('[userProfileImageUpload] Diretório criado com sucesso');
      } else {
        console.log('[userProfileImageUpload] Diretório já existe');
      }

      // Verificar permissões de escrita
      fs.accessSync(profileImagesPath, fs.constants.W_OK);
      console.log('[userProfileImageUpload] Diretório tem permissões de escrita');
    } catch (error) {
      console.error('[userProfileImageUpload] Erro ao verificar/criar diretório:', error);
    }

    console.log('[userProfileImageUpload] Definindo destino do arquivo:', profileImagesPath);
    cb(null, profileImagesPath);
  },
  filename: (req, file, cb) => {
    console.log('[userProfileImageUpload] Gerando nome de arquivo');
    console.log('[userProfileImageUpload] Arquivo original:', file.originalname);
    console.log('[userProfileImageUpload] Params:', req.params);

    // Usar um nome previsível para facilitar o acesso posterior
    const userId = req.params.id || 'new';
    const timestamp = Date.now();
    const extension = path.extname(file.originalname) || '.jpg';

    // Nome do arquivo: user-profile-{userId}-{timestamp}.{extension}
    const filename = `user-profile-${userId}-${timestamp}${extension}`;
    console.log('[userProfileImageUpload] Nome de arquivo gerado:', filename);

    cb(null, filename);
  },
});

const fileFilter = (req, file, cb) => {
  console.log('[userProfileImageUpload] Verificando tipo de arquivo');
  console.log('[userProfileImageUpload] Tipo MIME:', file.mimetype);

  // Aceita apenas arquivos de imagem
  if (file.mimetype.startsWith('image/')) {
    console.log('[userProfileImageUpload] Arquivo é uma imagem, aceitando');
    cb(null, true);
  } else {
    console.log('[userProfileImageUpload] Arquivo não é uma imagem, rejeitando');
    cb(new Error("Apenas imagens são permitidas"));
  }
};

console.log('[userProfileImageUpload] Configurando middleware de upload de imagem de perfil de usuário');
const userProfileImageUpload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024, // Limite de 2MB
  },
});
console.log('[userProfileImageUpload] Middleware de upload de imagem de perfil de usuário configurado com sucesso');

module.exports = userProfileImageUpload;
