import { api } from "@/utils/api";

export const companyLogoService = {
  // Obter a URL da imagem do logotipo da empresa
  getCompanyLogoUrl: (companyId, logoPath) => {
    console.log('[companyLogoService] Iniciando getCompanyLogoUrl');
    console.log('[companyLogoService] companyId:', companyId);
    console.log('[companyLogoService] logoPath original:', logoPath);

    if (!companyId || !logoPath) {
      console.log('[companyLogoService] companyId ou logoPath ausente, retornando null');
      return null;
    }

    // Extrair o nome do arquivo do caminho completo
    const filename = logoPath.includes('/') ? logoPath.split('/').pop() : logoPath;
    console.log('[companyLogoService] Nome do arquivo extraído:', filename);

    // Construir a URL completa para a imagem
    const baseUrl = api.defaults.baseURL || 'http://localhost:5000';
    console.log('[companyLogoService] baseUrl:', baseUrl);

    // Verificar se o caminho contém o diretório do Docker
    if (logoPath.includes('/usr/src/app/uploads/')) {
      // Extrair o caminho relativo a partir de /uploads/
      const relativePath = logoPath.split('/uploads/')[1];
      console.log('[companyLogoService] Caminho relativo extraído:', relativePath);

      // Construir URL com o caminho relativo
      const url = `${baseUrl}/uploads/${relativePath}`;
      console.log('[companyLogoService] URL final (caminho relativo):', url);
      return url;
    }

    // Fallback para a abordagem anterior usando apenas o nome do arquivo
    const url = `${baseUrl}/uploads/file/${filename}`;
    console.log('[companyLogoService] URL final (apenas nome do arquivo):', url);

    return url;
  }
};

export default companyLogoService;
