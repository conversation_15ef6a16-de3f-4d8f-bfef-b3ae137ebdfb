'use client';

import { motion } from 'framer-motion';
import { Star, Quote } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    content: "O High Tide Systems transformou completamente a gestão da nossa clínica. Reduzimos o tempo gasto com agendamentos em 70% e praticamente eliminamos os conflitos de horários.",
    author: "Dra. Mariana Costa",
    role: "Diretora Clínica",
    company: "Clínica Bem Estar",
    rating: 5,
    image: "/testimonials/avatar1.jpg"
  },
  {
    id: 2,
    content: "A integração entre os módulos é impressionante. Conseguimos ter uma visão completa de cada paciente, desde o histórico médico até os pagamentos, tudo em um só lugar.",
    author: "Dr. <PERSON>",
    role: "Pro<PERSON>riet<PERSON><PERSON>",
    company: "Centro Médico Saúde Total",
    rating: 5,
    image: "/testimonials/avatar2.jpg"
  },
  {
    id: 3,
    content: "O suporte ao cliente é excepcional. Sempre que tivemos dúvidas, a equipe do High Tide nos atendeu prontamente e resolveu nossos problemas com eficiência.",
    author: "<PERSON>",
    role: "Gerente Administrativa",
    company: "Instituto de Fisioterapia Avançada",
    rating: 5,
    image: "/testimonials/avatar3.jpg"
  }
];

const Testimonials = () => {
  return (
    <section className="py-20 bg-white dark:bg-gray-900 overflow-hidden">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            O que nossos clientes dizem
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Centenas de clínicas e consultórios já transformaram sua gestão com o High Tide Systems.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              className="bg-gray-50 dark:bg-gray-800 rounded-xl p-8 shadow-lg border border-gray-100 dark:border-gray-700 relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center text-white">
                <Quote size={16} />
              </div>
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-yellow-500 fill-current" />
                ))}
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-6 italic">
                "{testimonial.content}"
              </p>
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full mr-4 overflow-hidden flex items-center justify-center">
                  {testimonial.image ? (
                    <img 
                      src={testimonial.image} 
                      alt={testimonial.author}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = '';
                        e.target.parentElement.innerHTML = testimonial.author.charAt(0);
                      }}
                    />
                  ) : (
                    <span className="text-white font-semibold text-lg">
                      {testimonial.author.charAt(0)}
                    </span>
                  )}
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-white">
                    {testimonial.author}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {testimonial.role}, {testimonial.company}
                  </p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
