const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");

// Definição básica do Swagger
const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "ITH API",
      version: "1.0.0",
      description: "API de gerenciamento para o sistema Inside The Houses",
      contact: {
        name: "Equipe de Desenvolvimento",
        email: "não tem ainda kkkkkkkk",
      },
      license: {
        name: "Proprietário",
        url: "https://insidehouses.com/terms nem adianta clicar",
      },
    },
    servers: [
      {
        url: "http://localhost:5000",
        description: "Servidor de Desenvolvimento",
      },
      {
        url: "https://inside-houses-staging.com ainda não roda",
        description: "Servidor de Staging - NÃO RODA",
      },
      {
        url: "https://inside-houses-production.com muito menos esse",
        description: "Servidor de Produção - NÃO RODA",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
      schemas: {
        Error: {
          type: "object",
          properties: {
            message: {
              type: "string",
              description: "Mensagem de erro",
            },
            errors: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  msg: { type: "string" },
                  param: { type: "string" },
                  location: { type: "string" },
                },
              },
              description: "Detalhes dos erros de validação",
            },
          },
        },
      },
      responses: {
        UnauthorizedError: {
          description: "Token de autenticação ausente ou inválido",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/Error",
              },
              example: {
                message: "Token não fornecido",
              },
            },
          },
        },
        ForbiddenError: {
          description: "Sem permissão para acessar o recurso",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/Error",
              },
              example: {
                message: "Acesso negado",
              },
            },
          },
        },
        ValidationError: {
          description: "Erro de validação dos dados fornecidos",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/Error",
              },
              example: {
                message: "Erro de validação",
                errors: [
                  {
                    msg: "Email inválido",
                    param: "email",
                    location: "body",
                  },
                ],
              },
            },
          },
        },
        NotFoundError: {
          description: "Recurso não encontrado",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/Error",
              },
              example: {
                message: "Recurso não encontrado",
              },
            },
          },
        },
        ServerError: {
          description: "Erro interno do servidor",
          content: {
            "application/json": {
              schema: {
                $ref: "#/components/schemas/Error",
              },
              example: {
                message: "Erro interno do servidor",
              },
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      { name: "Auth", description: "Autenticação e gestão de sessão" },
      { name: "Usuários", description: "Operações relacionadas a usuários" },
      { name: "Clientes", description: "Operações relacionadas a clientes" },
      { name: "Agendamentos", description: "Gerenciamento de agendamentos" },
      {
        name: "Recorrências",
        description: "Gestão de agendamentos recorrentes",
      },
      { name: "Locais", description: "Gestão de locais para atendimento" },
      {
        name: "Tipos de Serviço",
        description: "Gestão de serviços oferecidos",
      },
      { name: "Convênios", description: "Gestão de convênios médicos" },
      { name: "Documentos", description: "Upload e gestão de documentos" },
      {
        name: "Horários de Trabalho",
        description: "Gestão de disponibilidade dos profissionais",
      },
      {
        name: "Notificações",
        description: "Sistema de notificações e lembretes",
      },
      { name: "Config. Email", description: "Configurações de envio de email" },
      {
        name: "Empresas",
        description: "Gestão de empresas no sistema (multi-tenancy)",
      },
      {
        name: "Unidades",
        description: "Gerenciamento de unidades/filiais da empresa",
      },
    ],
  },
  // Caminhos para os arquivos que contêm anotações JSDoc
  apis: ["./src/models/*.js", "./src/routes/*.js", "./src/swagger/*.js"],
};

const specs = swaggerJsdoc(options);

module.exports = {
  serve: swaggerUi.serve,
  setup: swaggerUi.setup(specs, {
    explorer: true,
    customCss: ".swagger-ui .topbar { display: none }",
    customSiteTitle: "ITH API - Documentação",
    // customfavIcon: '/favicon.ico'
  }),
  specs,
};
