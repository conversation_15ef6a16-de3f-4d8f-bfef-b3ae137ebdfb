// app/modules/scheduler/services/occupancyService.js
import { api } from "@/utils/api";
import { appointmentService } from "./appointmentService";
import workingHoursService from "./workingHoursService";
import { exportService } from "@/app/services/exportService";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";

// Função auxiliar para calcular o início e fim do período
const getDateRange = (period, date = new Date()) => {
  const startDate = new Date(date);
  const endDate = new Date(date);

  switch (period) {
    case 'day':
      // Hoje
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'week':
      // Esta semana
      const day = startDate.getDay();
      const diff = startDate.getDate() - day + (day === 0 ? -6 : 1); // Ajusta para começar na segunda-feira
      startDate.setDate(diff);
      startDate.setHours(0, 0, 0, 0);
      endDate.setDate(startDate.getDate() + 6);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'month':
      // Este mês
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
      break;
    case 'year':
      // Este ano
      startDate.setMonth(0, 1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(11, 31);
      endDate.setHours(23, 59, 59, 999);
      break;
    default:
      // Padrão: este mês
      startDate.setDate(1);
      startDate.setHours(0, 0, 0, 0);
      endDate.setMonth(endDate.getMonth() + 1);
      endDate.setDate(0);
      endDate.setHours(23, 59, 59, 999);
  }

  return { startDate, endDate };
};

// Serviço para análise de ocupação
export const occupancyService = {
  /**
   * Obtém dados de ocupação baseados em período, filtrando por profissional, serviço e local
   * @param {Object} params Parâmetros para filtragem
   * @returns {Promise<Object>} Dados de ocupação
   */
  getOccupancyData: async (params = {}) => {
    try {
      // Tentar obter os dados diretamente da API
      const response = await api.get("/occupancy", { params });
      return response.data;
    } catch (error) {
      console.error("Error fetching occupancy data from API:", error);

      // Fallback: calcular a ocupação localmente se o endpoint não existir
      return occupancyService.calculateOccupancyData(params);
    }
  },

  /**
   * Calcula dados de ocupação a partir dos agendamentos e horários disponíveis
   * @param {Object} params Parâmetros para filtragem
   * @returns {Promise<Object>} Dados de ocupação calculados
   */
  calculateOccupancyData: async (params = {}) => {
    try {
      const {
        period = 'month',
        date,
        providers = [],
        serviceTypes = [],
        locations = []
      } = params;

      // Calcular o intervalo de datas baseado no período
      const { startDate, endDate } = getDateRange(period, date ? new Date(date) : undefined);

      // Buscar todos os agendamentos no período
      const appointmentsResponse = await appointmentService.getAppointments({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        providers: providers.length > 0 ? providers : undefined,
        serviceTypes: serviceTypes.length > 0 ? serviceTypes : undefined,
        locations: locations.length > 0 ? locations : undefined,
        limit: 1000 // Limite alto para pegar todos os agendamentos no período
      });

      const appointments = appointmentsResponse.appointments || [];

      if (appointments.length === 0) {
        return {
          overallOccupancy: 0,
          providerOccupancy: [],
          serviceTypeDistribution: [],
          locationOccupancy: [],
          timeDistribution: [],
          weekdayDistribution: []
        };
      }

      // Listar os provedores únicos nos agendamentos filtrados
      const uniqueProviderIds = [...new Set(appointments.map(app => app.userId || app.providerId))];

      // Se não houver filtro de provedor, usar os que têm agendamentos no período
      const providersToAnalyze = providers.length > 0
        ? providers
        : uniqueProviderIds;

      // Obter todos os provedores para ter nomes e informações
      const allProviders = await appointmentService.getProviders();

      // Obter informações de disponibilidade para cada provedor
      const providersData = await Promise.all(providersToAnalyze.map(async (providerId) => {
        // Informações básicas do provedor
        const providerInfo = allProviders.find(p => p.id === providerId) || {
          id: providerId,
          fullName: `Profissional ${providerId.substring(0, 8)}`
        };

        // Calcular o total de horas disponíveis para cada provedor no período
        let totalAvailableHours = 0;
        let totalBookedHours = 0;

        // Buscar o calendário de disponibilidade para cada dia da semana
        const daysInPeriod = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

        for (let i = 0; i < daysInPeriod; i++) {
          const currentDate = new Date(startDate);
          currentDate.setDate(currentDate.getDate() + i);
          const dayOfWeek = currentDate.getDay();

          // Buscar grade de horários disponíveis para este dia da semana
          try {
            const timeGridData = await workingHoursService.getDayGrid(providerId, dayOfWeek);

            if (timeGridData && timeGridData.timeGrid) {
              // Conta quantas horas disponíveis neste dia
              const availableHoursToday = timeGridData.timeGrid.filter(Boolean).length;
              totalAvailableHours += availableHoursToday;
            }
          } catch (error) {
            console.error(`Error fetching time grid for provider ${providerId}, day ${dayOfWeek}:`, error);
          }
        }

        // Calcular horas ocupadas com agendamentos
        const providerAppointments = appointments.filter(
          a => (a.userId === providerId || a.providerId === providerId)
        );

        providerAppointments.forEach(appointment => {
          const start = new Date(appointment.startDate);
          const end = new Date(appointment.endDate);
          const durationHours = (end - start) / (1000 * 60 * 60);
          totalBookedHours += durationHours;
        });

        // Calcular taxa de ocupação
        const occupancyRate = totalAvailableHours > 0
          ? (totalBookedHours / totalAvailableHours) * 100
          : 0;

        return {
          providerId,
          providerName: providerInfo.fullName,
          totalAvailableHours,
          totalBookedHours,
          occupancyRate: Math.min(occupancyRate, 100), // Limita a 100% no máximo
          appointmentCount: providerAppointments.length
        };
      }));

      // Ordenar provedores por taxa de ocupação
      const providerOccupancy = providersData.sort((a, b) => b.occupancyRate - a.occupancyRate);

      // Calcular taxa de ocupação geral
      const totalAvailableHours = providersData.reduce((sum, p) => sum + p.totalAvailableHours, 0);
      const totalBookedHours = providersData.reduce((sum, p) => sum + p.totalBookedHours, 0);
      const overallOccupancy = totalAvailableHours > 0
        ? (totalBookedHours / totalAvailableHours) * 100
        : 0;

      // Distribuição por tipo de serviço
      const serviceTypeCounts = {};

      appointments.forEach(appointment => {
        const serviceTypeId = appointment.serviceTypeId;
        const serviceTypeName = appointment.serviceType?.name ||
                               appointment.serviceTypefullName ||
                               `Serviço ${serviceTypeId?.substring(0, 8)}`;

        if (serviceTypeId) {
          if (!serviceTypeCounts[serviceTypeId]) {
            serviceTypeCounts[serviceTypeId] = {
              serviceTypeId,
              serviceTypeName,
              count: 0,
              providerDistribution: {}
            };
          }

          serviceTypeCounts[serviceTypeId].count++;

          // Contagem por provedor para este tipo de serviço
          const providerId = appointment.userId || appointment.providerId;
          if (providerId) {
            if (!serviceTypeCounts[serviceTypeId].providerDistribution[providerId]) {
              const providerInfo = allProviders.find(p => p.id === providerId) || {
                id: providerId,
                fullName: `Profissional ${providerId.substring(0, 8)}`
              };

              serviceTypeCounts[serviceTypeId].providerDistribution[providerId] = {
                providerId,
                providerName: providerInfo.fullName,
                count: 0
              };
            }

            serviceTypeCounts[serviceTypeId].providerDistribution[providerId].count++;
          }
        }
      });

      // Converter para array e encontrar o top provedor para cada serviço
      const serviceTypeDistribution = Object.values(serviceTypeCounts)
        .map(service => {
          // Encontrar o provedor com mais agendamentos para este serviço
          const topProvider = Object.values(service.providerDistribution)
            .sort((a, b) => b.count - a.count)[0] || null;

          return {
            serviceTypeId: service.serviceTypeId,
            serviceTypeName: service.serviceTypeName,
            count: service.count,
            topProvider: topProvider,
            percentageOfTotal: (service.count / appointments.length) * 100
          };
        })
        .sort((a, b) => b.count - a.count);

      // Ocupação por local
      const locationCounts = {};

      appointments.forEach(appointment => {
        const locationId = appointment.locationId;
        const locationName = appointment.location?.name || `Local ${locationId?.substring(0, 8)}`;

        if (locationId) {
          if (!locationCounts[locationId]) {
            locationCounts[locationId] = {
              locationId,
              locationName,
              count: 0
            };
          }

          locationCounts[locationId].count++;
        }
      });

      const locationOccupancy = Object.values(locationCounts)
        .map(location => ({
          ...location,
          percentageOfTotal: (location.count / appointments.length) * 100
        }))
        .sort((a, b) => b.count - a.count);

      // Distribuição por hora do dia
      const hourCounts = Array(24).fill(0);

      appointments.forEach(appointment => {
        const startHour = new Date(appointment.startDate).getHours();
        hourCounts[startHour]++;
      });

      const timeDistribution = hourCounts.map((count, hour) => ({
        hour,
        label: `${String(hour).padStart(2, '0')}:00`,
        count,
        percentageOfTotal: (count / appointments.length) * 100
      }));

      // Distribuição por dia da semana
      const weekdayCounts = Array(7).fill(0);
      const weekdayLabels = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

      appointments.forEach(appointment => {
        const weekday = new Date(appointment.startDate).getDay();
        weekdayCounts[weekday]++;
      });

      const weekdayDistribution = weekdayCounts.map((count, day) => ({
        day,
        label: weekdayLabels[day],
        count,
        percentageOfTotal: (count / appointments.length) * 100
      }));

      return {
        overallOccupancy,
        providerOccupancy,
        serviceTypeDistribution,
        locationOccupancy,
        timeDistribution,
        weekdayDistribution
      };
    } catch (error) {
      console.error("Error calculating occupancy data:", error);
      throw error;
    }
  },

  /**
   * Obtém dados de ocupação de um provedor específico
   * @param {string} providerId ID do provedor
   * @param {Object} params Parâmetros adicionais
   * @returns {Promise<Object>} Dados detalhados de ocupação do provedor
   */
  getProviderOccupancyDetails: async (providerId, params = {}) => {
    try {
      const { period = 'month', date } = params;

      // Calcular o intervalo de datas
      const { startDate, endDate } = getDateRange(period, date ? new Date(date) : undefined);

      // Tentar obter diretamente da API
      try {
        const response = await api.get(`/occupancy/provider/${providerId}`, {
          params: {
            ...params,
            startDate: startDate.toISOString(),
            endDate: endDate.toISOString()
          }
        });
        return response.data;
      } catch (apiError) {
        console.error("API endpoint not available, calculating locally:", apiError);

        // Fallback: calcular localmente
        // Buscar agendamentos do provedor no período
        const appointmentsResponse = await appointmentService.getAppointments({
          providers: [providerId],
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          limit: 1000
        });

        const appointments = appointmentsResponse.appointments || [];

        // Calcular ocupação por dia da semana
        const weekdayOccupancy = [0, 1, 2, 3, 4, 5, 6].map(async (dayOfWeek) => {
          // Buscar disponibilidade para este dia
          const gridData = await workingHoursService.getDayGrid(providerId, dayOfWeek);
          const availableHours = gridData?.timeGrid?.filter(Boolean).length || 0;

          // Contar agendamentos neste dia da semana
          const dayAppointments = appointments.filter(
            a => new Date(a.startDate).getDay() === dayOfWeek
          );

          // Calcular horas ocupadas
          let bookedHours = 0;
          dayAppointments.forEach(appointment => {
            const start = new Date(appointment.startDate);
            const end = new Date(appointment.endDate);
            const durationHours = (end - start) / (1000 * 60 * 60);
            bookedHours += durationHours;
          });

          // Total de dias deste dia da semana no período
          const totalDaysInPeriod = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
          const daysOfThisWeekday = Math.floor(totalDaysInPeriod / 7) +
            ([...Array(totalDaysInPeriod % 7).keys()]
              .map(i => (startDate.getDay() + i) % 7)
              .includes(dayOfWeek) ? 1 : 0);

          const totalAvailableHours = availableHours * daysOfThisWeekday;
          const occupancyRate = totalAvailableHours > 0
            ? (bookedHours / totalAvailableHours) * 100
            : 0;

          const weekdays = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

          return {
            day: dayOfWeek,
            label: weekdays[dayOfWeek],
            availableHours: totalAvailableHours,
            bookedHours,
            appointmentCount: dayAppointments.length,
            occupancyRate: Math.min(occupancyRate, 100)
          };
        });

        // Aguardar todos os dias serem processados
        const weekdayOccupancyData = await Promise.all(weekdayOccupancy);

        // Calcular serviços mais realizados
        const serviceTypeCounts = {};
        appointments.forEach(appointment => {
          const serviceTypeId = appointment.serviceTypeId;
          const serviceTypeName = appointment.serviceType?.name ||
                                appointment.serviceTypefullName ||
                                `Serviço ${serviceTypeId?.substring(0, 8)}`;

          if (serviceTypeId) {
            if (!serviceTypeCounts[serviceTypeId]) {
              serviceTypeCounts[serviceTypeId] = {
                serviceTypeId,
                serviceTypeName,
                count: 0
              };
            }

            serviceTypeCounts[serviceTypeId].count++;
          }
        });

        const topServices = Object.values(serviceTypeCounts)
          .map(service => ({
            ...service,
            percentageOfTotal: (service.count / Math.max(appointments.length, 1)) * 100
          }))
          .sort((a, b) => b.count - a.count);

        // Calcular locais mais utilizados
        const locationCounts = {};
        appointments.forEach(appointment => {
          const locationId = appointment.locationId;
          const locationName = appointment.location?.name || `Local ${locationId?.substring(0, 8)}`;

          if (locationId) {
            if (!locationCounts[locationId]) {
              locationCounts[locationId] = {
                locationId,
                locationName,
                count: 0
              };
            }

            locationCounts[locationId].count++;
          }
        });

        const topLocations = Object.values(locationCounts)
          .map(location => ({
            ...location,
            percentageOfTotal: (location.count / Math.max(appointments.length, 1)) * 100
          }))
          .sort((a, b) => b.count - a.count);

        // Calcular estatísticas gerais
        let totalAvailableHours = 0;
        let totalBookedHours = 0;

        weekdayOccupancyData.forEach(day => {
          totalAvailableHours += day.availableHours;
          totalBookedHours += day.bookedHours;
        });

        const overallOccupancyRate = totalAvailableHours > 0
          ? (totalBookedHours / totalAvailableHours) * 100
          : 0;

        return {
          providerId,
          period,
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          appointmentCount: appointments.length,
          totalAvailableHours,
          totalBookedHours,
          overallOccupancyRate: Math.min(overallOccupancyRate, 100),
          weekdayOccupancy: weekdayOccupancyData,
          topServices,
          topLocations
        };
      }
    } catch (error) {
      console.error(`Error getting occupancy details for provider ${providerId}:`, error);
      throw error;
    }
  },

  /**
   * Exporta os dados de ocupação
   * @param {Object} data - Dados de ocupação
   * @param {string} periodLabel - Rótulo do período selecionado
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportOccupancyData: async (data, periodLabel, exportFormat = "xlsx") => {
    try {
      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Preparar os dados para exportação - Ocupação por profissional
      const providerData = data.providerOccupancy.map(provider => {
        return {
          providerName: provider.providerName,
          totalAvailableHours: provider.totalAvailableHours,
          totalBookedHours: provider.totalBookedHours,
          occupancyRate: provider.occupancyRate.toFixed(1) + '%',
          appointmentCount: provider.appointmentCount,
          status: provider.occupancyRate >= 90 ? 'Crítica' :
                 provider.occupancyRate >= 75 ? 'Alta' :
                 provider.occupancyRate >= 50 ? 'Média' : 'Baixa'
        };
      });

      // Definição das colunas para ocupação por profissional
      const providerColumns = [
        { key: "providerName", header: "Profissional" },
        { key: "totalAvailableHours", header: "Horas Disponíveis", align: "center" },
        { key: "totalBookedHours", header: "Horas Ocupadas", align: "center" },
        { key: "occupancyRate", header: "Taxa de Ocupação", align: "center" },
        { key: "appointmentCount", header: "Agendamentos", align: "center" },
        { key: "status", header: "Status", align: "center" }
      ];

      // Preparar os dados de distribuição por tipo de serviço
      const serviceData = data.serviceTypeDistribution.map(service => {
        return {
          serviceTypeName: service.serviceTypeName,
          count: service.count,
          percentageOfTotal: service.percentageOfTotal.toFixed(1) + '%',
          topProviderName: service.topProvider ? service.topProvider.providerName : 'N/A',
          topProviderCount: service.topProvider ? service.topProvider.count : 0
        };
      });

      // Definição das colunas para distribuição por tipo de serviço
      const serviceColumns = [
        { key: "serviceTypeName", header: "Tipo de Serviço" },
        { key: "count", header: "Agendamentos", align: "center" },
        { key: "percentageOfTotal", header: "% do Total", align: "center" },
        { key: "topProviderName", header: "Profissional Principal" },
        { key: "topProviderCount", header: "Agendamentos", align: "center" }
      ];

      // Preparar os dados de distribuição por local
      const locationData = data.locationOccupancy.map(location => {
        return {
          locationName: location.locationName,
          count: location.count,
          percentageOfTotal: location.percentageOfTotal.toFixed(1) + '%'
        };
      });

      // Definição das colunas para distribuição por local
      const locationColumns = [
        { key: "locationName", header: "Local" },
        { key: "count", header: "Agendamentos", align: "center" },
        { key: "percentageOfTotal", header: "% do Total", align: "center" }
      ];

      // Preparar os dados de distribuição por hora do dia
      const timeData = data.timeDistribution
        .filter(time => time.count > 0)
        .map(time => {
          return {
            hour: time.label,
            count: time.count,
            percentageOfTotal: time.percentageOfTotal.toFixed(1) + '%'
          };
        });

      // Definição das colunas para distribuição por hora do dia
      const timeColumns = [
        { key: "hour", header: "Horário" },
        { key: "count", header: "Agendamentos", align: "center" },
        { key: "percentageOfTotal", header: "% do Total", align: "center" }
      ];

      // Preparar os dados de distribuição por dia da semana
      const weekdayData = data.weekdayDistribution
        .filter(day => day.count > 0)
        .map(day => {
          return {
            weekday: day.label,
            count: day.count,
            percentageOfTotal: day.percentageOfTotal.toFixed(1) + '%'
          };
        });

      // Definição das colunas para distribuição por dia da semana
      const weekdayColumns = [
        { key: "weekday", header: "Dia da Semana" },
        { key: "count", header: "Agendamentos", align: "center" },
        { key: "percentageOfTotal", header: "% do Total", align: "center" }
      ];

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp} | ${periodLabel}`;
      subtitle += ` | Taxa de Ocupação Geral: ${data.overallOccupancy.toFixed(1)}%`;

      // Preparar os dados para exportação
      const exportData = {
        profissionais: providerData,
        servicos: serviceData,
        locais: locationData,
        horarios: timeData,
        diasSemana: weekdayData
      };



      // Exportar os dados em múltiplas tabelas
      return await exportService.exportData(
        exportData,
        {
          format: exportFormat,
          filename: "analise-ocupacao",
          title: "Análise de Ocupação",
          subtitle,
          multiTable: true,
          tables: [
            { name: "profissionais", title: "Ocupação por Profissional", columns: providerColumns },
            { name: "servicos", title: "Distribuição por Tipo de Serviço", columns: serviceColumns },
            { name: "locais", title: "Distribuição por Local", columns: locationColumns },
            { name: "horarios", title: "Distribuição por Horário", columns: timeColumns },
            { name: "diasSemana", title: "Distribuição por Dia da Semana", columns: weekdayColumns }
          ]
        }
      );
    } catch (error) {
      console.error("Erro ao exportar dados de ocupação:", error);
      return false;
    }
  },

  /**
   * Obtém comparação entre provedores para um tipo de serviço específico
   * @param {string} serviceTypeId ID do tipo de serviço
   * @param {Object} params Parâmetros adicionais
   * @returns {Promise<Object>} Dados de comparação
   */
  getServiceTypeProviderComparison: async (serviceTypeId, params = {}) => {
    try {
      const { period = 'month', date } = params;

      // Calcular o intervalo de datas
      const { startDate, endDate } = getDateRange(period, date ? new Date(date) : undefined);

      // Buscar agendamentos deste tipo de serviço no período
      const appointmentsResponse = await appointmentService.getAppointments({
        serviceTypes: [serviceTypeId],
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        limit: 1000
      });

      const appointments = appointmentsResponse.appointments || [];

      if (appointments.length === 0) {
        return {
          serviceTypeId,
          providerComparison: []
        };
      }

      // Agrupar por provedor
      const providerCounts = {};

      // Obter todos os provedores para ter nomes
      const allProviders = await appointmentService.getProviders();

      appointments.forEach(appointment => {
        const providerId = appointment.userId || appointment.providerId;

        if (providerId) {
          const providerInfo = allProviders.find(p => p.id === providerId) || {
            id: providerId,
            fullName: `Profissional ${providerId.substring(0, 8)}`
          };

          if (!providerCounts[providerId]) {
            providerCounts[providerId] = {
              providerId,
              providerName: providerInfo.fullName,
              count: 0,
              duration: 0
            };
          }

          providerCounts[providerId].count++;

          // Calcular duração
          const start = new Date(appointment.startDate);
          const end = new Date(appointment.endDate);
          const durationHours = (end - start) / (1000 * 60 * 60);
          providerCounts[providerId].duration += durationHours;
        }
      });

      const providerComparison = Object.values(providerCounts)
        .map(provider => ({
          ...provider,
          percentageOfCount: (provider.count / appointments.length) * 100,
          averageDuration: provider.count > 0 ? provider.duration / provider.count : 0
        }))
        .sort((a, b) => b.count - a.count);

      return {
        serviceTypeId,
        providerComparison
      };
    } catch (error) {
      console.error(`Error comparing providers for service type ${serviceTypeId}:`, error);
      throw error;
    }
  }
};