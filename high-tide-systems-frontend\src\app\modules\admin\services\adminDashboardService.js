// src/services/adminDashboardService.js
import { api } from "@/utils/api";
import { exportService } from "@/app/services/exportService";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";

export const adminDashboardService = {
  /**
   * Obtém todos os dados do dashboard em uma única chamada
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getAllDashboardData: async (options = {}) => {
    try {
      const params = new URLSearchParams();
      if (options.companyId) {
        params.append('companyId', options.companyId);
      }



      const response = await api.get(`/adminDashboard/all?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar dados do dashboard:", error);
      throw error;
    }
  },

  /**
   * Obtém apenas as estatísticas gerais (cards do topo)
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getStats: async (options = {}) => {
    try {
      const params = new URLSearchParams();
      if (options.companyId) {
        params.append('companyId', options.companyId);
      }

      const response = await api.get(`/adminDashboard/stats?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar estatísticas:", error);
      throw error;
    }
  },

  /**
   * Obtém dados de atividade para o gráfico principal
   * @param {string} period - Período de tempo (7dias, 30dias, 3meses)
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getActivityData: async (period = '7dias', options = {}) => {
    try {
      const params = new URLSearchParams();
      params.append('period', period);

      if (options.companyId) {
        params.append('companyId', options.companyId);
      }

      const response = await api.get(`/adminDashboard/activity?${params.toString()}`);
      return response.data.activityData;
    } catch (error) {
      console.error("Erro ao buscar dados de atividade:", error);
      return [];
    }
  },

  /**
   * Obtém distribuição de usuários por módulo para o gráfico de pizza
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getUserModuleDistribution: async (options = {}) => {
    try {
      const params = new URLSearchParams();
      if (options.companyId) {
        params.append('companyId', options.companyId);
      }

      const response = await api.get(`/adminDashboard/module-distribution?${params.toString()}`);
      return response.data.moduleDistribution;
    } catch (error) {
      console.error("Erro ao buscar distribuição por módulo:", error);
      return [];
    }
  },

  /**
   * Obtém lista de usuários ativos
   * @param {number} limit - Quantidade de usuários a retornar
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getActiveUsers: async (limit = 5, options = {}) => {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit);

      if (options.companyId) {
        params.append('companyId', options.companyId);
      }

      const response = await api.get(`/adminDashboard/active-users?${params.toString()}`);
      return response.data.users;
    } catch (error) {
      console.error("Erro ao buscar usuários ativos:", error);
      return [];
    }
  },

  /**
   * Obtém lista de atividades recentes
   * @param {number} limit - Quantidade de atividades a retornar
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getRecentActivity: async (limit = 7, options = {}) => {
    try {
      const params = new URLSearchParams();
      params.append('limit', limit);

      if (options.companyId) {
        params.append('companyId', options.companyId);
      }

      const response = await api.get(`/adminDashboard/recent-activity?${params.toString()}`);
      return response.data.activities;
    } catch (error) {
      console.error("Erro ao buscar atividades recentes:", error);
      return [];
    }
  },

  /**
   * Obtém distribuição de profissões por grupo para o gráfico de pizza
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getProfessionDistribution: async (options = {}) => {
    try {
      const params = new URLSearchParams();
      if (options.companyId) {
        params.append('companyId', options.companyId);
      }

      const response = await api.get(`/adminDashboard/profession-distribution?${params.toString()}`);
      return response.data.professionDistribution;
    } catch (error) {
      console.error("Erro ao buscar distribuição de profissões:", error);
      return [];
    }
  },

  /**
   * Obtém informações do sistema
   * @param {Object} options - Opções de filtro
   * @param {string} options.companyId - ID da empresa (opcional para SYSTEM_ADMIN)
   */
  getSystemInfo: async (options = {}) => {
    try {
      const params = new URLSearchParams();
      if (options.companyId) {
        params.append('companyId', options.companyId);
      }

      const response = await api.get(`/adminDashboard/system-info?${params.toString()}`);
      return response.data.systemInfo;
    } catch (error) {
      console.error("Erro ao buscar informações do sistema:", error);
      return {
        version: 'v0.1',
        lastBackup: new Date().toISOString(),
        securityStatus: 'Protegido',
        currentPlan: 'Premium (Anual)',
      };
    }
  },

  /**
   * Formata timestamp em uma string relativa (ex: "2 horas atrás")
   * @param {string} timestamp - Timestamp em formato ISO
   */
  formatTimeAgo: (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now - date;
    const diffMins = Math.round(diffMs / 60000);

    if (diffMins < 1) return "agora mesmo";
    if (diffMins < 60) return `${diffMins} min atrás`;

    const diffHrs = Math.round(diffMins / 60);
    if (diffHrs < 24) return `${diffHrs} h atrás`;

    const diffDays = Math.round(diffHrs / 24);
    if (diffDays === 1) return "ontem";
    if (diffDays < 7) return `${diffDays} dias atrás`;

    return date.toLocaleDateString("pt-BR");
  },

  /**
   * Exporta os dados do dashboard
   * @param {Object} data - Dados do dashboard
   * @param {string} companyName - Nome da empresa (opcional)
   * @param {string} period - Período selecionado
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportDashboardData: async (data, companyName, period, exportFormat = "xlsx") => {
    try {
      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Preparar os dados para exportação - Estatísticas
      const statsData = [
        {
          categoria: "Usuários",
          total: data.statsData.usersTotal,
          ativos: data.statsData.activeUsers,
          crescimento: `${data.growthData.usersGrowth >= 0 ? '+' : ''}${data.growthData.usersGrowth.toFixed(1)}%`
        },
        {
          categoria: "Clientes",
          total: data.statsData.clientsTotal,
          ativos: data.statsData.clientsTotal,
          crescimento: `${data.growthData.clientsGrowth >= 0 ? '+' : ''}${data.growthData.clientsGrowth.toFixed(1)}%`
        },
        {
          categoria: "Agendamentos",
          total: data.statsData.appointmentsTotal,
          ativos: "-",
          crescimento: `${data.growthData.appointmentsGrowth >= 0 ? '+' : ''}${data.growthData.appointmentsGrowth.toFixed(1)}%`
        },
        {
          categoria: "Profissões",
          total: data.statsData.professionsTotal,
          ativos: "-",
          crescimento: `${data.growthData.professionsGrowth >= 0 ? '+' : ''}${data.growthData.professionsGrowth.toFixed(1)}%`
        },
        {
          categoria: "Grupos",
          total: data.statsData.groupsTotal,
          ativos: "-",
          crescimento: `${data.growthData.groupsGrowth >= 0 ? '+' : ''}${data.growthData.groupsGrowth.toFixed(1)}%`
        }
      ];

      // Definição das colunas para estatísticas
      const statsColumns = [
        { key: "categoria", header: "Categoria" },
        { key: "total", header: "Total", align: "center" },
        { key: "ativos", header: "Ativos", align: "center" },
        { key: "crescimento", header: "Crescimento", align: "center" }
      ];

      // Preparar os dados de atividade
      const activityColumns = [
        { key: "name", header: "Data" },
        { key: "usuarios", header: "Usuários", align: "center" },
        { key: "clientes", header: "Clientes", align: "center" },
        { key: "agendamentos", header: "Agendamentos", align: "center" }
      ];

      // Preparar os dados de distribuição de módulos
      const moduleColumns = [
        { key: "name", header: "Módulo" },
        { key: "value", header: "Quantidade", align: "center" },
        { key: "percentage", header: "Porcentagem",
          format: (value) => `${value.toFixed(1)}%`,
          align: "center"
        }
      ];

      // Calcular porcentagens para os dados de módulos
      const totalUsers = data.userModuleData.reduce((sum, item) => sum + item.value, 0);
      const moduleData = data.userModuleData.map(item => ({
        ...item,
        percentage: (item.value / totalUsers) * 100
      }));

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (companyName) {
        subtitle += ` | Empresa: ${companyName}`;
      }
      subtitle += ` | Período: ${period === '7dias' ? 'Últimos 7 dias' : period === '30dias' ? 'Últimos 30 dias' : 'Últimos 3 meses'}`;

      // Preparar os dados para exportação
      const exportData = {
        estatisticas: statsData,
        atividade: data.activityData || [],
        distribuicaoModulos: moduleData
      };



      // Exportar os dados em múltiplas tabelas
      return await exportService.exportData(
        exportData,
        {
          format: exportFormat,
          filename: "dashboard-administracao",
          title: "Dashboard de Administração",
          subtitle,
          multiTable: true,
          tables: [
            { name: "estatisticas", title: "Estatísticas Gerais", columns: statsColumns },
            { name: "atividade", title: `Atividade no Sistema`, columns: activityColumns },
            { name: "distribuicaoModulos", title: "Distribuição de Usuários por Módulo", columns: moduleColumns }
          ]
        }
      );
    } catch (error) {
      console.error("Erro ao exportar dados do dashboard:", error);
      return false;
    }
  }
};

export default adminDashboardService;