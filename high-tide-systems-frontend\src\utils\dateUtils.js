/**
 * Utilitário para formatação de datas
 * Este módulo fornece funções para formatar datas de forma consistente
 */
import { format, parseISO, isValid } from 'date-fns';
import { ptBR } from 'date-fns/locale';

/**
 * Formata uma data para exibição
 * @param {string|Date} dateValue - A data a ser formatada
 * @param {string} formatString - O formato desejado (padrão: dd/MM/yyyy)
 * @param {Object} options - Opções adicionais
 * @param {string} options.defaultValue - Valor padrão para datas inválidas (padrão: 'N/A')
 * @returns {string} Data formatada
 */
export const formatDate = (dateValue, formatString = 'dd/MM/yyyy', options = {}) => {
  const { defaultValue = 'N/A' } = options;
  
  if (!dateValue) {
    return defaultValue;
  }
  
  try {
    // Se for string, tentar converter para Date
    const date = typeof dateValue === 'string' ? parseISO(dateValue) : dateValue;
    
    // Verificar se a data é válida
    if (!isValid(date)) {
      return defaultValue;
    }
    
    return format(date, formatString, { locale: ptBR });
  } catch (error) {
    console.error('Erro ao formatar data:', error);
    return defaultValue;
  }
};

/**
 * Formata uma data e hora para exibição
 * @param {string|Date} dateValue - A data a ser formatada
 * @param {Object} options - Opções adicionais
 * @returns {string} Data e hora formatadas
 */
export const formatDateTime = (dateValue, options = {}) => {
  return formatDate(dateValue, 'dd/MM/yyyy HH:mm', options);
};

/**
 * Formata uma data para ISO 8601 (para envio à API)
 * @param {Date} date - A data a ser formatada
 * @returns {string|null} Data formatada em ISO 8601 ou null se inválida
 */
export const formatToISO = (date) => {
  if (!date || !isValid(date)) {
    return null;
  }
  
  return date.toISOString();
};

/**
 * Verifica se uma data é válida
 * @param {string|Date} dateValue - A data a ser verificada
 * @returns {boolean} Verdadeiro se a data for válida
 */
export const isValidDate = (dateValue) => {
  if (!dateValue) {
    return false;
  }
  
  try {
    const date = typeof dateValue === 'string' ? parseISO(dateValue) : dateValue;
    return isValid(date);
  } catch (error) {
    return false;
  }
};
