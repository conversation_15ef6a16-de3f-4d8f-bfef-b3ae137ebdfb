// <PERSON><PERSON>, vamos definir a estrutura de permissões
// src/app/modules/admin/permissions/permissionsConfig.js

/**
 * Configuração das permissões do sistema
 * Estrutura: {
 *   [moduleId]: {
 *     name: 'Nome exibido do módulo',
 *     permissions: [
 *       {
 *         id: 'identificador_unico',
 *         name: 'Nome da permissão',
 *         description: 'Descrição detalhada'
 *       }
 *     ]
 *   }
 * }
 *
 * O formato de cada permissão será: 'module.action'
 * Exemplos: 'rh.view', 'financial.payroll.edit'
 */

export const PERMISSIONS_CONFIG = {
    // Módulo de Administração
    ADMIN: {
      name: 'Administra<PERSON>',
      icon: 'Settings',
      permissions: [
        {
          id: 'admin.users.view',
          name: 'Visualizar usuários',
          description: 'Permite visualizar a lista de usuários do sistema'
        },
        {
          id: 'admin.users.create',
          name: '<PERSON>ria<PERSON> usuá<PERSON>',
          description: 'Permite criar novos usuários no sistema'
        },
        {
          id: 'admin.users.edit',
          name: 'Editar usuários',
          description: 'Permite editar informações de usuários existentes'
        },
        {
          id: 'admin.users.delete',
          name: 'Remover usuários',
          description: 'Permite remover usuários do sistema'
        },
        {
          id: 'admin.permissions.manage',
          name: 'Gerenciar permissões',
          description: 'Permite gerenciar permissões de outros usuários'
        },
        {
          id: 'admin.logs.view',
          name: 'Visualizar logs',
          description: 'Permite visualizar logs de atividades do sistema'
        },
        {
          id: 'admin.config.edit',
          name: 'Editar configurações',
          description: 'Permite editar configurações gerais do sistema'
        },
        {
          id: 'admin.professions.view',
          name: 'Visualizar profissões',
          description: 'Permite visualizar a lista de profissões'
        },
        {
          id: 'admin.professions.create',
          name: 'Criar profissões',
          description: 'Permite criar novas profissões'
        },
        {
          id: 'admin.professions.edit',
          name: 'Editar profissões',
          description: 'Permite editar profissões existentes'
        },
        {
          id: 'admin.professions.delete',
          name: 'Excluir profissões',
          description: 'Permite excluir profissões'
        },
        {
          id: 'admin.profession-groups.view',
          name: 'Visualizar grupos de profissões',
          description: 'Permite visualizar a lista de grupos de profissões'
        },
        {
          id: 'admin.profession-groups.create',
          name: 'Criar grupos de profissões',
          description: 'Permite criar novos grupos de profissões'
        },
        {
          id: 'admin.profession-groups.edit',
          name: 'Editar grupos de profissões',
          description: 'Permite editar grupos de profissões existentes'
        },
        {
          id: 'admin.profession-groups.delete',
          name: 'Excluir grupos de profissões',
          description: 'Permite excluir grupos de profissões'
        }
      ]
    },

    // Módulo de RH
    RH: {
      name: 'Recursos Humanos',
      icon: 'Users',
      permissions: [
        {
          id: 'rh.dashboard.view',
          name: 'Visualizar dashboard',
          description: 'Permite visualizar o dashboard de RH'
        },
        {
          id: 'rh.employees.view',
          name: 'Visualizar funcionários',
          description: 'Permite visualizar a lista de funcionários'
        },
        {
          id: 'rh.employees.create',
          name: 'Adicionar funcionários',
          description: 'Permite adicionar novos funcionários'
        },
        {
          id: 'rh.employees.edit',
          name: 'Editar funcionários',
          description: 'Permite editar informações de funcionários'
        },
        {
          id: 'rh.payroll.view',
          name: 'Visualizar folha de pagamento',
          description: 'Permite visualizar a folha de pagamento'
        },
        {
          id: 'rh.payroll.manage',
          name: 'Gerenciar folha de pagamento',
          description: 'Permite gerenciar a folha de pagamento'
        },
        {
          id: 'rh.benefits.view',
          name: 'Visualizar benefícios',
          description: 'Permite visualizar benefícios de funcionários'
        },
        {
          id: 'rh.benefits.manage',
          name: 'Gerenciar benefícios',
          description: 'Permite gerenciar benefícios de funcionários'
        },
        {
          id: 'rh.attendance.view',
          name: 'Visualizar ponto',
          description: 'Permite visualizar registros de ponto'
        },
        {
          id: 'rh.attendance.manage',
          name: 'Gerenciar ponto',
          description: 'Permite gerenciar registros de ponto'
        }
      ]
    },

    // Módulo Financeiro
    FINANCIAL: {
      name: 'Financeiro',
      icon: 'DollarSign',
      permissions: [
        {
          id: 'financial.dashboard.view',
          name: 'Visualizar dashboard',
          description: 'Permite visualizar o dashboard financeiro'
        },
        {
          id: 'financial.invoices.view',
          name: 'Visualizar faturas',
          description: 'Permite visualizar faturas'
        },
        {
          id: 'financial.invoices.create',
          name: 'Criar faturas',
          description: 'Permite criar novas faturas'
        },
        {
          id: 'financial.invoices.edit',
          name: 'Editar faturas',
          description: 'Permite editar faturas existentes'
        },
        {
          id: 'financial.payments.view',
          name: 'Visualizar pagamentos',
          description: 'Permite visualizar pagamentos'
        },
        {
          id: 'financial.payments.process',
          name: 'Processar pagamentos',
          description: 'Permite processar pagamentos'
        },
        {
          id: 'financial.expenses.view',
          name: 'Visualizar despesas',
          description: 'Permite visualizar despesas'
        },
        {
          id: 'financial.expenses.manage',
          name: 'Gerenciar despesas',
          description: 'Permite gerenciar despesas'
        },
        {
          id: 'financial.reports.view',
          name: 'Visualizar relatórios',
          description: 'Permite visualizar relatórios financeiros'
        },
        {
          id: 'financial.reports.export',
          name: 'Exportar relatórios',
          description: 'Permite exportar relatórios financeiros'
        }
      ]
    },

    // Módulo de Agendamento
    SCHEDULING: {
      name: 'Agendamento',
      icon: 'Calendar',
      permissions: [
        {
          id: 'scheduling.calendar.view',
          name: 'Visualizar calendário',
          description: 'Permite visualizar o calendário de agendamentos'
        },
        {
          id: 'scheduling.calendar.create',
          name: 'Criar agendamentos',
          description: 'Permite criar novos agendamentos no calendário'
        },
        {
          id: 'scheduling.calendar.edit',
          name: 'Editar agendamentos',
          description: 'Permite editar agendamentos existentes'
        },
        {
          id: 'scheduling.calendar.delete',
          name: 'Excluir agendamentos',
          description: 'Permite excluir agendamentos do calendário'
        },
        {
          id: 'scheduling.working-hours.view',
          name: 'Visualizar horários de trabalho',
          description: 'Permite visualizar horários de trabalho'
        },
        {
          id: 'scheduling.working-hours.manage',
          name: 'Gerenciar horários de trabalho',
          description: 'Permite gerenciar horários de trabalho'
        },
        {
          id: 'scheduling.service-types.view',
          name: 'Visualizar tipos de serviço',
          description: 'Permite visualizar tipos de serviço'
        },
        {
          id: 'scheduling.service-types.create',
          name: 'Criar tipos de serviço',
          description: 'Permite criar novos tipos de serviço'
        },
        {
          id: 'scheduling.service-types.edit',
          name: 'Editar tipos de serviço',
          description: 'Permite editar tipos de serviço existentes'
        },
        {
          id: 'scheduling.service-types.delete',
          name: 'Excluir tipos de serviço',
          description: 'Permite excluir tipos de serviço'
        },
        {
          id: 'scheduling.locations.view',
          name: 'Visualizar localizações',
          description: 'Permite visualizar localizações'
        },
        {
          id: 'scheduling.locations.create',
          name: 'Criar localizações',
          description: 'Permite criar novas localizações'
        },
        {
          id: 'scheduling.locations.edit',
          name: 'Editar localizações',
          description: 'Permite editar localizações existentes'
        },
        {
          id: 'scheduling.locations.delete',
          name: 'Excluir localizações',
          description: 'Permite excluir localizações'
        },
        {
          id: 'scheduling.occupancy.view',
          name: 'Visualizar ocupação',
          description: 'Permite visualizar a ocupação dos profissionais'
        },
        {
          id: 'scheduling.appointments-report.view',
          name: 'Visualizar relatório de agendamentos',
          description: 'Permite visualizar o relatório de agendamentos'
        },
        {
          id: 'scheduling.appointments-report.export',
          name: 'Exportar relatório de agendamentos',
          description: 'Permite exportar o relatório de agendamentos'
        },
        {
          id: 'scheduling.appointments-dashboard.view',
          name: 'Visualizar dashboard de agendamentos',
          description: 'Permite visualizar o dashboard de agendamentos'
        },
        {
          id: 'scheduling.rooms.manage',
          name: 'Gerenciar salas',
          description: 'Permite gerenciar salas para agendamentos'
        },
        {
          id: 'scheduling.resources.manage',
          name: 'Gerenciar recursos',
          description: 'Permite gerenciar recursos para agendamentos'
        }
      ]
    },

    // Módulo de Pessoas
    PEOPLE: {
      name: 'Pessoas',
      icon: 'UserCheck',
      permissions: [
        {
          id: 'people.clients.view',
          name: 'Visualizar clientes',
          description: 'Permite visualizar a lista de clientes'
        },
        {
          id: 'people.clients.create',
          name: 'Criar clientes',
          description: 'Permite criar novos clientes'
        },
        {
          id: 'people.clients.edit',
          name: 'Editar clientes',
          description: 'Permite editar informações de clientes existentes'
        },
        {
          id: 'people.clients.delete',
          name: 'Excluir clientes',
          description: 'Permite excluir clientes'
        },
        {
          id: 'people.persons.view',
          name: 'Visualizar pacientes',
          description: 'Permite visualizar a lista de pacientes'
        },
        {
          id: 'people.persons.create',
          name: 'Criar pacientes',
          description: 'Permite criar novos pacientes'
        },
        {
          id: 'people.persons.edit',
          name: 'Editar pacientes',
          description: 'Permite editar informações de pacientes existentes'
        },
        {
          id: 'people.persons.delete',
          name: 'Excluir pacientes',
          description: 'Permite excluir pacientes'
        },
        {
          id: 'people.insurances.view',
          name: 'Visualizar convênios',
          description: 'Permite visualizar a lista de convênios'
        },
        {
          id: 'people.insurances.create',
          name: 'Criar convênios',
          description: 'Permite criar novos convênios'
        },
        {
          id: 'people.insurances.edit',
          name: 'Editar convênios',
          description: 'Permite editar informações de convênios existentes'
        },
        {
          id: 'people.insurances.delete',
          name: 'Excluir convênios',
          description: 'Permite excluir convênios'
        },
        {
          id: 'people.insurance-limits.view',
          name: 'Visualizar limites de convênio',
          description: 'Permite visualizar limites de convênio'
        },
        {
          id: 'people.insurance-limits.create',
          name: 'Criar limites de convênio',
          description: 'Permite criar novos limites de convênio'
        },
        {
          id: 'people.insurance-limits.edit',
          name: 'Editar limites de convênio',
          description: 'Permite editar limites de convênio existentes'
        },
        {
          id: 'people.insurance-limits.delete',
          name: 'Excluir limites de convênio',
          description: 'Permite excluir limites de convênio'
        }
      ]
    },

    // Módulo Básico
    BASIC: {
      name: 'Básico',
      icon: 'CheckSquare',
      permissions: [
        {
          id: 'basic.profile.view',
          name: 'Visualizar perfil',
          description: 'Permite visualizar seu próprio perfil'
        },
        {
          id: 'basic.profile.edit',
          name: 'Editar perfil',
          description: 'Permite editar seu próprio perfil'
        }
      ]
    }
  };

  // Obter todas as permissões disponíveis
  export const getAllPermissions = () => {
    const allPermissions = [];

    Object.keys(PERMISSIONS_CONFIG).forEach(moduleId => {
      const module = PERMISSIONS_CONFIG[moduleId];
      module.permissions.forEach(permission => {
        allPermissions.push({
          ...permission,
          moduleId
        });
      });
    });

    return allPermissions;
  };

  // Verificar se um usuário tem uma permissão específica
  export const hasPermission = (user, permissionId) => {
    if (!user || !user.permissions) return false;

    // System Admin tem todas as permissões
    if (user.role === 'SYSTEM_ADMIN') return true;

    // Verifica se o usuário tem a permissão específica
    return user.permissions.includes(permissionId);
  };

  // Função para verificar se o usuário tem acesso a um módulo
  export const hasModuleAccess = (user, moduleId) => {
    if (!user || !user.modules) return false;

    // System Admin tem acesso a todos os módulos
    if (user.role === 'SYSTEM_ADMIN') return true;

    return user.modules.includes(moduleId);
  };

  // Função para obter todas as permissões de um módulo
  export const getModulePermissions = (moduleId) => {
    if (!PERMISSIONS_CONFIG[moduleId]) return [];
    return PERMISSIONS_CONFIG[moduleId].permissions || [];
  };

  // Função para agrupar permissões por módulo
  export const groupPermissionsByModule = (permissions) => {
    const grouped = {};

    permissions.forEach(permissionId => {
      const [modulePrefix] = permissionId.split('.');
      const moduleId = Object.keys(PERMISSIONS_CONFIG).find(
        m => modulePrefix === m.toLowerCase()
      );

      if (moduleId) {
        if (!grouped[moduleId]) {
          grouped[moduleId] = [];
        }
        grouped[moduleId].push(permissionId);
      }
    });

    return grouped;
  };