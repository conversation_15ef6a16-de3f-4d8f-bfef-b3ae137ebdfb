// src/routes/personRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const profileImageUpload = require('../../middlewares/profileImageUpload');
const { PersonController, createPersonValidation } = require('../../controllers/personController');

// All routes require authentication
router.use(authenticate);

// Person CRUD routes
router.post('/', createPersonValidation, PersonController.create);
router.get('/', PersonController.list);
router.get('/:id', PersonController.get);
router.put('/:id', createPersonValidation, PersonController.update);
router.patch('/:id/status', PersonController.toggleStatus);
router.delete('/:id', PersonController.delete);

// Person insurance management
router.post('/insurance', PersonController.addInsurance);
router.get('/:personId/insurances', PersonController.listInsurances);
router.put('/:personId/insurance/:insuranceId', PersonController.updateInsurance);
router.delete('/:personId/insurance/:insuranceId', PersonController.removeInsurance);

// Profile image management
router.post('/:id/profile-image', profileImageUpload.single('profileImage'), PersonController.uploadProfileImage);
router.get('/:id/profile-image', PersonController.getProfileImage);

module.exports = router;