// tests/controllers/scheduling-controller-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Variáveis para armazenar IDs criados durante os testes
let personId;
let locationId;
let serviceTypeId;
let schedulingId;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do controlador de agendamentos...');
  
  try {
    // Pré-requisito: Obter IDs necessários para criar um agendamento
    console.log('\nObtendo dados necessários para os testes...');
    
    // Obter uma pessoa
    const personsResponse = await api.get('/persons');
    if (personsResponse.data.length === 0) {
      console.log('❌ Não há pessoas cadastradas para realizar os testes');
      return;
    }
    personId = personsResponse.data[0].id;
    console.log(`✅ Pessoa encontrada: ${personId}`);
    
    // Obter um local
    const locationsResponse = await api.get('/locations');
    if (locationsResponse.data.length === 0) {
      console.log('❌ Não há locais cadastrados para realizar os testes');
      return;
    }
    locationId = locationsResponse.data[0].id;
    console.log(`✅ Local encontrado: ${locationId}`);
    
    // Obter um tipo de serviço
    const serviceTypesResponse = await api.get('/service-types');
    if (serviceTypesResponse.data.length === 0) {
      console.log('❌ Não há tipos de serviço cadastrados para realizar os testes');
      return;
    }
    serviceTypeId = serviceTypesResponse.data[0].id;
    console.log(`✅ Tipo de serviço encontrado: ${serviceTypeId}`);
    
    // Teste 1: Criar um novo agendamento
    console.log('\n1. Criando agendamento...');
    
    // Dados para o agendamento
    const startDate = new Date();
    startDate.setHours(startDate.getHours() + 1);
    startDate.setMinutes(0, 0, 0);
    
    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + 1);
    
    const testScheduling = {
      personId,
      userId: TEST_TOKEN.replace('TEST_TOKEN_', ''),
      creatorId: TEST_TOKEN.replace('TEST_TOKEN_', ''),
      locationId,
      serviceTypeId,
      title: 'Teste de Agendamento',
      description: 'Descrição do teste de agendamento',
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      status: 'PENDING'
    };
    
    try {
      const createResponse = await api.post('/schedulings', testScheduling);
      
      if (createResponse.status === 201) {
        console.log('✅ Agendamento criado com sucesso!');
        schedulingId = createResponse.data.id;
        console.log(`ID do agendamento: ${schedulingId}`);
      } else {
        console.log('❌ Falha ao criar agendamento');
        return;
      }
    } catch (error) {
      console.log('❌ Falha ao criar agendamento:', error.response?.data?.message || error.message);
      // Continuar com os testes de listagem mesmo se a criação falhar
    }
    
    // Teste 2: Listar agendamentos
    console.log('\n2. Listando agendamentos...');
    const listResponse = await api.get('/schedulings');
    
    if (listResponse.status === 200) {
      console.log(`✅ ${listResponse.data.length} agendamentos encontrados`);
    } else {
      console.log('❌ Falha ao listar agendamentos');
    }
    
    // Se conseguimos criar um agendamento, continuar com os testes
    if (schedulingId) {
      // Teste 3: Obter agendamento por ID
      console.log('\n3. Obtendo agendamento por ID...');
      const getResponse = await api.get(`/schedulings/${schedulingId}`);
      
      if (getResponse.status === 200) {
        console.log('✅ Agendamento obtido com sucesso!');
        console.log(`Título: ${getResponse.data.title}`);
        console.log(`Status: ${getResponse.data.status}`);
      } else {
        console.log('❌ Falha ao obter agendamento');
      }
      
      // Teste 4: Atualizar agendamento
      console.log('\n4. Atualizando agendamento...');
      const updateResponse = await api.put(`/schedulings/${schedulingId}`, {
        title: 'Teste de Agendamento Atualizado',
        description: 'Descrição atualizada'
      });
      
      if (updateResponse.status === 200) {
        console.log('✅ Agendamento atualizado com sucesso!');
        console.log(`Novo título: ${updateResponse.data.title}`);
      } else {
        console.log('❌ Falha ao atualizar agendamento');
      }
      
      // Teste 5: Cancelar agendamento
      console.log('\n5. Cancelando agendamento...');
      const cancelResponse = await api.put(`/schedulings/${schedulingId}`, {
        status: 'CANCELLED'
      });
      
      if (cancelResponse.status === 200) {
        console.log('✅ Agendamento cancelado com sucesso!');
        console.log(`Status: ${cancelResponse.data.status}`);
      } else {
        console.log('❌ Falha ao cancelar agendamento');
      }
      
      // Teste 6: Excluir agendamento
      console.log('\n6. Excluindo agendamento...');
      const deleteResponse = await api.delete(`/schedulings/${schedulingId}`);
      
      if (deleteResponse.status === 200) {
        console.log('✅ Agendamento excluído com sucesso!');
      } else {
        console.log('❌ Falha ao excluir agendamento');
      }
    }
    
    console.log('\n✅ Testes do controlador de agendamentos concluídos!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
