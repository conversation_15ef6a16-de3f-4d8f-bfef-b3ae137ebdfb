"use client";

import React, { useState } from "react";
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  FileText,
  Settings,
  LayoutDashboard,
  Info,
  Building,
  CheckCircle,
  ArrowRight,
  PieChart,
  Activity,
  Play,
  Pause,
  BarChart4,
  LineChart,
  Tag,
  Briefcase
} from "lucide-react";
import { ModuleHeader } from "@/components/ui";

const IntroductionPage = () => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Info size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Introdução
        </h1>
      </div>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-scheduler-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-purple-600 to-purple-400 dark:from-purple-700 dark:to-purple-600 px-6 py-4">
          <div className="flex items-center">
            <Calendar className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo de Agendamento</h2>
          </div>
        </div>

        {/* Introduction text and video */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo de Agendamento do High Tide Systems. Este módulo é o centro de gerenciamento de agendamentos,
                permitindo organizar consultas, reuniões e compromissos de forma eficiente.
                Aqui você encontrará todas as ferramentas necessárias para gerenciar sua agenda e a de seus profissionais.
              </p>

              {/* Key features */}
              <div className="bg-purple-50 dark:bg-gray-700 rounded-lg p-4 border border-purple-200 dark:border-gray-600">
                <h3 className="font-semibold text-purple-800 dark:text-white mb-3 flex items-center">
                  <CheckCircle className="mr-2 text-purple-600 dark:text-purple-300" size={18} />
                  Principais Recursos
                </h3>
                <ul className="space-y-2 text-sm text-gray-700 dark:text-gray-300">
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0">1</span>
                    <span>Visualização de calendário diário, semanal e mensal</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0">2</span>
                    <span>Agendamento rápido com verificação de disponibilidade</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0">3</span>
                    <span>Configuração de horários de trabalho dos profissionais</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0">4</span>
                    <span>Gerenciamento de locais e salas de atendimento</span>
                  </li>
                  <li className="flex items-start">
                    <span className="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-2 flex-shrink-0">5</span>
                    <span>Relatórios detalhados de agendamentos</span>
                  </li>
                </ul>
              </div>
            </div>

            <div>
              {/* Interactive demo/video placeholder */}
              <div className="bg-gradient-to-r from-purple-700 to-purple-500 dark:from-purple-800 dark:to-purple-600 rounded-lg overflow-hidden shadow-lg h-80 relative">
                {isVideoPlaying ? (
                  <div className="absolute inset-0 bg-black/80 flex items-center justify-center">
                    <button
                      onClick={() => setIsVideoPlaying(false)}
                      className="absolute top-4 right-4 bg-white/20 hover:bg-white/30 rounded-full p-2 text-white transition-colors"
                    >
                      <Pause size={20} />
                    </button>

                    {/* Animation container */}
                    <div className="w-full h-full flex items-center justify-center">
                      <div className="ai-video-content w-4/5 h-4/5 relative">
                        {/* Scheduler module overview animation */}
                        <div className="absolute inset-0 flex flex-col items-center justify-center ai-slide" data-slide="1">
                          <div className="text-white text-xl font-bold mb-6">Módulo de Agendamento</div>
                          <div className="flex space-x-8 mb-8">
                            <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                              <div className="w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2">
                                <Calendar size={32} className="text-purple-300" />
                              </div>
                              <span className="text-purple-200 text-sm">Calendário</span>
                            </div>
                            <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                              <div className="w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2">
                                <Clock size={32} className="text-purple-300" />
                              </div>
                              <span className="text-purple-200 text-sm">Horários</span>
                            </div>
                            <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                              <div className="w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2">
                                <MapPin size={32} className="text-purple-300" />
                              </div>
                              <span className="text-purple-200 text-sm">Locais</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <style jsx>{`
                      .ai-video-content {
                        position: relative;
                        overflow: hidden;
                      }
                      .ai-slide {
                        animation: fadeIn 0.5s ease-in-out;
                      }
                      @keyframes fadeIn {
                        from { opacity: 0; transform: translateY(10px); }
                        to { opacity: 1; transform: translateY(0); }
                      }
                    `}</style>
                  </div>
                ) : (
                  <div className="text-center p-6">
                    <div className="w-20 h-20 rounded-full bg-primary-500/20 flex items-center justify-center mx-auto mb-4 hover:bg-primary-500/30 transition-colors cursor-pointer">
                      <Play size={36} className="text-primary-500 ml-1" />
                    </div>
                    <p className="text-white text-sm mb-2">Clique para iniciar a demonstração interativa</p>
                    <p className="text-purple-200 text-xs">Visualize as principais funcionalidades do módulo</p>
                  </div>
                )}
              </div>
            </div>
          </div>



          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-purple-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Calendar section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Calendar className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Calendário</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize e gerencie todos os agendamentos em uma interface intuitiva de calendário.
                      Alterne entre visualizações diárias, semanais e mensais para melhor organização.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Visualização de calendário;
                        Criação rápida de agendamentos; Filtros avançados; Exportação de dados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Calendar size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Working Hours section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Clock className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Horários de Trabalho</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Configure os horários de disponibilidade dos profissionais para agendamentos.
                      Defina horários personalizados para cada dia da semana.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Configuração de horários;
                        Seleção por arraste; Cópia de horários; Visualização por profissional.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Clock size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Locations section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <MapPin className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Locais e Salas</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os locais e salas disponíveis para agendamentos.
                      Organize os atendimentos por unidade, andar ou tipo de sala.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de locais;
                        Vinculação com unidades; Configuração de capacidade; Status de disponibilidade.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <MapPin size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Service Types section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Tag className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Tipos de Serviço</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie os diferentes tipos de serviços oferecidos pela sua clínica.
                      Configure preços, duração e profissionais habilitados para cada serviço.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Cadastro de serviços;
                        Definição de preços; Configuração de duração; Vinculação com profissionais.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Tag size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Occupancy section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Briefcase className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Ocupação</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Analise detalhadamente a ocupação dos profissionais, salas e horários.
                      Identifique períodos de maior demanda e otimize a agenda.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Análise de ocupação;
                        Gráficos por período; Filtros por profissional; Exportação de dados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <Briefcase size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Reports section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <FileText className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Relatórios</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Acesse relatórios detalhados sobre agendamentos, ocupação de salas,
                      produtividade de profissionais e muito mais.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Relatórios personalizáveis;
                        Exportação em diversos formatos; Listagem de agendamentos; Filtros avançados.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <FileText size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Dashboard section */}
            <div className="bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-purple-100 dark:bg-gray-600 px-4 py-3 border-b border-purple-200 dark:border-gray-500">
                <div className="flex items-center">
                  <LayoutDashboard className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
                  <h3 className="font-semibold text-purple-800 dark:text-white">Dashboard</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize estatísticas e indicadores de desempenho dos agendamentos.
                      Acompanhe métricas importantes para a gestão da sua clínica.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-purple-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Gráficos interativos;
                        Indicadores de desempenho; Análise de tendências; Filtros por período.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                      <BarChart4 size={32} className="text-purple-500 dark:text-purple-300" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Getting started section */}
          <div className="mt-8 bg-purple-50 dark:bg-gray-700 rounded-lg border border-purple-200 dark:border-gray-600 p-6">
            <h3 className="text-lg font-semibold text-purple-800 dark:text-white mb-4 flex items-center">
              <LayoutDashboard className="mr-2 text-purple-600 dark:text-purple-300" size={20} />
              Começando
            </h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Para começar a utilizar o módulo de agendamento, recomendamos seguir estes passos:
            </p>
            <ol className="space-y-3 text-gray-600 dark:text-gray-300">
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">1</span>
                <span>Verifique os <strong>horários de trabalho</strong> dos profissionais para garantir que estejam corretamente configurados.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">2</span>
                <span>Confira os <strong>locais e salas</strong> disponíveis para agendamentos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">3</span>
                <span>Acesse o <strong>calendário</strong> para visualizar e criar novos agendamentos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-300 mr-3 flex-shrink-0">4</span>
                <span>Utilize os <strong>relatórios</strong> para acompanhar e analisar os agendamentos realizados.</span>
              </li>
            </ol>
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => window.location.href = '/dashboard/scheduler/calendar'}
                className="px-5 py-2.5 bg-purple-600 hover:bg-purple-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <Calendar size={18} />
                Ir para o Calendário
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
