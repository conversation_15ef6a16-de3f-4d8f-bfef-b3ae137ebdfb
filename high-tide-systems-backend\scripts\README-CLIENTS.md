# Script de Cadastro de Clientes

Este script cadastra clientes para todas as empresas existentes no sistema, utilizando os convênios previamente cadastrados.

## Características

- Cria entre 10 e 20 clientes aleatórios para cada empresa
- Utiliza CEPs reais para buscar endereços completos via API ViaCEP
- Associa 1 ou 2 convênios aleatórios a cada cliente (se a empresa tiver convênios)
- Gera dados realistas como CPF válido, telefone, email, etc.
- Verifica duplicidade para não criar clientes repetidos

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-clients-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed-clients.js
```

## Detalhes do Script

O script realiza as seguintes operações:

1. Busca todas as empresas ativas no sistema (exceto a empresa de teste)
2. Para cada empresa, busca um usuário administrador para ser o criador dos clientes
3. Gera entre 10 e 20 clientes para cada empresa com:
   - Nome completo aleatório
   - CPF válido gerado algoritmicamente
   - Email baseado no nome
   - Login baseado no nome
   - Telefone aleatório
   - Data de nascimento entre 18 e 80 anos
   - Endereço completo obtido a partir de CEPs reais via API ViaCEP
4. Associa 1 ou 2 convênios aleatórios a cada cliente (se a empresa tiver convênios)
5. Cria uma pessoa titular associada a cada cliente

## CEPs Utilizados

O script utiliza CEPs reais de diversas cidades brasileiras:

- São Paulo - SP: 01310-200 (Av. Paulista)
- Rio de Janeiro - RJ: 22031-001 (Copacabana)
- Belo Horizonte - MG: 30130-110 (Centro)
- Salvador - BA: 40010-020 (Centro)
- Recife - PE: 50030-230 (Boa Vista)
- Porto Alegre - RS: 90010-280 (Centro)
- Curitiba - PR: 80010-010 (Centro)
- Fortaleza - CE: 60060-170 (Aldeota)
- Brasília - DF: 70070-120 (Asa Sul)
- Manaus - AM: 69010-060 (Centro)
- E outros...

## Observações

- A senha padrão para todos os clientes criados é: `123456`
- O script verifica se o cliente já existe pelo email, login ou CPF antes de criar
- Os convênios são associados tanto ao cliente quanto à pessoa titular
- Cada cliente tem uma pessoa titular associada com o mesmo nome e dados de contato
