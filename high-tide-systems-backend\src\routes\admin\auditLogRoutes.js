// src/routes/auditLogRoutes.js
const express = require('express');
const router = express.Router();
const AuditLogController = require('../../controllers/auditLogController');
const { authenticate } = require('../../middlewares/auth');
const systemAdminMiddleware = require('../../middlewares/systemAdmin');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas disponíveis para todos os usuários autenticados
router.get('/', AuditLogController.list);
router.get('/export', AuditLogController.export);
router.get('/stats', AuditLogController.getStats);
router.get('/:id', AuditLogController.getDetails);

// Rotas que requerem privilégios de administrador do sistema
router.post('/cleanup', systemAdminMiddleware, AuditLogController.cleanupOldLogs);

module.exports = router;