// src/app/modules/people/services/contactsService.js
import { api } from "@/utils/api";

export const contactsService = {
  // Get contacts for a person
  getContactsByPerson: async (personId) => {
    try {
      const response = await api.get("/contacts", {
        params: {
          personId
        }
      });
      
      return response.data;
    } catch (error) {
      console.error(`Error fetching contacts for person ${personId}:`, error);
      throw error;
    }
  },

  // Get a single contact by ID
  getContact: async (id) => {
    try {
      const response = await api.get(`/contacts/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching contact ${id}:`, error);
      throw error;
    }
  },

  // Create a new contact
  createContact: async (contactData) => {
    try {
      const response = await api.post("/contacts", contactData);
      return response.data;
    } catch (error) {
      console.error("Error creating contact:", error);
      throw error;
    }
  },

  // Update an existing contact
  updateContact: async (id, contactData) => {
    try {
      const response = await api.put(`/contacts/${id}`, contactData);
      return response.data;
    } catch (error) {
      console.error(`Error updating contact ${id}:`, error);
      throw error;
    }
  },

  // Delete a contact
  deleteContact: async (id) => {
    try {
      await api.delete(`/contacts/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting contact ${id}:`, error);
      throw error;
    }
  },
  
  // Get relationship options for dropdowns
  getRelationshipOptions: () => {
    return [
      { value: "Pai", label: "Pai" },
      { value: "Mãe", label: "Mãe" },
      { value: "Filho(a)", label: "Filho(a)" },
      { value: "Irmão(ã)", label: "Irmão(ã)" },
      { value: "Cônjuge", label: "Cônjuge" },
      { value: "Amigo(a)", label: "Amigo(a)" },
      { value: "Colega", label: "Colega" },
      { value: "Vizinho(a)", label: "Vizinho(a)" },
      { value: "Responsável", label: "Responsável" },
      { value: "Outro", label: "Outro" }
    ];
  }
};

export default contactsService;