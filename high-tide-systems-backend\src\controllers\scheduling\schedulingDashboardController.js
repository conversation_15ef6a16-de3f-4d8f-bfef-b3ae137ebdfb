const prisma = require("../../utils/prisma");

class SchedulingDashboardController {
  static async getDashboardData(req, res) {
    try {
      const {
        period = "30days",
        startDate: startDateParam,
        endDate: endDateParam
      } = req.query;

      let startDate, endDate;

      // Verificar se foram fornecidas datas personalizadas
      if (startDateParam && endDateParam) {
        // Usar datas explícitas se fornecidas
        startDate = new Date(startDateParam);
        endDate = new Date(endDateParam);

        // Ajustar horas para pegar o dia completo
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        console.log(`Usando período personalizado: ${startDate.toISOString()} até ${endDate.toISOString()}`);
      } else {
        // Determine a data de início com base no período
        startDate = new Date();
        endDate = new Date(); // Data atual para o fim do período

        switch (period) {
          // Períodos passados
          case "7days":
            startDate.setDate(startDate.getDate() - 7);
            break;
          case "30days":
            startDate.setDate(startDate.getDate() - 30);
            break;
          case "3months":
            startDate.setMonth(startDate.getMonth() - 3);
            break;
          case "1year":
            startDate.setFullYear(startDate.getFullYear() - 1);
            break;

          // Períodos futuros
          case "next7days":
            // Hoje até 7 dias no futuro
            endDate.setDate(endDate.getDate() + 7);
            break;
          case "next30days":
            // Hoje até 30 dias no futuro
            endDate.setDate(endDate.getDate() + 30);
            break;

          default:
            startDate.setDate(startDate.getDate() - 30); // Default: 30 days
        }

        // Ajustar horas para pegar o dia completo
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);
      }

      // Construir a cláusula where para filtrar por período e empresa do usuário
      const whereClause = {
        startDate: {
          gte: startDate,
          lte: endDate
        },
        // Se o usuário não for SYSTEM_ADMIN, limitar aos agendamentos da empresa dele
        ...((req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) ?
          { companyId: req.user.companyId } : {})
      };

      console.log("Obtendo dados do dashboard para o período:", period);
      console.log("Where clause:", JSON.stringify(whereClause));

      // 1. Obter estatísticas gerais
      const totalAppointments = await prisma.scheduling.count({
        where: whereClause
      });

      const completedAppointments = await prisma.scheduling.count({
        where: {
          ...whereClause,
          status: "COMPLETED"
        }
      });

      const cancelledAppointments = await prisma.scheduling.count({
        where: {
          ...whereClause,
          status: "CANCELLED"
        }
      });

      const noShowAppointments = await prisma.scheduling.count({
        where: {
          ...whereClause,
          status: "NO_SHOW"
        }
      });

      const pendingAppointments = await prisma.scheduling.count({
        where: {
          ...whereClause,
          status: "PENDING"
        }
      });

      const confirmedAppointments = await prisma.scheduling.count({
        where: {
          ...whereClause,
          status: "CONFIRMED"
        }
      });

      console.log("Estatísticas calculadas:", {
        total: totalAppointments,
        completed: completedAppointments,
        cancelled: cancelledAppointments,
        noShow: noShowAppointments,
        pending: pendingAppointments,
        confirmed: confirmedAppointments
      });

      // 2. Obter os agendamentos com as informações relacionadas
      const schedulingsData = await prisma.scheduling.findMany({
        where: whereClause,
        include: {
          Person: {
            select: {
              id: true,
              fullName: true
            }
          },
          location: {
            select: {
              id: true,
              name: true
            }
          },
          serviceType: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      console.log(`Obtidos ${schedulingsData.length} agendamentos para análise`);

      // 3. Processar dados para as estatísticas

      // Pacientes mais agendados
      const patientsMap = {};
      schedulingsData.forEach(scheduling => {
        scheduling.Person.forEach(person => {
          if (!person || !person.id) return;

          const personId = person.id;
          if (!patientsMap[personId]) {
            patientsMap[personId] = {
              id: personId,
              name: person.fullName || `Paciente ${personId.substring(0, 8)}`,
              count: 0
            };
          }
          patientsMap[personId].count++;
        });
      });

      const topPatients = Object.values(patientsMap)
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Serviços mais agendados
      const servicesMap = {};
      schedulingsData.forEach(scheduling => {
        if (!scheduling.serviceType || !scheduling.serviceType.id) return;

        const serviceId = scheduling.serviceType.id;
        if (!servicesMap[serviceId]) {
          servicesMap[serviceId] = {
            id: serviceId,
            name: scheduling.serviceType.name || `Serviço ${serviceId.substring(0, 8)}`,
            count: 0
          };
        }
        servicesMap[serviceId].count++;
      });

      const topServices = Object.values(servicesMap)
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Locais mais agendados
      const locationsMap = {};
      schedulingsData.forEach(scheduling => {
        if (!scheduling.location || !scheduling.location.id) return;

        const locationId = scheduling.location.id;
        if (!locationsMap[locationId]) {
          locationsMap[locationId] = {
            id: locationId,
            name: scheduling.location.name || `Local ${locationId.substring(0, 8)}`,
            count: 0
          };
        }
        locationsMap[locationId].count++;
      });

      const topLocations = Object.values(locationsMap)
        .sort((a, b) => b.count - a.count)
        .slice(0, 5);

      // Distribuição por status
      const statusMap = {};
      const statusLabels = {
        "PENDING": "Pendente",
        "CONFIRMED": "Confirmado",
        "CANCELLED": "Cancelado",
        "COMPLETED": "Concluído",
        "NO_SHOW": "Não Compareceu"
      };

      schedulingsData.forEach(scheduling => {
        const status = scheduling.status || "PENDING";
        if (!statusMap[status]) {
          statusMap[status] = {
            status: status,
            label: statusLabels[status] || status,
            count: 0
          };
        }
        statusMap[status].count++;
      });

      const statusDistribution = Object.values(statusMap);

      // Tendências de agendamentos
      const trendsMap = {};

      schedulingsData.forEach(scheduling => {
        if (!scheduling.startDate) return;

        const date = new Date(scheduling.startDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        let dateKey;

        // Determinar o formato da data com base no período ou intervalo personalizado
        if (startDateParam && endDateParam) {
          // Para período personalizado, determinar o formato com base na duração
          const durationDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

          if (durationDays <= 31) {
            // Para períodos de até 31 dias, mostrar por dia
            dateKey = `${year}-${month}-${day}`;
          } else if (durationDays <= 90) {
            // Para períodos de até 90 dias, mostrar por semana
            const weekNum = Math.ceil((date - new Date(year, 0, 1)) / 86400000 / 7);
            dateKey = `${year}-W${String(weekNum).padStart(2, '0')}`;
          } else {
            // Para períodos maiores, mostrar por mês
            dateKey = `${year}-${month}`;
          }
        } else if (period === "7days" || period === "30days") {
          dateKey = `${year}-${month}-${day}`;
        } else if (period === "3months") {
          // Semana do ano
          const weekNum = Math.ceil((date - new Date(year, 0, 1)) / 86400000 / 7);
          dateKey = `${year}-W${String(weekNum).padStart(2, '0')}`;
        } else {
          // Mês do ano
          dateKey = `${year}-${month}`;
        }

        if (!trendsMap[dateKey]) {
          trendsMap[dateKey] = {
            date: dateKey,
            count: 0
          };
        }
        trendsMap[dateKey].count++;
      });

      const appointmentTrends = Object.values(trendsMap)
        .sort((a, b) => a.date.localeCompare(b.date));

      // Montar o objeto de resposta
      const dashboardData = {
        stats: {
          totalAppointments,
          completedAppointments,
          cancelledAppointments,
          noShowAppointments,
          pendingAppointments,
          confirmedAppointments
        },
        topPatients,
        topServices,
        topLocations,
        statusDistribution,
        appointmentTrends
      };

      console.log("Enviando dados do dashboard com sucesso");
      res.json(dashboardData);
    } catch (error) {
      console.error("Erro ao obter dados do dashboard de agendamentos:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  SchedulingDashboardController,
};