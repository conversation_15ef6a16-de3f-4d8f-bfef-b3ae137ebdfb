// src/controllers/aba/curriculumFolderController.js
const { PrismaClient } = require('@prisma/client');
const { body, validationResult } = require('express-validator');
const prisma = new PrismaClient();

// Validação para criação de pasta curricular
const createCurriculumFolderValidation = [
  body('name').notEmpty().withMessage('O nome da pasta é obrigatório'),
  body('personId').notEmpty().withMessage('O aprendiz é obrigatório'),
  body('shareWithParents').isBoolean().optional(),
  body('shareWithSchools').isBoolean().optional(),
];

// Validação para atualização de pasta curricular
const updateCurriculumFolderValidation = [
  body('name').notEmpty().withMessage('O nome da pasta é obrigatório'),
  body('personId').notEmpty().withMessage('O aprendiz é obrigatório'),
  body('shareWithParents').isBoolean().optional(),
  body('shareWithSchools').isBoolean().optional(),
];

class CurriculumFolderController {
  /**
   * Cria uma nova pasta curricular
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, personId, shareWithParents, shareWithSchools } = req.body;

      // Verificar se o paciente existe e pertence à mesma empresa do usuário
      const person = await prisma.person.findUnique({
        where: { id: personId },
        select: { id: true, fullName: true, clientId: true }
      });

      if (!person) {
        return res.status(404).json({ message: 'Aprendiz não encontrado' });
      }

      // Criar pasta curricular
      const curriculumFolder = await prisma.curriculumFolder.create({
        data: {
          name,
          personId,
          shareWithParents: shareWithParents || false,
          shareWithSchools: shareWithSchools || false,
          companyId: req.user.companyId,
          createdById: req.user.id,
        },
      });

      res.status(201).json(curriculumFolder);
    } catch (error) {
      console.error("Erro ao criar pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todas as pastas curriculares com paginação e filtros
   */
  static async list(req, res) {
    try {
      const { page = 1, limit = 10, search = "", active, personId } = req.query;

      // Construir filtro de busca
      let where = {
        companyId: req.user.companyId,
      };

      // Filtro de status ativo/inativo
      if (active !== undefined) {
        where.active = active === 'true';
      }

      // Filtro por paciente específico
      if (personId) {
        where.personId = personId;
      }

      // Filtro de busca
      if (search) {
        where = {
          ...where,
          OR: [
            { name: { contains: search, mode: "insensitive" } },
            { 
              person: { 
                fullName: { contains: search, mode: "insensitive" } 
              } 
            }
          ]
        };
      }

      // Contar total de registros
      const total = await prisma.curriculumFolder.count({ where });

      // Buscar pastas curriculares com paginação
      const curriculumFolders = await prisma.curriculumFolder.findMany({
        where,
        include: {
          person: {
            select: {
              id: true,
              fullName: true,
              clientId: true,
              client: {
                select: {
                  id: true,
                  email: true
                }
              }
            }
          },
          createdBy: {
            select: {
              id: true,
              fullName: true
            }
          }
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      res.json({
        curriculumFolders,
        pagination: {
          total,
          page: Number(page),
          limit: Number(limit),
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error("Erro ao listar pastas curriculares:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém uma pasta curricular específica pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const curriculumFolder = await prisma.curriculumFolder.findUnique({
        where: { id },
        include: {
          person: {
            select: {
              id: true,
              fullName: true,
              clientId: true,
              client: {
                select: {
                  id: true,
                  email: true
                }
              }
            }
          },
          createdBy: {
            select: {
              id: true,
              fullName: true
            }
          }
        }
      });

      if (!curriculumFolder) {
        return res.status(404).json({ message: 'Pasta curricular não encontrada' });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && curriculumFolder.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para visualizar esta pasta curricular' });
      }

      res.json(curriculumFolder);
    } catch (error) {
      console.error("Erro ao buscar pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza uma pasta curricular existente
   */
  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { name, personId, shareWithParents, shareWithSchools } = req.body;

      // Verificar se a pasta curricular existe
      const existingFolder = await prisma.curriculumFolder.findUnique({
        where: { id }
      });

      if (!existingFolder) {
        return res.status(404).json({ message: 'Pasta curricular não encontrada' });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingFolder.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para editar esta pasta curricular' });
      }

      // Verificar se o paciente existe
      if (personId) {
        const person = await prisma.person.findUnique({
          where: { id: personId }
        });

        if (!person) {
          return res.status(404).json({ message: 'Aprendiz não encontrado' });
        }
      }

      // Atualizar pasta curricular
      const updatedFolder = await prisma.curriculumFolder.update({
        where: { id },
        data: {
          name,
          personId,
          shareWithParents: shareWithParents !== undefined ? shareWithParents : existingFolder.shareWithParents,
          shareWithSchools: shareWithSchools !== undefined ? shareWithSchools : existingFolder.shareWithSchools,
          updatedAt: new Date()
        }
      });

      res.json(updatedFolder);
    } catch (error) {
      console.error("Erro ao atualizar pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status ativo/inativo de uma pasta curricular
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a pasta curricular existe
      const existingFolder = await prisma.curriculumFolder.findUnique({
        where: { id }
      });

      if (!existingFolder) {
        return res.status(404).json({ message: 'Pasta curricular não encontrada' });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingFolder.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para alterar esta pasta curricular' });
      }

      // Alternar status
      const updatedFolder = await prisma.curriculumFolder.update({
        where: { id },
        data: {
          active: !existingFolder.active,
          updatedAt: new Date()
        }
      });

      res.json(updatedFolder);
    } catch (error) {
      console.error("Erro ao alternar status da pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Exclui uma pasta curricular
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a pasta curricular existe
      const existingFolder = await prisma.curriculumFolder.findUnique({
        where: { id }
      });

      if (!existingFolder) {
        return res.status(404).json({ message: 'Pasta curricular não encontrada' });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingFolder.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para excluir esta pasta curricular' });
      }

      // Soft delete da pasta curricular
      await prisma.curriculumFolder.update({
        where: { id },
        data: {
          active: false,
          deletedAt: new Date()
        }
      });

      res.json({ message: 'Pasta curricular excluída com sucesso' });
    } catch (error) {
      console.error("Erro ao excluir pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

// Exportar o controlador e as validações
module.exports = {
  CurriculumFolderController,
  createCurriculumFolderValidation,
  updateCurriculumFolderValidation
};
