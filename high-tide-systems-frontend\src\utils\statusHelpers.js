import { CheckCircle, XCircle, Clock, ShieldCheck, Shield, UserCog, User } from 'lucide-react';

/**
 * Retorna informações de exibição para cada status
 * @param {string} status - Status do agendamento
 * @returns {Object} - Objeto com texto, estilo e ícone
 */
export const getStatusInfo = (status) => {
  switch (status) {
    case 'CONFIRMED':
      return {
        text: 'Confirmado',
        border: 'border-emerald-300',
        icon: CheckCircle,
        iconColor: 'text-emerald-600',
        status
      };
    case 'CANCELLED':
      return {
        text: 'Cancelado',
        border: 'border-rose-300',
        icon: XCircle,
        iconColor: 'text-rose-600',
        status
      };
    case 'COMPLETED':
      return {
        text: 'Concluído',
        border: 'border-blue-300',
        icon: CheckCircle,
        iconColor: 'text-blue-600',
        status
      };
    case 'NO_SHOW':
      return {
        text: 'Não Compareceu',
        border: 'border-slate-300',
        icon: XCircle,
        iconColor: 'text-slate-600',
        status
      };
    case 'PENDING':
    default:
      return {
        text: 'Pendente',
        border: 'border-amber-300',
        icon: Clock,
        iconColor: 'text-amber-600',
        status
      };
  }
};

/**
 * Retorna informações sobre o papel do usuário
 * @param {string} role - Papel do usuário
 * @returns {Object} - Objeto com nome, estilo e ícone
 */
export const getRoleInfo = (role) => {
  switch (role) {
    case 'SYSTEM_ADMIN':
      return {
        name: 'Administrador do Sistema',
        bgColor: 'bg-gradient-to-r from-red-500 to-rose-500',
        textColor: 'text-white',
        icon: ShieldCheck
      };
    case 'COMPANY_ADMIN':
      return {
        name: 'Administrador da Empresa',
        bgColor: 'bg-gradient-to-r from-blue-500 to-indigo-500',
        textColor: 'text-white',
        icon: Shield
      };
    case 'CLIENT':
      return {
        name: 'Cliente',
        bgColor: 'bg-gradient-to-r from-purple-500 to-violet-500',
        textColor: 'text-white',
        icon: User
      };
    default:
      return {
        name: 'Funcionário',
        bgColor: 'bg-gradient-to-r from-emerald-500 to-teal-500',
        textColor: 'text-white',
        icon: UserCog
      };
  }
};