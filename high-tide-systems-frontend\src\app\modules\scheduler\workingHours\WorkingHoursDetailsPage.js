"use client";

import React, { useState, useEffect } from 'react';
import { ChevronLeft, Save, Loader2, AlertCircle, Clock } from 'lucide-react';
import { ModuleTable } from '@/components/ui';
import workingHoursService from '@/app/modules/scheduler/services/workingHoursService';
import { usePermissions } from '@/hooks/usePermissions';
import { useToast } from '@/contexts/ToastContext'; // Importando o hook de toast

const DAYS_OF_WEEK = [
  { value: '1', label: 'Segunda' },
  { value: '2', label: 'Terça' },
  { value: '3', label: 'Quarta' },
  { value: '4', label: 'Quinta' },
  { value: '5', label: 'Sex<PERSON>' },
  { value: '6', label: 'Sábado' },
];

const HOURS = Array.from({ length: 24 }, (_, i) => ({
  value: i,
  label: `${String(i).padStart(2, '0')}:00`,
}));

const WorkingHoursDetailsPage = ({ userId, userName, onBack, canManage = false }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [timeGrids, setTimeGrids] = useState({});
  const [originalData, setOriginalData] = useState({});
  const [hasChanges, setHasChanges] = useState(false);
  const [errorMessages, setErrorMessages] = useState([]);
  const [conflictingSchedules, setConflictingSchedules] = useState({});

  // Estados para controlar o arrasto de seleção
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartValue, setDragStartValue] = useState(null);
  const [currentDragDay, setCurrentDragDay] = useState(null);

  // Obtendo as permissões
  const { can } = usePermissions();

  // Obtendo as funções de toast
  const { toast_success, toast_error, toast_warning, toast_info } = useToast();

  // Verificando se o usuário pode gerenciar horários de trabalho
  const hasManageWorkingHours = can('scheduling.working-hours.manage');

  // Usando a verificação de permissão mais restritiva
  const canEdit = canManage && hasManageWorkingHours;

  useEffect(() => {
    loadAllTimeGrids();
  }, [userId]);

  // Carrega os horários de trabalho para todos os dias da semana
  const loadAllTimeGrids = async () => {
    setIsLoading(true);
    setErrorMessages([]);
    setConflictingSchedules({});

    try {
      const allGrids = {};
      const originalGrids = {};

      // Fazer requisições para todos os dias da semana em paralelo
      const promises = DAYS_OF_WEEK.map(async (day) => {
        const data = await workingHoursService.getUsersTimeGrid(day.value);
        const userInfo = data.users.find(user => user.userId === userId);

        if (userInfo) {
          allGrids[day.value] = [...userInfo.timeGrid];
          originalGrids[day.value] = [...userInfo.timeGrid];
        }
      });

      await Promise.all(promises);

      setTimeGrids(allGrids);
      setOriginalData(originalGrids);
      setHasChanges(false);

      // Exibe toast de sucesso
      toast_info({
        title: `Horários de ${userName}`,
        message: "Todos os dias da semana foram carregados"
      });
    } catch (error) {
      console.error("Erro ao carregar grade de horários:", error);
      setErrorMessages([{
        type: 'error',
        message: `Falha ao carregar horários de trabalho: ${error.message || 'Erro desconhecido'}`
      }]);

      // Exibe toast de erro
      toast_error({
        title: "Erro ao carregar horários",
        message: error.message || "Ocorreu um erro ao carregar os horários"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Função para iniciar o arrasto
  const handleMouseDown = (day, hour) => {
    // Permite alterações apenas se o usuário tiver permissão
    if (!canEdit) {
      toast_error({
        title: "Permissão negada",
        message: "Você não tem permissão para editar horários"
      });
      return;
    }

    setErrorMessages([]);
    setConflictingSchedules({});

    // Iniciar o arrasto
    setIsDragging(true);
    setCurrentDragDay(day);

    // Garantir que o grid para este dia existe
    const newGrids = { ...timeGrids };
    if (!newGrids[day]) {
      newGrids[day] = Array(24).fill(false);
      setTimeGrids(newGrids);
    }

    // Armazenar o valor inicial (se a célula estava selecionada ou não)
    // Vamos inverter este valor para todas as células durante o arrasto
    setDragStartValue(!timeGrids[day][hour]);

    // Atualizar a célula atual
    toggleCellValue(day, hour, !timeGrids[day][hour]);

    // Adicionar event listeners globais para capturar o movimento e o fim do arrasto
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Função para lidar com o movimento do mouse durante o arrasto
  const handleMouseOver = (day, hour) => {
    // Só processar se estiver arrastando e for o mesmo dia
    if (isDragging && day === currentDragDay) {
      toggleCellValue(day, hour, dragStartValue);
    }
  };

  // Função para finalizar o arrasto
  const handleMouseUp = () => {
    setIsDragging(false);
    setCurrentDragDay(null);
    setDragStartValue(null);

    // Remover event listeners globais
    document.removeEventListener('mouseup', handleMouseUp);
  };

  // Função auxiliar para alternar o valor de uma célula
  const toggleCellValue = (day, hour, newValue) => {
    const newGrids = { ...timeGrids };

    if (!newGrids[day]) {
      newGrids[day] = Array(24).fill(false);
    }

    newGrids[day][hour] = newValue;
    setTimeGrids(newGrids);

    // Verificar se há alterações em relação aos dados originais
    let changed = false;
    for (const [dayKey, grid] of Object.entries(newGrids)) {
      const originalGrid = originalData[dayKey] || Array(24).fill(false);
      if (JSON.stringify(grid) !== JSON.stringify(originalGrid)) {
        changed = true;
        break;
      }
    }

    setHasChanges(changed);

    // Notifica sobre alterações pendentes
    if (changed && !hasChanges) {
      toast_info("Alterações pendentes de salvamento");
    }
  };

  // Altera disponibilidade ao clicar em uma célula (mantida para compatibilidade)
  const handleCellClick = (day, hour) => {
    // Esta função agora é apenas um wrapper para handleMouseDown
    // para manter compatibilidade com código existente
    handleMouseDown(day, hour);
  };

  // Salva as alterações para todos os dias
  const saveChanges = async () => {
    // Verifica permissão antes de salvar
    if (!canEdit) {
      setErrorMessages([{
        type: 'error',
        message: 'Você não tem permissão para gerenciar horários de trabalho.'
      }]);

      toast_error({
        title: "Permissão negada",
        message: "Você não tem permissão para gerenciar horários de trabalho"
      });
      return;
    }

    setIsSaving(true);
    setErrorMessages([]);
    setConflictingSchedules({});

    try {
      const allConflicts = {};
      const savingErrors = [];

      // Salvar cada dia de forma sequencial para lidar melhor com erros
      for (const day of DAYS_OF_WEEK) {
        const dayValue = day.value;

        if (!timeGrids[dayValue]) continue;

        try {
          const userGrid = [{
            userId: userId,
            timeGrid: timeGrids[dayValue]
          }];

          const result = await workingHoursService.updateMultipleUsersGrid(dayValue, userGrid);

          if (!result.overallSuccess) {
            // Houve conflitos ou erros
            result.errors.forEach(err => {
              savingErrors.push({
                type: 'error',
                message: `${day.label}: ${err.message}`
              });

              // Armazenar conflitos de agendamento
              if (err.schedulings && err.schedulings.length > 0) {
                if (!allConflicts[dayValue]) {
                  allConflicts[dayValue] = [];
                }

                allConflicts[dayValue] = err.schedulings.map(s => ({
                  id: s.id,
                  title: s.title || 'Agendamento',
                  startDate: new Date(s.startDate),
                  endDate: new Date(s.endDate),
                  hour: new Date(s.startDate).getHours(),
                }));
              }
            });
          } else {
            // Atualizar os dados originais apenas para os dias salvos com sucesso
            setOriginalData(prev => ({
              ...prev,
              [dayValue]: [...timeGrids[dayValue]]
            }));
          }
        } catch (error) {
          savingErrors.push({
            type: 'error',
            message: `${day.label}: Falha ao salvar - ${error.message || 'Erro desconhecido'}`
          });
        }
      }

      // Atualizar estados após salvar todos os dias
      if (Object.keys(allConflicts).length > 0) {
        setConflictingSchedules(allConflicts);

        setErrorMessages([
          {
            type: 'warning',
            message: 'Alguns horários não puderam ser atualizados devido a conflitos com agendamentos existentes.'
          },
          ...savingErrors
        ]);

        // Exibe toast de aviso
        toast_warning({
          title: "Atualização parcial",
          message: "Alguns horários não puderam ser atualizados devido a conflitos"
        });
      } else if (savingErrors.length > 0) {
        setErrorMessages(savingErrors);

        // Exibe toast de erro
        toast_error({
          title: "Erros ao salvar",
          message: "Ocorreram erros ao salvar alguns horários"
        });
      } else {
        setErrorMessages([{
          type: 'success',
          message: 'Todos os horários foram atualizados com sucesso'
        }]);

        // Exibe toast de sucesso
        toast_success({
          title: "Horários atualizados",
          message: "Todos os horários foram atualizados com sucesso"
        });
      }

      // Verificar se ainda há alterações pendentes
      let stillHasChanges = false;
      for (const [dayKey, grid] of Object.entries(timeGrids)) {
        const originalGrid = originalData[dayKey] || Array(24).fill(false);
        if (JSON.stringify(grid) !== JSON.stringify(originalGrid)) {
          stillHasChanges = true;
          break;
        }
      }

      setHasChanges(stillHasChanges);
    } catch (error) {
      setErrorMessages([{
        type: 'error',
        message: `Erro: Falha ao salvar alterações. ${error.message || 'Erro desconhecido'}`
      }]);

      // Exibe toast de erro
      toast_error({
        title: "Erro ao salvar",
        message: error.message || "Ocorreu um erro ao salvar os horários"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const renderErrorMessages = () => {
    if (errorMessages.length === 0) return null;

    return (
      <div className="mb-4">
        {errorMessages.map((error, index) => (
          <div
            key={index}
            className={`mb-2 p-3 rounded-md ${
              error.type === 'error'
                ? 'bg-error-50 dark:bg-red-900/20 border border-error-200 dark:border-red-800/50 text-error-800 dark:text-red-300'
                : error.type === 'success'
                ? 'bg-success-50 dark:bg-green-900/20 border border-success-200 dark:border-green-800/50 text-success-800 dark:text-green-300'
                : 'bg-warning-50 dark:bg-amber-900/20 border border-warning-200 dark:border-amber-800/50 text-warning-800 dark:text-amber-300'
            }`}
          >
            <div className="flex items-start">
              {error.type === 'error' && <AlertCircle className="h-5 w-5 text-error-500 dark:text-red-400 mr-2 flex-shrink-0 mt-0.5" />}
              {error.type === 'warning' && <AlertCircle className="h-5 w-5 text-warning-500 dark:text-amber-400 mr-2 flex-shrink-0 mt-0.5" />}
              <span>{error.message}</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderAllDaysTable = () => (
    <div className="overflow-x-auto pb-4">
      <table className="min-w-full border-collapse bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
        <thead>
          <tr className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
            <th className="py-3 px-4 border-b border-r border-gray-200 dark:border-gray-700 text-left font-semibold text-gray-700 dark:text-gray-200 w-48">
              Dia
            </th>
            {HOURS.map(hour => (
              <th
                key={hour.value}
                className="py-3 px-2 text-center border-b border-r border-gray-200 dark:border-gray-700 font-semibold text-gray-700 dark:text-gray-200 w-10"
              >
                {hour.value}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {DAYS_OF_WEEK.map(day => {
            const dayTimeGrid = timeGrids[day.value] || Array(24).fill(false);
            const dayConflicts = conflictingSchedules[day.value] || [];

            return (
              <tr key={day.value} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <td className="py-3 px-4 border-b border-r border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-800 dark:text-gray-200 bg-gray-50 dark:bg-gray-700">
                  {day.label}
                </td>
                {HOURS.map(hour => {
                  // Verificar se existe conflito para esta célula
                  const hasConflict = dayConflicts.some(s => s.hour === hour.value);

                  return (
                    <td
                      key={`${day.value}-${hour.value}`}
                      className="py-1 px-1 border-b border-r border-gray-200 dark:border-gray-700 text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      onMouseDown={() => handleMouseDown(day.value, hour.value)}
                      onMouseOver={() => handleMouseOver(day.value, hour.value)}
                      title={hasConflict ? "Existe um agendamento neste horário" : "Clique e arraste para selecionar múltiplos horários"}
                    >
                      <div
                        className={`w-full h-6 rounded flex items-center justify-center transition-all duration-200 ${
                          hasConflict
                            ? 'bg-error-200 dark:bg-red-900/50 hover:bg-error-300 dark:hover:bg-red-800/50 shadow-sm dark:shadow-black/20'
                            : dayTimeGrid[hour.value]
                              ? 'bg-module-scheduler-border dark:bg-module-scheduler-border-dark hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark shadow-sm dark:shadow-black/20'
                              : 'bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500'
                        }`}
                      >
                        {hasConflict ? '!' : dayTimeGrid[hour.value] ? '' : ''}
                      </div>
                    </td>
                  );
                })}
              </tr>
            );
          })}
        </tbody>
      </table>
    </div>
  );

  const renderLoading = () => (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="w-16 h-16 rounded-full bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark flex items-center justify-center shadow-md dark:shadow-black/30 mb-5">
        <Loader2 className="h-8 w-8 animate-spin text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
      </div>
      <p className="text-gray-700 dark:text-gray-300 font-medium">Carregando quadro de horários...</p>
    </div>
  );

  const renderLegend = () => (
    <div className="flex justify-center mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg py-3 px-5 border border-gray-200 dark:border-gray-600 shadow-sm dark:shadow-black/20">
      <div className="flex items-center gap-8 flex-wrap">
        <div className="flex items-center gap-2">
          <div className="w-5 h-5 rounded bg-module-scheduler-border dark:bg-module-scheduler-border-dark border border-module-scheduler-border dark:border-module-scheduler-border-dark shadow-sm dark:shadow-black/20"></div>
          <span className="text-sm text-gray-700 dark:text-gray-200 font-medium">Disponível</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-5 h-5 rounded bg-gray-100 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 shadow-sm dark:shadow-black/20"></div>
          <span className="text-sm text-gray-700 dark:text-gray-200 font-medium">Indisponível</span>
        </div>
        {Object.keys(conflictingSchedules).length > 0 && (
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded bg-error-200 dark:bg-red-900/50 border border-error-300 dark:border-red-800 flex items-center justify-center text-xs shadow-sm dark:shadow-black/20">!</div>
            <span className="text-sm text-gray-700 dark:text-gray-200 font-medium">Conflito com agendamento</span>
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="w-full space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-scheduler-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden mb-6">
        <div className="bg-gradient-to-r from-purple-600 to-violet-600 dark:from-purple-700 dark:to-violet-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={onBack}
                className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-white text-module-scheduler-icon dark:bg-gray-800 dark:text-module-scheduler-icon-dark shadow-sm dark:shadow-black/20 hover:bg-module-scheduler-bg dark:hover:bg-module-scheduler-bg-dark transition-colors"
              >
                <ChevronLeft className="h-4 w-4" />
                Voltar
              </button>
              <h2 className="text-xl font-bold text-white">
                Horários de {userName}
              </h2>
            </div>

            {canEdit && (
              <button
                onClick={saveChanges}
                disabled={isLoading || isSaving || !hasChanges}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg shadow dark:shadow-black/20 transition-all duration-200 ${
                  hasChanges && !isLoading && !isSaving
                    ? 'bg-white dark:bg-gray-800 text-module-scheduler-icon dark:text-module-scheduler-icon-dark hover:bg-module-scheduler-bg dark:hover:bg-module-scheduler-bg-dark hover:shadow-lg dark:hover:shadow-black/30 transform hover:-translate-y-0.5'
                    : 'bg-white dark:bg-gray-800 bg-opacity-50 dark:bg-opacity-50 text-white cursor-not-allowed'
                }`}
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                Salvar Alterações
              </button>
            )}
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
        {renderErrorMessages()}

        <div className="bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark border border-module-scheduler-border dark:border-module-scheduler-border-dark rounded-lg p-3 mb-5">
          <div className="flex items-start gap-3">
            <Clock className="h-5 w-5 text-module-scheduler-icon dark:text-module-scheduler-icon-dark mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm text-module-scheduler-text dark:text-module-scheduler-text-dark font-medium mb-1">
                Nesta visualização:
              </p>
              <p className="text-sm text-gray-600 dark:text-gray-300">
                {canEdit
                  ? `Você está vendo todos os dias da semana para ${userName}. Clique nas células para alternar a disponibilidade ou clique e arraste para selecionar/desselecionar múltiplos horários de uma vez.`
                  : `Você está vendo todos os dias da semana para ${userName}.`}
              </p>
            </div>
          </div>
        </div>

        {isLoading ? renderLoading() : renderAllDaysTable()}

        {renderLegend()}
      </div>
    </div>
  );
};

export default WorkingHoursDetailsPage;