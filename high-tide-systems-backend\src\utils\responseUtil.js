/**
 * Utilitário para padronizar as respostas da API
 * Este módulo fornece funções para formatar respostas de forma consistente
 */

/**
 * Formata uma resposta de sucesso com dados
 * @param {Object} data - Os dados a serem retornados
 * @param {string} entityName - O nome da entidade principal (ex: 'persons', 'clients', 'schedulings')
 * @param {number} total - O número total de registros (para paginação)
 * @param {number} pages - O número total de páginas (para paginação)
 * @returns {Object} Resposta formatada
 */
const formatSuccessResponse = (data, entityName, total, pages) => {
  // Se os dados já estiverem no formato esperado, retorná-los diretamente
  if (data && data[entityName] !== undefined) {
    return data;
  }

  // Se os dados forem um array, formatá-los como uma lista paginada
  if (Array.isArray(data)) {
    return {
      [entityName]: data,
      total: total || data.length,
      pages: pages || Math.ceil((total || data.length) / 10)
    };
  }

  // Se os dados forem um objeto único, retorná-lo diretamente
  return data;
};

/**
 * Formata uma resposta de erro
 * @param {string} message - Mensagem de erro
 * @param {Array|Object} details - Detalhes adicionais do erro (opcional)
 * @param {number} statusCode - Código de status HTTP (opcional, padrão: 500)
 * @returns {Object} Resposta de erro formatada
 */
const formatErrorResponse = (message, details = null, statusCode = 500) => {
  return {
    message,
    details,
    statusCode
  };
};

module.exports = {
  formatSuccessResponse,
  formatErrorResponse
};
