'use client';

import React, { useState, useEffect } from 'react';

/**
 * Componente genérico para campos de entrada com máscara
 * 
 * @param {Object} props - Propriedades do componente
 * @param {string} props.type - Tipo de máscara: 'cpf', 'cnpj', 'phone', 'cep', ou 'custom'
 * @param {string} props.value - Valor atual do campo
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {string} props.name - Nome do campo
 * @param {string} props.id - ID do campo
 * @param {string} props.placeholder - Texto de placeholder
 * @param {string} props.className - Classes CSS adicionais
 * @param {boolean} props.disabled - Se o campo está desabilitado
 * @param {string} props.customMask - Máscara personalizada (quando type='custom')
 * @param {Object} props.inputProps - Propriedades adicionais para o input
 */
const MaskedInput = ({
  type = 'text',
  value = '',
  onChange,
  name,
  id,
  placeholder,
  className = '',
  disabled = false,
  customMask,
  ...inputProps
}) => {
  // Estado interno para controlar o valor formatado
  const [inputValue, setInputValue] = useState('');

  // Atualiza o estado interno quando o valor externo muda
  useEffect(() => {
    if (value !== undefined) {
      setInputValue(formatValue(value, type, customMask));
    }
  }, [value, type, customMask]);

  // Função para aplicar a máscara ao valor
  const applyMask = (value, mask) => {
    let maskedValue = '';
    let valueIndex = 0;

    for (let i = 0; i < mask.length && valueIndex < value.length; i++) {
      const maskChar = mask[i];
      const valueChar = value[valueIndex];

      if (maskChar === '#') {
        // Apenas dígitos
        if (/\d/.test(valueChar)) {
          maskedValue += valueChar;
          valueIndex++;
        } else {
          valueIndex++;
          i--;
        }
      } else if (maskChar === 'A') {
        // Apenas letras
        if (/[a-zA-Z]/.test(valueChar)) {
          maskedValue += valueChar;
          valueIndex++;
        } else {
          valueIndex++;
          i--;
        }
      } else if (maskChar === 'S') {
        // Letras ou dígitos
        if (/[a-zA-Z0-9]/.test(valueChar)) {
          maskedValue += valueChar;
          valueIndex++;
        } else {
          valueIndex++;
          i--;
        }
      } else {
        // Caracteres especiais da máscara
        maskedValue += maskChar;
        
        // Se o caractere do valor for igual ao caractere da máscara, avança
        if (valueChar === maskChar) {
          valueIndex++;
        }
      }
    }

    return maskedValue;
  };

  // Função para obter a máscara com base no tipo
  const getMask = (type) => {
    switch (type) {
      case 'cpf':
        return '###.###.###-##';
      case 'cnpj':
        return '##.###.###/####-##';
      case 'phone':
        return '(##) #####-####';
      case 'cep':
        return '#####-###';
      case 'custom':
        return customMask || '';
      default:
        return '';
    }
  };

  // Função para formatar o valor com base no tipo
  const formatValue = (value, type, customMask) => {
    if (!value) return '';
    
    // Remove caracteres não numéricos para tipos numéricos
    let cleanValue = value;
    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {
      cleanValue = value.replace(/\D/g, '');
    }
    
    const mask = getMask(type);
    return applyMask(cleanValue, mask);
  };

  // Função para remover a máscara e obter apenas os dígitos
  const unformatValue = (value) => {
    if (!value) return '';
    
    if (['cpf', 'cnpj', 'phone', 'cep'].includes(type)) {
      return value.replace(/\D/g, '');
    }
    
    return value;
  };

  // Manipulador de mudança de valor
  const handleChange = (e) => {
    const newValue = e.target.value;
    const formattedValue = formatValue(newValue, type, customMask);
    
    setInputValue(formattedValue);
    
    if (onChange) {
      // Cria um evento sintético com o valor formatado
      const syntheticEvent = {
        ...e,
        target: {
          ...e.target,
          name: name,
          value: formattedValue,
          rawValue: unformatValue(formattedValue)
        }
      };
      
      onChange(syntheticEvent);
    }
  };

  return (
    <input
      type="text"
      id={id}
      name={name}
      value={inputValue}
      onChange={handleChange}
      placeholder={placeholder}
      className={className}
      disabled={disabled}
      {...inputProps}
    />
  );
};

export default MaskedInput;
