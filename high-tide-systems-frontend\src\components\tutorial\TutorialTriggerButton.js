"use client";

import React from 'react';
import { HelpCircle } from 'lucide-react';
import { useTutorial } from '@/contexts/TutorialContext';

/**
 * <PERSON>t<PERSON> para iniciar um tutorial
 * 
 * @param {Object} props - Propriedades do componente
 * @param {Array} props.steps - Etapas do tutorial
 * @param {string} props.tutorialName - Nome único do tutorial
 * @param {boolean} props.showIfCompleted - Se deve mostrar o botão mesmo se o tutorial já foi concluído
 * @param {string} props.size - Tamanho do botão ('sm', 'md', 'lg')
 * @param {Object} props.className - Classes adicionais para o botão
 */
const TutorialTriggerButton = ({
  steps,
  tutorialName,
  showIfCompleted = true,
  size = 'md',
  className = '',
  children
}) => {
  const { startTutorial, isTutorialCompleted } = useTutorial();

  // Não renderizar o botão se o tutorial já foi concluído e showIfCompleted for falso
  if (!showIfCompleted && isTutorialCompleted(tutorialName)) {
    return null;
  }

  // Determinar tamanho do botão
  const sizeClasses = {
    sm: 'p-1.5 text-xs',
    md: 'p-2 text-sm',
    lg: 'p-2.5 text-base'
  };

  // Determinar tamanho do ícone
  const iconSize = {
    sm: 14,
    md: 16,
    lg: 20
  };

  const isCompleted = isTutorialCompleted(tutorialName);

  return (
    <button
      onClick={() => startTutorial(steps, tutorialName)}
      className={`
        relative flex items-center gap-1.5 rounded-full 
        ${isCompleted ? 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 dark:hover:bg-gray-600' : 'bg-primary-100 text-primary-700 hover:bg-primary-200 dark:bg-primary-900/40 dark:text-primary-300 dark:hover:bg-primary-800/60'}
        transition-colors ${sizeClasses[size]} ${className}
      `}
      aria-label={`Abrir tutorial: ${tutorialName}`}
      title={isCompleted ? "Ver tutorial novamente" : "Ver tutorial"}
    >
      <HelpCircle size={iconSize[size]} />
      {children}
      
      {/* Indicador de novo para tutoriais não completados */}
      {!isCompleted && (
        <span className="absolute -top-1 -right-1 h-2.5 w-2.5 rounded-full bg-primary-500 dark:bg-primary-400" />
      )}
    </button>
  );
};

export default TutorialTriggerButton;