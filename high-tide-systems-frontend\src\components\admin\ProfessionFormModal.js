import React, { useState, useEffect } from "react";
import { X, Loader2, Building } from "lucide-react";
import { professionsService } from "@/app/modules/admin/services/professionsService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useToast } from "@/contexts/ToastContext";
import { useAuth } from "@/contexts/AuthContext";

const ProfessionFormModal = ({ isOpen, onClose, profession = null, groups = [], onSuccess }) => {
  const { toast_success, toast_error } = useToast();
  const { user } = useAuth();
  const isSystemAdmin = user?.role === "SYSTEM_ADMIN";

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    groupId: "",
    companyId: "",
    active: true
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [companies, setCompanies] = useState([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);

  // Carregar empresas quando o modal é aberto (apenas para system admin)
  useEffect(() => {
    if (isOpen && isSystemAdmin) {
      loadCompanies();
    }
  }, [isOpen, isSystemAdmin]);

  // Carregar dados da profissão quando selecionada
  useEffect(() => {
    if (profession) {
      setFormData({
        name: profession.name || "",
        description: profession.description || "",
        groupId: profession.groupId || "",
        companyId: profession.companyId || "",
        active: profession.active !== undefined ? profession.active : true
      });
    } else {
      setFormData({
        name: "",
        description: "",
        groupId: "",
        companyId: user?.companyId || "",
        active: true
      });
    }
  }, [profession, user]);

  // Função para carregar empresas
  const loadCompanies = async () => {
    setIsLoadingCompanies(true);
    try {
      const companies = await companyService.getCompaniesForSelect();
      setCompanies(companies || []);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validar dados
      if (!formData.name.trim()) {
        toast_error("O nome da profissão é obrigatório");
        setIsSubmitting(false);
        return;
      }

      // Preparar dados para envio
      const dataToSend = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        groupId: formData.groupId || null,
        active: formData.active
      };

      // Adicionar companyId apenas se for system admin
      if (isSystemAdmin) {
        dataToSend.companyId = formData.companyId || null;
      }

      if (profession) {
        // Modo de edição
        await professionsService.updateProfession(profession.id, dataToSend);
        toast_success("Profissão atualizada com sucesso");
      } else {
        // Modo de criação
        await professionsService.createProfession(dataToSend);
        toast_success("Profissão criada com sucesso");
      }

      if (onSuccess) onSuccess();
    } catch (error) {
      console.error("Erro ao salvar profissão:", error);
      toast_error(error.response?.data?.message || "Erro ao salvar profissão");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>
      <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-4 border-b border-neutral-200 dark:border-gray-700">
          <h2 className="text-lg font-medium text-neutral-800 dark:text-neutral-100">
            {profession ? "Editar Profissão" : "Nova Profissão"}
          </h2>
          <button
            onClick={onClose}
            className="p-1 rounded-full hover:bg-neutral-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X size={20} className="text-neutral-500 dark:text-neutral-400" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Nome */}
          <div>
            <label
              htmlFor="name"
              className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
            >
              Nome <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
              required
            />
          </div>

          {/* Descrição */}
          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
            >
              Descrição
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows="3"
              className="w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100 resize-none"
            ></textarea>
          </div>

          {/* Grupo */}
          <div>
            <label
              htmlFor="groupId"
              className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1"
            >
              Grupo
            </label>
            <select
              id="groupId"
              name="groupId"
              value={formData.groupId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
            >
              <option value="">Sem grupo</option>
              {groups.map((group) => (
                <option key={group.id} value={group.id}>
                  {group.name}
                </option>
              ))}
            </select>
          </div>

          {/* Empresa (apenas para system admin) */}
          {isSystemAdmin && (
            <div>
              <label
                htmlFor="companyId"
                className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1 flex items-center gap-1"
              >
                <Building size={16} className="text-neutral-500" />
                Empresa
              </label>
              <select
                id="companyId"
                name="companyId"
                value={formData.companyId}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                disabled={isLoadingCompanies}
              >
                <option value="">Selecione uma empresa</option>
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </select>
              {isLoadingCompanies && (
                <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400 flex items-center gap-1">
                  <Loader2 size={12} className="animate-spin" />
                  <span>Carregando empresas...</span>
                </div>
              )}
            </div>
          )}

          {/* Status (apenas em modo de edição) */}
          {profession && (
            <div className="flex items-center">
              <input
                type="checkbox"
                id="active"
                name="active"
                checked={formData.active}
                onChange={handleChange}
                className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <label
                htmlFor="active"
                className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300"
              >
                Profissão ativa
              </label>
            </div>
          )}

          {/* Botões */}
          <div className="flex justify-end gap-2 pt-2">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-neutral-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors disabled:opacity-60"
            >
              Cancelar
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors disabled:opacity-60 flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <Loader2 size={16} className="animate-spin" />
                  <span>Salvando...</span>
                </>
              ) : (
                <span>Salvar</span>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProfessionFormModal;
