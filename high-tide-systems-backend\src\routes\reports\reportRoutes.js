// src/routes/reports/reportRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { cacheMiddleware, clearCacheMiddleware } = require('../../middlewares/cache');

// Configurar TTL para cache de relatórios (15 minutos)
const REPORT_CACHE_TTL = 900;

// Todas as rotas requerem autenticação
router.use(authenticate);

// Assumindo que temos um controlador de relatórios
// Se não existir, você precisará criar um
const ReportController = {
  salesReport: (req, res) => {
    // Implementação do relatório de vendas
    res.json({ message: 'Relatório de vendas' });
  },
  performanceReport: (req, res) => {
    // Implementação do relatório de desempenho
    res.json({ message: 'Relatório de desempenho' });
  },
  customerReport: (req, res) => {
    // Implementação do relatório de clientes
    res.json({ message: 'Relatório de clientes' });
  },
  appointmentReport: (req, res) => {
    // Implementação do relatório de agendamentos
    res.json({ message: 'Relatório de agendamentos' });
  },
  exportReport: (req, res) => {
    // Implementação da exportação de relatórios
    res.json({ message: 'Exportação de relatório' });
  }
};

// Aplicar cache para relatórios
router.get('/sales', cacheMiddleware('reports:sales', REPORT_CACHE_TTL), ReportController.salesReport);
router.get('/performance', cacheMiddleware('reports:performance', REPORT_CACHE_TTL), ReportController.performanceReport);
router.get('/customers', cacheMiddleware('reports:customers', REPORT_CACHE_TTL), ReportController.customerReport);
router.get('/appointments', cacheMiddleware('reports:appointments', REPORT_CACHE_TTL), ReportController.appointmentReport);

// Exportação de relatórios (sem cache, pois geralmente é uma operação única)
router.get('/export/:type', ReportController.exportReport);

module.exports = router;
