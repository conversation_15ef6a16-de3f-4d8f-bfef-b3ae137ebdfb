"use client";

import React, { useState, useEffect } from "react";
import { ClipboardList, Save, X } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleSelect, ModuleTextarea, ModuleTabs, ModuleFormGroup, ModuleCheckbox, ModuleRadio, ModuleRadioGroup } from "@/components/ui";
import { useToast } from "@/contexts/ToastContext";
import { personsService } from "@/app/modules/people/services/personsService";
import anamneseService from "../services/anamneseService";

const AnamneseFormModal = ({ isOpen, onClose, onSubmit, anamnese }) => {
  // Estado para a aba ativa
  const [activeTab, setActiveTab] = useState("historico-pessoal");

  // Estado para o formulário
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    personId: "",
    diagnostico: "",
    cuidador: "",
    profissaoCuidador: "",
    telefone: "",
    historicoPersonal: "",
    patologiaAssociada: "",
    convulsoes: "",
    sentou: "",
    engatinhou: "",
    andou: "",
    estereotipiasMotoras: "",
    alimentacaoSolidos: false,
    alimentacaoLiquidos: false,
    alimentacaoPastosos: false,
    alergiasIntolerancias: "",
    // Campos adicionais para informações médicas
    historicoMedico: "",
    medicacoes: "",
    alergias: "",
    historicoFamiliar: "",
    observacoesGerais: "",
    // AVD
    avdAlimentacao: "",
    avdBanho: "",
    avdVestuario: "",
    avdCuidadosPessoais: "",
    avdSono: "",
    avdEsfincter: "",
    // Desenvolvimento da Linguagem - Não Verbal
    gestosElementares: "NAO",
    naoSimbolicosConvencionais: "NAO",
    simbolicosRepresentacao: "NAO",
    // Desenvolvimento da Linguagem - Verbal
    verbal: "NAO",
    balbucio: "NAO",
    palavrasIsoladas: "NAO",
    quaisPalavrasIsoladas: "",
    enunciadoDuasPalavras: "NAO",
    frases: "NAO",
    estereotipiasVocais: "NAO",
    quaisEstereotipiasVocais: "",
    // Interação Social
    faltaExpressaoFacialAdequada: "NAO",
    apresentaAtencaoDiminuida: "NAO",
    apresentaPreferenciaIsolamento: "NAO",
    ageComoSeFosseSurdo: "NAO",
    olhaParaAlguemQueLheFala: "NAO",
    olhaQuandoChamadoPeloNome: "NAO",
    fazPedidoItensInteresse: "NAO",
    realizaImitacao: "NAO",
    brincaAdequadamenteBrinquedo: "NAO",
    preferenciasObjetosEspecificos: "",
    apresentaAversoes: "",
    autoEstimulacao: false,
    apresentaAutoAgressaoHeteroAgressao: false,
    apresentaBirrasIrritabilidade: false,
    apresentaManiasRituais: false,
    // Escola
    estuda: false,
    nomeEscola: "",
    serie: "",
    escolaRegular: false,
    professorApoio: false,
    // Outros
    outroCasoFamilia: false,
    outrosCasosDetalhamento: "",
    terapias: "",
    expectativasFamilia: ""
  });

  // Estado para erros de validação
  const [errors, setErrors] = useState({});

  // Estado para a lista de pacientes
  const [persons, setPersons] = useState([]);

  // Estado para indicar se está salvando
  const [isSaving, setIsSaving] = useState(false);

  // Hooks
  const { toast_success, toast_error } = useToast();

  // Carregar dados iniciais
  useEffect(() => {
    const loadPersons = async () => {
      try {
        console.log("Carregando pacientes...");
        const result = await personsService.getPersons({ limit: 100 });
        console.log("Resultado da API de pacientes:", result);

        // Verificar a estrutura do resultado
        if (result && result.persons) {
          console.log("Pacientes encontrados:", result.persons.length);
          setPersons(result.persons);
        } else if (result && result.items) {
          console.log("Pacientes encontrados (items):", result.items.length);
          setPersons(result.items);
        } else {
          console.error("Estrutura de resposta inesperada:", result);
        }
      } catch (error) {
        console.error("Erro ao carregar pacientes:", error);
      }
    };

    loadPersons();

    // Se for edição, preencher o formulário com os dados da anamnese
    if (anamnese) {
      console.log("Preenchendo formulário com dados da anamnese:", anamnese);
      try {
        setFormData({
        date: anamnese.date ? new Date(anamnese.date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        personId: anamnese.personId || "",
        diagnostico: anamnese.diagnostico || "",
        cuidador: anamnese.cuidador || "",
        profissaoCuidador: anamnese.profissaoCuidador || "",
        telefone: anamnese.telefone || "",
        historicoPersonal: anamnese.historicoPersonal || "",
        patologiaAssociada: anamnese.patologiaAssociada || "",
        convulsoes: anamnese.convulsoes || "",
        sentou: anamnese.sentou || "",
        engatinhou: anamnese.engatinhou || "",
        andou: anamnese.andou || "",
        estereotipiasMotoras: anamnese.estereotipiasMotoras || "",
        alimentacaoSolidos: anamnese.alimentacaoSolidos || false,
        alimentacaoLiquidos: anamnese.alimentacaoLiquidos || false,
        alimentacaoPastosos: anamnese.alimentacaoPastosos || false,
        alergiasIntolerancias: anamnese.alergiasIntolerancias || "",
        // Campos adicionais para informações médicas
        historicoMedico: anamnese.historicoMedico || "",
        medicacoes: anamnese.medicacoes || "",
        alergias: anamnese.alergias || "",
        historicoFamiliar: anamnese.historicoFamiliar || "",
        observacoesGerais: anamnese.observacoesGerais || "",
        // AVD
        avdAlimentacao: anamnese.avdAlimentacao || "",
        avdBanho: anamnese.avdBanho || "",
        avdVestuario: anamnese.avdVestuario || "",
        avdCuidadosPessoais: anamnese.avdCuidadosPessoais || "",
        avdSono: anamnese.avdSono || "",
        avdEsfincter: anamnese.avdEsfincter || "",
        // Desenvolvimento da Linguagem - Não Verbal
        gestosElementares: anamnese.gestosElementares || "NAO",
        naoSimbolicosConvencionais: anamnese.naoSimbolicosConvencionais || "NAO",
        simbolicosRepresentacao: anamnese.simbolicosRepresentacao || "NAO",
        // Desenvolvimento da Linguagem - Verbal
        verbal: anamnese.verbal || "NAO",
        balbucio: anamnese.balbucio || "NAO",
        palavrasIsoladas: anamnese.palavrasIsoladas || "NAO",
        quaisPalavrasIsoladas: anamnese.quaisPalavrasIsoladas || "",
        enunciadoDuasPalavras: anamnese.enunciadoDuasPalavras || "NAO",
        frases: anamnese.frases || "NAO",
        estereotipiasVocais: anamnese.estereotipiasVocais || "NAO",
        quaisEstereotipiasVocais: anamnese.quaisEstereotipiasVocais || "",
        // Interação Social
        faltaExpressaoFacialAdequada: anamnese.faltaExpressaoFacialAdequada || "NAO",
        apresentaAtencaoDiminuida: anamnese.apresentaAtencaoDiminuida || "NAO",
        apresentaPreferenciaIsolamento: anamnese.apresentaPreferenciaIsolamento || "NAO",
        ageComoSeFosseSurdo: anamnese.ageComoSeFosseSurdo || "NAO",
        olhaParaAlguemQueLheFala: anamnese.olhaParaAlguemQueLheFala || "NAO",
        olhaQuandoChamadoPeloNome: anamnese.olhaQuandoChamadoPeloNome || "NAO",
        fazPedidoItensInteresse: anamnese.fazPedidoItensInteresse || "NAO",
        realizaImitacao: anamnese.realizaImitacao || "NAO",
        brincaAdequadamenteBrinquedo: anamnese.brincaAdequadamenteBrinquedo || "NAO",
        preferenciasObjetosEspecificos: anamnese.preferenciasObjetosEspecificos || "",
        apresentaAversoes: anamnese.apresentaAversoes || "",
        autoEstimulacao: anamnese.autoEstimulacao || false,
        apresentaAutoAgressaoHeteroAgressao: anamnese.apresentaAutoAgressaoHeteroAgressao || false,
        apresentaBirrasIrritabilidade: anamnese.apresentaBirrasIrritabilidade || false,
        apresentaManiasRituais: anamnese.apresentaManiasRituais || false,
        // Escola
        estuda: anamnese.estuda || false,
        nomeEscola: anamnese.nomeEscola || "",
        serie: anamnese.serie || "",
        escolaRegular: anamnese.escolaRegular || false,
        professorApoio: anamnese.professorApoio || false,
        // Outros
        outroCasoFamilia: anamnese.outroCasoFamilia || false,
        outrosCasosDetalhamento: anamnese.outrosCasosDetalhamento || "",
        terapias: anamnese.terapias || "",
        expectativasFamilia: anamnese.expectativasFamilia || ""
      });
      } catch (error) {
        console.error("Erro ao preencher formulário com dados da anamnese:", error);
      }
    }
  }, [anamnese]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Limpar erro do campo quando ele for alterado
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.date) {
      newErrors.date = "A data é obrigatória";
    }

    if (!formData.personId) {
      newErrors.personId = "O paciente é obrigatório";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      console.log("Formulário inválido, não será enviado");
      return;
    }

    try {
      setIsSaving(true);
      console.log("Enviando dados do formulário:", formData);

      if (anamnese) {
        // Atualizar anamnese existente
        console.log(`Atualizando anamnese existente com ID ${anamnese.id}`);
        const result = await anamneseService.updateAnamnese(anamnese.id, formData);
        console.log("Resultado da atualização:", result);
        toast_success({
          title: "Sucesso",
          message: "Anamnese atualizada com sucesso!"
        });
      } else {
        // Criar nova anamnese
        console.log("Criando nova anamnese");
        const result = await anamneseService.createAnamnese(formData);
        console.log("Resultado da criação:", result);
        toast_success({
          title: "Sucesso",
          message: "Anamnese criada com sucesso!"
        });
      }

      onSubmit();
    } catch (error) {
      console.error("Erro ao salvar anamnese:", error);
      console.error("Detalhes do erro:", error.response?.data || error.message);
      toast_error({
        title: "Erro",
        message: "Não foi possível salvar a anamnese. Tente novamente mais tarde."
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Renderizar o footer do modal
  const renderFooter = () => (
    <div className="flex justify-end gap-2">
      <button
        type="button"
        onClick={onClose}
        className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        disabled={isSaving}
      >
        <X size={16} className="mr-2 inline" />
        Cancelar
      </button>
      <button
        type="submit"
        form="anamnese-form"
        className="px-4 py-2 bg-module-abaplus-primary hover:bg-module-abaplus-primary-dark text-white rounded-md flex items-center"
        disabled={isSaving}
      >
        <Save size={16} className="mr-2" />
        {isSaving ? "Salvando..." : "Salvar"}
      </button>
    </div>
  );

  // Calcular caracteres restantes
  const getRemainingChars = (field, maxLength) => {
    const currentLength = formData[field]?.length || 0;
    return maxLength - currentLength;
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={anamnese ? "Editar Anamnese" : "Nova Anamnese"}
      size="lg"
      moduleColor="abaplus"
      icon={<ClipboardList size={20} />}
      footer={renderFooter()}
    >
      <div className="p-6 space-y-6">
        <form
          id="anamnese-form"
          onSubmit={handleSubmit}
          onClick={(e) => {
            // Evitar que cliques dentro do formulário causem submissão acidental
            if (e.target.type !== 'submit') {
              e.stopPropagation();
            }
          }}
        >
          {/* Campos comuns a todas as abas */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <ModuleFormGroup label="Data *" error={errors.date}>
              <ModuleInput
                type="date"
                name="date"
                value={formData.date}
                onChange={handleChange}
                moduleColor="abaplus"
                error={!!errors.date}
                errorMessage={errors.date}
              />
            </ModuleFormGroup>

            <ModuleFormGroup label="Paciente *" error={errors.personId}>
              <ModuleSelect
                name="personId"
                value={formData.personId}
                onChange={handleChange}
                moduleColor="abaplus"
                error={!!errors.personId}
                errorMessage={errors.personId}
              >
                <option value="">Selecione um paciente</option>
                {Array.isArray(persons) && persons.length > 0 ? (
                  persons.map(person => (
                    <option key={person.id} value={person.id}>
                      {person.fullName || `Paciente ${person.id}`}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>Carregando pacientes...</option>
                )}
              </ModuleSelect>
            </ModuleFormGroup>
          </div>

          {/* Abas */}
          <ModuleTabs
            tabs={[
              { id: "historico-pessoal", label: "Histórico Pessoal" },
              { id: "desenvolvimento-motor", label: "Desenvolvimento Motor" },
              { id: "alimentacao", label: "Alimentação" },
              { id: "informacoes-medicas", label: "Informações Médicas" },
              { id: "avd", label: "AVD" },
              { id: "desenvolvimento-linguagem", label: "Desenvolvimento da Linguagem" },
              { id: "interacao-social", label: "Interação Social" },
              { id: "escola", label: "Escola" },
              { id: "outros", label: "Outros" }
            ]}
            activeTab={activeTab}
            onTabChange={(tabId) => {
              // Prevenir a submissão do formulário ao trocar de aba
              console.log(`Mudando para a aba ${tabId}`);

              // Usar setTimeout para garantir que o evento de clique não propague para o formulário
              setTimeout(() => {
                setActiveTab(tabId);
              }, 10);

              // Prevenir qualquer comportamento padrão
              return false;
            }}
            moduleColor="abaplus"
          />

          {/* Conteúdo das abas */}
          <div className="mt-6">
            {/* Aba Histórico Pessoal */}
            {activeTab === "historico-pessoal" && (
              <div className="space-y-4">
                <ModuleFormGroup label="Diagnóstico">
                  <ModuleTextarea
                    name="diagnostico"
                    value={formData.diagnostico}
                    onChange={handleChange}
                    placeholder="Diagnóstico do paciente"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("diagnostico", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Cuidador">
                  <ModuleTextarea
                    name="cuidador"
                    value={formData.cuidador}
                    onChange={handleChange}
                    placeholder="Informações sobre o cuidador"
                    moduleColor="abaplus"
                    rows={3}
                    maxLength={1000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("cuidador", 1000)} /1000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ModuleFormGroup label="Profissão do cuidador">
                    <ModuleInput
                      name="profissaoCuidador"
                      value={formData.profissaoCuidador}
                      onChange={handleChange}
                      placeholder="Profissão do cuidador"
                      moduleColor="abaplus"
                      maxLength={200}
                    />
                    <div className="text-xs text-gray-500 mt-1 text-right">
                      {getRemainingChars("profissaoCuidador", 200)} /200 caracteres restantes
                    </div>
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Telefone">
                    <ModuleInput
                      name="telefone"
                      value={formData.telefone}
                      onChange={handleChange}
                      placeholder="Telefone de contato"
                      moduleColor="abaplus"
                    />
                  </ModuleFormGroup>
                </div>

                <ModuleFormGroup label="Histórico Pessoal">
                  <ModuleTextarea
                    name="historicoPersonal"
                    value={formData.historicoPersonal}
                    onChange={handleChange}
                    placeholder="Histórico pessoal do paciente"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("historicoPersonal", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Patologia Associada">
                  <ModuleTextarea
                    name="patologiaAssociada"
                    value={formData.patologiaAssociada}
                    onChange={handleChange}
                    placeholder="Patologias associadas"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("patologiaAssociada", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Convulsões">
                  <ModuleTextarea
                    name="convulsoes"
                    value={formData.convulsoes}
                    onChange={handleChange}
                    placeholder="Informações sobre convulsões"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("convulsoes", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>
              </div>
            )}

            {/* Aba Desenvolvimento Motor */}
            {activeTab === "desenvolvimento-motor" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <ModuleFormGroup label="Sentou">
                    <ModuleInput
                      name="sentou"
                      value={formData.sentou}
                      onChange={handleChange}
                      placeholder="Quando sentou"
                      moduleColor="abaplus"
                      maxLength={50}
                    />
                    <div className="text-xs text-gray-500 mt-1 text-right">
                      {getRemainingChars("sentou", 50)} /50 caracteres restantes
                    </div>
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Engatinhou">
                    <ModuleInput
                      name="engatinhou"
                      value={formData.engatinhou}
                      onChange={handleChange}
                      placeholder="Quando engatinhou"
                      moduleColor="abaplus"
                      maxLength={50}
                    />
                    <div className="text-xs text-gray-500 mt-1 text-right">
                      {getRemainingChars("engatinhou", 50)} /50 caracteres restantes
                    </div>
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Andou">
                    <ModuleInput
                      name="andou"
                      value={formData.andou}
                      onChange={handleChange}
                      placeholder="Quando andou"
                      moduleColor="abaplus"
                      maxLength={50}
                    />
                    <div className="text-xs text-gray-500 mt-1 text-right">
                      {getRemainingChars("andou", 50)} /50 caracteres restantes
                    </div>
                  </ModuleFormGroup>
                </div>

                <ModuleFormGroup label="Estereotipias motoras">
                  <ModuleTextarea
                    name="estereotipiasMotoras"
                    value={formData.estereotipiasMotoras}
                    onChange={handleChange}
                    placeholder="Informações sobre estereotipias motoras"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("estereotipiasMotoras", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>
              </div>
            )}

            {/* Aba Alimentação */}
            {activeTab === "alimentacao" && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <ModuleFormGroup label="Sólidos">
                    <div className="flex items-center space-x-4">
                      <ModuleRadio
                        name="alimentacaoSolidos"
                        value="true"
                        selectedValue={formData.alimentacaoSolidos === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          alimentacaoSolidos: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Sim"
                      />
                      <ModuleRadio
                        name="alimentacaoSolidos"
                        value="false"
                        selectedValue={formData.alimentacaoSolidos === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          alimentacaoSolidos: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Não"
                      />
                    </div>
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Líquidos">
                    <div className="flex items-center space-x-4">
                      <ModuleRadio
                        name="alimentacaoLiquidos"
                        value="true"
                        selectedValue={formData.alimentacaoLiquidos === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          alimentacaoLiquidos: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Sim"
                      />
                      <ModuleRadio
                        name="alimentacaoLiquidos"
                        value="false"
                        selectedValue={formData.alimentacaoLiquidos === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          alimentacaoLiquidos: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Não"
                      />
                    </div>
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Pastosos">
                    <div className="flex items-center space-x-4">
                      <ModuleRadio
                        name="alimentacaoPastosos"
                        value="true"
                        selectedValue={formData.alimentacaoPastosos === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          alimentacaoPastosos: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Sim"
                      />
                      <ModuleRadio
                        name="alimentacaoPastosos"
                        value="false"
                        selectedValue={formData.alimentacaoPastosos === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          alimentacaoPastosos: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Não"
                      />
                    </div>
                  </ModuleFormGroup>
                </div>

                <ModuleFormGroup label="Alergias/Intolerâncias">
                  <ModuleTextarea
                    name="alergiasIntolerancias"
                    value={formData.alergiasIntolerancias}
                    onChange={handleChange}
                    placeholder="Informações sobre alergias e intolerâncias alimentares"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("alergiasIntolerancias", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>
              </div>
            )}

            {/* Aba Informações Médicas */}
            {activeTab === "informacoes-medicas" && (
              <div className="space-y-4">
                <ModuleFormGroup label="Histórico Médico">
                  <ModuleTextarea
                    name="historicoMedico"
                    value={formData.historicoMedico}
                    onChange={handleChange}
                    placeholder="Histórico médico do paciente"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("historicoMedico", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Medicações">
                  <ModuleTextarea
                    name="medicacoes"
                    value={formData.medicacoes}
                    onChange={handleChange}
                    placeholder="Medicações em uso"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("medicacoes", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Alergias">
                  <ModuleTextarea
                    name="alergias"
                    value={formData.alergias}
                    onChange={handleChange}
                    placeholder="Alergias conhecidas"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("alergias", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Histórico Familiar">
                  <ModuleTextarea
                    name="historicoFamiliar"
                    value={formData.historicoFamiliar}
                    onChange={handleChange}
                    placeholder="Histórico familiar relevante"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("historicoFamiliar", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Observações Gerais">
                  <ModuleTextarea
                    name="observacoesGerais"
                    value={formData.observacoesGerais}
                    onChange={handleChange}
                    placeholder="Observações gerais sobre o paciente"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("observacoesGerais", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>
              </div>
            )}

            {/* Aba AVD */}
            {activeTab === "avd" && (
              <div className="space-y-4">
                <ModuleFormGroup label="Alimentação">
                  <ModuleTextarea
                    name="avdAlimentacao"
                    value={formData.avdAlimentacao}
                    onChange={handleChange}
                    placeholder="Informações sobre alimentação"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("avdAlimentacao", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Banho">
                  <ModuleTextarea
                    name="avdBanho"
                    value={formData.avdBanho}
                    onChange={handleChange}
                    placeholder="Informações sobre banho"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("avdBanho", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Vestuário">
                  <ModuleTextarea
                    name="avdVestuario"
                    value={formData.avdVestuario}
                    onChange={handleChange}
                    placeholder="Informações sobre vestuário"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("avdVestuario", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Cuidados Pessoais">
                  <ModuleTextarea
                    name="avdCuidadosPessoais"
                    value={formData.avdCuidadosPessoais}
                    onChange={handleChange}
                    placeholder="Informações sobre cuidados pessoais"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("avdCuidadosPessoais", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Sono">
                  <ModuleTextarea
                    name="avdSono"
                    value={formData.avdSono}
                    onChange={handleChange}
                    placeholder="Informações sobre sono"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("avdSono", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Esfíncter">
                  <ModuleTextarea
                    name="avdEsfincter"
                    value={formData.avdEsfincter}
                    onChange={handleChange}
                    placeholder="Informações sobre esfíncter"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("avdEsfincter", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>
              </div>
            )}

            {/* Aba Desenvolvimento da Linguagem */}
            {activeTab === "desenvolvimento-linguagem" && (
              <div className="space-y-6">
                {/* Não Verbal (Gestos) */}
                <div className="border-b pb-4 mb-4">
                  <h3 className="text-lg font-semibold mb-4 text-module-abaplus-primary">Não Verbal (Gestos)</h3>

                  <div className="space-y-4">
                    <ModuleFormGroup label="Gestos Elementares (puxar, pegar na mão...)">
                      <ModuleRadioGroup
                        name="gestosElementares"
                        value={formData.gestosElementares}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          gestosElementares: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Não simbólicos Convencionais (apontar, chamar, tchau)">
                      <ModuleRadioGroup
                        name="naoSimbolicosConvencionais"
                        value={formData.naoSimbolicosConvencionais}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          naoSimbolicosConvencionais: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Simbólicos (representação)">
                      <ModuleRadioGroup
                        name="simbolicosRepresentacao"
                        value={formData.simbolicosRepresentacao}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          simbolicosRepresentacao: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>
                  </div>
                </div>

                {/* Verbal */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-module-abaplus-primary">Verbal</h3>

                  <div className="space-y-4">
                    <ModuleFormGroup label="Verbal">
                      <ModuleRadioGroup
                        name="verbal"
                        value={formData.verbal}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          verbal: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Balbucio">
                      <ModuleRadioGroup
                        name="balbucio"
                        value={formData.balbucio}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          balbucio: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Palavras Isoladas">
                      <ModuleRadioGroup
                        name="palavrasIsoladas"
                        value={formData.palavrasIsoladas}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          palavrasIsoladas: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Quais palavras isoladas?">
                      <ModuleTextarea
                        name="quaisPalavrasIsoladas"
                        value={formData.quaisPalavrasIsoladas}
                        onChange={handleChange}
                        placeholder="Descreva quais palavras isoladas"
                        moduleColor="abaplus"
                        rows={5}
                        maxLength={4000}
                      />
                      <div className="text-xs text-gray-500 mt-1 text-right">
                        {getRemainingChars("quaisPalavrasIsoladas", 4000)} /4000 caracteres restantes
                      </div>
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Enunciado de duas palavras">
                      <ModuleRadioGroup
                        name="enunciadoDuasPalavras"
                        value={formData.enunciadoDuasPalavras}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          enunciadoDuasPalavras: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Frases">
                      <ModuleRadioGroup
                        name="frases"
                        value={formData.frases}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          frases: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Estereotipias Vocais">
                      <ModuleRadioGroup
                        name="estereotipiasVocais"
                        value={formData.estereotipiasVocais}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          estereotipiasVocais: e.target.value
                        }))}
                        moduleColor="abaplus"
                        options={[
                          { value: "SIM", label: "Sim" },
                          { value: "NAO", label: "Não" },
                          { value: "AS_VEZES", label: "Às vezes" }
                        ]}
                      />
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Quais estereotipias vocais?">
                      <ModuleTextarea
                        name="quaisEstereotipiasVocais"
                        value={formData.quaisEstereotipiasVocais}
                        onChange={handleChange}
                        placeholder="Descreva quais estereotipias vocais"
                        moduleColor="abaplus"
                        rows={5}
                        maxLength={4000}
                      />
                      <div className="text-xs text-gray-500 mt-1 text-right">
                        {getRemainingChars("quaisEstereotipiasVocais", 4000)} /4000 caracteres restantes
                      </div>
                    </ModuleFormGroup>
                  </div>
                </div>
              </div>
            )}

            {/* Aba Interação Social */}
            {activeTab === "interacao-social" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold mb-4 text-module-abaplus-primary">Interação Social</h3>

                <div className="space-y-4">
                  <ModuleFormGroup label="Falta expressão facial adequada?">
                    <ModuleRadioGroup
                      name="faltaExpressaoFacialAdequada"
                      value={formData.faltaExpressaoFacialAdequada}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        faltaExpressaoFacialAdequada: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Apresenta atenção diminuída?">
                    <ModuleRadioGroup
                      name="apresentaAtencaoDiminuida"
                      value={formData.apresentaAtencaoDiminuida}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        apresentaAtencaoDiminuida: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Apresenta preferência ao isolamento?">
                    <ModuleRadioGroup
                      name="apresentaPreferenciaIsolamento"
                      value={formData.apresentaPreferenciaIsolamento}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        apresentaPreferenciaIsolamento: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Age como se fosse surdo?">
                    <ModuleRadioGroup
                      name="ageComoSeFosseSurdo"
                      value={formData.ageComoSeFosseSurdo}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        ageComoSeFosseSurdo: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Olha para alguém que lhe fala?">
                    <ModuleRadioGroup
                      name="olhaParaAlguemQueLheFala"
                      value={formData.olhaParaAlguemQueLheFala}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        olhaParaAlguemQueLheFala: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Olha quando chamado pelo nome?">
                    <ModuleRadioGroup
                      name="olhaQuandoChamadoPeloNome"
                      value={formData.olhaQuandoChamadoPeloNome}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        olhaQuandoChamadoPeloNome: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Faz pedido de itens de seu interesse?">
                    <ModuleRadioGroup
                      name="fazPedidoItensInteresse"
                      value={formData.fazPedidoItensInteresse}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        fazPedidoItensInteresse: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Realiza imitação?">
                    <ModuleRadioGroup
                      name="realizaImitacao"
                      value={formData.realizaImitacao}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        realizaImitacao: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Brinca adequadamente com o brinquedo?">
                    <ModuleRadioGroup
                      name="brincaAdequadamenteBrinquedo"
                      value={formData.brincaAdequadamenteBrinquedo}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        brincaAdequadamenteBrinquedo: e.target.value
                      }))}
                      moduleColor="abaplus"
                      options={[
                        { value: "SIM", label: "Sim" },
                        { value: "NAO", label: "Não" },
                        { value: "AS_VEZES", label: "Às vezes" }
                      ]}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Preferências por objetos específicos?">
                    <ModuleTextarea
                      name="preferenciasObjetosEspecificos"
                      value={formData.preferenciasObjetosEspecificos}
                      onChange={handleChange}
                      placeholder="Descreva as preferências por objetos específicos"
                      moduleColor="abaplus"
                      rows={5}
                      maxLength={8000}
                    />
                    <div className="text-xs text-gray-500 mt-1 text-right">
                      {getRemainingChars("preferenciasObjetosEspecificos", 8000)} /8000 caracteres restantes
                    </div>
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Apresenta aversões (tato, audição, visual, paladar e olfativa)?">
                    <ModuleTextarea
                      name="apresentaAversoes"
                      value={formData.apresentaAversoes}
                      onChange={handleChange}
                      placeholder="Descreva as aversões"
                      moduleColor="abaplus"
                      rows={5}
                      maxLength={8000}
                    />
                    <div className="text-xs text-gray-500 mt-1 text-right">
                      {getRemainingChars("apresentaAversoes", 8000)} /8000 caracteres restantes
                    </div>
                  </ModuleFormGroup>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ModuleFormGroup label="Auto estimulação (sucção de dedos, roer unhas, tiques, genitália)?">
                      <div className="flex items-center space-x-4">
                        <ModuleRadio
                          name="autoEstimulacao"
                          value="true"
                          selectedValue={formData.autoEstimulacao === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            autoEstimulacao: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Sim"
                        />
                        <ModuleRadio
                          name="autoEstimulacao"
                          value="false"
                          selectedValue={formData.autoEstimulacao === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            autoEstimulacao: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Não"
                        />
                      </div>
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Apresenta autoagressão/heteroagressão?">
                      <div className="flex items-center space-x-4">
                        <ModuleRadio
                          name="apresentaAutoAgressaoHeteroAgressao"
                          value="true"
                          selectedValue={formData.apresentaAutoAgressaoHeteroAgressao === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            apresentaAutoAgressaoHeteroAgressao: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Sim"
                        />
                        <ModuleRadio
                          name="apresentaAutoAgressaoHeteroAgressao"
                          value="false"
                          selectedValue={formData.apresentaAutoAgressaoHeteroAgressao === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            apresentaAutoAgressaoHeteroAgressao: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Não"
                        />
                      </div>
                    </ModuleFormGroup>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <ModuleFormGroup label="Apresenta birras/irritabilidade?">
                      <div className="flex items-center space-x-4">
                        <ModuleRadio
                          name="apresentaBirrasIrritabilidade"
                          value="true"
                          selectedValue={formData.apresentaBirrasIrritabilidade === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            apresentaBirrasIrritabilidade: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Sim"
                        />
                        <ModuleRadio
                          name="apresentaBirrasIrritabilidade"
                          value="false"
                          selectedValue={formData.apresentaBirrasIrritabilidade === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            apresentaBirrasIrritabilidade: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Não"
                        />
                      </div>
                    </ModuleFormGroup>

                    <ModuleFormGroup label="Apresenta manias/rituais?">
                      <div className="flex items-center space-x-4">
                        <ModuleRadio
                          name="apresentaManiasRituais"
                          value="true"
                          selectedValue={formData.apresentaManiasRituais === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            apresentaManiasRituais: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Sim"
                        />
                        <ModuleRadio
                          name="apresentaManiasRituais"
                          value="false"
                          selectedValue={formData.apresentaManiasRituais === true ? "true" : "false"}
                          onChange={(e) => setFormData(prev => ({
                            ...prev,
                            apresentaManiasRituais: e.target.value === "true"
                          }))}
                          moduleColor="abaplus"
                          label="Não"
                        />
                      </div>
                    </ModuleFormGroup>
                  </div>
                </div>
              </div>
            )}

            {/* Aba Escola */}
            {activeTab === "escola" && (
              <div className="space-y-4">
                <ModuleFormGroup label="Estuda">
                  <div className="flex items-center space-x-4">
                    <ModuleRadio
                      name="estuda"
                      value="true"
                      selectedValue={formData.estuda === true ? "true" : "false"}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        estuda: e.target.value === "true"
                      }))}
                      moduleColor="abaplus"
                      label="Sim"
                    />
                    <ModuleRadio
                      name="estuda"
                      value="false"
                      selectedValue={formData.estuda === true ? "true" : "false"}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        estuda: e.target.value === "true"
                      }))}
                      moduleColor="abaplus"
                      label="Não"
                    />
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Nome Escola">
                  <ModuleInput
                    name="nomeEscola"
                    value={formData.nomeEscola}
                    onChange={handleChange}
                    placeholder="Nome da escola"
                    moduleColor="abaplus"
                    maxLength={500}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("nomeEscola", 500)} /500 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Série">
                  <ModuleInput
                    name="serie"
                    value={formData.serie}
                    onChange={handleChange}
                    placeholder="Série escolar"
                    moduleColor="abaplus"
                    maxLength={500}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("serie", 500)} /500 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ModuleFormGroup label="Escola Regular">
                    <div className="flex items-center space-x-4">
                      <ModuleRadio
                        name="escolaRegular"
                        value="true"
                        selectedValue={formData.escolaRegular === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          escolaRegular: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Sim"
                      />
                      <ModuleRadio
                        name="escolaRegular"
                        value="false"
                        selectedValue={formData.escolaRegular === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          escolaRegular: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Não"
                      />
                    </div>
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Professor de Apoio">
                    <div className="flex items-center space-x-4">
                      <ModuleRadio
                        name="professorApoio"
                        value="true"
                        selectedValue={formData.professorApoio === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          professorApoio: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Sim"
                      />
                      <ModuleRadio
                        name="professorApoio"
                        value="false"
                        selectedValue={formData.professorApoio === true ? "true" : "false"}
                        onChange={(e) => setFormData(prev => ({
                          ...prev,
                          professorApoio: e.target.value === "true"
                        }))}
                        moduleColor="abaplus"
                        label="Não"
                      />
                    </div>
                  </ModuleFormGroup>
                </div>
              </div>
            )}

            {/* Aba Outros */}
            {activeTab === "outros" && (
              <div className="space-y-4">
                <ModuleFormGroup label="Outro caso na família?">
                  <div className="flex items-center space-x-4">
                    <ModuleRadio
                      name="outroCasoFamilia"
                      value="true"
                      selectedValue={formData.outroCasoFamilia === true ? "true" : "false"}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        outroCasoFamilia: e.target.value === "true"
                      }))}
                      moduleColor="abaplus"
                      label="Sim"
                    />
                    <ModuleRadio
                      name="outroCasoFamilia"
                      value="false"
                      selectedValue={formData.outroCasoFamilia === true ? "true" : "false"}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        outroCasoFamilia: e.target.value === "true"
                      }))}
                      moduleColor="abaplus"
                      label="Não"
                    />
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Outros casos (Detalhamento)">
                  <ModuleTextarea
                    name="outrosCasosDetalhamento"
                    value={formData.outrosCasosDetalhamento}
                    onChange={handleChange}
                    placeholder="Detalhamento de outros casos na família"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("outrosCasosDetalhamento", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Terapias">
                  <ModuleTextarea
                    name="terapias"
                    value={formData.terapias}
                    onChange={handleChange}
                    placeholder="Informações sobre terapias"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("terapias", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>

                <ModuleFormGroup label="Expectativas da família">
                  <ModuleTextarea
                    name="expectativasFamilia"
                    value={formData.expectativasFamilia}
                    onChange={handleChange}
                    placeholder="Expectativas da família"
                    moduleColor="abaplus"
                    rows={5}
                    maxLength={4000}
                  />
                  <div className="text-xs text-gray-500 mt-1 text-right">
                    {getRemainingChars("expectativasFamilia", 4000)} /4000 caracteres restantes
                  </div>
                </ModuleFormGroup>
              </div>
            )}
          </div>
        </form>
      </div>
    </ModuleModal>
  );
};

export default AnamneseFormModal;
