// src/middlewares/auth.js
const jwt = require('jsonwebtoken');
const prisma = require('../utils/prisma');
const cacheService = require('../services/cacheService');

const MAX_TOKEN_AGE = 24 * 60 * 60; // 24 horas em segundos
const TOKEN_BLACKLIST_PREFIX = 'token:blacklist:';

// Função para verificar se um token está na lista negra
async function isTokenBlacklisted(token) {
  try {
    return await cacheService.get(`${TOKEN_BLACKLIST_PREFIX}${token}`) === true;
  } catch (error) {
    console.error('Erro ao verificar token na lista negra:', error);
    return false;
  }
}

// Função para adicionar um token à lista negra
async function addToBlacklist(token, expiresIn = MAX_TOKEN_AGE) {
  try {
    await cacheService.set(`${TOKEN_BLACKLIST_PREFIX}${token}`, true, expiresIn);
    return true;
  } catch (error) {
    console.error('Erro ao adicionar token à lista negra:', error);
    return false;
  }
}

// Token de teste para ambiente de desenvolvimento e testes
const TEST_TOKEN_PREFIX = 'TEST_TOKEN_';
const isTestEnvironment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';

const authMiddleware = async (req, res, next) => {
  try {
    console.log('Verificando autenticação para:', req.method, req.originalUrl);
    console.log('Headers:', req.headers);

    // Verifica se o token foi fornecido
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      console.log('Token de autenticação não fornecido');
      return res.status(401).json({ message: 'Token de autenticação não fornecido' });
    }

    console.log('Token de autenticação fornecido:', authHeader.substring(0, 20) + '...');

    const [scheme, token] = authHeader.split(' ');

    // Verifica se o token está no formato correto
    if (!/^Bearer$/i.test(scheme)) {
      return res.status(401).json({ message: 'Formato de token inválido' });
    }

    // Verificar se é um token de teste em ambiente de desenvolvimento/teste
    if (isTestEnvironment && token.startsWith(TEST_TOKEN_PREFIX)) {
      console.log('Token de teste detectado em ambiente de desenvolvimento/teste');

      // Extrair o ID do usuário do token de teste
      const testUserId = token.replace(TEST_TOKEN_PREFIX, '');

      // Criar um usuário de teste para fins de teste
      const testUser = {
        id: testUserId || '00000000-0000-0000-0000-000000000001',
        login: 'test_user',
        email: '<EMAIL>',
        fullName: 'Test User',
        role: 'SYSTEM_ADMIN',
        active: true,
        modules: ['ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'BASIC'],
        permissions: ['*'],
        createdAt: new Date(),
        companyId: '00000000-0000-0000-0000-000000000001',
        company: {
          id: '00000000-0000-0000-0000-000000000001',
          name: 'Test Company',
          active: true
        }
      };

      // Adicionar usuário de teste à requisição
      req.user = testUser;
      console.log('Autenticação com token de teste bem-sucedida para:', testUser.id, testUser.login);
      return next();
    }

    // Verifica se o token está na lista negra
    if (await isTokenBlacklisted(token)) {
      return res.status(401).json({ message: 'Token foi revogado' });
    }

    // Verify and decode token
    console.log('[auth] Verificando token JWT');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    console.log('[auth] Token decodificado:', decoded);

    // Check token expiration
    const currentTimestamp = Math.floor(Date.now() / 1000);
    if (decoded.exp && decoded.exp < currentTimestamp) {
      console.log('[auth] Token expirado');
      return res.status(401).json({ message: 'Token expired' });
    }

    // Check if token is for a client or a user
    const isClient = decoded.isClient === true;

    let user;

    if (isClient) {
      // Find client with necessary data
      console.log('[auth] Buscando cliente com ID:', decoded.id);
      const client = await prisma.client.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          login: true,
          email: true,
          active: true,
          createdAt: true,
          companyId: true,
          Company: {
            select: {
              id: true,
              name: true,
              active: true,
              licenseValidUntil: true
            }
          }
        }
      });

      console.log('[auth] Resultado da busca do cliente:', client ? 'Cliente encontrado' : 'Cliente não encontrado');

      // Verify if client exists and is active
      if (!client) {
        console.log('[auth] Cliente não encontrado no banco de dados');
        return res.status(401).json({ message: 'Client not found' });
      }

      if (!client.active) {
        return res.status(401).json({ message: 'Client account is deactivated' });
      }

      // Verify if company is active
      if (client.companyId && client.Company) {
        if (!client.Company.active) {
          return res.status(401).json({ message: 'Company is deactivated. Contact administrator.' });
        }

        // Check company license
        if (client.Company.licenseValidUntil && new Date(client.Company.licenseValidUntil) < new Date()) {
          return res.status(401).json({ message: 'Company license has expired. Contact administrator.' });
        }
      }

      // Add client to request with isClient flag
      user = {
        ...client,
        isClient: true,
        // Add empty modules and permissions for compatibility
        modules: [],
        permissions: [],
        role: 'CLIENT',
        company: client.Company
      };
    } else {
      // Find user with all necessary data
      console.log('[auth] Buscando usuário com ID:', decoded.id);
      user = await prisma.user.findUnique({
        where: { id: decoded.id },
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          cpf: true,
          cnpj: true,
          birthDate: true,
          address: true,
          phone: true,
          modules: true,
          permissions: true,
          role: true,
          active: true,
          createdAt: true,
          lastLoginAt: true,
          passwordChangedAt: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              active: true,
              licenseValidUntil: true
            }
          }
        }
      });
      console.log('[auth] Resultado da busca do usuário:', user ? 'Usuário encontrado' : 'Usuário não encontrado');

      // Verify if user exists and is active
      if (!user) {
        console.log('[auth] Usuário não encontrado no banco de dados');
        return res.status(401).json({ message: 'User not found' });
      }

      if (!user.active) {
        return res.status(401).json({ message: 'User account is deactivated' });
      }

      // Verify if company is active (except for SYSTEM_ADMIN)
      if (user.companyId && user.role !== 'SYSTEM_ADMIN') {
        if (!user.company.active) {
          return res.status(401).json({ message: 'Company is deactivated. Contact administrator.' });
        }

        // Check company license
        if (user.company.licenseValidUntil && new Date(user.company.licenseValidUntil) < new Date()) {
          return res.status(401).json({ message: 'Company license has expired. Contact administrator.' });
        }
      }

      // Verify if token is issued before password change
      if (user.passwordChangedAt && decoded.iat < Math.floor(user.passwordChangedAt.getTime() / 1000)) {
        return res.status(401).json({ message: 'Password has been changed. Please login again.' });
      }

      // Verify if modules in token match current user modules
      if (JSON.stringify(decoded.modules) !== JSON.stringify(user.modules)) {
        return res.status(401).json({ message: 'Token outdated, please login again' });
      }
    }

    // Log this access to audit trail (only for regular users, not clients)
    if (!isClient) {
      try {
        await prisma.auditLog.create({
          data: {
            userId: user.id,
            action: 'API_ACCESS',
            entityType: 'User',
            entityId: user.id,
            details: {
              path: req.path,
              method: req.method,
              query: req.query
            },
            ipAddress: req.ip || req.connection.remoteAddress,
            userAgent: req.headers['user-agent'],
            companyId: user.companyId
          }
        });
      } catch (auditError) {
        console.error('Error logging to audit trail:', auditError);
        // Don't block the request if audit logging fails
      }
    } else {
      // For clients, we'll just log to console instead of the database
      console.log(`Client access: ${user.id} (${user.login}) accessed ${req.method} ${req.path}`);
    }

    // Add user/client to request
    req.user = user;

    console.log('Autenticação bem-sucedida para o usuário:', user.id, user.login);
    console.log('Permissões do usuário:', user.permissions);
    console.log('Empresa do usuário:', user.companyId);

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ message: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ message: 'Token expired' });
    }

    console.error('Authentication error:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
};

// Add utility function to revoke token (for logout)
authMiddleware.revokeToken = async (token) => {
  try {
    // Decodificar o token para obter o tempo de expiração
    const decoded = jwt.verify(token, process.env.JWT_SECRET, { ignoreExpiration: true });

    // Calcular o tempo restante até a expiração
    const currentTime = Math.floor(Date.now() / 1000);
    const expiryTime = decoded.exp || (currentTime + MAX_TOKEN_AGE);
    const timeToLive = Math.max(1, expiryTime - currentTime); // Mínimo de 1 segundo

    // Adicionar à lista negra no Redis
    return await addToBlacklist(token, timeToLive);
  } catch (error) {
    console.error('Erro ao revogar token:', error);
    return false;
  }
};

module.exports = {
  authenticate: authMiddleware,
  isTokenBlacklisted,
  addToBlacklist
};