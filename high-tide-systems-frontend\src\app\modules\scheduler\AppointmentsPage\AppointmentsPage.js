"use client";

import React, { useState, useEffect, useCallback } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import { format as dateFormat, parseISO, isAfter, isBefore, isEqual } from "date-fns";
import { ptBR } from "date-fns/locale";
import ModuleHeader from "@/components/ui/ModuleHeader";
import {
  Search,
  Calendar,
  Filter,
  Eye,
  RefreshCw,
  CheckCircle,
  XCircle
} from "lucide-react";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import { AppointmentModal } from "@/components/calendar/AppointmentModal";
import AppointmentTable from "@/components/appointmentsReport/AppointmentTable";
import ReportFilters from "@/components/appointmentsReport/ReportFilter";
import ClientReportFilters from "@/components/appointmentsReport/ClientReportFilter";
import { usePermissions } from "@/hooks/usePermissions";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";

// Tutorial steps for the appointments page
const getAppointmentsTutorialSteps = (isClientUser) => [
  {
    target: 'body',
    title: 'Meus Agendamentos',
    content: isClientUser
      ? 'Esta tela permite visualizar seus agendamentos e de pessoas relacionadas a você.'
      : 'Esta tela permite visualizar todos os seus agendamentos.',
    placement: 'center'
  },
  {
    target: '.filter-section',
    title: 'Filtros',
    content: isClientUser
      ? 'Use estes filtros para encontrar agendamentos específicos por data, status, paciente, local ou tipo de serviço.'
      : 'Use estes filtros para encontrar agendamentos específicos por data, status, profissional ou texto.',
    placement: 'bottom'
  },
  {
    target: '#scheduler-appointments-table',
    title: 'Tabela de Agendamentos',
    content: isClientUser
      ? 'Aqui você pode visualizar seus agendamentos e de pessoas relacionadas a você.'
      : 'Aqui você pode visualizar todos os seus agendamentos.',
    placement: 'top'
  }
];

const AppointmentsPage = () => {
  // Estado para filtros
  const [filters, setFilters] = useState({
    search: '',
    startDate: null,
    endDate: null,
    status: []
  });

  // Estado para agendamentos
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Estado para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalAppointments, setTotalAppointments] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Estado para controle do modal de agendamento
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);

  // Permissões - clientes só podem visualizar
  const { can, isClient } = usePermissions();
  const canView = can("scheduler.appointments.view");

  // Get the appropriate tutorial steps based on user type
  const appointmentsTutorialSteps = getAppointmentsTutorialSteps(isClient());

  // Toast notifications
  const { toast_success, toast_error } = useToast();

  // Função para carregar os agendamentos
  const loadAppointments = useCallback(async () => {
    setIsLoading(true);
    try {
      // Para clientes, não enviamos o filtro de providers (profissionais)
      const clientSafeFilters = { ...filters };

      // Se o usuário for cliente, remover o filtro de providers
      if (isClient() && clientSafeFilters.providers && clientSafeFilters.providers.length > 0) {
        console.log("[CLIENT-FILTER] Removendo filtro de providers para cliente");
        delete clientSafeFilters.providers;
      }

      // Buscar todos os agendamentos de uma vez (sem paginação no backend)
      const response = await appointmentService.getAppointments({
        // Não enviamos page e limit para buscar todos os itens
        // ou enviamos um limite grande para garantir que todos os itens sejam retornados
        limit: 1000, // Um número grande para garantir que todos os itens sejam retornados
        search: clientSafeFilters.search || undefined,
        startDate: clientSafeFilters.startDate ? dateFormat(clientSafeFilters.startDate, "yyyy-MM-dd") : undefined,
        endDate: clientSafeFilters.endDate ? dateFormat(clientSafeFilters.endDate, "yyyy-MM-dd") : undefined,
        status: clientSafeFilters.status && clientSafeFilters.status.length > 0 ? clientSafeFilters.status : undefined,
        persons: clientSafeFilters.persons && clientSafeFilters.persons.length > 0 ? clientSafeFilters.persons : undefined,
        locations: clientSafeFilters.locations && clientSafeFilters.locations.length > 0 ? clientSafeFilters.locations : undefined,
        serviceTypes: clientSafeFilters.serviceTypes && clientSafeFilters.serviceTypes.length > 0 ? clientSafeFilters.serviceTypes : undefined,
      });

      // Armazenar todos os agendamentos
      const allAppointments = response.appointments || [];
      setAppointments(allAppointments);

      // Calcular o total de páginas com base no número de itens e no tamanho da página
      const total = allAppointments.length;
      const pages = Math.ceil(total / itemsPerPage);

      setTotalAppointments(total);
      setTotalPages(pages || 1);

      // Não precisamos aplicar ordenação e paginação manualmente
      // O ModuleTable já faz isso internamente
      setFilteredAppointments(allAppointments);
    } catch (error) {
      console.error("Erro ao carregar agendamentos:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar os agendamentos. Tente novamente."
      });
    } finally {
      setIsLoading(false);
    }
  }, [filters, toast_error, itemsPerPage]);

  // Carregar agendamentos quando a página carregar
  useEffect(() => {
    loadAppointments();
  }, [loadAppointments]);

  // Aplicar filtros locais
  const applyLocalFilters = useCallback(() => {
    let filtered = [...appointments];

    // Aplicar filtro de texto
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(appointment =>
        (appointment.title && appointment.title.toLowerCase().includes(searchLower)) ||
        (appointment.description && appointment.description.toLowerCase().includes(searchLower)) ||
        (appointment.provider?.name && appointment.provider.name.toLowerCase().includes(searchLower)) ||
        (appointment.person?.name && appointment.person.name.toLowerCase().includes(searchLower)) ||
        (appointment.location?.name && appointment.location.name.toLowerCase().includes(searchLower))
      );
    }

    // Aplicar filtro de data inicial
    if (filters.startDate) {
      filtered = filtered.filter(appointment =>
        isAfter(new Date(appointment.startDate), new Date(filters.startDate)) ||
        isEqual(new Date(appointment.startDate), new Date(filters.startDate))
      );
    }

    // Aplicar filtro de data final
    if (filters.endDate) {
      filtered = filtered.filter(appointment =>
        isBefore(new Date(appointment.startDate), new Date(filters.endDate)) ||
        isEqual(new Date(appointment.startDate), new Date(filters.endDate))
      );
    }

    // Aplicar filtro de status
    if (filters.status.length > 0) {
      filtered = filtered.filter(appointment =>
        filters.status.includes(appointment.status)
      );
    }

    setFilteredAppointments(filtered);
  }, [appointments, filters]);

  // Aplicar filtros locais quando os filtros mudarem
  useEffect(() => {
    applyLocalFilters();
  }, [applyLocalFilters]);

  // Função para abrir o modal de visualização
  const handleViewAppointment = (appointment) => {
    setSelectedAppointment(appointment);
    setIsModalOpen(true);
  };

  // Função para exportar agendamentos
  const handleExport = async () => {
    try {
      // Implementar exportação se necessário
      toast_success({
        title: "Exportação concluída",
        message: "Os agendamentos foram exportados com sucesso."
      });
    } catch (error) {
      console.error("Erro ao exportar agendamentos:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível exportar os agendamentos. Tente novamente."
      });
    }
  };

  // Renderizar ações para cada linha da tabela
  const renderActions = (appointment) => {
    return (
      <div className="flex items-center space-x-2">
        <button
          onClick={() => handleViewAppointment(appointment)}
          className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          title="Visualizar"
        >
          <Eye size={18} />
        </button>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Calendar size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Meus Agendamentos
        </h1>
      </div>

      {/* Cabeçalho da página com filtros integrados */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description={isClient()
          ? "Visualize seus agendamentos e de pessoas relacionadas. Utilize os filtros abaixo para encontrar agendamentos específicos."
          : "Visualize todos os seus agendamentos. Utilize os filtros abaixo para encontrar agendamentos específicos."}
        moduleColor="scheduler"
        helpButton={
          <TutorialTriggerButton
            steps={appointmentsTutorialSteps}
            tutorialName="appointments-overview"
            size="md"
          />
        }
        filters={
          isClient() ? (
            <ClientReportFilters
              filters={filters}
              setFilters={setFilters}
              onSearch={() => {
                setCurrentPage(1);  // Voltar para a primeira página ao pesquisar
                loadAppointments();
              }}
              onExport={handleExport}
              isLoading={isLoading}
            />
          ) : (
            <ReportFilters
              filters={filters}
              setFilters={setFilters}
              onSearch={() => {
                setCurrentPage(1);  // Voltar para a primeira página ao pesquisar
                loadAppointments();
              }}
              onExport={handleExport}
              isLoading={isLoading}
            />
          )
        }
      />

      {/* Tabela de agendamentos */}
      <AppointmentTable
        appointments={filteredAppointments}
        renderActions={renderActions}
        isLoading={isLoading}
        currentPage={currentPage}
        totalPages={totalPages}
        itemsPerPage={itemsPerPage}
        setCurrentPage={setCurrentPage}
        setItemsPerPage={setItemsPerPage}
        onRefresh={loadAppointments}
        tableId="scheduler-appointments-table"
      />

      {/* Modal de visualização */}
      {isModalOpen && (
        <AppointmentModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedAppointment(null);
          }}
          selectedAppointment={selectedAppointment}
          onAppointmentChange={loadAppointments}
          canEdit={false}
          canDelete={false}
        />
      )}

      {/* Confirmação de exclusão */}
      <ConfirmationDialog
        isOpen={false}
        onClose={() => {}}
        onConfirm={() => {}}
        title="Confirmar exclusão"
        message="Tem certeza que deseja excluir este agendamento? Esta ação não pode ser desfeita."
        confirmButtonText="Excluir"
        cancelButtonText="Cancelar"
        variant="danger"
      />

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default AppointmentsPage;
