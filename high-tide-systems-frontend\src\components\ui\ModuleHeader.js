"use client";

import React from "react";
import { Plus } from "lucide-react";

/**
 * Componente genérico para cabeçalhos de módulos com filtros integrados
 *
 * @param {Object} props
 * @param {string} props.title - Título do cabeçalho
 * @param {React.ReactNode} props.icon - Ícone do cabeçalho
 * @param {string} props.description - Descrição/subtítulo do cabeçalho
 * @param {React.ReactNode} props.filters - Componente de filtros a ser renderizado
 * @param {Function} props.onExport - Função para exportar dados
 * @param {boolean} props.isExporting - Estado de exportação
 * @param {boolean} props.disableExport - Desabilitar botão de exportação
 * @param {Function} props.onAddNew - Função para adicionar novo item
 * @param {string} props.addNewLabel - Texto do botão de adicionar
 * @param {boolean} props.showAddButton - Mostrar botão de adicionar
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, etc)
 * @param {string} props.gradientFrom - Cor inicial do gradiente
 * @param {string} props.gradientTo - Cor final do gradiente
 * @param {string} props.darkGradientFrom - Cor inicial do gradiente no modo escuro
 * @param {string} props.darkGradientTo - Cor final do gradiente no modo escuro
 * @param {React.ReactNode} props.children - Elementos filhos a serem renderizados na área de ações
 */
const ModuleHeader = ({
  title,
  icon,
  description,
  filters,
  // Removido onExport, isExporting, disableExport - agora o botão de exportar fica no título da página
  onAddNew,
  addNewLabel = "Novo",
  showAddButton = true,
  moduleColor = "people", // Padrão para o módulo de pessoas
  gradientFrom = "orange-500",
  gradientTo = "amber-500",
  darkGradientFrom = "orange-600",
  darkGradientTo = "amber-600",
  children,
}) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    people: {
      border: "border-module-people-border",
      text: "text-module-people-text",
      darkText: "dark:text-module-people-text-dark",
      buttonBg: "from-orange-500 to-amber-500",
      buttonBgDark: "dark:from-orange-600 dark:to-amber-600",
      buttonHover: "hover:from-orange-600 hover:to-amber-600",
      buttonHoverDark: "dark:hover:from-orange-700 dark:hover:to-amber-700",
      borderColor: "border-orange-200",
      borderColorDark: "dark:border-orange-800/30",
      textColor: "text-orange-700",
      textColorDark: "dark:text-orange-300",
      hoverBg: "hover:bg-orange-50",
      hoverBgDark: "dark:hover:bg-orange-900/20",
    },
    scheduler: {
      border: "border-module-scheduler-border",
      text: "text-module-scheduler-text",
      darkText: "dark:text-module-scheduler-text-dark",
      buttonBg: "from-purple-600 to-violet-400",
      buttonBgDark: "dark:from-purple-700 dark:to-violet-600",
      buttonHover: "hover:from-purple-700 hover:to-violet-500",
      buttonHoverDark: "dark:hover:from-purple-800 dark:hover:to-violet-700",
      borderColor: "border-purple-200",
      borderColorDark: "dark:border-purple-800/30",
      textColor: "text-purple-700",
      textColorDark: "dark:text-purple-300",
      hoverBg: "hover:bg-purple-50",
      hoverBgDark: "dark:hover:bg-purple-900/20",
    },
    admin: {
      border: "border-module-admin-border",
      text: "text-module-admin-text",
      darkText: "dark:text-module-admin-text-dark",
      buttonBg: "from-slate-500 to-slate-600",
      buttonBgDark: "dark:from-slate-600 dark:to-slate-700",
      buttonHover: "hover:from-slate-600 hover:to-slate-700",
      buttonHoverDark: "dark:hover:from-slate-700 dark:hover:to-slate-800",
      borderColor: "border-slate-200",
      borderColorDark: "dark:border-slate-700/30",
      textColor: "text-slate-700",
      textColorDark: "dark:text-slate-300",
      hoverBg: "hover:bg-slate-50",
      hoverBgDark: "dark:hover:bg-slate-800/20",
    },
    financial: {
      border: "border-module-financial-border",
      text: "text-module-financial-text",
      darkText: "dark:text-module-financial-text-dark",
      buttonBg: "from-green-500 to-emerald-500",
      buttonBgDark: "dark:from-green-600 dark:to-emerald-600",
      buttonHover: "hover:from-green-600 hover:to-emerald-600",
      buttonHoverDark: "dark:hover:from-green-700 dark:hover:to-emerald-700",
      borderColor: "border-green-200",
      borderColorDark: "dark:border-green-800/30",
      textColor: "text-green-700",
      textColorDark: "dark:text-green-300",
      hoverBg: "hover:bg-green-50",
      hoverBgDark: "dark:hover:bg-green-900/20",
    },
    // Adicione outros módulos conforme necessário
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.people;

  // Gradiente personalizado ou padrão do módulo
  let gradientClasses;

  // Verificar o módulo atual e usar as cores específicas
  if (moduleColor === "scheduler") {
    gradientClasses = "from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600";
  } else if (moduleColor === "admin") {
    gradientClasses = "from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700";
  } else if (moduleColor === "financial") {
    gradientClasses = "from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600";
  } else if (moduleColor === "people") {
    gradientClasses = "from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600";
  } else {
    gradientClasses = `from-${gradientFrom} to-${gradientTo} dark:from-${darkGradientFrom} dark:to-${darkGradientTo}`;
  }

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl border ${colors.border} dark:border-gray-700 shadow-lg dark:shadow-black/30`}>
      <div className={`bg-gradient-to-r ${gradientClasses} rounded-t-xl px-6 py-4`}>
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold text-white flex items-center">
            {icon && (
              <span className="mr-3">{icon}</span>
            )}
            {title}
          </h1>

          <div className="flex gap-2">
            {showAddButton && onAddNew && (
              <button
                onClick={onAddNew}
                className="flex items-center gap-2 px-3 py-1 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors"
              >
                <Plus size={16} />
                <span>{addNewLabel}</span>
              </button>
            )}

            {/* Renderizar children (botões adicionais) */}
            {children}
          </div>
        </div>
      </div>

      <div className="px-5 pt-3 pb-4">
        {description && (
          <p className={`${colors.text} dark:text-gray-300 mb-4`}>
            {description}
          </p>
        )}

        {filters && (
          <div className="mt-2">
            {filters}
          </div>
        )}
      </div>
    </div>
  );
};

// Componente para botões de filtro com estilo consistente
export const FilterButton = ({
  type = "submit",
  onClick,
  children,
  moduleColor = "people",
  variant = "primary", // primary ou secondary
  className = "",
  disabled = false
}) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    people: {
      primary: "bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700",
      secondary: "border border-orange-200 dark:border-orange-800/30 text-orange-700 dark:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20",
    },
    scheduler: {
      primary: "bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700",
      secondary: "border border-purple-200 dark:border-purple-800/30 text-purple-700 dark:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20",
    },
    admin: {
      primary: "bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800",
      secondary: "border border-slate-200 dark:border-slate-700/30 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800/20",
    },
    financial: {
      primary: "bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600 text-white hover:from-green-600 hover:to-emerald-600 dark:hover:from-green-700 dark:hover:to-emerald-700",
      secondary: "border border-green-200 dark:border-green-800/30 text-green-700 dark:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20",
    },
    // Adicione outros módulos conforme necessário
  };

  let colors;

  // Obter as cores do módulo atual
  colors = moduleColors[moduleColor]?.[variant] || moduleColors.people[variant];

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`px-4 py-2 ${colors} rounded-lg transition-all ${variant === 'primary' ? 'shadow-md' : ''} ${className}`}
    >
      {children}
    </button>
  );
};

export default ModuleHeader;
