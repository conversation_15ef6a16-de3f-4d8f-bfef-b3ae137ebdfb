"use client";

import React, { useState, useEffect, useCallback } from "react";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleSelect } from "@/components/ui";
import ModuleTable from "@/components/ui/ModuleTable";
import MultiSelect from "@/components/ui/multi-select.js";
import {
  Plus,
  Search,
  RefreshCw,
  Edit,
  Trash,
  FileText,
  Power,
  CheckCircle,
  XCircle,
  Users
} from "lucide-react";
import curriculumFolderService from "@/app/modules/aba/services/curriculumFolderService";
import { personsService } from "@/app/modules/people/services/personsService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import CurriculumFolderFormModal from "./CurriculumFolderFormModal";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

// Componente FilterSection local
const FilterSection = ({ isOpen, moduleColor = 'default', children }) => {
  if (!isOpen) return null;

  // Cores baseadas no módulo
  const colors = {
    default: {
      borderColor: 'border-neutral-200 dark:border-gray-700',
      bgColor: 'bg-neutral-50 dark:bg-gray-700',
    },
    people: {
      borderColor: 'border-module-people-border/30 dark:border-module-people-border-dark/30',
      bgColor: 'bg-module-people-bg/5 dark:bg-module-people-bg-dark/5',
    },
    scheduler: {
      borderColor: 'border-module-scheduler-border/30 dark:border-module-scheduler-border-dark/30',
      bgColor: 'bg-module-scheduler-bg/5 dark:bg-module-scheduler-bg-dark/5',
    },
    admin: {
      borderColor: 'border-module-admin-border/30 dark:border-module-admin-border-dark/30',
      bgColor: 'bg-module-admin-bg/5 dark:bg-module-admin-bg-dark/5',
    },
    financial: {
      borderColor: 'border-module-financial-border/30 dark:border-module-financial-border-dark/30',
      bgColor: 'bg-module-financial-bg/5 dark:bg-module-financial-bg-dark/5',
    },
    abaplus: {
      borderColor: 'border-module-abaplus-border/30 dark:border-module-abaplus-border-dark/30',
      bgColor: 'bg-module-abaplus-bg/5 dark:bg-module-abaplus-bg-dark/5',
    }
  };

  const moduleColors = colors[moduleColor] || colors.default;

  return (
    <div className={cn(
      "p-6 mb-6 rounded-xl border shadow-soft dark:shadow-lg dark:shadow-black/30",
      moduleColors.borderColor,
      moduleColors.bgColor
    )}>
      {children}
    </div>
  );
};

const CurriculumFolderPage = () => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();

  // Estados para gerenciar os dados
  const [curriculumFolders, setCurriculumFolders] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Estados para filtros
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeFilter, setActiveFilter] = useState("all");
  const [personsFilter, setPersonsFilter] = useState([]);
  const [personOptions, setPersonOptions] = useState([]);
  const [isLoadingPersonOptions, setIsLoadingPersonOptions] = useState(false);

  // Estados para modais
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [folderToDelete, setFolderToDelete] = useState(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [folderToToggle, setFolderToToggle] = useState(null);

  // Função para carregar opções de pacientes para o multi-select
  const loadPersonOptions = useCallback(async () => {
    setIsLoadingPersonOptions(true);
    try {
      // Carregar todos os pacientes para o multi-select (com limite maior)
      const response = await personsService.getPersons({
        limit: 100, // Limite maior para ter mais opções
        active: true // Apenas pacientes ativos por padrão
      });

      // Extrair os pacientes da resposta, garantindo que temos um array válido
      const personsArray = response?.persons || response?.people || [];

      const options = personsArray.map(person => ({
        value: person.id,
        label: person.fullName,
        // Guardar o nome para ordenação
        sortName: person.fullName ? person.fullName.toLowerCase() : ''
      })) || [];

      // Ordenar por nome
      options.sort((a, b) => a.sortName.localeCompare(b.sortName));

      setPersonOptions(options);
    } catch (error) {
      console.error("Erro ao carregar opções de pacientes:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar a lista de pacientes."
      });
    } finally {
      setIsLoadingPersonOptions(false);
    }
  }, [toast_error]);

  // Função para carregar as pastas curriculares
  const loadCurriculumFolders = useCallback(async () => {
    setIsLoading(true);
    try {
      // Construir parâmetros de filtro
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        search: searchTerm
      };

      // Adicionar filtro de status ativo/inativo
      if (activeFilter !== "all") {
        params.active = activeFilter === "active";
      }

      // Adicionar filtro de pacientes
      if (personsFilter.length === 1) {
        params.personId = personsFilter[0].value;
      }

      const response = await curriculumFolderService.getCurriculumFolders(params);
      setCurriculumFolders(response.curriculumFolders || []);
      setPagination(response.pagination || pagination);
    } catch (error) {
      console.error("Erro ao carregar pastas curriculares:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar as pastas curriculares."
      });
    } finally {
      setIsLoading(false);
    }
  }, [pagination.page, pagination.limit, searchTerm, activeFilter, personsFilter, toast_error]);

  // Carregar dados iniciais
  useEffect(() => {
    loadCurriculumFolders();
  }, [loadCurriculumFolders]);

  // Carregar opções de pacientes
  useEffect(() => {
    loadPersonOptions();
  }, [loadPersonOptions]);

  // Handlers para ações de usuário
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setPagination(prev => ({ ...prev, page: 1 }));
    loadCurriculumFolders();
  };

  const handleRefresh = () => {
    loadCurriculumFolders();
  };

  const handleOpenFormModal = (folder = null) => {
    setSelectedFolder(folder);
    setIsFormModalOpen(true);
  };

  const handleCloseFormModal = (shouldRefresh = false) => {
    setIsFormModalOpen(false);
    setSelectedFolder(null);
    if (shouldRefresh) {
      loadCurriculumFolders();
    }
  };

  const handleDeleteFolder = (folder) => {
    setFolderToDelete(folder);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (!folderToDelete) return;

    try {
      await curriculumFolderService.deleteCurriculumFolder(folderToDelete.id);
      toast_success({
        title: "Sucesso",
        message: "Pasta curricular excluída com sucesso."
      });
      loadCurriculumFolders();
    } catch (error) {
      console.error("Erro ao excluir pasta curricular:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível excluir a pasta curricular."
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setFolderToDelete(null);
    }
  };

  const handleToggleStatus = (folder) => {
    setFolderToToggle(folder);
    setIsStatusDialogOpen(true);
  };

  const handleConfirmToggleStatus = async () => {
    if (!folderToToggle) return;

    try {
      await curriculumFolderService.toggleCurriculumFolderStatus(folderToToggle.id);
      toast_success({
        title: "Sucesso",
        message: `Pasta curricular ${folderToToggle.active ? "desativada" : "ativada"} com sucesso.`
      });
      loadCurriculumFolders();
    } catch (error) {
      console.error("Erro ao alterar status da pasta curricular:", error);
      toast_error({
        title: "Erro",
        message: "Não foi possível alterar o status da pasta curricular."
      });
    } finally {
      setIsStatusDialogOpen(false);
      setFolderToToggle(null);
    }
  };

  const handleResetFilters = () => {
    setSearchTerm("");
    setActiveFilter("all");
    setPersonsFilter([]);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePersonsFilterChange = (selected) => {
    setPersonsFilter(selected);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Pastas Curriculares"
        description="Gerencie as pastas curriculares dos aprendizes no módulo ABA+"
        moduleColor="abaplus"
      >
        <button
          onClick={handleRefresh}
          className="p-2 text-teal-600 hover:bg-teal-50 dark:text-teal-400 dark:hover:bg-teal-900/20 rounded-lg transition-colors"
          aria-label="Atualizar lista"
        >
          <RefreshCw size={20} />
        </button>
        <FilterButton
          isOpen={isFilterOpen}
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          moduleColor="abaplus"
        />
        <button
          onClick={() => handleOpenFormModal()}
          className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
        >
          <Plus size={16} />
          Nova Pasta
        </button>
      </ModuleHeader>

      {/* Filtros */}
      <FilterSection
        isOpen={isFilterOpen}
        moduleColor="abaplus"
      >
        <form onSubmit={handleSearch} className="space-y-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <ModuleInput
                moduleColor="abaplus"
                type="text"
                placeholder="Buscar por nome da pasta ou aprendiz..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                icon={<Search size={18} />}
                className="w-full"
              />
            </div>
            <div className="flex gap-2">
              <ModuleSelect
                moduleColor="abaplus"
                value={activeFilter}
                onChange={(e) => setActiveFilter(e.target.value)}
                className="w-40"
              >
                <option value="all">Todos</option>
                <option value="active">Ativos</option>
                <option value="inactive">Inativos</option>
              </ModuleSelect>
              <button
                type="submit"
                className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors"
              >
                <Search size={16} className="sm:hidden" />
                <span className="hidden sm:inline">Buscar</span>
              </button>
              <FilterButton
                type="button"
                onClick={handleResetFilters}
                moduleColor="abaplus"
                variant="secondary"
              >
                <RefreshCw size={16} className="sm:hidden" />
                <span className="hidden sm:inline">Limpar</span>
              </FilterButton>
            </div>
          </div>

          {/* Multi-select para filtrar por múltiplos pacientes */}
          <div className="w-full">
            <MultiSelect
              label="Filtrar por Aprendiz"
              value={personsFilter}
              onChange={handlePersonsFilterChange}
              options={personOptions}
              placeholder="Selecione um aprendiz pelo nome..."
              loading={isLoadingPersonOptions}
              moduleOverride="abaplus"
            />
          </div>
        </form>
      </FilterSection>

      {/* Tabela de Pastas Curriculares */}
      <ModuleTable
        moduleColor="abaplus"
        columns={[
          { header: 'Nome da Pasta', field: 'name', width: '25%' },
          { header: 'Aprendiz', field: 'person', width: '25%' },
          { header: 'Compartilhamento', field: 'sharing', width: '20%' },
          { header: 'Status', field: 'active', width: '10%' },
          { header: 'Cadastro', field: 'createdAt', width: '10%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '10%', sortable: false }
        ]}
        data={curriculumFolders.map(folder => ({
          id: folder.id,
          name: (
            <a
              href={`/dashboard/abaplus/curriculum-folders/${folder.id}`}
              className="text-teal-600 hover:text-teal-800 dark:text-teal-400 dark:hover:text-teal-300 hover:underline"
            >
              {folder.name}
            </a>
          ),
          person: (
            <div className="flex items-center gap-2">
              <Users size={16} className="text-gray-400" />
              <span>{folder.person?.fullName || 'Não informado'}</span>
            </div>
          ),
          sharing: (
            <div className="flex flex-col text-sm">
              <span>
                Responsáveis: {folder.shareWithParents ?
                  <span className="text-green-600 dark:text-green-400">Sim</span> :
                  <span className="text-red-600 dark:text-red-400">Não</span>}
              </span>
              <span>
                Escolas: {folder.shareWithSchools ?
                  <span className="text-green-600 dark:text-green-400">Sim</span> :
                  <span className="text-red-600 dark:text-red-400">Não</span>}
              </span>
            </div>
          ),
          active: folder.active ? (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
              <CheckCircle size={12} className="mr-1" />
              Ativo
            </span>
          ) : (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
              <XCircle size={12} className="mr-1" />
              Inativo
            </span>
          ),
          createdAt: format(new Date(folder.createdAt), 'dd/MM/yyyy'),
          actions: (
            <div className="flex justify-end gap-2">
              <button
                onClick={() => handleOpenFormModal(folder)}
                className="p-1 text-blue-600 hover:bg-blue-50 rounded dark:text-blue-400 dark:hover:bg-blue-900/20"
                aria-label="Editar"
              >
                <Edit size={18} />
              </button>
              <button
                onClick={() => handleToggleStatus(folder)}
                className={`p-1 rounded ${folder.active ? 'text-amber-600 hover:bg-amber-50 dark:text-amber-400 dark:hover:bg-amber-900/20' : 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20'}`}
                aria-label={folder.active ? "Desativar" : "Ativar"}
              >
                <Power size={18} />
              </button>
              <button
                onClick={() => handleDeleteFolder(folder)}
                className="p-1 text-red-600 hover:bg-red-50 rounded dark:text-red-400 dark:hover:bg-red-900/20"
                aria-label="Excluir"
              >
                <Trash size={18} />
              </button>
            </div>
          )
        }))}
        renderRow={(item, index, moduleColors, visibleColumns) => (
          <tr key={item.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('name') && (
              <td className="px-6 py-4">{item.name}</td>
            )}
            {visibleColumns.includes('person') && (
              <td className="px-6 py-4">{item.person}</td>
            )}
            {visibleColumns.includes('sharing') && (
              <td className="px-6 py-4">{item.sharing}</td>
            )}
            {visibleColumns.includes('active') && (
              <td className="px-6 py-4">{item.active}</td>
            )}
            {visibleColumns.includes('createdAt') && (
              <td className="px-6 py-4">{item.createdAt}</td>
            )}
            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4">{item.actions}</td>
            )}
          </tr>
        )}
        isLoading={isLoading}
        pagination={{
          page: pagination.page,
          pageCount: pagination.pages,
          onPageChange: handlePageChange
        }}
        emptyMessage="Nenhuma pasta curricular encontrada"
        emptyIcon={<FileText size={48} className="text-gray-400" />}
      />

      {/* Modal de Formulário */}
      {isFormModalOpen && (
        <CurriculumFolderFormModal
          isOpen={isFormModalOpen}
          onClose={handleCloseFormModal}
          folder={selectedFolder}
        />
      )}

      {/* Diálogo de Confirmação de Exclusão */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Excluir Pasta Curricular"
        description={`Tem certeza que deseja excluir a pasta "${folderToDelete?.name}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        confirmVariant="danger"
      />

      {/* Diálogo de Confirmação de Alteração de Status */}
      <ConfirmationDialog
        isOpen={isStatusDialogOpen}
        onClose={() => setIsStatusDialogOpen(false)}
        onConfirm={handleConfirmToggleStatus}
        title={folderToToggle?.active ? "Desativar Pasta Curricular" : "Ativar Pasta Curricular"}
        description={`Tem certeza que deseja ${folderToToggle?.active ? "desativar" : "ativar"} a pasta "${folderToToggle?.name}"?`}
        confirmText={folderToToggle?.active ? "Desativar" : "Ativar"}
        cancelText="Cancelar"
        confirmVariant={folderToToggle?.active ? "warning" : "success"}
      />
    </div>
  );
};

export default CurriculumFolderPage;
