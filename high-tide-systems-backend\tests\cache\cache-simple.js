// test-cache-simple.js
require('dotenv').config();
const cacheService = require('../../src/services/cacheService');

async function testCache() {
  try {
    console.log('Inicializando serviço de cache...');
    await cacheService.initialize();
    console.log('Servio de cache inicializado');

    // Testar set e get
    const key = 'test:simple';
    const value = { message: 'Teste simples', timestamp: new Date().toISOString() };

    console.log(`Armazenando valor com chave "${key}"...`);
    await cacheService.set(key, value, 60);
    console.log('Valor armazenado');

    console.log(`Recuperando valor com chave "${key}"...`);
    const storedValue = await cacheService.get(key);
    console.log('Valor recuperado:', storedValue);

    // Fechar conexão
    await cacheService.close();
    console.log('Conex<PERSON> fechada');
  } catch (error) {
    console.error('Erro:', error);
  }
}

testCache();
