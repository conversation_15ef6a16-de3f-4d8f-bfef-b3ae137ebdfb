'use client';

import { motion } from 'framer-motion';
import { Users, Calendar, Clock, Building } from 'lucide-react';
import { useInView } from 'framer-motion';
import { useRef, useState, useEffect } from 'react';

const stats = [
  {
    id: 1,
    title: "Clínicas atendidas",
    value: 500,
    icon: <Building className="h-8 w-8 text-primary-500" />,
    suffix: "+"
  },
  {
    id: 2,
    title: "Agendamentos por mês",
    value: 50000,
    icon: <Calendar className="h-8 w-8 text-primary-500" />,
    suffix: "+"
  },
  {
    id: 3,
    title: "Horas economizadas",
    value: 10000,
    icon: <Clock className="h-8 w-8 text-primary-500" />,
    suffix: "h"
  },
  {
    id: 4,
    title: "Pacientes gerenciados",
    value: 100000,
    icon: <Users className="h-8 w-8 text-primary-500" />,
    suffix: "+"
  }
];

// Componente para animar contagem
const CountUp = ({ end, duration = 2, suffix = "" }) => {
  const [count, setCount] = useState(0);
  const nodeRef = useRef(null);
  const isInView = useInView(nodeRef, { once: true, amount: 0.5 });
  
  useEffect(() => {
    if (!isInView) return;
    
    let startTime;
    let animationFrame;
    
    const startAnimation = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const progress = timestamp - startTime;
      const percentage = Math.min(progress / (duration * 1000), 1);
      
      // Função de easing para desacelerar no final
      const easeOutQuart = 1 - Math.pow(1 - percentage, 4);
      
      setCount(Math.floor(easeOutQuart * end));
      
      if (percentage < 1) {
        animationFrame = requestAnimationFrame(startAnimation);
      }
    };
    
    animationFrame = requestAnimationFrame(startAnimation);
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [end, duration, isInView]);
  
  return (
    <span ref={nodeRef} className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white">
      {isInView ? count.toLocaleString() : "0"}
      {suffix}
    </span>
  );
};

const Stats = () => {
  return (
    <section className="py-20 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div 
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Números que impressionam
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            O High Tide Systems está transformando a gestão de clínicas e consultórios em todo o Brasil.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.id}
              className="bg-white dark:bg-gray-900 rounded-xl p-8 shadow-lg border border-gray-100 dark:border-gray-700 text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="w-16 h-16 bg-primary-50 dark:bg-primary-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                {stat.icon}
              </div>
              <div className="mb-2">
                <CountUp end={stat.value} suffix={stat.suffix} />
              </div>
              <p className="text-lg text-gray-600 dark:text-gray-300">
                {stat.title}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Stats;
