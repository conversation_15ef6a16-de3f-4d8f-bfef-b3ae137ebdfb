'use client';

import React, { useEffect, useRef, useMemo, useCallback, useState } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowLeft, MoreVertical, UserPlus, LogOut, Users, X, Trash2 } from 'lucide-react';
import ChatInput from './ChatInput';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import MultiUserSearch from './MultiUserSearch';
import { ConfirmationDialog } from '../../components/ui';

const ChatConversation = ({ conversationId, onBack, compact = false }) => {
  const {
    messages,
    loadMessages,
    conversations,
    removeParticipantFromGroup,
    addParticipantToGroup,
    addMultipleParticipantsToGroup
  } = useChat();
  const { user } = useAuth();
  const messagesEndRef = useRef(null);

  // Estados para gerenciar o menu de opções e adição de participantes
  const [showOptionsMenu, setShowOptionsMenu] = useState(false);
  const [showAddParticipant, setShowAddParticipant] = useState(false);
  const [showParticipantsList, setShowParticipantsList] = useState(false);
  const [addingParticipants, setAddingParticipants] = useState(false);

  // Estados para diálogos de confirmação
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [confirmAction, setConfirmAction] = useState({
    type: '', // 'leave-group', 'delete-conversation', 'delete-messages'
    title: '',
    message: '',
    onConfirm: () => {}
  });

  // Memoizar a conversa atual para evitar recalculos
  const conversation = useMemo(() => {
    return conversations.find(c => c.id === conversationId);
  }, [conversations, conversationId]);

  // Memoizar as mensagens da conversa para evitar recalculos
  const conversationMessages = useMemo(() => {
    return messages[conversationId] || [];
  }, [messages, conversationId]);

  // Referência para controlar se as mensagens já foram carregadas
  const messagesLoadedRef = useRef(false);

  // Carregar mensagens ao montar o componente - apenas uma vez
  useEffect(() => {
    if (conversationId && !messagesLoadedRef.current) {
      // Verificar se já temos mensagens para esta conversa antes de carregar
      if (!messages[conversationId] || messages[conversationId].length === 0) {
        console.log('Carregando mensagens para conversa:', conversationId);
        loadMessages(conversationId);
        messagesLoadedRef.current = true;
      } else {
        messagesLoadedRef.current = true;
      }
    }

    // Limpar a referência quando o componente for desmontado
    return () => {
      messagesLoadedRef.current = false;
    };
  }, [conversationId, loadMessages, messages]);

  // Rolar para a última mensagem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationMessages]);

  // Verificar se o usuário atual é administrador do grupo
  const isGroupAdmin = useMemo(() => {
    if (!conversation || !user) return false;

    const userParticipant = conversation.participants?.find(p => p.userId === user.id);
    return userParticipant?.isAdmin || false;
  }, [conversation, user]);

  // Verificar se é uma conversa de grupo
  const isGroupChat = useMemo(() => {
    return conversation?.type === 'GROUP';
  }, [conversation]);

  // Função para confirmar saída do grupo
  const handleLeaveGroup = useCallback(() => {
    if (!conversation || !user) return;

    // Configurar o diálogo de confirmação
    setConfirmAction({
      type: 'leave-group',
      title: 'Sair do grupo',
      message: `Tem certeza que deseja sair do grupo "${conversation.title || 'Grupo'}"?`,
      onConfirm: () => {
        removeParticipantFromGroup(conversation.id, user.id)
          .then(() => {
            console.log('Saiu do grupo com sucesso');

            // Disparar evento para atualizar a lista de conversas
            setTimeout(() => {
              console.log('Forçando atualização da lista de conversas após sair do grupo');
              window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                detail: {
                  type: 'conversations',
                  action: 'delete',
                  conversationId: conversation.id,
                  timestamp: Date.now()
                }
              }));
            }, 300);

            // A navegação de volta para a lista de conversas é tratada no contexto
          })
          .catch(error => {
            console.error('Erro ao sair do grupo:', error);
            alert('Não foi possível sair do grupo. Tente novamente mais tarde.');
          });
      }
    });

    // Abrir o diálogo de confirmação
    setConfirmDialogOpen(true);
    setShowOptionsMenu(false);
  }, [conversation, user, removeParticipantFromGroup]);

  // Função para adicionar múltiplos participantes
  const handleAddParticipants = useCallback((selectedUsers) => {
    if (!conversation || !selectedUsers || selectedUsers.length === 0) return;

    setAddingParticipants(true);

    addMultipleParticipantsToGroup(conversation.id, selectedUsers)
      .then((result) => {
        console.log('Resultado da adição de participantes:', result);

        if (result.success) {
          console.log(`${result.successCount} participantes adicionados com sucesso`);

          if (result.errorCount > 0) {
            console.warn(`${result.errorCount} participantes não puderam ser adicionados`);
            // Poderia mostrar um toast com essa informação
          }
        } else {
          console.error('Falha ao adicionar participantes');
          alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');
        }

        setShowAddParticipant(false);
      })
      .catch(error => {
        console.error('Erro ao adicionar participantes:', error);
        alert('Não foi possível adicionar os participantes. Tente novamente mais tarde.');
      })
      .finally(() => {
        setAddingParticipants(false);
      });
  }, [conversation, addMultipleParticipantsToGroup]);

  // Função para apagar conversa (sair da conversa)
  const handleDeleteConversation = useCallback(() => {
    if (!conversation || !user) return;

    // Configurar o diálogo de confirmação
    setConfirmAction({
      type: 'delete-conversation',
      title: 'Apagar conversa',
      message: 'Tem certeza que deseja apagar esta conversa? Esta ação não pode ser desfeita.',
      onConfirm: () => {
        // Para apagar uma conversa, usamos a mesma função de sair do grupo
        // No backend, isso marca o participante como tendo saído da conversa
        removeParticipantFromGroup(conversation.id, user.id)
          .then(() => {
            console.log('Conversa apagada com sucesso');

            // Disparar evento para atualizar a lista de conversas
            setTimeout(() => {
              console.log('Forçando atualização da lista de conversas após apagar conversa');
              window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                detail: {
                  type: 'conversations',
                  action: 'delete',
                  conversationId: conversation.id,
                  timestamp: Date.now()
                }
              }));
            }, 300);

            // A navegação de volta para a lista de conversas é tratada no contexto
          })
          .catch(error => {
            console.error('Erro ao apagar conversa:', error);
            alert('Não foi possível apagar a conversa. Tente novamente mais tarde.');
          });
      }
    });

    // Abrir o diálogo de confirmação
    setConfirmDialogOpen(true);
    setShowOptionsMenu(false);
  }, [conversation, user, removeParticipantFromGroup]);

  // Obter o nome da conversa - memoizado para evitar recalculos
  const getConversationName = useCallback(() => {
    if (!conversation) return 'Conversa';

    if (conversation.type === 'GROUP') {
      return conversation.title || 'Grupo';
    }

    const otherParticipant = conversation.participants?.find(
      p => p.userId !== user?.id
    );

    return otherParticipant?.user?.fullName || 'Usuário';
  }, [conversation, user?.id]);

  // Obter a imagem da conversa - memoizado para evitar recalculos
  const getConversationImage = useCallback(() => {
    if (!conversation) return null;

    if (conversation.type === 'GROUP') {
      return null;
    }

    const otherParticipant = conversation.participants?.find(
      p => p.userId !== user?.id
    );

    return otherParticipant?.user?.profileImageUrl;
  }, [conversation, user?.id]);

  // Obter iniciais para avatar - memoizado para evitar recalculos
  const getInitials = useCallback((name) => {
    if (!name) return 'U';

    try {
      const names = name.split(' ');
      if (names.length === 1) return names[0].charAt(0);

      return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
    } catch (error) {
      console.error('Erro ao obter iniciais:', error);
      return 'U';
    }
  }, []);

  // Formatar data da mensagem - memoizado para evitar recalculos
  const formatMessageDate = useCallback((timestamp) => {
    if (!timestamp) return '';

    try {
      return format(new Date(timestamp), 'HH:mm', { locale: ptBR });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return '';
    }
  }, []);

  // Agrupar mensagens por data - memoizado para evitar recalculos
  const groupMessagesByDate = useCallback((messages) => {
    if (!messages || messages.length === 0) {
      console.log("Nenhuma mensagem para agrupar");
      return [];
    }

    console.log(`Agrupando ${messages.length} mensagens`);

    try {
      const groups = {};

      // Ordenar mensagens por data (mais antigas primeiro)
      const sortedMessages = [...messages].sort((a, b) =>
        new Date(a.createdAt) - new Date(b.createdAt)
      );

      sortedMessages.forEach(message => {
        if (!message || !message.createdAt) {
          console.warn("Mensagem inválida encontrada:", message);
          return;
        }

        try {
          const messageDate = new Date(message.createdAt);
          const date = format(messageDate, 'dd/MM/yyyy');

          if (!groups[date]) {
            groups[date] = [];
          }

          groups[date].push(message);
        } catch (err) {
          console.error("Erro ao processar mensagem:", err, message);
        }
      });

      // Verificar se temos grupos
      if (Object.keys(groups).length === 0) {
        console.warn("Nenhum grupo criado após processamento");
        return [];
      }

      // Ordenar os grupos por data (mais antigos primeiro)
      const result = Object.entries(groups)
        .sort(([dateA], [dateB]) => {
          // Converter strings de data para objetos Date para comparação
          const [dayA, monthA, yearA] = dateA.split('/').map(Number);
          const [dayB, monthB, yearB] = dateB.split('/').map(Number);
          return new Date(yearA, monthA - 1, dayA) - new Date(yearB, monthB - 1, dayB);
        })
        .map(([date, messages]) => ({
          date,
          messages
        }));

      console.log(`Criados ${result.length} grupos de mensagens`);
      return result;
    } catch (error) {
      console.error('Erro ao agrupar mensagens:', error);
      return [];
    }
  }, []);

  // Memoizar os grupos de mensagens para evitar recalculos
  const messageGroups = useMemo(() => {
    return groupMessagesByDate(conversationMessages);
  }, [groupMessagesByDate, conversationMessages]);

  // Se a conversa não for encontrada, mostrar mensagem de erro
  if (!conversation) {
    return (
      <div className="flex flex-col h-full">
        <div className="p-4 border-b border-blue-200 dark:border-blue-700 flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg">
          <button
            onClick={onBack}
            className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
            aria-label="Voltar"
          >
            <ArrowLeft size={compact ? 16 : 20} />
          </button>
          <h3 className="font-medium text-white text-lg">
            Conversa não encontrada
          </h3>
        </div>
        <div className="flex-1 flex items-center justify-center p-4 text-gray-500 dark:text-gray-400">
          <p>A conversa não foi encontrada ou foi removida.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Cabeçalho */}
      <div className="p-4 border-b border-orange-200 dark:border-orange-700 flex items-center gap-2  text-white">
        <button
          onClick={onBack}
          className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
          aria-label="Voltar"
        >
          <ArrowLeft size={compact ? 16 : 20} />
        </button>

        <div className="flex items-center gap-2 flex-1">
          {getConversationImage() ? (
            <img
              src={getConversationImage()}
              alt={getConversationName()}
              className={`${compact ? 'h-8 w-8' : 'h-10 w-10'} rounded-full object-cover`}
              onError={(e) => {
                e.target.onerror = null;
                e.target.style.display = 'none';
              }}
            />
          ) : (
            <div className={`${compact ? 'h-8 w-8' : 'h-10 w-10'} rounded-full bg-white/20 flex items-center justify-center text-white font-medium shadow-sm`}>
              {getInitials(getConversationName())}
            </div>
          )}

          <div>
            <h3 className={`font-medium text-white ${compact ? 'text-sm' : 'text-base'}`}>
              {getConversationName()}
            </h3>
            {conversation?.isOnline && (
              <p className="text-xs text-white/80">Online</p>
            )}
          </div>
        </div>

        <div className="relative">
          <button
            onClick={() => setShowOptionsMenu(!showOptionsMenu)}
            className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
            aria-label="Mais opções"
          >
            <MoreVertical size={compact ? 16 : 20} />
          </button>

          {/* Menu de opções */}
          {showOptionsMenu && (
            <div className="absolute right-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 w-48 z-10">
              {isGroupChat ? (
                <>
                  <button
                    onClick={() => {
                      setShowOptionsMenu(false);
                      setShowParticipantsList(true);
                    }}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Users size={16} />
                    <span>Ver participantes</span>
                  </button>

                  {isGroupAdmin && (
                    <button
                      onClick={() => {
                        setShowOptionsMenu(false);
                        setShowAddParticipant(true);
                      }}
                      className="w-full flex items-center gap-2 px-4 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                    >
                      <UserPlus size={16} />
                      <span>Adicionar participante</span>
                    </button>
                  )}

                  <button
                    onClick={handleLeaveGroup}
                    className="w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <LogOut size={16} />
                    <span>Sair do grupo</span>
                  </button>
                </>
              ) : (
                // Opções para conversas individuais
                <button
                  onClick={handleDeleteConversation}
                  className="w-full flex items-center gap-2 px-4 py-2 text-left text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                >
                  <Trash2 size={16} />
                  <span>Apagar conversa</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Tela de adicionar participantes */}
      {showAddParticipant && (
        <div className="absolute inset-0 bg-white dark:bg-gray-800 z-20 flex flex-col">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white">
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowAddParticipant(false)}
                className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
                aria-label="Voltar"
              >
                <ArrowLeft size={20} />
              </button>
              <h3 className="font-medium text-white text-base">Adicionar participantes</h3>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            <MultiUserSearch
              onAddUsers={handleAddParticipants}
              onClose={() => setShowAddParticipant(false)}
              title="Adicionar participantes ao grupo"
            />
          </div>

          {addingParticipants && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-30">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg flex flex-col items-center">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-orange-500 mb-4"></div>
                <p className="text-gray-700 dark:text-gray-300">Adicionando participantes...</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Lista de participantes */}
      {showParticipantsList && (
        <div className="absolute inset-0 bg-white dark:bg-gray-800 z-20 flex flex-col">
          <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white">
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowParticipantsList(false)}
                className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
                aria-label="Voltar"
              >
                <ArrowLeft size={20} />
              </button>
              <h3 className="font-medium text-white text-base">Participantes do grupo</h3>
            </div>

            {isGroupAdmin && (
              <button
                onClick={() => {
                  setShowParticipantsList(false);
                  setShowAddParticipant(true);
                }}
                className="p-1.5 text-white/80 hover:text-white rounded-full hover:bg-white/20 transition-colors"
                aria-label="Adicionar participante"
              >
                <UserPlus size={20} />
              </button>
            )}
          </div>

          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              {conversation?.participants?.map(participant => (
                <div key={participant.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-gray-700 dark:text-gray-300 font-medium">
                      {getInitials(participant.user?.fullName || 'Usuário')}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">
                        {participant.user?.fullName || 'Usuário'}
                        {participant.userId === user?.id && ' (Você)'}
                      </p>
                      {participant.isAdmin && (
                        <span className="text-xs text-orange-600 dark:text-orange-400">Administrador</span>
                      )}
                    </div>
                  </div>

                  {isGroupAdmin && participant.userId !== user?.id && (
                    <button
                      onClick={() => {
                        if (window.confirm(`Tem certeza que deseja remover ${participant.user?.fullName || 'este participante'} do grupo?`)) {
                          removeParticipantFromGroup(conversation.id, participant.userId)
                            .catch(error => {
                              console.error('Erro ao remover participante:', error);
                              alert('Não foi possível remover o participante. Tente novamente mais tarde.');
                            });
                        }
                      }}
                      className="p-1.5 text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                      aria-label="Remover participante"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Mensagens */}
      <div className="flex-1 overflow-y-auto p-5 space-y-5">
        {messageGroups.map(group => (
          <div key={group.date} className="space-y-3">
            <div className="flex justify-center">
              <div className="px-3 py-1 bg-orange-100 dark:bg-orange-900/30 rounded-full text-xs text-orange-700 dark:text-orange-300 shadow-sm">
                {group.date}
              </div>
            </div>

            {group.messages.map(message => {
              const isOwnMessage = message.senderId === user?.id;
              const isGroupChat = conversation.type === 'GROUP';

              // Encontrar o nome do remetente para mensagens de grupo
              const getSenderName = () => {
                if (isOwnMessage) return 'Você';

                const sender = conversation.participants?.find(
                  p => p.userId === message.senderId
                );

                return sender?.user?.fullName || 'Usuário';
              };

              // Obter as iniciais do remetente para mensagens de grupo
              const getSenderInitials = () => {
                if (isOwnMessage) return 'Você';

                const senderName = getSenderName();
                return getInitials(senderName);
              };

              return (
                <div
                  key={message.id}
                  className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}
                >
                  {/* Avatar do remetente (apenas para mensagens de grupo e que não são do usuário atual) */}
                  {isGroupChat && !isOwnMessage && (
                    <div className="mr-2 flex flex-col items-center justify-start">
                      <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-xs font-medium">
                        {getSenderInitials()}
                      </div>
                    </div>
                  )}

                  <div className={`max-w-[75%] ${compact ? 'max-w-[85%]' : ''}`}>
                    {/* Nome do remetente (apenas para mensagens de grupo e que não são do usuário atual) */}
                    {isGroupChat && !isOwnMessage && (
                      <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-1 ml-1">
                        {getSenderName()}
                      </div>
                    )}

                    <div
                      className={`px-4 py-3 rounded-lg shadow-sm ${
                        isOwnMessage
                          ? 'bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-br-none'
                          : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-none border border-orange-100 dark:border-orange-800'
                      }`}
                    >
                      {message.content}
                    </div>
                    <div className={`text-xs mt-1.5 ${isOwnMessage ? 'text-right' : ''} text-gray-500 dark:text-gray-400`}>
                      {formatMessageDate(message.createdAt)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        ))}

        {conversationMessages.length === 0 && (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            <p className="text-center text-sm">
              Nenhuma mensagem ainda. Comece a conversar!
            </p>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <ChatInput conversationId={conversationId} compact={compact} />

      {/* Diálogo de confirmação */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={confirmAction.onConfirm}
        title={confirmAction.title}
        message={confirmAction.message}
        confirmText="Confirmar"
        cancelText="Cancelar"
        variant="warning"
      />
    </div>
  );
};

export default ChatConversation;
