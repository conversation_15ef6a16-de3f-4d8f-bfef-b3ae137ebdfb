const axios = require('axios');
const fs = require('fs');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// CONFIGURAÇÕES
const BEARER_TOKEN = process.env.BEARER_TOKEN || 'SEU_TOKEN_AQUI';
const BASE_URL = 'https://query-api-prd.abamais.com/v1';
const DELAY_BETWEEN_REQUESTS = 100; // Delay em ms entre requisições

// Headers padrão para as requisições
const headers = {
  'Accept': 'application/json, text/plain, */*',
  'Accept-Language': 'pt-BR',
  'Authorization': `Bearer ${BEARER_TOKEN}`,
  'Connection': 'keep-alive',
  'Origin': 'https://app.abamais.com',
  'Referer': 'https://app.abamais.com/',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-site',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 OPR/118.0.0.0',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Opera GX";v="118", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"'
};

// Função para delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Função para buscar todos os pacientes (com paginação)
async function fetchAllPatients() {
  console.log('Buscando lista de pacientes...');
  
  let allPatients = [];
  let currentPage = 1;
  let totalPages = 1;

  try {
    // Primeira requisição para descobrir total de páginas
    const firstResponse = await axios.get(`${BASE_URL}/clientes`, {
      headers,
      params: {
        ativo: 'true',
        itensPorPagina: '100', // Máximo por página
        pagina: 1,
        v: Date.now()
      }
    });

    if (firstResponse.data.code === 'OK' && firstResponse.data.status === 'SUCCESS') {
      totalPages = firstResponse.data.data.totalPaginas;
      allPatients = firstResponse.data.data.resultados;
      
      console.log(`Encontrados ${firstResponse.data.data.totalResultados} pacientes em ${totalPages} páginas`);

      // Buscar páginas restantes
      for (let page = 2; page <= totalPages; page++) {
        console.log(`Buscando página ${page}/${totalPages}...`);
        
        const response = await axios.get(`${BASE_URL}/clientes`, {
          headers,
          params: {
            ativo: 'true',
            itensPorPagina: '100',
            pagina: page,
            v: Date.now()
          }
        });

        if (response.data.code === 'OK' && response.data.status === 'SUCCESS') {
          allPatients = allPatients.concat(response.data.data.resultados);
        }

        await delay(DELAY_BETWEEN_REQUESTS);
      }

      console.log(`Total de pacientes coletados: ${allPatients.length}`);
      return allPatients;
    } else {
      throw new Error('Resposta inválida da API');
    }
  } catch (error) {
    console.error('Erro ao buscar pacientes:', error.message);
    throw error;
  }
}

// Função para buscar detalhes de um paciente específico
async function fetchPatientDetails(codigo) {
  try {
    const response = await axios.get(`${BASE_URL}/clientes/${codigo}`, {
      headers
    });

    if (response.data.code === 'OK' && response.data.status === 'SUCCESS') {
      return response.data.data;
    } else {
      console.warn(`Erro ao buscar detalhes do paciente ${codigo}`);
      return null;
    }
  } catch (error) {
    console.error(`Erro ao buscar detalhes do paciente ${codigo}:`, error.message);
    return null;
  }
}

// Função para mapear dados da API para o schema Client
function mapToClientSchema(patientData) {
  const now = new Date().toISOString();
  
  return {
    id: uuidv4(),
    login: patientData.usuario?.nome || patientData.codigo?.toString() || '',
    email: patientData.email || `${patientData.codigo}@paciente.com`,
    password: '$2b$10$defaultHashedPassword', // Hash padrão - deve ser alterado
    active: patientData.ativo !== false,
    createdAt: patientData.dataInclusao ? new Date(patientData.dataInclusao).toISOString() : now,
    updatedAt: now,
    createdById: null, // Seria necessário mapear quem criou
    companyId: null, // Será preenchido na migração para banco
    deletedAt: patientData.ativo === false ? now : null,
    deletedById: null,
    // Campos extras da API original
    codigoOriginal: patientData.codigo,
    nomeCompleto: patientData.nome ? patientData.nome.toString() : '',
    convenioSaude: patientData.convenioSaude ? patientData.convenioSaude.toString() : null,
    grupoUsuario: patientData.usuario?.grupoUsuario?.nome || null
  };
}

// Função para mapear dados da API para o schema Person
function mapToPersonSchema(patientData, clientId) {
  const now = new Date().toISOString();
  
  return {
    id: uuidv4(),
    email: patientData.email ? patientData.email.toString() : null,
    phone: patientData.celular ? patientData.celular.toString() : (patientData.fone ? patientData.fone.toString() : null),
    address: patientData.logradouro ? patientData.logradouro.toString() : null,
    city: patientData.cidade ? patientData.cidade.toString() : null,
    state: patientData.estado ? patientData.estado.toString() : null,
    birthDate: patientData.dataNascimento ? new Date(patientData.dataNascimento).toISOString() : null,
    notes: patientData.obs ? patientData.obs.toString() : null,
    active: patientData.ativo !== false,
    createdAt: patientData.dataInclusao ? new Date(patientData.dataInclusao).toISOString() : now,
    updatedAt: now,
    deletedAt: patientData.ativo === false ? now : null,
    neighborhood: patientData.bairro ? patientData.bairro.toString() : null,
    postalCode: patientData.cep ? patientData.cep.toString() : null,
    clientId: clientId,
    cpf: patientData.cpf ? patientData.cpf.toString() : null,
    createdById: null, // Seria necessário mapear quem criou
    deletedById: null,
    fullName: patientData.nome ? patientData.nome.toString() : '',
    gender: patientData.sexo ? patientData.sexo.toString() : null,
    profileImageUrl: patientData.foto ? `https://app.abamais.com/photos/${patientData.foto}` : null,
    relationship: null, // Para pacientes principais, não há relacionamento
    useClientEmail: patientData.email ? true : false,
    useClientPhone: patientData.celular || patientData.fone ? true : false,
    // Campos extras da API original
    codigoOriginal: patientData.codigo,
    mae: patientData.mae ? patientData.mae.toString() : null,
    pai: patientData.pai ? patientData.pai.toString() : null,
    rg: patientData.rg ? patientData.rg.toString() : null,
    orgaoExpeditor: patientData.orgaoExpeditor ? patientData.orgaoExpeditor.toString() : null,
    profissao: patientData.profissao ? patientData.profissao.toString() : null,
    naturalidade: patientData.naturalidade ? patientData.naturalidade.toString() : null,
    contato: patientData.contato ? patientData.contato.toString() : null,
    site: patientData.site ? patientData.site.toString() : null
  };
}

// Função para mapear contatos do paciente
function mapToContactSchema(contactData, personId) {
  const now = new Date().toISOString();
  
  return {
    id: uuidv4(),
    name: contactData.nome ? contactData.nome.toString() : '',
    relationship: contactData.descricao ? contactData.descricao.toString() : null,
    email: null, // Não vem nos dados da API
    phone: null, // Não vem nos dados da API
    notes: null,
    createdAt: now,
    updatedAt: now,
    personId: personId,
    createdById: null, // Seria necessário mapear quem criou
    deletedById: null,
    // Campos extras da API original
    codigoOriginal: contactData.codigo
  };
}

// Função para achatar objetos aninhados
function flattenObject(obj, prefix = '') {
  const flattened = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key]) && !(obj[key] instanceof Date)) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }
  
  return flattened;
}

// Função principal
async function main() {
  try {
    console.log('Iniciando migração de pacientes...\n');

    // 1. Buscar lista de pacientes
    const patients = await fetchAllPatients();
    const patientCodes = patients.map(patient => patient.codigo);
    
    console.log(`\nBuscando detalhes para ${patientCodes.length} pacientes...\n`);

    // 2. Buscar detalhes de cada paciente
    const allPatientDetails = [];
    let processed = 0;

    for (const codigo of patientCodes) {
      console.log(`Processando paciente ${codigo} (${++processed}/${patientCodes.length})`);
      
      const details = await fetchPatientDetails(codigo);
      if (details) {
        allPatientDetails.push(details);
      }
      
      // Delay para não sobrecarregar a API
      await delay(DELAY_BETWEEN_REQUESTS);
    }

    console.log(`\nColetados detalhes de ${allPatientDetails.length} pacientes`);

    // 3. Gerar CSV com dados brutos
    console.log('\nGerando CSV com dados brutos dos pacientes...');
    
    if (allPatientDetails.length > 0) {
      // Coletar todas as chaves únicas dos objetos
      const allKeys = new Set();
      allPatientDetails.forEach(patient => {
        const flattenedPatient = flattenObject(patient);
        Object.keys(flattenedPatient).forEach(key => allKeys.add(key));
      });

      const rawCsvWriter = createCsvWriter({
        path: 'pacientes_dados_brutos.csv',
        header: Array.from(allKeys).map(key => ({ id: key, title: key }))
      });

      const flattenedData = allPatientDetails.map(patient => flattenObject(patient));
      await rawCsvWriter.writeRecords(flattenedData);
      console.log('✓ CSV de dados brutos dos pacientes salvo: pacientes_dados_brutos.csv');
    }

    // 4. Gerar CSVs formatados para os schemas
    console.log('Gerando CSVs formatados para schemas...');
    
    const clientsData = [];
    const personsData = [];
    const contactsData = [];

    allPatientDetails.forEach(patient => {
      // Mapear para Client
      const clientData = mapToClientSchema(patient);
      clientsData.push(clientData);

      // Mapear para Person
      const personData = mapToPersonSchema(patient, clientData.id);
      personsData.push(personData);

      // Mapear contatos se existirem
      if (patient.contatos && Array.isArray(patient.contatos)) {
        patient.contatos.forEach(contact => {
          const contactData = mapToContactSchema(contact, personData.id);
          contactsData.push(contactData);
        });
      }
    });

    // 5. Salvar CSV dos clientes
    const clientCsvWriter = createCsvWriter({
      path: 'pacientes_schema_client.csv',
      header: [
        { id: 'id', title: 'id' },
        { id: 'login', title: 'login' },
        { id: 'email', title: 'email' },
        { id: 'password', title: 'password' },
        { id: 'active', title: 'active' },
        { id: 'createdAt', title: 'createdAt' },
        { id: 'updatedAt', title: 'updatedAt' },
        { id: 'createdById', title: 'createdById' },
        { id: 'companyId', title: 'companyId' },
        { id: 'deletedAt', title: 'deletedAt' },
        { id: 'deletedById', title: 'deletedById' },
        { id: 'codigoOriginal', title: 'codigoOriginal' },
        { id: 'nomeCompleto', title: 'nomeCompleto' },
        { id: 'convenioSaude', title: 'convenioSaude' },
        { id: 'grupoUsuario', title: 'grupoUsuario' }
      ]
    });

    await clientCsvWriter.writeRecords(clientsData);
    console.log('✓ CSV formatado para schema Client salvo: pacientes_schema_client.csv');

    // 6. Salvar CSV das pessoas
    const personCsvWriter = createCsvWriter({
      path: 'pacientes_schema_person.csv',
      header: [
        { id: 'id', title: 'id' },
        { id: 'email', title: 'email' },
        { id: 'phone', title: 'phone' },
        { id: 'address', title: 'address' },
        { id: 'city', title: 'city' },
        { id: 'state', title: 'state' },
        { id: 'birthDate', title: 'birthDate' },
        { id: 'notes', title: 'notes' },
        { id: 'active', title: 'active' },
        { id: 'createdAt', title: 'createdAt' },
        { id: 'updatedAt', title: 'updatedAt' },
        { id: 'deletedAt', title: 'deletedAt' },
        { id: 'neighborhood', title: 'neighborhood' },
        { id: 'postalCode', title: 'postalCode' },
        { id: 'clientId', title: 'clientId' },
        { id: 'cpf', title: 'cpf' },
        { id: 'createdById', title: 'createdById' },
        { id: 'deletedById', title: 'deletedById' },
        { id: 'fullName', title: 'fullName' },
        { id: 'gender', title: 'gender' },
        { id: 'profileImageUrl', title: 'profileImageUrl' },
        { id: 'relationship', title: 'relationship' },
        { id: 'useClientEmail', title: 'useClientEmail' },
        { id: 'useClientPhone', title: 'useClientPhone' },
        { id: 'codigoOriginal', title: 'codigoOriginal' },
        { id: 'mae', title: 'mae' },
        { id: 'pai', title: 'pai' },
        { id: 'rg', title: 'rg' },
        { id: 'orgaoExpeditor', title: 'orgaoExpeditor' },
        { id: 'profissao', title: 'profissao' },
        { id: 'naturalidade', title: 'naturalidade' },
        { id: 'contato', title: 'contato' },
        { id: 'site', title: 'site' }
      ]
    });

    await personCsvWriter.writeRecords(personsData);
    console.log('✓ CSV formatado para schema Person salvo: pacientes_schema_person.csv');

    // 7. Salvar CSV dos contatos (se houver)
    if (contactsData.length > 0) {
      const contactCsvWriter = createCsvWriter({
        path: 'pacientes_schema_contact.csv',
        header: [
          { id: 'id', title: 'id' },
          { id: 'name', title: 'name' },
          { id: 'relationship', title: 'relationship' },
          { id: 'email', title: 'email' },
          { id: 'phone', title: 'phone' },
          { id: 'notes', title: 'notes' },
          { id: 'createdAt', title: 'createdAt' },
          { id: 'updatedAt', title: 'updatedAt' },
          { id: 'personId', title: 'personId' },
          { id: 'createdById', title: 'createdById' },
          { id: 'deletedById', title: 'deletedById' },
          { id: 'codigoOriginal', title: 'codigoOriginal' }
        ]
      });

      await contactCsvWriter.writeRecords(contactsData);
      console.log('✓ CSV formatado para schema Contact salvo: pacientes_schema_contact.csv');
    }

    // 8. Gerar relatório de migração
    console.log('\nGerando relatório de migração...');
    const report = {
      dataProcessamento: new Date().toISOString(),
      pacientes: {
        total: allPatientDetails.length,
        ativos: allPatientDetails.filter(p => p.ativo).length,
        inativos: allPatientDetails.filter(p => !p.ativo).length,
        comEmail: allPatientDetails.filter(p => p.email).length,
        comConvenio: allPatientDetails.filter(p => p.convenioSaude).length,
        comContatos: allPatientDetails.filter(p => p.contatos && p.contatos.length > 0).length
      },
      clientes: {
        total: clientsData.length
      },
      pessoas: {
        total: personsData.length
      },
      contatos: {
        total: contactsData.length
      },
      conveniosSaude: [...new Set(allPatientDetails.map(p => p.convenioSaude).filter(Boolean))],
      observacoes: [
        'Passwords foram definidos com hash padrão - DEVEM SER ALTERADOS',
        'Emails foram gerados automaticamente quando não fornecidos',
        'CompanyId precisa ser mapeado manualmente',
        'CreatedById precisa ser mapeado para usuários existentes',
        'Relacionamento Client-Person foi estabelecido via clientId'
      ]
    };

    fs.writeFileSync('relatorio_migracao_pacientes.json', JSON.stringify(report, null, 2));
    console.log('✓ Relatório de migração salvo: relatorio_migracao_pacientes.json');

    console.log('\n🎉 Migração de pacientes concluída com sucesso!');
    console.log(`📁 Arquivos gerados:`);
    console.log(`   • pacientes_dados_brutos.csv`);
    console.log(`   • pacientes_schema_client.csv`);
    console.log(`   • pacientes_schema_person.csv`);
    if (contactsData.length > 0) {
      console.log(`   • pacientes_schema_contact.csv`);
    }
    console.log(`   • relatorio_migracao_pacientes.json`);
    
    console.log(`\n📊 Resumo:`);
    console.log(`   • Total de pacientes: ${report.pacientes.total}`);
    console.log(`   • Pacientes ativos: ${report.pacientes.ativos}`);
    console.log(`   • Pacientes inativos: ${report.pacientes.inativos}`);
    console.log(`   • Com email: ${report.pacientes.comEmail}`);
    console.log(`   • Com convênio: ${report.pacientes.comConvenio}`);
    console.log(`   • Com contatos: ${report.pacientes.comContatos}`);
    console.log(`   • Total de contatos: ${report.contatos.total}`);

  } catch (error) {
    console.error('❌ Erro durante a migração:', error.message);
    process.exit(1);
  }
}

// Executar o script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, fetchAllPatients, fetchPatientDetails, mapToClientSchema, mapToPersonSchema, mapToContactSchema };