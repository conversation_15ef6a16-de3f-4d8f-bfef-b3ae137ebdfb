// src/swagger/serviceTypeRoutes.js

/**
 * @swagger
 * tags:
 *   name: Tipos de Serviço
 *   description: Gerenciamento de tipos de serviço oferecidos
 */

/**
 * @swagger
 * /service-types:
 *   post:
 *     summary: Cria um novo tipo de serviço
 *     description: Cria um novo tipo de serviço no sistema.
 *     tags: [Tipos de Serviço]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - value
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome do tipo de serviço
 *                 example: "Consulta Clínica"
 *               value:
 *                 type: number
 *                 format: float
 *                 description: Valor do serviço
 *                 example: 150.00
 *     responses:
 *       201:
 *         description: Tipo de serviço criado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceType'
 *       400:
 *         description: Dados inválidos ou nome já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               validationError:
 *                 value:
 *                   errors: [
 *                     {
 *                       msg: "Nome é obrigatório",
 *                       param: "name",
 *                       location: "body"
 *                     }
 *                   ]
 *               duplicateError:
 *                 value:
 *                   message: "Nome do serviço já existe"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista tipos de serviço
 *     description: Retorna uma lista de tipos de serviço. Permite filtrar por busca.
 *     tags: [Tipos de Serviço]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Texto para busca pelo nome
 *     responses:
 *       200:
 *         description: Lista de tipos de serviço
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 serviceType:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ServiceType'
 *                 total:
 *                   type: integer
 *                   description: Total de registros
 *                   example: 8
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /service-types/{id}:
 *   get:
 *     summary: Obtém detalhes de um tipo de serviço
 *     description: Retorna os detalhes completos de um tipo de serviço específico.
 *     tags: [Tipos de Serviço]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do tipo de serviço
 *     responses:
 *       200:
 *         description: Detalhes do tipo de serviço
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceType'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Tipo de serviço não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Tipo de serviço não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   put:
 *     summary: Atualiza um tipo de serviço
 *     description: Atualiza os dados de um tipo de serviço existente.
 *     tags: [Tipos de Serviço]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do tipo de serviço
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome do tipo de serviço
 *               value:
 *                 type: number
 *                 format: float
 *                 description: Valor do serviço
 *     responses:
 *       200:
 *         description: Tipo de serviço atualizado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ServiceType'
 *       400:
 *         description: Dados inválidos ou nome já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Nome do serviço já existe"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Tipo de serviço não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Tipo de serviço não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove um tipo de serviço
 *     description: Remove um tipo de serviço do sistema.
 *     tags: [Tipos de Serviço]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do tipo de serviço
 *     responses:
 *       204:
 *         description: Tipo de serviço removido com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Tipo de serviço não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Tipo de serviço não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */