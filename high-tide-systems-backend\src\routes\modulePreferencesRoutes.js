const express = require('express');
const router = express.Router();
const { authenticate } = require('../middlewares/auth');
const { UserController, updateModulePreferencesValidation } = require('../controllers/userController');

// Rotas para gerenciamento de preferências de módulos
console.log('[modulePreferencesRoutes] Registrando rota GET /');
router.get('/', authenticate, (req, res, next) => {
  console.log('[modulePreferencesRoutes] Rota GET / chamada');
  UserController.getModulePreferences(req, res, next);
});

console.log('[modulePreferencesRoutes] Registrando rota PUT /');
router.put('/', authenticate, updateModulePreferencesValidation, (req, res, next) => {
  console.log('[modulePreferencesRoutes] Rota PUT / chamada');
  UserController.updateModulePreferences(req, res, next);
});

module.exports = router;
