// modules/auth/services/authService.js
import { api } from '@/utils/api';

export const authService = {
  async login(email, password) {
    const response = await api.post('/auth/login', { email, password });
    localStorage.setItem('token', response.data.token);
    return response.data;
  },

  async register(name, email, password) {
    const response = await api.post('/auth/register', {
      name,
      email,
      password
    });
    localStorage.setItem('token', response.data.token);
    return response.data;
  },

  logout() {
    localStorage.removeItem('token');
  }
};