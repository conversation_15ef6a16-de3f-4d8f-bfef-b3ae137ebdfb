const fs = require('fs');
const path = require('path');

// Caminho base para uploads
const UPLOAD_PATH = path.resolve("./uploads");
const PROFILE_IMAGES_PATH = path.join(UPLOAD_PATH, "profile-images");

console.log('Verificando diretório de uploads:', UPLOAD_PATH);
console.log('Verificando diretório de imagens de perfil:', PROFILE_IMAGES_PATH);

// Verificar se os diretórios existem
if (!fs.existsSync(UPLOAD_PATH)) {
  console.log('Diretório de uploads não existe, criando...');
  fs.mkdirSync(UPLOAD_PATH, { recursive: true });
  console.log('Diretório de uploads criado com sucesso');
} else {
  console.log('Diretório de uploads já existe');
}

if (!fs.existsSync(PROFILE_IMAGES_PATH)) {
  console.log('Diretório de imagens de perfil não existe, criando...');
  fs.mkdirSync(PROFILE_IMAGES_PATH, { recursive: true });
  console.log('Diretório de imagens de perfil criado com sucesso');
} else {
  console.log('Diretório de imagens de perfil já existe');
}

// Criar um arquivo de teste
const TEST_FILE_PATH = path.join(PROFILE_IMAGES_PATH, 'test-image.txt');
console.log('Criando arquivo de teste:', TEST_FILE_PATH);
fs.writeFileSync(TEST_FILE_PATH, 'Este é um arquivo de teste para verificar se o diretório de uploads está funcionando corretamente.');
console.log('Arquivo de teste criado com sucesso');

// Listar arquivos no diretório de imagens de perfil
console.log('Arquivos no diretório de imagens de perfil:');
const files = fs.readdirSync(PROFILE_IMAGES_PATH);
if (files.length === 0) {
  console.log('Nenhum arquivo encontrado');
} else {
  files.forEach(file => {
    console.log(`- ${file}`);
  });
}

console.log('Teste concluído com sucesso');
