// src/controllers/workingHoursController.js

const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");

const timeToMinutes = (time) => {
  const [hours, minutes] = time.split(":").map(Number);
  return hours * 60 + (minutes || 0);
};

const minutesToTime = (minutes) => {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${String(hours).padStart(2, "0")}:${String(mins).padStart(2, "0")}`;
};

const workingHoursValidation = [
  body("userId").notEmpty().withMessage("Profissional é obrigatório"),
  body("schedules").isArray().withMessage("Horários são obrigatórios"),
  body("schedules.*.dayOfWeek")
    .isInt({ min: 0, max: 6 })
    .withMessage("Dia da semana inválido"),
  body("schedules.*.startTime")
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Hora início inválida"),
  body("schedules.*.endTime")
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Hora fim inválida"),
  body("schedules.*.breakStart")
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Hora início intervalo inválida"),
  body("schedules.*.breakEnd")
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage("Hora fim intervalo inválida"),
];

class WorkingHoursController {
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { userId, schedules } = req.body;

      // Desativa todos os horários existentes
      await prisma.workingHours.updateMany({
        where: { userId },
        data: { isActive: false },
      });

      // Cria os novos horários - ATUALIZADO para usar as novas colunas em minutos
      const workingHours = await Promise.all(
        schedules.map((schedule) => {
          // Converte os horários de string para minutos
          const startTimeMinutes = timeToMinutes(schedule.startTime);
          const endTimeMinutes = timeToMinutes(schedule.endTime);

          // Opcionalmente converte os horários de intervalos
          let breakStartMinutes = null;
          let breakEndMinutes = null;

          if (schedule.breakStart) {
            breakStartMinutes = timeToMinutes(schedule.breakStart);
          }

          if (schedule.breakEnd) {
            breakEndMinutes = timeToMinutes(schedule.breakEnd);
          }

          return prisma.workingHours.create({
            data: {
              userId,
              dayOfWeek: schedule.dayOfWeek,
              startTimeMinutes,
              endTimeMinutes,
              breakStartMinutes,
              breakEndMinutes,
              isActive: true,
            },
          });
        })
      );

      res.status(201).json(workingHours);
    } catch (error) {
      console.error("Erro ao definir horários de trabalho:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async list(req, res) {
    try {
      const { userId } = req.params;

      const workingHours = await prisma.workingHours.findMany({
        where: {
          userId,
          isActive: true,
        },
        orderBy: {
          dayOfWeek: "asc",
        },
      });

      res.json(workingHours);
    } catch (error) {
      console.error("Erro ao buscar horários de trabalho:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async update(req, res) {
    try {
      const { id } = req.params;
      const { dayOfWeek, startTime, endTime, breakStart, breakEnd } = req.body;

      // Busca o horário atual
      const currentHours = await prisma.workingHours.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              schedulingsAsProvider: {
                where: {
                  startDate: { gt: new Date() },
                  status: "PENDING",
                },
              },
            },
          },
        },
      });

      if (!currentHours) {
        return res.status(404).json({ message: "Horário não encontrado" });
      }

      // Verifica se existem agendamentos pendentes
      if (currentHours.user.schedulingsAsProvider.length > 0) {
        return res.status(400).json({
          message: "Não é possível editar horário com agendamentos pendentes",
          schedulings: currentHours.user.schedulingsAsProvider,
        });
      }

      // Converte os horários de string para minutos
      const startTimeMinutes = timeToMinutes(startTime);
      const endTimeMinutes = timeToMinutes(endTime);

      // Opcionalmente converte os horários de intervalos
      let breakStartMinutes = null;
      let breakEndMinutes = null;

      if (breakStart) {
        breakStartMinutes = timeToMinutes(breakStart);
      }

      if (breakEnd) {
        breakEndMinutes = timeToMinutes(breakEnd);
      }

      const updatedHours = await prisma.workingHours.update({
        where: { id },
        data: {
          dayOfWeek,
          startTimeMinutes,
          endTimeMinutes,
          breakStartMinutes,
          breakEndMinutes,
        },
      });

      res.json(updatedHours);
    } catch (error) {
      console.error("Erro ao atualizar horário de trabalho:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Busca o horário atual
      const currentHours = await prisma.workingHours.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              schedulingsAsProvider: {
                where: {
                  startDate: { gt: new Date() },
                  status: "PENDING",
                },
              },
            },
          },
        },
      });

      if (!currentHours) {
        return res.status(404).json({ message: "Horário não encontrado" });
      }

      // Verifica se existem agendamentos pendentes
      if (currentHours.user.schedulingsAsProvider.length > 0) {
        return res.status(400).json({
          message:
            "Não é possível desativar horário com agendamentos pendentes",
          schedulings: currentHours.user.schedulingsAsProvider,
        });
      }

      await prisma.workingHours.update({
        where: { id },
        data: { isActive: false },
      });

      res.json({ message: "Horário desativado com sucesso" });
    } catch (error) {
      console.error("Erro ao desativar horário de trabalho:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async setDaySchedule(req, res) {
    try {
      const { userId, dayOfWeek } = req.params;
      const { timeSlots } = req.body;
      // timeSlots será um array de objetos: [{ start: "09:00", end: "10:00" }, ...]

      // Verifica agendamentos existentes
      const existingSchedulings = await prisma.scheduling.findMany({
        where: {
          userId,
          status: "PENDING",
          startDate: {
            gt: new Date(),
          },
        },
      });

      if (existingSchedulings.length > 0) {
        return res.status(400).json({
          message: "Não é possível alterar horários com agendamentos pendentes",
          schedulings: existingSchedulings,
        });
      }

      // Desativa todos os horários existentes do dia
      await prisma.workingHours.updateMany({
        where: {
          userId,
          dayOfWeek: parseInt(dayOfWeek),
        },
        data: {
          isActive: false,
        },
      });

      // Cria os novos horários
      const workingHours = await Promise.all(
        timeSlots.map((slot) =>
          prisma.workingHours.create({
            data: {
              userId,
              dayOfWeek: parseInt(dayOfWeek),
              startTime: slot.start,
              endTime: slot.end,
              isActive: true,
            },
          })
        )
      );

      res.json(workingHours);
    } catch (error) {
      console.error("Erro ao definir horários:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  // Método para buscar a grade de horários (24 slots)
  static async getDayGrid(req, res) {
    try {
      const { userId, dayOfWeek } = req.params;

      const workingHours = await prisma.workingHours.findMany({
        where: {
          userId,
          dayOfWeek: parseInt(dayOfWeek),
          isActive: true,
        },
        orderBy: {
          startTimeMinutes: "asc", // Atualizado para usar startTimeMinutes
        },
      });

      // Inicializa grade com 24 slots (um para cada hora)
      const timeGrid = new Array(24).fill(false);

      workingHours.forEach((period) => {
        // Convertemos os minutos em horas
        const startHour = Math.floor(period.startTimeMinutes / 60);
        const endHour = Math.floor(period.endTimeMinutes / 60);

        // Se termina em hora exata, não incluímos a última hora
        const adjustedEndHour =
          period.endTimeMinutes % 60 === 0 ? endHour : endHour + 1;

        // Lógica para intervalo/pausa
        let breakStartHour = null;
        let breakEndHour = null;

        if (period.breakStartMinutes && period.breakEndMinutes) {
          breakStartHour = Math.floor(period.breakStartMinutes / 60);
          breakEndHour = Math.floor(period.breakEndMinutes / 60);
          if (period.breakEndMinutes % 60 === 0 && breakEndHour > 0) {
            breakEndHour = breakEndHour;
          } else {
            breakEndHour = breakEndHour + 1;
          }
        }

        // Marca slots de trabalho
        for (let i = startHour; i < adjustedEndHour; i++) {
          // Se estiver no intervalo de almoço, não marca como disponível
          if (
            !breakStartHour ||
            !breakEndHour ||
            !(i >= breakStartHour && i < breakEndHour)
          ) {
            timeGrid[i] = true;
          }
        }
      });

      res.json({
        dayOfWeek: parseInt(dayOfWeek),
        timeGrid,
        workingHours, // Inclui os dados originais também
      });
    } catch (error) {
      console.error("Erro ao buscar grade de horários:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  // Novo método para obter a grade de horários de múltiplos usuários (para o quadro)
  static async getUsersTimeGrid(req, res) {
    try {
      const { dayOfWeek } = req.params;
      const { userIds } = req.body; // Array de IDs dos usuários ou recuperar todos ativos

      // Se userIds não for fornecido, buscar todos os usuários ativos
      let userQuery = {};
      if (userIds && userIds.length > 0) {
        userQuery = {
          where: {
            id: { in: userIds },
            active: true,
          },
        };
      } else {
        userQuery = {
          where: {
            active: true,
          },
        };
      }

      // Buscar todos os usuários
      const users = await prisma.user.findMany({
        ...userQuery,
        select: {
          id: true,
          fullName: true,
        },
        orderBy: {
          fullName: "asc",
        },
      });

      // Buscar todos os horários de trabalho para o dia especificado
      const allWorkingHours = await prisma.workingHours.findMany({
        where: {
          userId: { in: users.map((u) => u.id) },
          dayOfWeek: parseInt(dayOfWeek),
          isActive: true,
        },
      });

      // Agrupar horários por usuário
      const workingHoursByUser = {};
      allWorkingHours.forEach((wh) => {
        if (!workingHoursByUser[wh.userId]) {
          workingHoursByUser[wh.userId] = [];
        }
        workingHoursByUser[wh.userId].push(wh);
      });

      // Gerar grade para cada usuário
      const result = users.map((user) => {
        const userWorkingHours = workingHoursByUser[user.id] || [];

        // Inicializa grade com 24 slots (um para cada hora)
        const timeGrid = new Array(24).fill(false);

        userWorkingHours.forEach((period) => {
          // Converter minutos para horas
          const startHour = Math.floor(period.startTimeMinutes / 60);
          const endHour = Math.floor(period.endTimeMinutes / 60);

          // Ajuste para "não incluir" a última hora se terminar em horário exato
          const adjustedEndHour =
            period.endTimeMinutes % 60 === 0 ? endHour : endHour + 1;

          let breakStartHour = null;
          let breakEndHour = null;

          if (period.breakStartMinutes && period.breakEndMinutes) {
            breakStartHour = Math.floor(period.breakStartMinutes / 60);
            breakEndHour = Math.floor(period.breakEndMinutes / 60);

            // Ajuste para "não incluir" a última hora do intervalo se terminar em horário exato
            if (period.breakEndMinutes % 60 !== 0) {
              breakEndHour += 1;
            }
          }

          // Marca slots de trabalho
          for (let i = startHour; i < adjustedEndHour; i++) {
            if (
              !breakStartHour ||
              !breakEndHour ||
              !(i >= breakStartHour && i < breakEndHour)
            ) {
              timeGrid[i] = true;
            }
          }
        });

        return {
          userId: user.id,
          userName: user.fullName,
          timeGrid,
        };
      });

      res.json({
        dayOfWeek: parseInt(dayOfWeek),
        users: result,
      });
    } catch (error) {
      console.error("Erro ao buscar grade de horários por usuário:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  // Novo método para atualizar grade horária (granularidade de 1h)
  static async updateHourlyGrid(req, res) {
    try {
      const { userId, dayOfWeek } = req.params;
      const { timeGrid } = req.body; // Array de 24 booleanos

      if (!Array.isArray(timeGrid) || timeGrid.length !== 24) {
        return res.status(400).json({
          message: "A grade horária deve ser um array de 24 valores booleanos",
        });
      }

      // Verificar agendamentos existentes
      const today = new Date();
      const targetDate = new Date(today);

      // Ajusta a data para o próximo dia da semana especificado
      const currentDay = today.getDay();
      const daysToAdd = (parseInt(dayOfWeek) - currentDay + 7) % 7;
      targetDate.setDate(today.getDate() + daysToAdd);

      // Define a data de início como meia-noite e a data de fim como 23:59:59
      const startOfDay = new Date(targetDate.setHours(0, 0, 0, 0));
      const endOfDay = new Date(targetDate.setHours(23, 59, 59, 999));

      const existingSchedulings = await prisma.scheduling.findMany({
        where: {
          userId,
          status: "PENDING",
          startDate: {
            gte: startOfDay,
            lte: endOfDay,
          },
        },
      });

      if (existingSchedulings.length > 0) {
        // Verificar se algum agendamento cairia em horário que será removido
        let hasConflict = false;
        for (const scheduling of existingSchedulings) {
          const scheduleHour = scheduling.startDate.getHours();
          if (timeGrid[scheduleHour] === false) {
            hasConflict = true;
            break;
          }
        }

        if (hasConflict) {
          return res.status(400).json({
            message:
              "Não é possível alterar horários com agendamentos pendentes",
            schedulings: existingSchedulings,
          });
        }
      }

      // Desativa todos os horários existentes do dia
      await prisma.workingHours.updateMany({
        where: {
          userId,
          dayOfWeek: parseInt(dayOfWeek),
        },
        data: {
          isActive: false,
        },
      });

      // Convertemos a grade para intervalos continuos
      const intervals = [];
      let currentInterval = null;

      for (let hour = 0; hour < 24; hour++) {
        if (timeGrid[hour]) {
          // Se estamos em um horário disponível
          if (!currentInterval) {
            // Inicia um novo intervalo
            currentInterval = { start: hour, end: hour + 1 };
          } else {
            // Estende o intervalo atual
            currentInterval.end = hour + 1;
          }
        } else if (currentInterval) {
          // Finaliza o intervalo atual
          intervals.push(currentInterval);
          currentInterval = null;
        }
      }

      // Adiciona o último intervalo se existir
      if (currentInterval) {
        intervals.push(currentInterval);
      }

      // Cria os novos horários de trabalho a partir dos intervalos
      // ATUALIZADO: Usa startTimeMinutes e endTimeMinutes em vez de startTime e endTime
      const createdHours = await Promise.all(
        intervals.map((interval) =>
          prisma.workingHours.create({
            data: {
              userId,
              dayOfWeek: parseInt(dayOfWeek),
              startTimeMinutes: interval.start * 60, // Converte horas para minutos
              endTimeMinutes: interval.end * 60, // Converte horas para minutos
              isActive: true,
            },
          })
        )
      );

      res.json({
        success: true,
        intervals,
        workingHours: createdHours,
      });
    } catch (error) {
      console.error("Erro ao atualizar grade horária:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  // Método para atualizar múltiplos usuários de uma vez
  static async updateMultipleUsersGrid(req, res) {
    try {
      const { dayOfWeek } = req.params;
      const { usersGrid } = req.body;
      // usersGrid: [{ userId: "123", timeGrid: [true, false, ...] }, ...]

      if (!Array.isArray(usersGrid)) {
        return res.status(400).json({
          message: "O formato dos dados está incorreto",
        });
      }

      const results = [];
      const errors = [];

      // Processa cada usuário em sequência
      for (const userGrid of usersGrid) {
        try {
          const { userId, timeGrid } = userGrid;

          if (!userId || !Array.isArray(timeGrid) || timeGrid.length !== 24) {
            errors.push({
              userId,
              message: "Dados inválidos para este usuário",
            });
            continue;
          }

          // Reutiliza a lógica do updateHourlyGrid
          const today = new Date();
          const targetDate = new Date(today);
          const currentDay = today.getDay();
          const daysToAdd = (parseInt(dayOfWeek) - currentDay + 7) % 7;
          targetDate.setDate(today.getDate() + daysToAdd);

          const startOfDay = new Date(targetDate.setHours(0, 0, 0, 0));
          const endOfDay = new Date(targetDate.setHours(23, 59, 59, 999));

          // Verifica conflitos
          const existingSchedulings = await prisma.scheduling.findMany({
            where: {
              userId,
              status: "PENDING",
              startDate: {
                gte: startOfDay,
                lte: endOfDay,
              },
            },
          });

          let hasConflict = false;
          for (const scheduling of existingSchedulings) {
            const scheduleHour = scheduling.startDate.getHours();
            if (timeGrid[scheduleHour] === false) {
              hasConflict = true;
              break;
            }
          }

          if (hasConflict) {
            errors.push({
              userId,
              message:
                "Não é possível alterar horários com agendamentos pendentes",
              schedulings: existingSchedulings,
            });
            continue;
          }

          // Desativa horários existentes
          await prisma.workingHours.updateMany({
            where: {
              userId,
              dayOfWeek: parseInt(dayOfWeek),
            },
            data: {
              isActive: false,
            },
          });

          // Converte para intervalos
          const intervals = [];
          let currentInterval = null;

          for (let hour = 0; hour < 24; hour++) {
            if (timeGrid[hour]) {
              if (!currentInterval) {
                currentInterval = { start: hour, end: hour + 1 };
              } else {
                currentInterval.end = hour + 1;
              }
            } else if (currentInterval) {
              intervals.push(currentInterval);
              currentInterval = null;
            }
          }

          if (currentInterval) {
            intervals.push(currentInterval);
          }

          // Cria novos horários - ATUALIZADO PARA USAR MINUTOS
          const createdHours = await Promise.all(
            intervals.map((interval) =>
              prisma.workingHours.create({
                data: {
                  userId,
                  dayOfWeek: parseInt(dayOfWeek),
                  startTimeMinutes: interval.start * 60, // Converte horas para minutos
                  endTimeMinutes: interval.end * 60, // Converte horas para minutos
                  isActive: true,
                },
              })
            )
          );

          results.push({
            userId,
            success: true,
            intervals,
            workingHours: createdHours,
          });
        } catch (error) {
          console.error(`Erro ao processar usuário ${userGrid.userId}:`, error);
          errors.push({
            userId: userGrid.userId,
            message: "Erro ao processar este usuário",
            error: error.message,
          });
        }
      }

      res.json({
        results,
        errors,
        overallSuccess: errors.length === 0,
      });
    } catch (error) {
      console.error("Erro ao atualizar grades horárias:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  WorkingHoursController,
  workingHoursValidation,
};
