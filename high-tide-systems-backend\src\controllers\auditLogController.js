// src/controllers/auditLogController.js
const prisma = require('../utils/prisma');
const { validationResult } = require('express-validator');

class AuditLogController {
  /**
   * Lista logs de auditoria com paginação e filtros
   * Permite filtrar por usuário, ação, entidade, data e mais
   */
  static async list(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        page = 1,
        limit = 20,
        search,
        startDate,
        endDate,
        action,
        entityType,
        userId,
      } = req.query;

      // Construir a condição where com base nos filtros
      const where = {
        AND: [
          // Filtro por empresa do usuário (exceto para admin de sistema)
          req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId
            ? { companyId: req.user.companyId }
            : {},
          
          // Filtro de busca textual
          search
            ? {
                OR: [
                  { action: { contains: search, mode: 'insensitive' } },
                  { entityType: { contains: search, mode: 'insensitive' } },
                  { entityId: { contains: search } },
                ],
              }
            : {},
          
          // Filtro de data inicial
          startDate
            ? { createdAt: { gte: new Date(startDate) } }
            : {},
          
          // Filtro de data final
          endDate
            ? { createdAt: { lte: new Date(endDate + 'T23:59:59.999Z') } }
            : {},
          
          // Filtro de ação
          action
            ? { action }
            : {},
          
          // Filtro de tipo de entidade
          entityType
            ? { entityType }
            : {},
          
          // Filtro de usuário
          userId
            ? { userId }
            : {},
        ],
      };

      // Buscar logs paginados
      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          orderBy: { createdAt: 'desc' },
          skip: (Number(page) - 1) * Number(limit),
          take: Number(limit),
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
              },
            },
          },
        }),
        prisma.auditLog.count({ where }),
      ]);

      // Formatar logs para a resposta
      const formattedLogs = logs.map(log => ({
        id: log.id,
        action: log.action,
        entityType: log.entityType,
        entityId: log.entityId,
        details: log.details,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        createdAt: log.createdAt,
        userId: log.userId,
        userName: log.user?.fullName || 'Sistema',
      }));

      res.json({
        logs: formattedLogs,
        total,
        pages: Math.ceil(total / Number(limit)),
      });
    } catch (error) {
      console.error('Erro ao listar logs de auditoria:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Exporta logs de auditoria para CSV
   * Permite os mesmos filtros que o método list
   */
  static async export(req, res) {
    try {
      const {
        startDate,
        endDate,
        action,
        entityType,
        userId,
      } = req.query;

      // Construir a condição where com base nos filtros
      const where = {
        AND: [
          // Filtro por empresa do usuário (exceto para admin de sistema)
          req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId
            ? { companyId: req.user.companyId }
            : {},
          
          // Filtro de data inicial
          startDate
            ? { createdAt: { gte: new Date(startDate) } }
            : {},
          
          // Filtro de data final
          endDate
            ? { createdAt: { lte: new Date(endDate + 'T23:59:59.999Z') } }
            : {},
          
          // Filtro de ação
          action
            ? { action }
            : {},
          
          // Filtro de tipo de entidade
          entityType
            ? { entityType }
            : {},
          
          // Filtro de usuário
          userId
            ? { userId }
            : {},
        ],
      };

      // Buscar todos os logs que correspondem aos filtros (sem paginação)
      const logs = await prisma.auditLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        include: {
          user: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
        },
      });

      // Transformar logs em CSV
      const csvHeader = 'ID,Ação,Tipo de Entidade,ID da Entidade,Usuário,Endereço IP,Data,Detalhes\n';
      
      const csvRows = logs.map(log => {
        // Formatação segura para CSV (evitar quebras no formato)
        const formatCSVField = (value) => {
          if (value === null || value === undefined) return '';
          return `"${String(value).replace(/"/g, '""')}"`;
        };

        const formattedDate = new Date(log.createdAt).toLocaleString('pt-BR');
        const userName = log.user?.fullName || 'Sistema';
        const details = log.details ? JSON.stringify(log.details) : '';

        return [
          formatCSVField(log.id),
          formatCSVField(log.action),
          formatCSVField(log.entityType),
          formatCSVField(log.entityId),
          formatCSVField(userName),
          formatCSVField(log.ipAddress),
          formatCSVField(formattedDate),
          formatCSVField(details),
        ].join(',');
      }).join('\n');

      const csv = csvHeader + csvRows;

      // Configurar headers para download
      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename=logs_auditoria.csv');
      
      // Enviar CSV
      res.send(csv);
    } catch (error) {
      console.error('Erro ao exportar logs de auditoria:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Recupera detalhes de um log específico
   */
  static async getDetails(req, res) {
    try {
      const { id } = req.params;

      const log = await prisma.auditLog.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              fullName: true,
              email: true,
            },
          },
        },
      });

      if (!log) {
        return res.status(404).json({ message: 'Log não encontrado' });
      }

      // Verificar se o usuário tem acesso ao log (mesma empresa ou admin)
      if (
        req.user.role !== 'SYSTEM_ADMIN' &&
        req.user.companyId &&
        log.companyId !== req.user.companyId
      ) {
        return res.status(403).json({ message: 'Acesso negado' });
      }

      res.json({
        id: log.id,
        action: log.action,
        entityType: log.entityType,
        entityId: log.entityId,
        details: log.details,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        createdAt: log.createdAt,
        userId: log.userId,
        userName: log.user?.fullName || 'Sistema',
      });
    } catch (error) {
      console.error('Erro ao buscar detalhes do log:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
  
  /**
   * Obtém estatísticas dos logs de auditoria
   * Retorna contagens por tipo de ação, por entidade, e tendências ao longo do tempo
   */
  static async getStats(req, res) {
    try {
      const { days = 30 } = req.query;
      
      // Data de início para filtro (X dias atrás)
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - Number(days));
      
      // Filtro de empresa
      const companyFilter = req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId
        ? { companyId: req.user.companyId }
        : {};
      
      // Contagem por tipo de ação
      const actionCounts = await prisma.$queryRaw`
        SELECT 
          "action", 
          COUNT(*) as count 
        FROM 
          "AuditLog" 
        WHERE 
          "createdAt" >= ${startDate}
          ${req.user.companyId ? ` AND "companyId" = ${req.user.companyId}` : ''}
        GROUP BY 
          "action" 
        ORDER BY 
          count DESC
      `;
      
      // Contagem por tipo de entidade
      const entityCounts = await prisma.$queryRaw`
        SELECT 
          "entityType", 
          COUNT(*) as count 
        FROM 
          "AuditLog" 
        WHERE 
          "createdAt" >= ${startDate}
          ${req.user.companyId ? ` AND "companyId" = ${req.user.companyId}` : ''}
        GROUP BY 
          "entityType" 
        ORDER BY 
          count DESC
      `;
      
      // Tendência diária (logs por dia)
      const dailyTrend = await prisma.$queryRaw`
        SELECT 
          DATE_TRUNC('day', "createdAt") as date,
          COUNT(*) as count
        FROM 
          "AuditLog"
        WHERE 
          "createdAt" >= ${startDate}
          ${req.user.companyId ? ` AND "companyId" = ${req.user.companyId}` : ''}
        GROUP BY 
          DATE_TRUNC('day', "createdAt")
        ORDER BY 
          date ASC
      `;
      
      // Usuários mais ativos
      const activeUsers = await prisma.$queryRaw`
        SELECT 
          u."id" as "userId",
          u."fullName" as "userName",
          COUNT(a.id) as count
        FROM 
          "AuditLog" a
        JOIN 
          "User" u ON a."userId" = u."id"
        WHERE 
          a."createdAt" >= ${startDate}
          ${req.user.companyId ? ` AND a."companyId" = ${req.user.companyId}` : ''}
        GROUP BY 
          u."id", u."fullName"
        ORDER BY 
          count DESC
        LIMIT 10
      `;
      
      // Formatar as tendências diárias
      const formattedDailyTrend = dailyTrend.map(item => ({
        date: item.date.toISOString().split('T')[0],
        count: Number(item.count)
      }));
      
      res.json({
        actionCounts: actionCounts.map(item => ({
          action: item.action,
          count: Number(item.count)
        })),
        entityCounts: entityCounts.map(item => ({
          entityType: item.entityType,
          count: Number(item.count)
        })),
        dailyTrend: formattedDailyTrend,
        activeUsers: activeUsers.map(user => ({
          userId: user.userId,
          userName: user.userName,
          count: Number(user.count)
        }))
      });
    } catch (error) {
      console.error('Erro ao obter estatísticas de auditoria:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
  
  /**
   * Limpa logs antigos do sistema
   * Disponível apenas para administradores
   */
  static async cleanupOldLogs(req, res) {
    try {
      // Verificar se é administrador do sistema
      if (req.user.role !== 'SYSTEM_ADMIN') {
        return res.status(403).json({ message: 'Apenas administradores podem limpar logs' });
      }
      
      const { olderThan = 365 } = req.body; // Padrão: remover logs com mais de 1 ano
      
      if (olderThan < 30) {
        return res.status(400).json({ 
          message: 'Por razões de segurança, só é possível remover logs com mais de 30 dias' 
        });
      }
      
      // Calcular data limite
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThan);
      
      // Contar quantos logs serão removidos
      const countToDelete = await prisma.auditLog.count({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      });
      
      // Remover logs antigos
      await prisma.auditLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        }
      });
      
      // Registrar a limpeza como um novo log de auditoria
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'CLEANUP_LOGS',
          entityType: 'AuditLog',
          entityId: 'batch-delete',
          details: { 
            olderThan,
            cutoffDate,
            logsRemoved: countToDelete
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent']
        }
      });
      
      res.json({
        message: `Foram removidos ${countToDelete} logs com data anterior a ${cutoffDate.toISOString().split('T')[0]}`
      });
    } catch (error) {
      console.error('Erro ao limpar logs antigos:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = AuditLogController;