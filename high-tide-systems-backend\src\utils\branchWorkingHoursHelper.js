const prisma = require('./prisma');
const WorkingHoursHelper = require('./workingHoursHelper');

class BranchWorkingHoursHelper {
  /**
   * Generates default working hours for a branch
   * Default is Monday to Friday, 9:00-12:00 and 13:00-18:00
   * @returns {Object} Default working hours configuration
   */
  static generateDefaultWorkingHours() {
    // Days of the week (1 = Monday, 5 = Friday)
    const workDays = [1, 2, 3, 4, 5];
    
    // Generate default working hours for each work day
    const workingHours = {};
    
    workDays.forEach(dayOfWeek => {
      workingHours[dayOfWeek] = [
        {
          startTimeMinutes: this.timeToMinutes('09:00'),
          endTimeMinutes: this.timeToMinutes('12:00'),
        },
        {
          startTimeMinutes: this.timeToMinutes('13:00'),
          endTimeMinutes: this.timeToMinutes('18:00'),
        }
      ];
    });
    
    return workingHours;
  }

  /**
   * Helper function to convert time strings to minutes
   * @param {string} timeStr - Time string in format "HH:MM"
   * @returns {number} Time in minutes
   */
  static timeToMinutes(timeStr) {
    if (!timeStr) return null;
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Helper function to convert minutes to time string
   * @param {number} minutes - Time in minutes
   * @returns {string} Time string in format "HH:MM"
   */
  static minutesToTime(minutes) {
    if (minutes === null || minutes === undefined) return null;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
  }

  /**
   * Apply branch working hours to a user
   * @param {string} userId - User ID
   * @param {string} branchId - Branch ID
   * @returns {Promise<Array>} Array of created working hours
   */
  static async applyBranchWorkingHoursToUser(userId, branchId) {
    try {
      // Get branch working hours
      const branch = await prisma.branch.findUnique({
        where: { id: branchId }
      });

      if (!branch || !branch.defaultWorkingHours) {
        console.log(`Branch ${branchId} not found or has no default working hours`);
        return [];
      }

      // Deactivate existing working hours for the user
      await prisma.workingHours.updateMany({
        where: { userId },
        data: { isActive: false }
      });

      // Create new working hours based on branch defaults
      const workingHours = [];
      const defaultWorkingHours = branch.defaultWorkingHours;

      for (const dayOfWeek in defaultWorkingHours) {
        const daySchedules = defaultWorkingHours[dayOfWeek];
        
        for (const schedule of daySchedules) {
          const workingHour = await prisma.workingHours.create({
            data: {
              userId,
              dayOfWeek: parseInt(dayOfWeek),
              startTimeMinutes: schedule.startTimeMinutes,
              endTimeMinutes: schedule.endTimeMinutes,
              breakStartMinutes: schedule.breakStartMinutes || null,
              breakEndMinutes: schedule.breakEndMinutes || null,
              isActive: true
            }
          });
          
          workingHours.push(workingHour);
        }
      }

      return workingHours;
    } catch (error) {
      console.error('Error applying branch working hours to user:', error);
      throw error;
    }
  }

  /**
   * Apply branch working hours to all users in the branch
   * @param {string} branchId - Branch ID
   * @returns {Promise<Object>} Result of the operation
   */
  static async applyBranchWorkingHoursToAllUsers(branchId) {
    try {
      // Get all active users in the branch
      const users = await prisma.user.findMany({
        where: {
          branchId,
          active: true
        }
      });

      if (!users.length) {
        return { success: true, message: 'No users found in this branch', usersUpdated: 0 };
      }

      // Apply working hours to each user
      const results = [];
      for (const user of users) {
        const workingHours = await this.applyBranchWorkingHoursToUser(user.id, branchId);
        results.push({
          userId: user.id,
          workingHoursCount: workingHours.length
        });
      }

      return {
        success: true,
        message: `Working hours applied to ${users.length} users`,
        usersUpdated: users.length,
        results
      };
    } catch (error) {
      console.error('Error applying branch working hours to all users:', error);
      throw error;
    }
  }
}

module.exports = BranchWorkingHoursHelper;
