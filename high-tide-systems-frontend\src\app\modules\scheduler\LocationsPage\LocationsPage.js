"use client";

import React, { useState, useEffect } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleSelect, ModuleTable } from "@/components/ui";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Power,
  MapPin,
  Phone,
  Building,
  CheckCircle,
  XCircle
} from "lucide-react";
import { locationService } from "@/app/modules/scheduler/services/locationService";
import { branchService } from "@/app/modules/admin/services/branchService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import LocationFormModal from "@/components/people/LocationFormModal";
import ExportMenu from "@/components/ui/ExportMenu";
import { useToast } from "@/contexts/ToastContext";
import MultiSelect from "@/components/ui/multi-select";

// Tutorial steps para a página de localizações
const locationsTutorialSteps = [
  {
    title: "Localizações",
    content: "Esta tela permite gerenciar as localizações disponíveis para agendamentos no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Nova Localização",
    content: "Clique aqui para adicionar uma nova localização.",
    selector: "button:has(span:contains('Nova Localização'))",
    position: "left"
  },
  {
    title: "Filtrar Localizações",
    content: "Use esta barra de pesquisa para encontrar localizações específicas pelo nome ou endereço.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtrar por Status",
    content: "Filtre as localizações por status (ativas ou inativas).",
    selector: "select:first-of-type",
    position: "bottom"
  },
  {
    title: "Filtrar por Unidade",
    content: "Filtre as localizações por unidade.",
    selector: "select:nth-of-type(2)",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de localizações em diferentes formatos usando este botão.",
    selector: "button:has(span:contains('Exportar'))",
    position: "left"
  },
  {
    title: "Gerenciar Localizações",
    content: "Edite, ative/desative ou exclua localizações existentes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const LocationsPage = () => {
  const { user } = useAuth();
  const [locations, setLocations] = useState([]);
  const [locationOptions, setLocationOptions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);
  const [totalLocations, setTotalLocations] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [selectedLocationIds, setSelectedLocationIds] = useState([]);
  const [statusFilter, setStatusFilter] = useState("");
  const [branchFilter, setBranchFilter] = useState("");
  const [branches, setBranches] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [locationFormOpen, setLocationFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const { toast_error } = useToast()

  // Constants
  const ITEMS_PER_PAGE = 10;
  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Função para carregar unidades (branches)
  const loadBranches = async () => {
    try {
      const response = await branchService.getBranches({
        companyId: user?.companyId,
        active: true,
        limit: 100
      });

      setBranches(response.branches || []);
    } catch (error) {
      console.error("Erro ao carregar unidades:", error);
      setBranches([]);
    }
  };

  // Função para carregar empresas (companies)
  const loadCompanies = async () => {
    try {
      if (!isSystemAdmin) return;

      const response = await companyService.getCompanies({
        active: true,
        limit: 100
      });

      setCompanies(response.companies || []);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
      setCompanies([]);
    }
  };

  const loadLocations = async (
    page = currentPage,
    searchQuery = search,
    status = statusFilter,
    branch = branchFilter,
    locationIds = selectedLocationIds
  ) => {
    setIsLoading(true);
    try {
      const params = {
        page,
        limit: ITEMS_PER_PAGE,
        search: searchQuery || undefined,
        active: status === "" ? undefined : status === "active",
        branchId: branch || undefined,
        companyId: !isSystemAdmin ? user?.companyId : undefined,
        locationIds: locationIds.length > 0 ? locationIds : undefined
      };

      const response = await locationService.getLocations(params);

      setLocations(response.locations || []);
      setTotalLocations(response.total || 0);
      setTotalPages(response.pages || 1);
      setCurrentPage(page);
    } catch (error) {
      console.error("Erro ao carregar localizações:", error);
      setLocations([]);
      setTotalLocations(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  const loadLocationOptions = async () => {
    setIsLoadingOptions(true);
    try {
      const params = {
        limit: 100, // Carregar mais opções para o filtro
        companyId: !isSystemAdmin ? user?.companyId : undefined
      };

      const response = await locationService.getLocations(params);
      const options = (response.locations || []).map(location => ({
        value: location.id,
        label: location.name
      }));
      setLocationOptions(options);
    } catch (error) {
      console.error("Erro ao carregar opções de localizações:", error);
      setLocationOptions([]);
    } finally {
      setIsLoadingOptions(false);
    }
  };

  useEffect(() => {
    loadLocations();
    loadLocationOptions();
    loadBranches();
    loadCompanies();
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    loadLocations(1, search, statusFilter, branchFilter, selectedLocationIds);
  };

  const handleLocationFilterChange = (selected) => {
    setSelectedLocationIds(selected);
    loadLocations(1, search, statusFilter, branchFilter, selected);
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    loadLocations(1, search, value, branchFilter, selectedLocationIds);
  };

  const handleBranchFilterChange = (value) => {
    setBranchFilter(value);
    loadLocations(1, search, statusFilter, value, selectedLocationIds);
  };

  const handlePageChange = (page) => {
    loadLocations(page, search, statusFilter, branchFilter, selectedLocationIds);
  };

  const handleResetFilters = () => {
    setSearch("");
    setStatusFilter("");
    setBranchFilter("");
    setSelectedLocationIds([]);
    loadLocations(1, "", "", "", []);
  };

  const handleEditLocation = (location) => {
    setSelectedLocation(location);
    setLocationFormOpen(true);
  };

  const handleToggleStatus = (location) => {
    setSelectedLocation(location);
    setActionToConfirm({
      type: "toggle-status",
      message: `${location.active ? "Desativar" : "Ativar"} a localização ${location.name
        }?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteLocation = (location) => {
    setSelectedLocation(location);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente a localização ${location.name}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Exportar usando os mesmos filtros da tabela atual
      await locationService.exportLocations({
        search: search || undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        branchId: branchFilter || undefined,
        companyId: !isSystemAdmin ? user?.companyId : undefined,
        locationIds: selectedLocationIds.length > 0 ? selectedLocationIds : undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar localizações:", error);
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await locationService.toggleLocationStatus(selectedLocation.id);
        loadLocations();
      } catch (error) {
        console.error("Erro ao alterar status da localização:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await locationService.deleteLocation(selectedLocation.id);
        loadLocations();
      } catch (error) {
        console.error("Erro ao excluir localização:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  return (
    <div className="space-y-6">
      {/* Título e botões de ação */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <MapPin size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Localizações
        </h1>

        <div className="flex items-center gap-2">
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || locations.length === 0}
            className="text-purple-600 dark:text-purple-400"
          />

          <button
            onClick={() => {
              setSelectedLocation(null);
              setLocationFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 shadow-md transition-all"
          >
            <Plus size={18} />
            <span className="font-medium">Nova Localização</span>
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description="Gerencie as localizações disponíveis para agendamentos no sistema."
        tutorialSteps={locationsTutorialSteps}
        tutorialName="locations-overview"
        moduleColor="scheduler"
        filters={
          <form
            onSubmit={handleSearch}
            className="flex flex-col gap-4"
          >
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Buscar por nome ou endereço..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <div className="w-full sm:w-40">
                  <ModuleSelect
                    moduleColor="scheduler"
                    value={statusFilter}
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                    placeholder="Status"
                  >
                    <option value="">Todos os status</option>
                    <option value="active">Ativos</option>
                    <option value="inactive">Inativos</option>
                  </ModuleSelect>
                </div>

                <div className="w-full sm:w-48">
                  <ModuleSelect
                    moduleColor="scheduler"
                    value={branchFilter}
                    onChange={(e) => handleBranchFilterChange(e.target.value)}
                    placeholder="Unidades"
                  >
                    <option value="">Todas as unidades</option>
                    {branches.map((branch) => (
                      <option key={branch.id} value={branch.id}>
                        {branch.name}
                      </option>
                    ))}
                  </ModuleSelect>
                </div>

                <FilterButton type="submit" moduleColor="scheduler" variant="primary">
                  <Filter size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Filtrar</span>
                </FilterButton>

                <FilterButton
                  type="button"
                  onClick={handleResetFilters}
                  moduleColor="scheduler"
                  variant="secondary"
                >
                  <RefreshCw size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Limpar</span>
                </FilterButton>
              </div>
            </div>

            {/* Multi-select para filtrar por múltiplos locais */}
            <div className="w-full">
              <MultiSelect
                label="Filtrar por Nome do Local"
                value={selectedLocationIds}
                onChange={handleLocationFilterChange}
                options={locationOptions}
                placeholder="Selecione um ou mais locais..."
                loading={isLoadingOptions}
                moduleOverride="scheduler"
              />
            </div>
          </form>
        }
      />

      {/* Tabela de Localizações */}
      <ModuleTable
        moduleColor="scheduler"
        columns={[
          { header: 'Nome', field: 'name', width: '25%' },
          { header: 'Endereço', field: 'address', width: '30%' },
          { header: 'Unidade', field: 'branch', width: '20%' },
          ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '15%' }] : []),
          { header: 'Status', field: 'active', width: '10%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={locations}
        isLoading={isLoading}
        emptyMessage="Nenhuma localização encontrada"
        emptyIcon={<MapPin size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalLocations}
        onPageChange={handlePageChange}
        showPagination={totalPages > 1}
        tableId="scheduler-locations-table"
        enableColumnToggle={true}
        defaultSortField="name"
        defaultSortDirection="asc"
        renderRow={(location, index, moduleColors, visibleColumns) => (
          <tr key={location.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('name') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-primary-100 dark:bg-primary-900/30 flex items-center justify-center text-primary-600 dark:text-primary-400">
                    <MapPin size={18} />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                      {location.name}
                    </div>
                    {location.phone && (
                      <div className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center">
                        <Phone size={12} className="mr-1" />
                        {location.phone}
                      </div>
                    )}
                  </div>
                </div>
              </td>
            )}
            {visibleColumns.includes('address') && (
              <td className="px-6 py-4">
                <div className="text-sm text-neutral-600 dark:text-neutral-300">
                  <div className="flex items-center">
                    <MapPin size={14} className="text-neutral-400 dark:text-neutral-500 mr-1" />
                    {location.address}
                  </div>
                </div>
              </td>
            )}
            {visibleColumns.includes('branch') && (
              <td className="px-6 py-4 whitespace-nowrap">
                {location.branch ? (
                  <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                    {location.branch.name}
                    {location.branch.code && (
                      <span className="text-xs ml-1 text-neutral-500 dark:text-neutral-400">
                        ({location.branch.code})
                      </span>
                    )}
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">-</span>
                )}
              </td>
            )}
            {isSystemAdmin && visibleColumns.includes('company') && (
              <td className="px-6 py-4 whitespace-nowrap">
                {location.company ? (
                  <div className="flex items-center">
                    <Building className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1" />
                    <span className="text-sm text-neutral-600 dark:text-neutral-300">
                      {location.company.name}
                    </span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">-</span>
                )}
              </td>
            )}
            {visibleColumns.includes('active') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${location.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    }`}
                >
                  {location.active ? (
                    <>
                      <CheckCircle size={12} />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}
            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                <button
                  onClick={() => handleEditLocation(location)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                  title="Editar"
                >
                  <Edit size={18} />
                </button>

                <button
                  onClick={() => handleToggleStatus(location)}
                  className={`p-1 transition-colors ${location.active
                      ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                      : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                    }`}
                  title={location.active ? "Desativar" : "Ativar"}
                >
                  <Power size={18} />
                </button>

                <button
                  onClick={() => handleDeleteLocation(location)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title="Excluir"
                >
                  <Trash size={18} />
                </button>
              </div>
            </td>
            )}
          </tr>
        )}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
        variant={actionToConfirm?.type === "delete" ? "danger" : "warning"}
      />

      {/* Location Form Modal */}
      {locationFormOpen && (
        <LocationFormModal
          isOpen={locationFormOpen}
          onClose={() => setLocationFormOpen(false)}
          location={selectedLocation}
          onSuccess={() => {
            setLocationFormOpen(false);
            loadLocations();
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default LocationsPage;