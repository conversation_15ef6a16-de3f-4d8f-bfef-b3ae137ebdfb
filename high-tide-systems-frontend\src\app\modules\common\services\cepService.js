// src/app/modules/common/services/cepService.js
import { api } from "@/utils/api";

export const cepService = {
  /**
   * Busca informações de endereço a partir de um CEP
   * @param {string} cep - CEP a ser consultado (pode conter máscara)
   * @returns {Promise<Object>} - Dados do endereço
   */
  searchByCep: async (cep) => {
    try {
      // Remove caracteres não numéricos
      const cleanCep = cep.replace(/\D/g, '');

      // Valida o CEP (deve ter 8 dígitos)
      if (cleanCep.length !== 8) {
        throw new Error('CEP inválido. O CEP deve conter 8 dígitos.');
      }

      const response = await api.get(`/cep/${cleanCep}`);
      console.log('Resposta da API de CEP:', response.data);
      return response.data;
    } catch (error) {
      console.error('Erro ao buscar CEP:', error);

      // Formata a mensagem de erro
      const errorMessage = error.response?.data?.message ||
                          error.message ||
                          'Erro ao buscar CEP';

      throw new Error(errorMessage);
    }
  }
};
