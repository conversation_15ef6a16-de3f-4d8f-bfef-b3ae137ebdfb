const axios = require('axios');
const fs = require('fs');
const createCsvWriter = require('csv-writer').createObjectCsvWriter;
const { v4: uuidv4 } = require('uuid');

// CONFIGURAÇÕES - PREENCHA COM SEU TOKEN
const BEARER_TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.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.TfyFsLA5M9m6E6NzkjygvVI8HSGr5SJKGbtNK9IEo3GMg7rsqs2OiAs6qni9m8vc6mjgZB8OAdH0_1ScDWl9Bpq5LSX1JB-plHAimz73ajVzqSWx6Tf5qMiZkDeTjwKdEAr_t-HoAmYrpHCwe1bjiGM6Gx9HjVFE4Bg54GlJgIlqUNS9JK_BawYMPbL1fLc-QLoHhRjBXeAqOyrGwiJPUAJnRiZaS6zIVocrXjBJjyZybS9QFWi1tlw58eeRhMjDNcp5hJyH649KxqO1KK4zvp8afbxUjuzNEvetXspsFbcfr1kdL9KybU3lvRnRAmv7N1C_VsepxXn8XdD4rKUFlg'; // Substitua pelo token completo
const BASE_URL = 'https://query-api-prd.abamais.com/v1';
const DELAY_BETWEEN_REQUESTS = 100; // Delay em ms entre requisições

// Headers padrão para as requisições
const headers = {
  'Accept': 'application/json, text/plain, */*',
  'Accept-Language': 'pt-BR',
  'Authorization': `Bearer ${BEARER_TOKEN}`,
  'Connection': 'keep-alive',
  'Origin': 'https://app.abamais.com',
  'Referer': 'https://app.abamais.com/',
  'Sec-Fetch-Dest': 'empty',
  'Sec-Fetch-Mode': 'cors',
  'Sec-Fetch-Site': 'same-site',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36 OPR/118.0.0.0',
  'sec-ch-ua': '"Not(A:Brand";v="99", "Opera GX";v="118", "Chromium";v="133"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"'
};

// Função para delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Função para buscar todos os funcionários (resumido)
async function fetchAllEmployees() {
  console.log('Buscando lista de funcionários...');
  
  try {
    const response = await axios.get(`${BASE_URL}/funcionarios`, {
      headers,
      params: {
        ativo: 'true',
        itensPorPagina: '10000',
        v: Date.now()
      }
    });

    if (response.data.code === 'OK' && response.data.status === 'SUCCESS') {
      console.log(`Encontrados ${response.data.data.totalResultados} funcionários`);
      return response.data.data.resultados;
    } else {
      throw new Error('Resposta inválida da API');
    }
  } catch (error) {
    console.error('Erro ao buscar funcionários:', error.message);
    throw error;
  }
}

// Função para buscar detalhes de um funcionário específico
async function fetchEmployeeDetails(codigo) {
  try {
    const response = await axios.get(`${BASE_URL}/funcionarios/${codigo}`, {
      headers
    });

    if (response.data.code === 'OK' && response.data.status === 'SUCCESS') {
      return response.data.data;
    } else {
      console.warn(`Erro ao buscar detalhes do funcionário ${codigo}`);
      return null;
    }
  } catch (error) {
    console.error(`Erro ao buscar detalhes do funcionário ${codigo}:`, error.message);
    return null;
  }
}

// Função para buscar todos os cargos/profissões
async function fetchAllProfessions() {
  console.log('Buscando lista de cargos/profissões...');
  
  try {
    const response = await axios.get(`${BASE_URL}/funcionarios/cargos`, {
      headers
    });

    if (response.data.code === 'OK' && response.data.status === 'SUCCESS') {
      console.log(`Encontradas ${response.data.data.length} profissões`);
      return response.data.data;
    } else {
      throw new Error('Resposta inválida da API para cargos');
    }
  } catch (error) {
    console.error('Erro ao buscar cargos:', error.message);
    throw error;
  }
}

// Função para mapear dados da API para o schema User
function mapToUserSchema(employeeData) {
  const now = new Date().toISOString();
  
  return {
    id: uuidv4(),
    email: employeeData.email || `${employeeData.codigo}@empresa.com`, // Email padrão se não fornecido
    password: '$2b$10$defaultHashedPassword', // Hash padrão - deve ser alterado
    active: employeeData.ativo || true,
    createdAt: now,
    updatedAt: now,
    address: employeeData.endereco || null,
    birthDate: employeeData.dataNascimento ? new Date(employeeData.dataNascimento).toISOString() : null,
    cnpj: null, // Funcionários geralmente não têm CNPJ
    cpf: employeeData.cpf || null,
    createdById: null, // Seria necessário mapear quem criou
    fullName: employeeData.nome || '',
    login: employeeData.login || employeeData.codigo?.toString() || '',
    modules: ['BASIC'], // Módulo padrão
    permissions: [], // Permissões vazias inicialmente
    phone: employeeData.celular || employeeData.telefone || null,
    companyId: null, // Seria necessário definir a empresa
    deletedAt: employeeData.ativo === false ? now : null,
    deletedById: null,
    failedLoginAttempts: 0,
    lastLoginAt: null,
    lastLoginIp: null,
    passwordChangedAt: now,
    role: employeeData.encarregado ? 'COMPANY_ADMIN' : 'EMPLOYEE',
    professionId: null, // Seria necessário mapear o cargo para profissão
    profileImageUrl: employeeData.foto ? `https://app.abamais.com/photos/${employeeData.foto}` : null,
    city: employeeData.cidade || null,
    postalCode: employeeData.cep || null,
    state: employeeData.estado || null,
    branchId: null, // Seria necessário mapear a filial
    neighborhood: employeeData.bairro || null,
    modulePreferences: null,
    // Campos extras da API original
    codigoOriginal: employeeData.codigo,
    cargoDescricao: employeeData.cargo?.descricao || null,
    encarregado: employeeData.encarregado || false
  };
}

// Função para mapear dados da API para o schema Profession
function mapToProfessionSchema(professionData) {
  const now = new Date().toISOString();
  
  return {
    id: uuidv4(),
    name: professionData.descricao || '',
    description: professionData.descricao || null, // Usando a mesma descrição
    active: true,
    groupId: null, // Seria necessário mapear grupos de profissão
    companyId: null, // Seria necessário definir a empresa
    createdAt: now,
    updatedAt: now,
    deletedAt: null,
    // Campos extras da API original
    codigoOriginal: professionData.codigo
  };
}

// Função principal
async function main() {
  try {
    console.log('Iniciando migração de funcionários e profissões...\n');

    // 1. Buscar lista de funcionários
    const employees = await fetchAllEmployees();
    const employeeCodes = employees.map(emp => emp.codigo);
    
    // 2. Buscar lista de profissões/cargos
    const professions = await fetchAllProfessions();
    
    console.log(`\nBuscando detalhes para ${employeeCodes.length} funcionários...\n`);

    // 3. Buscar detalhes de cada funcionário
    const allEmployeeDetails = [];
    let processed = 0;

    for (const codigo of employeeCodes) {
      console.log(`Processando funcionário ${codigo} (${++processed}/${employeeCodes.length})`);
      
      const details = await fetchEmployeeDetails(codigo);
      if (details) {
        allEmployeeDetails.push(details);
      }
      
      // Delay para não sobrecarregar a API
      await delay(DELAY_BETWEEN_REQUESTS);
    }

    console.log(`\nColetados detalhes de ${allEmployeeDetails.length} funcionários`);

    // 4. Gerar CSV com dados brutos dos funcionários
    console.log('\nGerando CSV com dados brutos dos funcionários...');
    
    if (allEmployeeDetails.length > 0) {
      // Coletar todas as chaves únicas dos objetos
      const allKeys = new Set();
      allEmployeeDetails.forEach(emp => {
        const flattenedEmp = flattenObject(emp);
        Object.keys(flattenedEmp).forEach(key => allKeys.add(key));
      });

      const rawCsvWriter = createCsvWriter({
        path: 'funcionarios_dados_brutos.csv',
        header: Array.from(allKeys).map(key => ({ id: key, title: key }))
      });

      const flattenedData = allEmployeeDetails.map(emp => flattenObject(emp));
      await rawCsvWriter.writeRecords(flattenedData);
      console.log('✓ CSV de dados brutos dos funcionários salvo: funcionarios_dados_brutos.csv');
    }

    // 5. Gerar CSV formatado para o schema User
    console.log('Gerando CSV formatado para schema User...');
    
    const userCsvWriter = createCsvWriter({
      path: 'funcionarios_schema_user.csv',
      header: [
        { id: 'id', title: 'id' },
        { id: 'email', title: 'email' },
        { id: 'password', title: 'password' },
        { id: 'active', title: 'active' },
        { id: 'createdAt', title: 'createdAt' },
        { id: 'updatedAt', title: 'updatedAt' },
        { id: 'address', title: 'address' },
        { id: 'birthDate', title: 'birthDate' },
        { id: 'cnpj', title: 'cnpj' },
        { id: 'cpf', title: 'cpf' },
        { id: 'createdById', title: 'createdById' },
        { id: 'fullName', title: 'fullName' },
        { id: 'login', title: 'login' },
        { id: 'modules', title: 'modules' },
        { id: 'permissions', title: 'permissions' },
        { id: 'phone', title: 'phone' },
        { id: 'companyId', title: 'companyId' },
        { id: 'deletedAt', title: 'deletedAt' },
        { id: 'deletedById', title: 'deletedById' },
        { id: 'failedLoginAttempts', title: 'failedLoginAttempts' },
        { id: 'lastLoginAt', title: 'lastLoginAt' },
        { id: 'lastLoginIp', title: 'lastLoginIp' },
        { id: 'passwordChangedAt', title: 'passwordChangedAt' },
        { id: 'role', title: 'role' },
        { id: 'professionId', title: 'professionId' },
        { id: 'profileImageUrl', title: 'profileImageUrl' },
        { id: 'city', title: 'city' },
        { id: 'postalCode', title: 'postalCode' },
        { id: 'state', title: 'state' },
        { id: 'branchId', title: 'branchId' },
        { id: 'neighborhood', title: 'neighborhood' },
        { id: 'modulePreferences', title: 'modulePreferences' },
        { id: 'codigoOriginal', title: 'codigoOriginal' },
        { id: 'cargoDescricao', title: 'cargoDescricao' },
        { id: 'encarregado', title: 'encarregado' }
      ]
    });

    const mappedData = allEmployeeDetails.map(emp => mapToUserSchema(emp));
    await userCsvWriter.writeRecords(mappedData);
    console.log('✓ CSV formatado para schema User salvo: funcionarios_schema_user.csv');

    // 6. Gerar CSV das profissões/cargos
    console.log('Gerando CSV das profissões...');
    
    if (professions.length > 0) {
      // CSV com dados brutos das profissões
      const professionRawCsvWriter = createCsvWriter({
        path: 'profissoes_dados_brutos.csv',
        header: [
          { id: 'codigo', title: 'codigo' },
          { id: 'descricao', title: 'descricao' }
        ]
      });

      await professionRawCsvWriter.writeRecords(professions);
      console.log('✓ CSV de dados brutos das profissões salvo: profissoes_dados_brutos.csv');

      // CSV formatado para schema Profession
      const professionSchemaCsvWriter = createCsvWriter({
        path: 'profissoes_schema_profession.csv',
        header: [
          { id: 'id', title: 'id' },
          { id: 'name', title: 'name' },
          { id: 'description', title: 'description' },
          { id: 'active', title: 'active' },
          { id: 'groupId', title: 'groupId' },
          { id: 'companyId', title: 'companyId' },
          { id: 'createdAt', title: 'createdAt' },
          { id: 'updatedAt', title: 'updatedAt' },
          { id: 'deletedAt', title: 'deletedAt' },
          { id: 'codigoOriginal', title: 'codigoOriginal' }
        ]
      });

      const mappedProfessions = professions.map(prof => mapToProfessionSchema(prof));
      await professionSchemaCsvWriter.writeRecords(mappedProfessions);
      console.log('✓ CSV formatado para schema Profession salvo: profissoes_schema_profession.csv');
    }

    // 7. Gerar relatório de migração
    console.log('\nGerando relatório de migração...');
    const report = {
      dataProcessamento: new Date().toISOString(),
      funcionarios: {
        total: allEmployeeDetails.length,
        ativos: allEmployeeDetails.filter(emp => emp.ativo).length,
        inativos: allEmployeeDetails.filter(emp => !emp.ativo).length,
        encarregados: allEmployeeDetails.filter(emp => emp.encarregado).length
      },
      profissoes: {
        total: professions.length,
        lista: professions.map(p => ({ codigo: p.codigo, descricao: p.descricao }))
      },
      cargosEncontrados: [...new Set(allEmployeeDetails.map(emp => emp.cargo?.descricao).filter(Boolean))],
      observacoes: [
        'Passwords foram definidos com hash padrão - DEVEM SER ALTERADOS',
        'Emails foram gerados automaticamente quando não fornecidos',
        'CompanyId, BranchId e ProfessionId precisam ser mapeados manualmente',
        'Módulos e permissões foram definidos com valores padrão',
        'GroupId das profissões precisa ser mapeado para grupos existentes'
      ]
    };

    fs.writeFileSync('relatorio_migracao.json', JSON.stringify(report, null, 2));
    console.log('✓ Relatório de migração salvo: relatorio_migracao.json');

    console.log('\n🎉 Migração concluída com sucesso!');
    console.log(`📁 Arquivos gerados:`);
    console.log(`   • funcionarios_dados_brutos.csv`);
    console.log(`   • funcionarios_schema_user.csv`);
    console.log(`   • profissoes_dados_brutos.csv`);
    console.log(`   • profissoes_schema_profession.csv`);
    console.log(`   • relatorio_migracao.json`);
    
    console.log(`\n📊 Resumo:`);
    console.log(`   • Total de funcionários: ${report.funcionarios.total}`);
    console.log(`   • Funcionários ativos: ${report.funcionarios.ativos}`);
    console.log(`   • Funcionários inativos: ${report.funcionarios.inativos}`);
    console.log(`   • Encarregados: ${report.funcionarios.encarregados}`);
    console.log(`   • Total de profissões: ${report.profissoes.total}`);

  } catch (error) {
    console.error('❌ Erro durante a migração:', error.message);
    process.exit(1);
  }
}

// Função auxiliar para achatar objetos aninhados
function flattenObject(obj, prefix = '') {
  const flattened = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (obj[key] !== null && typeof obj[key] === 'object' && !Array.isArray(obj[key]) && !(obj[key] instanceof Date)) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }
  
  return flattened;
}

// Executar o script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, fetchAllEmployees, fetchEmployeeDetails, fetchAllProfessions, mapToUserSchema, mapToProfessionSchema };