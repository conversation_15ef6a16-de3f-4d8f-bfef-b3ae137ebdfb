import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash, Calendar, CreditCard, AlertCircle } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import PersonInsuranceFormModal from "@/components/people/PersonInsuranceFormModal";

const PersonInsurancesTab = ({ personId, onClose, isCreating = false, onAddTempInsurance, tempInsurances = [] }) => {
  const [insurances, setInsurances] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [selectedInsurance, setSelectedInsurance] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [insuranceToDelete, setInsuranceToDelete] = useState(null);

  useEffect(() => {
    if (personId) {
      loadInsurances();
    } else if (isCreating) {
      // Se estiver no modo de criação, não precisa carregar do servidor
      setIsLoading(false);
    }
  }, [personId, isCreating]);

  const loadInsurances = async () => {
    if (!personId) return;

    setIsLoading(true);
    setError(null);
    try {
      const data = await insurancesService.listPersonInsurances(personId);
      setInsurances(data);
    } catch (err) {
      console.error("Erro ao carregar convênios:", err);
      setError("Não foi possível carregar os convênios da pessoa.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddInsurance = () => {
    setSelectedInsurance(null);
    setFormModalOpen(true);
  };

  // Função para adicionar um convênio temporário durante a criação
  const handleAddTempInsurance = (insurance) => {
    if (isCreating && onAddTempInsurance) {
      onAddTempInsurance({
        id: `temp-${Date.now()}`,
        ...insurance,
        isTemp: true
      });
    }
  };

  const handleEditInsurance = (insurance) => {
    setSelectedInsurance(insurance);
    setFormModalOpen(true);
  };

  const handleDeleteInsurance = (insurance) => {
    setInsuranceToDelete(insurance);
    setConfirmDialogOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await insurancesService.removePersonInsurance(personId, insuranceToDelete.insuranceId || insuranceToDelete.id);
      loadInsurances();
      setConfirmDialogOpen(false);
      // Adicionar notificação de sucesso
      if (window.showToast) {
        window.showToast({
          type: "success",
          message: `Convênio removido com sucesso`
        });
      }
    } catch (err) {
      console.error("Erro ao excluir convênio:", err);
      setError("Não foi possível excluir o convênio.");
      // Adicionar notificação de erro
      if (window.showToast) {
        window.showToast({
          type: "error",
          message: `Erro ao remover convênio: ${err.response?.data?.message || "Erro desconhecido"}`
        });
      }
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  const renderInsurancesList = () => {
    // Determine which list of insurances to usar
    const insuranceList = isCreating ? tempInsurances : insurances;

    // Normalize insurance data structure as it could vary based on API response
    const normalizedInsurances = insuranceList.map(ins => {
      // If the insurance object is already normalized
      if (ins.insurance) {
        return {
          id: ins.insurance.id,
          name: ins.insurance.name,
          policyNumber: ins.policyNumber,
          validUntil: ins.validUntil,
          notes: ins.notes,
          // For deletion, we need the join record ID
          insuranceId: ins.insurance.id
        };
      }
      // If we directly get Insurance objects
      else if (ins.id && ins.name) {
        return {
          id: ins.id,
          name: ins.name,
          policyNumber: ins.policyNumber,
          validUntil: ins.validUntil,
          notes: ins.notes,
          insuranceId: ins.id
        };
      }
      // If we get records from PersonInsurance with separate insurance properties
      else if (ins.insuranceId) {
        return {
          id: ins.insuranceId,
          name: ins.insuranceName || `Convênio ${ins.insuranceId}`,
          policyNumber: ins.policyNumber,
          validUntil: ins.validUntil,
          notes: ins.notes,
          insuranceId: ins.insuranceId
        };
      }
      // Fallback for unexpected structure
      return ins;
    });

    if (normalizedInsurances.length === 0) {
      return (
        <div className="bg-neutral-50 dark:bg-gray-700 p-6 rounded-lg text-center text-neutral-600 dark:text-neutral-300">
          <p>Nenhum convênio associado a esta pessoa.</p>
        </div>
      );
    }

    return (
      <div className="grid gap-4">
        {normalizedInsurances.map((insurance) => (
          <div
            key={insurance.id || insurance.insuranceId}
            className="bg-white dark:bg-gray-700 border border-neutral-200 dark:border-gray-600 rounded-lg p-4 shadow-sm"
          >
            <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
              <div className="space-y-1">
                <h4 className="font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2">
                  {insurance.name}
                </h4>
                <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-sm">
                  {insurance.policyNumber && (
                    <span className="flex items-center gap-1 text-neutral-600 dark:text-neutral-300">
                      <CreditCard size={14} />
                      <span>Carteirinha: {insurance.policyNumber}</span>
                    </span>
                  )}
                  {insurance.validUntil && (
                    <span className="flex items-center gap-1 text-neutral-600 dark:text-neutral-300">
                      <Calendar size={14} />
                      <span>Validade: {formatDate(insurance.validUntil)}</span>
                    </span>
                  )}
                </div>
                {insurance.notes && (
                  <p className="text-sm text-neutral-500 dark:text-neutral-400">
                    {insurance.notes}
                  </p>
                )}
              </div>
              <div className="flex items-center gap-2">
                <button
                  onClick={() => handleEditInsurance(insurance)}
                  className="p-2 hover:bg-neutral-100 dark:hover:bg-gray-600 rounded-lg text-neutral-600 dark:text-neutral-300"
                >
                  <Edit size={18} />
                </button>
                <button
                  onClick={() => handleDeleteInsurance(insurance)}
                  className="p-2 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg text-red-600 dark:text-red-400"
                >
                  <Trash size={18} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  // Verificar se há um ID de pessoa e não está no modo de criação
  if (!personId && !isCreating) {
    return (
      <div className="p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex flex-col items-center justify-center space-y-4">
        <div className="flex items-center gap-2">
          <CreditCard size={24} />
          <h3 className="text-lg font-semibold">Convênios</h3>
        </div>
        <p className="text-center">Salve os dados básicos da pessoa antes de adicionar convênios.</p>
        <button
          onClick={() => onClose()}
          className="mt-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
        >
          Voltar para Informações
        </button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-200">
          Convênios Associados
        </h3>
        <button
          onClick={handleAddInsurance}
          className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
        >
          <Plus size={16} />
          <span>Adicionar Convênio</span>
        </button>
      </div>

      {error && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start gap-2">
          <AlertCircle size={16} className="mt-0.5" />
          <span>{error}</span>
        </div>
      )}

      {renderInsurancesList()}

      {formModalOpen && (
        <PersonInsuranceFormModal
          isOpen={formModalOpen}
          onClose={() => setFormModalOpen(false)}
          personId={personId}
          personInsurance={selectedInsurance}
          isCreating={isCreating}
          onAddTempInsurance={onAddTempInsurance}
          onSuccess={() => {
            if (!isCreating) {
              loadInsurances();
            }
            setFormModalOpen(false);
          }}
        />
      )}

      {confirmDialogOpen && (
        <ConfirmationDialog
          isOpen={confirmDialogOpen}
          onClose={() => setConfirmDialogOpen(false)}
          onConfirm={confirmDelete}
          title="Remover Convênio"
          message={`Tem certeza que deseja remover o convênio ${insuranceToDelete?.name} desta pessoa?`}
          variant="danger"
        />
      )}
    </div>
  );
};

export default PersonInsurancesTab;