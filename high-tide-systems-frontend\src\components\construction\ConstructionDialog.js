"use client";

import React, { useRef, useEffect, useState } from 'react';
import { X, Construction, HardHat, Hammer, Wrench, AlertTriangle } from 'lucide-react';

/**
 * Caixa de diálogo para exibir mensagens "Em Construção"
 * 
 * @param {Object} props - Propriedades do componente
 * @param {string} props.title - <PERSON><PERSON><PERSON><PERSON> da mensagem
 * @param {string|React.ReactNode} props.content - Conteúdo/descrição da mensagem
 * @param {string} props.position - Posição do diálogo ('top', 'right', 'bottom', 'left', 'auto')
 * @param {string} props.targetSelector - Seletor do elemento alvo para posicionamento
 * @param {string} props.icon - Ícone a ser exibido ('Construction', 'HardHat', 'Hammer', 'Wrench', 'AlertTriangle')
 * @param {function} props.onClose - Função chamada ao clicar em "Fechar"
 */
const ConstructionDialog = ({
  title,
  content,
  position = 'auto',
  targetSelector,
  icon = 'Construction',
  onClose
}) => {
  const dialogRef = useRef(null);
  const [dialogPosition, setDialogPosition] = useState({ top: '50%', left: '50%', transform: 'translate(-50%, -50%)' });
  const [calculatedPosition, setCalculatedPosition] = useState(position);

  // Selecionar o ícone correto
  const IconComponent = () => {
    switch (icon) {
      case 'HardHat':
        return <HardHat size={24} className="text-amber-500 dark:text-amber-400" />;
      case 'Hammer':
        return <Hammer size={24} className="text-amber-500 dark:text-amber-400" />;
      case 'Wrench':
        return <Wrench size={24} className="text-amber-500 dark:text-amber-400" />;
      case 'AlertTriangle':
        return <AlertTriangle size={24} className="text-amber-500 dark:text-amber-400" />;
      case 'Construction':
      default:
        return <Construction size={24} className="text-amber-500 dark:text-amber-400" />;
    }
  };

  // Calcula a melhor posição para o diálogo com base no elemento alvo
  useEffect(() => {
    if (!targetSelector || !dialogRef.current) return;

    const calculatePosition = () => {
      const targetElement = document.querySelector(targetSelector);
      if (!targetElement) return;

      const targetRect = targetElement.getBoundingClientRect();
      const dialogRect = dialogRef.current.getBoundingClientRect();
      
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
      
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Auto-calcular a melhor posição se for 'auto'
      let bestPosition = position;
      if (position === 'auto') {
        // Calcular espaço disponível em cada direção
        const spaceAbove = targetRect.top;
        const spaceBelow = viewportHeight - targetRect.bottom;
        const spaceLeft = targetRect.left;
        const spaceRight = viewportWidth - targetRect.right;

        // Determinar a direção com mais espaço
        const maxSpace = Math.max(spaceAbove, spaceBelow, spaceLeft, spaceRight);
        
        if (maxSpace === spaceBelow) bestPosition = 'bottom';
        else if (maxSpace === spaceAbove) bestPosition = 'top';
        else if (maxSpace === spaceRight) bestPosition = 'right';
        else bestPosition = 'left';
      }

      setCalculatedPosition(bestPosition);

      let newPosition = {};
      
      // Calcular posição com base na direção escolhida
      switch (bestPosition) {
        case 'top':
          newPosition = {
            top: targetRect.top + scrollTop - dialogRect.height - 20,
            left: targetRect.left + scrollLeft + (targetRect.width / 2) - (dialogRect.width / 2),
            transform: 'none'
          };
          break;
        case 'right':
          newPosition = {
            top: targetRect.top + scrollTop + (targetRect.height / 2) - (dialogRect.height / 2),
            left: targetRect.right + scrollLeft + 20,
            transform: 'none'
          };
          break;
        case 'bottom':
          newPosition = {
            top: targetRect.bottom + scrollTop + 20,
            left: targetRect.left + scrollLeft + (targetRect.width / 2) - (dialogRect.width / 2),
            transform: 'none'
          };
          break;
        case 'left':
          newPosition = {
            top: targetRect.top + scrollTop + (targetRect.height / 2) - (dialogRect.height / 2),
            left: targetRect.left + scrollLeft - dialogRect.width - 20,
            transform: 'none'
          };
          break;
        default:
          // Posição centralizada como fallback
          newPosition = {
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)'
          };
      }

      // Ajuste para garantir que o diálogo fique dentro da viewport
      if (newPosition.left < 20) newPosition.left = 20;
      if (newPosition.top < 20) newPosition.top = 20;
      if (newPosition.left + dialogRect.width > viewportWidth - 20) {
        newPosition.left = viewportWidth - dialogRect.width - 20;
      }
      if (newPosition.top + dialogRect.height > viewportHeight - 20) {
        newPosition.top = viewportHeight - dialogRect.height - 20;
      }

      setDialogPosition(newPosition);
    };

    // Pequeno atraso para garantir que o diálogo tenha sido renderizado com dimensões corretas
    const timer = setTimeout(calculatePosition, 100);

    // Recalcular em caso de redimensionamento da janela
    window.addEventListener('resize', calculatePosition);
    
    return () => {
      clearTimeout(timer);
      window.removeEventListener('resize', calculatePosition);
    };
  }, [targetSelector, position, dialogRef.current]);

  // Classe para adicionar seta direcional
  const getPositionClass = () => {
    switch (calculatedPosition) {
      case 'top': return 'construction-dialog-arrow-bottom';
      case 'right': return 'construction-dialog-arrow-left';
      case 'bottom': return 'construction-dialog-arrow-top';
      case 'left': return 'construction-dialog-arrow-right';
      default: return '';
    }
  };

  return (
    <>
      {/* Estilos para setas direcionais */}
      <style jsx global>{`
        .construction-dialog-arrow-top:after {
          content: '';
          position: absolute;
          top: -10px;
          left: 50%;
          transform: translateX(-50%);
          border-width: 0 10px 10px 10px;
          border-style: solid;
          border-color: transparent transparent #ffffff transparent;
          filter: drop-shadow(0 -2px 2px rgba(0,0,0,0.1));
        }
        
        .construction-dialog-arrow-right:after {
          content: '';
          position: absolute;
          right: -10px;
          top: 50%;
          transform: translateY(-50%);
          border-width: 10px 0 10px 10px;
          border-style: solid;
          border-color: transparent transparent transparent #ffffff;
          filter: drop-shadow(2px 0 2px rgba(0,0,0,0.1));
        }
        
        .construction-dialog-arrow-bottom:after {
          content: '';
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
          border-width: 10px 10px 0 10px;
          border-style: solid;
          border-color: #ffffff transparent transparent transparent;
          filter: drop-shadow(0 2px 2px rgba(0,0,0,0.1));
        }
        
        .construction-dialog-arrow-left:after {
          content: '';
          position: absolute;
          left: -10px;
          top: 50%;
          transform: translateY(-50%);
          border-width: 10px 10px 10px 0;
          border-style: solid;
          border-color: transparent #ffffff transparent transparent;
          filter: drop-shadow(-2px 0 2px rgba(0,0,0,0.1));
        }
        
        .dark .construction-dialog-arrow-top:after,
        .dark .construction-dialog-arrow-right:after,
        .dark .construction-dialog-arrow-bottom:after,
        .dark .construction-dialog-arrow-left:after {
          border-color: transparent;
        }
        
        .dark .construction-dialog-arrow-top:after {
          border-bottom-color: #1f2937;
        }
        
        .dark .construction-dialog-arrow-right:after {
          border-left-color: #1f2937;
        }
        
        .dark .construction-dialog-arrow-bottom:after {
          border-top-color: #1f2937;
        }
        
        .dark .construction-dialog-arrow-left:after {
          border-right-color: #1f2937;
        }
      `}</style>

      {/* Diálogo de "Em Construção" */}
      <div 
        ref={dialogRef}
        className={`fixed z-[9999] w-80 p-5 rounded-lg bg-white dark:bg-gray-800 shadow-xl dark:shadow-hard-dark ${getPositionClass()}`}
        style={{
          top: dialogPosition.top,
          left: dialogPosition.left,
          transform: dialogPosition.transform,
        }}
      >
        {/* Cabeçalho */}
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-2">
            <IconComponent />
            <h3 className="text-lg font-bold text-amber-600 dark:text-amber-400">
              {title}
            </h3>
          </div>
          <button 
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 rounded-full transition-colors"
            aria-label="Fechar mensagem"
          >
            <X size={18} />
          </button>
        </div>
        
        {/* Conteúdo */}
        <div className="mb-4 text-gray-700 dark:text-gray-300">
          {content}
        </div>
        
        {/* Botão de fechar */}
        <div className="flex justify-end">
          <button 
            onClick={onClose}
            className="px-4 py-2 rounded-md bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white transition-colors"
          >
            Entendi
          </button>
        </div>
      </div>
    </>
  );
};

export default ConstructionDialog;
