# Script de Cadastro de Pacientes

Este script cadastra pacientes (pessoas adicionais) para os clientes existentes no sistema, criando relações familiares entre eles.

## Características

- Cria entre 0 e 4 pacientes adicionais para cada cliente, com maior probabilidade de criar 1 paciente
- Utiliza relações familiares realistas (cônjuge, filho, pai, mãe, irmão, etc.)
- Gera datas de nascimento coerentes com o tipo de relação (filhos mais jovens, pais mais velhos, etc.)
- Utiliza o mesmo endereço do titular em 70% dos casos
- Nos outros 30%, utiliza CEPs reais para buscar endereços completos via API ViaCEP
- Associa o mesmo convênio do cliente ao paciente em 80% dos casos
- Gera dados realistas como CPF válido, telefone, email, etc.

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-patients-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed-patients.js
```

### Opção 3: Executando dentro do container Docker

```bash
docker exec -it high-tide-systems-api node prisma/seed-patients.js
```

## Detalhes do Script

O script realiza as seguintes operações:

1. Busca todos os clientes ativos no sistema com suas pessoas titulares e convênios
2. Para cada cliente, determina quantos pacientes adicionais criar:
   - 15% de chance de não criar nenhum paciente
   - 50% de chance de criar 1 paciente
   - 20% de chance de criar 2 pacientes
   - 10% de chance de criar 3 pacientes
   - 5% de chance de criar 4 pacientes
3. Para cada paciente, gera:
   - Nome completo aleatório
   - Relacionamento com o titular (cônjuge, filho, pai, mãe, etc.)
   - CPF válido gerado algoritmicamente
   - Data de nascimento coerente com o tipo de relação
   - Endereço (mesmo do titular ou novo endereço via CEP)
   - Dados de contato (email e telefone, com chance de serem nulos)
4. Associa o mesmo convênio do cliente ao paciente em 80% dos casos

## Tipos de Relacionamentos

O script utiliza os seguintes tipos de relacionamentos:

- Cônjuge
- Filho(a)
- Pai
- Mãe
- Irmão(ã)
- Avô(ó)
- Neto(a)
- Tio(a)
- Sobrinho(a)
- Primo(a)

## Observações

- O script verifica se o CPF já existe antes de criar um novo paciente
- As datas de nascimento são geradas de acordo com o tipo de relacionamento:
  - Filhos, sobrinhos e netos: 0-25 anos
  - Pais, mães, avós e tios: 40-80 anos
  - Outros relacionamentos: 18-70 anos
- Os pacientes são associados ao mesmo cliente que o titular
- Os convênios associados aos pacientes mantêm o mesmo número de apólice e data de validade do titular
