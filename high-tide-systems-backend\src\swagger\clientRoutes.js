// src/swagger/clientRoutes.js

/**
 * @swagger
 * tags:
 *   name: Clientes
 *   description: Gerenciamento de clientes
 */

/**
 * @swagger
 * /clients:
 *   post:
 *     summary: Cria um novo cliente
 *     description: Cria um novo cliente no sistema. Requer autenticação.
 *     tags: [Clientes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - login
 *               - email
 *               - fullName
 *               - password
 *             properties:
 *               login:
 *                 type: string
 *                 description: Nome de login do cliente
 *                 example: carlos.oliveira
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email do cliente
 *                 example: <EMAIL>
 *               fullName:
 *                 type: string
 *                 description: Nome completo do cliente
 *                 example: <PERSON>
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 6
 *                 description: Senha do cliente (mínimo 6 caracteres)
 *                 example: senha789
 *               cpf:
 *                 type: string
 *                 description: CPF do cliente (sem formatação)
 *                 example: "12345678901"
 *               cnpj:
 *                 type: string
 *                 description: CNPJ do cliente (sem formatação)
 *                 example: "12345678000199"
 *               birthDate:
 *                 type: string
 *                 format: date
 *                 description: Data de nascimento do cliente
 *                 example: "1980-10-20"
 *               address:
 *                 type: string
 *                 description: Endereço do cliente
 *                 example: Rua das Flores, 789
 *               phone:
 *                 type: string
 *                 description: Telefone do cliente
 *                 example: "11977776666"
 *     responses:
 *       201:
 *         description: Cliente criado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ClientResponse'
 *       400:
 *         description: Dados inválidos ou cliente já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista clientes
 *     description: Retorna uma lista paginada de clientes. Permite filtrar por busca e status.
 *     tags: [Clientes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Número de itens por página
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Texto para busca (nome, email, login, CPF, CNPJ)
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filtrar por status (ativo/inativo)
 *     responses:
 *       200:
 *         description: Lista de clientes
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 clients:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ClientResponse'
 *                 total:
 *                   type: integer
 *                   description: Total de registros
 *                 pages:
 *                   type: integer
 *                   description: Total de páginas
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /clients/{id}:
 *   get:
 *     summary: Obtém detalhes de um cliente
 *     description: Retorna os detalhes completos de um cliente específico, incluindo convênios.
 *     tags: [Clientes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do cliente
 *     responses:
 *       200:
 *         description: Detalhes do cliente
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ClientResponse'
 *                 - type: object
 *                   properties:
 *                     clientInsurances:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           insurance:
 *                             $ref: '#/components/schemas/Insurance'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Cliente não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Cliente não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   
 *   put:
 *     summary: Atualiza um cliente
 *     description: Atualiza os dados de um cliente existente.
 *     tags: [Clientes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do cliente
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Nome completo do cliente
 *               address:
 *                 type: string
 *                 description: Endereço do cliente
 *               phone:
 *                 type: string
 *                 description: Telefone do cliente
 *               birthDate:
 *                 type: string
 *                 format: date
 *                 description: Data de nascimento do cliente
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email do cliente
 *               password:
 *                 type: string
 *                 format: password
 *                 description: Nova senha do cliente (opcional)
 *     responses:
 *       200:
 *         description: Cliente atualizado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ClientResponse'
 *       400:
 *         description: Dados inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Cliente não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Cliente não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove um cliente
 *     description: Remove um cliente do sistema.
 *     tags: [Clientes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do cliente
 *     responses:
 *       204:
 *         description: Cliente removido com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Cliente não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Cliente não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /clients/{id}/status:
 *   patch:
 *     summary: Alterna o status de um cliente
 *     description: Ativa ou desativa um cliente no sistema.
 *     tags: [Clientes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do cliente
 *     responses:
 *       200:
 *         description: Status alterado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   format: uuid
 *                 fullName:
 *                   type: string
 *                 active:
 *                   type: boolean
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Cliente não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Cliente não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */