// tests/manual-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Variáveis para armazenar IDs criados durante os testes
let personId;
let insuranceId;
let serviceTypeId;
let limitId;
let schedulingId;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes das alterações...');
  
  try {
    // Teste 1: Criar uma pessoa
    console.log('\n1. <PERSON><PERSON>do pessoa...');
    const testPerson = {
      fullName: `Test Person ${Date.now()}`,
      cpf: `${Date.now()}`.substring(0, 11),
      birthDate: '1990-01-01',
      address: 'Test Address',
      neighborhood: 'Test Neighborhood',
      city: 'Test City',
      state: 'TS',
      postalCode: '12345-678',
      phone: '1234567890',
      email: `test_person_${Date.now()}@example.com`,
      gender: 'M',
      notes: 'Test notes'
    };
    
    const createPersonResponse = await api.post('/persons', testPerson);
    
    if (createPersonResponse.status === 201) {
      console.log('✅ Pessoa criada com sucesso!');
      personId = createPersonResponse.data.id;
      console.log(`ID da pessoa: ${personId}`);
    } else {
      console.log('❌ Falha ao criar pessoa');
      return;
    }
    
    // Teste 2: Criar um convênio
    console.log('\n2. Criando convênio...');
    const testInsurance = {
      name: `Test Insurance ${Date.now()}`
    };
    
    const createInsuranceResponse = await api.post('/insurances', testInsurance);
    
    if (createInsuranceResponse.status === 201) {
      console.log('✅ Convênio criado com sucesso!');
      insuranceId = createInsuranceResponse.data.id;
      console.log(`ID do convênio: ${insuranceId}`);
    } else {
      console.log('❌ Falha ao criar convênio');
      return;
    }
    
    // Teste 3: Criar um tipo de serviço
    console.log('\n3. Criando tipo de serviço...');
    const testServiceType = {
      name: `Test Service Type ${Date.now()}`,
      value: 100
    };
    
    const createServiceTypeResponse = await api.post('/service-types', testServiceType);
    
    if (createServiceTypeResponse.status === 201) {
      console.log('✅ Tipo de serviço criado com sucesso!');
      serviceTypeId = createServiceTypeResponse.data.id;
      console.log(`ID do tipo de serviço: ${serviceTypeId}`);
    } else {
      console.log('❌ Falha ao criar tipo de serviço');
      return;
    }
    
    // Teste 4: Criar um limite de serviço
    console.log('\n4. Criando limite de serviço...');
    const testLimit = {
      personId,
      insuranceId,
      serviceTypeId,
      monthlyLimit: 5
    };
    
    const createLimitResponse = await api.post('/insurance-service-limits', testLimit);
    
    if (createLimitResponse.status === 201) {
      console.log('✅ Limite de serviço criado com sucesso!');
      limitId = createLimitResponse.data.id;
      console.log(`ID do limite: ${limitId}`);
    } else {
      console.log('❌ Falha ao criar limite de serviço');
      return;
    }
    
    // Teste 5: Criar um agendamento
    console.log('\n5. Criando agendamento...');
    
    // Obter um local
    const locationsResponse = await api.get('/locations');
    if (locationsResponse.data.length === 0) {
      console.log('❌ Não há locais cadastrados para realizar os testes');
      return;
    }
    const locationId = locationsResponse.data[0].id;
    console.log(`✅ Local encontrado: ${locationId}`);
    
    // Dados para o agendamento
    const startDate = new Date();
    startDate.setHours(startDate.getHours() + 1);
    startDate.setMinutes(0, 0, 0);
    
    const endDate = new Date(startDate);
    endDate.setHours(endDate.getHours() + 1);
    
    const testScheduling = {
      personId,
      userId: TEST_TOKEN.replace('TEST_TOKEN_', ''),
      creatorId: TEST_TOKEN.replace('TEST_TOKEN_', ''),
      locationId,
      serviceTypeId,
      insuranceId,
      title: 'Teste de Agendamento',
      description: 'Descrição do teste de agendamento',
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      status: 'PENDING'
    };
    
    try {
      const createSchedulingResponse = await api.post('/schedulings', testScheduling);
      
      if (createSchedulingResponse.status === 201) {
        console.log('✅ Agendamento criado com sucesso!');
        schedulingId = createSchedulingResponse.data.mainScheduling.id;
        console.log(`ID do agendamento: ${schedulingId}`);
      } else {
        console.log('❌ Falha ao criar agendamento');
        return;
      }
    } catch (error) {
      console.log('❌ Falha ao criar agendamento:', error.response?.data?.message || error.message);
      // Continuar com os testes mesmo se a criação falhar
    }
    
    // Teste 6: Atualizar agendamento
    if (schedulingId) {
      console.log('\n6. Atualizando agendamento...');
      const updateSchedulingResponse = await api.put(`/schedulings/${schedulingId}`, {
        title: 'Teste de Agendamento Atualizado',
        description: 'Descrição atualizada'
      });
      
      if (updateSchedulingResponse.status === 200) {
        console.log('✅ Agendamento atualizado com sucesso!');
        console.log(`Novo título: ${updateSchedulingResponse.data.title}`);
      } else {
        console.log('❌ Falha ao atualizar agendamento');
      }
    }
    
    // Teste 7: Listar pessoas
    console.log('\n7. Listando pessoas...');
    const listPersonsResponse = await api.get('/persons');
    
    if (listPersonsResponse.status === 200) {
      console.log(`✅ ${listPersonsResponse.data.people.length} pessoas encontradas`);
    } else {
      console.log('❌ Falha ao listar pessoas');
    }
    
    // Teste 8: Listar limites de serviço
    console.log('\n8. Listando limites de serviço...');
    const listLimitsResponse = await api.get('/insurance-service-limits');
    
    if (listLimitsResponse.status === 200) {
      console.log(`✅ ${listLimitsResponse.data.length} limites encontrados`);
    } else {
      console.log('❌ Falha ao listar limites de serviço');
    }
    
    // Limpeza
    console.log('\n9. Limpando dados de teste...');
    
    // Excluir agendamento
    if (schedulingId) {
      try {
        const deleteSchedulingResponse = await api.delete(`/schedulings/${schedulingId}`);
        if (deleteSchedulingResponse.status === 204) {
          console.log('✅ Agendamento excluído com sucesso!');
        } else {
          console.log('❌ Falha ao excluir agendamento');
        }
      } catch (error) {
        console.log('❌ Falha ao excluir agendamento:', error.response?.data?.message || error.message);
      }
    }
    
    // Excluir limite de serviço
    if (limitId) {
      try {
        const deleteLimitResponse = await api.delete(`/insurance-service-limits/${limitId}`);
        if (deleteLimitResponse.status === 204) {
          console.log('✅ Limite de serviço excluído com sucesso!');
        } else {
          console.log('❌ Falha ao excluir limite de serviço');
        }
      } catch (error) {
        console.log('❌ Falha ao excluir limite de serviço:', error.response?.data?.message || error.message);
      }
    }
    
    // Excluir tipo de serviço
    if (serviceTypeId) {
      try {
        const deleteServiceTypeResponse = await api.delete(`/service-types/${serviceTypeId}`);
        if (deleteServiceTypeResponse.status === 204) {
          console.log('✅ Tipo de serviço excluído com sucesso!');
        } else {
          console.log('❌ Falha ao excluir tipo de serviço');
        }
      } catch (error) {
        console.log('❌ Falha ao excluir tipo de serviço:', error.response?.data?.message || error.message);
      }
    }
    
    // Excluir convênio
    if (insuranceId) {
      try {
        const deleteInsuranceResponse = await api.delete(`/insurances/${insuranceId}`);
        if (deleteInsuranceResponse.status === 204) {
          console.log('✅ Convênio excluído com sucesso!');
        } else {
          console.log('❌ Falha ao excluir convênio');
        }
      } catch (error) {
        console.log('❌ Falha ao excluir convênio:', error.response?.data?.message || error.message);
      }
    }
    
    // Excluir pessoa
    if (personId) {
      try {
        const deletePersonResponse = await api.delete(`/persons/${personId}`);
        if (deletePersonResponse.status === 204) {
          console.log('✅ Pessoa excluída com sucesso!');
        } else {
          console.log('❌ Falha ao excluir pessoa');
        }
      } catch (error) {
        console.log('❌ Falha ao excluir pessoa:', error.response?.data?.message || error.message);
      }
    }
    
    console.log('\n✅ Testes concluídos com sucesso!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
