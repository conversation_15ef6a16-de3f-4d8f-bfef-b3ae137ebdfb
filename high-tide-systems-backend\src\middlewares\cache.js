// src/middlewares/cache.js
const cacheService = require('../services/cacheService');

/**
 * Middleware para cachear respostas de rotas
 * @param {string} prefix - Prefixo para a chave de cache (geralmente o nome do recurso)
 * @param {number} ttl - Tempo de vida em segundos (opcional)
 * @returns {Function} - Middleware Express
 */
const cacheMiddleware = (prefix, ttl) => {
  return async (req, res, next) => {
    // Não usar cache para métodos que modificam dados
    if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(req.method)) {
      return next();
    }

    try {
      // Gerar chave de cache baseada no prefixo, caminho e parâmetros
      const cacheKey = cacheService.generateKey(
        `${prefix}:${req.originalUrl}`,
        {
          userId: req.user?.id,
          companyId: req.user?.companyId,
          role: req.user?.role
        }
      );

      // Verificar se a resposta está em cache
      const cachedResponse = await cacheService.get(cacheKey);

      if (cachedResponse) {
        console.log(`[CACHE] Hit para ${req.originalUrl}`);
        return res.json(cachedResponse);
      }

      // Se não estiver em cache, interceptar a resposta para armazenar no cache
      const originalJson = res.json;
      res.json = function(data) {
        // Restaurar o método original
        res.json = originalJson;

        // Armazenar no cache apenas se for uma resposta bem-sucedida
        if (res.statusCode >= 200 && res.statusCode < 300) {
          cacheService.set(cacheKey, data, ttl)
            .catch(err => console.error(`[CACHE] Erro ao armazenar cache para ${req.originalUrl}:`, err));
        }

        // Chamar o método original
        return originalJson.call(this, data);
      };

      console.log(`[CACHE] Miss para ${req.originalUrl}`);
      next();
    } catch (error) {
      console.error(`[CACHE] Erro no middleware de cache:`, error);
      next();
    }
  };
};

/**
 * Middleware para limpar o cache quando dados são modificados
 * @param {string} pattern - Padrão de chave para limpar (ex: "users:*")
 * @returns {Function} - Middleware Express
 */
const clearCacheMiddleware = (pattern) => {
  return async (req, res, next) => {
    // Armazenar o método original
    const originalJson = res.json;
    const originalSend = res.send;

    // Função para limpar o cache após resposta bem-sucedida
    const clearCacheAfterResponse = async () => {
      try {
        // Limpar cache apenas se a operação foi bem-sucedida
        if (res.statusCode >= 200 && res.statusCode < 300) {
          await cacheService.clear(pattern);
          console.log(`[CACHE] Cache limpo para padrão: ${pattern}`);
        }
      } catch (error) {
        console.error(`[CACHE] Erro ao limpar cache para ${pattern}:`, error);
      }
    };

    // Sobrescrever o método json
    res.json = function(data) {
      // Restaurar o método original
      res.json = originalJson;
      
      // Limpar o cache após enviar a resposta
      clearCacheAfterResponse();
      
      // Chamar o método original
      return originalJson.call(this, data);
    };

    // Sobrescrever o método send
    res.send = function(data) {
      // Restaurar o método original
      res.send = originalSend;
      
      // Limpar o cache após enviar a resposta
      clearCacheAfterResponse();
      
      // Chamar o método original
      return originalSend.call(this, data);
    };

    next();
  };
};

module.exports = {
  cacheMiddleware,
  clearCacheMiddleware
};
