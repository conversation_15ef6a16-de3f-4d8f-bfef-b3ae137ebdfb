# Script de Cadastro de Limites de Convênio

Este script cadastra limites de convênio para pacientes no módulo <PERSON>, associando limites de utilização mensal para diferentes tipos de serviço.

## Características

- Processa todas as pessoas que já possuem convênios associados
- Para cada convênio de uma pessoa, há 80% de chance de criar limites
- Cria limites para diferentes tipos de serviço disponíveis na empresa do convênio
- Os limites mensais variam entre 1 e 20 sessões, com maior probabilidade entre 4 e 12
- O limite anual é calculado como 12 vezes o limite mensal
- Verifica duplicidade para não criar limites repetidos

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-insurance-limits-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed-insurance-limits.js
```

### Opção 3: Executando dentro do container Docker

```bash
docker exec -it high-tide-systems-api node prisma/seed-insurance-limits.js
```

## Detalhes do Script

O script realiza as seguintes operações:

1. Busca todas as pessoas que já possuem convênios associados (tabela `PersonInsurance`)
2. Para cada pessoa com convênios:
   - Para cada convênio da pessoa, determina se terá limites (80% de chance)
   - Busca os tipos de serviço disponíveis para a empresa do convênio
   - Seleciona aleatoriamente alguns tipos de serviço para criar limites
   - Para cada tipo de serviço selecionado:
     - Verifica se já existe um limite para esta combinação
     - Determina o limite mensal (entre 1 e 20, com maior probabilidade entre 4 e 12)
     - Cria o limite na tabela `PersonInsuranceServiceLimit`

## Distribuição dos Limites

O script utiliza a seguinte distribuição para os limites mensais:

- 10% de chance para limites entre 1 e 3 sessões mensais
- 70% de chance para limites entre 4 e 12 sessões mensais
- 20% de chance para limites entre 13 e 20 sessões mensais

## Observações

- O script verifica se o limite já existe para a combinação pessoa-convênio-serviço antes de criar um novo
- Nem todas as pessoas terão limites para todos os seus convênios (apenas 80% dos convênios terão limites)
- Nem todos os tipos de serviço disponíveis na empresa terão limites (apenas uma seleção aleatória)
- Os limites são importantes para o controle de utilização de serviços pelos pacientes através de convênios
