const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const Papa = require('papaparse');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const prisma = new PrismaClient();

// CONFIGURAÇÕES
const DEFAULT_PASSWORD = process.env.DEFAULT_PASSWORD || 'TrocarSenha123!';

// Função para ler CSV
async function readCSV(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const results = Papa.parse(fileContent, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: false, // Manter como string
      transformHeader: (header) => header.trim(),
      transform: (value, header) => {
        if (value === '' || value === 'null' || value === 'undefined') {
          return null;
        }

        if (['ativo', 'active'].includes(header)) {
          return value === 'true' || value === true;
        }

        return value;
      }
    });

    if (results.errors.length > 0) {
      console.warn(`Avisos ao ler ${filePath}:`, results.errors);
    }

    return results.data;
  } catch (error) {
    console.error(`Erro ao ler arquivo ${filePath}:`, error.message);
    throw error;
  }
}

// Função para verificar arquivos CSV
function checkCSVFiles() {
  if (!fs.existsSync('pacientes_dados_brutos.csv')) {
    console.error('❌ Arquivo pacientes_dados_brutos.csv não encontrado');
    console.error('💡 Execute primeiro: node migrate-patients.js');
    return false;
  }
  return true;
}

// Função para encontrar empresa da migração
async function findMigrationCompany() {
  console.log('🔍 Buscando empresa da migração...');

  try {
    const company = await prisma.company.findFirst({
      where: {
        OR: [
          { name: 'ABA+ Migrada' },
          { name: process.env.DEFAULT_COMPANY_NAME || 'ABA+ Migrada' }
        ]
      }
    });

    if (!company) {
      throw new Error('Empresa da migração não encontrada. Execute primeiro a migração de funcionários.');
    }

    console.log(`✓ Empresa encontrada: ${company.name} (ID: ${company.id})`);
    return company;
  } catch (error) {
    console.error('Erro ao buscar empresa:', error.message);
    throw error;
  }
}

// Função para encontrar usuário criador padrão
async function findDefaultCreator(companyId) {
  try {
    const creator = await prisma.user.findFirst({
      where: {
        companyId: companyId,
        role: 'COMPANY_ADMIN'
      }
    });

    return creator?.id || null;
  } catch (error) {
    console.warn('Não foi possível encontrar usuário criador padrão');
    return null;
  }
}

// Função para gerar login único
function generateLogin(nome, codigo) {
  if (!nome) return `paciente${codigo}`;

  // Remover acentos e caracteres especiais, converter para minúsculo
  const cleanName = nome
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '')
    .substring(0, 15);

  return cleanName || `paciente${codigo}`;
}

// Função principal de migração
async function main() {
  try {
    console.log('🚀 Iniciando migração de pacientes para banco de dados...\n');

    // Verificar arquivos
    if (!checkCSVFiles()) {
      process.exit(1);
    }

    // Encontrar empresa e criador
    const company = await findMigrationCompany();
    const defaultCreatorId = await findDefaultCreator(company.id);

    // Ler dados brutos dos pacientes
    console.log('📄 Lendo dados dos pacientes...');
    const pacientesData = await readCSV('pacientes_dados_brutos.csv');
    console.log(`✓ ${pacientesData.length} pacientes carregados`);

    // Hash da senha padrão
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS) || 12;
    const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, saltRounds);

    // Contadores
    let clientsCreated = 0;
    let personsCreated = 0;
    let clientPersonRelationsCreated = 0;
    let contactsCreated = 0;
    let errors = 0;

    console.log('\n👥 Processando pacientes...\n');

    for (const paciente of pacientesData) {
      try {
        const codigo = paciente.codigo;
        const nome = paciente.nome?.toString().trim() || '';
        const mae = paciente.mae?.toString().trim() || null;
        const pai = paciente.pai?.toString().trim() || null;
        const email = paciente.email?.toString().trim() || null;
        const usuarioNome = paciente['usuario.nome']?.toString().trim() || null;

        console.log(`Processando: ${nome} (código: ${codigo})`);

        // Determinar se tem responsável
        const temResponsavel = mae || pai;
        let clientData, personData;

        if (temResponsavel) {
          // CASO 1: Paciente com responsável (criança)
          const responsavelNome = mae || pai; // Prioriza mae, depois pai
          const loginResponsavel = usuarioNome || generateLogin(responsavelNome, codigo);
          const emailResponsavel = email || `${loginResponsavel}@responsavel.com`;

          console.log(`  → Criando Client para responsável: ${responsavelNome}`);

          // Verificar se client já existe
          const existingClient = await prisma.client.findFirst({
            where: {
              OR: [
                { email: emailResponsavel },
                { login: loginResponsavel }
              ]
            }
          });

          if (existingClient) {
            console.log(`  ⚠️  Cliente já existe: ${emailResponsavel}`);
            clientData = existingClient;
          } else {
            // Criar Client para responsável
            clientData = await prisma.client.create({
              data: {
                login: loginResponsavel,
                email: emailResponsavel,
                password: hashedPassword,
                active: paciente.ativo !== false,
                createdAt: paciente.dataInclusao ? new Date(paciente.dataInclusao) : new Date(),
                updatedAt: new Date(),
                createdById: defaultCreatorId,
                companyId: company.id
              }
            });
            clientsCreated++;
            console.log(`  ✓ Client criado: ${responsavelNome} (${emailResponsavel})`);
          }

          // Verificar se person já existe
          const existingPerson = await prisma.person.findFirst({
            where: {
              OR: [
                { cpf: paciente.cpf?.toString() },
                { fullName: nome }
              ]
            }
          });

          if (existingPerson) {
            console.log(`  ⚠️  Pessoa já existe: ${nome}`);
            // Verificar se já existe relação ClientPerson
            const existingRelation = await prisma.clientPerson.findFirst({
              where: {
                clientId: clientData.id,
                personId: existingPerson.id
              }
            });

            if (!existingRelation) {
              // Criar relação ClientPerson
              await prisma.clientPerson.create({
                data: {
                  clientId: clientData.id,
                  personId: existingPerson.id,
                  relationship: mae ? 'mae' : 'pai',
                  isPrimary: true
                }
              });
              clientPersonRelationsCreated++;
              console.log(`  ✓ Relação Client-Person criada`);
            }
            continue;
          }

          // Criar Person para criança
          personData = await prisma.person.create({
            data: {
              email: null, // Criança não tem email próprio
              phone: paciente.celular?.toString() || paciente.fone?.toString() || null,
              address: paciente.logradouro?.toString() || null,
              city: paciente.cidade?.toString() || null,
              state: paciente.estado?.toString() || null,
              birthDate: paciente.dataNascimento ? new Date(paciente.dataNascimento) : null,
              notes: paciente.obs?.toString() || null,
              active: paciente.ativo !== false,
              createdAt: paciente.dataInclusao ? new Date(paciente.dataInclusao) : new Date(),
              updatedAt: new Date(),
              neighborhood: paciente.bairro?.toString() || null,
              postalCode: paciente.cep?.toString() || null,
              cpf: paciente.cpf?.toString() || null,
              createdById: defaultCreatorId,
              fullName: nome,
              gender: paciente.sexo?.toString() || null,
              profileImageUrl: paciente.foto ? `https://app.abamais.com/photos/${paciente.foto}` : null,
              relationship: null, // Paciente principal
              useClientEmail: false,
              useClientPhone: true
            }
          });
          personsCreated++;
          console.log(`  ✓ Person criada: ${nome} (criança)`);

          // Criar relação ClientPerson
          await prisma.clientPerson.create({
            data: {
              clientId: clientData.id,
              personId: personData.id,
              relationship: mae ? 'mae' : 'pai', // Quem é o responsável
              isPrimary: true // Responsável principal
            }
          });
          clientPersonRelationsCreated++;
          console.log(`  ✓ Relação Client-Person criada (${mae ? 'mãe' : 'pai'} responsável)`);

        } else {
          // CASO 2: Paciente sem responsável (adulto)
          const loginPaciente = usuarioNome || generateLogin(nome, codigo);
          const emailPaciente = email || `${loginPaciente}@paciente.com`;

          console.log(`  → Paciente sem responsável: ${nome} (provavelmente adulto)`);

          // Verificar se client já existe
          const existingClient = await prisma.client.findFirst({
            where: {
              OR: [
                { email: emailPaciente },
                { login: loginPaciente }
              ]
            }
          });

          if (existingClient) {
            console.log(`  ⚠️  Cliente já existe: ${emailPaciente}`);
            clientData = existingClient;
          } else {
            // Criar Client para o próprio paciente
            clientData = await prisma.client.create({
              data: {
                login: loginPaciente,
                email: emailPaciente,
                password: hashedPassword,
                active: paciente.ativo !== false,
                createdAt: paciente.dataInclusao ? new Date(paciente.dataInclusao) : new Date(),
                updatedAt: new Date(),
                createdById: defaultCreatorId,
                companyId: company.id
              }
            });
            clientsCreated++;
            console.log(`  ✓ Client criado: ${nome} (próprio paciente)`);
          }

          // Verificar se person já existe
          const existingPerson = await prisma.person.findFirst({
            where: {
              OR: [
                { cpf: paciente.cpf?.toString() },
                { fullName: nome }
              ]
            }
          });

          if (existingPerson) {
            console.log(`  ⚠️  Pessoa já existe: ${nome}`);
            // Verificar se já existe relação ClientPerson
            const existingRelation = await prisma.clientPerson.findFirst({
              where: {
                clientId: clientData.id,
                personId: existingPerson.id
              }
            });

            if (!existingRelation) {
              // Criar relação ClientPerson
              await prisma.clientPerson.create({
                data: {
                  clientId: clientData.id,
                  personId: existingPerson.id,
                  relationship: 'self', // Próprio paciente
                  isPrimary: true
                }
              });
              clientPersonRelationsCreated++;
              console.log(`  ✓ Relação Client-Person criada (próprio paciente)`);
            }
            continue;
          }

          // Criar Person para o paciente
          personData = await prisma.person.create({
            data: {
              email: emailPaciente,
              phone: paciente.celular?.toString() || paciente.fone?.toString() || null,
              address: paciente.logradouro?.toString() || null,
              city: paciente.cidade?.toString() || null,
              state: paciente.estado?.toString() || null,
              birthDate: paciente.dataNascimento ? new Date(paciente.dataNascimento) : null,
              notes: paciente.obs?.toString() || null,
              active: paciente.ativo !== false,
              createdAt: paciente.dataInclusao ? new Date(paciente.dataInclusao) : new Date(),
              updatedAt: new Date(),
              neighborhood: paciente.bairro?.toString() || null,
              postalCode: paciente.cep?.toString() || null,
              cpf: paciente.cpf?.toString() || null,
              createdById: defaultCreatorId,
              fullName: nome,
              gender: paciente.sexo?.toString() || null,
              profileImageUrl: paciente.foto ? `https://app.abamais.com/photos/${paciente.foto}` : null,
              relationship: null,
              useClientEmail: true,
              useClientPhone: true
            }
          });
          personsCreated++;
          console.log(`  ✓ Person criada: ${nome} (próprio paciente)`);

          // Criar relação ClientPerson
          await prisma.clientPerson.create({
            data: {
              clientId: clientData.id,
              personId: personData.id,
              relationship: 'self', // Próprio paciente
              isPrimary: true
            }
          });
          clientPersonRelationsCreated++;
          console.log(`  ✓ Relação Client-Person criada (próprio paciente)`);
        }

        console.log(''); // Linha em branco para separar

      } catch (error) {
        console.error(`❌ Erro ao processar paciente ${paciente.nome}:`, error.message);
        errors++;
        continue;
      }
    }

    // Migrar contatos extras (se existir arquivo)
    if (fs.existsSync('pacientes_schema_contact.csv')) {
      console.log('\n📞 Processando contatos extras...');

      const contatosData = await readCSV('pacientes_schema_contact.csv');

      for (const contato of contatosData) {
        try {
          // Verificar se o person existe pelo personId
          const person = await prisma.person.findUnique({
            where: { id: contato.personId }
          });

          if (!person) {
            console.log(`⚠️  Person não encontrado para contato: ${contato.name}`);
            continue;
          }

          // Criar contato
          await prisma.contact.create({
            data: {
              name: contato.name?.toString() || '',
              relationship: contato.relationship?.toString() || null,
              email: null, // Não vem nos dados
              phone: null, // Não vem nos dados
              notes: null,
              createdAt: new Date(),
              updatedAt: new Date(),
              personId: contato.personId,
              createdById: defaultCreatorId
            }
          });

          contactsCreated++;
          console.log(`✓ Contato criado: ${contato.name} (${contato.relationship})`);

        } catch (error) {
          console.error(`❌ Erro ao criar contato ${contato.name}:`, error.message);
          errors++;
        }
      }
    }

    // Gerar relatório
    console.log('\n📄 Gerando relatório...');
    const report = {
      timestamp: new Date().toISOString(),
      migration: {
        companyId: company.id,
        defaultPassword: DEFAULT_PASSWORD
      },
      statistics: {
        totalPacientes: pacientesData.length,
        clientsCreated: clientsCreated,
        personsCreated: personsCreated,
        clientPersonRelationsCreated: clientPersonRelationsCreated,
        contactsCreated: contactsCreated,
        errors: errors
      },
      warnings: [
        'TODOS OS CLIENTES FORAM CRIADOS COM A SENHA PADRÃO: ' + DEFAULT_PASSWORD,
        'As senhas DEVEM ser alteradas antes do uso em produção',
        'Responsáveis receberam contas Client para fazer login',
        'Crianças estão vinculadas aos responsáveis via tabela ClientPerson',
        'Casos sem responsável viraram Client + Person próprios com relacionamento "self"',
        'Relacionamentos são criados na tabela ClientPerson com isPrimary=true'
      ]
    };

    fs.writeFileSync('relatorio_migracao_pacientes_banco.json', JSON.stringify(report, null, 2));

    console.log('\n🎉 Migração de pacientes concluída!');
    console.log('\n📊 Resumo:');
    console.log(`   • Clientes criados: ${clientsCreated}`);
    console.log(`   • Pessoas criadas: ${personsCreated}`);
    console.log(`   • Relações Client-Person criadas: ${clientPersonRelationsCreated}`);
    console.log(`   • Contatos criados: ${contactsCreated}`);
    console.log(`   • Erros: ${errors}`);

    console.log('\n⚠️  IMPORTANTE:');
    console.log(`   • Senha padrão para todos: ${DEFAULT_PASSWORD}`);
    console.log(`   • ALTERE AS SENHAS antes da produção!`);

  } catch (error) {
    console.error('❌ Erro durante a migração:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Função para limpar dados da migração de pacientes
async function cleanPatientMigration() {
  console.log('🧹 Limpando dados da migração de pacientes...');

  try {
    const company = await findMigrationCompany();

    // Deletar contatos
    const deletedContacts = await prisma.contact.deleteMany({
      where: {
        person: {
          clientPersons: {
            some: {
              client: {
                companyId: company.id
              }
            }
          }
        }
      }
    });
    console.log(`✓ ${deletedContacts.count} contatos deletados`);

    // Deletar relações ClientPerson
    const deletedClientPersons = await prisma.clientPerson.deleteMany({
      where: {
        client: {
          companyId: company.id
        }
      }
    });
    console.log(`✓ ${deletedClientPersons.count} relações Client-Person deletadas`);

    // Deletar pessoas
    const deletedPersons = await prisma.person.deleteMany({
      where: {
        clientPersons: {
          some: {
            client: {
              companyId: company.id
            }
          }
        }
      }
    });
    console.log(`✓ ${deletedPersons.count} pessoas deletadas`);

    // Deletar clientes
    const deletedClients = await prisma.client.deleteMany({
      where: { companyId: company.id }
    });
    console.log(`✓ ${deletedClients.count} clientes deletados`);

    console.log('🧹 Limpeza de pacientes concluída!');
  } catch (error) {
    console.error('Erro na limpeza:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script
if (require.main === module) {
  const command = process.argv[2];

  if (command === 'clean') {
    cleanPatientMigration().catch(console.error);
  } else {
    main().catch(console.error);
  }
}

module.exports = { main, cleanPatientMigration };