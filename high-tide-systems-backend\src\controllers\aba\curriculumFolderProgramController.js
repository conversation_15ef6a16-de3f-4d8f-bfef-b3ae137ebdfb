// src/controllers/aba/curriculumFolderProgramController.js
const { PrismaClient } = require('@prisma/client');
const { body, validationResult } = require('express-validator');
const prisma = new PrismaClient();

// Validação para criação de programa em pasta curricular
const createCurriculumFolderProgramValidation = [
  body('name').notEmpty().withMessage('O nome do programa é obrigatório'),
  body('type').notEmpty().withMessage('O tipo do programa é obrigatório'),
  body('status').optional(),
  body('originalProgramId').optional(),
];

// Validação para adicionar programa existente à pasta curricular
const addExistingProgramValidation = [
  body('programId').notEmpty().withMessage('O ID do programa é obrigatório'),
  body('status').optional(),
];

class CurriculumFolderProgramController {
  /**
   * Lista todos os programas de uma pasta curricular
   */
  static async list(req, res) {
    try {
      const { folderId } = req.params;
      const { page = 1, limit = 50, search = "", status } = req.query;

      // Verificar se a pasta curricular existe
      const folder = await prisma.curriculumFolder.findUnique({
        where: { id: folderId }
      });

      if (!folder) {
        return res.status(404).json({ message: 'Pasta curricular não encontrada' });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && folder.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para acessar esta pasta curricular' });
      }

      // Verificar se o modelo CurriculumFolderProgram existe
      try {
        // Tentar acessar o modelo para verificar se ele existe
        await prisma.curriculumFolderProgram.findFirst();
      } catch (modelError) {
        // Se o modelo não existir, retornar uma lista vazia com paginação
        console.log("Modelo CurriculumFolderProgram ainda não existe no banco de dados");
        return res.json({
          programs: [],
          pagination: {
            total: 0,
            page: Number(page),
            limit: Number(limit),
            pages: 0
          }
        });
      }

      // Construir filtro de busca
      let where = {
        curriculumFolderId: folderId,
        active: true,
      };

      // Filtro por status
      if (status) {
        where.status = status;
      }

      // Filtro de busca
      if (search) {
        where = {
          ...where,
          OR: [
            { name: { contains: search, mode: "insensitive" } },
            { type: { contains: search, mode: "insensitive" } },
          ]
        };
      }

      // Contar total de registros
      const total = await prisma.curriculumFolderProgram.count({ where });

      // Buscar programas com paginação
      const programs = await prisma.curriculumFolderProgram.findMany({
        where,
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true
            }
          }
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      res.json({
        programs,
        pagination: {
          total,
          page: Number(page),
          limit: Number(limit),
          pages: Math.ceil(total / Number(limit))
        }
      });
    } catch (error) {
      console.error("Erro ao listar programas da pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Cria um novo programa em uma pasta curricular
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { folderId } = req.params;
      const {
        name,
        type,
        protocol,
        skill,
        milestone,
        teachingType,
        targetsPerSession,
        attemptsPerTarget,
        teachingProcedure,
        instruction,
        objective,
        promptStep,
        correctionProcedure,
        learningCriteria,
        materials,
        notes,
        status = 'unallocated',
        originalProgramId
      } = req.body;

      // Verificar se a pasta curricular existe
      const folder = await prisma.curriculumFolder.findUnique({
        where: { id: folderId }
      });

      if (!folder) {
        return res.status(404).json({ message: 'Pasta curricular não encontrada' });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && folder.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para acessar esta pasta curricular' });
      }

      // Verificar se o modelo CurriculumFolderProgram existe
      try {
        // Tentar acessar o modelo para verificar se ele existe
        await prisma.curriculumFolderProgram.findFirst();
      } catch (modelError) {
        // Se o modelo não existir, retornar uma mensagem informativa
        console.log("Modelo CurriculumFolderProgram ainda não existe no banco de dados");
        return res.status(503).json({
          message: "Funcionalidade temporariamente indisponível. É necessário executar a migração do banco de dados.",
          details: "Execute a migração do banco de dados para criar o modelo CurriculumFolderProgram."
        });
      }

      // Criar programa na pasta curricular
      const program = await prisma.curriculumFolderProgram.create({
        data: {
          name,
          type,
          protocol,
          skill,
          milestone,
          teachingType,
          targetsPerSession: targetsPerSession || 1,
          attemptsPerTarget: attemptsPerTarget || 1,
          teachingProcedure: teachingProcedure || "",
          instruction: instruction || "",
          objective: objective || "",
          promptStep: promptStep || "",
          correctionProcedure: correctionProcedure || "",
          learningCriteria: learningCriteria || "",
          materials: materials || "",
          notes: notes || "",
          status,
          originalProgramId,
          curriculumFolderId: folderId,
          companyId: req.user.companyId,
          createdById: req.user.id,
        },
      });

      res.status(201).json(program);
    } catch (error) {
      console.error("Erro ao criar programa na pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém um programa específico de uma pasta curricular
   */
  static async get(req, res) {
    try {
      const { folderId, programId } = req.params;

      // Verificar se o modelo CurriculumFolderProgram existe
      try {
        // Tentar acessar o modelo para verificar se ele existe
        await prisma.curriculumFolderProgram.findFirst();
      } catch (modelError) {
        // Se o modelo não existir, retornar uma mensagem informativa
        console.log("Modelo CurriculumFolderProgram ainda não existe no banco de dados");
        return res.status(503).json({
          message: "Funcionalidade temporariamente indisponível. É necessário executar a migração do banco de dados.",
          details: "Execute a migração do banco de dados para criar o modelo CurriculumFolderProgram."
        });
      }

      // Buscar programa
      const program = await prisma.curriculumFolderProgram.findFirst({
        where: {
          id: programId,
          curriculumFolderId: folderId
        },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      if (!program) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && program.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para acessar este programa" });
      }

      res.status(200).json(program);
    } catch (error) {
      console.error("Erro ao buscar programa da pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza um programa de uma pasta curricular
   */
  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { folderId, programId } = req.params;
      const {
        name,
        type,
        protocol,
        skill,
        milestone,
        teachingType,
        targetsPerSession,
        attemptsPerTarget,
        teachingProcedure,
        instruction,
        objective,
        promptStep,
        correctionProcedure,
        learningCriteria,
        materials,
        notes,
        status
      } = req.body;

      // Verificar se o modelo CurriculumFolderProgram existe
      try {
        // Tentar acessar o modelo para verificar se ele existe
        await prisma.curriculumFolderProgram.findFirst();
      } catch (modelError) {
        // Se o modelo não existir, retornar uma mensagem informativa
        console.log("Modelo CurriculumFolderProgram ainda não existe no banco de dados");
        return res.status(503).json({
          message: "Funcionalidade temporariamente indisponível. É necessário executar a migração do banco de dados.",
          details: "Execute a migração do banco de dados para criar o modelo CurriculumFolderProgram."
        });
      }

      // Verificar se o programa existe
      const existingProgram = await prisma.curriculumFolderProgram.findFirst({
        where: {
          id: programId,
          curriculumFolderId: folderId
        }
      });

      if (!existingProgram) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingProgram.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para editar este programa" });
      }

      // Atualizar programa
      const updatedProgram = await prisma.curriculumFolderProgram.update({
        where: { id: programId },
        data: {
          name: name !== undefined ? name : existingProgram.name,
          type: type !== undefined ? type : existingProgram.type,
          protocol: protocol !== undefined ? protocol : existingProgram.protocol,
          skill: skill !== undefined ? skill : existingProgram.skill,
          milestone: milestone !== undefined ? milestone : existingProgram.milestone,
          teachingType: teachingType !== undefined ? teachingType : existingProgram.teachingType,
          targetsPerSession: targetsPerSession !== undefined ? targetsPerSession : existingProgram.targetsPerSession,
          attemptsPerTarget: attemptsPerTarget !== undefined ? attemptsPerTarget : existingProgram.attemptsPerTarget,
          teachingProcedure: teachingProcedure !== undefined ? teachingProcedure : existingProgram.teachingProcedure,
          instruction: instruction !== undefined ? instruction : existingProgram.instruction,
          objective: objective !== undefined ? objective : existingProgram.objective,
          promptStep: promptStep !== undefined ? promptStep : existingProgram.promptStep,
          correctionProcedure: correctionProcedure !== undefined ? correctionProcedure : existingProgram.correctionProcedure,
          learningCriteria: learningCriteria !== undefined ? learningCriteria : existingProgram.learningCriteria,
          materials: materials !== undefined ? materials : existingProgram.materials,
          notes: notes !== undefined ? notes : existingProgram.notes,
          status: status !== undefined ? status : existingProgram.status,
          updatedAt: new Date()
        }
      });

      res.json(updatedProgram);
    } catch (error) {
      console.error("Erro ao atualizar programa da pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Adiciona um programa existente do catálogo à pasta curricular
   */
  static async addExistingProgram(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { folderId } = req.params;
      const { programId, status = 'unallocated' } = req.body;

      // Verificar se a pasta curricular existe
      const folder = await prisma.curriculumFolder.findUnique({
        where: { id: folderId }
      });

      if (!folder) {
        return res.status(404).json({ message: 'Pasta curricular não encontrada' });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && folder.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para acessar esta pasta curricular' });
      }

      // Verificar se o modelo CurriculumFolderProgram existe
      try {
        // Tentar acessar o modelo para verificar se ele existe
        await prisma.curriculumFolderProgram.findFirst();
      } catch (modelError) {
        // Se o modelo não existir, retornar uma mensagem informativa
        console.log("Modelo CurriculumFolderProgram ainda não existe no banco de dados");
        return res.status(503).json({
          message: "Funcionalidade temporariamente indisponível. É necessário executar a migração do banco de dados.",
          details: "Execute a migração do banco de dados para criar o modelo CurriculumFolderProgram."
        });
      }

      // Buscar o programa original no catálogo
      const originalProgram = await prisma.program.findUnique({
        where: { id: programId }
      });

      if (!originalProgram) {
        return res.status(404).json({ message: 'Programa original não encontrado' });
      }

      // Verificar permissão para o programa original
      if (req.user.role !== 'SYSTEM_ADMIN' && originalProgram.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para acessar este programa' });
      }

      // Criar uma cópia do programa na pasta curricular
      const program = await prisma.curriculumFolderProgram.create({
        data: {
          name: originalProgram.name,
          type: originalProgram.type,
          protocol: originalProgram.protocol,
          skill: originalProgram.skill,
          milestone: originalProgram.milestone,
          teachingType: originalProgram.teachingType,
          targetsPerSession: originalProgram.targetsPerSession || 1,
          attemptsPerTarget: originalProgram.attemptsPerTarget || 1,
          teachingProcedure: originalProgram.teachingProcedure || "",
          instruction: originalProgram.instruction || "",
          objective: originalProgram.objective || "",
          promptStep: originalProgram.promptStep || "",
          correctionProcedure: originalProgram.correctionProcedure || "",
          learningCriteria: originalProgram.learningCriteria || "",
          materials: originalProgram.materials || "",
          notes: originalProgram.notes || "",
          status,
          originalProgramId: programId,
          curriculumFolderId: folderId,
          companyId: req.user.companyId,
          createdById: req.user.id,
        },
      });

      res.status(201).json(program);
    } catch (error) {
      console.error("Erro ao adicionar programa existente à pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Exclui um programa de uma pasta curricular
   */
  static async delete(req, res) {
    try {
      const { folderId, programId } = req.params;

      // Verificar se o modelo CurriculumFolderProgram existe
      try {
        // Tentar acessar o modelo para verificar se ele existe
        await prisma.curriculumFolderProgram.findFirst();
      } catch (modelError) {
        // Se o modelo não existir, retornar uma mensagem informativa
        console.log("Modelo CurriculumFolderProgram ainda não existe no banco de dados");
        return res.status(503).json({
          message: "Funcionalidade temporariamente indisponível. É necessário executar a migração do banco de dados.",
          details: "Execute a migração do banco de dados para criar o modelo CurriculumFolderProgram."
        });
      }

      // Verificar se o programa existe
      const existingProgram = await prisma.curriculumFolderProgram.findFirst({
        where: {
          id: programId,
          curriculumFolderId: folderId
        }
      });

      if (!existingProgram) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingProgram.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para excluir este programa" });
      }

      // Soft delete do programa
      await prisma.curriculumFolderProgram.update({
        where: { id: programId },
        data: {
          active: false,
          deletedAt: new Date()
        }
      });

      res.json({ message: "Programa excluído com sucesso" });
    } catch (error) {
      console.error("Erro ao excluir programa da pasta curricular:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

// Exportar o controlador e as validações
module.exports = {
  CurriculumFolderProgramController,
  createCurriculumFolderProgramValidation,
  addExistingProgramValidation
};
