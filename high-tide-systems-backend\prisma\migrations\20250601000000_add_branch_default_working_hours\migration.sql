-- Add defaultWorkingHours field to Branch model
ALTER TABLE "Branch" ADD COLUMN "defaultWorkingHours" JSONB;

-- Add branchId field to User model
ALTER TABLE "User" ADD COLUMN "branchId" TEXT;

-- Add foreign key constraint
ALTER TABLE "User" ADD CONSTRAINT "User_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- Add index for branchId
CREATE INDEX "User_branchId_idx" ON "User"("branchId");
