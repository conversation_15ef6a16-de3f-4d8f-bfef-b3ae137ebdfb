"use client";

import React, { useState, useEffect } from "react";
import { BookOpen, Target, Settings, Info, Plus, Edit, Trash, FileText, Upload } from "lucide-react";
import {
  ModuleModal,
  ModuleInput,
  ModuleSelect,
  ModuleTextarea,
  ModuleFormGroup,
} from "@/components/ui";
import ModuleTabs from "@/components/ui/ModuleTabs";
import { useToast } from "@/contexts/ToastContext";

const ProgramFormModal = ({ isOpen, onClose, onSave, program }) => {
  // Hook para exibir notificações toast
  const { toast_success, toast_info, toast_error } = useToast();
  // Opções para o campo de protocolo
  const protocolOptions = [
    "IAR",
    "SPM -P 2- 5 ANOS",
    "CARS2 - ST - Childhood Autism Rating Scale, 2nd Edition - Standard Form",
    "CARS HF - Childhood Autism Rating Scale - High Functioning",
    "PDI- AVALIAÇÃO DE DESEMPENHO FUNCIONÁRIO",
    "COPM - MEDIDA CANADENSE DE DESEMPENHO OCUPACIONAL (COPM)"
  ];

  // Opções para o campo de habilidade
  const skillOptions = [
    "MANDO",
    "TATO",
    "OUVINTE",
    "PERCEPÇÃO VISUAL",
    "BRINCAR INDEPENDENTE",
    "BRINCAR SOCIAL",
    "IMITAÇÃO MOTORA",
    "ECÓICO",
    "COMPORTAMENTO VOCAL ESPONTÂNEO",
    "CATEGORIZAÇÃO (CARACTERÍSTICA, FUNÇÃO, CLASSSE)",
    "INTRAVERBAL",
    "HABILIDADES DE ROTINA DE CLASSE E EM GRUPO",
    "ASPECTOS DA LINGUAGEM",
    "LEITURA",
    "ESCRITA",
    "MATEMÁTICA"
  ];

  // Opções para o campo de tipo de ensino
  const teachingTypeOptions = [
    "Tentativa Discreta - Estruturada",
    "Análise de Tarefas",
    "Ensino Naturalístico",
    "Tentativa Discreta - Intercalada",
    "Frequência",
    "Duração"
  ];

  // Estado para os dados do formulário
  const [formData, setFormData] = useState({
    type: "PROGRAM_CATALOG",
    name: "",
    protocol: "",
    skill: "",
    milestone: "",
    teachingType: "",
    targetsPerSession: 1,
    attemptsPerTarget: 1,
    teachingProcedure: "",
    instruction: "",
    objective: "",
    promptStep: "",       // Novo campo para Passo da dica
    correctionProcedure: "",
    learningCriteria: "",
    materials: "",
    notes: ""
  });

  // Estado para erros de validação
  const [errors, setErrors] = useState({});

  // Estado para a aba ativa
  const [activeTab, setActiveTab] = useState("general");

  // Verificar se deve mostrar a aba de instruções para aplicação
  const shouldShowApplicationTab = !["Frequência", "Duração"].includes(formData.teachingType);

  // Estado para os alvos do programa
  const [targets, setTargets] = useState([]);

  // Estado para o formulário de novo alvo
  const [targetForm, setTargetForm] = useState({
    target: "",
    order: 1,
    group: ""
  });

  // Estado para erros do formulário de alvo
  const [targetErrors, setTargetErrors] = useState({});

  // Estado para os arquivos do programa
  const [files, setFiles] = useState([]);
  const [isUploading, setIsUploading] = useState(false);

  // Função para fazer upload de arquivos
  const handleFileUpload = (e) => {
    const fileList = e.target.files;
    if (!fileList || fileList.length === 0) return;

    setIsUploading(true);

    // Simulando o upload de arquivos (em um ambiente real, isso seria uma chamada à API)
    const newFiles = Array.from(fileList).map((file, index) => {
      // Extrair a extensão do arquivo
      const extension = file.name.split('.').pop();

      // Calcular o tamanho em KB ou MB
      let fileSize;
      if (file.size < 1024 * 1024) {
        fileSize = `${(file.size / 1024).toFixed(2)} KB`;
      } else {
        fileSize = `${(file.size / (1024 * 1024)).toFixed(2)} MB`;
      }

      return {
        id: `temp-${Date.now()}-${index}`,
        name: file.name.split('.')[0], // Nome sem extensão
        extension: extension,
        size: fileSize,
        createdAt: new Date().toISOString(),
        file: file // Manter referência ao arquivo para upload real
      };
    });

    // Adicionar os novos arquivos ao estado
    setFiles(prevFiles => [...prevFiles, ...newFiles]);
    setIsUploading(false);

    // Exibir toast de sucesso
    if (newFiles.length === 1) {
      toast_success(`Arquivo ${newFiles[0].name}.${newFiles[0].extension} adicionado com sucesso!`);
    } else {
      toast_success(`${newFiles.length} arquivos adicionados com sucesso!`);
    }

    // Limpar o input de arquivo
    e.target.value = null;
  };

  // Função para remover um arquivo
  const handleRemoveFile = (fileId) => {
    // Encontrar o arquivo para exibir o nome na mensagem de sucesso
    const fileToRemove = files.find(file => file.id === fileId);

    // Remover o arquivo da lista
    setFiles(prevFiles => prevFiles.filter(file => file.id !== fileId));

    // Exibir toast de sucesso
    if (fileToRemove) {
      toast_success(`Arquivo ${fileToRemove.name}.${fileToRemove.extension} removido com sucesso!`);
    }
  };

  // Função para fazer download de um arquivo
  const handleDownloadFile = (file) => {
    // Se tivermos o arquivo real (para arquivos recém-carregados)
    if (file.file) {
      const url = URL.createObjectURL(file.file);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${file.name}.${file.extension}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Exibir toast de sucesso
      toast_success(`Arquivo ${file.name}.${file.extension} baixado com sucesso!`);
    } else {
      // Para arquivos simulados ou que viriam do servidor
      // Em um ambiente real, isso seria uma chamada à API para obter o arquivo
      toast_info(`Iniciando download de ${file.name}.${file.extension}`);

      // Simulação de download para demonstração
      setTimeout(() => {
        toast_success(`Arquivo ${file.name}.${file.extension} baixado com sucesso!`);
      }, 1500);
    }
  };

  // Função para formatar a data
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Carregar dados do programa se estiver editando
  useEffect(() => {
    if (program) {
      setFormData({
        type: program.type || "PROGRAM_CATALOG",
        name: program.name || "",
        protocol: program.protocol || "",
        skill: program.skill || "",
        milestone: program.milestone || "",
        teachingType: program.teachingType || "",
        targetsPerSession: program.targetsPerSession || 1,
        attemptsPerTarget: program.attemptsPerTarget || 1,
        teachingProcedure: program.teachingProcedure || "",
        instruction: program.instruction || "",
        objective: program.objective || "",
        promptStep: program.promptStep || "",       // Novo campo para Passo da dica
        correctionProcedure: program.correctionProcedure || "",
        learningCriteria: program.learningCriteria || "",
        materials: program.materials || "",
        notes: program.notes || ""
      });

      // Se o programa tem alvos, carregá-los
      if (program.targets) {
        setTargets(program.targets);
      }

      // Se o programa tem arquivos, carregá-los (simulado)
      if (program.id) {
        // Em um ambiente real, isso seria uma chamada à API
        // Simulando alguns arquivos para demonstração
        const mockFiles = [
          {
            id: '1',
            name: 'Documento de Referência',
            extension: 'pdf',
            size: '2.5 MB',
            createdAt: '2023-05-15T10:30:00Z'
          },
          {
            id: '2',
            name: 'Planilha de Acompanhamento',
            extension: 'xlsx',
            size: '1.2 MB',
            createdAt: '2023-05-16T14:45:00Z'
          }
        ];
        setFiles(mockFiles);
      }
    }
  }, [program]);

  // Manipulador de mudança nos campos do formulário
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Limpar erro do campo quando ele for alterado
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: null }));
    }

    // Se o campo alterado for o tipo de ensino e o valor for "Frequência" ou "Duração"
    // e a aba atual for "application", mudar para a aba "general"
    if (name === "teachingType" && ["Frequência", "Duração"].includes(value) && activeTab === "application") {
      setActiveTab("general");
    }
  };

  // Manipulador de mudança nos campos do formulário de alvo
  const handleTargetChange = (e) => {
    const { name, value } = e.target;
    setTargetForm((prev) => ({ ...prev, [name]: value }));

    // Limpar erro do campo quando ele for alterado
    if (targetErrors[name]) {
      setTargetErrors((prev) => ({ ...prev, [name]: null }));
    }
  };

  // Validar formulário de alvo
  const validateTargetForm = () => {
    const newErrors = {};

    if (!targetForm.target.trim()) {
      newErrors.target = "O alvo é obrigatório";
    }

    if (!targetForm.order || isNaN(targetForm.order) || targetForm.order < 1) {
      newErrors.order = "A ordem deve ser um número maior que zero";
    }

    setTargetErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Adicionar novo alvo
  const handleAddTarget = () => {
    if (validateTargetForm()) {
      const newTarget = {
        id: `temp-${Date.now()}`, // ID temporário
        target: targetForm.target,
        order: parseInt(targetForm.order),
        group: targetForm.group,
        situation: "ACTIVE"
      };

      // Adicionar o novo alvo à lista
      setTargets((prev) => [...prev, newTarget]);

      // Limpar o formulário
      setTargetForm({
        target: "",
        order: Math.max(...targets.map(t => t.order), 0) + 1, // Próxima ordem
        group: ""
      });

      // Exibir toast de sucesso
      toast_success("Alvo adicionado com sucesso!");
    }
  };

  // Remover alvo
  const handleRemoveTarget = (targetId) => {
    setTargets((prev) => prev.filter((target) => target.id !== targetId));
    toast_success("Alvo removido com sucesso!");
  };

  // Validar formulário
  const validateForm = () => {
    const newErrors = {};

    if (!formData.type) {
      newErrors.type = "O tipo do programa é obrigatório";
    }

    if (!formData.name.trim()) {
      newErrors.name = "O nome do programa é obrigatório";
    }

    if (formData.targetsPerSession && (isNaN(formData.targetsPerSession) || formData.targetsPerSession < 1)) {
      newErrors.targetsPerSession = "Deve ser um número maior que zero";
    }

    if (formData.attemptsPerTarget && (isNaN(formData.attemptsPerTarget) || formData.attemptsPerTarget < 1)) {
      newErrors.attemptsPerTarget = "Deve ser um número maior que zero";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Manipulador de envio do formulário
  const handleSubmit = (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Converter valores numéricos
      const formattedData = {
        ...formData,
        targetsPerSession: formData.targetsPerSession ? parseInt(formData.targetsPerSession) : 1,
        attemptsPerTarget: formData.attemptsPerTarget ? parseInt(formData.attemptsPerTarget) : 1,
        targets: targets // Incluir os alvos no objeto enviado
      };

      onSave(formattedData);
    }
  };

  // Renderizar botões do rodapé
  const renderFooter = () => (
    <div className="flex justify-end space-x-2">
      <button
        type="button"
        onClick={onClose}
        className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
      >
        Cancelar
      </button>
      <button
        type="button"
        onClick={handleSubmit}
        className="px-4 py-2 bg-module-abaplus-primary hover:bg-module-abaplus-primary-dark text-white rounded-md"
      >
        {program ? "Atualizar" : "Criar"}
      </button>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={program ? "Editar Programa" : "Novo Programa"}
      size="lg"
      moduleColor="abaplus"
      icon={<BookOpen size={20} />}
      footer={renderFooter()}
    >
      <div className="p-6 space-y-6">
        <ModuleTabs
          tabs={[
            { id: "general", label: "Geral", icon: <Info size={16} /> },
            ...(shouldShowApplicationTab
              ? [{ id: "application", label: "Inst. para aplicação", icon: <Settings size={16} /> }]
              : []),
            { id: "targets", label: "Alvos/Estímulos", icon: <Target size={16} /> },
            { id: "files", label: "Arquivos", icon: <FileText size={16} /> },
          ]}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          moduleColor="abaplus"
        />

        <div className="mt-6">
          {/* Aba Geral */}
          {activeTab === "general" && (
            <div className="space-y-4">
              <ModuleFormGroup label="Tipo de Programa *" error={errors.type}>
                <ModuleSelect
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  moduleColor="abaplus"
                  error={!!errors.type}
                  errorMessage={errors.type}
                >
                  <option value="PROGRAM_CATALOG">Catálogo de Programas</option>
                  <option value="LEARNING_PROGRAM">Programa de Aprendizagem</option>
                </ModuleSelect>
              </ModuleFormGroup>

              <ModuleFormGroup label="Nome *" error={errors.name}>
                <ModuleInput
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Nome do programa"
                  moduleColor="abaplus"
                  error={!!errors.name}
                  errorMessage={errors.name}
                />
              </ModuleFormGroup>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModuleFormGroup label="Protocolo">
                  <ModuleSelect
                    name="protocol"
                    value={formData.protocol}
                    onChange={handleChange}
                    moduleColor="abaplus"
                  >
                    <option value="">Selecione um protocolo</option>
                    {protocolOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </ModuleSelect>
                </ModuleFormGroup>

                <ModuleFormGroup label="Habilidade">
                  <ModuleSelect
                    name="skill"
                    value={formData.skill}
                    onChange={handleChange}
                    moduleColor="abaplus"
                  >
                    <option value="">Selecione uma habilidade</option>
                    {skillOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </ModuleSelect>
                </ModuleFormGroup>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModuleFormGroup label="Marco">
                  <ModuleInput
                    name="milestone"
                    value={formData.milestone}
                    onChange={handleChange}
                    placeholder="Marco"
                    moduleColor="abaplus"
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Tipo de Ensino">
                  <ModuleSelect
                    name="teachingType"
                    value={formData.teachingType}
                    onChange={handleChange}
                    moduleColor="abaplus"
                  >
                    <option value="">Selecione um tipo de ensino</option>
                    {teachingTypeOptions.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </ModuleSelect>
                </ModuleFormGroup>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <ModuleFormGroup label="Alvos por Sessão" error={errors.targetsPerSession}>
                  <ModuleInput
                    name="targetsPerSession"
                    value={formData.targetsPerSession}
                    onChange={handleChange}
                    placeholder="Quantidade de alvos por sessão"
                    moduleColor="abaplus"
                    type="number"
                    min="1"
                    error={!!errors.targetsPerSession}
                    errorMessage={errors.targetsPerSession}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Tentativas por Alvo" error={errors.attemptsPerTarget}>
                  <ModuleInput
                    name="attemptsPerTarget"
                    value={formData.attemptsPerTarget}
                    onChange={handleChange}
                    placeholder="Quantidade de tentativas por alvo"
                    moduleColor="abaplus"
                    type="number"
                    min="1"
                    error={!!errors.attemptsPerTarget}
                    errorMessage={errors.attemptsPerTarget}
                  />
                </ModuleFormGroup>
              </div>


            </div>
          )}

          {/* Aba Instruções para Aplicação */}
          {activeTab === "application" && (
            <div className="space-y-6">
              <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Detalhes da Aplicação
                </h3>

                <ModuleFormGroup label="Procedimento de Ensino">
                  <ModuleTextarea
                    name="teachingProcedure"
                    value={formData.teachingProcedure}
                    onChange={handleChange}
                    placeholder="Descreva o procedimento de ensino"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Instrução (Sd)">
                  <ModuleTextarea
                    name="instruction"
                    value={formData.instruction}
                    onChange={handleChange}
                    placeholder="Descreva a instrução (Sd) para aplicação"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Objetivo">
                  <ModuleTextarea
                    name="objective"
                    value={formData.objective}
                    onChange={handleChange}
                    placeholder="Descreva o objetivo do programa"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Passo da dica">
                  <ModuleTextarea
                    name="promptStep"
                    value={formData.promptStep}
                    onChange={handleChange}
                    placeholder="Descreva o passo da dica"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Procedimento de Correção">
                  <ModuleTextarea
                    name="correctionProcedure"
                    value={formData.correctionProcedure}
                    onChange={handleChange}
                    placeholder="Descreva o procedimento de correção"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Critério de Aprendizagem">
                  <ModuleTextarea
                    name="learningCriteria"
                    value={formData.learningCriteria}
                    onChange={handleChange}
                    placeholder="Descreva o critério de aprendizagem"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Materiais utilizados">
                  <ModuleTextarea
                    name="materials"
                    value={formData.materials}
                    onChange={handleChange}
                    placeholder="Descreva os materiais necessários"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Observações">
                  <ModuleTextarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleChange}
                    placeholder="Observações adicionais"
                    moduleColor="abaplus"
                    rows={4}
                    maxLength={4000}
                  />
                </ModuleFormGroup>
              </div>
            </div>
          )}

          {/* Aba Alvos/Estímulos */}
          {activeTab === "targets" && (
            <div className="space-y-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                  Adicionar Novo Alvo
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <ModuleFormGroup label="Alvo *" error={targetErrors.target}>
                    <ModuleInput
                      name="target"
                      value={targetForm.target}
                      onChange={handleTargetChange}
                      placeholder="Nome do alvo"
                      moduleColor="abaplus"
                      error={!!targetErrors.target}
                      errorMessage={targetErrors.target}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Ordem *" error={targetErrors.order}>
                    <ModuleInput
                      name="order"
                      value={targetForm.order}
                      onChange={handleTargetChange}
                      placeholder="Ordem numérica"
                      moduleColor="abaplus"
                      type="number"
                      min="1"
                      error={!!targetErrors.order}
                      errorMessage={targetErrors.order}
                    />
                  </ModuleFormGroup>

                  <ModuleFormGroup label="Grupo">
                    <ModuleInput
                      name="group"
                      value={targetForm.group}
                      onChange={handleTargetChange}
                      placeholder="Grupo (opcional)"
                      moduleColor="abaplus"
                    />
                  </ModuleFormGroup>
                </div>
                <div className="mt-4 flex justify-end">
                  <button
                    type="button"
                    className="px-4 py-2 bg-module-abaplus-primary hover:bg-module-abaplus-primary-dark text-white rounded-md flex items-center"
                    onClick={handleAddTarget}
                  >
                    <Plus size={16} className="mr-2" />
                    Adicionar Alvo
                  </button>
                </div>
              </div>

              <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Alvos do Programa
                  </h3>
                </div>

                {targets.length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-800">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Ordem
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Alvo
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Grupo
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Situação
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                            Ações
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                        {targets.map((target) => (
                          <tr key={target.id}>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {target.order}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                              {target.target}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {target.group || "-"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {target.situation === "ACTIVE" ? (
                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-400">
                                  Ativo
                                </span>
                              ) : (
                                <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800/30 dark:text-red-400">
                                  Inativo
                                </span>
                              )}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <button
                                type="button"
                                className="text-red-500 hover:text-red-600"
                                onClick={() => handleRemoveTarget(target.id)}
                                title="Remover alvo"
                              >
                                <Trash size={16} />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Target size={40} className="mx-auto text-gray-400 dark:text-gray-600 mb-2" />
                    <p className="text-gray-500 dark:text-gray-400">
                      Nenhum alvo cadastrado para este programa.
                    </p>
                    <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                      Use o formulário acima para adicionar alvos.
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Aba Arquivos */}
          {activeTab === "files" && (
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  Arquivos do Programa
                </h3>
                <label
                  htmlFor="file-upload"
                  className="px-3 py-1.5 bg-module-abaplus-primary hover:bg-module-abaplus-primary-dark text-white rounded-md flex items-center text-sm cursor-pointer"
                >
                  <Upload size={16} className="mr-1" />
                  Adicionar Arquivo
                  <input
                    id="file-upload"
                    type="file"
                    className="hidden"
                    onChange={handleFileUpload}
                    multiple
                  />
                </label>
              </div>

              {isUploading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-teal-500 mx-auto"></div>
                  <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">Enviando arquivos...</p>
                </div>
              ) : (
                <>
                  {files.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              ID
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Data Registro
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Nome
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Extensão
                            </th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Tamanho
                            </th>
                            <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                              Ações
                            </th>
                          </tr>
                        </thead>
                        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                          {files.map((file) => (
                            <tr key={file.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {file.id.substring(0, 8)}...
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {formatDate(file.createdAt)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                                {file.name}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {file.extension.toUpperCase()}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {file.size}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button
                                  type="button"
                                  className="text-blue-500 hover:text-blue-600 mr-3"
                                  onClick={() => handleDownloadFile(file)}
                                  title={`Baixar ${file.name}.${file.extension}`}
                                >
                                  <FileText size={16} />
                                </button>
                                <button
                                  type="button"
                                  className="text-red-500 hover:text-red-600"
                                  onClick={() => handleRemoveFile(file.id)}
                                >
                                  <Trash size={16} />
                                </button>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <FileText size={40} className="mx-auto text-gray-400 dark:text-gray-600 mb-2" />
                      <p className="text-gray-500 dark:text-gray-400">
                        Nenhum arquivo anexado a este programa.
                      </p>
                      <p className="text-gray-500 dark:text-gray-400 text-sm mt-1">
                        Clique em "Adicionar Arquivo" para começar.
                      </p>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </ModuleModal>
  );
};

export default ProgramFormModal;
