const prisma = require("../../utils/prisma");
const AvailabilityHelper = require("../../utils/availabilityHelper");
const emailService = require("../../services/emailService");

class SchedulingController {
  // Método para verificar disponibilidade de horário
  static async checkAvailability(req, res) {
    try {
      const { providerId, startDate, endDate, excludeId } = req.body;

      // Validar parâmetros obrigatórios
      if (!providerId || !startDate || !endDate) {
        return res.status(400).json({
          message: "Parâmetros obrigatórios faltando: providerId, startDate, endDate",
          available: false
        });
      }

      // Verificar disponibilidade usando o AvailabilityHelper
      const availability = await AvailabilityHelper.checkProviderAvailability(
        providerId,
        startDate,
        endDate,
        excludeId
      );

      // Se houver um ID para excluir (caso de edição), verificar se o conflito é com o próprio agendamento
      if (!availability.available && availability.reason === 'CONFLICT' && excludeId) {
        if (availability.conflictData.id === excludeId) {
          // Se o conflito é com o próprio agendamento sendo editado, considerar disponível
          return res.status(200).json({
            available: true,
            message: "Horário disponível (excluindo o próprio agendamento)"
          });
        }
      }

      // Retornar o resultado da verificação
      return res.status(availability.available ? 200 : 409).json({
        available: availability.available,
        reason: availability.reason,
        conflict: availability.conflictData,
        message: availability.available
          ? "Horário disponível"
          : availability.reason === 'CONFLICT'
            ? "Horário indisponível: conflito com agendamento existente"
            : availability.reason === 'HOLIDAY'
              ? "Horário indisponível: feriado"
              : availability.reason === 'BREAK_TIME'
                ? "Horário indisponível: intervalo de almoço/descanso"
                : availability.reason === 'OUTSIDE_WORKING_HOURS'
                  ? "Horário indisponível: fora do horário de trabalho"
                  : "Horário indisponível"
      });
    } catch (error) {
      console.error("Erro ao verificar disponibilidade:", error);
      return res.status(500).json({
        available: false,
        message: "Erro ao verificar disponibilidade",
        error: error.message
      });
    }
  }

  // Método para verificar disponibilidade de horário para um paciente
  static async checkPatientAvailability(req, res) {
    try {
      const { personId, startDate, endDate, excludeId } = req.body;

      // Validar parâmetros obrigatórios
      if (!personId || !startDate || !endDate) {
        return res.status(400).json({
          message: "Parâmetros obrigatórios faltando: personId, startDate, endDate",
          available: false
        });
      }

      // Verificar disponibilidade do paciente
      const availability = await AvailabilityHelper.checkPatientAvailability(
        personId,
        startDate,
        endDate,
        excludeId
      );

      // Se houver um ID para excluir (caso de edição), verificar se o conflito é com o próprio agendamento
      if (!availability.available && availability.reason === 'PATIENT_CONFLICT' && excludeId) {
        if (availability.conflictData.id === excludeId) {
          // Se o conflito é com o próprio agendamento sendo editado, considerar disponível
          return res.status(200).json({
            available: true,
            message: "Horário disponível para o paciente (excluindo o próprio agendamento)"
          });
        }
      }

      // Retornar o resultado da verificação
      return res.status(availability.available ? 200 : 409).json({
        available: availability.available,
        reason: availability.reason,
        conflict: availability.conflictData,
        message: availability.available
          ? "Horário disponível para o paciente"
          : "Paciente já possui um agendamento neste horário"
      });
    } catch (error) {
      console.error("Erro ao verificar disponibilidade do paciente:", error);
      return res.status(500).json({
        available: false,
        message: "Erro ao verificar disponibilidade do paciente",
        error: error.message
      });
    }
  }

  static async create(req, res) {
    try {
      const {
        userId,
        personId,
        creatorId,
        locationId,
        title,
        description,
        startDate,
        endDate,
        serviceTypeId,
        insuranceId,
        branchId,
        sequentialAppointments = 1 // NOVO: Parâmetro para agendamentos sequenciais (padrão: 1)
      } = req.body;

      console.log(`Criando agendamento: ${title} para pessoa ${personId} com profissional ${userId}`);
      console.log(`Agendamentos sequenciais: ${sequentialAppointments}`);

      // Validate the input parameters
      if (!userId || !personId || !locationId || !title || !startDate || !endDate || !serviceTypeId) {
        return res.status(400).json({
          message: "Campos obrigatórios faltando",
        });
      }

      // Validar o número de agendamentos sequenciais
      if (sequentialAppointments < 1 || sequentialAppointments > 5) {
        return res.status(400).json({
          message: "Número de agendamentos sequenciais deve ser entre 1 e 5",
        });
      }

      // Verify person exists and get clientId
      const person = await prisma.person.findUnique({
        where: { id: personId },
        select: {
          id: true,
          fullName: true,
          clientId: true
        }
      });

      if (!person) {
        return res.status(404).json({
          message: "Pessoa não encontrada"
        });
      }

      if (!person.clientId) {
        return res.status(400).json({
          message: "A pessoa selecionada não está associada a um cliente"
        });
      }

      // NOVO: Verificar disponibilidade para todos os agendamentos sequenciais
      // Primeiro, verificar a disponibilidade do profissional para o agendamento original
      const originalProviderAvailability = await AvailabilityHelper.checkProviderAvailability(
        userId,
        startDate,
        endDate,
        null // Não há ID para excluir na criação
      );

      if (!originalProviderAvailability.available) {
        return res.status(400).json({
          message: "Horário indisponível para o profissional no agendamento principal",
          reason: originalProviderAvailability.reason,
          conflictData: originalProviderAvailability.conflictData,
        });
      }

      // Verificar se o paciente já tem um agendamento no mesmo horário
      const originalPatientAvailability = await AvailabilityHelper.checkPatientAvailability(
        personId,
        startDate,
        endDate,
        null // Não há ID para excluir na criação
      );

      if (!originalPatientAvailability.available) {
        return res.status(400).json({
          message: "Paciente já possui um agendamento neste horário",
          reason: originalPatientAvailability.reason,
          conflictData: originalPatientAvailability.conflictData,
        });
      }

      // Se houver agendamentos sequenciais, verificar a disponibilidade para cada um
      const allSlots = [];
      if (sequentialAppointments > 1) {
        const originalStartDate = new Date(startDate);
        const originalEndDate = new Date(endDate);
        const appointmentDuration = originalEndDate.getTime() - originalStartDate.getTime();

        let previousEndDate = originalEndDate;

        for (let i = 1; i < sequentialAppointments; i++) {
          // Cada agendamento começa exatamente quando o anterior termina
          const nextStartDate = new Date(previousEndDate.getTime()); // Usar o horário de término do anterior
          const nextEndDate = new Date(nextStartDate.getTime() + appointmentDuration);

          // Verificar disponibilidade do profissional para o agendamento sequencial
          const providerSlotAvailability = await AvailabilityHelper.checkProviderAvailability(
            userId,
            nextStartDate.toISOString(),
            nextEndDate.toISOString(),
            null // Não há ID para excluir na criação
          );

          if (!providerSlotAvailability.available) {
            return res.status(400).json({
              message: `Horário indisponível para o profissional no agendamento sequencial #${i + 1}`,
              reason: providerSlotAvailability.reason,
              conflictData: providerSlotAvailability.conflictData,
              startDate: nextStartDate,
              endDate: nextEndDate
            });
          }

          // Verificar disponibilidade do paciente para o agendamento sequencial
          const patientSlotAvailability = await AvailabilityHelper.checkPatientAvailability(
            personId,
            nextStartDate.toISOString(),
            nextEndDate.toISOString(),
            null // Não há ID para excluir na criação
          );

          if (!patientSlotAvailability.available) {
            return res.status(400).json({
              message: `Paciente já possui um agendamento no horário do agendamento sequencial #${i + 1}`,
              reason: patientSlotAvailability.reason,
              conflictData: patientSlotAvailability.conflictData,
              startDate: nextStartDate,
              endDate: nextEndDate
            });
          }

          allSlots.push({
            startDate: nextStartDate,
            endDate: nextEndDate
          });

          previousEndDate = nextEndDate;
        }
      }

      // Get location details to set companyId and branchId
      const location = await prisma.location.findUnique({
        where: { id: locationId },
        select: {
          companyId: true,
          branchId: true
        }
      });

      if (!location) {
        return res.status(404).json({
          message: "Local não encontrado"
        });
      }

      // Set company and branch IDs from location if not provided
      const schedulingCompanyId = location.companyId;
      const schedulingBranchId = branchId || location.branchId;

      // Usar transação para garantir consistência e evitar race conditions
      const { allSchedulings } = await prisma.$transaction(async (prismaClient) => {
        // Se um convênio foi informado, verificar os limites de serviço
        if (insuranceId) {
          // Verificar se existe um limite de serviço para esta combinação
          const limit = await prismaClient.personInsuranceServiceLimit.findUnique({
            where: {
              personId_insuranceId_serviceTypeId: {
                personId,
                insuranceId,
                serviceTypeId
              }
            }
          });

          if (limit) {
            // Calcular o início do mês corrente
            const appointmentDate = new Date(startDate);
            const startOfMonth = new Date(appointmentDate.getFullYear(), appointmentDate.getMonth(), 1);
            const endOfMonth = new Date(appointmentDate.getFullYear(), appointmentDate.getMonth() + 1, 0, 23, 59, 59);

            // Obter o nome do mês para mensagens mais claras
            const monthName = new Intl.DateTimeFormat('pt-BR', { month: 'long', year: 'numeric' }).format(appointmentDate);
            console.log(`Verificando limites para o mês de ${monthName} (${startOfMonth.toISOString()} até ${endOfMonth.toISOString()})`);

            // Calcular o início do ano corrente
            const startOfYear = new Date(appointmentDate.getFullYear(), 0, 1);
            const endOfYear = new Date(appointmentDate.getFullYear(), 11, 31, 23, 59, 59);

            // Contar agendamentos no mês corrente
            const monthlyAppointmentsCount = await prismaClient.scheduling.count({
              where: {
                Person: {
                  some: {
                    id: personId
                  }
                },
                insuranceId,
                serviceTypeId,
                startDate: {
                  gte: startOfMonth,
                  lte: endOfMonth
                },
                status: {
                  notIn: ['CANCELLED']
                }
              }
            });

            console.log(`Agendamentos utilizados no mês de ${monthName}: ${monthlyAppointmentsCount}/${limit.monthlyLimit}`);
            console.log(`Tentando agendar mais ${sequentialAppointments} agendamentos`);

            // Verificar limite mensal considerando os agendamentos sequenciais
            if (limit.monthlyLimit > 0 && (monthlyAppointmentsCount + sequentialAppointments) > limit.monthlyLimit) {
              console.log(`LIMITE EXCEDIDO: ${monthlyAppointmentsCount} + ${sequentialAppointments} > ${limit.monthlyLimit}`);

              // Obter detalhes da pessoa, convênio e serviço para mensagem mais clara
              const [person, insurance, serviceType] = await Promise.all([
                prismaClient.person.findUnique({ where: { id: personId }, select: { fullName: true } }),
                prismaClient.insurance.findUnique({ where: { id: insuranceId }, select: { name: true } }),
                prismaClient.serviceType.findUnique({ where: { id: serviceTypeId }, select: { name: true } })
              ]);

              throw new Error(`Limite mensal de ${limit.monthlyLimit} agendamentos atingido para ${monthName} com o convênio ${insurance?.name || 'selecionado'} no serviço ${serviceType?.name || 'selecionado'}. ` +
                        `Você já utilizou ${monthlyAppointmentsCount} e está tentando agendar ${sequentialAppointments} ` +
                        `(${monthlyAppointmentsCount + sequentialAppointments} no total).`);
            }

            // Se existe limite anual, verificar também
            if (limit.yearlyLimit > 0) {
              const yearlyAppointmentsCount = await prismaClient.scheduling.count({
                where: {
                  Person: {
                    some: {
                      id: personId
                    }
                  },
                  insuranceId,
                  serviceTypeId,
                  startDate: {
                    gte: startOfYear,
                    lte: endOfYear
                  },
                  status: {
                    notIn: ['CANCELLED']
                  }
                }
              });

              console.log(`Agendamentos utilizados no ano de ${appointmentDate.getFullYear()}: ${yearlyAppointmentsCount}/${limit.yearlyLimit}`);

              // Verificar limite anual considerando os agendamentos sequenciais
              if (limit.yearlyLimit > 0 && (yearlyAppointmentsCount + sequentialAppointments) > limit.yearlyLimit) {
                console.log(`LIMITE ANUAL EXCEDIDO: ${yearlyAppointmentsCount} + ${sequentialAppointments} > ${limit.yearlyLimit}`);

                // Obter detalhes da pessoa, convênio e serviço para mensagem mais clara
                const [person, insurance, serviceType] = await Promise.all([
                  prismaClient.person.findUnique({ where: { id: personId }, select: { fullName: true } }),
                  prismaClient.insurance.findUnique({ where: { id: insuranceId }, select: { name: true } }),
                  prismaClient.serviceType.findUnique({ where: { id: serviceTypeId }, select: { name: true } })
                ]);

                throw new Error(`Limite anual de ${limit.yearlyLimit} agendamentos atingido para ${appointmentDate.getFullYear()} com o convênio ${insurance?.name || 'selecionado'} no serviço ${serviceType?.name || 'selecionado'}. ` +
                          `Você já utilizou ${yearlyAppointmentsCount} e está tentando agendar ${sequentialAppointments} ` +
                          `(${yearlyAppointmentsCount + sequentialAppointments} no total).`);
              }
            }
          }
        }

        // Criar o agendamento principal
        const mainScheduling = await prismaClient.scheduling.create({
          data: {
            userId,
            clientId: person.clientId, // Adicionar clientId da pessoa
            creatorId: creatorId || req.user.id,
            locationId,
            title,
            description,
            startDate: new Date(startDate),
            endDate: new Date(endDate),
            serviceTypeId,
            insuranceId,
            companyId: schedulingCompanyId,
            branchId: schedulingBranchId,
            Person: {
              connect: { id: personId }
            }
          },
          include: {
            Person: {
              include: {
                client: true
              }
            },
            provider: true,
            serviceType: true,
            location: true,
            branch: true,
          },
        });

        // Array para armazenar todos os agendamentos criados
        const allSchedulings = [mainScheduling];

        // Criar agendamentos sequenciais, se necessário
        if (sequentialAppointments > 1) {
          // Garantir que estamos criando o número correto de agendamentos sequenciais
          // allSlots já contém exatamente sequentialAppointments - 1 slots
          for (let i = 0; i < allSlots.length; i++) {
            const sequentialSlot = allSlots[i];
            const sequentialIndex = i + 1; // Índice começa em 1 para o primeiro agendamento sequencial

            const sequentialScheduling = await prismaClient.scheduling.create({
              data: {
                userId,
                clientId: person.clientId, // Adicionar clientId da pessoa
                creatorId: creatorId || req.user.id,
                locationId,
                title: `${title} (Continuação ${sequentialIndex})`, // Adiciona sufixo para indicar sequência
                description,
                startDate: sequentialSlot.startDate,
                endDate: sequentialSlot.endDate,
                serviceTypeId,
                insuranceId,
                companyId: schedulingCompanyId,
                branchId: schedulingBranchId,
                Person: {
                  connect: { id: personId }
                }
              },
              include: {
                Person: {
                  include: {
                    client: true
                  }
                },
                provider: true,
                serviceType: true,
                location: true,
                branch: true,
              },
            });

            allSchedulings.push(sequentialScheduling);
          }
        }

        return { allSchedulings };
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'CREATE',
          entityType: 'Scheduling',
          entityId: allSchedulings[0].id, // Usar o ID do primeiro agendamento (principal)
          details: {
            requestData: req.body,
            sequentialAppointments: sequentialAppointments,
            totalCreated: allSchedulings.length
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: schedulingCompanyId
        }
      });

      // Enviar email de confirmação ao cliente (se disponível)
      try {
        // Verificar se o serviço de email está disponível
        if (typeof emailService?.sendNewAppointmentEmail === 'function') {
          // Usar o primeiro agendamento (principal)
          const mainAppointment = allSchedulings[0];

          // Use client email if available, otherwise use person email
          const emailTo = mainAppointment.Person?.[0]?.client?.email || mainAppointment.Person?.[0]?.email;

          if (emailTo) {
            await emailService.sendNewAppointmentEmail(
              mainAppointment,
              mainAppointment.Person[0],
              mainAppointment.provider,
              mainAppointment.serviceType,
              mainAppointment.location,
              mainAppointment.branch
            );
            console.log(`Email de confirmação enviado para ${emailTo}`);
          }
        } else {
          console.log('Serviço de email não disponível ou não configurado');
        }
      } catch (emailError) {
        console.error("Erro ao enviar email de confirmação:", emailError);
        // Não impedimos a criação do agendamento se o email falhar
      }

      // NOVO: Preparar resposta com todos os agendamentos criados
      const response = {
        mainScheduling: allSchedulings[0], // O primeiro agendamento é o principal
        sequentialSchedulings: allSchedulings.slice(1), // Todos exceto o principal
        totalCreated: allSchedulings.length
      };

      // Retorna todos os agendamentos criados
      res.status(201).json(response);
    } catch (error) {
      console.error("Erro ao criar agendamento: ", error);

      // Verificar se é um erro de limite de convênio
      if (error.message && (
          error.message.includes('Limite mensal') ||
          error.message.includes('Limite anual')
      )) {
        return res.status(400).json({ message: error.message });
      }

      // Verificar se é um erro de disponibilidade
      if (error.message && error.message.includes('indisponível')) {
        return res.status(400).json({ message: error.message });
      }

      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async update(req, res) {
    try {
      const { id } = req.params;
      const {
        userId,
        personId,
        creatorId,
        locationId,
        title,
        description,
        startDate,
        endDate,
        serviceTypeId,
        insuranceId,
        branchId,
        status
      } = req.body;

      // Check if scheduling exists
      const existingScheduling = await prisma.scheduling.findUnique({
        where: { id }
      });

      if (!existingScheduling) {
        return res.status(404).json({ message: "Agendamento não encontrado" });
      }

      // Check if user has access to this scheduling
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && existingScheduling.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a este agendamento' });
      }

      let updateData = {
        userId,
        creatorId,
        locationId,
        title,
        description,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        serviceTypeId,
        insuranceId,
        branchId,
        status
      };

      // If location changed, update the branch and company if needed
      if (locationId && locationId !== existingScheduling.locationId) {
        const location = await prisma.location.findUnique({
          where: { id: locationId },
          select: {
            companyId: true,
            branchId: true
          }
        });

        if (location) {
          updateData.companyId = location.companyId;
          // Only set branchId from location if not explicitly provided
          if (!branchId) {
            updateData.branchId = location.branchId;
          }
        }
      }

      // Remove undefined values
      Object.keys(updateData).forEach(key =>
        updateData[key] === undefined && delete updateData[key]
      );

      // Verificar disponibilidade do profissional se houver mudança de horário ou profissional
      if ((startDate && startDate !== existingScheduling.startDate) ||
          (endDate && endDate !== existingScheduling.endDate) ||
          (userId && userId !== existingScheduling.userId)) {

        const newStartDate = startDate ? new Date(startDate) : existingScheduling.startDate;
        const newEndDate = endDate ? new Date(endDate) : existingScheduling.endDate;
        const newUserId = userId || existingScheduling.userId;

        // Verificar disponibilidade do profissional
        const providerAvailability = await AvailabilityHelper.checkProviderAvailability(
          newUserId,
          newStartDate,
          newEndDate,
          id // Excluir o próprio agendamento da verificação
        );

        if (!providerAvailability.available) {
          return res.status(400).json({
            message: "Horário indisponível para o profissional",
            reason: providerAvailability.reason,
            conflictData: providerAvailability.conflictData
          });
        }
      }

      // Verificar disponibilidade do paciente se houver mudança de horário ou paciente
      if (personId && ((startDate && startDate !== existingScheduling.startDate) ||
                       (endDate && endDate !== existingScheduling.endDate))) {

        const newStartDate = startDate ? new Date(startDate) : existingScheduling.startDate;
        const newEndDate = endDate ? new Date(endDate) : existingScheduling.endDate;

        // Verificar disponibilidade do paciente
        const patientAvailability = await AvailabilityHelper.checkPatientAvailability(
          personId,
          newStartDate,
          newEndDate,
          id // Excluir o próprio agendamento da verificação
        );

        if (!patientAvailability.available) {
          return res.status(400).json({
            message: "Paciente já possui um agendamento neste horário",
            reason: patientAvailability.reason,
            conflictData: patientAvailability.conflictData
          });
        }
      }

      // Update the scheduling
      const scheduling = await prisma.scheduling.update({
        where: { id },
        data: updateData,
      });

      // If personId changed, update the many-to-many relationship
      if (personId) {
        // Get current connected persons
        const currentPersons = await prisma.scheduling.findUnique({
          where: { id },
          select: {
            Person: {
              select: { id: true, clientId: true }
            }
          }
        });

        // If the person is different, update the relationship
        const currentPersonIds = currentPersons?.Person?.map(p => p.id) || [];
        if (!currentPersonIds.includes(personId)) {
          // Get the person to get their primary client
          const person = await prisma.person.findUnique({
            where: { id: personId },
            include: {
              clientPersons: {
                where: { isPrimary: true },
                include: { client: true }
              }
            }
          });

          if (!person) {
            return res.status(404).json({ message: "Pessoa não encontrada" });
          }

          // Get the primary client or the first client if no primary is set
          const primaryClientPerson = person.clientPersons.find(cp => cp.isPrimary) || person.clientPersons[0];

          if (!primaryClientPerson) {
            return res.status(400).json({ message: "Pessoa não está associada a nenhum cliente" });
          }

          // Update both the Person relationship and the clientId
          await prisma.scheduling.update({
            where: { id },
            data: {
              clientId: primaryClientPerson.clientId, // Update clientId to match the person's primary client
              Person: {
                set: [], // Clear existing connections
                connect: { id: personId }
              }
            }
          });
        }
      }

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Scheduling',
          entityId: scheduling.id,
          details: { requestData: req.body },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: scheduling.companyId
        }
      });

      res.json(scheduling);
    } catch (error) {
      console.error("Erro ao atualizar agendamento:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async list(req, res) {
    try {
      // Log the query parameters for debugging
      console.log("Scheduling list query parameters:", req.query);

      const {
        page = 1,
        limit = 10,
        providers,
        providerIds,
        persons,
        personIds,
        locations,
        locationIds,
        serviceTypes,
        serviceTypeIds,
        branches,
        branchIds,
        startDate,
        endDate,
        status,
        include_insurance_info // Novo parâmetro
      } = req.query;

      // Log for debugging
      console.log(`[SCHEDULING-LIST] Usuário: ${req.user.id}, isClient: ${req.user.isClient}, role: ${req.user.role}`);

      // Build where clause
      const where = {
        // If user is a client, only show their own appointments
        ...(req.user.isClient ?
          {
            OR: [
              {
                Person: {
                  some: {
                    clientId: req.user.id
                  }
                }
              }
            ]
          } :
          // If user is not system admin, limit to their company
          (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) ?
            { companyId: req.user.companyId } : {})
      };

      // Handle both providers and providerIds parameters
      const providerIdsToFilter = providerIds || providers;
      if (providerIdsToFilter && providerIdsToFilter.length > 0) {
        where.userId = {
          in: Array.isArray(providerIdsToFilter) ? providerIdsToFilter : [providerIdsToFilter],
        };
      }

      // Handle both persons and personIds parameters
      const personIdsToFilter = personIds || persons;
      if (personIdsToFilter && personIdsToFilter.length > 0) {
        console.log("Filtering by personIds:", personIdsToFilter);
        where.Person = {
          some: {
            id: { in: Array.isArray(personIdsToFilter) ? personIdsToFilter : [personIdsToFilter] }
          }
        };
      }

      // Handle both locations and locationIds parameters
      const locationIdsToFilter = locationIds || locations;
      if (locationIdsToFilter && locationIdsToFilter.length > 0) {
        where.locationId = {
          in: Array.isArray(locationIdsToFilter) ? locationIdsToFilter : [locationIdsToFilter],
        };
      }

      // Handle both serviceTypes and serviceTypeIds parameters
      const serviceTypeIdsToFilter = serviceTypeIds || serviceTypes;
      if (serviceTypeIdsToFilter && serviceTypeIdsToFilter.length > 0) {
        where.serviceTypeId = {
          in: Array.isArray(serviceTypeIdsToFilter) ? serviceTypeIdsToFilter : [serviceTypeIdsToFilter],
        };
      }

      // Handle both branches and branchIds parameters
      const branchIdsToFilter = branchIds || branches;
      if (branchIdsToFilter && branchIdsToFilter.length > 0) {
        where.branchId = {
          in: Array.isArray(branchIdsToFilter) ? branchIdsToFilter : [branchIdsToFilter],
        };
      }

      // Add date range filter
      if (startDate && endDate) {
        where.startDate = {
          gte: new Date(startDate),
          lte: new Date(endDate)
        };
      } else if (startDate) {
        where.startDate = {
          gte: new Date(startDate)
        };
      } else if (endDate) {
        where.startDate = {
          lte: new Date(endDate)
        };
      }

      // Add status filter
      if (status) {
        // Verificar se status é um array
        if (Array.isArray(status)) {
          // Usar o operador 'in' do Prisma para filtrar por múltiplos status
          where.status = {
            in: status
          };
          console.log("Filtrando por múltiplos status:", status);
        } else {
          // Se for um único status, verificar se é uma string com valores separados por vírgula
          if (typeof status === 'string' && status.includes(',')) {
            // Dividir a string em um array de valores
            const statusArray = status.split(',').map(s => s.trim());
            where.status = {
              in: statusArray
            };
            console.log("Filtrando por múltiplos status (string separada por vírgula):", statusArray);
          } else {
            // Se for um único status, usar diretamente
            where.status = status;
            console.log("Filtrando por um único status:", status);
          }
        }
      }

      // Add clientId filter (for client dashboard)
      if (req.query.clientId) {
        where.Person = {
          some: {
            clientId: req.query.clientId
          }
        };
        console.log("Filtrando por clientId:", req.query.clientId);
      }

      // Log the final where clause for debugging
      console.log("Final where clause for scheduling query:", JSON.stringify(where, null, 2));

      // Executar a consulta principal
      const [schedulings, total] = await Promise.all([
        prisma.scheduling.findMany({
          where,
          skip: (Number(page) - 1) * Number(limit),
          take: Number(limit),
          select: {
            id: true,
            userId: true,
            locationId: true,
            title: true,
            description: true,
            startDate: true,
            endDate: true,
            serviceTypeId: true,
            insuranceId: true,
            status: true,
            branchId: true,
            provider: {
              select: {
                fullName: true,
                professionObj: {
                  select: {
                    name: true
                  }
                }
              },
            },
            Person: {
              select: {
                id: true,
                fullName: true,
                client: {
                  select: {
                    id: true,
                    email: true
                  }
                }
              },
            },

            location: {
              select: {
                name: true,
              },
            },
            branch: {
              select: {
                name: true,
                code: true
              }
            },
            serviceType: {
              select: {
                name: true,
              },
            },
            insurance: {
              select: {
                id: true,
                name: true
              }
            }
          },
          orderBy: {
            startDate: "asc",
          },
        }),
        prisma.scheduling.count({ where }),
      ]);

      // Incluir informações de limite de convênio se solicitado
      if (include_insurance_info === 'true' && schedulings.length > 0) {
        // Para cada agendamento com convênio, buscar informações de limite
        // Usamos a data atual para calcular mês/ano corrente
        const now = new Date();
        const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        const startOfYear = new Date(now.getFullYear(), 0, 1);
        const endOfYear = new Date(now.getFullYear(), 11, 31, 23, 59, 59);

        for (const scheduling of schedulings) {
          // Extrair o ID da pessoa do relacionamento Person
          const personId = scheduling.Person && scheduling.Person[0] ? scheduling.Person[0].id : null;

          if (scheduling.insuranceId && personId && scheduling.serviceTypeId) {
            try {
              // Buscar limite configurado
              const limit = await prisma.personInsuranceServiceLimit.findUnique({
                where: {
                  personId_insuranceId_serviceTypeId: {
                    personId: personId,
                    insuranceId: scheduling.insuranceId,
                    serviceTypeId: scheduling.serviceTypeId
                  }
                }
              });

              if (limit) {
                // Contar agendamentos no mês corrente
                const monthlyUsed = await prisma.scheduling.count({
                  where: {
                    Person: {
                      some: {
                        id: personId
                      }
                    },
                    insuranceId: scheduling.insuranceId,
                    serviceTypeId: scheduling.serviceTypeId,
                    startDate: {
                      gte: startOfMonth,
                      lte: endOfMonth
                    },
                    status: {
                      notIn: ['CANCELLED']
                    }
                  }
                });

                // Contar agendamentos no ano corrente se necessário
                let yearlyUsed = 0;
                if (limit.yearlyLimit > 0) {
                  yearlyUsed = await prisma.scheduling.count({
                    where: {
                      Person: {
                        some: {
                          id: personId
                        }
                      },
                      insuranceId: scheduling.insuranceId,
                      serviceTypeId: scheduling.serviceTypeId,
                      startDate: {
                        gte: startOfYear,
                        lte: endOfYear
                      },
                      status: {
                        notIn: ['CANCELLED']
                      }
                    }
                  });
                }

                // Adicionar informações de limite ao agendamento
                scheduling.insuranceInfo = {
                  monthlyLimit: limit.monthlyLimit,
                  monthlyUsed,
                  yearlyLimit: limit.yearlyLimit,
                  yearlyUsed,
                  hasReachedLimit:
                    (limit.monthlyLimit > 0 && monthlyUsed >= limit.monthlyLimit) ||
                    (limit.yearlyLimit > 0 && yearlyUsed >= limit.yearlyLimit)
                };
              }
            } catch (error) {
              console.error(`Erro ao obter informações de limite para agendamento ${scheduling.id}:`, error);
            }
          }
        }
      }

      res.json({
        schedulings,
        total,
        pages: Math.ceil(total / Number(limit)),
      });
    } catch (error) {
      console.error("Erro ao listar agendamentos: ", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async get(req, res) {
    try {
      const { id } = req.params;

      const scheduling = await prisma.scheduling.findUnique({
        where: { id },
        include: {
          provider: {
            select: {
              id: true,
              fullName: true,
              email: true,
              phone: true,
              professionObj: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          Person: {
            select: {
              id: true,
              fullName: true,
              email: true,
              phone: true,
              client: {
                select: {
                  id: true,
                  email: true
                }
              }
            }
          },
          location: {
            select: {
              id: true,
              name: true,
              address: true,
              phone: true
            }
          },
          branch: {
            select: {
              id: true,
              name: true,
              code: true,
              address: true,
              phone: true
            }
          },
          serviceType: {
            select: {
              id: true,
              name: true,
              value: true
            }
          },
          insurance: {
            select: {
              id: true,
              name: true
            }
          },
          recurrence: {
            select: {
              id: true,
              title: true,
              recurrenceType: true,
              numberOfOccurrences: true,
              endDate: true
            }
          }
        }
      });

      if (!scheduling) {
        return res.status(404).json({ message: "Agendamento não encontrado" });
      }

      // Check if user has access to this scheduling
      if (req.user.isClient) {
        // For clients, check if the appointment belongs to them or their related persons
        const isClientAppointment = scheduling.clientId === req.user.id;
        const isRelatedPersonAppointment = scheduling.Person?.client?.id === req.user.id;

        if (!isClientAppointment && !isRelatedPersonAppointment) {
          return res.status(403).json({ message: 'Você não tem permissão para acessar este agendamento' });
        }
      } else if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && scheduling.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a este agendamento' });
      }

      res.json(scheduling);
    } catch (error) {
      console.error("Erro ao buscar detalhes do agendamento:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Check if scheduling exists
      const scheduling = await prisma.scheduling.findUnique({
        where: { id }
      });

      if (!scheduling) {
        return res.status(404).json({ message: "Agendamento não encontrado" });
      }

      // Check if user has access to this scheduling
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && scheduling.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a este agendamento' });
      }

      await prisma.scheduling.delete({
        where: { id },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'DELETE',
          entityType: 'Scheduling',
          entityId: id,
          details: { deletedScheduling: scheduling },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: scheduling.companyId
        }
      });

      res.status(204).send();
    } catch (error) {
      console.error("Erro ao deletar agendamento: ", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  SchedulingController,
};