version: '3'

services:
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: high-tide-systems
    restart: unless-stopped
    environment:
      - NEXT_PUBLIC_API_URL=https://hightide.site/api
    ports:
      - "127.0.0.1:3000:3000"  # Restrito apenas à localhost, o Nginx encaminhará
    volumes:
      - ./public:/app/public  # Para facilitar atualizações de assets estáticos
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
