"use client";

import React, { useState, useEffect } from "react";
import { 
  UserPlus, 
  Users, 
  Loader2, 
  RefreshCw,
  Edit,
  Trash,
  Mail,
  Phone,
  User,
  FileText,
  X
} from "lucide-react";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import ContactFormModal from "./ContactFormModal";
import { contactsService } from "@/app/modules/people/services/contactsService";

const PersonContactsSection = ({ personId }) => {
  const [contacts, setContacts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [contactFormOpen, setContactFormOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [contactToDelete, setContactToDelete] = useState(null);
  const [notesModalOpen, setNotesModalOpen] = useState(false);
  const [selectedNotes, setSelectedNotes] = useState({ name: "", notes: "" });
  
  useEffect(() => {
    loadContacts();
  }, [personId]);
  
  const loadContacts = async () => {
    if (!personId) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const data = await contactsService.getContactsByPerson(personId);
      setContacts(data || []);
    } catch (err) {
      console.error("Error fetching contacts:", err);
      setError("Não foi possível carregar os contatos.");
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleOpenContactForm = (contact = null) => {
    setSelectedContact(contact);
    setContactFormOpen(true);
  };
  
  const handleDeleteContact = (contact) => {
    setContactToDelete(contact.id);
    setConfirmDialogOpen(true);
  };
  
  const handleViewNotes = (contact) => {
    setSelectedNotes({
      name: contact.name,
      notes: contact.notes || ""
    });
    setNotesModalOpen(true);
  };
  
  const confirmDeleteContact = async () => {
    if (!contactToDelete) return;
    
    try {
      await contactsService.deleteContact(contactToDelete);
      setContacts(contacts.filter(c => c.id !== contactToDelete));
      setConfirmDialogOpen(false);
      setContactToDelete(null);
    } catch (err) {
      console.error("Error deleting contact:", err);
      setError("Não foi possível excluir o contato.");
    }
  };
  
  const formatPhone = (phone) => {
    if (!phone) return "N/A";
    
    // Phone format: (00) 00000-0000
    const phoneNumbers = phone.replace(/\D/g, '');
    return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-gray-100">Contatos</h3>
          {isLoading ? (
            <Loader2 size={16} className="animate-spin text-neutral-400 dark:text-gray-500" />
          ) : (
            <span className="text-sm text-neutral-500 dark:text-gray-400 bg-neutral-100 dark:bg-gray-700 px-2 py-0.5 rounded-full">
              {contacts.length}
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => loadContacts()}
            className="p-2 text-neutral-600 dark:text-gray-300 hover:bg-neutral-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            title="Atualizar"
          >
            <RefreshCw size={16} />
          </button>
          
          <button
            onClick={() => handleOpenContactForm()}
            className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <UserPlus size={16} />
            <span>Adicionar Contato</span>
          </button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300">
          {error}
          <button
            onClick={loadContacts}
            className="ml-2 underline hover:no-underline"
          >
            Tentar novamente
          </button>
        </div>
      )}
      
      {isLoading ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex justify-center items-center">
          <div className="flex flex-col items-center">
            <Loader2 size={32} className="text-primary-500 dark:text-primary-400 animate-spin mb-4" />
            <p className="text-neutral-600 dark:text-gray-300">Carregando contatos...</p>
          </div>
        </div>
      ) : contacts.length === 0 ? (
        <div className="bg-white dark:bg-gray-800 rounded-xl border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20 p-8 flex flex-col items-center">
          <Users size={48} className="text-neutral-300 dark:text-gray-600 mb-4" />
          <h4 className="text-lg font-medium text-neutral-800 dark:text-gray-100 mb-2">Nenhum contato</h4>
          <p className="text-neutral-600 dark:text-gray-300 mb-6 max-w-md text-center">
            Adicione contatos relacionados a esta pessoa, como familiares, amigos ou outras pessoas de referência.
          </p>
          <button
            onClick={() => handleOpenContactForm()}
            className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <UserPlus size={16} />
            <span>Adicionar Contato</span>
          </button>
        </div>
      ) : (
        <div className="bg-white dark:bg-gray-800 rounded-xl overflow-hidden border border-neutral-200 dark:border-gray-700 shadow-sm dark:shadow-md dark:shadow-black/20">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
            <thead>
              <tr className="bg-neutral-50 dark:bg-gray-900">
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Nome
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Relacionamento
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Telefone
                </th>
                <th className="px-4 py-3 text-right text-xs font-medium text-neutral-500 dark:text-gray-400 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
              {contacts.map(contact => (
                <tr key={contact.id} className="hover:bg-neutral-50 dark:hover:bg-gray-700">
                  <td className="px-4 py-3 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-700 flex items-center justify-center text-neutral-600 dark:text-gray-300 font-medium">
                        {contact.name.charAt(0).toUpperCase()}
                      </div>
                      <span className="ml-2 text-neutral-700 dark:text-gray-200">{contact.name}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                      {contact.relationship || "Não especificado"}
                    </span>
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    {contact.email ? (
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3 text-neutral-400 dark:text-gray-500" />
                        <a href={`mailto:${contact.email}`} className="text-primary-600 dark:text-primary-400 hover:underline">
                          {contact.email}
                        </a>
                      </div>
                    ) : (
                      <span className="text-neutral-400 dark:text-gray-500">Não informado</span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap">
                    {contact.phone ? (
                      <div className="flex items-center gap-1">
                        <Phone className="h-3 w-3 text-neutral-400 dark:text-gray-500" />
                        <a href={`tel:${contact.phone}`} className="text-neutral-700 dark:text-gray-300">
                          {formatPhone(contact.phone)}
                        </a>
                      </div>
                    ) : (
                      <span className="text-neutral-400 dark:text-gray-500">Não informado</span>
                    )}
                  </td>
                  <td className="px-4 py-3 whitespace-nowrap text-right">
                    <div className="flex justify-end">
                      <button
                        onClick={() => handleOpenContactForm(contact)}
                        className="p-1 text-neutral-500 dark:text-gray-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                        title="Editar"
                      >
                        <Edit size={16} />
                      </button>
                      <button
                        onClick={() => handleViewNotes(contact)}
                        className={`p-1 ${contact.notes ? 'text-neutral-500 dark:text-gray-400 hover:text-blue-500 dark:hover:text-blue-400' : 'text-neutral-300 dark:text-gray-600 cursor-not-allowed'}`}
                        title={contact.notes ? "Ver Observações" : "Sem Observações"}
                        disabled={!contact.notes}
                      >
                        <FileText size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteContact(contact)}
                        className="p-1 text-neutral-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                        title="Excluir"
                      >
                        <Trash size={16} />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {/* Notes Modal */}
      {notesModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
          <div className="fixed inset-0 bg-black/50" onClick={() => setNotesModalOpen(false)}></div>
          
          <div className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-lg dark:shadow-black/30 max-w-md w-full z-[55]">
            <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
              <h3 className="text-xl font-semibold text-neutral-800 dark:text-gray-100">
                Observações de {selectedNotes.name}
              </h3>
              <button
                onClick={() => setNotesModalOpen(false)}
                className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="p-6">
              <div className="bg-neutral-50 dark:bg-gray-700 p-4 rounded-lg border border-neutral-200 dark:border-gray-600 text-neutral-700 dark:text-gray-300 min-h-[100px] whitespace-pre-wrap">
                {selectedNotes.notes || "Nenhuma observação disponível."}
              </div>
              
              <div className="mt-4 flex justify-end">
                <button
                  onClick={() => setNotesModalOpen(false)}
                  className="px-4 py-2 bg-neutral-100 dark:bg-gray-700 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Fechar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Contact Form Modal */}
      {contactFormOpen && (
        <ContactFormModal
          isOpen={contactFormOpen}
          onClose={() => {
            setContactFormOpen(false);
            setSelectedContact(null);
          }}
          contact={selectedContact}
          personId={personId}
          onSuccess={() => {
            setContactFormOpen(false);
            setSelectedContact(null);
            loadContacts();
          }}
        />
      )}
      
      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => {
          setConfirmDialogOpen(false);
          setContactToDelete(null);
        }}
        onConfirm={confirmDeleteContact}
        title="Excluir Contato"
        message="Tem certeza que deseja excluir este contato? Esta ação não pode ser desfeita."
        variant="danger"
        confirmText="Excluir"
        cancelText="Cancelar"
      />
    </div>
  );
};

export default PersonContactsSection;