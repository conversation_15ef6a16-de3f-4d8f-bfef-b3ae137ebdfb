const { validationResult } = require('express-validator');
const { body } = require('express-validator');
const prisma = require('../utils/prisma');

// Validações
const createInsuranceValidation = [
  body('name').notEmpty().withMessage('Nome é obrigatório'),
];

class InsuranceController {
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, companyId } = req.body;

      // Preparar dados para criação
      const data = { name };

      // Se companyId foi fornecido, adicionar relação com company
      if (companyId) {
        data.company = {
          connect: { id: companyId }
        };
      }

      const insurance = await prisma.insurance.create({
        data,
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      res.status(201).json(insurance);
    } catch (error) {
      if (error.code === 'P2002') {
        return res.status(400).json({ message: 'Nome do convênio já existe para esta empresa' });
      }
      console.error('Erro ao criar convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  static async update(req, res) {
    try {
      const { id } = req.params;
      const { name, companyId } = req.body;

      // Preparar dados para atualização
      const data = { name };

      // Se companyId foi fornecido, atualizar relação com company
      if (companyId) {
        data.company = {
          connect: { id: companyId }
        };
      } else {
        // Se companyId não foi fornecido, desconectar relacionamento
        data.company = {
          disconnect: true
        };
      }

      const insurance = await prisma.insurance.update({
        where: { id },
        data,
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      res.json(insurance);
    } catch (error) {
      if (error.code === 'P2002') {
        return res.status(400).json({ message: 'Nome do convênio já existe para esta empresa' });
      }
      console.error('Erro ao atualizar convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  static async list(req, res) {
    try {
      const { search, companyId, insuranceIds, page = 1, limit = 10 } = req.query;

      // Processar insuranceIds se existir
      let insuranceIdsArray = [];
      if (insuranceIds) {
        // Se for um único valor, converter para array
        if (!Array.isArray(insuranceIds)) {
          insuranceIdsArray = [insuranceIds];
        } else {
          insuranceIdsArray = insuranceIds;
        }
        console.log("Filtrando por IDs de convênios:", insuranceIdsArray);
      }

      // Construir filtro
      let where = {};

      // Filtrar por termo de busca
      if (search) {
        where.name = {
          contains: search,
          mode: 'insensitive',
        };
      }

      // Filtrar por empresa
      if (companyId) {
        where.companyId = companyId;
      }

      // Filtrar por IDs específicos se fornecidos
      if (insuranceIdsArray.length > 0) {
        where.id = { in: insuranceIdsArray };
      }

      // Converter page e limit para números
      const pageNum = parseInt(page, 10);
      const limitNum = parseInt(limit, 10);

      // Calcular o offset para paginação
      const skip = (pageNum - 1) * limitNum;

      // Buscar os convênios com paginação
      const [insurances, total] = await Promise.all([
        prisma.insurance.findMany({
          where,
          include: {
            company: {
              select: {
                id: true,
                name: true
              }
            }
          },
          orderBy: { name: 'asc' },
          skip: skip,
          take: limitNum
        }),
        prisma.insurance.count({ where })
      ]);

      // Calcular o número total de páginas
      const totalPages = Math.ceil(total / limitNum);

      // Retornar os dados com metadados de paginação
      res.json({
        insurances,
        total,
        pages: totalPages,
        currentPage: pageNum
      });
    } catch (error) {
      console.error('Erro ao listar convênios:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  static async get(req, res) {
    try {
      const { id } = req.params;

      const insurance = await prisma.insurance.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      if (!insurance) {
        return res.status(404).json({ message: 'Convênio não encontrado' });
      }

      res.json(insurance);
    } catch (error) {
      console.error('Erro ao buscar convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  static async delete(req, res) {
    try {
      const { id } = req.params;

      await prisma.insurance.delete({
        where: { id },
      });

      res.status(204).send();
    } catch (error) {
      if (error.code === 'P2003') {
        return res.status(400).json({ message: 'Este convênio está sendo usado por outros registros e não pode ser excluído' });
      }
      console.error('Erro ao deletar convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  // Métodos para gerenciar a relação PersonInsurance
  static async addPersonInsurance(req, res) {
    try {
      const { insuranceId, personId, policyNumber, validUntil, notes } = req.body;

      // Garantir que validUntil seja uma data válida ou null
      let formattedValidUntil = null;
      if (validUntil) {
        formattedValidUntil = new Date(validUntil);
        if (isNaN(formattedValidUntil.getTime())) {
          return res.status(400).json({ message: 'Data de validade inválida' });
        }
      }

      // Verificar se a pessoa existe
      const person = await prisma.person.findUnique({
        where: { id: personId }
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Verificar se o convênio existe
      const insurance = await prisma.insurance.findUnique({
        where: { id: insuranceId }
      });

      if (!insurance) {
        return res.status(404).json({ message: 'Convênio não encontrado' });
      }

      // Verificar se a relação já existe
      const existingRelation = await prisma.personInsurance.findUnique({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
      });

      if (existingRelation) {
        return res.status(400).json({ message: 'Pessoa já possui este convênio' });
      }

      const personInsurance = await prisma.personInsurance.create({
        data: {
          insuranceId,
          personId,
          policyNumber,
          validUntil: formattedValidUntil,
          notes
        },
        include: {
          insurance: true,
          person: {
            select: {
              id: true,
              fullName: true
            }
          }
        },
      });

      res.status(201).json(personInsurance);
    } catch (error) {
      if (error.code === 'P2002') {
        return res.status(400).json({ message: 'Pessoa já possui este convênio' });
      }
      console.error('Erro ao adicionar convênio à pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  static async removePersonInsurance(req, res) {
    try {
      const { personId, insuranceId } = req.params;

      await prisma.personInsurance.delete({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
      });

      res.status(204).send();
    } catch (error) {
      console.error('Erro ao remover convênio da pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  static async listPersonInsurances(req, res) {
    try {
      const { personId } = req.params;

      const personInsurances = await prisma.personInsurance.findMany({
        where: { personId },
        include: {
          insurance: true,
        },
      });

      res.json(personInsurances);
    } catch (error) {
      console.error('Erro ao listar convênios da pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  static async updatePersonInsurance(req, res) {
    try {
      const { personId, insuranceId } = req.params;
      const { policyNumber, validUntil, notes } = req.body;

      // Garantir que validUntil seja uma data válida ou null
      let formattedValidUntil = null;
      if (validUntil) {
        formattedValidUntil = new Date(validUntil);
        if (isNaN(formattedValidUntil.getTime())) {
          return res.status(400).json({ message: 'Data de validade inválida' });
        }
      }

      // Verificar se o registro existe
      const existingRecord = await prisma.personInsurance.findUnique({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
      });

      if (!existingRecord) {
        return res.status(404).json({ message: 'Associação entre pessoa e convênio não encontrada' });
      }

      // Atualizar o registro
      const personInsurance = await prisma.personInsurance.update({
        where: {
          personId_insuranceId: {
            personId,
            insuranceId,
          },
        },
        data: {
          policyNumber,
          validUntil: formattedValidUntil,
          notes,
        },
        include: {
          insurance: true,
          person: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      res.json(personInsurance);
    } catch (error) {
      console.error('Erro ao atualizar convênio da pessoa:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  InsuranceController,
  createInsuranceValidation,
};