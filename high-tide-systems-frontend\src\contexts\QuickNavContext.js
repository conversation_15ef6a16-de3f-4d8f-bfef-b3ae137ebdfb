'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

// Contexto para gerenciar o estado do QuickNav
const QuickNavContext = createContext();

// Hook personalizado para usar o contexto
export const useQuickNav = () => {
  const context = useContext(QuickNavContext);
  if (!context) {
    throw new Error('useQuickNav deve ser usado dentro de um QuickNavProvider');
  }
  return context;
};

// Provider do contexto
export const QuickNavProvider = ({ children }) => {
  // Estado para controlar se o QuickNav está aberto
  const [isOpen, setIsOpen] = useState(false);
  
  // Estado para armazenar o histórico de navegação
  const [navigationHistory, setNavigationHistory] = useState([]);
  
  // Estado para armazenar o termo de pesquisa
  const [searchTerm, setSearchTerm] = useState('');

  // Função para abrir o QuickNav
  const openQuickNav = useCallback(() => {
    setIsOpen(true);
    setSearchTerm('');
  }, []);

  // Função para fechar o QuickNav
  const closeQuickNav = useCallback(() => {
    setIsOpen(false);
    setSearchTerm('');
  }, []);

  // Função para adicionar um item ao histórico de navegação
  const addToHistory = useCallback((item) => {
    setNavigationHistory(prev => {
      // Remover o item se já existir no histórico
      const filteredHistory = prev.filter(historyItem => historyItem.path !== item.path);
      
      // Adicionar o novo item no início do histórico
      return [item, ...filteredHistory].slice(0, 10); // Limitar a 10 itens
    });
  }, []);

  // Detectar o atalho de teclado (Ctrl+K ou /)
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Ctrl+K ou / (quando não estiver em um campo de texto)
      if (
        (event.ctrlKey && event.key === 'k') || 
        (event.key === '/' && 
          !['INPUT', 'TEXTAREA'].includes(document.activeElement.tagName) &&
          !document.activeElement.isContentEditable
        )
      ) {
        event.preventDefault();
        openQuickNav();
      }
      
      // Fechar com Escape
      if (event.key === 'Escape' && isOpen) {
        closeQuickNav();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, openQuickNav, closeQuickNav]);

  // Valor do contexto
  const value = {
    isOpen,
    openQuickNav,
    closeQuickNav,
    searchTerm,
    setSearchTerm,
    navigationHistory,
    addToHistory
  };

  return (
    <QuickNavContext.Provider value={value}>
      {children}
    </QuickNavContext.Provider>
  );
};

export default QuickNavContext;
