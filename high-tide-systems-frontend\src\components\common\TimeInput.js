'use client';

import React, { useState, useRef } from 'react';

/**
 * Componente de input de horário personalizado
 * @param {Object} props - Propriedades do componente
 * @param {string} props.value - Valor inicial do input (formato HH:MM)
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {string} props.className - Classes CSS adicionais
 * @param {string} props.placeholder - Placeholder do input
 * @param {boolean} props.disabled - Se o input está desabilitado
 * @param {string} props.id - ID do input
 * @param {string} props.name - Nome do input
 * @param {string} props.error - Mensagem de erro
 * @param {string} props.errorClassName - Classes CSS para o erro
 */
const TimeInput = ({
  value: initialValue = '',
  onChange,
  className = '',
  placeholder = '00:00',
  disabled = false,
  id,
  name,
  error,
  errorClassName = 'text-xs text-red-500 mt-1',
}) => {
  // Referência para o input
  const inputRef = useRef(null);

  // Estado interno para controlar o valor durante a edição
  // Usamos o valor inicial apenas na primeira renderização
  const [inputValue, setInputValue] = useState(initialValue || '');

  // Formata o valor para o formato HH:MM
  const formatTimeValue = (val) => {
    // Remove caracteres não numéricos, exceto ':'
    let cleaned = val.replace(/[^\d:]/g, '');

    // Se não tiver ':', adiciona após os primeiros 2 dígitos
    if (cleaned.length > 2 && !cleaned.includes(':')) {
      cleaned = cleaned.substring(0, 2) + ':' + cleaned.substring(2);
    }

    // Limita o tamanho total a 5 caracteres (HH:MM)
    if (cleaned.length > 5) {
      cleaned = cleaned.substring(0, 5);
    }

    // Valida horas e minutos
    if (cleaned.includes(':')) {
      const [hours, minutes] = cleaned.split(':');

      // Valida horas (0-23)
      if (hours.length === 2 && parseInt(hours) > 23) {
        cleaned = '23' + cleaned.substring(2);
      }

      // Valida minutos (0-59)
      if (minutes && minutes.length === 2 && parseInt(minutes) > 59) {
        cleaned = cleaned.substring(0, 3) + '59';
      }
    }

    return cleaned;
  };

  // Manipulador de mudança do input
  const handleChange = (e) => {
    console.log('TimeInput handleChange - valor original:', e.target.value);
    const newValue = formatTimeValue(e.target.value);
    console.log('TimeInput handleChange - valor formatado:', newValue);

    // Atualiza apenas o estado interno, sem notificar o componente pai ainda
    // Isso evita que o React recrie o componente e cause perda de foco
    setInputValue(newValue);
    console.log('TimeInput handleChange - estado interno atualizado');
  };

  // Verifica se o formato do horário é válido (HH:MM)
  const isValidTimeFormat = (val) => {
    if (!val) return true; // Vazio é considerado válido

    // Verifica o formato HH:MM
    const regex = /^([0-1][0-9]|2[0-3]):([0-5][0-9])$/;
    return regex.test(val);
  };

  // Manipulador de teclas
  const handleKeyDown = (e) => {
    // Adiciona ':' automaticamente após digitar 2 números
    if (
      e.target.value.length === 2 &&
      !e.target.value.includes(':') &&
      e.key !== 'Backspace' &&
      e.key !== 'Delete' &&
      e.key !== 'Tab' &&
      e.key !== ':'
    ) {
      const newValue = e.target.value + ':';
      setInputValue(newValue);
      e.preventDefault();

      // Posiciona o cursor após o ':'
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.selectionStart = 3;
          inputRef.current.selectionEnd = 3;
        }
      }, 0);
    }

    // Permite teclas de navegação e edição
    if (
      ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End', 'Tab', 'Backspace', 'Delete'].includes(e.key) ||
      (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key.toLowerCase()))
    ) {
      return;
    }

    // Permite apenas números e ':'
    if (!/[0-9:]/.test(e.key)) {
      e.preventDefault();
    }
  };

  // Manipulador de foco
  const handleFocus = (e) => {
    // Seleciona todo o texto ao focar
    e.target.select();
  };

  // Manipulador de perda de foco
  const handleBlur = () => {
    console.log('TimeInput handleBlur - valor atual:', inputValue);

    // Formata o valor final ao perder o foco
    if (inputValue && !isValidTimeFormat(inputValue)) {
      // Se o valor não estiver completo, tenta completá-lo
      let formattedValue = inputValue;

      if (formattedValue.length === 1) {
        // Um dígito: assume como hora e adiciona minutos zerados
        formattedValue = '0' + formattedValue + ':00';
      } else if (formattedValue.length === 2) {
        // Dois dígitos: assume como hora e adiciona minutos zerados
        formattedValue = formattedValue + ':00';
      } else if (formattedValue.includes(':')) {
        const [hours, minutes] = formattedValue.split(':');

        // Completa horas se necessário
        let formattedHours = hours;
        if (hours.length === 1) {
          formattedHours = '0' + hours;
        }

        // Completa minutos se necessário
        let formattedMinutes = minutes || '00';
        if (minutes && minutes.length === 1) {
          formattedMinutes = minutes + '0';
        }

        formattedValue = formattedHours + ':' + formattedMinutes;
      }

      // Atualiza o valor interno
      if (isValidTimeFormat(formattedValue)) {
        console.log('TimeInput handleBlur - valor formatado:', formattedValue);
        setInputValue(formattedValue);
        // Notifica o componente pai com o valor formatado
        onChange(formattedValue);
      }
    } else if (inputValue) { // Só notifica se houver um valor válido
      // Se o valor já estiver no formato correto, notifica o componente pai
      console.log('TimeInput handleBlur - notificando componente pai com valor atual');
      onChange(inputValue);
    }
  };

  return (
    <div className="flex flex-col">
      <input
        ref={inputRef}
        type="text"
        value={inputValue}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        id={id}
        name={name}
        className={className}
        maxLength={5}
      />
      {error && <p className={errorClassName}>{error}</p>}
    </div>
  );
};

export default TimeInput;
