'use client';

import React, { forwardRef } from 'react';

/**
 * Componente de input que se adapta à cor do módulo
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {string} props.type - Tipo do input (text, email, password, etc)
 * @param {string} props.placeholder - Placeholder do input
 * @param {string} props.value - Valor do input
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {boolean} props.disabled - Se o input está desabilitado
 * @param {boolean} props.required - Se o input é obrigatório
 * @param {string} props.name - Nome do input
 * @param {string} props.id - ID do input
 * @param {boolean} props.error - Se o input tem erro
 * @param {string} props.errorMessage - Mensagem de erro
 * @param {React.ReactNode} props.leftIcon - Ícone à esquerda
 * @param {React.ReactNode} props.rightIcon - Ícone à direita
 * @param {string} props.className - Classes adicionais
 */
const ModuleInput = forwardRef(({
  moduleColor = 'default',
  type = 'text',
  placeholder,
  value,
  onChange,
  disabled = false,
  required = false,
  name,
  id,
  error = false,
  errorMessage,
  leftIcon,
  rightIcon,
  className = '',
  ...rest
}, ref) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      focusRing: 'outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-primary-500 focus-visible:!border-primary-500 dark:focus-visible:!ring-primary-400 dark:focus-visible:!border-primary-400',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-gray-400 dark:text-gray-500',
    },
    people: {
      focusRing: 'outline-none focus:ring-1 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-people-border focus-visible:!border-module-people-border dark:focus-visible:!ring-module-people-border-dark dark:focus-visible:!border-module-people-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-people-icon dark:text-module-people-icon-dark',
    },
    scheduler: {
      focusRing: 'outline-none focus:ring-1 focus:ring-module-scheduler-border focus:border-module-scheduler-border dark:focus:ring-module-scheduler-border-dark dark:focus:border-module-scheduler-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-scheduler-border focus-visible:!border-module-scheduler-border dark:focus-visible:!ring-module-scheduler-border-dark dark:focus-visible:!border-module-scheduler-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark',
    },
    admin: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-admin-border focus:border-module-admin-border dark:focus:ring-module-admin-border-dark dark:focus:border-module-admin-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-admin-border focus-visible:!border-module-admin-border dark:focus-visible:!ring-module-admin-border-dark dark:focus-visible:!border-module-admin-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-admin-icon dark:text-module-admin-icon-dark',
    },
    financial: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-financial-border focus:border-module-financial-border dark:focus:ring-module-financial-border-dark dark:focus:border-module-financial-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-financial-border focus-visible:!border-module-financial-border dark:focus-visible:!ring-module-financial-border-dark dark:focus-visible:!border-module-financial-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-financial-icon dark:text-module-financial-icon-dark',
    },
    abaplus: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-abaplus-border focus:border-module-abaplus-border dark:focus:ring-module-abaplus-border-dark dark:focus:border-module-abaplus-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-abaplus-border focus-visible:!border-module-abaplus-border dark:focus-visible:!ring-module-abaplus-border-dark dark:focus-visible:!border-module-abaplus-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-abaplus-icon dark:text-module-abaplus-icon-dark',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;

  // Classes base para todos os inputs
  const baseClasses = 'w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 outline-none';

  // Classes para o estado de foco
  const focusClasses = error ? colors.errorRing : colors.focusRing;

  // Classes para o estado de erro
  const errorClasses = error ? colors.errorBorder : '';

  // Classes para o padding com ícones
  const paddingClasses = leftIcon ? 'pl-10' : '';

  // Combinar todas as classes
  const inputClasses = `${baseClasses} ${focusClasses} ${errorClasses} ${paddingClasses} ${className}`;

  return (
    <div className="relative">
      {leftIcon && (
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <span className={colors.iconColor}>
            {leftIcon}
          </span>
        </div>
      )}

      <input
        ref={ref}
        type={type}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        name={name}
        id={id}
        className={inputClasses}
        {...rest}
      />

      {rightIcon && (
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
          {rightIcon}
        </div>
      )}

      {error && errorMessage && (
        <p className="mt-1 text-xs text-red-600 dark:text-red-400">
          {errorMessage}
        </p>
      )}
    </div>
  );
});

ModuleInput.displayName = 'ModuleInput';

export default ModuleInput;
