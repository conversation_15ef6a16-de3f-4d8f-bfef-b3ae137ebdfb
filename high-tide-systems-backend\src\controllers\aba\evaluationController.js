// src/controllers/aba/evaluationController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createEvaluationValidation = [
  body("type").isIn(["SKILL_ACQUISITION", "BEHAVIOR_REDUCTION"]).withMessage("Tipo de avaliação inválido"),
  body("name").notEmpty().withMessage("Nome da avaliação é obrigatório"),
  body("observations").optional(),
];

const updateEvaluationValidation = [
  body("type").optional().isIn(["SKILL_ACQUISITION", "BEHAVIOR_REDUCTION"]).withMessage("Tipo de avaliação inválido"),
  body("name").optional().notEmpty().withMessage("Nome da avaliação é obrigatório"),
  body("observations").optional(),
  body("active").optional().isBoolean().withMessage("O campo active deve ser um booleano"),
];

class EvaluationController {
  /**
   * Cria uma nova avaliação
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { type, name, observations } = req.body;

      // Criar avaliação
      const evaluation = await prisma.evaluation.create({
        data: {
          type,
          name,
          observations,
          companyId: req.user.companyId,
          createdById: req.user.id,
        },
      });

      res.status(201).json(evaluation);
    } catch (error) {
      console.error("Erro ao criar avaliação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todas as avaliações com filtros e paginação
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search = "",
        type,
        active = "true",
      } = req.query;

      // Construir filtros
      const where = {
        companyId: req.user.companyId,
        deletedAt: null,
      };

      // Filtro de busca
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { observations: { contains: search, mode: "insensitive" } },
        ];
      }

      // Filtro de tipo
      if (type) {
        where.type = type;
      }

      // Filtro de status
      if (active !== "all") {
        where.active = active === "true";
      }

      // Contar total de registros
      const total = await prisma.evaluation.count({ where });

      // Buscar avaliações com paginação
      const evaluations = await prisma.evaluation.findMany({
        where,
        include: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          levels: {
            select: {
              id: true,
              order: true,
              description: true,
              ageRange: true,
            },
            orderBy: {
              order: "asc",
            },
          },
          skills: {
            select: {
              skill: {
                select: {
                  id: true,
                  code: true,
                  order: true,
                  description: true,
                },
              },
            },
          },
          scores: {
            select: {
              id: true,
              type: true,
              value: true,
              description: true,
            },
          },
          tasks: {
            select: {
              id: true,
              order: true,
              name: true,
              milestone: true,
              item: true,
              question: true,
              example: true,
              criteria: true,
              objective: true,
              skillId: true,
              levelId: true,
            },
            orderBy: {
              order: "asc",
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar dados para o frontend
      const formattedEvaluations = evaluations.map(evaluation => ({
        ...evaluation,
        // Transformar a relação skills para um formato mais simples
        skills: evaluation.skills.map(s => s.skill),
      }));

      // Formatar resposta
      const response = formatSuccessResponse(
        formattedEvaluations,
        "evaluations",
        total,
        Math.ceil(total / Number(limit))
      );

      res.json(response);
    } catch (error) {
      console.error("Erro ao listar avaliações:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém uma avaliação específica pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const evaluation = await prisma.evaluation.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          levels: {
            select: {
              id: true,
              order: true,
              description: true,
              ageRange: true,
            },
            orderBy: {
              order: "asc",
            },
          },
          skills: {
            select: {
              skill: {
                select: {
                  id: true,
                  code: true,
                  order: true,
                  description: true,
                },
              },
            },
          },
          scores: {
            select: {
              id: true,
              type: true,
              value: true,
              description: true,
            },
          },
          tasks: {
            select: {
              id: true,
              order: true,
              name: true,
              milestone: true,
              item: true,
              question: true,
              example: true,
              criteria: true,
              objective: true,
              skillId: true,
              levelId: true,
              skill: {
                select: {
                  id: true,
                  description: true,
                },
              },
              level: {
                select: {
                  id: true,
                  description: true,
                },
              },
            },
            orderBy: {
              order: "asc",
            },
          },
        },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para visualizar esta avaliação" });
      }

      // Formatar dados para o frontend
      const formattedEvaluation = {
        ...evaluation,
        // Transformar a relação skills para um formato mais simples
        skills: evaluation.skills.map(s => s.skill),
      };

      res.json(formattedEvaluation);
    } catch (error) {
      console.error("Erro ao buscar avaliação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza uma avaliação existente
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Verificar se a avaliação existe
      const existingEvaluation = await prisma.evaluation.findUnique({
        where: { id },
      });

      if (!existingEvaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingEvaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para atualizar esta avaliação" });
      }

      const { type, name, observations, active } = req.body;

      // Atualizar avaliação
      const updatedEvaluation = await prisma.evaluation.update({
        where: { id },
        data: {
          type: type !== undefined ? type : undefined,
          name: name !== undefined ? name : undefined,
          observations: observations !== undefined ? observations : undefined,
          active: active !== undefined ? active : undefined,
          updatedAt: new Date(),
        },
      });

      res.json(updatedEvaluation);
    } catch (error) {
      console.error("Erro ao atualizar avaliação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status de uma avaliação (ativo/inativo)
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a avaliação existe
      const evaluation = await prisma.evaluation.findUnique({
        where: { id },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para alterar esta avaliação" });
      }

      // Alternar status
      const updatedEvaluation = await prisma.evaluation.update({
        where: { id },
        data: {
          active: !evaluation.active,
          updatedAt: new Date(),
        },
      });

      res.json(updatedEvaluation);
    } catch (error) {
      console.error("Erro ao alternar status da avaliação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove uma avaliação (soft delete)
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a avaliação existe
      const evaluation = await prisma.evaluation.findUnique({
        where: { id },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para remover esta avaliação" });
      }

      // Soft delete
      await prisma.evaluation.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          active: false,
        },
      });

      res.json({ message: "Avaliação removida com sucesso" });
    } catch (error) {
      console.error("Erro ao remover avaliação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  EvaluationController,
  createEvaluationValidation,
  updateEvaluationValidation,
};
