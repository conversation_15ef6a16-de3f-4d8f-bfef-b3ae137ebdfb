"use client";

import { useState } from "react";
import { InputMask } from "@react-input/mask";
import {
  UserPlus,
  Mail,
  Lock,
  User,
  Phone,
  MapPin,
  CreditCard,
  Calendar,
} from "lucide-react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { api } from "@/utils/api";

export default function SignupPage() {
  const router = useRouter();
  const [documentType, setDocumentType] = useState("cpf");
  const [formData, setFormData] = useState({
    login: "",
    fullName: "",
    email: "",
    password: "",
    confirmPassword: "",
    cpf: "",
    cnpj: "",
    birthDate: "",
    address: "",
    phone: "",
  });

  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Limpa o erro do campo quando o usuário começa a digitar
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleDocumentTypeChange = (e) => {
    setDocumentType(e.target.value);
    // Limpa os valores de CPF e CNPJ quando troca o tipo
    setFormData((prev) => ({
      ...prev,
      cpf: "",
      cnpj: "",
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.login) newErrors.login = "Login é obrigatório";
    if (!formData.fullName) newErrors.fullName = "Nome é obrigatório";
    if (!formData.email || !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }
    if (!formData.password || formData.password.length < 6) {
      newErrors.password = "Senha deve ter no mínimo 6 caracteres";
    }
    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = "Senhas não conferem";
    }

    if (documentType === "cpf" && !formData.cpf) {
      newErrors.cpf = "CPF é obrigatório";
    }
    if (documentType === "cnpj" && !formData.cnpj) {
      newErrors.cnpj = "CNPJ é obrigatório";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const removeMask = (value) => value?.replace(/\D/g, "") || "";

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    try {
      const response = await api.post("/auth/register", {
        login: formData.login,
        fullName: formData.fullName,
        email: formData.email,
        password: formData.password,
        cpf: documentType === "cpf" ? removeMask(formData.cpf) : undefined,
        cnpj: documentType === "cnpj" ? removeMask(formData.cnpj) : undefined,
        birthDate: formData.birthDate || undefined,
        address: formData.address || undefined,
        phone: formData.phone ? removeMask(formData.phone) : undefined,
      });

      router.push("/login?registered=true");
    } catch (error) {
      setErrors((prev) => ({
        ...prev,
        submit: error.response?.data?.message || "Erro ao criar conta",
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const inputClasses =
    "block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 bg-white text-gray-900";
  const labelClasses = "block text-sm font-medium text-gray-700 mb-1";
  const iconContainerClasses =
    "absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none";

  return (
    <div className="flex items-center justify-center min-h-screen p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-4xl p-8 space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center items-center">
            <div className="flex justify-center">
              <UserPlus className="h-12 w-12 text-orange-500" />
            </div>
            <h2 className="mx-4 text-3xl font-bold text-gray-900">
              Crie sua conta
            </h2>
          </div>
          <p className="mt-2 text-sm text-gray-600">
            Já tem uma conta?{" "}
            <Link
              href="/login"
              className="font-medium text-orange-500 hover:text-orange-600 transition-colors"
            >
              Faça login
            </Link>
          </p>
        </div>

        {errors.submit && (
          <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg">
            {errors.submit}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Login */}
            <div>
              <label className={labelClasses} htmlFor="login">
                Login
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <User className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="login"
                  name="login"
                  type="text"
                  value={formData.login}
                  onChange={handleChange}
                  required
                  className={cn(inputClasses, errors.login && "border-red-500")}
                  placeholder="Seu login"
                  disabled={isLoading}
                />
              </div>
              {errors.login && (
                <p className="mt-1 text-xs text-red-600">{errors.login}</p>
              )}
            </div>

            {/* Nome completo */}
            <div>
              <label className={labelClasses} htmlFor="fullName">
                Nome completo
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <User className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="fullName"
                  name="fullName"
                  type="text"
                  value={formData.fullName}
                  onChange={handleChange}
                  required
                  className={cn(
                    inputClasses,
                    errors.fullName && "border-red-500"
                  )}
                  placeholder="Seu nome completo"
                  disabled={isLoading}
                />
              </div>
              {errors.fullName && (
                <p className="mt-1 text-xs text-red-600">{errors.fullName}</p>
              )}
            </div>

            {/* Email */}
            <div>
              <label className={labelClasses} htmlFor="email">
                Email
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <Mail className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className={cn(inputClasses, errors.email && "border-red-500")}
                  placeholder="<EMAIL>"
                  disabled={isLoading}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-xs text-red-600">{errors.email}</p>
              )}
            </div>

            {/* Telefone */}
            <div>
              <label className={labelClasses} htmlFor="phone">
                Telefone
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <Phone className="h-5 w-5 text-gray-400" />
                </div>
                <InputMask
                  mask="(99) 99999-9999"
                  replacement={{ 9: /[0-9]/ }}
                  showMask
                  placeholder="(00) 00000-0000"
                  className={cn(inputClasses, errors.phone && "border-red-500")}
                  value={formData.phone}
                  onChange={(e) =>
                    handleChange({
                      target: { name: "phone", value: e.target.value },
                    })
                  }
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Seletor de Tipo de Documento */}
            {/* <div>
              <label className={labelClasses}>Tipo de Documento</label>
              <div className="flex space-x-6">
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="cpf"
                    name="documentType"
                    value="cpf"
                    checked={documentType === "cpf"}
                    onChange={handleDocumentTypeChange}
                    className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300"
                    disabled={isLoading}
                  />
                  <label htmlFor="cpf" className="ml-2 text-sm text-gray-700">
                    CPF
                  </label>
                </div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="cnpj"
                    name="documentType"
                    value="cnpj"
                    checked={documentType === "cnpj"}
                    onChange={handleDocumentTypeChange}
                    className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300"
                    disabled={isLoading}
                  />
                  <label htmlFor="cnpj" className="ml-2 text-sm text-gray-700">
                    CNPJ
                  </label>
                </div>
              </div>
            </div> */}

            {/* Campo de Documento (CPF ou CNPJ) */}
            <div>
              <label className={labelClasses} htmlFor={documentType}>
                {documentType.toUpperCase()}
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <CreditCard className="h-5 w-5 text-gray-400" />
                </div>
                <InputMask
                  mask={
                    documentType === "cpf"
                      ? "999.999.999-99"
                      : "99.999.999/9999-99"
                  }
                  replacement={{ 9: /[0-9]/ }}
                  showMask
                  placeholder={
                    documentType === "cpf"
                      ? "000.000.000-00"
                      : "00.000.000/0000-00"
                  }
                  className={cn(
                    inputClasses,
                    (documentType === "cpf" && errors.cpf) ||
                      (documentType === "cnpj" && errors.cnpj)
                      ? "border-red-500"
                      : ""
                  )}
                  value={documentType === "cpf" ? formData.cpf : formData.cnpj}
                  onChange={(e) =>
                    handleChange({
                      target: {
                        name: documentType,
                        value: e.target.value,
                      },
                    })
                  }
                  disabled={isLoading}
                />
              </div>
              {documentType === "cpf" && errors.cpf && (
                <p className="mt-1 text-xs text-red-600">{errors.cpf}</p>
              )}
              {documentType === "cnpj" && errors.cnpj && (
                <p className="mt-1 text-xs text-red-600">{errors.cnpj}</p>
              )}
              {/* <div className="flex place-content-around mt-2">
                <div>
                  <input
                    type="radio"
                    id="cpf"
                    name="documentType"
                    value="cpf"
                    checked={documentType === "cpf"}
                    onChange={handleDocumentTypeChange}
                    className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300"
                    disabled={isLoading}
                  />
                  <label htmlFor="cpf" className="ml-2 text-sm text-gray-700">
                    CPF
                  </label>
                </div>
                <div>
                  <input
                    type="radio"
                    id="cnpj"
                    name="documentType"
                    value="cnpj"
                    checked={documentType === "cnpj"}
                    onChange={handleDocumentTypeChange}
                    className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300"
                    disabled={isLoading}
                  />
                  <label htmlFor="cnpj" className="ml-2 text-sm text-gray-700">
                    CNPJ
                  </label>
                </div>
              </div> */}
            </div>

            {/* Data de Nascimento */}
            <div>
              <label className={labelClasses} htmlFor="birthDate">
                Data de Nascimento
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <Calendar className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="birthDate"
                  name="birthDate"
                  type="date"
                  value={formData.birthDate}
                  onChange={handleChange}
                  className={inputClasses}
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Endereço */}
            <div>
              <label className={labelClasses} htmlFor="address">
                Endereço
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <MapPin className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="address"
                  name="address"
                  type="text"
                  value={formData.address}
                  onChange={handleChange}
                  className={inputClasses}
                  placeholder="Seu endereço"
                  disabled={isLoading}
                />
              </div>
            </div>

            {/* Senha */}
            <div>
              <label className={labelClasses} htmlFor="password">
                Senha
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className={cn(
                    inputClasses,
                    errors.password && "border-red-500"
                  )}
                  placeholder="••••••"
                  disabled={isLoading}
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Mínimo de 6 caracteres
              </p>
              {errors.password && (
                <p className="mt-1 text-xs text-red-600">{errors.password}</p>
              )}
            </div>

            {/* Confirmar Senha */}
            <div>
              <label className={labelClasses} htmlFor="confirmPassword">
                Confirme a senha
              </label>
              <div className="relative">
                <div className={iconContainerClasses}>
                  <Lock className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                  className={cn(
                    inputClasses,
                    errors.confirmPassword && "border-red-500"
                  )}
                  placeholder="••••••"
                  disabled={isLoading}
                />
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-xs text-red-600">
                  {errors.confirmPassword}
                </p>
              )}
            </div>
          </div>

          {/* Termos e Condições */}
          <div className="flex items-center">
            <input
              id="terms"
              name="terms"
              type="checkbox"
              required
              className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded"
              disabled={isLoading}
            />
            <label htmlFor="terms" className="ml-2 block text-sm text-gray-700">
              Eu concordo com os{" "}
              <Link
                href="/terms"
                className="font-medium text-orange-500 hover:text-orange-600 transition-colors"
              >
                Termos de Serviço
              </Link>{" "}
              e{" "}
              <Link
                href="/privacy"
                className="font-medium text-orange-500 hover:text-orange-600 transition-colors"
              >
                Política de Privacidade
              </Link>
            </label>
          </div>

          {/* Botão de Submit */}
          <button
            type="submit"
            disabled={isLoading}
            className={cn(
              "w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-orange-500 transition-colors",
              isLoading
                ? "opacity-50 cursor-not-allowed"
                : "hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            )}
          >
            {isLoading ? "Criando conta..." : "Criar conta"}
          </button>
        </form>
      </div>
    </div>
  );
}
