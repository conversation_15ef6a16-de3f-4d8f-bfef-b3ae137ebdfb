"use client";

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { getModuleRedirectionPath } from '@/utils/moduleRedirection';

export default function PeopleModuleRoute() {
  const router = useRouter();

  useEffect(() => {
    // Obtém o caminho de redirecionamento com base nas preferências do usuário
    const redirectPath = getModuleRedirectionPath(
      'people',
      '/dashboard/people/introduction', // Agora temos uma página de introdução
      '/dashboard/people/clients'
    );

    // Redireciona para o caminho determinado
    router.push(redirectPath);
  }, [router]);

  return <div>Carregando...</div>;
}