"use client";

import React, { useState, useEffect } from "react";
import {
  Building,
  Search,
  Plus,
  Edit,
  Trash,
  MapPin,
  Phone,
  Mail,
  Star,
  Eye,
  Check,
  X,
  Filter,
  RefreshCw,
  Loader2
} from "lucide-react";
import { ModuleSelect, ModuleTable } from "@/components/ui";
import { useAuth } from "@/contexts/AuthContext";
import { branchService } from "@/app/modules/admin/services/branchService";
import { companyService } from "@/app/modules/admin/services/companyService";
import BranchFormModal from "./BranchFormModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";

const BranchesTab = () => {
  const { user } = useAuth();
  const [branches, setBranches] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalBranches, setTotalBranches] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState("");
  const [companyFilter, setCompanyFilter] = useState("");
  const [companies, setCompanies] = useState([]);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [selectedBranch, setSelectedBranch] = useState(null);
  const [formModalOpen, setFormModalOpen] = useState(false);

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  useEffect(() => {
    // If user is a company admin, set companyFilter to their company
    if (user?.role === 'COMPANY_ADMIN' && user?.companyId) {
      setCompanyFilter(user.companyId);
    }

    if (isSystemAdmin) {
      loadCompanies();
    }

    loadBranches();
  }, [user]);

  const loadCompanies = async () => {
    try {
      const response = await companyService.getCompanies({
        active: true,
        limit: 100 // Get a large number to populate dropdown
      });

      setCompanies(response.companies || []);
    } catch (error) {
      console.error("Error loading companies:", error);
    }
  };

  const loadBranches = async (page = currentPage) => {
    setIsLoading(true);
    try {
      const params = {
        page,
        limit: 10,
        search: search || undefined,
        active: statusFilter || undefined,
        companyId: companyFilter || (user?.role === 'COMPANY_ADMIN' ? user.companyId : undefined)
      };

      const response = await branchService.getBranches(params);

      setBranches(response.branches || []);
      setTotalBranches(response.total || 0);
      setTotalPages(response.pages || 1);
      setCurrentPage(page);
    } catch (error) {
      console.error("Error loading branches:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    loadBranches(1);
  };

  const handleResetFilters = () => {
    setSearch("");
    setStatusFilter("");
    if (isSystemAdmin) {
      setCompanyFilter("");
    }
    loadBranches(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
    loadBranches(page);
  };

  const handleAddBranch = () => {
    setSelectedBranch(null);
    setFormModalOpen(true);
  };

  const handleEditBranch = (branch) => {
    setSelectedBranch(branch);
    setFormModalOpen(true);
  };

  const handleToggleStatus = (branch) => {
    setSelectedBranch(branch);
    setActionToConfirm({
      type: "toggle-status",
      message: `${branch.active ? "Desativar" : "Ativar"} a unidade ${branch.name}?`
    });
    setConfirmationDialogOpen(true);
  };

  const handleSetHeadquarters = (branch) => {
    if (branch.isHeadquarters) return; // Already HQ

    setSelectedBranch(branch);
    setActionToConfirm({
      type: "set-headquarters",
      message: `Definir ${branch.name} como a matriz/sede principal?`,
      variant: "info"
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteBranch = (branch) => {
    setSelectedBranch(branch);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente a unidade ${branch.name}?`,
      variant: "danger"
    });
    setConfirmationDialogOpen(true);
  };

  const confirmAction = async () => {
    if (!selectedBranch) return;

    try {
      if (actionToConfirm.type === "toggle-status") {
        await branchService.toggleBranchStatus(selectedBranch.id);
      } else if (actionToConfirm.type === "delete") {
        await branchService.deleteBranch(selectedBranch.id);
      } else if (actionToConfirm.type === "set-headquarters") {
        await branchService.setHeadquarters(selectedBranch.id);
      }

      // Reload branches after action
      loadBranches();
    } catch (error) {
      console.error("Error performing action:", error);
      alert(error.response?.data?.message || "Ocorreu um erro ao executar esta ação");
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-100 flex items-center gap-2">
          <Building className="h-5 w-5 text-primary-500 dark:text-primary-400" />
          Gerenciamento de Unidades
        </h3>

        <button
          onClick={handleAddBranch}
          className="flex items-center gap-2 px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>Nova Unidade</span>
        </button>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-xl dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-4">
        <form
          onSubmit={handleSearch}
          className="flex flex-col md:flex-row gap-4"
        >
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
            <input
              type="text"
              placeholder="Buscar por nome, código ou endereço..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <div className="w-full sm:w-40">
              <ModuleSelect
                moduleColor="admin"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                placeholder="Status"
              >
                <option value="">Todos os status</option>
                <option value="true">Ativas</option>
                <option value="false">Inativas</option>
              </ModuleSelect>
            </div>

            {isSystemAdmin && (
              <div className="w-full sm:w-48">
                <ModuleSelect
                  moduleColor="admin"
                  value={companyFilter}
                  onChange={(e) => setCompanyFilter(e.target.value)}
                  placeholder="Empresas"
                >
                  <option value="">Todas as empresas</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            )}

            <button
              type="submit"
              className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
            >
              <Filter className="sm:hidden h-5 w-5" />
              <span className="hidden sm:inline">Filtrar</span>
            </button>

            <button
              type="button"
              onClick={handleResetFilters}
              className="px-4 py-2 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-gray-300 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 transition-colors"
            >
              <RefreshCw className="sm:hidden h-5 w-5" />
              <span className="hidden sm:inline">Limpar</span>
            </button>
          </div>
        </form>
      </div>

      {/* Branches List */}
      <ModuleTable
        moduleColor="admin"
        title="Unidades"
        columns={[
          { header: 'Unidade', field: 'name', width: '20%' },
          { header: 'Endereço', field: 'address', width: '20%' },
          { header: 'Contato', field: 'contact', width: '15%' },
          ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '15%' }] : []),
          { header: 'Status', field: 'status', width: '10%' },
          { header: 'Ações', field: 'actions', width: '15%', sortable: false }
        ]}
        data={branches}
        isLoading={isLoading}
        emptyMessage="Nenhuma unidade encontrada"
        emptyIcon={<Building size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalBranches}
        onPageChange={handlePageChange}
        showPagination={totalPages > 1}
        tableId="admin-branches-table"
        enableColumnToggle={true}
        renderRow={(branch, index, moduleColors, visibleColumns) => (
          <tr key={branch.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('name') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 flex items-center justify-center">
                    <div className={`p-2 rounded-full ${branch.isHeadquarters ? 'bg-amber-100 dark:bg-amber-900/50 text-amber-600 dark:text-amber-400' : 'bg-neutral-100 dark:bg-gray-700 text-neutral-600 dark:text-gray-400'}`}>
                      <Building className="h-5 w-5" />
                    </div>
                  </div>
                  <div className="ml-3">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-neutral-800 dark:text-neutral-100">{branch.name}</div>
                      {branch.isHeadquarters && (
                        <div className="ml-2 px-2 py-0.5 bg-amber-100 dark:bg-amber-900/50 text-amber-800 dark:text-amber-300 rounded-full text-xs flex items-center">
                          <Star className="h-3 w-3 mr-1" />
                          Matriz
                        </div>
                      )}
                    </div>
                    {branch.code && (
                      <div className="text-xs text-neutral-500 dark:text-neutral-400">Código: {branch.code}</div>
                    )}
                  </div>
                </div>
              </td>
            )}
            {visibleColumns.includes('address') && (
              <td className="px-4 py-4">
                <div className="text-sm text-neutral-600 dark:text-neutral-300 flex items-center">
                  <MapPin className="h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                  <div>
                    <div>{branch.address}</div>
                    {branch.city && branch.state && (
                      <div className="text-xs text-neutral-500 dark:text-neutral-400">{branch.city}, {branch.state}</div>
                    )}
                  </div>
                </div>
              </td>
            )}
            {visibleColumns.includes('contact') && (
              <td className="px-4 py-4">
                {branch.phone && (
                  <div className="text-sm text-neutral-600 dark:text-neutral-300 flex items-center mb-1">
                    <Phone className="h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                    {branch.phone}
                  </div>
                )}
                {branch.email && (
                  <div className="text-sm text-neutral-600 dark:text-neutral-300 flex items-center">
                    <Mail className="h-4 w-4 mr-1 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                    {branch.email}
                  </div>
                )}
              </td>
            )}
            {isSystemAdmin && visibleColumns.includes('company') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                {branch.company?.name || 'N/A'}
              </td>
            )}
            {visibleColumns.includes('status') && (
              <td className="px-4 py-4">
                <span
                  className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    branch.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                  }`}
                >
                  {branch.active ? "Ativa" : "Inativa"}
                </span>
              </td>
            )}
            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right">
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => handleEditBranch(branch)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>

                  {!branch.isHeadquarters && (
                    <button
                      onClick={() => handleSetHeadquarters(branch)}
                      className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400 transition-colors"
                      title="Definir como Matriz"
                    >
                      <Star size={16} />
                    </button>
                  )}

                  <button
                    onClick={() => handleToggleStatus(branch)}
                    className={`p-1 transition-colors ${
                      branch.active
                        ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                        : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                    }`}
                    title={branch.active ? "Desativar" : "Ativar"}
                  >
                    {branch.active ? (
                      <X size={16} />
                    ) : (
                      <Check size={16} />
                    )}
                  </button>

                  <button
                    onClick={() => handleDeleteBranch(branch)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />

      {/* Modals */}
      {formModalOpen && (
        <BranchFormModal
          isOpen={formModalOpen}
          onClose={() => setFormModalOpen(false)}
          branch={selectedBranch}
          onSuccess={() => {
            setFormModalOpen(false);
            loadBranches();
          }}
        />
      )}

      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title={actionToConfirm?.type === "set-headquarters" ? "Definir Matriz" : "Confirmar ação"}
        message={actionToConfirm?.message || ""}
        variant={actionToConfirm?.variant || "warning"}
      />
    </div>
  );
};

export default BranchesTab;