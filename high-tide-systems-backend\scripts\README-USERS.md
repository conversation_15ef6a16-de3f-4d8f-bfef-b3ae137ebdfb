# Script de Cadastro de Usuários

Este script cadastra usuários para todas as empresas existentes no sistema, com base nas profissões previamente cadastradas.

## Características

- Cria entre 1 e 3 usuários para cada profissão selecionada em cada unidade
- Atribui módulos e permissões específicas com base no tipo de profissão
- Gera dados realistas como nome, email, login e telefone
- Verifica duplicidade para não criar usuários repetidos

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-users-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed-users.js
```

### Opção 3: Executando dentro do container Docker

```bash
docker exec -it high-tide-systems-api node prisma/seed-users.js
```

## Detalhes do Script

O script realiza as seguintes operações:

1. <PERSON><PERSON> todas as empresas ativas no sistema (exceto a empresa de teste)
2. Para cada empresa, busca todas as profissões cadastradas
3. Para cada unidade da empresa, seleciona até 10 profissões aleatoriamente
4. Para cada profissão selecionada, cria entre 1 e 3 usuários com:
   - Nome completo aleatório
   - Email baseado no nome e domínio da empresa
   - Login baseado no nome
   - Telefone aleatório
   - Senha padrão: 123456 (hash pré-gerado)
   - Módulos e permissões específicas com base no tipo de profissão

## Módulos e Permissões

O script atribui módulos e permissões específicas com base no tipo de profissão:

### Área médica
- **Médico**: Módulos BASIC, SCHEDULING; Permissões para visualizar agendamentos, pacientes e gerenciar prontuários
- **Dentista**: Módulos BASIC, SCHEDULING; Permissões para visualizar agendamentos, pacientes e gerenciar prontuários
- **Enfermeiro**: Módulos BASIC, SCHEDULING; Permissões para visualizar agendamentos, pacientes, gerenciar prontuários e suprimentos
- **Fisioterapeuta**: Módulos BASIC, SCHEDULING; Permissões para visualizar agendamentos, pacientes e gerenciar prontuários
- **Psicólogo**: Módulos BASIC, SCHEDULING; Permissões para visualizar agendamentos, pacientes e gerenciar prontuários

### Área administrativa
- **Recepcionista**: Módulos BASIC, SCHEDULING, ADMIN; Permissões para gerenciar agendamentos e pacientes
- **Secretário(a)**: Módulos BASIC, SCHEDULING, ADMIN; Permissões para gerenciar agendamentos, pacientes e visualizar relatórios
- **Assistente Administrativo**: Módulos BASIC, ADMIN; Permissões para gerenciar pacientes e visualizar relatórios

### Área financeira
- **Contador**: Módulos BASIC, FINANCIAL; Permissões para gerenciar relatórios financeiros e lançamentos
- **Analista Financeiro**: Módulos BASIC, FINANCIAL; Permissões para gerenciar relatórios financeiros e lançamentos
- **Tesoureiro**: Módulos BASIC, FINANCIAL; Permissões para gerenciar relatórios financeiros, lançamentos e aprovar pagamentos

### Área de RH
- **Analista de RH**: Módulos BASIC, RH, ADMIN; Permissões para gerenciar funcionários e relatórios
- **Recrutador**: Módulos BASIC, RH; Permissões para visualizar e criar funcionários, visualizar relatórios
- **Gestor de Treinamento**: Módulos BASIC, RH; Permissões para visualizar funcionários, relatórios e gerenciar treinamentos

### Área de TI
- **Analista de Sistemas**: Módulos BASIC, ADMIN; Permissões para visualizar logs do sistema, relatórios e gerenciar configurações
- **Suporte Técnico**: Módulos BASIC, ADMIN; Permissões para visualizar logs do sistema e relatórios
