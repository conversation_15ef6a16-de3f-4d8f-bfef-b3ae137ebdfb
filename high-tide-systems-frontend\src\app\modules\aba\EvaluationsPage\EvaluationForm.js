"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Save,
  ClipboardList,
  Layers,
  Plus,
  Trash,
  Edit,
  Activity,
  Link2,
  Percent,
  CheckSquare
} from "lucide-react";
import { ModuleModal, ModuleInput, ModuleSelect, ModuleTextarea, ModuleTabs, ModuleFormGroup, ModuleTable } from "@/components/ui";
import { useToast } from "@/contexts/ToastContext";
import { evaluationsService } from "@/app/modules/aba/services/evaluationsService";
import { skillsService } from "@/app/modules/aba/services/skillsService";
import { levelService } from "@/app/modules/aba/services/levelService";
import LevelFormModal from "./LevelFormModal";
import LevelSelectionModal from "./LevelSelectionModal";
import SkillFormModal from "./SkillFormModal";
import SkillSelectionModal from "./SkillSelectionModal";
import ScoreFormModal from "./ScoreFormModal";
import TaskFormModal from "./TaskFormModal";
import TaskSelectionModal from "./TaskSelectionModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";

const EvaluationForm = ({ isOpen, onClose, evaluation, onSuccess }) => {
  const { toast_error } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("general");
  const [formData, setFormData] = useState({
    type: "SKILL_ACQUISITION",
    name: "",
    observations: "",
    levels: [],
    skills: [],
    scores: [],
    tasks: []
  });
  const [errors, setErrors] = useState({});

  // Estados para níveis
  const [isLevelModalOpen, setIsLevelModalOpen] = useState(false);
  const [isLevelSelectionModalOpen, setIsLevelSelectionModalOpen] = useState(false);
  const [selectedLevel, setSelectedLevel] = useState(null);
  const [isDeleteLevelModalOpen, setIsDeleteLevelModalOpen] = useState(false);

  // Estados para habilidades
  const [isSkillModalOpen, setIsSkillModalOpen] = useState(false);
  const [isSkillSelectionModalOpen, setIsSkillSelectionModalOpen] = useState(false);
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [isDeleteSkillModalOpen, setIsDeleteSkillModalOpen] = useState(false);

  // Estados para pontuações
  const [isScoreModalOpen, setIsScoreModalOpen] = useState(false);
  const [selectedScore, setSelectedScore] = useState(null);
  const [isDeleteScoreModalOpen, setIsDeleteScoreModalOpen] = useState(false);

  // Estados para tarefas/testes
  const [isTaskModalOpen, setIsTaskModalOpen] = useState(false);
  const [isTaskSelectionModalOpen, setIsTaskSelectionModalOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [isDeleteTaskModalOpen, setIsDeleteTaskModalOpen] = useState(false);

  // Carregar dados da avaliação para edição
  useEffect(() => {
    if (evaluation) {
      setFormData({
        type: evaluation.type || "SKILL_ACQUISITION",
        name: evaluation.name || "",
        observations: evaluation.observations || "",
        levels: evaluation.levels || [],
        skills: evaluation.skills || [],
        scores: evaluation.scores || [],
        tasks: evaluation.tasks || []
      });
    }
  }, [evaluation]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Limpar erro do campo quando ele for alterado
    if (errors[name]) {
      setErrors({ ...errors, [name]: null });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.type) {
      newErrors.type = "O tipo de avaliação é obrigatório";
    }

    if (!formData.name || formData.name.trim() === "") {
      newErrors.name = "O nome da avaliação é obrigatório";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      setActiveTab("general");
      return;
    }

    try {
      setIsLoading(true);

      if (evaluation) {
        // Atualizar avaliação existente
        await evaluationsService.updateEvaluation(evaluation.id, formData);
      } else {
        // Criar nova avaliação
        await evaluationsService.createEvaluation(formData);
      }

      onSuccess();
    } catch (error) {
      console.error("Erro ao salvar avaliação:", error);
      toast_error("Não foi possível salvar a avaliação");
    } finally {
      setIsLoading(false);
    }
  };

  // Manipuladores para o modal de nível
  const handleOpenLevelModal = (level = null) => {
    setSelectedLevel(level);
    setIsLevelModalOpen(true);
  };

  const handleCloseLevelModal = () => {
    setSelectedLevel(null);
    setIsLevelModalOpen(false);
  };

  const handleSaveLevel = async (levelData) => {
    console.log("EvaluationForm - handleSaveLevel - Recebendo dados do nível:", levelData);
    console.log("EvaluationForm - Valor da variável evaluation:", evaluation);

    try {
      let updatedLevels;
      let savedLevel;

      if (selectedLevel) {
        // Atualizar nível existente
        console.log("EvaluationForm - Atualizando nível existente:", selectedLevel.id);

        if (evaluation && selectedLevel.id && !selectedLevel.id.includes('random')) {
          // Se estamos editando uma avaliação existente e o nível tem um ID real (não temporário)
          // Atualizar no backend
          console.log("EvaluationForm - Enviando atualização para o backend");
          try {
            await levelService.updateLevel(selectedLevel.id, {
              ...levelData,
              evaluationId: evaluation.id
            });
          } catch (error) {
            console.error("Erro ao atualizar nível no backend:", error);
          }
        }

        updatedLevels = formData.levels.map(level =>
          level.id === selectedLevel.id ? { ...level, ...levelData } : level
        );

      } else {
        // Adicionar novo nível
        console.log("EvaluationForm - Adicionando novo nível");

        if (evaluation && evaluation.id) {
          // Se estamos editando uma avaliação existente, criar o nível no backend
          console.log("EvaluationForm - Enviando novo nível para o backend, ID da avaliação:", evaluation.id);
          try {
            // Adicionar o ID da avaliação aos dados do nível
            const levelWithEvaluationId = {
              ...levelData,
              evaluationId: evaluation.id
            };

            console.log("EvaluationForm - Dados completos para criação:", levelWithEvaluationId);
            // Enviar ao backend usando o levelService em vez do evaluationsService
            savedLevel = await levelService.createLevel(levelWithEvaluationId);
            console.log("EvaluationForm - Nível salvo no backend:", savedLevel);
          } catch (error) {
            console.error("Erro ao salvar nível no backend:", error);
          }
        } else {
          console.log("EvaluationForm - Não é possível enviar para o backend porque evaluation ou evaluation.id é nulo");
        }

        // Se não conseguimos salvar no backend ou estamos criando uma nova avaliação,
        // usar um ID temporário
        if (!savedLevel) {
          savedLevel = {
            id: 'random-' + Math.random().toString(36).substring(2, 11), // ID temporário
            ...levelData
          };
        }

        updatedLevels = [...formData.levels, savedLevel];
      }

      // Ordenar níveis por ordem
      updatedLevels.sort((a, b) => a.order - b.order);

      console.log("EvaluationForm - Níveis atualizados:", updatedLevels);
      setFormData({ ...formData, levels: updatedLevels });
      handleCloseLevelModal();
    } catch (error) {
      console.error("Erro ao processar nível:", error);
    }
  };

  // Manipuladores para exclusão de nível
  const handleOpenDeleteLevelModal = (level) => {
    setSelectedLevel(level);
    setIsDeleteLevelModalOpen(true);
  };

  const handleCloseDeleteLevelModal = () => {
    setSelectedLevel(null);
    setIsDeleteLevelModalOpen(false);
  };

  const handleDeleteLevel = () => {
    const updatedLevels = formData.levels.filter(level => level.id !== selectedLevel.id);
    setFormData({ ...formData, levels: updatedLevels });
    handleCloseDeleteLevelModal();
  };

  // Manipuladores para o modal de seleção de níveis
  const handleOpenLevelSelectionModal = () => {
    setIsLevelSelectionModalOpen(true);
  };

  const handleCloseLevelSelectionModal = () => {
    setIsLevelSelectionModalOpen(false);
  };

  const handleSelectLevels = (selectedLevels) => {
    // Filtrar níveis já adicionados
    const newLevels = selectedLevels.filter(
      newLevel => !formData.levels.some(existingLevel => existingLevel.id === newLevel.id)
    );

    // Adicionar novos níveis à lista
    const updatedLevels = [...formData.levels, ...newLevels];

    // Ordenar níveis por ordem
    updatedLevels.sort((a, b) => a.order - b.order);

    setFormData({ ...formData, levels: updatedLevels });
    handleCloseLevelSelectionModal();
  };

  // Manipuladores para o modal de seleção de habilidades
  const handleOpenSkillSelectionModal = () => {
    setIsSkillSelectionModalOpen(true);
  };

  const handleCloseSkillSelectionModal = () => {
    setIsSkillSelectionModalOpen(false);
  };

  const handleSelectSkills = async (selectedSkills) => {
    console.log("EvaluationForm - handleSelectSkills - Habilidades selecionadas:", selectedSkills);

    try {
      // Filtrar habilidades já adicionadas
      const newSkills = selectedSkills.filter(
        newSkill => !formData.skills.some(existingSkill => existingSkill.id === newSkill.id)
      );

      console.log("EvaluationForm - Novas habilidades a serem adicionadas:", newSkills);

      // Se estamos editando uma avaliação existente, vincular as habilidades no backend
      if (evaluation && evaluation.id && newSkills.length > 0) {
        console.log("EvaluationForm - Vinculando habilidades à avaliação no backend");

        try {
          // Extrair IDs das novas habilidades
          const skillIds = newSkills.map(skill => skill.id);
          console.log("EvaluationForm - IDs das habilidades a serem vinculadas:", skillIds);

          // Vincular habilidades à avaliação no backend
          await evaluationsService.addSkillsToEvaluation(evaluation.id, skillIds);
          console.log("EvaluationForm - Habilidades vinculadas com sucesso no backend");
        } catch (error) {
          console.error("Erro ao vincular habilidades no backend:", error);
          toast_error("Não foi possível vincular algumas habilidades");
        }
      }

      // Adicionar novas habilidades à lista local
      const updatedSkills = [...formData.skills, ...newSkills];
      console.log("EvaluationForm - Lista atualizada de habilidades:", updatedSkills);

      setFormData({ ...formData, skills: updatedSkills });
      handleCloseSkillSelectionModal();
    } catch (error) {
      console.error("Erro ao processar seleção de habilidades:", error);
      toast_error("Ocorreu um erro ao processar as habilidades selecionadas");
    }
  };

  // Manipuladores para o modal de criação de habilidade
  const handleOpenSkillModal = (skill = null) => {
    setSelectedSkill(skill);
    setIsSkillModalOpen(true);
  };

  const handleCloseSkillModal = () => {
    setSelectedSkill(null);
    setIsSkillModalOpen(false);
  };

  const handleSaveSkill = async (skillData) => {
    console.log("EvaluationForm - handleSaveSkill - Recebendo dados da habilidade:", skillData);
    console.log("EvaluationForm - Valor da variável evaluation:", evaluation);

    try {
      let savedSkill;

      if (selectedSkill) {
        // Atualizar habilidade existente na lista
        console.log("EvaluationForm - Atualizando habilidade existente:", selectedSkill.id);

        if (evaluation && selectedSkill.id && !selectedSkill.id.includes('random')) {
          // Se estamos editando uma avaliação existente e a habilidade tem um ID real (não temporário)
          // Atualizar no backend
          console.log("EvaluationForm - Enviando atualização para o backend");
          try {
            await skillsService.updateSkill(selectedSkill.id, {
              ...skillData
            });
          } catch (error) {
            console.error("Erro ao atualizar habilidade no backend:", error);
          }
        }

        savedSkill = { ...selectedSkill, ...skillData };
        const updatedSkills = formData.skills.map(skill =>
          skill.id === selectedSkill.id ? savedSkill : skill
        );
        setFormData({ ...formData, skills: updatedSkills });
      } else {
        // Criar nova habilidade
        console.log("EvaluationForm - Adicionando nova habilidade");

        if (evaluation && evaluation.id) {
          // Se estamos editando uma avaliação existente, criar a habilidade no backend
          console.log("EvaluationForm - Enviando nova habilidade para o backend");
          try {
            // Criar a habilidade no backend
            const createdSkill = await skillsService.createSkill(skillData);
            console.log("EvaluationForm - Habilidade criada no backend:", createdSkill);

            // Vincular a habilidade à avaliação
            await evaluationsService.addSkillsToEvaluation(evaluation.id, [createdSkill.id]);
            console.log("EvaluationForm - Habilidade vinculada à avaliação");

            savedSkill = createdSkill;
          } catch (error) {
            console.error("Erro ao salvar habilidade no backend:", error);
            // Se falhar, usar ID temporário
            savedSkill = {
              id: 'random-' + Math.random().toString(36).substring(2, 11), // ID temporário
              ...skillData
            };
          }
        } else {
          console.log("EvaluationForm - Não é possível enviar para o backend porque evaluation ou evaluation.id é nulo");
          // Usar ID temporário para nova avaliação
          savedSkill = {
            id: 'random-' + Math.random().toString(36).substring(2, 11), // ID temporário
            ...skillData
          };
        }

        setFormData({ ...formData, skills: [...formData.skills, savedSkill] });
      }

      handleCloseSkillModal();
    } catch (error) {
      console.error("Erro ao salvar habilidade:", error);
      toast_error("Não foi possível salvar a habilidade");
    }
  };

  // Manipuladores para exclusão de habilidade
  const handleOpenDeleteSkillModal = (skill) => {
    setSelectedSkill(skill);
    setIsDeleteSkillModalOpen(true);
  };

  const handleCloseDeleteSkillModal = () => {
    setSelectedSkill(null);
    setIsDeleteSkillModalOpen(false);
  };

  const handleDeleteSkill = () => {
    const updatedSkills = formData.skills.filter(skill => skill.id !== selectedSkill.id);
    setFormData({ ...formData, skills: updatedSkills });
    handleCloseDeleteSkillModal();
  };

  // Manipuladores para o modal de pontuação
  const handleOpenScoreModal = (score = null) => {
    setSelectedScore(score);
    setIsScoreModalOpen(true);
  };

  const handleCloseScoreModal = () => {
    setSelectedScore(null);
    setIsScoreModalOpen(false);
  };

  const handleSaveScore = async (scoreData) => {
    console.log("EvaluationForm - handleSaveScore - Recebendo dados da pontuação:", scoreData);
    console.log("EvaluationForm - Valor da variável evaluation:", evaluation);

    try {
      let savedScore;
      let updatedScores;

      if (selectedScore) {
        // Atualizar pontuação existente
        console.log("EvaluationForm - Atualizando pontuação existente:", selectedScore.id);

        if (evaluation && selectedScore.id && !selectedScore.id.includes('random')) {
          // Se estamos editando uma avaliação existente e a pontuação tem um ID real (não temporário)
          // Atualizar no backend
          console.log("EvaluationForm - Enviando atualização para o backend");
          try {
            await evaluationsService.updateScore(evaluation.id, selectedScore.id, {
              ...scoreData,
              evaluationId: evaluation.id
            });
          } catch (error) {
            console.error("Erro ao atualizar pontuação no backend:", error);
          }
        }

        savedScore = { ...selectedScore, ...scoreData };
        updatedScores = formData.scores.map(score =>
          score.id === selectedScore.id ? savedScore : score
        );
      } else {
        // Adicionar nova pontuação
        console.log("EvaluationForm - Adicionando nova pontuação");

        if (evaluation && evaluation.id) {
          // Se estamos editando uma avaliação existente, criar a pontuação no backend
          console.log("EvaluationForm - Enviando nova pontuação para o backend");
          try {
            // Adicionar o ID da avaliação aos dados da pontuação
            const scoreWithEvaluationId = {
              ...scoreData,
              evaluationId: evaluation.id
            };

            // Enviar ao backend
            const scores = await evaluationsService.addScoresToEvaluation(evaluation.id, [scoreWithEvaluationId]);
            console.log("EvaluationForm - Pontuação salva no backend:", scores);

            // Se a API retornar a pontuação criada, usar esse objeto
            if (scores && scores.success) {
              // Buscar a pontuação recém-criada
              const updatedEvaluation = await evaluationsService.getEvaluation(evaluation.id);
              const newScores = updatedEvaluation.scores || [];
              const latestScore = newScores.length > 0 ? newScores[newScores.length - 1] : null;

              if (latestScore) {
                savedScore = latestScore;
              } else {
                // Fallback para ID temporário se não conseguir obter o ID real
                savedScore = {
                  id: 'random-' + Math.random().toString(36).substring(2, 11),
                  ...scoreData
                };
              }
            } else {
              // Fallback para ID temporário
              savedScore = {
                id: 'random-' + Math.random().toString(36).substring(2, 11),
                ...scoreData
              };
            }
          } catch (error) {
            console.error("Erro ao salvar pontuação no backend:", error);
            // Fallback para ID temporário em caso de erro
            savedScore = {
              id: 'random-' + Math.random().toString(36).substring(2, 11),
              ...scoreData
            };
          }
        } else {
          console.log("EvaluationForm - Não é possível enviar para o backend porque evaluation ou evaluation.id é nulo");
          // Usar ID temporário para nova avaliação
          savedScore = {
            id: 'random-' + Math.random().toString(36).substring(2, 11),
            ...scoreData
          };
        }

        updatedScores = [...formData.scores, savedScore];
      }

      setFormData({ ...formData, scores: updatedScores });
      handleCloseScoreModal();
    } catch (error) {
      console.error("Erro ao processar pontuação:", error);
      toast_error("Não foi possível salvar a pontuação");
    }
  };

  // Manipuladores para exclusão de pontuação
  const handleOpenDeleteScoreModal = (score) => {
    setSelectedScore(score);
    setIsDeleteScoreModalOpen(true);
  };

  const handleCloseDeleteScoreModal = () => {
    setSelectedScore(null);
    setIsDeleteScoreModalOpen(false);
  };

  const handleDeleteScore = () => {
    const updatedScores = formData.scores.filter(score => score.id !== selectedScore.id);
    setFormData({ ...formData, scores: updatedScores });
    handleCloseDeleteScoreModal();
  };

  // Manipuladores para o modal de tarefa/teste
  const handleOpenTaskModal = (task = null) => {
    setSelectedTask(task);
    setIsTaskModalOpen(true);
  };

  const handleCloseTaskModal = () => {
    setSelectedTask(null);
    setIsTaskModalOpen(false);
  };

  const handleSaveTask = async (taskData) => {
    console.log("EvaluationForm - handleSaveTask - Recebendo dados da tarefa:", taskData);
    console.log("EvaluationForm - Valor da variável evaluation:", evaluation);

    try {
      let savedTask;
      let updatedTasks;

      if (selectedTask) {
        // Atualizar tarefa existente
        console.log("EvaluationForm - Atualizando tarefa existente:", selectedTask.id);

        if (evaluation && selectedTask.id && !selectedTask.id.includes('random')) {
          // Se estamos editando uma avaliação existente e a tarefa tem um ID real (não temporário)
          // Atualizar no backend
          console.log("EvaluationForm - Enviando atualização para o backend");
          try {
            await evaluationsService.updateTask(evaluation.id, selectedTask.id, {
              ...taskData,
              evaluationId: evaluation.id
            });
          } catch (error) {
            console.error("Erro ao atualizar tarefa no backend:", error);
          }
        }

        savedTask = { ...selectedTask, ...taskData };
        updatedTasks = formData.tasks.map(task =>
          task.id === selectedTask.id ? savedTask : task
        );
      } else {
        // Adicionar nova tarefa
        console.log("EvaluationForm - Adicionando nova tarefa");

        if (evaluation && evaluation.id) {
          // Se estamos editando uma avaliação existente, criar a tarefa no backend
          console.log("EvaluationForm - Enviando nova tarefa para o backend");
          try {
            // Adicionar o ID da avaliação aos dados da tarefa
            const taskWithEvaluationId = {
              ...taskData,
              evaluationId: evaluation.id
            };

            // Enviar ao backend
            const tasks = await evaluationsService.addTasksToEvaluation(evaluation.id, [taskWithEvaluationId]);
            console.log("EvaluationForm - Tarefa salva no backend:", tasks);

            // Se a API retornar a tarefa criada, usar esse objeto
            if (tasks && tasks.success) {
              // Buscar a tarefa recém-criada
              const updatedEvaluation = await evaluationsService.getEvaluation(evaluation.id);
              const newTasks = updatedEvaluation.tasks || [];
              const latestTask = newTasks.length > 0 ? newTasks[newTasks.length - 1] : null;

              if (latestTask) {
                savedTask = latestTask;
              } else {
                // Fallback para ID temporário se não conseguir obter o ID real
                savedTask = {
                  id: 'random-' + Math.random().toString(36).substring(2, 11),
                  ...taskData
                };
              }
            } else {
              // Fallback para ID temporário
              savedTask = {
                id: 'random-' + Math.random().toString(36).substring(2, 11),
                ...taskData
              };
            }
          } catch (error) {
            console.error("Erro ao salvar tarefa no backend:", error);
            // Fallback para ID temporário em caso de erro
            savedTask = {
              id: 'random-' + Math.random().toString(36).substring(2, 11),
              ...taskData
            };
          }
        } else {
          console.log("EvaluationForm - Não é possível enviar para o backend porque evaluation ou evaluation.id é nulo");
          // Usar ID temporário para nova avaliação
          savedTask = {
            id: 'random-' + Math.random().toString(36).substring(2, 11),
            ...taskData
          };
        }

        updatedTasks = [...formData.tasks, savedTask];
      }

      // Ordenar tarefas por ordem
      updatedTasks.sort((a, b) => a.order - b.order);

      setFormData({ ...formData, tasks: updatedTasks });
      handleCloseTaskModal();
    } catch (error) {
      console.error("Erro ao processar tarefa:", error);
      toast_error("Não foi possível salvar a tarefa");
    }
  };

  // Manipuladores para exclusão de tarefa
  const handleOpenDeleteTaskModal = (task) => {
    setSelectedTask(task);
    setIsDeleteTaskModalOpen(true);
  };

  const handleCloseDeleteTaskModal = () => {
    setSelectedTask(null);
    setIsDeleteTaskModalOpen(false);
  };

  const handleDeleteTask = () => {
    const updatedTasks = formData.tasks.filter(task => task.id !== selectedTask.id);
    setFormData({ ...formData, tasks: updatedTasks });
    handleCloseDeleteTaskModal();
  };

  // Manipuladores para o modal de seleção de tarefas
  const handleOpenTaskSelectionModal = () => {
    setIsTaskSelectionModalOpen(true);
  };

  const handleCloseTaskSelectionModal = () => {
    setIsTaskSelectionModalOpen(false);
  };

  const handleSelectTasks = async (selectedTasks) => {
    console.log("EvaluationForm - handleSelectTasks - Tarefas selecionadas:", selectedTasks);

    try {
      // Filtrar tarefas já adicionadas
      const newTasks = selectedTasks.filter(
        newTask => !formData.tasks.some(existingTask => existingTask.id === newTask.id)
      );

      console.log("EvaluationForm - Novas tarefas a serem adicionadas:", newTasks);

      // Se estamos editando uma avaliação existente, vincular as tarefas no backend
      if (evaluation && evaluation.id && newTasks.length > 0) {
        console.log("EvaluationForm - Vinculando tarefas à avaliação no backend");

        try {
          // Extrair IDs das novas tarefas
          const taskIds = newTasks.map(task => task.id);
          console.log("EvaluationForm - IDs das tarefas a serem vinculadas:", taskIds);

          // Vincular tarefas à avaliação no backend
          // Implementar método para vincular tarefas existentes à avaliação
          // Por enquanto, vamos apenas adicionar ao estado local
          console.log("EvaluationForm - Adicionando tarefas ao estado local");
        } catch (error) {
          console.error("Erro ao vincular tarefas no backend:", error);
          toast_error("Não foi possível vincular algumas tarefas");
        }
      }

      // Adicionar novas tarefas à lista local
      const updatedTasks = [...formData.tasks, ...newTasks];

      // Ordenar tarefas por ordem
      updatedTasks.sort((a, b) => a.order - b.order);

      console.log("EvaluationForm - Lista atualizada de tarefas:", updatedTasks);

      setFormData({ ...formData, tasks: updatedTasks });
      handleCloseTaskSelectionModal();
    } catch (error) {
      console.error("Erro ao processar seleção de tarefas:", error);
      toast_error("Ocorreu um erro ao processar as tarefas selecionadas");
    }
  };

  return (
    <>
      <ModuleModal
        isOpen={isOpen}
        onClose={onClose}
        title={evaluation ? "Editar Avaliação" : "Nova Avaliação"}
        size="lg"
        moduleColor="abaplus"
        icon={<ClipboardList size={20} />}
      >
        <div className="p-6 space-y-6">
          <ModuleTabs
            tabs={[
              { id: "general", label: "Geral", icon: <ClipboardList size={16} /> },
              { id: "levels", label: "Níveis", icon: <Layers size={16} /> },
              { id: "skills", label: "Habilidades", icon: <Activity size={16} /> },
              { id: "scores", label: "Pontuação", icon: <Percent size={16} /> },
              { id: "tasks", label: "Tarefa/Teste", icon: <CheckSquare size={16} /> }
            ]}
            activeTab={activeTab}
            onTabChange={setActiveTab}
            moduleColor="abaplus"
          />

          <div className="mt-6">
            {/* Aba Geral */}
            {activeTab === "general" && (
              <div className="space-y-4">
                <ModuleFormGroup label="Tipo de Avaliação *" error={errors.type}>
                  <ModuleSelect
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                    moduleColor="abaplus"
                    error={!!errors.type}
                  >
                    <option value="SKILL_ACQUISITION">Aquisição de Habilidades</option>
                    <option value="BEHAVIOR_REDUCTION">Redução de Comportamentos</option>
                  </ModuleSelect>
                </ModuleFormGroup>

                <ModuleFormGroup label="Nome *" error={errors.name}>
                  <ModuleInput
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="Nome da avaliação"
                    moduleColor="abaplus"
                    error={!!errors.name}
                  />
                </ModuleFormGroup>

                <ModuleFormGroup label="Observações">
                  <ModuleTextarea
                    name="observations"
                    value={formData.observations}
                    onChange={handleChange}
                    placeholder="Observações sobre a avaliação"
                    rows={4}
                    moduleColor="abaplus"
                  />
                </ModuleFormGroup>
              </div>
            )}

            {/* Aba Níveis */}
            {activeTab === "levels" && (
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Níveis de Avaliação
                  </h3>
                  <div className="flex gap-2">
                    <button
                      onClick={handleOpenLevelSelectionModal}
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow transition-colors flex items-center gap-1.5 text-sm"
                    >
                      <Link2 size={16} />
                      Vincular Níveis
                    </button>
                    <button
                      onClick={() => handleOpenLevelModal()}
                      className="px-3 py-1.5 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-1.5 text-sm"
                    >
                      <Plus size={16} />
                      Novo Nível
                    </button>
                  </div>
                </div>

                {formData.levels.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Layers className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                      Nenhum nível cadastrado
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Clique no botão "Novo Nível" para adicionar um nível à avaliação.
                    </p>
                  </div>
                ) : (
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ordem
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Descrição
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Faixa Etária
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {formData.levels.map((level) => (
                          <tr key={level.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {level.order}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {level.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                              {level.ageRange || "-"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end gap-2">
                                <button
                                  onClick={() => handleOpenLevelModal(level)}
                                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                  aria-label="Editar"
                                >
                                  <Edit size={16} />
                                </button>
                                <button
                                  onClick={() => handleOpenDeleteLevelModal(level)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  aria-label="Excluir"
                                >
                                  <Trash size={16} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}

            {/* Aba Habilidades */}
            {activeTab === "skills" && (
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Habilidades Relacionadas
                  </h3>
                  <div className="flex gap-2">
                    <button
                      onClick={handleOpenSkillSelectionModal}
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow transition-colors flex items-center gap-1.5 text-sm"
                    >
                      <Link2 size={16} />
                      Vincular Habilidades
                    </button>
                    <button
                      onClick={() => handleOpenSkillModal()}
                      className="px-3 py-1.5 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-1.5 text-sm"
                    >
                      <Plus size={16} />
                      Nova Habilidade
                    </button>
                  </div>
                </div>

                {formData.skills.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Activity className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                      Nenhuma habilidade vinculada
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Clique em "Vincular Habilidades" para selecionar habilidades existentes ou "Nova Habilidade" para criar uma nova.
                    </p>
                  </div>
                ) : (
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Código
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ordem
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Descrição
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {formData.skills.map((skill) => (
                          <tr key={skill.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {skill.code || "-"}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {skill.order}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {skill.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end gap-2">
                                <button
                                  onClick={() => handleOpenSkillModal(skill)}
                                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                  aria-label="Editar"
                                >
                                  <Edit size={16} />
                                </button>
                                <button
                                  onClick={() => handleOpenDeleteSkillModal(skill)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  aria-label="Excluir"
                                >
                                  <Trash size={16} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}

            {/* Aba Pontuação */}
            {activeTab === "scores" && (
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Pontuações da Avaliação
                  </h3>
                  <button
                    onClick={() => handleOpenScoreModal()}
                    className="px-3 py-1.5 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-1.5 text-sm"
                  >
                    <Plus size={16} />
                    Nova Pontuação
                  </button>
                </div>

                {formData.scores.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <Percent className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                      Nenhuma pontuação cadastrada
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Clique no botão "Nova Pontuação" para adicionar uma pontuação à avaliação.
                    </p>
                  </div>
                ) : (
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Tipo
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Valor
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Descrição
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {formData.scores.map((score) => (
                          <tr key={score.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {score.type}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {score.value}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {score.description}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end gap-2">
                                <button
                                  onClick={() => handleOpenScoreModal(score)}
                                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                  aria-label="Editar"
                                >
                                  <Edit size={16} />
                                </button>
                                <button
                                  onClick={() => handleOpenDeleteScoreModal(score)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  aria-label="Excluir"
                                >
                                  <Trash size={16} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}

            {/* Aba Tarefa/Teste */}
            {activeTab === "tasks" && (
              <div className="space-y-4">
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Tarefas e Testes da Avaliação
                  </h3>
                  <div className="flex gap-2">
                    <button
                      onClick={handleOpenTaskSelectionModal}
                      className="px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-lg shadow transition-colors flex items-center gap-1.5 text-sm"
                    >
                      <Link2 size={16} />
                      Vincular Tarefas
                    </button>
                    <button
                      onClick={() => handleOpenTaskModal()}
                      className="px-3 py-1.5 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-1.5 text-sm"
                    >
                      <Plus size={16} />
                      Nova Tarefa/Teste
                    </button>
                  </div>
                </div>

                {formData.tasks.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <CheckSquare className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">
                      Nenhuma tarefa ou teste cadastrado
                    </h3>
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Clique no botão "Nova Tarefa/Teste" para adicionar uma tarefa ou teste à avaliação.
                    </p>
                  </div>
                ) : (
                  <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                      <thead className="bg-gray-50 dark:bg-gray-700">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ordem
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Nome
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Habilidade
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Nível
                          </th>
                          <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                            Ações
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        {formData.tasks.map((task) => (
                          <tr key={task.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {task.order}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {task.name}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {task.skillId ? formData.skills.find(s => s.id === task.skillId)?.description || '-' : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                              {task.levelId ? formData.levels.find(l => l.id === task.levelId)?.description || '-' : '-'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                              <div className="flex justify-end gap-2">
                                <button
                                  onClick={() => handleOpenTaskModal(task)}
                                  className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                                  aria-label="Editar"
                                >
                                  <Edit size={16} />
                                </button>
                                <button
                                  onClick={() => handleOpenDeleteTaskModal(task)}
                                  className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                                  aria-label="Excluir"
                                >
                                  <Trash size={16} />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              disabled={isLoading}
            >
              Cancelar
            </button>
            <button
              onClick={handleSubmit}
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                  <span>Salvando...</span>
                </>
              ) : (
                <>
                  <Save size={18} />
                  <span>Salvar</span>
                </>
              )}
            </button>
          </div>
        </div>
      </ModuleModal>

      {/* Modal de Formulário de Nível */}
      {isLevelModalOpen && (
        <LevelFormModal
          isOpen={isLevelModalOpen}
          onClose={handleCloseLevelModal}
          onSave={handleSaveLevel}
          level={selectedLevel}
        />
      )}

      {/* Modal de Confirmação de Exclusão de Nível */}
      <ConfirmationDialog
        isOpen={isDeleteLevelModalOpen}
        onClose={handleCloseDeleteLevelModal}
        onConfirm={handleDeleteLevel}
        title="Excluir Nível"
        message={`Tem certeza que deseja excluir o nível "${selectedLevel?.description}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        confirmVariant="danger"
      />

      {/* Modal de Formulário de Habilidade */}
      {isSkillModalOpen && (
        <SkillFormModal
          isOpen={isSkillModalOpen}
          onClose={handleCloseSkillModal}
          onSave={handleSaveSkill}
          skill={selectedSkill}
        />
      )}

      {/* Modal de Seleção de Habilidades */}
      {isSkillSelectionModalOpen && (
        <SkillSelectionModal
          isOpen={isSkillSelectionModalOpen}
          onClose={handleCloseSkillSelectionModal}
          onSelect={handleSelectSkills}
          currentSkills={formData.skills}
        />
      )}

      {/* Modal de Confirmação de Exclusão de Habilidade */}
      <ConfirmationDialog
        isOpen={isDeleteSkillModalOpen}
        onClose={handleCloseDeleteSkillModal}
        onConfirm={handleDeleteSkill}
        title="Remover Habilidade"
        message={`Tem certeza que deseja remover a habilidade "${selectedSkill?.description}" desta avaliação?`}
        confirmText="Remover"
        cancelText="Cancelar"
        confirmVariant="danger"
      />

      {/* Modal de Seleção de Níveis */}
      {isLevelSelectionModalOpen && (
        <LevelSelectionModal
          isOpen={isLevelSelectionModalOpen}
          onClose={handleCloseLevelSelectionModal}
          onSelect={handleSelectLevels}
          currentLevels={formData.levels}
        />
      )}

      {/* Modal de Formulário de Pontuação */}
      {isScoreModalOpen && (
        <ScoreFormModal
          isOpen={isScoreModalOpen}
          onClose={handleCloseScoreModal}
          onSave={handleSaveScore}
          score={selectedScore}
        />
      )}

      {/* Modal de Confirmação de Exclusão de Pontuação */}
      <ConfirmationDialog
        isOpen={isDeleteScoreModalOpen}
        onClose={handleCloseDeleteScoreModal}
        onConfirm={handleDeleteScore}
        title="Excluir Pontuação"
        message={`Tem certeza que deseja excluir a pontuação "${selectedScore?.description || selectedScore?.type}"?`}
        confirmText="Excluir"
        cancelText="Cancelar"
        confirmVariant="danger"
      />

      {/* Modal de Formulário de Tarefa/Teste */}
      {isTaskModalOpen && (
        <TaskFormModal
          isOpen={isTaskModalOpen}
          onClose={handleCloseTaskModal}
          onSave={handleSaveTask}
          task={selectedTask}
          skills={formData.skills}
          levels={formData.levels}
        />
      )}

      {/* Modal de Confirmação de Exclusão de Tarefa */}
      <ConfirmationDialog
        isOpen={isDeleteTaskModalOpen}
        onClose={handleCloseDeleteTaskModal}
        onConfirm={handleDeleteTask}
        title="Excluir Tarefa/Teste"
        message={`Tem certeza que deseja excluir a tarefa/teste "${selectedTask?.name}"?`}
        confirmText="Excluir"
        cancelText="Cancelar"
        confirmVariant="danger"
      />

      {/* Modal de Seleção de Tarefas */}
      {isTaskSelectionModalOpen && (
        <TaskSelectionModal
          isOpen={isTaskSelectionModalOpen}
          onClose={handleCloseTaskSelectionModal}
          onSelect={handleSelectTasks}
          currentTasks={formData.tasks}
          skills={formData.skills}
          levels={formData.levels}
        />
      )}
    </>
  );
};

export default EvaluationForm;
