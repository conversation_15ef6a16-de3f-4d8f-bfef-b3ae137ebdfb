// src/routes/aba/skillRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { SkillController, createSkillValidation, updateSkillValidation } = require('../../controllers/aba/skillController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para habilidades
router.post('/', createSkillValidation, SkillController.create);
router.get('/', SkillController.list);
router.get('/:id', SkillController.get);
router.put('/:id', updateSkillValidation, SkillController.update);
router.patch('/:id/status', SkillController.toggleStatus);
router.delete('/:id', SkillController.delete);

// Rotas para associação com avaliações
router.post('/evaluations/:evaluationId/skills/:skillId', SkillController.addToEvaluation);
router.delete('/evaluations/:evaluationId/skills/:skillId', SkillController.removeFromEvaluation);

module.exports = router;
