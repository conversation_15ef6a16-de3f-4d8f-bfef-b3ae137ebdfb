"use client";

import React, { useEffect } from 'react';
import TutorialHighlight from './TutorialHighlight';
import TutorialDialog from './TutorialDialog';
import { useTutorial } from '@/contexts/TutorialContext';

/**
 * Componente que combina o destaque com o diálogo para uma etapa do tutorial
 * Com scroll automático ao navegar entre etapas
 */
const TutorialStep = () => {
  const {
    isActive,
    currentStep,
    currentStepIndex,
    steps,
    isFirstStep,
    isLastStep,
    nextStep,
    prevStep,
    endTutorial
  } = useTutorial();

  // Efeito para rolar para o elemento destacado quando a etapa mudar
  useEffect(() => {
    if (isActive && currentStep && currentStep.selector) {
      const scrollToElement = () => {
        // Tenta encontrar todos os elementos que correspondem ao seletor
        const elements = document.querySelectorAll(currentStep.selector);

        if (!elements || elements.length === 0) {
          console.warn(`TutorialStep: Elemento com seletor "${currentStep.selector}" não encontrado.`);
          return;
        }

        console.log(`TutorialStep: Encontrados ${elements.length} elementos com seletor "${currentStep.selector}"`);

        // Encontra o primeiro elemento visível
        let targetElement = null;
        for (let i = 0; i < elements.length; i++) {
          const element = elements[i];
          const rect = element.getBoundingClientRect();

          // Verifica se o elemento está visível na tela
          if (rect.width > 0 && rect.height > 0 &&
              rect.top < window.innerHeight &&
              rect.left < window.innerWidth &&
              rect.bottom > 0 &&
              rect.right > 0) {

            // Verifica se o elemento não está oculto por CSS
            const style = window.getComputedStyle(element);
            if (style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0') {
              targetElement = element;
              console.log(`TutorialStep: Elemento visível encontrado na posição ${i+1}`, rect);
              break;
            }
          }
        }

        // Se não encontrou nenhum elemento visível, usa o primeiro
        if (!targetElement && elements.length > 0) {
          targetElement = elements[0];
          console.log(`TutorialStep: Nenhum elemento visível encontrado, usando o primeiro elemento`);
        }

        if (targetElement) {
          // Pequeno atraso para garantir que a UI esteja pronta
          setTimeout(() => {
            targetElement.scrollIntoView({
              behavior: 'smooth',
              block: 'center',
              inline: 'center'
            });
            console.log(`TutorialStep: Rolando para o elemento no passo ${currentStepIndex + 1}`);
          }, 100);
        }
      };

      // Pequeno atraso para garantir que a página esteja completamente carregada
      setTimeout(scrollToElement, 300);
    }
  }, [isActive, currentStep, currentStepIndex]);

  if (!isActive || !currentStep) return null;

  const {
    title,
    content,
    selector,
    position = 'auto',
    shape = 'auto',
    highlightPadding = 10,
    pulsate = true,
    dialogOffsetX = 20,
    dialogOffsetY = 20
  } = currentStep;

  return (
    <TutorialHighlight
      selector={selector}
      shape={shape}
      padding={highlightPadding}
      pulsate={pulsate}
    >
      <TutorialDialog
        title={title}
        content={content}
        position={position}
        targetSelector={selector}
        offsetX={dialogOffsetX}
        offsetY={dialogOffsetY}
        currentStep={currentStepIndex}
        totalSteps={steps.length}
        onNext={nextStep}
        onPrev={prevStep}
        onClose={endTutorial}
        isFirstStep={isFirstStep}
        isLastStep={isLastStep}
      />
    </TutorialHighlight>
  );
};

export default TutorialStep;