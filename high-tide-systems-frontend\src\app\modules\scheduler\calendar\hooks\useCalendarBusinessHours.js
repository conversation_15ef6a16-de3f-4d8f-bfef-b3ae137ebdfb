"use client";

import { useState, useEffect } from 'react';
import { calendarConfig } from '../utils/appointmentConstants';

const useCalendarBusinessHours = (filters, providersAvailability) => {
  const [businessHours, setBusinessHours] = useState(calendarConfig.defaultBusinessHours);

  useEffect(() => {
    const newBusinessHours = calculateBusinessHours();
    setBusinessHours(newBusinessHours);
    console.log('Business hours atualizados:', newBusinessHours);
  }, [filters.providers, providersAvailability]);

  // Função para calcular os horários de funcionamento com base nos provedores selecionados
  const calculateBusinessHours = () => {
    // Se não houver provedores selecionados, retorne horário comercial padrão
    if (!filters.providers || filters.providers.length === 0) {
      return calendarConfig.defaultBusinessHours;
    }
    
    // Combina a disponibilidade de todos os provedores selecionados
    let combinedAvailability = {};
    
    filters.providers.forEach(providerId => {
      if (providersAvailability[providerId]) {
        // Para cada dia da semana
        for (let day = 0; day < 7; day++) {
          if (providersAvailability[providerId][day]) {
            if (!combinedAvailability[day]) {
              combinedAvailability[day] = Array(24).fill(false);
            }
            
            // Para cada hora do dia
            for (let hour = 0; hour < 24; hour++) {
              // Se pelo menos um provedor estiver disponível, marque como disponível
              if (providersAvailability[providerId][day][hour]) {
                combinedAvailability[day][hour] = true;
              }
            }
          }
        }
      }
    });
    
    // Converte a disponibilidade combinada para formato businessHours
    return convertAvailabilityToBusinessHours(combinedAvailability);
  };

  // Converte a matriz de disponibilidade para o formato aceito pelo FullCalendar
  const convertAvailabilityToBusinessHours = (providerAvailability) => {
    if (!providerAvailability) return calendarConfig.defaultBusinessHours;
    
    const businessHoursConfig = [];
    
    // Para cada dia da semana (0-6, domingo a sábado)
    for (let dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
      // Verifique se temos dados para este dia
      if (providerAvailability[dayOfWeek]) {
        // Encontre blocos contíguos de horas disponíveis
        let blocks = [];
        let start = null;
        let end = null;
        
        // Para cada hora do dia (0-23)
        for (let hour = 0; hour <= 23; hour++) {
          // Se a hora estiver disponível
          if (providerAvailability[dayOfWeek][hour]) {
            // Se não tivermos um início de bloco, defina este como início
            if (start === null) {
              start = hour;
            }
            // Sempre atualize o fim para a hora atual + 1
            end = hour + 1;
          } 
          // Se a hora não estiver disponível ou estarmos na última hora
          else if (start !== null) {
            // Adicione o bloco atual à lista de blocos
            blocks.push({ start, end });
            // Redefina start e end para o próximo bloco
            start = null;
            end = null;
          }
        }
        
        // Verifique se há um bloco ainda aberto no final do loop
        if (start !== null) {
          blocks.push({ start, end });
        }
        
        // Adicione cada bloco como uma entrada de businessHours
        blocks.forEach(block => {
          businessHoursConfig.push({
            daysOfWeek: [dayOfWeek],
            startTime: `${String(block.start).padStart(2, '0')}:00`,
            endTime: `${String(block.end).padStart(2, '0')}:00`
          });
        });
      }
    }
    
    // Se não houver blocos definidos, use o padrão
    return businessHoursConfig.length > 0 
      ? businessHoursConfig 
      : calendarConfig.defaultBusinessHours;
  };

  return businessHours;
};

export default useCalendarBusinessHours;