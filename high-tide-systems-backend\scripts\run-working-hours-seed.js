// scripts/run-working-hours-seed.js
const { execSync } = require('child_process');
const path = require('path');

console.log('Executando seed de horários de trabalho...');

try {
  // Executar o script de seed
  execSync('node prisma/seed-working-hours.js', { 
    stdio: 'inherit',
    cwd: path.resolve(__dirname, '..')
  });
  
  console.log('\nSeed de horários de trabalho executado com sucesso!');
} catch (error) {
  console.error('Erro ao executar o seed de horários de trabalho:', error);
  process.exit(1);
}
