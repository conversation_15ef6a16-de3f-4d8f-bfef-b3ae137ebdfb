// Constantes de status dos agendamentos
export const APPOINTMENT_STATUS = {
    PENDING: {
      color: "#8b5cf6", // violet-500
      darkColor: "#a78bfa", // violet-400 para modo escuro
      label: "Pendente",
      bgClass: "bg-violet-500",
      textClass: "text-violet-50",
    },
    CONFIRMED: {
      color: "#22c55e", // green-600
      darkColor: "#4ade80", // green-400 para modo escuro
      label: "Confirmado",
      bgClass: "bg-success-500",
      textClass: "text-success-50",
    },
    CANCELLED: {
      color: "#ef4444", // red-500
      darkColor: "#f87171", // red-400 para modo escuro
      label: "Cancelado",
      bgClass: "bg-error-500",
      textClass: "text-error-50",
    },
    COMPLETED: {
      color: "#6b7280", // gray-500
      darkColor: "#9ca3af", // gray-400 para modo escuro
      label: "Concluído",
      bgClass: "bg-neutral-500",
      textClass: "text-neutral-50",
    },
    NO_SHOW: {
      color: "#d97706", // amber-600 (slightly darkened yellow)
      darkColor: "#f59e0b", // amber-500 para modo escuro
      label: "Não Compareceu",
      bgClass: "bg-amber-600",
      textClass: "text-amber-50",
    }
  };

  // Configurações padrão para o calendário
  export const calendarConfig = {
    // Configurações globais
    defaultTimedEventDuration: "01:00:00", // Duração padrão de 1 hora para todos os eventos
    forceEventDuration: true, // Forçar a duração do evento
    views: {
      dayGridMonth: {
        // Configurações para a visualização de mês
        defaultTimedEventDuration: "01:00:00", // Duração padrão de 1 hora
      },
      timeGridWeek: {
        slotDuration: "00:30:00",
        slotLabelFormat: {
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        },
        slotLabelInterval: "01:00",
        snapDuration: "01:00:00", // Alterado de 15 minutos para 1 hora
        defaultTimedEventDuration: "01:00:00", // Duração padrão de 1 hora
        nowIndicator: true,
      },
      timeGridDay: {
        // Configurações para a visualização de dia
        slotDuration: "00:30:00",
        slotLabelInterval: "01:00",
        snapDuration: "01:00:00", // Duração do snap de 1 hora
        defaultTimedEventDuration: "01:00:00", // Duração padrão de 1 hora
        nowIndicator: true,
      },
    },
    slotMinTime: "08:00:00",
    slotMaxTime: "20:00:00",
    defaultBusinessHours: [{
      daysOfWeek: [1, 2, 3, 4, 5], // Segunda a sexta
      startTime: '08:00',
      endTime: '20:00'
    }]
  };