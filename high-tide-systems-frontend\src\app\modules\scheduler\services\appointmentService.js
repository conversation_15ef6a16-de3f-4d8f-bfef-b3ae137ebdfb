// app/modules/scheduler/services/appointmentService.js
import { exportService } from "@/app/services/exportService";
import { api } from "@/utils/api";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import { extractData, processAppointments } from "@/utils/apiResponseAdapter";

// Função auxiliar para verificar se uma data é válida
const isValidDate = (date) => {
  return date instanceof Date && !isNaN(date.getTime());
};

export const appointmentService = {
  getAppointments: async (filters = {}) => {
    // Log dos filtros recebidos para depuração
    console.log("Filtros recebidos na função getAppointments:", JSON.stringify(filters, null, 2));

    const { page = 1, limit = 50000 } = filters;

    // Criar um novo objeto para os filtros da API
    const apiFilters = {
      page,
      limit,
      include_insurance_info: true
    };

    // Adicionar campo de busca se existir
    if (filters.search) {
      apiFilters.search = filters.search;
    }

    // Handle providers filter - usando a notação de array para compatibilidade com a API
    if (filters.providers && filters.providers.length > 0) {
      // Extrair os IDs válidos
      const providerIds = filters.providers
        .map(p => {
          // Log para depuração
          console.log("Provider item:", p);
          return p && typeof p === 'object' ? p.value : p;
        })
        .filter(Boolean);

      // Adicionar cada ID como um parâmetro separado com a notação de array
      providerIds.forEach(id => {
        // Usar a notação de array para compatibilidade com a API
        if (!apiFilters.providerIds) {
          apiFilters.providerIds = [];
        }
        apiFilters.providerIds.push(id);
      });

      console.log("Provider IDs:", apiFilters.providerIds);
    }

    // Handle persons filter (old clients filter)
    if (filters.persons && filters.persons.length > 0) {
      // Extrair os IDs válidos
      const personIds = filters.persons
        .map(p => {
          // Log para depuração
          console.log("Person item:", p);
          return p && typeof p === 'object' ? p.value : p;
        })
        .filter(Boolean);

      // Adicionar cada ID como um parâmetro separado com a notação de array
      personIds.forEach(id => {
        // Usar a notação de array para compatibilidade com a API
        if (!apiFilters.personIds) {
          apiFilters.personIds = [];
        }
        apiFilters.personIds.push(id);
      });

      console.log("Person IDs:", apiFilters.personIds);
    }

    // Handle locations filter
    if (filters.locations && filters.locations.length > 0) {
      // Extrair os IDs válidos
      const locationIds = filters.locations
        .map(l => {
          // Log para depuração
          console.log("Location item:", l);
          return l && typeof l === 'object' ? l.value : l;
        })
        .filter(Boolean);

      // Adicionar cada ID como um parâmetro separado com a notação de array
      locationIds.forEach(id => {
        // Usar a notação de array para compatibilidade com a API
        if (!apiFilters.locationIds) {
          apiFilters.locationIds = [];
        }
        apiFilters.locationIds.push(id);
      });

      console.log("Location IDs:", apiFilters.locationIds);
    }

    // Handle service types filter
    if (filters.serviceTypes && filters.serviceTypes.length > 0) {
      // Extrair os IDs válidos
      const serviceTypeIds = filters.serviceTypes
        .map(s => {
          // Log para depuração
          console.log("Service type item:", s);
          return s && typeof s === 'object' ? s.value : s;
        })
        .filter(Boolean);

      // Adicionar cada ID como um parâmetro separado com a notação de array
      serviceTypeIds.forEach(id => {
        // Usar a notação de array para compatibilidade com a API
        if (!apiFilters.serviceTypeIds) {
          apiFilters.serviceTypeIds = [];
        }
        apiFilters.serviceTypeIds.push(id);
      });

      console.log("Service Type IDs:", apiFilters.serviceTypeIds);
    }

    // Handle status filter
    if (filters.status) {
      let statusValues = [];

      // Check if status is a string (comma-separated values) or an array
      if (typeof filters.status === 'string') {
        // Split the comma-separated string into an array
        statusValues = filters.status.split(',').map(s => s.trim()).filter(Boolean);
        console.log("Status string converted to array:", statusValues);
      } else if (Array.isArray(filters.status) && filters.status.length > 0) {
        // Extract valid values from array
        statusValues = filters.status
          .map(s => {
            // Log para depuração
            console.log("Status item:", s);
            return s && typeof s === 'object' ? s.value : s;
          })
          .filter(Boolean);
      }

      // Adicionar cada valor como um parâmetro separado com a notação de array
      if (statusValues.length > 0) {
        apiFilters.status = [];
        statusValues.forEach(value => {
          apiFilters.status.push(value);
        });
        console.log("Status values:", apiFilters.status);
      }
    }

    // Converter datas para o formato esperado pela API
    if (filters.startDate) {
      apiFilters.startDate = typeof filters.startDate === 'string'
        ? filters.startDate
        : format(filters.startDate, "yyyy-MM-dd");
    }

    if (filters.endDate) {
      apiFilters.endDate = typeof filters.endDate === 'string'
        ? filters.endDate
        : format(filters.endDate, "yyyy-MM-dd");
    }

    try {
      // Log dos parâmetros para depuração
      console.log("Enviando filtros para API:", JSON.stringify(apiFilters, null, 2));

      // Construir a URL manualmente para garantir o formato correto dos arrays
      let url = "/schedulings?";

      // Adicionar parâmetros básicos
      url += `page=${apiFilters.page || 1}&limit=${apiFilters.limit || 20}&include_insurance_info=true`;

      console.log("URL base construída:", url);

      // Adicionar parâmetros de busca
      if (apiFilters.search) {
        url += `&search=${encodeURIComponent(apiFilters.search)}`;
      }

      // Adicionar datas
      if (apiFilters.startDate) {
        url += `&startDate=${encodeURIComponent(apiFilters.startDate)}`;
      }

      if (apiFilters.endDate) {
        url += `&endDate=${encodeURIComponent(apiFilters.endDate)}`;
      }

      // Adicionar arrays com a notação correta
      if (apiFilters.providerIds && apiFilters.providerIds.length > 0) {
        console.log("Adicionando providerIds à URL:", apiFilters.providerIds);
        apiFilters.providerIds.forEach(id => {
          if (id) {
            url += `&providerIds[]=${encodeURIComponent(id)}`;
          }
        });
      } else if (filters.providerIds && filters.providerIds.length > 0) {
        console.log("Adicionando providerIds do filtro original à URL:", filters.providerIds);
        filters.providerIds.forEach(id => {
          if (id) {
            url += `&providerIds[]=${encodeURIComponent(typeof id === 'object' ? id.value : id)}`;
          }
        });
      }

      if (apiFilters.personIds && apiFilters.personIds.length > 0) {
        console.log("Adicionando personIds à URL:", apiFilters.personIds);
        apiFilters.personIds.forEach(id => {
          if (id) {
            url += `&personIds[]=${encodeURIComponent(id)}`;
          }
        });
      } else if (filters.personIds && filters.personIds.length > 0) {
        console.log("Adicionando personIds do filtro original à URL:", filters.personIds);
        filters.personIds.forEach(id => {
          if (id) {
            url += `&personIds[]=${encodeURIComponent(typeof id === 'object' ? id.value : id)}`;
          }
        });
      }

      if (apiFilters.locationIds && apiFilters.locationIds.length > 0) {
        console.log("Adicionando locationIds à URL:", apiFilters.locationIds);
        apiFilters.locationIds.forEach(id => {
          if (id) {
            url += `&locationIds[]=${encodeURIComponent(id)}`;
          }
        });
      } else if (filters.locationIds && filters.locationIds.length > 0) {
        console.log("Adicionando locationIds do filtro original à URL:", filters.locationIds);
        filters.locationIds.forEach(id => {
          if (id) {
            url += `&locationIds[]=${encodeURIComponent(typeof id === 'object' ? id.value : id)}`;
          }
        });
      }

      if (apiFilters.serviceTypeIds && apiFilters.serviceTypeIds.length > 0) {
        console.log("Adicionando serviceTypeIds à URL:", apiFilters.serviceTypeIds);
        apiFilters.serviceTypeIds.forEach(id => {
          if (id) {
            url += `&serviceTypeIds[]=${encodeURIComponent(id)}`;
          }
        });
      } else if (filters.serviceTypeIds && filters.serviceTypeIds.length > 0) {
        console.log("Adicionando serviceTypeIds do filtro original à URL:", filters.serviceTypeIds);
        filters.serviceTypeIds.forEach(id => {
          if (id) {
            url += `&serviceTypeIds[]=${encodeURIComponent(typeof id === 'object' ? id.value : id)}`;
          }
        });
      }

      if (apiFilters.status && apiFilters.status.length > 0) {
        console.log("Adicionando status à URL:", apiFilters.status);
        apiFilters.status.forEach(status => {
          if (status) {
            url += `&status[]=${encodeURIComponent(status)}`;
          }
        });
      } else if (filters.status) {
        // Handle string format (comma-separated values)
        if (typeof filters.status === 'string') {
          console.log("Adicionando status string à URL:", filters.status);
          const statusValues = filters.status.split(',').map(s => s.trim()).filter(Boolean);
          statusValues.forEach(status => {
            if (status) {
              url += `&status[]=${encodeURIComponent(status)}`;
            }
          });
        }
        // Handle array format
        else if (Array.isArray(filters.status) && filters.status.length > 0) {
          console.log("Adicionando status do filtro original à URL:", filters.status);
          filters.status.forEach(status => {
            if (status) {
              url += `&status[]=${encodeURIComponent(typeof status === 'object' ? status.value : status)}`;
            }
          });
        }
      }

      // Verificar se a URL contém os parâmetros esperados
      const expectedParams = [];
      if (apiFilters.providerIds?.length > 0) expectedParams.push('providerIds[]');
      if (apiFilters.personIds?.length > 0) expectedParams.push('personIds[]');
      if (apiFilters.locationIds?.length > 0) expectedParams.push('locationIds[]');
      if (apiFilters.serviceTypeIds?.length > 0) expectedParams.push('serviceTypeIds[]');
      if (apiFilters.status?.length > 0) expectedParams.push('status[]');

      // Verificar se todos os parâmetros esperados estão na URL
      const missingParams = expectedParams.filter(param => !url.includes(param));
      if (missingParams.length > 0) {
        console.warn("AVISO: Parâmetros esperados não encontrados na URL:", missingParams);
        console.warn("Filtros originais:", JSON.stringify(filters, null, 2));
        console.warn("Filtros processados:", JSON.stringify(apiFilters, null, 2));

        // Tentar adicionar os parâmetros faltantes manualmente
        if (apiFilters.providerIds?.length > 0 && !url.includes('providerIds[]')) {
          apiFilters.providerIds.forEach(id => {
            if (id) url += `&providerIds[]=${encodeURIComponent(id)}`;
          });
        }
        if (apiFilters.personIds?.length > 0 && !url.includes('personIds[]')) {
          apiFilters.personIds.forEach(id => {
            if (id) url += `&personIds[]=${encodeURIComponent(id)}`;
          });
        }
        if (apiFilters.locationIds?.length > 0 && !url.includes('locationIds[]')) {
          apiFilters.locationIds.forEach(id => {
            if (id) url += `&locationIds[]=${encodeURIComponent(id)}`;
          });
        }
        if (apiFilters.serviceTypeIds?.length > 0 && !url.includes('serviceTypeIds[]')) {
          apiFilters.serviceTypeIds.forEach(id => {
            if (id) url += `&serviceTypeIds[]=${encodeURIComponent(id)}`;
          });
        }
        if (apiFilters.status?.length > 0 && !url.includes('status[]')) {
          apiFilters.status.forEach(status => {
            if (status) url += `&status[]=${encodeURIComponent(status)}`;
          });
        } else if (typeof filters.status === 'string' && !url.includes('status[]')) {
          const statusValues = filters.status.split(',').map(s => s.trim()).filter(Boolean);
          statusValues.forEach(status => {
            if (status) url += `&status[]=${encodeURIComponent(status)}`;
          });
        }
      }

      console.log("URL final construída:", url);

      // Verificar se a URL está correta
      if (url.includes('undefined') || url.includes('null')) {
        console.warn("AVISO: URL contém valores undefined ou null:", url);
      }

      const response = await api.get(url);

      if (!response?.data) {
        console.error("Resposta inválida da API:", response);
        return { appointments: [], total: 0, pages: 0 };
      }

      // Log da resposta para depuração
      console.log("Resposta da API:", JSON.stringify(response.data, null, 2));

      // Verificar se a resposta contém dados
      if (!response.data || (response.data.schedulings && response.data.schedulings.length === 0)) {
        console.warn("Resposta da API não contém agendamentos");
      }

      // Usar o adaptador para extrair os dados de forma consistente
      const { schedulings } = extractData(response.data, 'schedulings', ['appointments']);
      console.log(`[APPOINTMENTS] Carregados ${schedulings.length} agendamentos`);

      // Log detalhado dos primeiros agendamentos para depuração
      if (schedulings.length > 0) {
        console.log("Primeiro agendamento:", schedulings[0]);
      }

      // Processar os agendamentos para garantir compatibilidade com diferentes formatos
      const processedAppointments = processAppointments(schedulings);

      // Adicionar informações de limite de convênio aos agendamentos processados
      const appointmentsWithInsuranceInfo = processedAppointments.map(appointment => {
        // Extrair informações sobre limite de convênio do agendamento original
        const originalScheduling = schedulings.find(s => s.id === appointment.id) || {};
        const insuranceInfo = originalScheduling.insuranceInfo || {};
        const hasLimitWarning =
          (insuranceInfo.monthlyUsed !== undefined &&
           insuranceInfo.monthlyLimit !== undefined &&
           insuranceInfo.monthlyUsed >= insuranceInfo.monthlyLimit);

        // Criar string de uso para exibição
        let usageDisplay = null;
        if (insuranceInfo.monthlyLimit > 0) {
          usageDisplay = `${insuranceInfo.monthlyUsed || 0}/${insuranceInfo.monthlyLimit}`;
        }

        // Adicionar informações de convênio ao agendamento processado
        return {
          ...appointment,
          insurance: originalScheduling.insurance ? {
            id: originalScheduling.insurance.id,
            name: originalScheduling.insurance.name,
            limitWarning: hasLimitWarning,
            usage: usageDisplay
          } : null
        };
      });

      return {
        appointments: appointmentsWithInsuranceInfo,
        total: response.data.total || schedulings.length,
        pages: response.data.pages || 1,
      };
    } catch (error) {
      console.error("Erro ao carregar agendamentos:", error);
      return { appointments: [], total: 0, pages: 0 };
    }
  },

  // Modificações para o appointmentService.js

  // Função modificada para criar agendamentos sequenciais
  createAppointment: async (appointmentData) => {
    console.log("Criando agendamento:", appointmentData);

    // Extrair a quantidade de agendamentos sequenciais (se houver)
    const sequentialAppointments = appointmentData.sequentialAppointments || 1;
    console.log(`Número de agendamentos sequenciais: ${sequentialAppointments}`);

    // Criar o agendamento principal
    const response = await api.post("/schedulings", {
      title: appointmentData.title,
      description: appointmentData.description,
      startDate: appointmentData.startDate,
      endDate: appointmentData.endDate,
      userId: appointmentData.providerId,
      personId: appointmentData.personId,
      creatorId: appointmentData.creatorId || appointmentData.providerId,
      locationId: appointmentData.locationId,
      serviceTypeId: appointmentData.serviceTypeId,
      insuranceId: appointmentData.insuranceId,
    });

    // Armazenar todos os agendamentos criados
    const createdAppointments = [
      {
        id: response.data.id,
        title: response.data.title,
        description: response.data.description,
        startDate: new Date(response.data.startDate),
        endDate: new Date(response.data.endDate),
        providerId: response.data.userId,
        personId: response.data.personId,
        locationId: response.data.locationId,
        serviceTypeId: response.data.serviceTypeId,
      }
    ];

    // Se há mais de um agendamento sequencial, criar os adicionais
    if (sequentialAppointments > 1) {
      try {
        // Duração fixa de 1 hora (3600000 ms) para agendamentos sequenciais
        const sequentialDuration = 3600000; // 1 hora em milissegundos

        // Obter a data de término do agendamento original
        const originalEndDate = new Date(appointmentData.endDate);

        // Verificar se a data é válida antes de usar toISOString()
        if (isValidDate(originalEndDate)) {
          console.log(`Data de término original: ${originalEndDate.toISOString()}`);
        } else {
          console.error("Data de término original inválida:", appointmentData.endDate);
          throw new Error("Data de término inválida");
        }

        // Armazenar as datas de início e fim de cada agendamento sequencial
        // para o caso de falha na criação de algum deles
        const sequentialDates = [];

        // Calcular as datas de todos os agendamentos sequenciais antecipadamente
        for (let i = 1; i < sequentialAppointments; i++) {
          const startTime = originalEndDate.getTime() + ((i - 1) * sequentialDuration);
          const endTime = startTime + sequentialDuration;
          sequentialDates.push({
            startDate: new Date(startTime),
            endDate: new Date(endTime)
          });
        }

        // Criar os agendamentos sequenciais
        for (let i = 1; i < sequentialAppointments; i++) {
          try {
            // Usar as datas pré-calculadas para este agendamento sequencial
            const sequentialDate = sequentialDates[i-1];
            const nextStartDate = sequentialDate.startDate;
            const nextEndDate = sequentialDate.endDate;

            // Verificar se as datas são válidas
            if (!isValidDate(nextStartDate) || !isValidDate(nextEndDate)) {
              console.error(`Datas pré-calculadas inválidas para agendamento #${i}:`, {
                nextStartDate,
                nextEndDate
              });
              throw new Error(`Datas pré-calculadas inválidas para agendamento #${i}`);
            }

            console.log(`Criando agendamento sequencial #${i}: ${nextStartDate.toISOString()} - ${nextEndDate.toISOString()}`);

            // Criar o agendamento sequencial
            const sequentialResponse = await api.post("/schedulings", {
              title: appointmentData.title, // Mesmo título
              description: appointmentData.description,
              startDate: nextStartDate.toISOString(),
              endDate: nextEndDate.toISOString(),
              userId: appointmentData.providerId,
              personId: appointmentData.personId,
              creatorId: appointmentData.creatorId || appointmentData.providerId,
              locationId: appointmentData.locationId,
              serviceTypeId: appointmentData.serviceTypeId,
              insuranceId: appointmentData.insuranceId,
            });

            // Adicionar à lista de agendamentos criados
            try {
              // Verificar se a resposta contém dados válidos
              if (sequentialResponse && sequentialResponse.data) {
                // Criar objetos Date a partir das strings ISO
                let startDateStr = sequentialResponse.data.startDate;
                let endDateStr = sequentialResponse.data.endDate;

                // Garantir que as strings de data são válidas
                if (!startDateStr || !endDateStr) {
                  console.error(`Datas inválidas na resposta do servidor para agendamento #${i+1}:`, {
                    startDate: startDateStr,
                    endDate: endDateStr
                  });

                  // Usar as datas pré-calculadas como fallback
                  console.log(`Usando datas pré-calculadas como fallback para agendamento #${i+1}`);
                  startDateStr = nextStartDate.toISOString();
                  endDateStr = nextEndDate.toISOString();
                }

                // Criar objetos Date a partir das strings ISO
                let startDateObj = new Date(startDateStr);
                let endDateObj = new Date(endDateStr);

                // Verificar se as datas são válidas
                if (!isValidDate(startDateObj) || !isValidDate(endDateObj)) {
                  console.error(`Objetos Date inválidos para agendamento #${i+1}:`, {
                    startDateObj,
                    endDateObj,
                    startDateStr,
                    endDateStr
                  });

                  // Usar as datas pré-calculadas como fallback
                  console.log(`Usando datas pré-calculadas como fallback para objetos Date inválidos no agendamento #${i+1}`);
                  startDateObj = nextStartDate;
                  endDateObj = nextEndDate;
                }

                // Verificar se as datas são válidas antes de chamar toISOString()
                console.log(`Agendamento #${i+1} criado: ${startDateObj.toISOString()} - ${endDateObj.toISOString()}`);

                // Calcular a próxima data de início e fim para garantir consistência
                const nextStartTime = endDateObj.getTime();
                const nextStartDate = new Date(nextStartTime);
                const nextEndDate = new Date(nextStartTime + sequentialDuration);

                // Adicionar o agendamento criado à lista
                createdAppointments.push({
                  id: sequentialResponse.data.id,
                  title: sequentialResponse.data.title,
                  description: sequentialResponse.data.description,
                  startDate: startDateObj,
                  endDate: endDateObj,
                  providerId: sequentialResponse.data.userId,
                  personId: sequentialResponse.data.personId,
                  locationId: sequentialResponse.data.locationId,
                  serviceTypeId: sequentialResponse.data.serviceTypeId,
                });

                // Verificar se o agendamento foi adicionado corretamente
                const lastAppointment = createdAppointments[createdAppointments.length - 1];
                console.log(`Verificando último agendamento adicionado (#${i+1}):`, {
                  startDate: lastAppointment.startDate.toISOString(),
                  endDate: lastAppointment.endDate.toISOString(),
                  isStartValid: isValidDate(lastAppointment.startDate),
                  isEndValid: isValidDate(lastAppointment.endDate)
                });
              } else {
                console.error(`Resposta inválida para o agendamento sequencial #${i+1}:`, sequentialResponse);
              }
            } catch (dateError) {
              console.error(`Erro ao processar datas do agendamento sequencial #${i+1}:`, dateError);
            }
          } catch (appointmentError) {
            console.error(`Erro ao criar agendamento sequencial #${i+1}:`, appointmentError);
            // Continuar com o próximo agendamento mesmo se este falhar
            continue;
          }
        }
      } catch (error) {
        console.error("Erro ao criar agendamentos sequenciais:", error);
        // Retornar o agendamento principal mesmo em caso de erro nos sequenciais
        return createdAppointments[0];
      }
    }

    console.log(`Total de ${createdAppointments.length} agendamentos criados`);
    return createdAppointments[0]; // Retorna o agendamento original para compatibilidade
  },

  // Função modificada para criar recorrências com agendamentos sequenciais
  createRecurrence: async (recurrenceData) => {
    console.log("\n========== INÍCIO: FRONTEND - CRIAR RECORRÊNCIA ==========");
    console.log("Dados recebidos do frontend:");
    console.log(`- Título: ${recurrenceData.title}`);
    console.log(`- Tipo de recorrência: ${recurrenceData.recurrenceType}`);
    console.log(`- Valor: ${recurrenceData.recurrenceValue || recurrenceData.numberOfOccurrences}`);
    console.log(`- Data original: ${recurrenceData.startDate}`);
    console.log(`- Quantidade de padrões: ${recurrenceData.patterns?.length || 0}`);
    console.log(`- Usuário: ${recurrenceData.providerId || recurrenceData.userId}`);
    console.log(`- Pessoa: ${recurrenceData.personId}`);

    // Extrair a quantidade de agendamentos sequenciais (se houver)
    const sequentialAppointments = recurrenceData.sequentialAppointments || 1;
    console.log(`- Agendamentos sequenciais: ${sequentialAppointments}x`);

    // Verificações básicas
    if (!recurrenceData.personId || !recurrenceData.userId || !recurrenceData.locationId) {
      console.log("ERRO: Dados obrigatórios para recorrência ausentes");
      throw new Error("Dados obrigatórios para recorrência ausentes");
    }

    if (!recurrenceData.patterns || recurrenceData.patterns.length === 0) {
      console.log("ERRO: Nenhum padrão de recorrência informado");
      throw new Error("É necessário pelo menos um padrão de recorrência");
    }

    // Log detalhado de padrões
    if (recurrenceData.patterns?.length > 0) {
      console.log("Padrões de recorrência:");
      recurrenceData.patterns.forEach((pattern, index) => {
        console.log(`Padrão ${index + 1}: Dia ${pattern.dayOfWeek}, Horário ${pattern.startTime} - ${pattern.endTime}`);
      });
    }

    // Processa o valor da recorrência
    let recurrenceValue;

    if (recurrenceData.recurrenceType === 'OCCURRENCES') {
      // Para ocorrências, garantir que é um número
      recurrenceValue = recurrenceData.numberOfOccurrences || recurrenceData.recurrenceValue;
      if (typeof recurrenceValue === 'string') {
        recurrenceValue = parseInt(recurrenceValue, 10);
      }
      // Garantir que é pelo menos 1
      if (recurrenceValue < 1) {
        console.log(`AVISO: Valor de ocorrências menor que 1, ajustando para 1`);
        recurrenceValue = 1;
      }
      console.log(`Valor de ocorrências processado: ${recurrenceValue}`);
    } else {
      // Para END_DATE, garantir que é uma data válida
      recurrenceValue = recurrenceData.endDate || recurrenceData.recurrenceValue;
      // Se for uma data, converter para string ISO
      if (recurrenceValue instanceof Date) {
        recurrenceValue = recurrenceValue.toISOString();
      }
      console.log(`Data final processada: ${recurrenceValue}`);
    }

    // Garantir que a data inicial esteja no formato ISO
    let startDate = recurrenceData.startDate;
    if (startDate instanceof Date) {
      console.log(`Convertendo data de início de objeto Date para string ISO`);
      startDate = startDate.toISOString();
    }
    console.log(`Data de início processada: ${startDate}`);

    // Informações sobre fuso horário
    const tzOffset = new Date().getTimezoneOffset();
    console.log(`Fuso horário do navegador: UTC${tzOffset <= 0 ? '+' : '-'}${Math.abs(Math.floor(tzOffset / 60))}:${Math.abs(tzOffset % 60).toString().padStart(2, '0')}`);

    // Formatar os padrões corretamente
    console.log("Processando padrões de recorrência...");
    const patterns = recurrenceData.patterns.map((pattern, index) => {
      // Garantir que o formato de hora seja HH:MM
      const formatTimeString = (timeStr) => {
        if (!timeStr) {
          console.log(`AVISO: Horário vazio, usando "00:00" como padrão`);
          return "00:00";
        }

        // Se já estiver no formato correto
        if (timeStr.match(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)) {
          if (timeStr.length === 5) {
            return timeStr;
          }
          console.log(`AVISO: Formatando horário "${timeStr}" para incluir zeros à esquerda`);
          return timeStr.padStart(5, '0');
        }

        // Tenta extrair horas e minutos de outros formatos
        const match = timeStr.match(/(\d{1,2}):(\d{1,2})/);
        if (match) {
          const hours = match[1].padStart(2, '0');
          const minutes = match[2].padStart(2, '0');
          console.log(`AVISO: Reformatando horário "${timeStr}" para "${hours}:${minutes}"`);
          return `${hours}:${minutes}`;
        }

        // Fallback seguro
        console.log(`ERRO: Não foi possível processar o formato de hora "${timeStr}", usando "00:00" como fallback`);
        return "00:00";
      };

      const processedPattern = {
        dayOfWeek: pattern.dayOfWeek,
        startTime: formatTimeString(pattern.startTime),
        endTime: formatTimeString(pattern.endTime),
      };

      console.log(`Padrão ${index + 1} processado: Dia ${processedPattern.dayOfWeek}, Horário ${processedPattern.startTime} - ${processedPattern.endTime}`);
      return processedPattern;
    });

    console.log(`Enviando requisição de recorrência:
    - Tipo: ${recurrenceData.recurrenceType}
    - Valor: ${recurrenceValue}
    - Padrões: ${patterns.length} dias
    - Data início: ${startDate}
    - Agendamentos sequenciais: ${sequentialAppointments}x
  `);

    try {
      console.log("Enviando requisição ao servidor...");

      // Fazer a requisição à API
      const response = await api.post("/recurrences", {
        title: recurrenceData.title,
        description: recurrenceData.description,
        userId: recurrenceData.providerId || recurrenceData.userId,
        startDate: startDate,
        personId: recurrenceData.personId,
        locationId: recurrenceData.locationId,
        serviceTypeId: recurrenceData.serviceTypeId,
        insuranceId: recurrenceData.insuranceId,
        recurrenceType: recurrenceData.recurrenceType,
        recurrenceValue: recurrenceValue,
        patterns: patterns,
        sequentialAppointments: sequentialAppointments // NOVO: Enviar o número de agendamentos sequenciais
      });

      console.log("Resposta recebida do servidor:");
      console.log(`- Status: ${response.status}`);
      console.log(`- Recorrência criada: ID ${response.data.recurrence?.id || 'desconhecido'}`);
      console.log(`- Agendamento original criado: ID ${response.data.originalAppointment?.id || 'desconhecido'}`);
      console.log(`- Agendamento original: ${response.data.originalAppointment?.startDate || 'N/A'} - ${response.data.originalAppointment?.endDate || 'N/A'}`);
      console.log(`- Quantidade de agendamentos recorrentes: ${response.data.schedulings?.length || 0}`);

      if (response.data.schedulings?.length > 0) {
        console.log("Primeiros agendamentos recorrentes:");
        response.data.schedulings.slice(0, 3).forEach((scheduling, index) => {
          console.log(`  ${index + 1}: ${scheduling.startDate} - ${scheduling.endDate}`);
        });

        if (response.data.schedulings.length > 3) {
          console.log(`  ... e mais ${response.data.schedulings.length - 3} agendamentos`);
        }
      }

      console.log("========== FIM: FRONTEND - CRIAR RECORRÊNCIA ==========\n");

      return {
        id: response.data.recurrence?.id,
        title: response.data.recurrence?.title,
        description: response.data.recurrence?.description,
        recurrenceType: response.data.recurrence?.recurrenceType,
        numberOfOccurrences: response.data.recurrence?.numberOfOccurrences,
        patterns: response.data.recurrence?.patterns,
        schedulings: response.data.schedulings || [],
        originalAppointment: response.data.originalAppointment // Agendamento original na resposta
      };
    } catch (error) {
      console.error("ERRO ao criar recorrência:", error);
      console.log("Detalhes do erro:", error.response?.data || error.message);
      console.log("========== FIM COM ERRO: FRONTEND - CRIAR RECORRÊNCIA ==========\n");
      throw error;
    }
  },

  // Obter um agendamento pelo ID
  getAppointmentById: async (id) => {
    try {
      // Verificar se o ID é válido
      if (!id || id === "undefined" || id === "null") {
        console.error(`ID de agendamento inválido: ${id}`);
        return null;
      }

      console.log(`Buscando agendamento com ID: ${id}`);
      const response = await api.get(`/schedulings/${id}`);

      if (!response?.data) {
        console.error("Resposta inválida da API ao buscar agendamento:", response);
        return null;
      }

      // Extrair o agendamento da resposta
      const { mainScheduling } = extractData(response.data, 'mainScheduling', ['scheduling']);

      if (!mainScheduling) {
        console.error("Agendamento não encontrado na resposta:", response.data);
        return null;
      }

      // Verificar se o ID está definido
      if (!mainScheduling.id) {
        console.error("Agendamento encontrado, mas sem ID válido. Usando ID original:", id);
        mainScheduling.id = id; // Usar o ID original da requisição
      }

      console.log("Agendamento encontrado:", mainScheduling);

      // Log detalhado para depuração
      console.log("Dados do agendamento encontrado:", {
        id: mainScheduling.id,
        personId: mainScheduling.personId,
        clientId: mainScheduling.clientId,
        Person: mainScheduling.Person,
        insuranceId: mainScheduling.insuranceId,
        insurance: mainScheduling.insurance
      });

      // Extrair o personId de todas as fontes possíveis
      const personId = mainScheduling.Person?.[0]?.id ||
                      mainScheduling.person?.id ||
                      mainScheduling.personId ||
                      mainScheduling.clientId ||
                      "";

      // Log detalhado para depuração
      console.log("[APPOINTMENT-SERVICE] Fontes de personId:", {
        fromPerson: mainScheduling.Person?.[0]?.id,
        fromPersonObj: mainScheduling.person?.id,
        fromPersonId: mainScheduling.personId,
        fromClientId: mainScheduling.clientId,
        final: personId
      });

      // Processar o agendamento para garantir formato consistente
      const processedAppointment = {
        id: mainScheduling.id, // Já garantimos que o ID está definido acima
        title: mainScheduling.title,
        description: mainScheduling.description || "",
        providerId: mainScheduling.userId,
        // Usar o personId extraído
        personId: personId,
        locationId: mainScheduling.locationId,
        serviceTypeId: mainScheduling.serviceTypeId,
        insuranceId: mainScheduling.insuranceId,
        startDate: mainScheduling.startDate,
        endDate: mainScheduling.endDate,
        status: mainScheduling.status || "PENDING",
        // Adicionar objetos completos
        person: mainScheduling.Person?.[0] || mainScheduling.person || null,
        insurance: mainScheduling.insurance || null,
        serviceType: mainScheduling.serviceType || null,
        location: mainScheduling.location || null,
        // Adicionar campos originais para depuração
        clientId: mainScheduling.clientId,
        Person: mainScheduling.Person
      };

      // Verificar novamente se o ID está definido
      if (!processedAppointment.id) {
        console.error("ID do agendamento indefinido após processamento! Usando ID original:", id);
        processedAppointment.id = id; // Usar o ID original da requisição
      }

      // Log do resultado processado
      console.log("Agendamento processado:", {
        id: processedAppointment.id,
        personId: processedAppointment.personId,
        insuranceId: processedAppointment.insuranceId,
        serviceTypeId: processedAppointment.serviceTypeId
      });

      return processedAppointment;
    } catch (error) {
      console.error(`Erro ao buscar agendamento com ID ${id}:`, error);
      return null;
    }
  },

  updateAppointment: async (id, appointmentData) => {
    const response = await api.put(`/schedulings/${id}`, {
      title: appointmentData.title,
      description: appointmentData.description,
      startDate: appointmentData.startDate,
      endDate: appointmentData.endDate,
      userId: appointmentData.providerId,
      personId: appointmentData.personId,
      creatorId: appointmentData.creatorId || appointmentData.providerId,
      locationId: appointmentData.locationId,
      serviceTypeId: appointmentData.serviceTypeId,
      insuranceId: appointmentData.insuranceId,
      status: appointmentData.status,
    });

    return {
      id: response.data.id,
      title: response.data.title,
      description: response.data.description,
      startDate: new Date(response.data.startDate),
      endDate: new Date(response.data.endDate),
      providerId: response.data.userId,
      personId: response.data.personId,
      locationId: response.data.locationId,
      serviceTypeId: response.data.serviceTypeId,
      status: response.data.status,
    };
  },

  deleteAppointment: async (id) => {
    await api.delete(`/schedulings/${id}`);
    return true;
  },

  // Additional methods for getting related data
  getProviders: async () => {
    try {
      console.log("[PROVIDERS] Buscando profissionais para o calendário...");

      // Forçar parâmetros específicos para garantir que recebemos todos os profissionais
      const response = await api.get("/users", {
        params: {
          modules: ["SCHEDULING"],
          limit: 1000, // Aumentar o limite para obter todos os profissionais
          forCalendar: true, // Novo parâmetro para filtrar profissionais para o calendário
          active: true // Apenas usuários ativos
        },
      });

      // Log detalhado da resposta
      console.log("[PROVIDERS] Resposta completa da API:", response);

      // Ensure we have valid user objects with id property
      if (!response?.data?.users) {
        console.warn("[PROVIDERS] Nenhum profissional encontrado na resposta da API");
        return [];
      }

      // Verificar a estrutura dos dados
      const users = response.data.users;
      console.log(`[PROVIDERS] Carregados ${users.length} profissionais`);

      // Verificar se há dados inválidos
      const invalidUsers = users.filter(user => !user || !user.id || !user.fullName);
      if (invalidUsers.length > 0) {
        console.warn("[PROVIDERS] Profissionais com dados inválidos:", invalidUsers);
      }

      // Filtrar apenas usuários válidos e garantir que tenham o módulo SCHEDULING
      const validUsers = users.filter(user => {
        const isValid = user && user.id && user.fullName;
        const hasSchedulingModule = user.modules && Array.isArray(user.modules) &&
                                   user.modules.includes("SCHEDULING");

        return isValid && hasSchedulingModule;
      });

      console.log(`[PROVIDERS] ${validUsers.length} profissionais válidos após filtragem`);

      // Log dos primeiros 5 profissionais para debug
      if (validUsers.length > 0) {
        console.log("[PROVIDERS] Amostra dos primeiros 5 profissionais:",
          validUsers.slice(0, 5).map(u => ({ id: u.id, name: u.fullName }))
        );
      }

      // Criar uma lista de teste se não houver profissionais
      if (validUsers.length === 0) {
        console.warn("[PROVIDERS] Nenhum profissional válido encontrado. Criando lista de teste...");
        return [
          { id: "test-provider-1", fullName: "Dr. Teste 1" },
          { id: "test-provider-2", fullName: "Dr. Teste 2" },
          { id: "test-provider-3", fullName: "Dr. Teste 3" }
        ];
      }

      return validUsers;
    } catch (error) {
      console.error("Erro ao obter provedores:", error);
      // Retornar uma lista de teste em caso de erro
      console.warn("[PROVIDERS] Erro ao buscar profissionais. Criando lista de teste...");
      return [
        { id: "test-provider-1", fullName: "Dr. Teste 1" },
        { id: "test-provider-2", fullName: "Dr. Teste 2" },
        { id: "test-provider-3", fullName: "Dr. Teste 3" }
      ];
    }
  },

  // Get persons instead of clients
  getPersons: async () => {
    try {
      const response = await api.get("/persons", {
        params: {
          limit: 1000 // Aumentar o limite para obter todos os pacientes
        }
      });
      console.log("Persons API response:", response.data);

      // Extrair os dados usando o adaptador para lidar com diferentes formatos
      const { persons } = extractData(response.data, 'persons', ['people']);

      // Verificar se temos dados válidos
      if (!persons || !Array.isArray(persons) || persons.length === 0) {
        console.log("No valid persons data found in response");
        return [];
      }

      // Filter out any invalid entries
      const validPersons = persons.filter(
        person => person && person.id && person.fullName
      );

      console.log(`[PERSONS] Carregados ${validPersons.length} pacientes`);
      return validPersons;
    } catch (error) {
      console.error("Erro ao obter pacientes:", error);
      return [];
    }
  },

  // Keep the clients method for backward compatibility
  getClients: async () => {
    try {
      // Try to use the new persons endpoint first
      try {
        const personsResponse = await api.get("/persons", {
          params: {
            limit: 1000 // Aumentar o limite para obter todos os pacientes
          }
        });
        console.log("Persons API response (from getClients):", personsResponse.data);

        // Extrair os dados usando o adaptador
        const { persons } = extractData(personsResponse.data, 'persons', ['people']);

        if (persons && Array.isArray(persons) && persons.length > 0) {
          const validPersons = persons.filter(p => p && p.id);
          console.log(`[CLIENTS] Carregados ${validPersons.length} clientes via endpoint persons`);
          return validPersons;
        }
      } catch (e) {
        console.log("Persons endpoint failed, falling back to clients", e);
      }

      // Fall back to the original clients endpoint if needed
      const response = await api.get("/clients", {
        params: {
          limit: 1000 // Aumentar o limite para obter todos os clientes
        }
      });
      const { clients } = extractData(response.data, 'clients', ['data']);
      const validClients = (clients || []).filter(client => client && client.id);
      console.log(`[CLIENTS] Carregados ${validClients.length} clientes via endpoint clients`);
      return validClients;
    } catch (error) {
      console.error("Erro ao obter clientes:", error);
      return [];
    }
  },

  getLocations: async () => {
    try {
      const response = await api.get("/locations", {
        params: {
          limit: 1000 // Aumentar o limite para obter todos os locais
        }
      });

      // Make sure we return an array
      const locations = (response?.data?.locations || []).filter(
        location => location && location.id
      );

      console.log(`[LOCATIONS] Carregados ${locations.length} locais`);
      return locations;
    } catch (error) {
      console.error("Erro ao obter locais:", error);
      return [];
    }
  },

  getServiceTypes: async () => {
    try {
      console.log("Fetching service types...");
      const response = await api.get("/service-types", {
        params: {
          limit: 1000 // Aumentar o limite para obter todos os tipos de serviço
        }
      });
      console.log("Service types response:", response?.data);

      // FIXED: Changed from serviceType (singular) to serviceTypes (plural)
      const serviceTypes = response?.data?.serviceTypes || [];

      // Handle both array and single object cases
      let validServiceTypes = [];
      if (Array.isArray(serviceTypes)) {
        validServiceTypes = serviceTypes.filter(type => type && type.id);
      } else if (serviceTypes && serviceTypes.id) {
        validServiceTypes = [serviceTypes];
      }

      console.log(`[SERVICE_TYPES] Carregados ${validServiceTypes.length} tipos de serviço`);
      return validServiceTypes;
    } catch (error) {
      console.error("Erro ao obter tipos de serviço:", error);
      return [];
    }
  },

  getInsurances: async () => {
    try {
      const response = await api.get("/insurances", {
        params: {
          limit: 1000 // Aumentar o limite para obter todos os convênios
        }
      });
      console.log("API insurances response:", response.data);

      // Aqui verificamos a estrutura da resposta e extraímos o array de convênios
      let insurances = [];

      if (response?.data?.insurances && Array.isArray(response.data.insurances)) {
        // Se a resposta tem um campo 'insurances' que é um array
        insurances = response.data.insurances;
      } else if (Array.isArray(response?.data)) {
        // Se a resposta já é um array diretamente
        insurances = response.data;
      } else if (response?.data) {
        // Se a resposta é um objeto, procuramos um campo que possa conter os convênios
        const possibleArrayFields = ['data', 'items', 'results', 'list'];
        for (const field of possibleArrayFields) {
          if (response.data[field] && Array.isArray(response.data[field])) {
            insurances = response.data[field];
            break;
          }
        }
      }

      // Filtramos para garantir que cada item tenha um ID
      const validInsurances = insurances.filter(insurance => insurance && insurance.id);
      console.log(`[INSURANCES] Carregados ${validInsurances.length} convênios`);
      return validInsurances;
    } catch (error) {
      console.error("Erro ao obter convênios:", error);
      return [];
    }
  },

  /**
 * Exporta a lista de agendamentos com os filtros aplicados
 * @param {Object} filters - Filtros atuais (busca, status, etc)
 * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
 * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
 */
  exportAppointments: async (filters, exportFormat = "xlsx") => {
    try {
      // Obter os dados filtrados da API
      const response = await appointmentService.getAppointments(filters);

      // Extrair os dados dos agendamentos
      const data = response?.appointments || [];

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Mapeamento de status para textos legíveis
      const statusLabels = {
        "PENDING": "Pendente",
        "CONFIRMED": "Confirmado",
        "CANCELLED": "Cancelado",
        "COMPLETED": "Concluído",
        "NO_SHOW": "Não Compareceu"
      };

      // Definição das colunas com formatação
      const columns = [
        { key: "title", header: "Título" },
        {
          key: "startDate",
          header: "Data Início",
          type: "date"
        },
        {
          key: "startTime",
          header: "Hora Início",
          format: (_, item) => {
            const date = new Date(item.startDate);
            return dateFormat(date, "HH:mm", { locale: ptBR });
          }
        },
        {
          key: "endDate",
          header: "Data Fim",
          type: "date"
        },
        {
          key: "endTime",
          header: "Hora Fim",
          format: (_, item) => {
            const date = new Date(item.endDate);
            return dateFormat(date, "HH:mm", { locale: ptBR });
          }
        },
        { key: "personfullName", header: "Paciente" },
        { key: "providerfullName", header: "Profissional" },
        { key: "serviceTypefullName", header: "Tipo de Serviço" },
        {
          key: "status",
          header: "Status",
          format: (value) => statusLabels[value] || value,
          align: "center"
        },
        { key: "description", header: "Descrição" }
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(appointment => {
        return {
          title: appointment.title || "",
          startDate: appointment.startDate || "",
          startTime: appointment.startDate || "",
          endDate: appointment.endDate || "",
          endTime: appointment.endDate || "",
          personfullName: appointment.personfullName || "",
          providerfullName: appointment.providerfullName || "",
          serviceTypefullName: appointment.serviceTypefullName || "",
          status: appointment.status || "PENDING",
          description: appointment.description || ""
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];

      // Adicionar informações de filtro ao subtítulo se disponíveis
      if (filters.providers?.length) {
        subtitleParts.push(`Profissionais: ${filters.providers.length} selecionados`);
      }

      if (filters.persons?.length) {
        subtitleParts.push(`Pacientes: ${filters.persons.length} selecionados`);
      }

      if (filters.serviceTypes?.length) {
        subtitleParts.push(`Serviços: ${filters.serviceTypes.length} selecionados`);
      }

      if (filters.locations?.length) {
        subtitleParts.push(`Locais: ${filters.locations.length} selecionados`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "agendamentos",
        columns,
        title: "Lista de Agendamentos",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar agendamentos:", error);
      return false;
    }
  },

  checkInsuranceLimits: async (params) => {
    try {
      // Aceitar tanto o formato de objeto quanto parâmetros individuais para compatibilidade
      let personId, insuranceId, serviceTypeId, appointmentDate;

      if (typeof params === 'object' && params !== null && !Array.isArray(params)) {
        // Formato de objeto
        personId = params.personId;
        insuranceId = params.insuranceId;
        serviceTypeId = params.serviceTypeId;
        appointmentDate = params.appointmentDate || params.startDate;
      } else {
        // Formato de parâmetros individuais (legado)
        personId = params;
        insuranceId = arguments[1];
        serviceTypeId = arguments[2];
        appointmentDate = arguments[3];
      }

      console.log(`Verificando limites de convênio para: Pessoa=${personId}, Convênio=${insuranceId}, Serviço=${serviceTypeId}, Data=${appointmentDate}`);

      if (!personId || !insuranceId || !serviceTypeId) {
        console.log('Dados insuficientes para verificar limites de convênio');
        return { available: true, message: "Dados insuficientes para verificar limites" };
      }

      // Converter a data para string se for um objeto Date
      const dateParam = appointmentDate instanceof Date ? appointmentDate.toISOString() : appointmentDate;
      console.log(`Data para verificação de limites: ${dateParam}`);

      const response = await api.post(`/insurance-service-limits/check`, {
        personId,
        insuranceId,
        serviceTypeId,
        date: dateParam
      });

      console.log(`Resposta da verificação de limites:`, response.data);
      return response.data || { available: true };
    } catch (error) {
      console.error("Erro ao verificar limites de convênio:", error);
      // Em caso de erro, permitir a operação para não bloquear o usuário
      return { available: true, error: error.message };
    }
  },

  // Verificar disponibilidade de horário no servidor para um profissional
  checkAvailability: async ({ providerId, startDate, endDate, excludeId }) => {
    try {
      console.log(`[SERVER-CHECK] Verificando disponibilidade no servidor para: ${providerId}, ${startDate} - ${endDate}`);

      // Preparar parâmetros para a requisição
      const params = {
        providerId,
        startDate,
        endDate
      };

      // Se tiver um ID para excluir (caso de edição), adicionar ao parâmetro
      if (excludeId) {
        params.excludeId = excludeId;
      }

      // Fazer a requisição para verificar disponibilidade
      const response = await api.post(`/schedulings/check-availability`, params);

      // Se a resposta não tiver dados, assumir que está disponível
      if (!response.data) {
        return { available: true };
      }

      console.log(`[SERVER-CHECK] Resposta do servidor:`, response.data);

      // Retornar os dados da resposta
      return response.data;
    } catch (error) {
      console.error("Erro ao verificar disponibilidade no servidor:", error);

      // Se o erro for 409 (Conflict), significa que há um conflito de horário
      if (error.response && error.response.status === 409 && error.response.data) {
        return {
          available: false,
          conflict: error.response.data.conflictData || error.response.data
        };
      }

      // Em caso de outros erros, assumir que está disponível para não bloquear o usuário
      return { available: true, error: error.message };
    }
  },

  // Verificar disponibilidade de horário no servidor para um paciente
  checkPatientAvailability: async ({ personId, startDate, endDate, excludeId }) => {
    try {
      console.log(`[SERVER-CHECK] Verificando disponibilidade do paciente no servidor para: ${personId}, ${startDate} - ${endDate}`);

      // Preparar parâmetros para a requisição
      const params = {
        personId,
        startDate,
        endDate
      };

      // Se tiver um ID para excluir (caso de edição), adicionar ao parâmetro
      if (excludeId) {
        params.excludeId = excludeId;
      }

      // Fazer a requisição para verificar disponibilidade
      const response = await api.post(`/schedulings/check-patient-availability`, params);

      // Se a resposta não tiver dados, assumir que está disponível
      if (!response.data) {
        return { available: true };
      }

      console.log(`[SERVER-CHECK] Resposta do servidor para disponibilidade do paciente:`, response.data);

      // Retornar os dados da resposta
      return response.data;
    } catch (error) {
      console.error("Erro ao verificar disponibilidade do paciente no servidor:", error);

      // Se o erro for 409 (Conflict), significa que há um conflito de horário
      if (error.response && error.response.status === 409 && error.response.data) {
        return {
          available: false,
          conflict: error.response.data.conflictData || error.response.data
        };
      }

      // Em caso de outros erros, assumir que está disponível para não bloquear o usuário
      return { available: true, error: error.message };
    }
  }
};