// src/routes/chat/messageRoutes.js
const express = require('express');
const router = express.Router();
const messageController = require('../../controllers/chat/messageController');
const { cacheMiddleware } = require('../../middlewares/cache');

// Rotas de mensagens
router.get('/unread', cacheMiddleware('chat:unread', 30), messageController.getUnreadMessages);
router.delete('/:messageId', messageController.deleteMessage);

module.exports = router;
