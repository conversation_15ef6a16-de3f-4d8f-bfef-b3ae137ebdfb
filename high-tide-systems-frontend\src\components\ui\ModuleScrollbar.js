'use client';

import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import '@/styles/scrollbar.css';

/**
 * Componente que aplica estilos de scrollbar personalizados com base no módulo atual
 *
 * Este componente deve ser incluído no layout principal da aplicação
 */
const ModuleScrollbar = () => {
  const pathname = usePathname();

  useEffect(() => {
    // Determinar o módulo atual com base no pathname
    let currentModule = 'default';

    if (pathname.includes('/modules/people') || pathname.includes('/pessoas')) {
      currentModule = 'people';
    } else if (pathname.includes('/modules/scheduler') || pathname.includes('/agendamento')) {
      currentModule = 'scheduler';
    } else if (pathname.includes('/modules/admin') || pathname.includes('/admin')) {
      currentModule = 'admin';
    } else if (pathname.includes('/modules/financial') || pathname.includes('/financeiro')) {
      currentModule = 'financial';
    }

    // Remover todas as classes de módulo anteriores do document e body
    document.documentElement.classList.remove(
      'module-people',
      'module-scheduler',
      'module-admin',
      'module-financial',
      'custom-scrollbar'
    );

    document.body.classList.remove(
      'module-people',
      'module-scheduler',
      'module-admin',
      'module-financial',
      'custom-scrollbar'
    );

    // Adicionar a classe do módulo atual ao document e body
    if (currentModule !== 'default') {
      document.documentElement.classList.add(`module-${currentModule}`);
      document.body.classList.add(`module-${currentModule}`);
    }

    // Adicionar classe para scrollbar personalizado ao document e body
    document.documentElement.classList.add('custom-scrollbar');
    document.body.classList.add('custom-scrollbar');

    // Limpar ao desmontar
    return () => {
      document.documentElement.classList.remove(
        'module-people',
        'module-scheduler',
        'module-admin',
        'module-financial',
        'custom-scrollbar'
      );

      document.body.classList.remove(
        'module-people',
        'module-scheduler',
        'module-admin',
        'module-financial',
        'custom-scrollbar'
      );
    };
  }, [pathname]);

  // Este componente não renderiza nada visualmente
  return null;
};

export default ModuleScrollbar;
