"use client";

import React, { createContext, useContext, useState, useCallback } from 'react';

// Contexto para o sistema de mensagens "Em Construção"
const ConstructionContext = createContext(null);

// Provider que disponibiliza as funções e estados das mensagens
export const ConstructionProvider = ({ children }) => {
  // Estado para controlar se a mensagem está ativa
  const [isActive, setIsActive] = useState(false);
  
  // Estado para armazenar as informações da mensagem
  const [messageInfo, setMessageInfo] = useState({
    title: 'Em Construção',
    content: 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',
    position: 'auto',
    targetSelector: null,
    icon: 'Construction' // Ícone padrão
  });

  // Função para mostrar a mensagem "Em Construção"
  const showConstructionMessage = useCallback((options = {}) => {
    // Fechar qualquer mensagem que possa estar aberta
    setIsActive(false);
    
    // Definir a nova mensagem após um pequeno atraso
    setTimeout(() => {
      setMessageInfo({
        title: options.title || 'Em Construção',
        content: options.content || 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',
        position: options.position || 'auto',
        targetSelector: options.targetSelector || null,
        icon: options.icon || 'Construction'
      });
      setIsActive(true);
    }, 50);
  }, []);

  // Função para fechar a mensagem
  const closeMessage = useCallback(() => {
    setIsActive(false);
  }, []);

  return (
    <ConstructionContext.Provider
      value={{
        isActive,
        messageInfo,
        showConstructionMessage,
        closeMessage
      }}
    >
      {children}
    </ConstructionContext.Provider>
  );
};

// Hook personalizado para usar o contexto de mensagens "Em Construção"
export const useConstruction = () => {
  const context = useContext(ConstructionContext);
  
  if (!context) {
    throw new Error('useConstruction deve ser usado dentro de um ConstructionProvider');
  }
  
  return context;
};
