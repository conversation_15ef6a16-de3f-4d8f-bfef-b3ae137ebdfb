const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { cacheMiddleware, clearCacheMiddleware } = require('../../middlewares/cache');
const { SchedulingController } = require('../../controllers/scheduling/schedulingController');
const { SchedulingDashboardController } = require('../../controllers/scheduling/schedulingDashboardController');

// Configurar TTL para cache de agendamentos (2 minutos)
const SCHEDULING_CACHE_TTL = 120;

// Aplicar middleware para limpar cache quando dados são modificados
router.post('/', authenticate, clearCacheMiddleware('scheduling:*'), SchedulingController.create);

// Rota para verificar disponibilidade de horário para profissional
router.post('/check-availability', authenticate, SchedulingController.checkAvailability);

// Rota para verificar disponibilidade de horário para paciente
router.post('/check-patient-availability', authenticate, SchedulingController.checkPatientAvailability);

// Aplicar cache para listagens e consultas
router.get('/', authenticate, cacheMiddleware('scheduling:list', SCHEDULING_CACHE_TTL), SchedulingController.list);
router.get('/dashboard', authenticate, cacheMiddleware('scheduling:dashboard', SCHEDULING_CACHE_TTL), SchedulingDashboardController.getDashboardData);
router.get('/:id', authenticate, cacheMiddleware('scheduling:detail', SCHEDULING_CACHE_TTL), SchedulingController.get);

// Limpar cache quando dados são modificados
router.put('/:id', authenticate, clearCacheMiddleware('scheduling:*'), SchedulingController.update);
router.delete('/:id', authenticate, clearCacheMiddleware('scheduling:*'), SchedulingController.delete);

module.exports = router;