// src/swagger/insuranceRoutes.js

/**
 * @swagger
 * tags:
 *   name: Convênios
 *   description: Gerenciamento de convênios médicos
 */

/**
 * @swagger
 * /insurances:
 *   post:
 *     summary: Cria um novo convênio
 *     description: Cria um novo convênio no sistema.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome do convênio
 *                 example: "Unimed"
 *     responses:
 *       201:
 *         description: Convênio criado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Insurance'
 *       400:
 *         description: Dados inválidos ou nome já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Nome do convênio já existe"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista convênios
 *     description: Retorna uma lista de convênios. Permite filtrar por busca.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Texto para busca pelo nome
 *     responses:
 *       200:
 *         description: Lista de convênios
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Insurance'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /insurances/{id}:
 *   get:
 *     summary: Obtém detalhes de um convênio
 *     description: Retorna os detalhes completos de um convênio específico.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do convênio
 *     responses:
 *       200:
 *         description: Detalhes do convênio
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Insurance'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Convênio não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Convênio não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   put:
 *     summary: Atualiza um convênio
 *     description: Atualiza os dados de um convênio existente.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do convênio
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome do convênio
 *     responses:
 *       200:
 *         description: Convênio atualizado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Insurance'
 *       400:
 *         description: Dados inválidos ou nome já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Nome do convênio já existe"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Convênio não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Convênio não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove um convênio
 *     description: Remove um convênio do sistema.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do convênio
 *     responses:
 *       204:
 *         description: Convênio removido com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Convênio não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Convênio não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /insurances/client:
 *   post:
 *     summary: Adiciona um convênio a um cliente
 *     description: Associa um convênio existente a um cliente.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - clientId
 *               - insuranceId
 *             properties:
 *               clientId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do cliente
 *               insuranceId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do convênio
 *     responses:
 *       201:
 *         description: Convênio associado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 clientId:
 *                   type: string
 *                   format: uuid
 *                 insuranceId:
 *                   type: string
 *                   format: uuid
 *                 client:
 *                   $ref: '#/components/schemas/ClientResponse'
 *                 insurance:
 *                   $ref: '#/components/schemas/Insurance'
 *       400:
 *         description: Dados inválidos ou cliente já possui este convênio
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Cliente já possui este convênio"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /insurances/client/{clientId}/{insuranceId}:
 *   delete:
 *     summary: Remove um convênio de um cliente
 *     description: Remove a associação entre um cliente e um convênio.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do cliente
 *       - in: path
 *         name: insuranceId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do convênio
 *     responses:
 *       204:
 *         description: Associação removida com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Cliente ou convênio não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /insurances/client/{clientId}:
 *   get:
 *     summary: Lista convênios de um cliente
 *     description: Retorna todos os convênios associados a um cliente específico.
 *     tags: [Convênios]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: clientId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do cliente
 *     responses:
 *       200:
 *         description: Lista de convênios do cliente
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   clientId:
 *                     type: string
 *                     format: uuid
 *                   insuranceId:
 *                     type: string
 *                     format: uuid
 *                   insurance:
 *                     $ref: '#/components/schemas/Insurance'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Cliente não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */