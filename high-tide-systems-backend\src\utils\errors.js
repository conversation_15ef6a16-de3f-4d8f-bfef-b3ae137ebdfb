// src/utils/errors.js

/**
 * Classe base para erros da aplicação
 */
class AppError extends Error {
  constructor(message, status) {
    super(message);
    this.name = this.constructor.name;
    this.status = status;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Erro para recursos não encontrados
 */
class NotFoundError extends AppError {
  constructor(message = 'Recurso não encontrado') {
    super(message, 404);
  }
}

/**
 * Erro para operações não autorizadas
 */
class UnauthorizedError extends AppError {
  constructor(message = 'Não autorizado') {
    super(message, 401);
  }
}

/**
 * Erro para acesso proibido
 */
class ForbiddenError extends AppError {
  constructor(message = 'Acesso negado') {
    super(message, 403);
  }
}

/**
 * Erro para dados inválidos
 */
class ValidationError extends AppError {
  constructor(message = 'Dados inválidos', errors = []) {
    super(message, 400);
    this.errors = errors;
  }
}

/**
 * Erro para conflitos
 */
class ConflictError extends AppError {
  constructor(message = 'Conflito de recursos') {
    super(message, 409);
  }
}

/**
 * Erro para serviços externos
 */
class ExternalServiceError extends AppError {
  constructor(message = 'Erro em serviço externo', service = 'unknown') {
    super(message, 502);
    this.service = service;
  }
}

module.exports = {
  AppError,
  NotFoundError,
  UnauthorizedError,
  ForbiddenError,
  ValidationError,
  ConflictError,
  ExternalServiceError
};
