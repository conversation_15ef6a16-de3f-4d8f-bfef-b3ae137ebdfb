import { api } from "@/utils/api";

export const emailConfigService = {
  // Obter configurações de email
  getEmailConfigs: async ({ companyId } = {}) => {
    try {
      const params = new URLSearchParams();
      if (companyId) params.append('companyId', companyId);

      const response = await api.get(`/email-configs?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar configurações de email:", error);
      throw error;
    }
  },

  // Obter uma configuração de email específica
  getEmailConfig: async (id) => {
    try {
      const response = await api.get(`/email-configs/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar configuração de email ${id}:`, error);
      throw error;
    }
  },

  // Criar uma nova configuração de email
  createEmailConfig: async (data) => {
    try {
      const response = await api.post('/email-configs', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar configuração de email:", error);
      throw error;
    }
  },

  // Atualizar uma configuração de email existente
  updateEmailConfig: async (id, data) => {
    try {
      const response = await api.put(`/email-configs/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar configuração de email ${id}:`, error);
      throw error;
    }
  },

  // Alternar o status de uma configuração de email (ativo/inativo)
  toggleEmailConfigStatus: async (id) => {
    try {
      const response = await api.patch(`/email-configs/${id}/toggle-status`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao alterar status da configuração de email ${id}:`, error);
      throw error;
    }
  },

  // Excluir uma configuração de email
  deleteEmailConfig: async (id) => {
    try {
      await api.delete(`/email-configs/${id}`);
      return true;
    } catch (error) {
      console.error(`Erro ao excluir configuração de email ${id}:`, error);
      throw error;
    }
  },

  // Testar uma configuração de email
  testEmailConfig: async (id, { testEmail }) => {
    try {
      const response = await api.post(`/email-configs/${id}/test`, { testEmail });
      return response.data;
    } catch (error) {
      console.error(`Erro ao testar configuração de email ${id}:`, error);
      throw error;
    }
  }
};

export default emailConfigService;