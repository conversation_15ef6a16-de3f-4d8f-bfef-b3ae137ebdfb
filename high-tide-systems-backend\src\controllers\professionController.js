// src/controllers/professionController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");
const errorHandler = require("../utils/errorHandler");

// Validações
const createProfessionValidation = [
  body("name").notEmpty().withMessage("Nome da profissão é obrigatório"),
  body("description").optional(),
  body("groupId").optional(),
  body("companyId").optional(),
];

const updateProfessionValidation = [
  body("name").optional(),
  body("description").optional(),
  body("groupId").optional(),
  body("active").optional().isBoolean().withMessage("O campo active deve ser um booleano"),
];

class ProfessionController {
  /**
   * Lista todos os usuários de uma profissão específica
   */
  static async listUsers(req, res) {
    try {
      const { id } = req.params;
      const { active } = req.query;

      // Verificar se a profissão existe
      const profession = await prisma.profession.findUnique({
        where: { id }
      });

      if (!profession) {
        return res.status(404).json({ message: 'Profissão não encontrada' });
      }

      // Construir filtro para o Prisma
      const where = {
        professionId: id,
        deletedAt: null
      };

      // Filtrar por status ativo se especificado
      if (active !== undefined) {
        where.active = active === 'true';
      }

      // Se o usuário não for SYSTEM_ADMIN, limitar à mesma empresa
      if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
        where.companyId = req.user.companyId;
      }

      // Buscar usuários com a profissão especificada
      const users = await prisma.user.findMany({
        where,
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          phone: true,
          active: true,
          role: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true
            }
          }
        },
        orderBy: {
          fullName: 'asc'
        }
      });

      res.json({
        profession,
        users,
        total: users.length
      });
    } catch (error) {
      console.error('Erro ao listar usuários da profissão:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Lista todas as profissões
   */
  static async list(req, res) {
    try {
      const {
        search,
        groupId,
        active,
        companyId,
        professionIds,
        sortField = 'name',
        sortDirection = 'asc'
      } = req.query;

      // Construir filtro para o Prisma
      const where = {};

      // Filtrar por IDs específicos de profissões
      if (professionIds) {
        // Converter para array se for um único valor
        let professionIdsArray;

        if (Array.isArray(professionIds)) {
          professionIdsArray = professionIds;
        } else if (typeof professionIds === 'string' && professionIds.includes(',')) {
          // Se for uma string com vírgulas, dividir em array
          professionIdsArray = professionIds.split(',');
        } else {
          professionIdsArray = [professionIds];
        }

        // Garantir que todos os IDs são strings
        professionIdsArray = professionIdsArray.map(id => String(id));

        where.id = { in: professionIdsArray };
        console.log("Filtrando profissões por IDs:", professionIdsArray);
      }

      // Filtrar por status ativo
      if (active !== undefined) {
        where.active = active === 'true';
      }

      // Filtrar por grupo
      if (groupId) {
        where.groupId = groupId;
      }

      // Filtrar por empresa
      if (companyId) {
        where.companyId = companyId;
      } else if (req.user?.companyId) {
        // Se não foi especificado, mas o usuário tem uma empresa, filtrar por ela
        where.companyId = req.user.companyId;
      }
      // Se o usuário não tem empresa (como SYSTEM_ADMIN), não filtrar por empresa

      // Adicionar condição para ignorar registros excluídos logicamente
      where.deletedAt = null;

      // Validar e limpar parâmetros de ordenação
      const cleanSortField = ['name', 'description', 'createdAt', 'updatedAt'].includes(sortField)
        ? sortField
        : 'name';
      const cleanSortDirection = ['asc', 'desc'].includes(sortDirection)
        ? sortDirection
        : 'asc';

      // Construir objeto de ordenação
      const orderBy = {};
      orderBy[cleanSortField] = cleanSortDirection;

      console.log(`Ordenando profissões por ${cleanSortField} em ordem ${cleanSortDirection}`);

      // Buscar profissões com filtros
      let professions = await prisma.profession.findMany({
        where,
        include: {
          group: {
            select: {
              id: true,
              name: true
            }
          },
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              users: true
            }
          }
        },
        orderBy
      });

      // Transformar o resultado para o formato esperado
      professions = professions.map(p => ({
        id: p.id,
        name: p.name,
        description: p.description,
        active: p.active,
        createdAt: p.createdAt,
        updatedAt: p.updatedAt,
        companyId: p.companyId,
        groupId: p.groupId,
        group: p.group,
        company: p.company,
        _count: p._count
      }));

      // Filtrar por termo de busca (no nome ou descrição)
      if (search) {
        const searchLower = search.toLowerCase();
        professions = professions.filter(profession =>
          profession.name.toLowerCase().includes(searchLower) ||
          (profession.description && profession.description.toLowerCase().includes(searchLower))
        );
      }

      res.json(professions);
    } catch (error) {
      console.error('Erro ao listar profissões:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Obtém uma profissão pelo ID
   */
  static async getById(req, res) {
    try {
      const { id } = req.params;

      // Buscar a profissão pelo ID
      const profession = await prisma.profession.findUnique({
        where: { id },
        include: {
          group: {
            select: {
              id: true,
              name: true
            }
          },
          company: {
            select: {
              id: true,
              name: true
            }
          },
          _count: {
            select: {
              users: true
            }
          }
        }
      });

      // Transformar o resultado para o formato esperado
      const formattedProfession = profession ? {
        id: profession.id,
        name: profession.name,
        description: profession.description,
        active: profession.active,
        createdAt: profession.createdAt,
        updatedAt: profession.updatedAt,
        companyId: profession.companyId,
        groupId: profession.groupId,
        group: profession.group,
        company: profession.company,
        _count: profession._count
      } : null;

      if (!formattedProfession) {
        return res.status(404).json({ message: 'Profissão não encontrada' });
      }

      res.json(formattedProfession);
    } catch (error) {
      console.error('Erro ao buscar profissão:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Cria uma nova profissão
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, description, groupId } = req.body;

      // Determinar a empresa
      let companyId = req.body.companyId;
      if (!companyId && req.user?.companyId) {
        companyId = req.user.companyId;
      }

      // Verificar se o grupo existe, se fornecido
      if (groupId) {
        const group = await prisma.professionGroup.findUnique({
          where: { id: groupId }
        });

        if (!group) {
          return res.status(404).json({ message: 'Grupo de profissão não encontrado' });
        }
      }

      // Verificar se já existe uma profissão com o mesmo nome na empresa
      const existingProfession = await prisma.profession.findFirst({
        where: {
          name,
          companyId
        }
      });

      if (existingProfession) {
        return res.status(400).json({ message: 'Já existe uma profissão com este nome na empresa' });
      }

      // Criar a profissão
      const profession = await prisma.profession.create({
        data: {
          name,
          description,
          groupId,
          companyId
        }
      });

      res.status(201).json(profession);
    } catch (error) {
      console.error('Erro ao criar profissão:', error);
      const errorResponse = errorHandler.handleError(error);
      res.status(errorResponse.status).json({
        message: errorResponse.message,
        errors: errorResponse.errors
      });
    }
  }

  /**
   * Atualiza uma profissão existente
   */
  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { name, description, groupId, active } = req.body;

      // Verificar se a profissão existe
      const existingProfession = await prisma.profession.findUnique({
        where: { id }
      });

      if (!existingProfession) {
        return res.status(404).json({ message: 'Profissão não encontrada' });
      }

      // Verificar se o usuário tem permissão para editar esta profissão
      if (req.user?.companyId && existingProfession.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para editar esta profissão' });
      }

      // Verificar se o grupo existe, se fornecido
      if (groupId) {
        const group = await prisma.professionGroup.findUnique({
          where: { id: groupId }
        });

        if (!group) {
          return res.status(404).json({ message: 'Grupo de profissão não encontrado' });
        }
      }

      // Verificar se já existe outra profissão com o mesmo nome na empresa
      if (name && name !== existingProfession.name) {
        const duplicateProfession = await prisma.profession.findFirst({
          where: {
            name,
            companyId: existingProfession.companyId,
            id: { not: id }
          }
        });

        if (duplicateProfession) {
          return res.status(400).json({ message: 'Já existe outra profissão com este nome na empresa' });
        }
      }

      // Preparar dados para atualização
      const updateData = {};
      if (name !== undefined) updateData.name = name;
      if (description !== undefined) updateData.description = description;
      if (groupId !== undefined) updateData.groupId = groupId;
      if (active !== undefined) updateData.active = active;

      // Atualizar a profissão
      const profession = await prisma.profession.update({
        where: { id },
        data: updateData
      });

      res.json(profession);
    } catch (error) {
      console.error('Erro ao atualizar profissão:', error);
      const errorResponse = errorHandler.handleError(error);
      res.status(errorResponse.status).json({
        message: errorResponse.message,
        errors: errorResponse.errors
      });
    }
  }

  /**
   * Remove uma profissão (soft delete)
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a profissão existe
      const profession = await prisma.profession.findUnique({
        where: { id }
      });

      if (!profession) {
        return res.status(404).json({ message: 'Profissão não encontrada' });
      }

      // Verificar se o usuário tem permissão para excluir esta profissão
      if (req.user?.companyId && profession.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Você não tem permissão para excluir esta profissão' });
      }

      // Verificar se há usuários usando esta profissão
      const usersCount = await prisma.user.count({
        where: { professionId: id }
      });

      if (usersCount > 0) {
        return res.status(400).json({
          message: 'Esta profissão está sendo usada por usuários e não pode ser excluída',
          usersCount
        });
      }

      // Realizar soft delete
      await prisma.profession.update({
        where: { id },
        data: {
          active: false,
          deletedAt: new Date()
        }
      });

      res.json({ message: 'Profissão excluída com sucesso' });
    } catch (error) {
      console.error('Erro ao excluir profissão:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  ProfessionController,
  createProfessionValidation,
  updateProfessionValidation
};
