'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * Componente de seção de filtros para a tabela
 * 
 * @param {Object} props
 * @param {boolean} props.isOpen - Se a seção de filtros está aberta
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {React.ReactNode} props.children - Conte<PERSON>do da seção de filtros
 */
const FilterSection = ({ isOpen, moduleColor = 'default', children }) => {
  if (!isOpen) return null;

  // Cores baseadas no módulo
  const colors = {
    default: {
      borderColor: 'border-neutral-200 dark:border-gray-700',
      bgColor: 'bg-neutral-50 dark:bg-gray-700',
    },
    people: {
      borderColor: 'border-module-people-border/30 dark:border-module-people-border-dark/30',
      bgColor: 'bg-module-people-bg/5 dark:bg-module-people-bg-dark/5',
    },
    scheduler: {
      borderColor: 'border-module-scheduler-border/30 dark:border-module-scheduler-border-dark/30',
      bgColor: 'bg-module-scheduler-bg/5 dark:bg-module-scheduler-bg-dark/5',
    },
    admin: {
      borderColor: 'border-module-admin-border/30 dark:border-module-admin-border-dark/30',
      bgColor: 'bg-module-admin-bg/5 dark:bg-module-admin-bg-dark/5',
    },
    financial: {
      borderColor: 'border-module-financial-border/30 dark:border-module-financial-border-dark/30',
      bgColor: 'bg-module-financial-bg/5 dark:bg-module-financial-bg-dark/5',
    },
    abaplus: {
      borderColor: 'border-module-abaplus-border/30 dark:border-module-abaplus-border-dark/30',
      bgColor: 'bg-module-abaplus-bg/5 dark:bg-module-abaplus-bg-dark/5',
    }
  };

  const moduleColors = colors[moduleColor] || colors.default;

  return (
    <div className={cn(
      "p-6 mb-6 rounded-xl border shadow-soft dark:shadow-lg dark:shadow-black/30",
      moduleColors.borderColor,
      moduleColors.bgColor
    )}>
      {children}
    </div>
  );
};

export default FilterSection;
