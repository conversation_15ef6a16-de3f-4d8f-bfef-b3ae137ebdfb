'use client';

import { useRef, useEffect } from 'react';

/**
 * Hook para criar um "foco preso" em um elemento
 * Útil para modais e menus de acessibilidade
 * @param {boolean} isActive - Se o trap deve estar ativo
 * @returns {React.RefObject} - Ref para aplicar ao elemento
 */
export function useFocusTrap(isActive) {
  const elementRef = useRef(null);
  
  useEffect(() => {
    if (!isActive || !elementRef.current) return;
    
    const element = elementRef.current;
    const focusableElements = element.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    if (focusableElements.length === 0) return;
    
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    // Focar o primeiro elemento quando o trap é ativado
    firstElement.focus();
    
    const handleKeyDown = (e) => {
      if (e.key !== 'Tab') return;
      
      // Shift + Tab => navegação para trás
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } 
      // Tab => navegação para frente
      else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };
    
    element.addEventListener('keydown', handleKeyDown);
    return () => {
      element.removeEventListener('keydown', handleKeyDown);
    };
  }, [isActive]);
  
  return elementRef;
}