const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { cacheMiddleware, clearCacheMiddleware } = require('../../middlewares/cache');
const { ClientController, createClientValidation } = require('../../controllers/clientController');

// Configurar TTL para cache de clientes (5 minutos)
const CLIENT_CACHE_TTL = 300;

// Rotas protegidas
router.use(authenticate);

// Aplicar middleware para limpar cache quando dados são modificados
router.post('/', createClientValidation, clearCacheMiddleware('clients:*'), ClientController.create);

// Aplicar cache para listagens e consultas
router.get('/', cacheMiddleware('clients:list', CLIENT_CACHE_TTL), ClientController.list);
router.get('/:id', cacheMiddleware('clients:detail', CLIENT_CACHE_TTL), ClientController.getProfile);

// Limpar cache quando dados são modificados
router.put('/:id', clearCacheMiddleware('clients:*'), ClientController.update);
router.patch('/:id/status', clearCacheMiddleware('clients:*'), ClientController.toggleStatus);
router.delete('/:id', clearCacheMiddleware('clients:*'), ClientController.delete);

module.exports = router;