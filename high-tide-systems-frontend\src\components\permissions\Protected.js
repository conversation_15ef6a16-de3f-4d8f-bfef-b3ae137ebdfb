'use client';

import React from 'react';
import { usePermissions } from '@/hooks/usePermissions';

// Componente que renderiza condicionalmente baseado em permissões
export function Protected({ 
  permission, 
  requireAll = false, 
  fallback = null, 
  children 
}) {
  const { can, canAll, canAny } = usePermissions();
  
  const hasAccess = React.useMemo(() => {
    if (!permission) return true;
    
    if (Array.isArray(permission)) {
      return requireAll ? canAll(permission) : canAny(permission);
    }
    
    return can(permission);
  }, [permission, requireAll, can, canAll, canAny]);
  
  return hasAccess ? children : fallback;
}

// Componente que renderiza condicionalmente baseado em módulos
export function ProtectedModule({ 
  module, 
  requireAll = false, 
  fallback = null, 
  children 
}) {
  const { hasModule, isAdmin } = usePermissions();
  
  const hasAccess = React.useMemo(() => {
    if (isAdmin()) return true;
    
    if (!module) return true;
    
    if (Array.isArray(module)) {
      return requireAll 
        ? module.every(m => hasModule(m))
        : module.some(m => hasModule(m));
    }
    
    return hasModule(module);
  }, [module, requireAll, hasModule, isAdmin]);
  
  return hasAccess ? children : fallback;
}

// Componente que renderiza apenas para administradores
export function AdminOnly({ fallback = null, children }) {
  const { isAdmin } = usePermissions();
  
  return isAdmin() ? children : fallback;
}