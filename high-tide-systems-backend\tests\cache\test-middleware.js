// test-middleware.js
require('dotenv').config();
const { cacheMiddleware } = require('../../src/middlewares/cache');
const cacheService = require('../../src/services/cacheService');

// Mock de objetos Express
function createMockExpressObjects() {
  const req = {
    originalUrl: '/api/test',
    method: 'GET',
    user: {
      id: 1,
      role: 'ADMIN',
      companyId: 1
    }
  };

  const res = {
    statusCode: 200,
    jsonCalled: false,
    jsonData: null,
    json: function(data) {
      this.jsonCalled = true;
      this.jsonData = data;
      return this;
    }
  };

  const next = jest.fn();

  return { req, res, next };
}

async function testCacheMiddleware() {
  try {
    console.log('=== TESTE DO MIDDLEWARE DE CACHE ===\n');

    // Inicializar o serviço de cache
    console.log('Inicializando serviço de cache...');
    await cacheService.initialize();
    console.log('Serviço de cache inicializado');

    // Limpar qualquer cache existente para o teste
    await cacheService.clear('test:*');
    console.log('Cache limpo para o teste');

    // Criar middleware de cache para teste
    const middleware = cacheMiddleware('test', 60);

    // Criar objetos mock
    const { req, res, next } = createMockExpressObjects();

    // Primeira chamada - deve ser um cache miss
    console.log('\n1. Primeira chamada ao middleware (cache miss)...');
    await middleware(req, res, next);

    // Verificar se next() foi chamado
    if (next.mock.calls.length === 1) {
      console.log('✅ next() foi chamado como esperado');
    } else {
      console.error('❌ next() não foi chamado');
    }

    // Simular resposta da rota
    const testData = { message: 'Dados de teste', timestamp: Date.now() };
    res.json(testData);

    if (res.jsonCalled) {
      console.log('✅ res.json() foi chamado como esperado');
      console.log('Dados retornados:', res.jsonData);
    } else {
      console.error('❌ res.json() não foi chamado');
    }

    // Verificar se os dados foram armazenados no cache
    const cacheKey = `test:${req.originalUrl}:userId:${req.user.id}:companyId:${req.user.companyId}:role:${req.user.role}`;
    const cachedData = await cacheService.get(cacheKey);

    if (cachedData) {
      console.log('✅ Dados armazenados no cache');
      console.log('Dados em cache:', cachedData);
    } else {
      console.error('❌ Dados não foram armazenados no cache');
    }

    // Fechar conexão
    await cacheService.close();
    console.log('\nConexão fechada');

    console.log('\n=== TESTE CONCLUÍDO ===');
  } catch (error) {
    console.error('Erro durante o teste:', error);
  }
}

// Criar mock para jest.fn()
global.jest = {
  fn: () => {
    const mockFn = () => {};
    mockFn.mock = { calls: [] };
    return mockFn;
  }
};

// Executar o teste
testCacheMiddleware();
