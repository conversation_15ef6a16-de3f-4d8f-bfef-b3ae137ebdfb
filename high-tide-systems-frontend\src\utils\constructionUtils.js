"use client";

// Lista de módulos sem funcionalidade implementada
export const underConstructionModules = [
  'financial',
  'hr'
];

// Lista de submenus sem funcionalidade implementada
export const underConstructionSubmenus = [
  'admin.logs',
  'admin.backup',
  'financial.reports',
  'financial.invoices',
  'financial.payments',
  'financial.expenses',
  'hr.payroll',
  'hr.benefits',
  'hr.attendance',
  'scheduling.appointments-dashboard'
];

// Mensagens personalizadas para cada submenu em construção
export const constructionMessages = {
  'admin.logs': {
    title: 'Logs do Sistema em Construção',
    content: 'O módulo de logs do sistema está em desenvolvimento. Em breve você poderá visualizar todas as atividades realizadas no sistema.',
    icon: 'AlertTriangle'
  },
  'admin.backup': {
    title: 'Backup em Construção',
    content: 'O módulo de backup está em desenvolvimento. Em breve você poderá realizar backups e restaurações dos dados do sistema.',
    icon: 'Database'
  },
  'financial.reports': {
    title: 'Relatórios Financeiros em Construção',
    content: 'O módulo de relatórios financeiros está em desenvolvimento. Em breve você poderá gerar relatórios detalhados sobre as finanças da empresa.',
    icon: 'BarChart'
  },
  'financial.invoices': {
    title: 'Faturas em Construção',
    content: 'O módulo de faturas está em desenvolvimento. Em breve você poderá gerenciar todas as faturas da empresa.',
    icon: 'FileText'
  },
  'financial.payments': {
    title: 'Pagamentos em Construção',
    content: 'O módulo de pagamentos está em desenvolvimento. Em breve você poderá gerenciar todos os pagamentos da empresa.',
    icon: 'CreditCard'
  },
  'financial.expenses': {
    title: 'Despesas em Construção',
    content: 'O módulo de despesas está em desenvolvimento. Em breve você poderá gerenciar todas as despesas da empresa.',
    icon: 'DollarSign'
  },
  'hr.payroll': {
    title: 'Folha de Pagamento em Construção',
    content: 'O módulo de folha de pagamento está em desenvolvimento. Em breve você poderá gerenciar os salários e benefícios dos funcionários.',
    icon: 'Briefcase'
  },
  'hr.benefits': {
    title: 'Benefícios em Construção',
    content: 'O módulo de benefícios está em desenvolvimento. Em breve você poderá gerenciar os benefícios oferecidos aos funcionários.',
    icon: 'Gift'
  },
  'hr.attendance': {
    title: 'Presença em Construção',
    content: 'O módulo de controle de presença está em desenvolvimento. Em breve você poderá gerenciar a frequência dos funcionários.',
    icon: 'Clock'
  },
  'scheduling.appointments-dashboard': {
    title: 'Dashboard de Agendamentos em Construção',
    content: 'O dashboard de agendamentos está em desenvolvimento. Em breve você poderá visualizar estatísticas e análises sobre os agendamentos.',
    icon: 'BarChart'
  }
};

/**
 * Verifica se um módulo ou submódulo está em construção
 * @param {string} moduleId - ID do módulo
 * @param {string} [submenuId] - ID do submenu (opcional)
 * @returns {boolean} - Retorna true se o módulo ou submódulo estiver em construção
 */
export const isUnderConstruction = (moduleId, submenuId = null) => {
  // Verificar se o módulo inteiro está em construção
  if (underConstructionModules.includes(moduleId)) {
    return true;
  }

  // Verificar se o submenu específico está em construção
  if (submenuId) {
    return underConstructionSubmenus.includes(`${moduleId}.${submenuId}`);
  }

  // Caso especial: o módulo admin não deve ser considerado em construção na página inicial
  if (moduleId === 'admin') {
    return false;
  }

  // Verificar se todos os submenus do módulo estão em construção
  return underConstructionSubmenus.filter(key => key.startsWith(`${moduleId}.`)).length > 0;
};

/**
 * Obtém a mensagem de construção para um módulo ou submódulo
 * @param {string} moduleId - ID do módulo
 * @param {string} [submenuId] - ID do submenu (opcional)
 * @returns {Object} - Retorna a mensagem de construção
 */
export const getConstructionMessage = (moduleId, submenuId = null) => {
  // Mensagens específicas para módulos
  const moduleMessages = {
    'financial': {
      title: 'Módulo Financeiro em Construção',
      content: 'O módulo financeiro está em desenvolvimento e estará disponível em breve. Você poderá gerenciar faturas, pagamentos, despesas e relatórios financeiros.',
      icon: 'DollarSign'
    },
    'hr': {
      title: 'Módulo de RH em Construção',
      content: 'O módulo de Recursos Humanos está em desenvolvimento e estará disponível em breve. Você poderá gerenciar funcionários, folha de pagamento, benefícios e muito mais.',
      icon: 'Users'
    }
  };

  // Se for um módulo inteiro em construção
  if (underConstructionModules.includes(moduleId) && !submenuId) {
    return moduleMessages[moduleId] || {
      title: 'Módulo em Construção',
      content: 'Este módulo está em desenvolvimento e estará disponível em breve.',
      icon: 'Construction'
    };
  }

  // Se for um submenu específico
  if (submenuId) {
    const key = `${moduleId}.${submenuId}`;
    return constructionMessages[key] || {
      title: 'Em Construção',
      content: 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',
      icon: 'Construction'
    };
  }

  return {
    title: 'Módulo em Construção',
    content: 'Este módulo está em desenvolvimento e estará disponível em breve.',
    icon: 'Construction'
  };
};
