"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  RefreshCw,
  Edit,
  Trash,
  Power,
  CheckCircle,
  XCircle,
  Plus,
  BookOpen
} from "lucide-react";
import ModuleHeader from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleSelect } from "@/components/ui";
import ModuleTable from "@/components/ui/ModuleTable";
import { programsService } from "../services/programsService";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import ProgramFormModal from "./ProgramFormModal";

const ProgramsPage = () => {
  // Podemos importar useAuth() no futuro para verificação de permissões
  const { toast_success, toast_error } = useToast();

  // Estados
  const [programs, setPrograms] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });
  const [filters, setFilters] = useState({
    search: "",
    type: "",
    active: "true"
  });
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [programToDelete, setProgramToDelete] = useState(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [programToToggle, setProgramToToggle] = useState(null);

  // Colunas da tabela
  const columns = [
    { field: "type", header: "Tipo", sortable: true },
    { field: "name", header: "Nome", sortable: true },
    { field: "protocol", header: "Protocolo", sortable: true },
    { field: "skill", header: "Habilidade", sortable: true },
    { field: "targetsCount", header: "Alvos", sortable: true },
    { field: "status", header: "Status", sortable: true },
    { field: "actions", header: "Ações", sortable: false }
  ];

  // Carregar programas
  const loadPrograms = async () => {
    try {
      setIsLoading(true);
      const data = await programsService.getPrograms({
        page: pagination.page,
        limit: pagination.limit,
        search: filters.search,
        type: filters.type,
        active: filters.active === "all" ? undefined : filters.active === "true"
      });

      setPrograms(data.items);
      setPagination({
        page: data.page,
        limit: data.limit,
        total: data.total,
        pages: data.pages
      });
    } catch (error) {
      console.error("Erro ao carregar programas:", error);
      toast_error("Não foi possível carregar os programas. Tente novamente mais tarde.");
    } finally {
      setIsLoading(false);
    }
  };

  // Carregar programas ao montar o componente ou quando os filtros/paginação mudarem
  useEffect(() => {
    loadPrograms();
  }, [pagination.page, filters]);

  // Manipuladores de eventos
  const handlePageChange = (page) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setPagination((prev) => ({ ...prev, page: 1 }));
    loadPrograms();
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters((prev) => ({ ...prev, [name]: value }));
  };

  const handleRefresh = () => {
    // Limpar filtros e recarregar dados
    setFilters({
      search: "",
      type: "",
      active: "true"
    });
    setPagination(prev => ({ ...prev, page: 1 }));
    loadPrograms();
  };

  const handleAddNew = () => {
    setSelectedProgram(null);
    setIsFormModalOpen(true);
  };

  const handleEdit = (program) => {
    setSelectedProgram(program);
    setIsFormModalOpen(true);
  };

  const handleFormSubmit = async (formData) => {
    try {
      if (selectedProgram) {
        // Atualizar programa existente
        await programsService.updateProgram(selectedProgram.id, formData);
        toast_success("Programa atualizado com sucesso!");
      } else {
        // Criar novo programa
        await programsService.createProgram(formData);
        toast_success("Programa criado com sucesso!");
      }
      setIsFormModalOpen(false);
      loadPrograms();
    } catch (error) {
      console.error("Erro ao salvar programa:", error);
      toast_error("Não foi possível salvar o programa. Tente novamente mais tarde.");
    }
  };

  const handleDeleteClick = (program) => {
    setProgramToDelete(program);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await programsService.deleteProgram(programToDelete.id);
      toast_success("Programa excluído com sucesso!");
      setIsDeleteDialogOpen(false);
      loadPrograms();
    } catch (error) {
      console.error("Erro ao excluir programa:", error);
      toast_error("Não foi possível excluir o programa. Tente novamente mais tarde.");
    }
  };

  const handleToggleStatusClick = (program) => {
    setProgramToToggle(program);
    setIsStatusDialogOpen(true);
  };

  const handleToggleStatusConfirm = async () => {
    try {
      await programsService.toggleProgramStatus(programToToggle.id);
      toast_success(`Programa ${programToToggle.active ? "desativado" : "ativado"} com sucesso!`);
      setIsStatusDialogOpen(false);
      loadPrograms();
    } catch (error) {
      console.error("Erro ao alterar status do programa:", error);
      toast_error("Não foi possível alterar o status do programa. Tente novamente mais tarde.");
    }
  };

  // Componente de filtros para programas
  const ProgramFilters = () => {
    return (
      <form onSubmit={handleSearch} className="flex flex-col gap-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search size={16} className="text-gray-400" />
              </div>
              <ModuleInput
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Buscar por nome, protocolo ou habilidade"
                moduleColor="abaplus"
                className="pl-10 w-full"
              />
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <ModuleSelect
              name="type"
              value={filters.type}
              onChange={handleFilterChange}
              moduleColor="abaplus"
              className="min-w-[180px]"
            >
              <option value="">Todos os tipos</option>
              <option value="PROGRAM_CATALOG">Catálogo de Programas</option>
              <option value="LEARNING_PROGRAM">Programa de Aprendizagem</option>
            </ModuleSelect>

            <ModuleSelect
              name="active"
              value={filters.active}
              onChange={handleFilterChange}
              moduleColor="abaplus"
              className="min-w-[150px]"
            >
              <option value="true">Ativos</option>
              <option value="false">Inativos</option>
              <option value="all">Todos</option>
            </ModuleSelect>

            <button
              type="submit"
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
            >
              <Search size={16} className="sm:hidden" />
              <span className="hidden sm:inline">Buscar</span>
            </button>

            <button
              type="button"
              onClick={handleRefresh}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors flex items-center gap-2"
            >
              <RefreshCw size={16} className="sm:hidden" />
              <span className="hidden sm:inline">Limpar</span>
            </button>
          </div>
        </div>
      </form>
    );
  };

  return (
    <div className="space-y-6">
      {/* Cabeçalho com filtros integrados */}
      <ModuleHeader
        title="Filtros de Programas"
        moduleColor="abaplus"
        icon={<BookOpen size={20} />}
        filters={<ProgramFilters />}
        onAddNew={handleAddNew}
        addNewLabel="Novo Programa"
      />

      {/* Tabela de Programas */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden">
        <ModuleTable
          columns={columns}
          data={programs}
          isLoading={isLoading}
          currentPage={pagination.page}
          totalPages={pagination.pages}
          totalItems={pagination.total}
          onPageChange={handlePageChange}
          emptyMessage="Nenhum programa encontrado"
          moduleColor="abaplus"
          renderRow={(program, _index, moduleColors, visibleColumns) => (
            <tr key={program.id} className={moduleColors.hoverBg}>
              {visibleColumns.includes('type') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {programsService.getProgramTypeLabel(program.type)}
                  </div>
                </td>
              )}
              {visibleColumns.includes('name') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100 font-medium">
                    {program.name}
                  </div>
                </td>
              )}
              {visibleColumns.includes('protocol') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {program.protocol || "-"}
                  </div>
                </td>
              )}
              {visibleColumns.includes('skill') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {program.skill || "-"}
                  </div>
                </td>
              )}
              {visibleColumns.includes('targetsCount') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">
                    {program._count?.targets || 0}
                  </div>
                </td>
              )}
              {visibleColumns.includes('status') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    {program.active ? (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100">
                        <CheckCircle size={14} className="mr-1" /> Ativo
                      </span>
                    ) : (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100">
                        <XCircle size={14} className="mr-1" /> Inativo
                      </span>
                    )}
                  </div>
                </td>
              )}
              {visibleColumns.includes('actions') && (
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <button
                    onClick={() => handleEdit(program)}
                    className="text-module-abaplus-primary hover:text-module-abaplus-primary-dark mr-3"
                    title="Editar"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleToggleStatusClick(program)}
                    className={`${
                      program.active
                        ? "text-amber-500 hover:text-amber-600"
                        : "text-green-500 hover:text-green-600"
                    } mr-3`}
                    title={program.active ? "Desativar" : "Ativar"}
                  >
                    <Power size={18} />
                  </button>
                  <button
                    onClick={() => handleDeleteClick(program)}
                    className="text-red-500 hover:text-red-600"
                    title="Excluir"
                  >
                    <Trash size={18} />
                  </button>
                </td>
              )}
            </tr>
          )}
        />
      </div>

      {/* Modal de Formulário */}
      {isFormModalOpen && (
        <ProgramFormModal
          isOpen={isFormModalOpen}
          onClose={() => setIsFormModalOpen(false)}
          onSave={handleFormSubmit}
          program={selectedProgram}
        />
      )}

      {/* Diálogo de Confirmação de Exclusão */}
      <ConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={handleDeleteConfirm}
        title="Excluir Programa"
        message={`Tem certeza que deseja excluir o programa "${programToDelete?.name}"? Esta ação não pode ser desfeita.`}
        confirmButtonText="Excluir"
        confirmButtonVariant="danger"
      />

      {/* Diálogo de Confirmação de Alteração de Status */}
      <ConfirmationDialog
        isOpen={isStatusDialogOpen}
        onClose={() => setIsStatusDialogOpen(false)}
        onConfirm={handleToggleStatusConfirm}
        title={programToToggle?.active ? "Desativar Programa" : "Ativar Programa"}
        message={`Tem certeza que deseja ${
          programToToggle?.active ? "desativar" : "ativar"
        } o programa "${programToToggle?.name}"?`}
        confirmButtonText={programToToggle?.active ? "Desativar" : "Ativar"}
        confirmButtonVariant={programToToggle?.active ? "warning" : "success"}
      />
    </div>
  );
};

export default ProgramsPage;
