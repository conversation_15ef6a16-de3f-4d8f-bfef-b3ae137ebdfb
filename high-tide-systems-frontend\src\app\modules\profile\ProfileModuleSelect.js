'use client';

import React, { forwardRef } from 'react';
import ProfileSelect from './ProfileSelect';

/**
 * Componente de select específico para a página de perfil
 * Usa o ProfileSelect para corrigir o problema de posicionamento dos dropdowns
 */
const ProfileModuleSelect = forwardRef(({
  moduleColor = 'default',
  placeholder,
  value,
  onChange,
  disabled = false,
  required = false,
  name,
  id,
  error = false,
  errorMessage,
  children,
  className = '',
  ...rest
}, ref) => {
  // Extrair as opções dos children (elementos option)
  const extractOptions = () => {
    const options = [];
    React.Children.forEach(children, child => {
      if (child && child.type === 'option') {
        options.push({
          value: child.props.value,
          label: child.props.children,
          disabled: child.props.disabled
        });
      }
    });
    return options;
  };

  const options = extractOptions();

  return (
    <div className="profile-select-wrapper">
      <ProfileSelect
        ref={ref}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        name={name}
        id={id}
        moduleColor={moduleColor}
        error={error}
        placeholder={placeholder}
        options={options}
        className={className}
        {...rest}
      />

      {error && errorMessage && (
        <p className="mt-1 text-xs text-red-600 dark:text-red-400">
          {errorMessage}
        </p>
      )}
    </div>
  );
});

ProfileModuleSelect.displayName = 'ProfileModuleSelect';

export default ProfileModuleSelect;
