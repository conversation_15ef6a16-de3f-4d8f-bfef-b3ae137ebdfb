'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Upload, Trash, Loader2 } from 'lucide-react';
import { userService } from '@/app/modules/admin/services/userService';

const UserProfileImageUpload = ({
  userId,
  initialImageUrl,
  onImageUploaded,
  deferUpload = false,
  uploadRef = null,
  size = 'medium',
  disabled = false
}) => {
  const [imageUrl, setImageUrl] = useState(initialImageUrl || null);
  const [previewUrl, setPreviewUrl] = useState(initialImageUrl || null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);

  // Tamanhos disponíveis
  const sizes = {
    small: { container: 'h-16 w-16', icon: 'h-4 w-4' },
    medium: { container: 'h-24 w-24', icon: 'h-6 w-6' },
    large: { container: 'h-32 w-32', icon: 'h-8 w-8' }
  };

  // Atualizar a URL da imagem quando a prop initialImageUrl mudar
  useEffect(() => {
    if (initialImageUrl) {
      setImageUrl(initialImageUrl);
      setPreviewUrl(initialImageUrl);
    }
  }, [initialImageUrl]);

  // Expor o método de upload para o componente pai
  useEffect(() => {
    if (uploadRef) {
      uploadRef.current = {
        uploadSelectedImage: async () => {
          console.log('Método uploadSelectedImage chamado');
          console.log('Arquivo selecionado:', selectedFile ? selectedFile.name : 'Nenhum');
          console.log('ID do usuário:', userId);

          if (selectedFile && userId) {
            console.log('Iniciando upload do arquivo selecionado');
            const result = await uploadImage(selectedFile);
            console.log('Resultado do upload:', result);
            return result;
          }
          console.log('Nenhum arquivo para upload ou ID do usuário ausente');
          return null;
        },
        hasSelectedFile: () => {
          const hasFile = !!selectedFile;
          console.log('Verificando se há arquivo selecionado:', hasFile);
          return hasFile;
        }
      };
    }
  }, [selectedFile, userId, uploadRef]);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Verificar tipo de arquivo (apenas imagens)
    if (!file.type.startsWith('image/')) {
      setError('Por favor, selecione uma imagem válida');
      return;
    }

    // Limitar tamanho (2MB)
    if (file.size > 2 * 1024 * 1024) {
      setError('A imagem deve ter no máximo 2MB');
      return;
    }

    setError(null);
    setSelectedFile(file);
    console.log('Arquivo selecionado:', file.name, file.type, file.size);

    // Criar uma URL para pré-visualização
    const newPreviewUrl = URL.createObjectURL(file);
    setPreviewUrl(newPreviewUrl);

    // Se não estiver adiando o upload, fazer o upload imediatamente
    if (!deferUpload && userId) {
      uploadImage(file);
    } else {
      // Notificar o componente pai sobre a mudança de arquivo
      if (onImageUploaded) {
        onImageUploaded(null, file);
      }
    }
  };

  // Método para fazer o upload da imagem
  const uploadImage = async (file) => {
    if (!file || !userId) {
      console.error('Upload cancelado: arquivo ou userId ausente', { file: !!file, userId });
      return null;
    }

    setIsUploading(true);
    console.log('Iniciando upload de imagem para usuário ID:', userId);
    console.log('Arquivo a ser enviado:', file.name, file.type, file.size);

    try {
      console.log('Chamando serviço de upload de imagem');
      const response = await userService.uploadProfileImage(userId, file);
      console.log('Upload de imagem concluído com sucesso');
      console.log('Resposta completa:', JSON.stringify(response));

      // Atualizar URL da imagem com timestamp para evitar cache
      const timestamp = new Date().getTime();

      // Usar a URL completa retornada pelo servidor
      const newImageUrl = response.fullImageUrl ? `${response.fullImageUrl}?t=${timestamp}` : null;
      console.log('Nova URL da imagem:', newImageUrl);

      setImageUrl(newImageUrl);
      setSelectedFile(null); // Limpar o arquivo selecionado após o upload

      if (onImageUploaded) {
        onImageUploaded(newImageUrl);
      }

      return response;
    } catch (error) {
      console.error('Erro ao fazer upload da imagem:', error);
      setError(error.response?.data?.message || 'Erro ao fazer upload da imagem');
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const removeImage = () => {
    setImageUrl(null);
    setPreviewUrl(null);
    setSelectedFile(null);
    
    if (onImageUploaded) {
      onImageUploaded(null);
    }
  };

  const triggerFileInput = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="flex flex-col items-center">
      <div className="relative group">
        {previewUrl ? (
          <div className={`relative ${sizes[size].container} overflow-hidden rounded-full border border-neutral-300 dark:border-gray-600`}>
            <img
              src={previewUrl}
              alt="Imagem de perfil"
              className="h-full w-full object-cover"
            />
            {!disabled && (
              <button
                type="button"
                onClick={removeImage}
                className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                title="Remover imagem"
              >
                <Trash className="h-3 w-3" />
              </button>
            )}
          </div>
        ) : (
          <div
            onClick={triggerFileInput}
            className={`${sizes[size].container} flex items-center justify-center rounded-full border-2 border-dashed border-neutral-300 dark:border-gray-600 ${!disabled ? 'cursor-pointer hover:border-primary-500 dark:hover:border-primary-400' : 'cursor-not-allowed opacity-70'}`}
          >
            {isUploading ? (
              <Loader2 className={`${sizes[size].icon} text-neutral-400 dark:text-gray-500 animate-spin`} />
            ) : (
              <Upload className={`${sizes[size].icon} text-neutral-400 dark:text-gray-500`} />
            )}
          </div>
        )}
      </div>

      {!disabled && (
        <div className="mt-2 flex flex-col items-center">
          <button
            type="button"
            onClick={triggerFileInput}
            disabled={isUploading}
            className="text-xs text-primary-600 dark:text-primary-400 hover:underline focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {previewUrl ? 'Alterar foto' : 'Adicionar foto'}
          </button>
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
            disabled={isUploading || disabled}
          />
        </div>
      )}

      {error && (
        <p className="mt-1 text-xs text-red-600 dark:text-red-400">{error}</p>
      )}
    </div>
  );
};

export default UserProfileImageUpload;
