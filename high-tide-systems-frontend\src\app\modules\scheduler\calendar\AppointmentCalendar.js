"use client";

import React, { useState, useEffect } from "react";
import { Calendar, Filter } from "lucide-react";
import { CalendarFilters } from "@/components/calendar/CalendarFilters";
import { ClientCalendarFilters } from "@/components/calendar/ClientCalendarFilters";
import { AppointmentModal } from "@/components/calendar/AppointmentModal";
import { usePermissions } from "@/hooks/usePermissions";
import { useAuth } from "@/contexts/AuthContext";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import ModuleHeader from "@/components/ui/ModuleHeader";
import html2canvas from "html2canvas";
import { saveAs } from "file-saver";
import { jsPDF } from "jspdf";

// Componentes refatorados
import CalendarWrapper from "./components/CalendarWrapper";
import AppointmentEventContent from "./components/AppointmentEventContent";
import CalendarExportButton from "./components/CalendarExportButton";
import MultipleEventsModal from "./components/MultipleEventsModal";

// Hooks personalizados
import useAppointmentCalendar from "./hooks/useAppointmentCalendar";
import useAppointmentAvailability from "./hooks/useAppointmentAvailability";
import useCalendarBusinessHours from "./hooks/useCalendarBusinessHours";

// Componentes do tutorial
import TutorialManager from "@/components/tutorial/TutorialManager";

const calendarTutorialSteps = [
  {
    title: "Bem-vindo ao Calendário",
    content: "Este é o calendário de agendamentos. Aqui você pode visualizar, criar e gerenciar todos os seus compromissos.",
    selector: ".fc-view-harness", // Seletor para o componente do calendário
    position: "top"
  },
  {
    title: "Filtros de Calendário",
    content: "Use estes filtros para encontrar agendamentos específicos. Você pode filtrar por profissionais, pacientes, tipos de serviço e locais.",
    selector: ".filter-section", // Adicione esta classe ao componente CalendarFilters
    position: "bottom"
  },
  {
    title: "Criar Agendamento",
    content: "Para criar um novo agendamento, basta clicar em qualquer espaço vazio no calendário e preencher as informações necessárias.",
    selector: ".fc-daygrid-day:not(.fc-day-other):nth-child(3)", // Seleciona um dia do calendário para exemplo
    position: "bottom"
  },
  {
    title: "Visualizações do Calendário",
    content: "Alterne entre diferentes visualizações: mês, semana ou dia para ver seus agendamentos com mais detalhes.",
    selector: ".fc-toolbar-chunk:last-child", // Seletor para os botões de visualização
    position: "bottom"
  },
  {
    title: "Exportar Agendamentos",
    content: "Você pode exportar seus agendamentos em diferentes formatos usando este botão.",
    selector: ".export-button", // Adicione esta classe ao componente de exportação
    position: "right"
  }
];

const AppointmentCalendar = () => {
  // Estado para os filtros do calendário
  const [filters, setFilters] = useState({
    providers: [],
    persons: [],
    serviceTypes: [],
    locations: [],
  });

  // Log de debug para os filtros
  useEffect(() => {
    console.log("[AppointmentCalendar] Filtros atualizados:", filters);
  }, [filters]);

  // Estado para exportação
  const [isExporting, setIsExporting] = useState(false);

  // Estado para o modal de múltiplos eventos
  const [isMultipleEventsModalOpen, setIsMultipleEventsModalOpen] = useState(false);
  const [multipleEvents, setMultipleEvents] = useState([]);
  const [multipleEventsDate, setMultipleEventsDate] = useState(null);

  // Detecta modo escuro
  const [isDarkMode, setIsDarkMode] = useState(false);

  // Hooks de permissões
  const { can, isClient } = usePermissions();
  const { user } = useAuth();

  // Log para debug
  console.log("[DEBUG] User object:", user);
  console.log("[DEBUG] isClient result:", isClient());

  const permissions = {
    canViewCalendar: can('scheduling.calendar.view') || can('scheduler.calendar.view'),
    canCreateAppointment: can('scheduling.calendar.create'),
    canEditAppointment: can('scheduling.calendar.edit'),
    canDeleteAppointment: can('scheduling.calendar.delete')
  };

  // Detectar dark mode
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const darkModeActive =
        document.documentElement.classList.contains('dark') ||
        window.matchMedia('(prefers-color-scheme: dark)').matches;

      setIsDarkMode(darkModeActive);

      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'class') {
            setIsDarkMode(document.documentElement.classList.contains('dark'));
          }
        });
      });

      observer.observe(document.documentElement, { attributes: true });
      return () => observer.disconnect();
    }
  }, []);

  // Hook principal do calendário
  const {
    calendarRef,
    isModalOpen,
    setIsModalOpen,
    selectedDate,
    selectedAppointment,
    setSelectedAppointment,
    appointments,
    isLoading,
    currentView,
    handleSlotClassNames,
    handleDateSelect: baseHandleDateSelect,
    handleEventClick: baseHandleEventClick,
    handleDatesSet,
    handleSearch,
    loadAppointments
  } = useAppointmentCalendar(filters, isDarkMode, permissions);

  // Hook de disponibilidade de horários
  const {
    providersAvailability,
    errorMessage,
    setErrorMessage,
    checkSingleProviderAvailability,
    checkProvidersAvailability,
    validateAppointment
  } = useAppointmentAvailability(filters, currentView);

  // Hook de horários comerciais
  const businessHours = useCalendarBusinessHours(filters, providersAvailability);

  // Funções que combinam o core com a verificação de disponibilidade
  const handleDateSelect = (selectInfo) => {
    baseHandleDateSelect(selectInfo, checkProvidersAvailability, setErrorMessage);
  };

  const handleEventClick = (clickInfo) => {
    baseHandleEventClick(clickInfo, checkSingleProviderAvailability, setErrorMessage);
  };

  // Função para abrir o modal de múltiplos eventos já está implementada inline no CalendarWrapper

  // Renderização de conteúdo de eventos
  const renderEventContent = (eventInfo) => {
    return <AppointmentEventContent eventInfo={eventInfo} isDarkMode={isDarkMode} />;
  };

  // Função para exportar agendamentos
  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Para formatos de imagem e PDF, capturamos a visualização atual do calendário
      if (format === 'image' || format === 'pdf') {
        // Verificar se a referência do calendário existe
        if (!calendarRef.current) {
          console.error("Referência do calendário não encontrada");
          return;
        }

        // Obter o elemento do calendário
        const calendarElement = calendarRef.current.elRef.current;

        // Salvar o estado original dos elementos que serão modificados
        const elementsToHide = [];

        // 1. Esconder o dropdown de exportação
        const existingDropdown = document.getElementById('calendar-export-dropdown-container');
        if (existingDropdown) {
          elementsToHide.push({ element: existingDropdown, display: existingDropdown.style.display });
          existingDropdown.style.display = 'none';
        }

        const looseDropdown = document.getElementById('calendar-export-dropdown');
        if (looseDropdown) {
          elementsToHide.push({ element: looseDropdown, display: looseDropdown.style.display });
          looseDropdown.style.display = 'none';
        }

        // 2. Esconder botões de exportação no header do calendário
        const exportButtons = calendarElement.querySelectorAll('.fc-exportButton-button');
        exportButtons.forEach(button => {
          elementsToHide.push({ element: button, display: button.style.display });
          button.style.display = 'none';
        });

        // 3. Aplicar estilos temporários para garantir que o texto dos eventos seja renderizado corretamente
        const eventElements = calendarElement.querySelectorAll('.fc-event');
        const eventStyles = [];

        eventElements.forEach(event => {
          // Salvar estilos originais
          const originalStyles = {
            overflow: event.style.overflow,
            textOverflow: event.style.textOverflow,
            whiteSpace: event.style.whiteSpace
          };

          eventStyles.push({ element: event, styles: originalStyles });

          // Aplicar estilos para garantir que o texto seja renderizado corretamente
          event.style.overflow = 'visible';
          event.style.textOverflow = 'clip';
          event.style.whiteSpace = 'normal';

          // Processar todos os elementos de texto dentro do evento
          const textElements = event.querySelectorAll('div');
          textElements.forEach(textEl => {
            // Salvar estilos originais
            const originalTextStyles = {
              overflow: textEl.style.overflow,
              textOverflow: textEl.style.textOverflow,
              whiteSpace: textEl.style.whiteSpace,
              fontFamily: textEl.style.fontFamily,
              fontSize: textEl.style.fontSize,
              lineHeight: textEl.style.lineHeight
            };

            // Adicionar ao array de estilos para restauração
            eventStyles.push({
              element: textEl,
              styles: originalTextStyles,
              isTextElement: true
            });

            // Garantir que o texto seja renderizado corretamente
            textEl.style.overflow = 'visible';
            textEl.style.textOverflow = 'clip';
            textEl.style.whiteSpace = 'normal';
            textEl.style.fontFamily = 'Arial, sans-serif';

            // Se for um texto muito pequeno, aumentar o tamanho para a exportação
            if (textEl.classList.contains('text-xs') ||
                textEl.classList.contains('text-[10px]') ||
                textEl.classList.contains('text-[9px]')) {
              textEl.style.fontSize = '12px';
              textEl.style.lineHeight = '1.2';
            }
          });
        });

        // 4. Aplicar estilos para garantir que os botões de visualização sejam renderizados corretamente
        const viewButtons = calendarElement.querySelectorAll('.fc-button');
        const buttonStyles = [];

        viewButtons.forEach(button => {
          // Salvar estilos originais
          const originalStyles = {
            fontFamily: button.style.fontFamily,
            fontSize: button.style.fontSize,
            fontWeight: button.style.fontWeight,
            textTransform: button.style.textTransform,
            letterSpacing: button.style.letterSpacing
          };

          buttonStyles.push({ element: button, styles: originalStyles });

          // Aplicar estilos para garantir que o texto seja renderizado corretamente
          button.style.fontFamily = 'Arial, sans-serif';
          button.style.fontSize = '14px';
          button.style.fontWeight = 'bold';
          button.style.textTransform = 'none';
          button.style.letterSpacing = 'normal';

          // Garantir que o texto dentro do botão seja renderizado corretamente
          // Em vez de modificar o innerHTML, vamos apenas aplicar estilos
          // Isso evita problemas de duplicação de texto após a restauração
          const buttonSpans = button.querySelectorAll('span');
          if (buttonSpans.length > 0) {
            // Se o botão já tem spans internos, aplicar estilos a eles
            buttonSpans.forEach(span => {
              // Salvar estilos originais
              const originalSpanStyles = {
                fontFamily: span.style.fontFamily,
                fontSize: span.style.fontSize,
                fontWeight: span.style.fontWeight,
                textTransform: span.style.textTransform,
                letterSpacing: span.style.letterSpacing
              };

              // Adicionar ao array de estilos para restauração
              buttonStyles.push({
                element: span,
                styles: originalSpanStyles,
                isSpan: true
              });

              // Aplicar novos estilos
              span.style.fontFamily = 'Arial, sans-serif';
              span.style.fontSize = '14px';
              span.style.fontWeight = 'bold';
              span.style.textTransform = 'none';
              span.style.letterSpacing = 'normal';
            });
          }
        });

        // Configurações para melhorar a qualidade da imagem
        const options = {
          scale: 2, // Aumenta a escala para melhor qualidade
          useCORS: true, // Permite carregar imagens de outros domínios
          allowTaint: true, // Permite que a tela seja "manchada" com imagens de outros domínios
          backgroundColor: isDarkMode ? '#1f2937' : '#ffffff', // Cor de fundo baseada no tema
          logging: false, // Desativa logs para melhor performance
          letterRendering: true, // Melhora a renderização de texto
          foreignObjectRendering: false, // Desativar para evitar problemas com texto
          removeContainer: false, // Não remover o container temporário
          ignoreElements: (element) => {
            // Ignorar elementos de exportação
            return element.classList && (
              element.classList.contains('fc-exportButton-button') ||
              element.id === 'calendar-export-dropdown' ||
              element.id === 'calendar-export-dropdown-container'
            );
          },
          onclone: (clonedDoc) => {
            // Função executada após a clonagem do documento, antes da renderização
            // Podemos fazer ajustes adicionais aqui

            // Corrigir o problema específico com o texto "Amandaaaaaaaaaaa"
            const eventTitles = clonedDoc.querySelectorAll('.fc-event-title');
            eventTitles.forEach(title => {

              // Garantir que o texto não seja cortado
              title.style.overflow = 'visible';
              title.style.textOverflow = 'clip';
              title.style.whiteSpace = 'normal';
              title.style.fontFamily = 'Arial, sans-serif';
            });
          }
        };

        // Converter o elemento para canvas
        const canvas = await html2canvas(calendarElement, options);

        // Restaurar o estado original dos elementos
        elementsToHide.forEach(item => {
          item.element.style.display = item.display;
        });

        // Restaurar estilos originais dos eventos
        eventStyles.forEach(item => {
          Object.assign(item.element.style, item.styles);
        });

        // Restaurar estilos originais dos botões e spans
        buttonStyles.forEach(item => {
          Object.assign(item.element.style, item.styles);
        });

        if (format === 'image') {
          // Exportar como imagem PNG
          canvas.toBlob((blob) => {
            // Salvar o blob como arquivo
            saveAs(blob, `calendario-${new Date().toISOString().split('T')[0]}.png`);
          }, 'image/png');
        } else if (format === 'pdf') {
          // Exportar como PDF
          const imgData = canvas.toDataURL('image/png');

          // Obter dimensões do canvas
          const canvasWidth = canvas.width;
          const canvasHeight = canvas.height;

          // Calcular orientação e tamanho do PDF
          const orientation = canvasWidth > canvasHeight ? 'landscape' : 'portrait';

          // Criar novo documento PDF
          const pdf = new jsPDF({
            orientation: orientation,
            unit: 'px',
            format: [canvasWidth, canvasHeight],
            compress: true
          });

          // Adicionar título
          const title = "Calendário de Agendamentos";
          pdf.setFontSize(16);
          pdf.text(title, 20, 20);

          // Adicionar data de exportação
          const today = new Date().toLocaleDateString('pt-BR');
          pdf.setFontSize(10);
          pdf.text(`Exportado em: ${today}`, 20, 35);

          // Adicionar a imagem do calendário
          // Ajustar posição para deixar espaço para o título
          pdf.addImage(imgData, 'PNG', 0, 45, canvasWidth, canvasHeight);

          // Salvar o PDF
          pdf.save(`calendario-${new Date().toISOString().split('T')[0]}.pdf`);
        }
      } else {
        // Exportar usando os mesmos filtros do calendário (apenas Excel)
        await appointmentService.exportAppointments({
          providers: filters.providers || [],
          persons: filters.persons || [],
          serviceTypes: filters.serviceTypes || [],
          locations: filters.locations || []
        }, format);
      }
    } catch (error) {
      console.error("Erro ao exportar agendamentos:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Verificar se o usuário tem permissão para visualizar o calendário
  if (!permissions.canViewCalendar) {
    return (
      <div className="p-6 bg-neutral-50 dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 text-center">
        <p className="text-neutral-600 dark:text-gray-300">
          Você não tem permissão para visualizar o calendário de agendamentos.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Calendar size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Calendário de Agendamentos
        </h1>
      </div>

      {/* Cabeçalho da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description={isClient()
          ? "Visualize seus agendamentos e de pessoas relacionadas. Utilize os filtros abaixo para encontrar agendamentos específicos."
          : "Visualize e gerencie todos os agendamentos do sistema. Utilize os filtros abaixo para encontrar agendamentos específicos."}
        moduleColor="scheduler"
        tutorialSteps={calendarTutorialSteps}
        tutorialName="calendar-overview"
        filters={
          <div className="filter-section">
            {isClient() ? (
              <ClientCalendarFilters
                filters={filters}
                onFiltersChange={setFilters}
                onSearch={handleSearch}
              />
            ) : (
              <CalendarFilters
                filters={filters}
                onFiltersChange={setFilters}
                onSearch={handleSearch}
              />
            )}
          </div>
        }
        customButtons={
          <div className="text-white">
            <CalendarExportButton
              onExport={handleExport}
              isExporting={isExporting}
              disabled={isLoading || appointments.length === 0}
              inHeader={true}
            />
          </div>
        }
      />



      {/* Mensagem de erro */}
      {errorMessage && (
        <div className="bg-error-50 dark:bg-red-900/20 border border-error-200 dark:border-red-800/50 text-error-800 dark:text-red-300 px-4 py-3 rounded relative">
          <span className="block sm:inline whitespace-pre-line">
            {errorMessage}
          </span>
          <button
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setErrorMessage(null)}
          >
            <span className="text-error-500 dark:text-red-400 hover:text-error-800 dark:hover:text-red-300">×</span>
          </button>
        </div>
      )}

      {/* Calendário */}
      <CalendarWrapper
        calendarRef={calendarRef}
        isDarkMode={isDarkMode}
        isLoading={isLoading}
        appointments={appointments}
        businessHours={businessHours}
        handleDateSelect={handleDateSelect}
        handleEventClick={handleEventClick}
        handleDatesSet={handleDatesSet}
        renderEventContent={renderEventContent}
        handleSlotClassNames={handleSlotClassNames}
        canCreateAppointment={permissions.canCreateAppointment}
        onShowMultipleEvents={(date, events) => {
          setMultipleEvents(events);
          setMultipleEventsDate(date);
          setIsMultipleEventsModalOpen(true);
        }}
        onExport={handleExport}
      />

      {/* Modal de agendamento */}
      <AppointmentModal
        isOpen={isModalOpen}
        onClose={() => {
          // Fechar o modal
          setIsModalOpen(false);
        }}
        selectedDate={selectedDate}
        selectedAppointment={selectedAppointment}
        onAppointmentChange={loadAppointments}
        checkAvailability={validateAppointment}
        canCreate={permissions.canCreateAppointment}
        canEdit={permissions.canEditAppointment}
        canDelete={permissions.canDeleteAppointment}
      />

      {/* Modal de múltiplos eventos */}
      <MultipleEventsModal
        isOpen={isMultipleEventsModalOpen}
        onClose={() => setIsMultipleEventsModalOpen(false)}
        events={multipleEvents}
        date={multipleEventsDate}
        isDarkMode={isDarkMode}
        onEditAppointment={(event) => {
          // Usar o mesmo fluxo de edição que o handleEventClick
          setSelectedAppointment({
            id: event.id,
            title: event.title,
            description: event.extendedProps.description || '',
            startDate: new Date(event.start),
            endDate: new Date(event.end),
            providerId: event.extendedProps.providerId,
            personId: event.extendedProps.personId,
            locationId: event.extendedProps.locationId,
            serviceTypeId: event.extendedProps.serviceTypeId,
            insuranceId: event.extendedProps.insuranceId,
            status: event.extendedProps.status || 'PENDING',
            extendedProps: event.extendedProps
          });
          setIsModalOpen(true);
        }}
        onAppointmentChange={loadAppointments}
      />

      {/* Gerenciador de tutorial - IMPORTANTE: Inclua em todas as páginas que usam tutoriais */}
      <TutorialManager />
    </div>
  );
};

export default AppointmentCalendar;