const { validationResult } = require('express-validator');
const { body } = require('express-validator');
const prisma = require('../../utils/prisma');
const RecurrenceHelper = require('../../utils/recurrenceHelpers');

// Validações
const createRecurrenceValidation = [
  body('title').notEmpty().withMessage('Título é obrigatório'),
  body('personId').notEmpty().withMessage('Pessoa é obrigatória'),
  body('userId').notEmpty().withMessage('Profissional é obrigatório'),
  body('locationId').notEmpty().withMessage('Local é obrigatório'),
  body('serviceTypeId').notEmpty().withMessage('Tipo de serviço é obrigatório'),
  body('recurrenceType').isIn(['OCCURRENCES', 'END_DATE']).withMessage('Tipo de recorrência inválido'),
  body('recurrenceValue').notEmpty().withMessage('Valor de recorrência é obrigatório'),
  body('patterns').isArray().withMessage('Padrões são obrigatórios'),
  body('patterns.*.dayOfWeek').isInt({ min: 0, max: 6 }).withMessage('Dia da semana inválido'),
  body('patterns.*.startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Hora início inválida'),
  body('patterns.*.endTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Hora fim inválida')
];

class RecurrenceController {
  /**
   * Cria uma nova recorrência de agendamentos
   */
  static async create(req, res) {
    console.log("=== INICIANDO CRIAÇÃO DE RECORRÊNCIA ===");
    console.log(`Requisição recebida às ${new Date().toISOString()}`);
    console.log("Headers:", JSON.stringify(req.headers));

    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        console.log("Erros de validação:", JSON.stringify(errors.array()));
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        title,
        description,
        personId,
        userId,
        locationId,
        serviceTypeId,
        insuranceId,
        recurrenceType,
        recurrenceValue,
        patterns: originalPatterns,
        branchId,
        sequentialAppointments = 1 // NOVO: Número de agendamentos sequenciais (padrão: 1)
      } = req.body;

      // Adicionar userId aos padrões para uso posterior
      const patterns = originalPatterns.map(pattern => ({
        ...pattern,
        userId // Adicionar userId aos padrões
      }));

      console.log(`Padrões processados com userId:`, JSON.stringify(patterns));

      console.log("Dados da requisição:");
      console.log(`- Título: ${title}`);
      console.log(`- Tipo de recorrência: ${recurrenceType}`);
      console.log(`- Valor de recorrência: ${recurrenceValue}`);
      console.log(`- Usuário: ${userId}`);
      console.log(`- Pessoa: ${personId}`);
      console.log(`- Local: ${locationId}`);
      console.log(`- Branch: ${branchId || 'Não especificado'}`);
      console.log(`- Agendamentos sequenciais: ${sequentialAppointments}x`);

      // Extrair a data inicial para o agendamento original
      const originalStartDate = new Date(req.body.startDate);
      console.log(`- Data original (UTC): ${originalStartDate.toISOString()}`);
      console.log(`- Data original (Local): ${originalStartDate.toString()}`);
      console.log(`- Dia da semana original: ${originalStartDate.getDay()} (0=Domingo, 1=Segunda, ...)`);

      // Validação para garantir que o número de ocorrências seja pelo menos 1
      if (recurrenceType === 'OCCURRENCES' && (!recurrenceValue || parseInt(recurrenceValue) < 1)) {
        console.log("ERRO: Número de ocorrências deve ser pelo menos 1");
        return res.status(400).json({
          message: 'Número de ocorrências deve ser pelo menos 1'
        });
      }

      // Validação para garantir que temos pelo menos um padrão
      if (!patterns || patterns.length === 0) {
        console.log("ERRO: Nenhum padrão de recorrência fornecido");
        return res.status(400).json({
          message: 'Pelo menos um padrão de recorrência é obrigatório'
        });
      }

      // Validação para garantir que o número de agendamentos sequenciais seja entre 1 e 5
      if (sequentialAppointments < 1 || sequentialAppointments > 5) {
        console.log(`ERRO: Número de agendamentos sequenciais inválido: ${sequentialAppointments}`);
        return res.status(400).json({
          message: 'Número de agendamentos sequenciais deve ser entre 1 e 5'
        });
      }

      console.log("Padrões de recorrência:");
      for (const [index, pattern] of patterns.entries()) {
        console.log(`Padrão ${index + 1}:`);
        console.log(`- Dia da semana: ${pattern.dayOfWeek} (0=Domingo, 1=Segunda, ...)`);
        console.log(`- Horário: ${pattern.startTime} - ${pattern.endTime}`);
      }

      // Check if person exists
      const person = await prisma.person.findUnique({
        where: { id: personId }
      });

      if (!person) {
        console.log(`ERRO: Pessoa com ID ${personId} não encontrada`);
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      console.log(`Pessoa encontrada: ${person.fullName}`);

      // Gera todas as datas da recorrência
      console.log("Chamando RecurrenceHelper para gerar datas de recorrência...");
      const dates = await RecurrenceHelper.generateRecurrenceDates(
        patterns,
        originalStartDate,
        recurrenceType,
        recurrenceValue
      );

      // Validação para garantir que temos pelo menos uma data
      if (!dates || dates.length === 0) {
        console.log("ERRO: Nenhuma data de recorrência gerada");
        return res.status(400).json({
          message: 'Não foi possível gerar datas para a recorrência. Verifique os parâmetros.'
        });
      }

      console.log(`Geradas ${dates.length} datas de recorrência:`);
      for (const [index, date] of dates.entries()) {
        console.log(`Data ${index + 1}: ${date.startDate.toISOString()} - ${date.endDate.toISOString()}`);
      }

      // Verifica disponibilidade para todas as datas recorrentes (incluindo agendamentos sequenciais)
      console.log("Verificando disponibilidade para todas as datas...");

      // NOVO: Verificar disponibilidade para agendamentos originais e sequenciais
      // Para cada data recorrente, também verifique os horários sequenciais
      const allDatesToCheck = [];
      for (const date of dates) {
        // Adicionar a data original da recorrência
        allDatesToCheck.push(date);

        // Se há agendamentos sequenciais, adicionar esses horários também
        if (sequentialAppointments > 1) {
          const appointmentDuration = date.endDate.getTime() - date.startDate.getTime();
          let currentEndDate = new Date(date.endDate);

          for (let i = 1; i < sequentialAppointments; i++) {
            // Hora de início do próximo agendamento: 1h após o término do anterior
            const nextStartDate = new Date(currentEndDate.getTime() + 3600000);
            const nextEndDate = new Date(nextStartDate.getTime() + appointmentDuration);

            allDatesToCheck.push({
              startDate: nextStartDate,
              endDate: nextEndDate,
              dayOfWeek: date.dayOfWeek,
              isSequential: true, // Marcar como agendamento sequencial
              sequentialIndex: i // Índice do agendamento sequencial
            });

            currentEndDate = nextEndDate;
          }
        }
      }

      console.log(`Verificando disponibilidade para ${allDatesToCheck.length} datas no total (incluindo sequenciais)`);

      // Passar excludeId se estiver editando uma recorrência existente
      const excludeId = req.body.excludeId || null;
      if (excludeId) {
        console.log(`Excluindo agendamento ${excludeId} da verificação de disponibilidade`);
      }

      const availability = await RecurrenceHelper.checkRecurrenceAvailability(userId, personId, allDatesToCheck, excludeId);
      if (!availability.available) {
        console.log(`ERRO: Conflito de horário para data ${availability.conflictDate}`);
        console.log(`Razão: ${availability.reason}`);

        // Mensagem personalizada com base no tipo de conflito
        let message = 'Há conflitos de horário';
        if (availability.reason === 'PROVIDER_SCHEDULE_CONFLICT') {
          message = 'O profissional já possui um agendamento neste horário';
        } else if (availability.reason === 'PATIENT_SCHEDULE_CONFLICT') {
          message = 'O paciente já possui um agendamento neste horário';
        } else if (availability.reason === 'HOLIDAY') {
          message = 'A data selecionada é um feriado';
        }

        return res.status(400).json({
          message,
          conflictDate: availability.conflictDate,
          reason: availability.reason
        });
      }

      console.log("Todas as datas disponíveis para agendamento");

      // Get companyId from location
      const location = await prisma.location.findUnique({
        where: { id: locationId },
        select: { companyId: true, branchId: true }
      });

      if (!location) {
        console.log(`ERRO: Local com ID ${locationId} não encontrado`);
        return res.status(404).json({ message: 'Local não encontrado' });
      }

      console.log(`Local encontrado - companyId: ${location.companyId}, branchId: ${location.branchId || 'NULL'}`);

      // NOVO: Verificar limites de convênio se um convênio foi informado
      if (insuranceId) {
        console.log(`Verificando limites para convênio ${insuranceId}...`);
        // Verificar se existe um limite de serviço para esta combinação
        const limit = await prisma.personInsuranceServiceLimit.findUnique({
          where: {
            personId_insuranceId_serviceTypeId: {
              personId,
              insuranceId,
              serviceTypeId
            }
          }
        });

        if (limit) {
          console.log(`Limite encontrado: mensal=${limit.monthlyLimit}, anual=${limit.yearlyLimit}`);
          // Calcular o início do mês corrente
          const appointmentDate = new Date();
          const startOfMonth = new Date(appointmentDate.getFullYear(), appointmentDate.getMonth(), 1);
          const endOfMonth = new Date(appointmentDate.getFullYear(), appointmentDate.getMonth() + 1, 0, 23, 59, 59);

          // Calcular o início do ano corrente
          const startOfYear = new Date(appointmentDate.getFullYear(), 0, 1);
          const endOfYear = new Date(appointmentDate.getFullYear(), 11, 31, 23, 59, 59);

          // Contar agendamentos no mês corrente
          const monthlyAppointmentsCount = await prisma.scheduling.count({
            where: {
              Person: {
                some: {
                  id: personId
                }
              },
              insuranceId,
              serviceTypeId,
              startDate: {
                gte: startOfMonth,
                lte: endOfMonth
              },
              status: {
                notIn: ['CANCELLED']
              }
            }
          });

          console.log(`Agendamentos no mês atual: ${monthlyAppointmentsCount}`);

          // Contar quantos agendamentos serão criados no mês atual
          const appointmentsInCurrentMonth = dates.filter(date => {
            const startDate = new Date(date.startDate);
            return startDate >= startOfMonth && startDate <= endOfMonth;
          }).length * sequentialAppointments;

          // Adicionar o agendamento original se estiver no mês atual
          const originalDate = new Date(originalStartDate);
          const originalInCurrentMonth = originalDate >= startOfMonth && originalDate <= endOfMonth ? sequentialAppointments : 0;

          const totalMonthlyAppointments = appointmentsInCurrentMonth + originalInCurrentMonth;

          console.log(`Agendamentos recorrentes no mês atual: ${appointmentsInCurrentMonth}`);
          console.log(`Agendamentos originais no mês atual: ${originalInCurrentMonth}`);
          console.log(`Total de novos agendamentos no mês atual: ${totalMonthlyAppointments}`);

          // Verificar limite mensal considerando todos os agendamentos
          if (limit.monthlyLimit > 0 && (monthlyAppointmentsCount + totalMonthlyAppointments) > limit.monthlyLimit) {
            console.log(`ERRO: Limite mensal excedido. Existentes: ${monthlyAppointmentsCount}, A criar: ${totalMonthlyAppointments}, Limite: ${limit.monthlyLimit}`);
            return res.status(400).json({
              message: `Limite mensal de ${limit.monthlyLimit} agendamentos atingido para este serviço com este convênio. ` +
                      `Você já utilizou ${monthlyAppointmentsCount} e está tentando agendar ${totalMonthlyAppointments} ` +
                      `(${monthlyAppointmentsCount + totalMonthlyAppointments} no total).`
            });
          }

          // Se existe limite anual, verificar também
          if (limit.yearlyLimit > 0) {
            const yearlyAppointmentsCount = await prisma.scheduling.count({
              where: {
                Person: {
                  some: {
                    id: personId
                  }
                },
                insuranceId,
                serviceTypeId,
                startDate: {
                  gte: startOfYear,
                  lte: endOfYear
                },
                status: {
                  notIn: ['CANCELLED']
                }
              }
            });

            console.log(`Agendamentos no ano atual: ${yearlyAppointmentsCount}`);

            // Contar quantos agendamentos serão criados no ano atual
            const appointmentsInCurrentYear = dates.filter(date => {
              const startDate = new Date(date.startDate);
              return startDate >= startOfYear && startDate <= endOfYear;
            }).length * sequentialAppointments;

            // Adicionar o agendamento original se estiver no ano atual
            const originalInCurrentYear = originalDate >= startOfYear && originalDate <= endOfYear ? sequentialAppointments : 0;

            const totalYearlyAppointments = appointmentsInCurrentYear + originalInCurrentYear;

            console.log(`Agendamentos recorrentes no ano atual: ${appointmentsInCurrentYear}`);
            console.log(`Agendamentos originais no ano atual: ${originalInCurrentYear}`);
            console.log(`Total de novos agendamentos no ano atual: ${totalYearlyAppointments}`);

            // Verificar limite anual considerando todos os agendamentos
            if (limit.yearlyLimit > 0 && (yearlyAppointmentsCount + totalYearlyAppointments) > limit.yearlyLimit) {
              console.log(`ERRO: Limite anual excedido. Existentes: ${yearlyAppointmentsCount}, A criar: ${totalYearlyAppointments}, Limite: ${limit.yearlyLimit}`);
              return res.status(400).json({
                message: `Limite anual de ${limit.yearlyLimit} agendamentos atingido para este serviço com este convênio. ` +
                        `Você já utilizou ${yearlyAppointmentsCount} e está tentando agendar ${totalYearlyAppointments} ` +
                        `(${yearlyAppointmentsCount + totalYearlyAppointments} no total).`
              });
            }
          }
        } else {
          console.log(`Nenhum limite encontrado para esta combinação de pessoa/convênio/serviço`);
        }
      }

      // Helper function to convert time string to minutes
      function timeToMinutes(timeStr) {
        const [hours, minutes] = timeStr.split(':').map(Number);
        const totalMinutes = hours * 60 + minutes;
        console.log(`[timeToMinutes] Convertendo ${timeStr} para ${totalMinutes} minutos (${Math.floor(totalMinutes/60)}:${totalMinutes%60})`);
        return totalMinutes;
      }

      // Usar transação para garantir consistência
      const { originalAppointment, originalSequentialAppointments, recurrence, schedulings } =
        await prisma.$transaction(async (prismaClient) => {
          // ETAPA 1: Criar o agendamento original separadamente
          console.log("=== CRIANDO AGENDAMENTO ORIGINAL ===");

          // Calcular data final baseado na duração do primeiro padrão
          const firstPattern = patterns[0];
          const startHourMin = firstPattern.startTime.split(':').map(Number);
          const endHourMin = firstPattern.endTime.split(':').map(Number);

          // Calcula a duração em minutos
          const startMinutes = startHourMin[0] * 60 + startHourMin[1];
          const endMinutes = endHourMin[0] * 60 + endHourMin[1];
          const durationMinutes = endMinutes - startMinutes;

          // Calcula a data final do agendamento original
          const originalEndDate = new Date(originalStartDate);
          originalEndDate.setMinutes(originalEndDate.getMinutes() + durationMinutes);

          console.log(`Data original - início: ${originalStartDate.toISOString()}, fim: ${originalEndDate.toISOString()}`);
          console.log(`Duração calculada: ${durationMinutes} minutos`);

          // Cria o agendamento original (sem recorrenceId)
          const originalAppointment = await prismaClient.scheduling.create({
            data: {
              title,
              description,
              userId,
              clientId: person.clientId, // Usar o clientId da pessoa
              locationId,
              serviceTypeId,
              insuranceId,
              startDate: originalStartDate,
              endDate: originalEndDate,
              creatorId: req.user.id,
              companyId: location.companyId,
              branchId: branchId || location.branchId,
              // NÃO inclui recurrenceId para manter independente
              Person: {
                connect: { id: personId }
              }
            }
          });

          console.log(`Agendamento original criado com ID: ${originalAppointment.id}`);
          console.log(`Agendamento original - início: ${originalAppointment.startDate}, fim: ${originalAppointment.endDate}`);

          // NOVO: Criar agendamentos sequenciais para o agendamento original (se necessário)
          const originalSequentialAppointments = [];

          if (sequentialAppointments > 1) {
            console.log(`Criando ${sequentialAppointments - 1} agendamentos sequenciais para o agendamento original...`);

            let previousEndDate = new Date(originalEndDate);
            const appointmentDuration = originalEndDate.getTime() - originalStartDate.getTime();

            for (let i = 1; i < sequentialAppointments; i++) {
              // Hora de início: exatamente quando o anterior termina
              const sequentialStartDate = new Date(previousEndDate.getTime());
              const sequentialEndDate = new Date(sequentialStartDate.getTime() + appointmentDuration);

              console.log(`Agendamento sequencial original #${i} - início: ${sequentialStartDate.toISOString()}, fim: ${sequentialEndDate.toISOString()}`);

              const sequentialAppointment = await prismaClient.scheduling.create({
                data: {
                  title,
                  description,
                  userId,
                  clientId: person.clientId, // Usar o clientId da pessoa
                  locationId,
                  serviceTypeId,
                  insuranceId,
                  startDate: sequentialStartDate,
                  endDate: sequentialEndDate,
                  creatorId: req.user.id,
                  companyId: location.companyId,
                  branchId: branchId || location.branchId,
                  // NÃO inclui recurrenceId para manter independente
                  Person: {
                    connect: { id: personId }
                  }
                }
              });

              console.log(`Agendamento sequencial original #${i} criado com ID: ${sequentialAppointment.id}`);
              originalSequentialAppointments.push(sequentialAppointment);

              previousEndDate = sequentialEndDate;
            }
          }

          // ETAPA 2: Criar a recorrência
          console.log("=== CRIANDO RECORRÊNCIA ===");

          const recurrence = await prismaClient.recurrence.create({
            data: {
              title,
              description,
              // Campos diretos
              recurrenceType,
              numberOfOccurrences: recurrenceType === 'OCCURRENCES' ? parseInt(recurrenceValue, 10) : null,
              endDate: recurrenceType === 'END_DATE' ? new Date(recurrenceValue) : null,
              // Não incluir campos de ID redundantes quando usamos relações
              // Adicionar todas as relações obrigatórias
              Client: {
                connect: { id: person.clientId }
              },
              // Relação com o criador
              creator: {
                connect: { id: req.user.id }
              },
              // Relação com o provedor (profissional)
              provider: {
                connect: { id: userId }
              },
              // Relação com a pessoa
              person: {
                connect: { id: personId }
              },
              // Relação com o local
              location: {
                connect: { id: locationId }
              },
              // Relação com o tipo de serviço
              serviceType: {
                connect: { id: serviceTypeId }
              },
              // Relação com o seguro (se houver)
              ...(insuranceId ? {
                insurance: {
                  connect: { id: insuranceId }
                }
              } : {}),
              // Relação com a empresa (se houver)
              ...(location.companyId ? {
                company: {
                  connect: { id: location.companyId }
                }
              } : {}),
              // Relação com a filial (se houver)
              ...(branchId || location.branchId ? {
                branch: {
                  connect: { id: branchId || location.branchId }
                }
              } : {}),
              patterns: {
                create: patterns.map(pattern => ({
                  dayOfWeek: pattern.dayOfWeek,
                  startTimeMinutes: timeToMinutes(pattern.startTime),
                  endTimeMinutes: timeToMinutes(pattern.endTime)
                }))
              }
            },
            include: {
              patterns: true
            }
          });

          console.log(`Recorrência criada com ID: ${recurrence.id}`);
          console.log(`Recorrência - tipo: ${recurrence.recurrenceType}, valor: ${recurrence.numberOfOccurrences || recurrence.endDate}`);
          console.log("Padrões criados:");
          for (const pattern of recurrence.patterns) {
            const startHour = Math.floor(pattern.startTimeMinutes / 60);
            const startMinute = pattern.startTimeMinutes % 60;
            const endHour = Math.floor(pattern.endTimeMinutes / 60);
            const endMinute = pattern.endTimeMinutes % 60;

            console.log(`- Dia ${pattern.dayOfWeek}: ${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')} - ${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')} (${pattern.startTimeMinutes} - ${pattern.endTimeMinutes} minutos)`);
          }

          // ETAPA 3: Criar os agendamentos da recorrência
          console.log("=== CRIANDO AGENDAMENTOS RECORRENTES ===");

          const schedulings = [];

          // Para cada data de recorrência
          for (let dateIndex = 0; dateIndex < dates.length; dateIndex++) {
            const date = dates[dateIndex];
            console.log(`Criando agendamento recorrente ${dateIndex + 1}/${dates.length}...`);
            console.log(`- Data: ${date.startDate.toISOString()} - ${date.endDate.toISOString()}`);

            // Criar o agendamento principal da recorrência
            const scheduling = await prismaClient.scheduling.create({
              data: {
                title,
                description,
                userId,
                clientId: person.clientId, // Usar o clientId da pessoa
                locationId,
                serviceTypeId,
                insuranceId,
                startDate: date.startDate,
                endDate: date.endDate,
                recurrenceId: recurrence.id,
                creatorId: req.user.id,
                companyId: location.companyId,
                branchId: branchId || location.branchId,
                Person: {
                  connect: { id: personId }
                }
              }
            });

            console.log(`- Agendamento criado com ID: ${scheduling.id}`);
            schedulings.push(scheduling);

            // NOVO: Criar agendamentos sequenciais para este agendamento recorrente (se necessário)
            if (sequentialAppointments > 1) {
              let previousEndDate = new Date(date.endDate);
              const appointmentDuration = date.endDate.getTime() - date.startDate.getTime();

              for (let i = 1; i < sequentialAppointments; i++) {
                // Hora de início: exatamente quando o anterior termina
                const sequentialStartDate = new Date(previousEndDate.getTime());
                const sequentialEndDate = new Date(sequentialStartDate.getTime() + appointmentDuration);

                console.log(`- Agendamento sequencial #${i} - início: ${sequentialStartDate.toISOString()}, fim: ${sequentialEndDate.toISOString()}`);

                const sequentialScheduling = await prismaClient.scheduling.create({
                  data: {
                    title: `${title} (Continuação ${i})`,
                    description,
                    userId,
                    clientId: person.clientId, // Usar o clientId da pessoa
                    locationId,
                    serviceTypeId,
                    insuranceId,
                    startDate: sequentialStartDate,
                    endDate: sequentialEndDate,
                    recurrenceId: recurrence.id, // Mesmo ID de recorrência
                    creatorId: req.user.id,
                    companyId: location.companyId,
                    branchId: branchId || location.branchId,
                    Person: {
                      connect: { id: personId }
                    }
                  }
                });

                console.log(`- Agendamento sequencial #${i} criado com ID: ${sequentialScheduling.id}`);
                schedulings.push(sequentialScheduling);

                previousEndDate = sequentialEndDate;
              }
            }
          }

          console.log(`Total de ${schedulings.length} agendamentos recorrentes criados`);

          // Retornar os objetos criados para uso fora da transação
          return { originalAppointment, originalSequentialAppointments, recurrence, schedulings };
        });

      // ETAPA 4: Preparar e enviar resposta
      console.log("=== ENVIANDO RESPOSTA ===");
      console.log(`Resposta inclui: agendamento original, ${originalSequentialAppointments.length} sequenciais originais, recorrência e ${schedulings.length} agendamentos recorrentes`);

      // Retorna agendamento original, recorrência e agendamentos recorrentes
      res.status(201).json({
        originalAppointment,
        originalSequentialAppointments, // NOVO: Agendamentos sequenciais do original
        recurrence,
        schedulings
      });

      console.log("=== PROCESSO DE CRIAÇÃO DE RECORRÊNCIA CONCLUÍDO ===");
    } catch (error) {
      console.error('ERRO FATAL ao criar recorrência:', error);
      console.log("Stack trace:", error.stack);

      // Verificar se é um erro de limite de convênio
      if (error.message && (
          error.message.includes('Limite mensal') ||
          error.message.includes('Limite anual')
      )) {
        return res.status(400).json({ message: error.message });
      }

      // Verificar se é um erro de disponibilidade
      if (error.message && error.message.includes('indisponível')) {
        return res.status(400).json({ message: error.message });
      }

      res.status(500).json({ message: 'Erro interno do servidor', error: error.message });
    }
  }

  /**
   * Atualiza um agendamento da recorrência
   */
  static async update(req, res) {
    console.log("=== INICIANDO ATUALIZAÇÃO DE AGENDAMENTO RECORRENTE ===");
    try {
      const { id } = req.params;
      const { updateAll, ...updateData } = req.body;

      console.log(`Atualizando agendamento: ${id}`);
      console.log(`Atualizar todos? ${updateAll ? 'Sim' : 'Não'}`);
      console.log("Dados para atualização:", JSON.stringify(updateData));

      const scheduling = await prisma.scheduling.findUnique({
        where: { id },
        include: { recurrence: true }
      });

      if (!scheduling) {
        console.log(`ERRO: Agendamento ${id} não encontrado`);
        return res.status(404).json({ message: 'Agendamento não encontrado' });
      }

      console.log(`Agendamento encontrado, recorrenceId: ${scheduling.recurrenceId || 'NULL'}`);

      if (updateAll && scheduling.recurrenceId) {
        console.log(`Atualizando todos os agendamentos futuros da recorrência ${scheduling.recurrenceId}`);

        // Atualiza todos os agendamentos futuros da recorrência
        const updatedSchedulings = await prisma.scheduling.updateMany({
          where: {
            recurrenceId: scheduling.recurrenceId,
            startDate: { gte: scheduling.startDate }
          },
          data: updateData
        });

        console.log(`${updatedSchedulings.count} agendamentos atualizados`);

        res.json({
          message: 'Agendamentos atualizados com sucesso',
          updated: updatedSchedulings.count
        });
      } else {
        console.log(`Atualizando apenas o agendamento específico ${id}`);

        // Atualiza apenas o agendamento específico
        const updatedScheduling = await prisma.scheduling.update({
          where: { id },
          data: updateData
        });

        console.log(`Agendamento atualizado: ${updatedScheduling.id}`);

        res.json(updatedScheduling);
      }

      console.log("=== ATUALIZAÇÃO DE AGENDAMENTO CONCLUÍDA ===");
    } catch (error) {
      console.error('ERRO FATAL ao atualizar agendamento:', error);
      console.log("Stack trace:", error.stack);
      res.status(500).json({ message: 'Erro interno do servidor', error: error.message });
    }
  }

  /**
   * Lista todas as recorrências
   */
  static async list(req, res) {
    try {
      const { page = 1, limit = 10 } = req.query;

      const where = {
        // If user is not system admin, limit to their company
        ...((req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) ?
          { companyId: req.user.companyId } : {})
      };

      const [recurrences, total] = await Promise.all([
        prisma.recurrence.findMany({
          where,
          skip: (page - 1) * limit,
          take: Number(limit),
          include: {
            patterns: true,
            schedulings: {
              where: {
                startDate: { gte: new Date() }
              }
            },
            person: {
              select: {
                id: true,
                fullName: true,
                phone: true,
                email: true,
                client: {
                  select: {
                    id: true,
                    email: true
                  }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        }),
        prisma.recurrence.count({ where })
      ]);

      res.json({
        recurrences,
        total,
        pages: Math.ceil(total / limit)
      });
    } catch (error) {
      console.error('Erro ao listar recorrências:', error);
      res.status(500).json({ message: 'Erro interno do servidor', error: error.message });
    }
  }

  /**
   * Cancela uma recorrência inteira
   */
  static async cancel(req, res) {
    try {
      const { id } = req.params;

      // Atualizar a recorrência para inativa
      await prisma.recurrence.update({
        where: { id },
        data: { active: false }
      });

      await prisma.scheduling.updateMany({
        where: {
          recurrenceId: id,
          startDate: { gte: new Date() }
        },
        data: {
          status: 'CANCELLED'
        }
      });

      res.json({ message: 'Recorrência cancelada com sucesso' });
    } catch (error) {
      console.error('Erro ao cancelar recorrência:', error);
      res.status(500).json({ message: 'Erro interno do servidor', error: error.message });
    }
  }
}

module.exports = {
  RecurrenceController,
  createRecurrenceValidation
};