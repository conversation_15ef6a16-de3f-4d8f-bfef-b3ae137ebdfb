const nodemailer = require("nodemailer");

class EmailService {
  constructor() {
    // Initialize with null transporter - will be configured in initialize()
    this.transporter = null;
  }

  /**
   * Initialize the email service with configuration from environment variables
   * @returns {Promise} - Result of initialization
   */
  async initialize() {
    try {
      this.transporter = nodemailer.createTransport({
        host: process.env.EMAIL_HOST,
        port: process.env.EMAIL_PORT,
        secure: process.env.EMAIL_SECURE === "true",
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASSWORD,
        },
      });

      // Test connection
      await this.transporter.verify();
      console.log("Serviço de email inicializado");
      return { success: true };
    } catch (error) {
      console.error("Error initializing email service:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Envia um email usando o transporter configurado
   * @param {string} to - Email do destinatário
   * @param {string} subject - Assun<PERSON> do email
   * @param {string} html - Conteúdo HTML do email
   * @returns {Promise} - Resultado do envio
   */
  async sendEmail(to, subject, html) {
    try {
      if (!this.transporter) {
        await this.initialize();
      }

      const result = await this.transporter.sendMail({
        from: `"${process.env.EMAIL_NAME}" <${process.env.EMAIL_USER}>`,
        to,
        subject,
        html,
      });

      console.log(`Email enviado para ${to}, ID: ${result.messageId}`);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error("Erro ao enviar email:", error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Envia email de confirmação de novo agendamento
   * @param {object} scheduling - Dados do agendamento
   * @param {object} client - Dados do cliente
   * @param {object} provider - Dados do profissional
   * @param {object} serviceType - Tipo de serviço
   * @param {object} location - Local do agendamento
   * @param {object} branch - Unidade onde será realizado o agendamento (opcional)
   */
  async sendNewAppointmentEmail(
    scheduling,
    client,
    provider,
    serviceType,
    location,
    branch = null
  ) {
    // Verificar se o cliente é válido
    if (!client || !client.email) {
      console.error("Cliente inválido ou sem email", client);
      return { success: false, error: "Cliente inválido ou sem email" };
    }

    const formattedDate = scheduling.startDate.toLocaleDateString("pt-BR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const formattedStartTime = scheduling.startDate.toLocaleTimeString(
      "pt-BR",
      {
        hour: "2-digit",
        minute: "2-digit",
      }
    );

    const formattedEndTime = scheduling.endDate.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    const subject = `Confirmação de Agendamento - ${scheduling.title}`;

    // Add branch information if available
    const branchInfo = branch
      ? `<p><strong>Unidade:</strong> ${branch.name}${
          branch.code ? ` (${branch.code})` : ""
        }</p>
       <p><strong>Endereço da Unidade:</strong> ${branch.address}</p>`
      : "";

    const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4682B4;">Confirmação de Agendamento</h2>
      <p>Olá, <strong>${client.fullName || client.login || "Cliente"}</strong>!</p>
      <p>Seu agendamento foi confirmado com sucesso.</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4682B4;">${scheduling.title}</h3>
        <p><strong>Data:</strong> ${formattedDate}</p>
        <p><strong>Horário:</strong> ${formattedStartTime} às ${formattedEndTime}</p>
        <p><strong>Profissional:</strong> ${provider.fullName}</p>
        <p><strong>Serviço:</strong> ${serviceType.name}</p>
        ${branchInfo}
        <p><strong>Local:</strong> ${location.name} - ${location.address}</p>
        ${
          scheduling.description
            ? `<p><strong>Observações:</strong> ${scheduling.description}</p>`
            : ""
        }
      </div>
      
      <p>Para cancelar ou reagendar, entre em contato conosco.</p>
      <p>Agradecemos pela preferência!</p>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777;">
        <p>Este é um email automático, por favor não responda.</p>
      </div>
    </div>
  `;

    return this.sendEmail(client.email, subject, html);
  }

  /**
   * Envia email de lembrete 1 dia antes do agendamento
   * @param {object} scheduling - Dados do agendamento
   * @param {object} client - Dados do cliente
   * @param {object} provider - Dados do profissional
   * @param {object} serviceType - Tipo de serviço
   * @param {object} location - Local do agendamento
   * @param {object} branch - Unidade onde será realizado o agendamento (opcional)
   */
  async sendReminderEmail(
    scheduling,
    client,
    provider,
    serviceType,
    location,
    branch = null
  ) {
    // Verificar se o cliente é válido
    if (!client || !client.email) {
      console.error("Cliente inválido ou sem email", client);
      return { success: false, error: "Cliente inválido ou sem email" };
    }

    const formattedDate = scheduling.startDate.toLocaleDateString("pt-BR", {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    });

    const formattedStartTime = scheduling.startDate.toLocaleTimeString(
      "pt-BR",
      {
        hour: "2-digit",
        minute: "2-digit",
      }
    );

    const formattedEndTime = scheduling.endDate.toLocaleTimeString("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
    });

    // Tokens para confirmar ou cancelar (em uma implementação real, você usaria JWT para maior segurança)
    const confirmToken = Buffer.from(
      `confirm-${scheduling.id}-${Date.now()}`
    ).toString("base64");
    const cancelToken = Buffer.from(
      `cancel-${scheduling.id}-${Date.now()}`
    ).toString("base64");

    const subject = `Lembrete: Seu agendamento amanhã - ${scheduling.title}`;

    // Add branch information if available
    const branchInfo = branch
      ? `<p><strong>Unidade:</strong> ${branch.name}${
          branch.code ? ` (${branch.code})` : ""
        }</p>
       <p><strong>Endereço da Unidade:</strong> ${branch.address}</p>`
      : "";

    const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4682B4;">Lembrete de Agendamento</h2>
      <p>Olá, <strong>${client.fullName || client.login || "Cliente"}</strong>!</p>
      <p>Este é um lembrete do seu agendamento <strong>amanhã</strong>.</p>
      
      <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <h3 style="margin-top: 0; color: #4682B4;">${scheduling.title}</h3>
        <p><strong>Data:</strong> ${formattedDate}</p>
        <p><strong>Horário:</strong> ${formattedStartTime} às ${formattedEndTime}</p>
        <p><strong>Profissional:</strong> ${provider.fullName}</p>
        <p><strong>Serviço:</strong> ${serviceType.name}</p>
        ${branchInfo}
        <p><strong>Local:</strong> ${location.name} - ${location.address}</p>
        ${
          scheduling.description
            ? `<p><strong>Observações:</strong> ${scheduling.description}</p>`
            : ""
        }
      </div>
      
      <div style="margin: 30px 0; text-align: center;">
        <p>Por favor, confirme ou cancele seu agendamento:</p>
        
        <a href="${
          process.env.FRONTEND_URL
        }/appointments/confirm/${confirmToken}" 
           style="display: inline-block; background-color: #4CAF50; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; margin-right: 10px;">
           Confirmar
        </a>
        
        <a href="${
          process.env.FRONTEND_URL
        }/appointments/cancel/${cancelToken}" 
           style="display: inline-block; background-color: #f44336; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px;">
           Cancelar
        </a>
      </div>
      
      <p>Se tiver dúvidas, entre em contato conosco.</p>
      <p>Agradecemos pela preferência!</p>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #777;">
        <p>Este é um email automático, por favor não responda.</p>
      </div>
    </div>
  `;

    return this.sendEmail(client.email, subject, html);
  }
}

module.exports = new EmailService();