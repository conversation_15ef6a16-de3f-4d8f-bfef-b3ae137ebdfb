// src/controllers/aba/skillController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createSkillValidation = [
  body("code").optional(),
  body("order").isInt({ min: 1 }).withMessage("Ordem deve ser um número inteiro positivo"),
  body("description").notEmpty().withMessage("Descrição da habilidade é obrigatória"),
];

const updateSkillValidation = [
  body("code").optional(),
  body("order").optional().isInt({ min: 1 }).withMessage("Ordem deve ser um número inteiro positivo"),
  body("description").optional().notEmpty().withMessage("Descrição da habilidade é obrigatória"),
  body("active").optional().isBoolean().withMessage("O campo active deve ser um booleano"),
];

class SkillController {
  /**
   * Cria uma nova habilidade
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { code, order, description } = req.body;

      // Criar habilidade
      const skill = await prisma.skill.create({
        data: {
          code,
          order,
          description,
          companyId: req.user.companyId,
        },
      });

      res.status(201).json(skill);
    } catch (error) {
      console.error("Erro ao criar habilidade:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todas as habilidades com filtros e paginação
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search = "",
        active = "true",
      } = req.query;

      // Construir filtros
      const where = {
        companyId: req.user.companyId,
        deletedAt: null,
      };

      // Filtro de busca
      if (search) {
        where.OR = [
          { code: { contains: search, mode: "insensitive" } },
          { description: { contains: search, mode: "insensitive" } },
        ];
      }

      // Filtro de status
      if (active !== "all") {
        where.active = active === "true";
      }

      // Contar total de registros
      const total = await prisma.skill.count({ where });

      // Buscar habilidades com paginação
      const skills = await prisma.skill.findMany({
        where,
        include: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: {
          order: "asc",
        },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar resposta
      const response = formatSuccessResponse(
        skills,
        "skills",
        total,
        Math.ceil(total / Number(limit))
      );

      res.json(response);
    } catch (error) {
      console.error("Erro ao listar habilidades:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém uma habilidade específica pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const skill = await prisma.skill.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!skill) {
        return res.status(404).json({ message: "Habilidade não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && skill.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para visualizar esta habilidade" });
      }

      res.json(skill);
    } catch (error) {
      console.error("Erro ao buscar habilidade:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza uma habilidade existente
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Verificar se a habilidade existe
      const existingSkill = await prisma.skill.findUnique({
        where: { id },
      });

      if (!existingSkill) {
        return res.status(404).json({ message: "Habilidade não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingSkill.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para atualizar esta habilidade" });
      }

      const { code, order, description, active } = req.body;

      // Atualizar habilidade
      const updatedSkill = await prisma.skill.update({
        where: { id },
        data: {
          code: code !== undefined ? code : undefined,
          order: order !== undefined ? order : undefined,
          description: description !== undefined ? description : undefined,
          active: active !== undefined ? active : undefined,
          updatedAt: new Date(),
        },
      });

      res.json(updatedSkill);
    } catch (error) {
      console.error("Erro ao atualizar habilidade:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status de uma habilidade (ativo/inativo)
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a habilidade existe
      const skill = await prisma.skill.findUnique({
        where: { id },
      });

      if (!skill) {
        return res.status(404).json({ message: "Habilidade não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && skill.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para alterar esta habilidade" });
      }

      // Alternar status
      const updatedSkill = await prisma.skill.update({
        where: { id },
        data: {
          active: !skill.active,
          updatedAt: new Date(),
        },
      });

      res.json(updatedSkill);
    } catch (error) {
      console.error("Erro ao alternar status da habilidade:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove uma habilidade (soft delete)
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a habilidade existe
      const skill = await prisma.skill.findUnique({
        where: { id },
      });

      if (!skill) {
        return res.status(404).json({ message: "Habilidade não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && skill.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para remover esta habilidade" });
      }

      // Soft delete
      await prisma.skill.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          active: false,
        },
      });

      res.json({ message: "Habilidade removida com sucesso" });
    } catch (error) {
      console.error("Erro ao remover habilidade:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Adiciona uma habilidade a uma avaliação
   */
  static async addToEvaluation(req, res) {
    try {
      const { evaluationId, skillId } = req.params;

      // Verificar se a avaliação existe
      const evaluation = await prisma.evaluation.findUnique({
        where: { id: evaluationId },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para modificar esta avaliação" });
      }

      // Verificar se a habilidade existe
      const skill = await prisma.skill.findUnique({
        where: { id: skillId },
      });

      if (!skill) {
        return res.status(404).json({ message: "Habilidade não encontrada" });
      }

      // Verificar se a habilidade pertence à mesma empresa
      if (skill.companyId !== evaluation.companyId) {
        return res.status(400).json({ message: "A habilidade não pertence à mesma empresa da avaliação" });
      }

      // Verificar se a relação já existe
      const existingRelation = await prisma.evaluationSkill.findUnique({
        where: {
          evaluationId_skillId: {
            evaluationId,
            skillId,
          },
        },
      });

      if (existingRelation) {
        return res.status(400).json({ message: "Esta habilidade já está associada a esta avaliação" });
      }

      // Adicionar habilidade à avaliação
      await prisma.evaluationSkill.create({
        data: {
          evaluationId,
          skillId,
        },
      });

      res.status(201).json({ message: "Habilidade adicionada à avaliação com sucesso" });
    } catch (error) {
      console.error("Erro ao adicionar habilidade à avaliação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove uma habilidade de uma avaliação
   */
  static async removeFromEvaluation(req, res) {
    try {
      const { evaluationId, skillId } = req.params;

      // Verificar se a avaliação existe
      const evaluation = await prisma.evaluation.findUnique({
        where: { id: evaluationId },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para modificar esta avaliação" });
      }

      // Verificar se a relação existe
      const existingRelation = await prisma.evaluationSkill.findUnique({
        where: {
          evaluationId_skillId: {
            evaluationId,
            skillId,
          },
        },
      });

      if (!existingRelation) {
        return res.status(404).json({ message: "Esta habilidade não está associada a esta avaliação" });
      }

      // Remover habilidade da avaliação
      await prisma.evaluationSkill.delete({
        where: {
          evaluationId_skillId: {
            evaluationId,
            skillId,
          },
        },
      });

      res.json({ message: "Habilidade removida da avaliação com sucesso" });
    } catch (error) {
      console.error("Erro ao remover habilidade da avaliação:", error);
      
      // Verificar se o erro é de restrição de chave estrangeira
      if (error.code === 'P2003') {
        return res.status(400).json({ 
          message: "Não é possível remover esta habilidade porque ela está sendo usada em tarefas/testes" 
        });
      }
      
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  SkillController,
  createSkillValidation,
  updateSkillValidation,
};
