"use client";

import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Clock, Plus, Trash, AlertCircle, Info } from 'lucide-react';
import TimeInput from '../common/TimeInput';

const DAYS_OF_WEEK = [
  { value: '1', label: 'Segunda-feira' },
  { value: '2', label: 'Terça-feira' },
  { value: '3', label: 'Quarta-feira' },
  { value: '4', label: 'Quinta-feira' },
  { value: '5', label: '<PERSON>ta-feira' },
  { value: '6', label: 'Sábado' },
  { value: '0', label: 'Domingo' },
];

const BranchWorkingHoursForm = forwardRef(({
  defaultWorkingHours,
  onChange,
  isLoading,
  labelClasses,
  inputClasses,
  errorClasses,
  onValidationChange // Nova prop para informar o componente pai sobre o estado da validação
}, ref) => {
  const [workingHours, setWorkingHours] = useState({});
  const [errors, setErrors] = useState({});

  // Initialize working hours from props or with default values
  useEffect(() => {
    if (defaultWorkingHours) {
      // Converte os dados da API para o formato de exibição
      const displayHours = {};

      Object.keys(defaultWorkingHours).forEach(day => {
        displayHours[day] = defaultWorkingHours[day].map(slot => ({
          startTime: minutesToTime(slot.startTimeMinutes),
          endTime: minutesToTime(slot.endTimeMinutes)
        }));
      });

      console.log('useEffect - dados convertidos para exibição:', displayHours);
      setWorkingHours(displayHours);
    } else {
      // Default working hours: Monday to Friday, 9:00-18:00
      const defaultHours = {};

      // Add default hours for weekdays (1-5)
      for (let i = 1; i <= 5; i++) {
        defaultHours[i] = [
          { startTime: '09:00', endTime: '18:00' }
        ];
      }

      console.log('useEffect - usando horários padrão:', defaultHours);
      setWorkingHours(defaultHours);
    }
  }, [defaultWorkingHours]);

  // Convert minutes to time string (HH:MM)
  const minutesToTime = (minutes) => {
    if (minutes === null || minutes === undefined) return '';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
  };

  // Convert time string (HH:MM) to minutes
  const timeToMinutes = (timeStr) => {
    if (!timeStr) return null;

    // Garantir que a string tem o formato correto
    if (!timeStr.includes(':')) {
      // Se não tiver ':', tenta formatar
      if (timeStr.length <= 2) {
        // Apenas horas
        return parseInt(timeStr) * 60;
      } else {
        // Assume que os dois primeiros dígitos são horas e o resto são minutos
        const hours = parseInt(timeStr.slice(0, 2));
        const minutes = parseInt(timeStr.slice(2));
        return hours * 60 + minutes;
      }
    }

    // Formato normal HH:MM
    const parts = timeStr.split(':');
    const hours = parseInt(parts[0] || '0');
    const minutes = parseInt(parts[1] || '0');

    // Validação básica
    if (isNaN(hours) || isNaN(minutes)) return null;

    return hours * 60 + minutes;
  };

  // Format working hours for display and editing
  const formatWorkingHoursForDisplay = (workingHoursData) => {
    console.log('formatWorkingHoursForDisplay - dados recebidos:', workingHoursData);
    const formatted = {};

    Object.keys(workingHoursData).forEach(dayOfWeek => {
      console.log('formatWorkingHoursForDisplay - processando dia:', dayOfWeek, 'slots:', workingHoursData[dayOfWeek]);

      formatted[dayOfWeek] = workingHoursData[dayOfWeek].map(slot => {
        // Verifica se os campos _startTime e _endTime existem (campos preservados)
        if (slot._startTime !== undefined || slot._endTime !== undefined) {
          console.log('formatWorkingHoursForDisplay - usando campos preservados');
          return {
            startTime: slot._startTime || '',
            endTime: slot._endTime || ''
          };
        }

        // Caso contrário, converte de minutos para string
        const startTime = minutesToTime(slot.startTimeMinutes);
        const endTime = minutesToTime(slot.endTimeMinutes);

        console.log('formatWorkingHoursForDisplay - convertendo slot:',
          'startTimeMinutes:', slot.startTimeMinutes, '->', startTime,
          'endTimeMinutes:', slot.endTimeMinutes, '->', endTime);

        return {
          startTime,
          endTime
          // Removidos os campos de intervalo
        };
      });
    });

    console.log('formatWorkingHoursForDisplay - resultado formatado:', formatted);
    return formatted;
  };

  // Format working hours for API
  const formatWorkingHoursForAPI = (workingHoursData) => {
    console.log('formatWorkingHoursForAPI - dados recebidos:', workingHoursData);
    const formatted = {};

    Object.keys(workingHoursData).forEach(dayOfWeek => {
      console.log('formatWorkingHoursForAPI - processando dia:', dayOfWeek, 'slots:', workingHoursData[dayOfWeek]);

      // Filtra slots vazios (sem horário de início e término)
      const validSlots = workingHoursData[dayOfWeek].filter(slot => {
        const hasStartTime = slot.startTime && slot.startTime.trim() !== '';
        const hasEndTime = slot.endTime && slot.endTime.trim() !== '';
        return hasStartTime || hasEndTime;
      });

      console.log('formatWorkingHoursForAPI - slots válidos:', validSlots);

      formatted[dayOfWeek] = validSlots.map(slot => {
        // Verifica se os valores existem antes de convertê-los
        const startTime = slot.startTime || '';
        const endTime = slot.endTime || '';

        const startTimeMinutes = timeToMinutes(startTime);
        const endTimeMinutes = timeToMinutes(endTime);

        console.log('formatWorkingHoursForAPI - convertendo slot:',
          'startTime:', startTime, '->', startTimeMinutes, 'minutos',
          'endTime:', endTime, '->', endTimeMinutes, 'minutos');

        return {
          startTimeMinutes,
          endTimeMinutes,
          // Removidos os campos de intervalo
          breakStartMinutes: null,
          breakEndMinutes: null
        };
      });

      // Se não houver slots válidos, adiciona um slot vazio
      if (formatted[dayOfWeek].length === 0) {
        formatted[dayOfWeek] = [{
          startTimeMinutes: null,
          endTimeMinutes: null,
          breakStartMinutes: null,
          breakEndMinutes: null
        }];
      }
    });

    console.log('formatWorkingHoursForAPI - resultado formatado:', formatted);
    return formatted;
  };

  // Handle changes to working hours
  const handleWorkingHoursChange = (dayOfWeek, index, field, value) => {
    console.log('handleWorkingHoursChange - dia:', dayOfWeek, 'índice:', index, 'campo:', field, 'valor:', value);

    // Cria uma cópia profunda do estado atual para evitar problemas de referência
    const newWorkingHours = JSON.parse(JSON.stringify(workingHours));

    if (!newWorkingHours[dayOfWeek]) {
      newWorkingHours[dayOfWeek] = [];
      console.log('handleWorkingHoursChange - criando array para o dia', dayOfWeek);
    }

    if (!newWorkingHours[dayOfWeek][index]) {
      newWorkingHours[dayOfWeek][index] = {
        startTime: '',
        endTime: ''
      };
      console.log('handleWorkingHoursChange - criando objeto para o índice', index);
    }

    // Preserva os valores existentes e atualiza apenas o campo especificado
    console.log('handleWorkingHoursChange - valor anterior:', newWorkingHours[dayOfWeek][index][field]);
    newWorkingHours[dayOfWeek][index][field] = value;
    console.log('handleWorkingHoursChange - novo valor:', newWorkingHours[dayOfWeek][index][field]);
    console.log('handleWorkingHoursChange - slot completo:', newWorkingHours[dayOfWeek][index]);

    // Atualiza o estado local
    setWorkingHours(newWorkingHours);
    console.log('handleWorkingHoursChange - estado atualizado');
    console.log('handleWorkingHoursChange - estado completo:', newWorkingHours);

    // Validate time slots
    validateTimeSlot(dayOfWeek, index, newWorkingHours[dayOfWeek][index]);

    // Notifica o componente pai com uma cópia dos dados
    if (onChange) {
      console.log('handleWorkingHoursChange - notificando componente pai');

      // Converte os dados para o formato da API
      const apiData = formatWorkingHoursForAPI(newWorkingHours);
      console.log('handleWorkingHoursChange - dados formatados para API:', apiData);
      onChange(apiData);
    }
  };

  // Validate time slot - validação durante a edição (menos rigorosa)
  const validateTimeSlot = (dayOfWeek, index, slot) => {
    const newErrors = { ...errors };

    if (!newErrors[dayOfWeek]) {
      newErrors[dayOfWeek] = [];
    }

    if (!newErrors[dayOfWeek][index]) {
      newErrors[dayOfWeek][index] = {};
    }

    // Clear previous errors
    newErrors[dayOfWeek][index] = {};

    // Só validamos campos obrigatórios quando ambos estiverem preenchidos ou quando o formulário for enviado
    // Isso permite que o usuário preencha um campo por vez

    // Validate that end time is after start time if both are provided
    if (slot.startTime && slot.endTime) {
      const startMinutes = timeToMinutes(slot.startTime);
      const endMinutes = timeToMinutes(slot.endTime);

      if (endMinutes <= startMinutes) {
        newErrors[dayOfWeek][index].endTime = 'Horário de término deve ser após o início';
      }
    }

    setErrors(newErrors);
  };

  // Validate all time slots - validação completa para submissão do formulário
  const validateAllTimeSlots = () => {
    const newErrors = {};
    let isValid = true;

    // Verificar cada dia e cada slot
    Object.keys(workingHours).forEach(dayOfWeek => {
      if (!newErrors[dayOfWeek]) {
        newErrors[dayOfWeek] = [];
      }

      workingHours[dayOfWeek].forEach((slot, index) => {
        if (!newErrors[dayOfWeek][index]) {
          newErrors[dayOfWeek][index] = {};
        }

        // Validar horário inicial
        if (!slot.startTime) {
          newErrors[dayOfWeek][index].startTime = 'Horário de início é obrigatório';
          isValid = false;
        }

        // Validar horário final
        if (!slot.endTime) {
          newErrors[dayOfWeek][index].endTime = 'Horário de término é obrigatório';
          isValid = false;
        }

        // Validar que o horário final é após o inicial
        if (slot.startTime && slot.endTime) {
          const startMinutes = timeToMinutes(slot.startTime);
          const endMinutes = timeToMinutes(slot.endTime);

          if (endMinutes <= startMinutes) {
            newErrors[dayOfWeek][index].endTime = 'Horário de término deve ser após o início';
            isValid = false;
          }
        }

        // Removida a validação de intervalos
      });
    });

    setErrors(newErrors);

    // Notificar o componente pai sobre o estado da validação
    if (onValidationChange) {
      onValidationChange(isValid);
    }

    return isValid;
  };

  // Add a new time slot for a day (apenas um horário por dia)
  const addTimeSlot = (dayOfWeek) => {
    const newWorkingHours = { ...workingHours };

    // Se já existe um horário para este dia, não adiciona outro
    if (newWorkingHours[dayOfWeek] && newWorkingHours[dayOfWeek].length > 0) {
      return;
    }

    if (!newWorkingHours[dayOfWeek]) {
      newWorkingHours[dayOfWeek] = [];
    }

    newWorkingHours[dayOfWeek].push({
      startTime: '',
      endTime: ''
    });

    setWorkingHours(newWorkingHours);

    // Notify parent component
    if (onChange) {
      onChange(formatWorkingHoursForAPI(newWorkingHours));
    }
  };

  // Remove a time slot
  const removeTimeSlot = (dayOfWeek, index) => {
    const newWorkingHours = { ...workingHours };

    if (newWorkingHours[dayOfWeek] && newWorkingHours[dayOfWeek].length > 0) {
      newWorkingHours[dayOfWeek].splice(index, 1);

      // If no more slots for this day, remove the day
      if (newWorkingHours[dayOfWeek].length === 0) {
        delete newWorkingHours[dayOfWeek];
      }

      setWorkingHours(newWorkingHours);

      // Remove errors for this slot
      const newErrors = { ...errors };
      if (newErrors[dayOfWeek] && newErrors[dayOfWeek].length > 0) {
        newErrors[dayOfWeek].splice(index, 1);

        if (newErrors[dayOfWeek].length === 0) {
          delete newErrors[dayOfWeek];
        }

        setErrors(newErrors);
      }

      // Notify parent component
      if (onChange) {
        onChange(formatWorkingHoursForAPI(newWorkingHours));
      }
    }
  };

  // Copy working hours from one day to other days
  const copyWorkingHours = (fromDay, toDays) => {
    console.log('copyWorkingHours - copiando de:', fromDay, 'para:', toDays);
    console.log('copyWorkingHours - horários atuais:', workingHours);

    if (!workingHours[fromDay] || workingHours[fromDay].length === 0) {
      console.log('copyWorkingHours - não há horários para copiar do dia', fromDay);
      return;
    }

    // Cria uma cópia profunda para evitar problemas de referência
    const newWorkingHours = JSON.parse(JSON.stringify(workingHours));

    console.log('copyWorkingHours - horários a serem copiados:', workingHours[fromDay]);

    toDays.forEach(toDay => {
      console.log('copyWorkingHours - copiando para o dia:', toDay);
      newWorkingHours[toDay] = JSON.parse(JSON.stringify(workingHours[fromDay]));
    });

    console.log('copyWorkingHours - novos horários:', newWorkingHours);

    setWorkingHours(newWorkingHours);
    console.log('copyWorkingHours - estado atualizado');

    // Notify parent component
    if (onChange) {
      console.log('copyWorkingHours - notificando componente pai');
      const formattedData = formatWorkingHoursForAPI(newWorkingHours);
      console.log('copyWorkingHours - dados formatados para API:', formattedData);
      onChange(formattedData);
    }
  };

  // Copy Monday to all weekdays
  const copyMondayToWeekdays = () => {
    console.log('copyMondayToWeekdays - chamado');
    console.log('copyMondayToWeekdays - estado atual:', workingHours);

    // Verifica se há horários válidos na segunda-feira
    if (!workingHours['1'] || workingHours['1'].length === 0) {
      console.log('copyMondayToWeekdays - não há horários na segunda-feira para copiar');
      alert('Não há horários na segunda-feira para copiar.');
      return;
    }

    // Verifica se os horários da segunda-feira têm valores válidos
    const mondaySlots = workingHours['1'];
    console.log('copyMondayToWeekdays - slots da segunda-feira:', mondaySlots);

    // Verifica se há pelo menos um slot com horário de início e término válidos
    const hasValidSlot = mondaySlots.some(slot => {
      return slot.startTime && slot.startTime.trim() !== '' &&
             slot.endTime && slot.endTime.trim() !== '';
    });

    if (!hasValidSlot) {
      console.log('copyMondayToWeekdays - não há horários válidos na segunda-feira');
      alert('Não há horários válidos na segunda-feira. Certifique-se de que há pelo menos um horário com início e término preenchidos.');
      return;
    }

    // Filtra apenas os slots válidos
    const validMondaySlots = mondaySlots.filter(slot => {
      return slot.startTime && slot.startTime.trim() !== '' &&
             slot.endTime && slot.endTime.trim() !== '';
    });

    console.log('copyMondayToWeekdays - slots válidos da segunda-feira:', validMondaySlots);

    // Cria uma cópia profunda do estado atual
    const newWorkingHours = JSON.parse(JSON.stringify(workingHours));

    // Copia os horários da segunda-feira para os outros dias da semana
    ['2', '3', '4', '5'].forEach(day => {
      console.log(`copyMondayToWeekdays - copiando para o dia ${day}`);

      // Garante que o array para o dia existe
      if (!newWorkingHours[day]) {
        newWorkingHours[day] = [];
      }

      // Limpa os horários existentes e adiciona os novos
      newWorkingHours[day] = JSON.parse(JSON.stringify(validMondaySlots));
    });

    console.log('copyMondayToWeekdays - novos horários:', newWorkingHours);

    // Atualiza o estado
    setWorkingHours(newWorkingHours);
    console.log('copyMondayToWeekdays - estado atualizado');

    // Notifica o componente pai
    if (onChange) {
      console.log('copyMondayToWeekdays - notificando componente pai');

      // Converte os dados para o formato da API
      const apiData = formatWorkingHoursForAPI(newWorkingHours);
      console.log('copyMondayToWeekdays - dados formatados para API:', apiData);
      onChange(apiData);
    }

    alert('Horários da segunda-feira copiados com sucesso para os outros dias da semana!');
  };

  // Render time input field
  const renderTimeInput = (dayOfWeek, index, field, label, value) => {
    const hasError = errors[dayOfWeek]?.[index]?.[field];

    // Função simplificada para lidar com mudanças no input de horário
    const handleTimeChange = (newValue) => {
      // Só atualiza se o valor for válido
      if (newValue) {
        console.log('handleTimeChange - novo valor válido:', newValue);
        handleWorkingHoursChange(dayOfWeek, index, field, newValue);
      } else {
        console.log('handleTimeChange - ignorando valor vazio');
      }
    };

    return (
      <div className="flex flex-col">
        <label className={`${labelClasses} text-xs`}>
          {label}
        </label>
        <TimeInput
          value={value || ''}
          onChange={handleTimeChange}
          className={`${inputClasses} ${hasError ? 'border-red-500 dark:border-red-700' : ''}`}
          disabled={isLoading}
          error={hasError}
          errorClassName={errorClasses}
        />
      </div>
    );
  };

  // Expor a função de validação para o componente pai via useImperativeHandle
  React.useImperativeHandle(ref, () => ({
    validateAllTimeSlots
  }));

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h4 className="text-base font-medium text-neutral-800 dark:text-gray-200 flex items-center gap-2">
          <Clock className="h-4 w-4 text-primary-500 dark:text-primary-400" />
          Horários de Trabalho Padrão
        </h4>

        <button
          type="button"
          onClick={() => {
            console.log('Botão "Copiar Segunda para dias úteis" clicado');
            copyMondayToWeekdays();
          }}
          className="text-xs px-2 py-1 bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded border border-primary-200 dark:border-primary-800 hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors"
        >
          Copiar Segunda para dias úteis
        </button>
      </div>

      <div className="bg-gray-50 dark:bg-gray-800/50 p-3 rounded-lg border border-gray-200 dark:border-gray-700 mb-4">
        <div className="flex items-start gap-2">
          <Info size={16} className="text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0" />
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Configure os horários de trabalho padrão para esta unidade. Estes horários serão aplicados automaticamente a todos os usuários associados a esta unidade.
          </p>
        </div>
      </div>

      <div className="space-y-8">
        {DAYS_OF_WEEK.map((day) => (
          <div key={day.value} className="border-b border-gray-200 dark:border-gray-700 pb-6 last:border-0">
            <div className="flex justify-between items-center mb-3">
              <h5 className="font-medium text-gray-800 dark:text-gray-200">{day.label}</h5>
              <button
                type="button"
                onClick={() => addTimeSlot(day.value)}
                disabled={workingHours[day.value] && workingHours[day.value].length > 0}
                className="text-xs px-2 py-1 bg-primary-50 dark:bg-primary-900/30 text-primary-700 dark:text-primary-300 rounded border border-primary-200 dark:border-primary-800 hover:bg-primary-100 dark:hover:bg-primary-900/50 transition-colors flex items-center gap-1 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus size={12} />
                Adicionar Horário
              </button>
            </div>

            {(!workingHours[day.value] || workingHours[day.value].length === 0) ? (
              <div className="text-sm text-gray-500 dark:text-gray-400 italic">
                Nenhum horário configurado para este dia
              </div>
            ) : (
              <div className="space-y-4">
                {workingHours[day.value].map((slot, index) => (
                  <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    {/* Start Time */}
                    <div className="md:col-span-1">
                      {renderTimeInput(day.value, index, 'startTime', 'Início', slot.startTime)}
                    </div>

                    {/* End Time */}
                    <div className="md:col-span-1">
                      {renderTimeInput(day.value, index, 'endTime', 'Término', slot.endTime)}
                    </div>

                    {/* Remove Button */}
                    <div className="md:col-span-1 flex items-end justify-end">
                      <button
                        type="button"
                        onClick={() => removeTimeSlot(day.value, index)}
                        className="text-xs px-2 py-1 bg-red-50 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded border border-red-200 dark:border-red-800 hover:bg-red-100 dark:hover:bg-red-900/50 transition-colors flex items-center gap-1"
                      >
                        <Trash size={12} />
                        Remover
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
});

export default BranchWorkingHoursForm;
