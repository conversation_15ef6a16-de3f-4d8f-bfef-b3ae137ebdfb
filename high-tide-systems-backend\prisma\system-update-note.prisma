// Model for system update notes
// This file is a specification for the model that needs to be added to the schema.prisma file

model SystemUpdateNote {
  id        String   @id @default(uuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String
  user      User     @relation(fields: [createdBy], references: [id])

  @@index([createdAt])
}

// Add this relation to the User model:
// SystemUpdateNotes SystemUpdateNote[]
