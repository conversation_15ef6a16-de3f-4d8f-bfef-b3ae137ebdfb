'use client';

import React from 'react';
import { useChat } from '../../contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import ChatHeader from './ChatHeader';
import ChatList from './ChatList';
import ChatMessages from './ChatMessages';
import ChatInput from './ChatInput';

const ChatContainer = () => {
  const {
    isPanelOpen,
    isModalOpen,
    activeConversation,
    isLoading
  } = useChat();
  const { user } = useAuth();

  // Se nem o painel nem o modal estiverem abertos, ou se for um cliente, não renderizar nada
  if ((!isPanelOpen && !isModalOpen) || (user && user.isClient)) return null;

  return (
    <div
      className={`chat-container fixed z-50 flex flex-col overflow-hidden bg-white dark:bg-gray-800 shadow-xl rounded-lg border border-orange-200 dark:border-orange-800 ${
        isModalOpen
          ? 'inset-4 md:inset-10 lg:inset-20'
          : 'bottom-4 right-4 w-80 h-96 md:w-96 md:h-[32rem]'
      }`}
    >
      <ChatHeader />

      <div className="flex-1 flex flex-col overflow-hidden">
        {!activeConversation ? (
          <div className="flex-1 overflow-y-auto">
            <ChatList />
          </div>
        ) : (
          <>
            <ChatMessages conversationId={activeConversation} />
            <ChatInput conversationId={activeConversation} />
          </>
        )}
      </div>

      {isLoading && (
        <div className="absolute inset-0 bg-black/20 dark:bg-black/40 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg flex items-center">
            <div className="w-5 h-5 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mr-3"></div>
            <span className="text-gray-700 dark:text-gray-300">Carregando...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatContainer;
