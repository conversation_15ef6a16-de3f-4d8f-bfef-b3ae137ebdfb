// components/calendar/InsuranceLimitTooltip.js
import React from 'react';
import { CreditCard, Calendar, Clock, AlertCircle } from 'lucide-react';

const InsuranceLimitTooltip = ({ insuranceInfo, isDarkMode }) => {
  // Se não há informações de convênio ou limites, não mostrar nada
  if (!insuranceInfo || !insuranceInfo.name) {
    return null;
  }

  // Extrair informações de limite
  const monthlyLimit = insuranceInfo.monthlyLimit || 0;
  const monthlyUsed = insuranceInfo.monthlyUsed || 0;

  // Determinar se o limite foi atingido
  const isMonthlyLimitReached = monthlyLimit > 0 && monthlyUsed >= monthlyLimit;
  const isAnyLimitReached = isMonthlyLimitReached;

  return (
    <div className={`p-2 rounded-md text-xs ${isDarkMode ? 'bg-gray-800' : 'bg-white'} shadow-lg border ${isDarkMode ? 'border-gray-700' : 'border-neutral-200'}`}>
      <div className="flex items-center gap-1 font-medium mb-1.5">
        <CreditCard size={12} className={isDarkMode ? 'text-primary-400' : 'text-primary-600'} />
        <span>{insuranceInfo.name}</span>
      </div>

      {isAnyLimitReached && (
        <div className="flex items-start mb-2 text-red-600 dark:text-red-400">
          <AlertCircle size={12} className="mr-1 mt-0.5" />
          <span className="font-medium">Limite atingido</span>
        </div>
      )}

      <div className="space-y-1.5">
        {/* Limite Mensal */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1">
            <Clock size={10} className={isDarkMode ? 'text-neutral-400' : 'text-neutral-600'} />
            <span className={isDarkMode ? 'text-neutral-300' : 'text-neutral-700'}>Mensal:</span>
          </div>

          <span className={`font-medium ${isMonthlyLimitReached ? 'text-red-500 dark:text-red-400' : isDarkMode ? 'text-neutral-300' : 'text-neutral-700'}`}>
            {monthlyLimit > 0
              ? `${monthlyUsed}/${monthlyLimit}`
              : 'Ilimitado'}
          </span>
        </div>

        {/* Removido o limite anual conforme solicitado */}
      </div>
    </div>
  );
};

export default InsuranceLimitTooltip;