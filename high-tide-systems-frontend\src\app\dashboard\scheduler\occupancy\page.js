"use client";

import React from 'react';
import OccupancyDashboard from '@/app/modules/scheduler/occupancy/OccupancyDashboard';
import { usePermissions } from '@/hooks/usePermissions';

export default function OccupancyRoute() {
  const { can } = usePermissions();
  const canViewDashboard = can('scheduling.dashboard.view');

  if (!canViewDashboard) {
    return (
      <div className="p-6 bg-neutral-50 dark:bg-gray-800 rounded-lg border border-neutral-200 dark:border-gray-700 text-center">
        <p className="text-neutral-600 dark:text-gray-300">
          Você não tem permissão para visualizar o dashboard de agendamentos.
        </p>
      </div>
    );
  }

  return <OccupancyDashboard />;
}
