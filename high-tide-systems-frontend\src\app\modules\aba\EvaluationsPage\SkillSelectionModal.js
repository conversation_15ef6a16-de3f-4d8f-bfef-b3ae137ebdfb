"use client";

import React, { useState, useEffect } from "react";
import { Activity, Search, Check, X } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleTable, ModuleSelect } from "@/components/ui";
import { skillsService } from "@/app/modules/aba/services/skillsService";
import { useToast } from "@/contexts/ToastContext";

const SkillSelectionModal = ({ isOpen, onClose, onSelect, currentSkills = [] }) => {
  const { toast_error } = useToast();
  const [skills, setSkills] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSkills, setSelectedSkills] = useState([]);
  const [filters, setFilters] = useState({
    search: "",
    active: true,
    page: 1,
    limit: 10
  });
  const [totalItems, setTotalItems] = useState(0);

  // Carregar habilidades disponíveis
  useEffect(() => {
    const loadSkills = async () => {
      try {
        setIsLoading(true);
        const response = await skillsService.getSkills(filters);

        // Filtrar habilidades que já estão na avaliação
        const currentSkillIds = currentSkills.map(skill => skill.id);
        const filteredSkills = response.items.filter(skill => !currentSkillIds.includes(skill.id));

        setSkills(filteredSkills);
        setTotalItems(response.total - currentSkillIds.length);
      } catch (error) {
        console.error("Erro ao carregar habilidades:", error);
        toast_error("Não foi possível carregar as habilidades");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadSkills();
    }
  }, [isOpen, filters, currentSkills, toast_error]);

  // Manipuladores de eventos
  const handleSearch = (e) => {
    e.preventDefault();
    setFilters({ ...filters, page: 1 });
  };

  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFilters({
      ...filters,
      [name]: type === "checkbox" ? checked : value,
      page: 1
    });
  };

  const handlePageChange = (page) => {
    setFilters({ ...filters, page });
  };

  const handleSelectSkill = (skill) => {
    const isSelected = selectedSkills.some(s => s.id === skill.id);

    if (isSelected) {
      setSelectedSkills(selectedSkills.filter(s => s.id !== skill.id));
    } else {
      setSelectedSkills([...selectedSkills, skill]);
    }
  };

  const handleConfirmSelection = () => {
    onSelect(selectedSkills);
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title="Selecionar Habilidades"
      size="lg"
      moduleColor="abaplus"
      icon={<Activity size={20} />}
    >
      <div className="p-6 space-y-6">
        {/* Barra de pesquisa */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <ModuleInput
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Pesquisar por código ou descrição..."
                icon={<Search size={18} />}
                moduleColor="abaplus"
                className="w-full"
              />
            </div>
            <div className="w-full md:w-1/4">
              <ModuleSelect
                name="active"
                value={filters.active === "" ? "" : filters.active ? "true" : "false"}
                onChange={(e) => {
                  const value = e.target.value;
                  setFilters({
                    ...filters,
                    active: value === "" ? "" : value === "true",
                    page: 1
                  });
                }}
                moduleColor="abaplus"
              >
                <option value="">Todos</option>
                <option value="true">Ativos</option>
                <option value="false">Inativos</option>
              </ModuleSelect>
            </div>
            <button
              type="submit"
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center justify-center gap-2"
            >
              <Search size={18} />
              Pesquisar
            </button>
          </form>
        </div>

        {/* Tabela de Habilidades */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <ModuleTable
            columns={[
              { key: "select", field: "select", header: "Selecionar" },
              { key: "code", field: "code", header: "Código" },
              { key: "order", field: "order", header: "Ordem" },
              { key: "description", field: "description", header: "Descrição" }
            ]}
            data={skills}
            isLoading={isLoading}
            pagination={{
              currentPage: filters.page,
              totalItems,
              itemsPerPage: filters.limit,
              onPageChange: handlePageChange
            }}
            emptyMessage="Nenhuma habilidade encontrada"
            emptyIcon={<Activity size={24} />}
            tableId="skills-selection-table"
            defaultSortField="order"
            defaultSortDirection="asc"
            renderRow={(skill, _index, moduleColors, visibleColumns) => (
              <tr
                key={skill.id}
                className={`${moduleColors.hoverBg} ${
                  selectedSkills.some(s => s.id === skill.id) ? "bg-teal-50 dark:bg-teal-900/20" : ""
                }`}
                onClick={() => handleSelectSkill(skill)}
              >
                {visibleColumns.includes("select") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center justify-center">
                      <div
                        className={`w-6 h-6 rounded-md flex items-center justify-center cursor-pointer ${
                          selectedSkills.some(s => s.id === skill.id)
                            ? "bg-teal-500 text-white"
                            : "border border-gray-300 dark:border-gray-600"
                        }`}
                      >
                        {selectedSkills.some(s => s.id === skill.id) && <Check size={16} />}
                      </div>
                    </div>
                  </td>
                )}
                {visibleColumns.includes("code") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{skill.code || "-"}</div>
                  </td>
                )}
                {visibleColumns.includes("order") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">{skill.order}</div>
                  </td>
                )}
                {visibleColumns.includes("description") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">{skill.description}</div>
                  </td>
                )}
              </tr>
            )}
            moduleColor="abaplus"
          />
        </div>

        {/* Resumo da seleção */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {selectedSkills.length} habilidade(s) selecionada(s)
              </span>
            </div>
            <div className="flex gap-2">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-2"
              >
                <X size={18} />
                Cancelar
              </button>
              <button
                onClick={handleConfirmSelection}
                className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
                disabled={selectedSkills.length === 0}
              >
                <Check size={18} />
                Confirmar Seleção
              </button>
            </div>
          </div>
        </div>
      </div>
    </ModuleModal>
  );
};

export default SkillSelectionModal;
