import React, { useState, useEffect } from "react";
import { Plus, Edit, Trash, AlertCircle, Clock, CreditCard, Tag } from "lucide-react";
import { ModuleTable } from "@/components/ui";
import { insuranceServiceLimitService } from "@/app/modules/people/services/insuranceServiceLimitService";
import InsuranceServiceLimitFormModal from "@/components/people/InsuranceServiceLimitFormModal";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";

const PersonInsuranceLimitsTab = ({ personId }) => {
  const [limits, setLimits] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [selectedLimit, setSelectedLimit] = useState(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [limitToDelete, setLimitToDelete] = useState(null);

  useEffect(() => {
    loadLimits();
  }, [personId]);

  const loadLimits = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await insuranceServiceLimitService.getLimitsByPerson(personId);
      setLimits(data);
    } catch (err) {
      console.error("Erro ao carregar limites:", err);
      setError("Não foi possível carregar os limites de serviço.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddLimit = () => {
    setSelectedLimit(null);
    setFormModalOpen(true);
  };

  const handleEditLimit = (limit) => {
    setSelectedLimit(limit);
    setFormModalOpen(true);
  };

  const handleDeleteLimit = (limit) => {
    setLimitToDelete(limit);
    setConfirmDialogOpen(true);
  };

  const confirmDelete = async () => {
    try {
      await insuranceServiceLimitService.deleteLimit(limitToDelete.id);
      loadLimits();
      setConfirmDialogOpen(false);
    } catch (err) {
      console.error("Erro ao excluir limite:", err);
      setError("Não foi possível excluir o limite de serviço.");
    }
  };

  // O ModuleTable já lida com o estado de carregamento

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-neutral-800 dark:text-neutral-200">
          Limites de Serviço por Convênio
        </h3>
        <button
          onClick={handleAddLimit}
          className="flex items-center gap-2 px-3 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
        >
          <Plus size={16} />
          <span>Adicionar Limite</span>
        </button>
      </div>

      <ModuleTable
        moduleColor="people"
        columns={[
          { header: 'Convênio', field: 'insurance', width: '30%' },
          { header: 'Tipo de Serviço', field: 'serviceType', width: '30%' },
          { header: 'Limite Mensal', field: 'monthlyLimit', width: '25%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={limits}
        isLoading={isLoading}
        emptyMessage="Nenhum limite de serviço configurado para esta pessoa."
        emptyIcon={<AlertCircle size={24} />}
        tableId="person-insurance-limits-table"
        enableColumnToggle={true}
        defaultSortField="insurance"
        defaultSortDirection="asc"
        renderRow={(limit, index, moduleColors, visibleColumns) => (
          <tr key={limit.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('insurance') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <CreditCard className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                  <span className="text-neutral-700 dark:text-neutral-300 truncate">
                    {limit.Insurance?.name || limit.insurance?.name || "N/A"}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('serviceType') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <Tag className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                  <span className="text-neutral-700 dark:text-neutral-300 truncate">
                    {limit.ServiceType?.name || limit.serviceType?.name || "N/A"}
                  </span>
                </div>
                {limit.notes && (
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1 ml-5">
                    {limit.notes}
                  </p>
                )}
              </td>
            )}

            {visibleColumns.includes('monthlyLimit') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                  <span className="text-neutral-700 dark:text-neutral-300">
                    {limit.monthlyLimit > 0 ? `${limit.monthlyLimit}x por mês` : "Ilimitado"}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right">
                <div className="flex items-center justify-end gap-2">
                  <button
                    onClick={() => handleEditLimit(limit)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteLimit(limit)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />

      {formModalOpen && (
        <InsuranceServiceLimitFormModal
          isOpen={formModalOpen}
          onClose={() => setFormModalOpen(false)}
          limit={selectedLimit}
          personId={personId}
          onSuccess={() => {
            loadLimits();
            setFormModalOpen(false);
          }}
        />
      )}

      {confirmDialogOpen && (
        <ConfirmationDialog
          isOpen={confirmDialogOpen}
          onClose={() => setConfirmDialogOpen(false)}
          onConfirm={confirmDelete}
          title="Excluir Limite de Serviço"
          message={`Tem certeza que deseja excluir o limite para ${limitToDelete?.Insurance?.name || limitToDelete?.insurance?.name} - ${limitToDelete?.ServiceType?.name || limitToDelete?.serviceType?.name}?`}
          variant="danger"
        />
      )}
    </div>
  );
};

export default PersonInsuranceLimitsTab;