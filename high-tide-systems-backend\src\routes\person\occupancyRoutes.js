// src/routes/occupancyRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { OccupancyController } = require('../../controllers/occupancyController');

// Rota para dados gerais de ocupação
router.get('/', authenticate, OccupancyController.getOccupancyData);

// Rota para detalhes de ocupação de um provedor específico
router.get('/provider/:providerId', authenticate, OccupancyController.getProviderOccupancyDetails);

// Rota para comparação de provedores em um tipo de serviço específico
router.get('/service-type/:serviceTypeId', authenticate, OccupancyController.getServiceTypeProviderComparison);

module.exports = router;