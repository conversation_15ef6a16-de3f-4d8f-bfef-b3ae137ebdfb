const multer = require("multer");
const path = require("path");
const crypto = require("crypto");
const fs = require("fs");

// Caminho base para uploads
const UPLOAD_PATH = process.env.NODE_ENV === 'production'
  ? path.resolve("/tmp/uploads")
  : path.resolve("/usr/src/app/uploads");

console.log('[upload.js] Caminho base para uploads:', UPLOAD_PATH);

// Garante que o diretório de uploads existe
const ensureUploadDirectoryExists = () => {
  const directories = [
    UPLOAD_PATH,
    path.join(UPLOAD_PATH, "documents")
  ];

  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
};

// Cria os diretórios necessários
ensureUploadDirectoryExists();

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    console.log('[upload.js] Configurando destino para upload');
    console.log('[upload.js] Usuário:', req.user.id);

    const userId = req.user.id;
    const userUploadPath = path.join(UPLOAD_PATH, "documents", userId);
    console.log('[upload.js] Caminho de destino:', userUploadPath);

    // Cria diretório específico para o usuário se não existir
    if (!fs.existsSync(userUploadPath)) {
      console.log('[upload.js] Diretório não existe, criando...');
      fs.mkdirSync(userUploadPath, { recursive: true });
      console.log('[upload.js] Diretório criado com sucesso');
    } else {
      console.log('[upload.js] Diretório já existe');
    }

    cb(null, userUploadPath);
  },
  filename: (req, file, cb) => {
    console.log('[upload.js] Gerando nome de arquivo');
    console.log('[upload.js] Arquivo original:', file.originalname);

    // Gera um nome único para o arquivo
    const uniqueSuffix = crypto.randomBytes(16).toString("hex");
    // Mantém o nome original do arquivo, mas adiciona um sufixo único
    const filename = `${path.parse(file.originalname).name}-${uniqueSuffix}${path.extname(file.originalname)}`;
    console.log('[upload.js] Nome de arquivo gerado:', filename);

    cb(null, filename);
  },
});

const fileFilter = (req, file, cb) => {
  // Aceita apenas arquivos PDF
  if (file.mimetype.startsWith('image/') || file.mimetype === 'application/pdf') {
    cb(null, true);
  } else {
    cb(new Error("Apenas imagens e PDFs são permitidos"));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // Limite de 5MB
  },
});

module.exports = upload;