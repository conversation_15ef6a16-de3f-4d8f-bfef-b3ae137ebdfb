-- Script para adicionar a coluna deletedAt às tabelas Profession e ProfessionGroup

-- Adicionar coluna deletedAt à tabela ProfessionGroup
ALTER TABLE "ProfessionGroup" 
ADD COLUMN IF NOT EXISTS "deletedAt" TIMESTAMP(3);

-- Adicionar coluna deletedAt à tabela Profession
ALTER TABLE "Profession" 
ADD COLUMN IF NOT EXISTS "deletedAt" TIMESTAMP(3);

-- <PERSON><PERSON><PERSON> índi<PERSON> para a coluna deletedAt na tabela ProfessionGroup
CREATE INDEX IF NOT EXISTS "ProfessionGroup_deletedAt_idx" 
ON "ProfessionGroup"("deletedAt");

-- C<PERSON>r índice para a coluna deletedAt na tabela Profession
CREATE INDEX IF NOT EXISTS "Profession_deletedAt_idx" 
ON "Profession"("deletedAt");
