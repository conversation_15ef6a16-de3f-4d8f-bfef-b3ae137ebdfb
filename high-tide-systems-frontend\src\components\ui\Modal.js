'use client';

import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';
import { X } from 'lucide-react';

const Modal = ({ isOpen, onClose, title, children, size = 'md' }) => {
  const [mounted, setMounted] = useState(false);

  // Montar o componente apenas no cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Efeito para prevenir scroll quando o modal estiver aberto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen || !mounted) return null;

  // Determinar a largura do modal com base no tamanho
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-2xl',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    full: 'max-w-full mx-4'
  };

  // Usar createPortal para renderizar o modal no nível mais alto do DOM
  const modalContent = (
    <div className="fixed inset-0 z-[11000] flex items-center justify-center overflow-y-auto">
      {/* Overlay de fundo escuro */}
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>

      <div className={`relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 ${sizeClasses[size]} w-full max-h-[90vh] flex flex-col z-[11050]`}>
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-gray-700">
          <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">
            {title}
          </h3>
          <button
            onClick={onClose}
            className="text-neutral-500 dark:text-gray-400 hover:text-neutral-700 dark:hover:text-gray-300"
          >
            <X size={20} />
          </button>
        </div>

        {/* Conteúdo */}
        <div className="overflow-y-auto flex-1">
          {children}
        </div>
      </div>
    </div>
  );

  // Renderizar o modal usando um portal para garantir que ele fique acima de tudo
  return createPortal(modalContent, document.body);
};

export default Modal;
