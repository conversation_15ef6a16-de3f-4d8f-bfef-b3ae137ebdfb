// src/controllers/cepController.js
const https = require('https');
const cacheService = require('../services/cacheService');

// CEPs raramente mudam, então podemos usar um TTL longo (1 semana)
const CEP_CACHE_TTL = 7 * 24 * 60 * 60; // 7 dias em segundos

class CepController {
  /**
   * Busca informações de endereço a partir de um CEP
   * Utiliza a API ViaCEP (https://viacep.com.br/)
   */
  static async searchByCep(req, res) {
    try {
      const { cep } = req.params;

      // Remove caracteres não numéricos
      const cleanCep = cep.replace(/\D/g, '');

      // Valida o CEP (deve ter 8 dígitos)
      if (cleanCep.length !== 8) {
        return res.status(400).json({
          message: 'CEP inválido. O CEP deve conter 8 dígitos.'
        });
      }

      // Verificar se o CEP está em cache
      const cacheKey = `cep:${cleanCep}`;
      const cachedData = await cacheService.get(cacheKey);

      if (cachedData) {
        console.log(`[CACHE] Usando dados em cache para CEP ${cleanCep}`);
        return res.json(cachedData);
      }

      // Se não estiver em cache, buscar na API
      console.log(`[CACHE] CEP ${cleanCep} não encontrado em cache, buscando na API`);

      // Faz a requisição para a API ViaCEP usando o módulo https nativo
      const url = `https://viacep.com.br/ws/${cleanCep}/json/`;

      // Criamos uma Promise para trabalhar com o https de forma assíncrona
      const fetchCep = () => {
        return new Promise((resolve, reject) => {
          https.get(url, (apiRes) => {
            let data = '';

            // Recebe os dados em chunks
            apiRes.on('data', (chunk) => {
              data += chunk;
            });

            // Quando terminar de receber os dados
            apiRes.on('end', () => {
              try {
                // Converte a string JSON para objeto
                const jsonData = JSON.parse(data);
                resolve(jsonData);
              } catch (e) {
                reject(new Error('Erro ao processar resposta da API'));
              }
            });
          }).on('error', (err) => {
            reject(err);
          });
        });
      };

      // Aguarda a resposta da API
      const responseData = await fetchCep();

      // Verifica se a resposta contém erro
      if (responseData.erro) {
        return res.status(404).json({ message: 'CEP não encontrado' });
      }

      // Formata a resposta
      const addressData = {
        cep: responseData.cep,
        logradouro: responseData.logradouro,
        complemento: responseData.complemento,
        bairro: responseData.bairro,
        localidade: responseData.localidade,
        uf: responseData.uf,
        ibge: responseData.ibge,
        gia: responseData.gia,
        ddd: responseData.ddd,
        siafi: responseData.siafi
      };

      // Armazenar no cache
      await cacheService.set(cacheKey, addressData, CEP_CACHE_TTL);
      console.log(`[CACHE] CEP ${cleanCep} armazenado em cache`);

      return res.json(addressData);
    } catch (error) {
      console.error('Erro ao buscar CEP:', error);

      return res.status(500).json({
        message: 'Erro interno do servidor ao buscar CEP'
      });
    }
  }
}

module.exports = { CepController };
