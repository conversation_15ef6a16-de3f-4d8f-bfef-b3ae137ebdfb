"use client";

import React, { useEffect, useState } from 'react';
import { DragDropContext } from 'react-beautiful-dnd';

// Este componente é um wrapper para o DragDropContext que garante que ele só seja renderizado no cliente
// e que o componente seja montado corretamente antes de renderizar o DragDropContext
const DragDropWrapper = ({ children, onDragEnd }) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Garantir que o componente só seja renderizado no cliente
    setIsMounted(true);
  }, []);

  if (!isMounted) {
    // Renderizar um placeholder enquanto o componente não está montado
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {React.Children.map(children, child => {
          return React.cloneElement(child, { isDragDisabled: true });
        })}
      </div>
    );
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {children}
      </div>
    </DragDropContext>
  );
};

export default DragDropWrapper;
