// src/swagger/userRoutes.js

/**
 * @swagger
 * tags:
 *   name: Usu<PERSON>rios
 *   description: Gerenciamento de usuários do sistema
 */

/**
 * @swagger
 * /users:
 *   post:
 *     summary: Cria um novo usuário
 *     description: Cria um novo usuário no sistema. Requer autenticação.
 *     tags: [Usuários]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - login
 *               - email
 *               - fullName
 *               - password
 *             properties:
 *               login:
 *                 type: string
 *                 description: Nome de login do usuário
 *                 example: maria.santos
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email do usuário
 *                 example: <EMAIL>
 *               fullName:
 *                 type: string
 *                 description: Nome completo do usuário
 *                 example: <PERSON>
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 6
 *                 description: Senha do usuário (mínimo 6 caracteres)
 *                 example: senha456
 *               cpf:
 *                 type: string
 *                 description: CPF do usuário (sem formatação)
 *                 example: "98765432100"
 *               cnpj:
 *                 type: string
 *                 description: CNPJ do usuário (sem formatação)
 *                 example: "98765432000199"
 *               birthDate:
 *                 type: string
 *                 format: date
 *                 description: Data de nascimento do usuário
 *                 example: "1985-05-15"
 *               address:
 *                 type: string
 *                 description: Endereço do usuário
 *                 example: Av. Exemplo, 456
 *               phone:
 *                 type: string
 *                 description: Telefone do usuário
 *                 example: "11988887777"
 *     responses:
 *       201:
 *         description: Usuário criado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserResponse'
 *       400:
 *         description: Dados inválidos ou usuário já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista usuários
 *     description: Retorna uma lista paginada de usuários. Permite filtrar por busca, status e módulo.
 *     tags: [Usuários]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Número de itens por página
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Texto para busca (nome, email, login)
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filtrar por status (ativo/inativo)
 *       - in: query
 *         name: module
 *         schema:
 *           type: string
 *           enum: [ADMIN, RH, FINANCIAL, SCHEDULING, BASIC]
 *         description: Filtrar por módulo
 *     responses:
 *       200:
 *         description: Lista de usuários
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 users:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/UserResponse'
 *                 total:
 *                   type: integer
 *                   description: Total de registros
 *                 pages:
 *                   type: integer
 *                   description: Total de páginas
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /users/{id}:
 *   get:
 *     summary: Obtém detalhes de um usuário
 *     description: Retorna os detalhes completos de um usuário específico.
 *     tags: [Usuários]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do usuário
 *     responses:
 *       200:
 *         description: Detalhes do usuário
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Usuário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Usuário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *   
 *   put:
 *     summary: Atualiza um usuário
 *     description: Atualiza os dados de um usuário existente.
 *     tags: [Usuários]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do usuário
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Nome completo do usuário
 *               address:
 *                 type: string
 *                 description: Endereço do usuário
 *               phone:
 *                 type: string
 *                 description: Telefone do usuário
 *               birthDate:
 *                 type: string
 *                 format: date
 *                 description: Data de nascimento do usuário
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email do usuário
 *               password:
 *                 type: string
 *                 format: password
 *                 description: Nova senha do usuário (opcional)
 *     responses:
 *       200:
 *         description: Usuário atualizado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserResponse'
 *       400:
 *         description: Dados inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Usuário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Usuário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove um usuário
 *     description: Remove um usuário do sistema.
 *     tags: [Usuários]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do usuário
 *     responses:
 *       204:
 *         description: Usuário removido com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Usuário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Usuário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /users/{id}/modules:
 *   patch:
 *     summary: Atualiza os módulos de um usuário
 *     description: Atualiza os módulos de acesso de um usuário. Apenas administradores podem conceder acesso de admin.
 *     tags: [Usuários]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do usuário
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - modules
 *             properties:
 *               modules:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [ADMIN, RH, FINANCIAL, SCHEDULING, BASIC]
 *                 description: Módulos de acesso
 *                 example: ["BASIC", "SCHEDULING"]
 *     responses:
 *       200:
 *         description: Módulos atualizados com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   format: uuid
 *                 fullName:
 *                   type: string
 *                 modules:
 *                   type: array
 *                   items:
 *                     type: string
 *       400:
 *         description: Dados inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Sem permissão para conceder acesso de administrador
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Apenas administradores podem conceder acesso de administrador"
 *       404:
 *         description: Usuário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Usuário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /users/{id}/status:
 *   patch:
 *     summary: Alterna o status de um usuário
 *     description: Ativa ou desativa um usuário no sistema.
 *     tags: [Usuários]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do usuário
 *     responses:
 *       200:
 *         description: Status alterado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   format: uuid
 *                 fullName:
 *                   type: string
 *                 active:
 *                   type: boolean
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Usuário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Usuário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */