import React from 'react';

const ModalFooter = ({
  isEditMode,
  canDelete,
  hasPermission,
  formData,
  isLoading,
  limitInfo,
  handleDelete,
  onClose
}) => {
  const buttonBaseClasses =
    "px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2";

  // Verificar se o limite mensal foi atingido
  const isLimitReached = limitInfo &&
    (limitInfo.monthly && !limitInfo.monthly.unlimited && limitInfo.monthly.used >= limitInfo.monthly.limit);

  return (
    <div className="border-t border-neutral-200 dark:border-gray-700 px-6 py-4 bg-neutral-50 dark:bg-gray-900 flex justify-between gap-2">
      <div className="flex gap-2">
        {/* Botão de exclusão - visível apenas para edição e com permissão */}
        {isEditMode && canDelete && (
          <button
            type="button"
            onClick={handleDelete}
            className={`${buttonBaseClasses} text-white bg-error-500 dark:bg-error-600 hover:bg-error-600 dark:hover:bg-error-700`}
            disabled={isLoading}
          >
            {isLoading ? "Excluindo..." : "Excluir"}
          </button>
        )}
      </div>

      <div className="flex gap-2 ml-auto">
        <button
          type="button"
          onClick={onClose}
          className={`${buttonBaseClasses} text-neutral-700 dark:text-neutral-200 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-600`}
          disabled={isLoading}
        >
          Cancelar
        </button>

        {/* Botão de salvar - visível apenas com permissão */}
        {hasPermission && (
          <button
            type="submit"
            className={`${buttonBaseClasses} text-white bg-primary-500 dark:bg-primary-600 hover:bg-primary-600 dark:hover:bg-primary-700`}
            disabled={
              isLoading ||
              (formData.recurrence.enabled &&
                formData.recurrence.patterns.length === 0) ||
              !hasPermission ||
              isLimitReached
            }
          >
            {isLoading
              ? "Salvando..."
              : isEditMode
                ? "Atualizar"
                : formData.recurrence.enabled
                  ? "Criar Recorrência"
                  : "Criar"}
          </button>
        )}
      </div>
    </div>
  );
};

export default ModalFooter;