"use client";

import React, { useEffect, useState } from 'react';
import { HelpCircle } from 'lucide-react';
import { usePathname } from 'next/navigation';
import { useTutorial } from '@/contexts/TutorialContext';
import { getTutorialForRoute } from '@/tutorials/tutorialMapping';

/**
 * Botão de ajuda contextual que mostra tutoriais específicos com base na rota atual
 */
const ContextualHelpButton = () => {
  const pathname = usePathname();
  const { startTutorial, isActive } = useTutorial();
  const [currentTutorial, setCurrentTutorial] = useState(null);
  const [isHovering, setIsHovering] = useState(false);

  // Busca o tutorial apropriado quando a rota muda
  useEffect(() => {
    const tutorialData = getTutorialForRoute(pathname);
    setCurrentTutorial(tutorialData);
  }, [pathname]);

  // Função para iniciar o tutorial contextual
  const handleStartTutorial = () => {
    if (currentTutorial && currentTutorial.steps && currentTutorial.steps.length > 0) {
      startTutorial(currentTutorial.steps, currentTutorial.name);
    } else {
      // Se não temos um tutorial para esta página, podemos mostrar uma mensagem
      console.log('Nenhum tutorial disponível para esta página');
      // Você pode adicionar aqui uma notificação para o usuário
    }
  };

  // Se o tutorial já estiver ativo, não mostramos o botão
  if (isActive) return null;

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Tooltip que aparece ao passar o mouse */}
      {isHovering && currentTutorial && (
        <div className="absolute bottom-16 right-0 bg-white dark:bg-gray-800 shadow-lg rounded-lg p-3 mb-2 w-48 text-sm text-gray-700 dark:text-gray-300 animate-fade-in">
          Clique para ver o tutorial desta página
          <div className="absolute bottom-0 right-5 transform translate-y-1/2 rotate-45 w-2 h-2 bg-white dark:bg-gray-800"></div>
        </div>
      )}
      
      {/* Botão principal */}
      <button
        onClick={handleStartTutorial}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        className={`
          w-12 h-12 rounded-full flex items-center justify-center shadow-lg 
          transition-all duration-300 hover:shadow-xl
          ${currentTutorial 
            ? 'bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700' 
            : 'bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400'}
        `}
        aria-label="Mostrar tutorial da página"
        disabled={!currentTutorial}
      >
        <HelpCircle size={currentTutorial ? 28 : 24} />
        
        {/* Indicador de "novo" para páginas com tutorial */}
        {currentTutorial && (
          <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-white border-2 border-primary-500 dark:border-primary-600"></span>
        )}
      </button>
    </div>
  );
};

export default ContextualHelpButton;