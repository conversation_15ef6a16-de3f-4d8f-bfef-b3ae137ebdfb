'use client';

/**
 * Função para realizar rolagem suave até um elemento com o ID especificado
 * @param {string} elementId - O ID do elemento para o qual rolar
 * @param {number} offset - Deslocamento opcional do topo (para compensar headers fixos)
 * @param {number} duration - Duração da animação em milissegundos
 */
export const scrollToElement = (elementId, offset = 0, duration = 800) => {
  // Remover o # se estiver presente
  const id = elementId.startsWith('#') ? elementId.substring(1) : elementId;
  
  // Encontrar o elemento pelo ID
  const element = document.getElementById(id);
  
  if (!element) {
    console.warn(`Elemento com ID "${id}" não encontrado`);
    return;
  }
  
  // Calcular a posição de destino
  const targetPosition = element.getBoundingClientRect().top + window.pageYOffset - offset;
  const startPosition = window.pageYOffset;
  const distance = targetPosition - startPosition;
  
  let startTime = null;
  
  // Função de easing para uma animação mais natural
  const easeInOutQuad = (t) => {
    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
  };
  
  // Função de animação
  const animation = (currentTime) => {
    if (startTime === null) startTime = currentTime;
    const timeElapsed = currentTime - startTime;
    const progress = Math.min(timeElapsed / duration, 1);
    const easedProgress = easeInOutQuad(progress);
    
    window.scrollTo(0, startPosition + distance * easedProgress);
    
    if (timeElapsed < duration) {
      requestAnimationFrame(animation);
    }
  };
  
  // Iniciar a animação
  requestAnimationFrame(animation);
};
