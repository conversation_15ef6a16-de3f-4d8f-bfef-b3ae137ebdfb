{"dataProcessamento": "2025-05-26T16:32:46.743Z", "funcionarios": {"total": 79, "ativos": 0, "inativos": 79, "encarregados": 35}, "profissoes": {"total": 52, "lista": [{"codigo": 3, "descricao": "Acompanhante de Idosos"}, {"codigo": 31, "descricao": "Analista Administrativo"}, {"codigo": 99, "descricao": "Analista de Recrutamento e Seleção"}, {"codigo": 100, "descricao": "Analista de Recursos Humanos"}, {"codigo": 107, "descricao": "<PERSON><PERSON><PERSON> de Si<PERSON>mas"}, {"codigo": 747, "descricao": "Aplicador ABA"}, {"codigo": 734, "descricao": "Arte Terapeuta"}, {"codigo": 136, "descricao": "Assistente de Departamento Pessoal"}, {"codigo": 744, "descricao": "Assistente de Supervisão"}, {"codigo": 752, "descricao": "Assistente Social"}, {"codigo": 730, "descricao": "Assistente Terapêutico(a)"}, {"codigo": 177, "descricao": "<PERSON><PERSON><PERSON>"}, {"codigo": 701, "descricao": "Auxiliar Administrativo"}, {"codigo": 740, "descricao": "Auxiliar de Desenvolvimento Infantil"}, {"codigo": 735, "descricao": "Circo Terapeuta"}, {"codigo": 239, "descricao": "Clínico Geral"}, {"codigo": 749, "descricao": "Consultor Comportamental"}, {"codigo": 746, "descricao": "Coordenador(a) ABA"}, {"codigo": 748, "descricao": "Coordenador(a) Administrativo(a)"}, {"codigo": 739, "descricao": "Coordenador(a) Escolar"}, {"codigo": 737, "descricao": "Diretor(a) Administrativo"}, {"codigo": 736, "descricao": "Diretor(a) Clínica"}, {"codigo": 741, "descricao": "Educador Fís<PERSON>"}, {"codigo": 355, "descricao": "<PERSON>fer<PERSON><PERSON>"}, {"codigo": 378, "descricao": "Estagiário (a)"}, {"codigo": 376, "descricao": "Estagiário Administrativo"}, {"codigo": 379, "descricao": "Estagiário de Atendimento a Clientes"}, {"codigo": 447, "descricao": "Fisioterapeuta"}, {"codigo": 448, "descricao": "Fonoaudiólogo(a)"}, {"codigo": 738, "descricao": "Fundador(a)"}, {"codigo": 459, "descricao": "G<PERSON>nte Administrativo"}, {"codigo": 514, "descricao": "Geriatra"}, {"codigo": 731, "descricao": "Implementador"}, {"codigo": 542, "descricao": "Médico"}, {"codigo": 732, "descricao": "Musicoterapeuta"}, {"codigo": 550, "descricao": "Neurologist<PERSON>"}, {"codigo": 743, "descricao": "Neuropsicólogo(a)"}, {"codigo": 551, "descricao": "Nutricionista"}, {"codigo": 571, "descricao": "Pediatra"}, {"codigo": 577, "descricao": "Professor <PERSON>"}, {"codigo": 578, "descricao": "Professor <PERSON>"}, {"codigo": 567, "descricao": "Psicólogo(a)"}, {"codigo": 733, "descricao": "Psicomotricista"}, {"codigo": 702, "descricao": "Psicopedagogo(a)"}, {"codigo": 742, "descricao": "Psiquiatra"}, {"codigo": 583, "descricao": "Recepcionista"}, {"codigo": 590, "descricao": "Secretária"}, {"codigo": 745, "descricao": "Supervisor(a) ABA"}, {"codigo": 589, "descricao": "Supervisor Administrativo"}, {"codigo": 751, "descricao": "Técnico Comportamental"}, {"codigo": 750, "descricao": "Terapeuta Líder"}, {"codigo": 654, "descricao": "Terapeuta Ocupacional"}]}, "cargosEncontrados": ["Coordenador(a) ABA", "Fisioterapeuta", "Aplicador ABA", "G<PERSON>nte Administrativo", "Supervisor Administrativo", "Diretor(a) Clínica", "Fonoaudiólogo(a)", "Auxiliar Administrativo", "Terapeuta Ocupacional", "Nutricionista", "Psicólogo(a)", "Analista Administrativo", "Assistente de Supervisão", "Supervisor(a) ABA", "Assistente de Departamento Pessoal", "Psicopedagogo(a)", "Estagiário (a)", "Neuropsicólogo(a)", "Diretor(a) Administrativo", "Musicoterapeuta", "Analista de Recursos Humanos", "<PERSON><PERSON><PERSON>"], "observacoes": ["Passwords foram definidos com hash padrão - DEVEM SER ALTERADOS", "Emails foram gerados automaticamente quando não fornecidos", "CompanyId, BranchId e ProfessionId precisam ser mapeados manualmente", "Módulos e permissões foram definidos com valores padrão", "GroupId das profissões precisa ser mapeado para grupos existentes"]}