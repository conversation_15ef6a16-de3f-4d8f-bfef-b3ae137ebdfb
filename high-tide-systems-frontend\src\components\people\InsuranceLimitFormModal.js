import React, { useState, useEffect } from "react";
import { CreditCard, FileText, CreditCard as CardIcon } from "lucide-react";
import { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from "@/components/ui";
import { insuranceServiceLimitService } from "@/app/modules/people/services/insuranceServiceLimitService";
import { personsService } from "@/app/modules/people/services/personsService";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { serviceTypeService } from "@/app/modules/scheduler/services/serviceTypeService";
import { useToast } from "@/contexts/ToastContext";

const InsuranceLimitFormModal = ({ isOpen, onClose, limit = null, onSuccess, personId = null }) => {
  const { toast_success, toast_error } = useToast();
  const [formData, setFormData] = useState({
    personId: personId || "",
    insuranceId: "",
    serviceTypeId: "",
    monthlyLimit: 0,
    notes: ""
  });
  const [persons, setPersons] = useState([]);
  const [insurances, setInsurances] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [personInsurances, setPersonInsurances] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);

  // Carregar dados iniciais
  useEffect(() => {
    loadOptions();
  }, []);

  // Carregar opções para os selects
  const loadOptions = async () => {
    setIsLoadingOptions(true);
    try {
      // Carregar pessoas (se não tiver personId fixo)
      if (!personId) {
        const personsData = await personsService.getPersons({ limit: 100 });
        const personsArray = personsData?.persons || personsData?.people || personsData?.data || [];
        setPersons(personsArray);
      }

      // Carregar tipos de serviço
      const serviceTypesData = await serviceTypeService.getServiceTypes();
      const serviceTypesArray = serviceTypesData?.serviceTypes || serviceTypesData?.data || [];
      setServiceTypes(serviceTypesArray);

      // Carregar convênios (todos)
      const insurancesData = await insurancesService.getInsurances();
      const insurancesArray = insurancesData?.insurances || insurancesData?.data || [];
      setInsurances(insurancesArray);

      // Se tiver personId, carregar convênios da pessoa
      if (formData.personId) {
        loadPersonInsurances(formData.personId);
      }
    } catch (error) {
      console.error("Erro ao carregar opções:", error);
      toast_error("Erro ao carregar opções. Por favor, tente novamente.");
    } finally {
      setIsLoadingOptions(false);
    }
  };

  // Carregar convênios da pessoa selecionada
  const loadPersonInsurances = async (personId) => {
    if (!personId) return;

    try {
      const data = await insurancesService.listPersonInsurances(personId);
      const personInsurancesArray = data?.insurances || data || [];
      setPersonInsurances(personInsurancesArray);
    } catch (error) {
      console.error("Erro ao carregar convênios da pessoa:", error);
      toast_error("Erro ao carregar convênios da pessoa.");
    }
  };

  // Preencher formulário quando editando
  useEffect(() => {
    if (limit) {
      setFormData({
        personId: limit.personId || "",
        insuranceId: limit.insuranceId || "",
        serviceTypeId: limit.serviceTypeId || "",
        monthlyLimit: limit.monthlyLimit || 0,
        notes: limit.notes || ""
      });

      // Carregar convênios da pessoa se tiver personId
      if (limit.personId) {
        loadPersonInsurances(limit.personId);
      }
    } else if (personId) {
      setFormData(prev => ({
        ...prev,
        personId
      }));
      loadPersonInsurances(personId);
    }
  }, [limit, personId]);

  // Atualizar convênios quando a pessoa mudar
  useEffect(() => {
    if (formData.personId) {
      loadPersonInsurances(formData.personId);
    }
  }, [formData.personId]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value } = e.target;

    // Converter para número quando for monthlyLimit
    if (name === "monthlyLimit") {
      setFormData({
        ...formData,
        [name]: parseInt(value, 10) || 0
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Validar dados
    if (!formData.personId || !formData.insuranceId || !formData.serviceTypeId) {
      toast_error("Por favor, preencha todos os campos obrigatórios.");
      setIsSubmitting(false);
      return;
    }

    try {
      if (limit) {
        // Modo de edição
        await insuranceServiceLimitService.updateLimit(limit.id, {
          monthlyLimit: formData.monthlyLimit,
          notes: formData.notes
        });
        toast_success("Limite de convênio atualizado com sucesso");
      } else {
        // Modo de adição
        await insuranceServiceLimitService.createLimit(formData);
        toast_success("Limite de convênio criado com sucesso");
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar limite de convênio:", err);
      toast_error(err.response?.data?.message || "Ocorreu um erro ao salvar o limite de convênio.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Normalizar dados de convênios da pessoa
  const normalizePersonInsurances = () => {
    if (!personInsurances || !Array.isArray(personInsurances) || personInsurances.length === 0) return [];

    return personInsurances.map(ins => {
      if (!ins) return null;
      // Se o objeto já tiver a estrutura correta
      if (ins.insurance) {
        return {
          id: ins.insurance.id,
          name: ins.insurance.name
        };
      }
      // Se for um objeto de convênio direto
      else if (ins.id && ins.name) {
        return {
          id: ins.id,
          name: ins.name
        };
      }
      // Se tiver apenas o ID do convênio
      else if (ins.insuranceId) {
        // Buscar o nome no array de todos os convênios
        const insuranceDetails = Array.isArray(insurances) ? insurances.find(i => i?.id === ins.insuranceId) : null;
        return {
          id: ins.insuranceId,
          name: insuranceDetails?.name || `Convênio ${ins.insuranceId}`
        };
      }
      return null;
    }).filter(Boolean);
  };

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="people"
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="people"
        type="submit"
        form="insurance-limit-form"
        isLoading={isSubmitting}
      >
        {limit ? "Atualizar" : "Salvar"}
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={limit ? "Editar Limite de Convênio" : "Novo Limite de Convênio"}
      icon={<CreditCard size={22} />}
      moduleColor="people"
      size="md"
      animateExit={true}
      footer={modalFooter}
    >
      <form id="insurance-limit-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6">
          <div>
            <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4">
              <CreditCard className="w-4 h-4" />
              Informações do Limite
            </h4>
          </div>

          {/* Seleção de Pessoa (se não tiver personId fixo) */}
          {!personId && (
            <ModuleFormGroup
              moduleColor="people"
              label="Paciente"
              htmlFor="personId"
              icon={<CardIcon size={16} />}
              required
            >
              <ModuleSelect
                moduleColor="people"
                id="personId"
                name="personId"
                value={formData.personId}
                onChange={handleChange}
                disabled={isLoadingOptions || isSubmitting || !!limit}
                required
                placeholder="Selecione um paciente"
              >
                {Array.isArray(persons) && persons.map((person) => (
                  <option key={person?.id} value={person?.id}>
                    {person?.fullName || 'Sem nome'}
                  </option>
                ))}
              </ModuleSelect>
            </ModuleFormGroup>
          )}

          {/* Seleção de Convênio */}
          <ModuleFormGroup
            moduleColor="people"
            label="Convênio"
            htmlFor="insuranceId"
            icon={<CardIcon size={16} />}
            required
            helpText={formData.personId && normalizePersonInsurances().length === 0 ? "Este paciente não possui convênios associados." : ""}
          >
            <ModuleSelect
              moduleColor="people"
              id="insuranceId"
              name="insuranceId"
              value={formData.insuranceId}
              onChange={handleChange}
              disabled={isLoadingOptions || isSubmitting || !formData.personId || !!limit}
              required
              placeholder="Selecione um convênio"
            >
              {Array.isArray(normalizePersonInsurances()) && normalizePersonInsurances().map((insurance) => (
                <option key={insurance?.id} value={insurance?.id}>
                  {insurance?.name || 'Sem nome'}
                </option>
              ))}
            </ModuleSelect>
          </ModuleFormGroup>

          {/* Seleção de Tipo de Serviço */}
          <ModuleFormGroup
            moduleColor="people"
            label="Tipo de Serviço"
            htmlFor="serviceTypeId"
            icon={<FileText size={16} />}
            required
          >
            <ModuleSelect
              moduleColor="people"
              id="serviceTypeId"
              name="serviceTypeId"
              value={formData.serviceTypeId}
              onChange={handleChange}
              disabled={isLoadingOptions || isSubmitting || !!limit}
              required
              placeholder="Selecione um tipo de serviço"
            >
              {Array.isArray(serviceTypes) && serviceTypes.map((serviceType) => (
                <option key={serviceType?.id} value={serviceType?.id}>
                  {serviceType?.name || 'Sem nome'}
                </option>
              ))}
            </ModuleSelect>
          </ModuleFormGroup>

          {/* Limite Mensal */}
          <ModuleFormGroup
            moduleColor="people"
            label="Limite Mensal"
            htmlFor="monthlyLimit"
            icon={<CardIcon size={16} />}
            helpText="0 = ilimitado"
          >
            <ModuleInput
              moduleColor="people"
              type="number"
              id="monthlyLimit"
              name="monthlyLimit"
              value={formData.monthlyLimit}
              onChange={handleChange}
              min="0"
              disabled={isSubmitting}
            />
          </ModuleFormGroup>

          {/* Observações */}
          <ModuleFormGroup
            moduleColor="people"
            label="Observações"
            htmlFor="notes"
            icon={<FileText size={16} />}
          >
            <ModuleTextarea
              moduleColor="people"
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              rows={3}
              disabled={isSubmitting}
            />
          </ModuleFormGroup>

        </form>
    </ModuleModal>
  );
};

export default InsuranceLimitFormModal;
