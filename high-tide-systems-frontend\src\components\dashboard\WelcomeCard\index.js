'use client';

import React from 'react';
import { Calendar } from 'lucide-react';
import TimeIcon from '../TimeIcon';
import { getGreeting, formatDate, getTimeBasedColors } from '@/utils/dateFormatters';
import { getRoleInfo } from '@/utils/statusHelpers';

export const WelcomeCard = ({ user }) => {
  const roleInfo = getRoleInfo(user?.role);
  const RoleIcon = roleInfo.icon;
  const formattedDate = formatDate();
  const isClient = user?.role === 'CLIENT';

  // Obter cores dinâmicas baseadas no horário
  const timeColors = getTimeBasedColors();

  // Adicionar classes para dark mode às cores baseadas no horário
  const darkTimeColors = {
    ...timeColors,
    border: `${timeColors.border} dark:border-gray-700`,
    text: `${timeColors.text} dark:text-primary-400`,
    shadow: `${timeColors.shadow} dark:shadow-gray-900`,
    boxShadow: timeColors.boxShadow
  };

  return (
    <div id='WelcomeCard' className={`bg-gradient-to-r from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 rounded-xl border ${darkTimeColors.border} shadow-xl dark:shadow-lg dark:shadow-black/30 mb-8 relative overflow-hidden`}
      style={{ boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 1px 3px 0 rgba(0, 0, 0, 0.1) inset' }}>
      <div className="absolute top-0 left-0 w-full h-full bg-white/20 dark:bg-black/10 backdrop-blur-sm"></div>
      <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-transparent via-transparent to-gray-50/50 dark:to-gray-900/50"></div>
      <div className="relative p-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
          <div className="flex items-start gap-4">
            <div className={`p-4 rounded-xl ${darkTimeColors.text} border-2 ${darkTimeColors.border} shadow-lg dark:shadow-md dark:shadow-black/20 ${darkTimeColors.shadow}`}
              style={{ boxShadow: darkTimeColors.boxShadow }}>
              {isClient ? <RoleIcon size={24} /> : <TimeIcon />}
            </div>
            <div>
              <h2 className="text-3xl font-bold text-gray-800 dark:text-gray-100 mb-1">
                {getGreeting()}, <span className={darkTimeColors.text}>{user?.fullName?.split(' ')[0] || 'Usuário'}</span>!
              </h2>
              <p className="text-gray-500 dark:text-gray-400 flex items-center">
                <Calendar size={14} className="mr-2 text-gray-400 dark:text-gray-500" />
                {formattedDate}
              </p>
              {isClient && (
                <div className="mt-1 space-y-1">
                  <p className="text-gray-500 dark:text-gray-400 flex items-center">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 mr-2">
                      Área do Cliente
                    </span>
                    Bem-vindo à sua área exclusiva
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Aqui você pode gerenciar seus dados e visualizar seus agendamentos
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeCard;