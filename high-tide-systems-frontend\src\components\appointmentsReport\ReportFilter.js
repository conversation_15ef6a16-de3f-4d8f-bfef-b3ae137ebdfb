import React, { useState, useEffect } from "react";
import { format as dateFormat } from "date-fns";
import {
  Search,
  Calendar,
  Filter,
  RefreshCw
} from "lucide-react";
import { FilterButton } from "@/components/ui/ModuleHeader";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import MultiSelect from "@/components/ui/multi-select";

const ReportFilters = ({
  filters,
  setFilters,
  onSearch,
  onExport,
  isLoading
}) => {
  // Estado para os dados dos selects
  const [providers, setProviders] = useState([]);
  const [persons, setPersons] = useState([]);
  const [locations, setLocations] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [isFilterExpanded, setIsFilterExpanded] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(false);

  // Opções de status
  const statusOptions = [
    { value: "PENDING", label: "Pendente" },
    { value: "CONFIRMED", label: "Confirmado" },
    { value: "CANCELLED", label: "Cancelado" },
    { value: "COMPLETED", label: "Concluído" },
    { value: "NO_SHOW", label: "Não Compareceu" }
  ];

  // Carregar dados para os filtros
  useEffect(() => {
    const loadFilterData = async () => {
      setIsDataLoading(true);
      try {
        console.log("Carregando dados para filtros do relatório...");

        // Carregar dados de forma separada para melhor depuração
        console.log("Carregando profissionais...");
        const providersData = await appointmentService.getProviders();
        console.log(`Carregados ${providersData?.length || 0} profissionais`);

        console.log("Carregando pessoas...");
        const personsData = await appointmentService.getPersons();
        console.log(`Carregadas ${personsData?.length || 0} pessoas`);

        console.log("Carregando locais...");
        const locationsData = await appointmentService.getLocations();
        console.log(`Carregados ${locationsData?.length || 0} locais`);

        console.log("Carregando tipos de serviço...");
        const serviceTypesData = await appointmentService.getServiceTypes();
        console.log(`Carregados ${serviceTypesData?.length || 0} tipos de serviço`);

        // Formatar dados para o MultiSelect
        setProviders(
          providersData.map(provider => ({
            value: provider.id,
            label: provider.fullName
          }))
        );

        setPersons(
          personsData.map(person => ({
            value: person.id,
            label: person.fullName
          }))
        );

        setLocations(
          locationsData.map(location => ({
            value: location.id,
            label: location.name
          }))
        );

        setServiceTypes(
          serviceTypesData.map(type => ({
            value: type.id,
            label: type.name
          }))
        );
      } catch (error) {
        console.error("Erro ao carregar dados para filtros:", error);
      } finally {
        setIsDataLoading(false);
      }
    };

    loadFilterData();
  }, []);

  // Atualizar filtros diretamente sem aplicar automaticamente
  const handleFilterChange = (newFilters) => {
    console.log("Filtros alterados:", JSON.stringify(newFilters, null, 2));

    // Verificar se os valores dos MultiSelect estão corretos
    if (newFilters.status && newFilters.status.length > 0) {
      console.log("Status no handleFilterChange:", newFilters.status);
    }
    if (newFilters.providers && newFilters.providers.length > 0) {
      console.log("Providers no handleFilterChange:", newFilters.providers);
    }
    if (newFilters.persons && newFilters.persons.length > 0) {
      console.log("Persons no handleFilterChange:", newFilters.persons);
    }
    if (newFilters.locations && newFilters.locations.length > 0) {
      console.log("Locations no handleFilterChange:", newFilters.locations);
    }
    if (newFilters.serviceTypes && newFilters.serviceTypes.length > 0) {
      console.log("ServiceTypes no handleFilterChange:", newFilters.serviceTypes);
    }

    // Atualizar o estado dos filtros sem acionar a busca
    setFilters(newFilters);

    // Não aplicamos automaticamente os filtros - a busca só será feita quando o botão "Buscar" for clicado
  };

  // Limpar todos os filtros sem aplicar automaticamente
  const handleClearFilters = () => {
    const clearedFilters = {
      search: "",
      startDate: null,
      endDate: null,
      status: [],
      providers: [],
      persons: [],
      locations: [],
      serviceTypes: []
    };

    // Atualizar o estado dos filtros
    setFilters(clearedFilters);

    // Não aplicamos automaticamente os filtros - a busca só será feita quando o botão "Buscar" for clicado
    // Se quiser aplicar automaticamente após limpar, descomente a linha abaixo
    // onSearch();
  };

  // Não precisamos mais do menu de exportação interno, pois estamos usando o componente ExportMenu

  // Não precisamos mais do código para fechar o menu de exportação, pois o componente ExportMenu já lida com isso

  return (
    <div className="space-y-4">
      {/* Barra de pesquisa e botões */}
      <div className="flex flex-col md:flex-row gap-3 md:items-center">
        <div className="flex-1">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Buscar agendamentos..."
              value={filters.search}
              onChange={(e) => handleFilterChange({ ...filters, search: e.target.value })}
              className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <FilterButton
            type="button"
            onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            moduleColor="scheduler"
            variant="secondary"
          >
            <div className="flex items-center gap-2">
              <Filter size={16} />
              <span>Filtros</span>
              <span className="bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 py-0.5 rounded-full text-xs">
                {Object.entries(filters).filter(([key, value]) => {
                  if (key === 'search') return false;
                  if (Array.isArray(value)) return value.length > 0;
                  return value !== null && value !== '';
                }).length}
              </span>
            </div>
          </FilterButton>

          <FilterButton
            type="button"
            onClick={onSearch}
            moduleColor="scheduler"
            variant="primary"
            disabled={isLoading}
          >
            <div className="flex items-center gap-2">
              <Search size={16} />
              <span>{isLoading ? "Buscando..." : "Buscar"}</span>
            </div>
          </FilterButton>
        </div>
      </div>

      {/* Filtros avançados (expansíveis) */}
      {isFilterExpanded && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Período - Data Inicial
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="date"
                value={filters.startDate ? dateFormat(filters.startDate, 'yyyy-MM-dd') : ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  startDate: e.target.value ? new Date(e.target.value) : null
                })}
                className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Período - Data Final
            </label>
            <div className="relative">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Calendar className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="date"
                value={filters.endDate ? dateFormat(filters.endDate, 'yyyy-MM-dd') : ''}
                onChange={(e) => handleFilterChange({
                  ...filters,
                  endDate: e.target.value ? new Date(e.target.value) : null
                })}
                className="pl-10 pr-4 py-2 w-full rounded-md border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Status
            </label>
            <MultiSelect
              options={statusOptions}
              value={filters.status || []}
              onChange={(selected) => {
                console.log("Status selecionados:", selected);
                handleFilterChange({ ...filters, status: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Profissionais
            </label>
            <MultiSelect
              options={providers}
              value={filters.providers || []}
              onChange={(selected) => {
                console.log("Profissionais selecionados:", selected);
                handleFilterChange({ ...filters, providers: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Pacientes
            </label>
            <MultiSelect
              options={persons}
              value={filters.persons || []}
              onChange={(selected) => {
                console.log("Pacientes selecionados:", selected);
                handleFilterChange({ ...filters, persons: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Locais
            </label>
            <MultiSelect
              options={locations}
              value={filters.locations || []}
              onChange={(selected) => {
                console.log("Locais selecionados:", selected);
                handleFilterChange({ ...filters, locations: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Tipos de Serviço
            </label>
            <MultiSelect
              options={serviceTypes}
              value={filters.serviceTypes || []}
              onChange={(selected) => {
                console.log("Tipos de serviço selecionados:", selected);
                handleFilterChange({ ...filters, serviceTypes: selected });
              }}
              placeholder="Selecione..."
              className="w-full"
              moduleOverride="scheduler"
              loading={isDataLoading}
            />
          </div>

          <div className="flex items-end md:col-span-2 lg:col-span-4">
            <FilterButton
              type="button"
              onClick={handleClearFilters}
              moduleColor="scheduler"
              variant="secondary"
            >
              <div className="flex items-center gap-2">
                <RefreshCw size={16} />
                <span>Limpar Filtros</span>
              </div>
            </FilterButton>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReportFilters;