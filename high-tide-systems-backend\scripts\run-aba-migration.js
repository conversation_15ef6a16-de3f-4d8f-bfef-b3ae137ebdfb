// scripts/run-aba-migration.js
const { execSync } = require('child_process');
const path = require('path');

// Função para executar um comando no container Docker
function runDockerCommand(command) {
  console.log(`\n=== Executando comando: ${command} ===\n`);
  
  try {
    // Executar o comando no container Docker
    execSync(`docker exec -it high-tide-systems-api ${command}`, { 
      stdio: 'inherit',
      cwd: path.resolve(__dirname, '..')
    });
    
    console.log(`\n✅ Comando executado com sucesso!\n`);
    return true;
  } catch (error) {
    console.error(`\n❌ Erro ao executar o comando:`, error);
    return false;
  }
}

// Função principal para executar a migração
async function runMigration() {
  console.log('=== Iniciando migração do módulo ABA+ ===');
  
  // Gerar a migração
  const migrationName = 'add_aba_module';
  const generateSuccess = runDockerCommand(`npx prisma migrate dev --name ${migrationName}`);
  
  if (!generateSuccess) {
    console.error('❌ Falha ao gerar a migração. Verifique os erros acima.');
    process.exit(1);
  }
  
  // Gerar o cliente Prisma
  const generateClientSuccess = runDockerCommand('npx prisma generate');
  
  if (!generateClientSuccess) {
    console.error('❌ Falha ao gerar o cliente Prisma. Verifique os erros acima.');
    process.exit(1);
  }
  
  console.log('\n=== Migração do módulo ABA+ concluída com sucesso! ===\n');
}

// Executar a função principal
runMigration().catch(error => {
  console.error('Erro não tratado:', error);
  process.exit(1);
});
