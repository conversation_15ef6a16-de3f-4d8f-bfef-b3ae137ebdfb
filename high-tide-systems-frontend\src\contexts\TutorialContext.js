"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';

// Contexto para o sistema de tutorial
const TutorialContext = createContext(null);

// Provider que disponibiliza as funções e estados do tutorial
export const TutorialProvider = ({ children }) => {
  // Estado para controlar se o tutorial está ativo
  const [isActive, setIsActive] = useState(false);

  // Estado para armazenar as etapas do tutorial atual
  const [steps, setSteps] = useState([]);

  // Estado para rastrear a etapa atual
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Nome do tutorial atual
  const [tutorialName, setTutorialName] = useState('');

  // Estado para armazenar os tutoriais visualizados pelo usuário
  const [completedTutorials, setCompletedTutorials] = useState({});

  // Carregar tutoriais concluídos do localStorage no carregamento inicial
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCompletedTutorials = localStorage.getItem('completedTutorials');
      if (savedCompletedTutorials) {
        try {
          setCompletedTutorials(JSON.parse(savedCompletedTutorials));
        } catch (e) {
          console.error('Erro ao carregar tutoriais concluídos:', e);
        }
      }
    }
  }, []);

  // Função para iniciar um tutorial
  const startTutorial = useCallback((tutorialSteps, name) => {
    if (!tutorialSteps || tutorialSteps.length === 0) {
      console.warn('Tutorial: Tentativa de iniciar tutorial sem passos');
      return;
    }

    console.log(`Tutorial: Iniciando tutorial "${name}" com ${tutorialSteps.length} passos`);
    console.log('Tutorial: Passos:', tutorialSteps);

    // Fechar qualquer tutorial que possa estar aberto
    setIsActive(false);

    // Verificar se os seletores existem na página
    const validSteps = tutorialSteps.filter(step => {
      if (!step.selector) {
        console.warn(`Tutorial: Passo sem seletor encontrado no tutorial "${name}"`);
        return false;
      }

      try {
        const elements = document.querySelectorAll(step.selector);
        const hasElements = elements && elements.length > 0;

        if (!hasElements) {
          console.warn(`Tutorial: Seletor "${step.selector}" não encontrado na página para o tutorial "${name}"`);
        }

        return hasElements;
      } catch (error) {
        console.error(`Tutorial: Erro ao verificar seletor "${step.selector}"`, error);
        return false;
      }
    });

    if (validSteps.length === 0) {
      console.warn(`Tutorial: Nenhum passo válido encontrado para o tutorial "${name}"`);
      return;
    }

    if (validSteps.length < tutorialSteps.length) {
      console.warn(`Tutorial: Alguns passos foram removidos porque seus seletores não foram encontrados (${validSteps.length}/${tutorialSteps.length})`);
    }

    // Definir o novo tutorial após um pequeno atraso
    setTimeout(() => {
      setSteps(validSteps);
      setCurrentStepIndex(0);
      setTutorialName(name || 'tutorial');
      setIsActive(true);
      console.log(`Tutorial: Primeiro passo ativo, seletor: ${validSteps[0]?.selector}`);
    }, 50);
  }, []);

  // Função para avançar para a próxima etapa
  const nextStep = useCallback(() => {
    console.log(`Tutorial: Avançando do passo ${currentStepIndex + 1} de ${steps.length}`);

    if (currentStepIndex < steps.length - 1) {
      const nextIndex = currentStepIndex + 1;
      console.log(`Tutorial: Indo para o passo ${nextIndex + 1}, seletor: ${steps[nextIndex]?.selector}`);
      setCurrentStepIndex(nextIndex);
    } else {
      // Se for a última etapa, marca o tutorial como concluído e encerra
      console.log(`Tutorial: Concluindo tutorial "${tutorialName}"`);

      if (tutorialName) {
        const newCompletedTutorials = {
          ...completedTutorials,
          [tutorialName]: true
        };

        setCompletedTutorials(newCompletedTutorials);

        // Salvar no localStorage
        if (typeof window !== 'undefined') {
          localStorage.setItem('completedTutorials', JSON.stringify(newCompletedTutorials));
        }
      }

      setIsActive(false);
    }
  }, [currentStepIndex, steps, steps.length, tutorialName, completedTutorials]);

  // Função para voltar para a etapa anterior
  const prevStep = useCallback(() => {
    console.log(`Tutorial: Voltando do passo ${currentStepIndex + 1} de ${steps.length}`);

    if (currentStepIndex > 0) {
      const prevIndex = currentStepIndex - 1;
      console.log(`Tutorial: Voltando para o passo ${prevIndex + 1}, seletor: ${steps[prevIndex]?.selector}`);
      setCurrentStepIndex(prevIndex);
    }
  }, [currentStepIndex, steps]);

  // Função para encerrar o tutorial
  const endTutorial = useCallback(() => {
    setIsActive(false);
    setCurrentStepIndex(0);
  }, []);

  // Verificar se um tutorial específico já foi concluído
  const isTutorialCompleted = useCallback((tutorialName) => {
    return !!completedTutorials[tutorialName];
  }, [completedTutorials]);

  // Função para resetar um tutorial específico (marcar como não visto)
  const resetTutorial = useCallback((tutorialName) => {
    if (completedTutorials[tutorialName]) {
      const newCompletedTutorials = { ...completedTutorials };
      delete newCompletedTutorials[tutorialName];

      setCompletedTutorials(newCompletedTutorials);

      // Atualizar localStorage
      if (typeof window !== 'undefined') {
        localStorage.setItem('completedTutorials', JSON.stringify(newCompletedTutorials));
      }
    }
  }, [completedTutorials]);

  // Função para resetar todos os tutoriais
  const resetAllTutorials = useCallback(() => {
    setCompletedTutorials({});

    // Limpar localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('completedTutorials', JSON.stringify({}));
    }
  }, []);

  // Obter a etapa atual do tutorial
  const currentStep = steps[currentStepIndex];

  // Verifica se é a primeira etapa
  const isFirstStep = currentStepIndex === 0;

  // Verifica se é a última etapa
  const isLastStep = currentStepIndex === steps.length - 1;

  // Verifica se há etapas disponíveis
  const hasSteps = steps.length > 0;

  return (
    <TutorialContext.Provider
      value={{
        isActive,
        currentStep,
        currentStepIndex,
        steps,
        tutorialName,
        isFirstStep,
        isLastStep,
        hasSteps,
        startTutorial,
        nextStep,
        prevStep,
        endTutorial,
        isTutorialCompleted,
        resetTutorial,
        resetAllTutorials
      }}
    >
      {children}
    </TutorialContext.Provider>
  );
};

// Hook personalizado para usar o contexto do tutorial
export const useTutorial = () => {
  const context = useContext(TutorialContext);

  if (!context) {
    throw new Error('useTutorial deve ser usado dentro de um TutorialProvider');
  }

  return context;
};