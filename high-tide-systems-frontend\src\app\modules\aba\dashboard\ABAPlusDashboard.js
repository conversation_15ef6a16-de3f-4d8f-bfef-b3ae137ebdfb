"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Chart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON>
} from "recharts";
import {
  Brain,
  Activity,
  BookOpen,
  ClipboardList,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  Filter,
  RefreshCw,
  BarChart2,
  <PERSON><PERSON><PERSON> as PieChartIcon
} from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { abaPlusDashboardService } from "../services/abaPlusDashboardService";
import { ModuleHeader, ModuleTable, ModuleFormGroup, ModuleSelect } from "@/components/ui";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";

const ABAPlusDashboard = () => {
  const { user } = useAuth();
  const { toast_error } = useToast();
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [chartType, setChartType] = useState("bar");
  const [dateRange, setDateRange] = useState({
    startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
    endDate: new Date()
  });

  // Carregar dados do dashboard
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setIsLoading(true);
        const data = await abaPlusDashboardService.getABAPlusDashboardData({
          startDate: dateRange.startDate,
          endDate: dateRange.endDate
        });
        setDashboardData(data);
      } catch (error) {
        console.error("Erro ao carregar dados do dashboard:", error);
        toast_error("Não foi possível carregar os dados do dashboard");
      } finally {
        setIsLoading(false);
      }
    };

    loadDashboardData();
  }, [dateRange, toast_error]);

  // Cores para os gráficos
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8"];

  // Dados de exemplo para o dashboard (serão substituídos por dados reais da API)
  const sampleData = {
    skillsByCategory: [
      { name: "Comunicação", value: 24 },
      { name: "Habilidades Sociais", value: 18 },
      { name: "Comportamento", value: 15 },
      { name: "Acadêmico", value: 12 },
      { name: "Motor", value: 9 }
    ],
    programsByStatus: [
      { name: "Em Andamento", value: 35 },
      { name: "Concluído", value: 20 },
      { name: "Não Iniciado", value: 15 },
      { name: "Pausado", value: 5 }
    ],
    evaluationsByMonth: [
      { name: "Jan", count: 12 },
      { name: "Fev", count: 15 },
      { name: "Mar", count: 18 },
      { name: "Abr", count: 14 },
      { name: "Mai", count: 22 },
      { name: "Jun", count: 19 }
    ],
    recentPrograms: [
      {
        id: "1",
        learnerName: "João Silva",
        skillName: "Comunicação Verbal",
        status: "Em Andamento",
        progress: 65,
        lastUpdate: "2023-06-15"
      },
      {
        id: "2",
        learnerName: "Maria Oliveira",
        skillName: "Interação Social",
        status: "Em Andamento",
        progress: 42,
        lastUpdate: "2023-06-14"
      },
      {
        id: "3",
        learnerName: "Pedro Santos",
        skillName: "Comportamento Adaptativo",
        status: "Concluído",
        progress: 100,
        lastUpdate: "2023-06-10"
      },
      {
        id: "4",
        learnerName: "Ana Costa",
        skillName: "Habilidades Acadêmicas",
        status: "Não Iniciado",
        progress: 0,
        lastUpdate: "2023-06-08"
      }
    ]
  };

  // Função para formatar data
  const formatDate = (dateString) => {
    if (!dateString) return "";
    return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
  };

  // Função para atualizar o dashboard
  const handleRefresh = async () => {
    try {
      setIsLoading(true);
      const data = await abaPlusDashboardService.getABAPlusDashboardData({
        startDate: dateRange.startDate,
        endDate: dateRange.endDate
      });
      setDashboardData(data);
      toast_success("Dashboard atualizado com sucesso");
    } catch (error) {
      console.error("Erro ao atualizar dashboard:", error);
      toast_error("Não foi possível atualizar o dashboard");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Dashboard ABA+"
        description="Análise e estatísticas do módulo ABA+"
        moduleColor="abaplus"
      >
        <button
          onClick={handleRefresh}
          className="p-2 text-teal-600 hover:bg-teal-50 dark:text-teal-400 dark:hover:bg-teal-900/20 rounded-lg transition-colors"
          aria-label="Atualizar dashboard"
        >
          <RefreshCw size={20} />
        </button>
      </ModuleHeader>

      {/* Filtros */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md">
        <div className="flex flex-col md:flex-row gap-4 items-end">
          <ModuleFormGroup label="Tipo de Gráfico" className="w-full md:w-1/4">
            <ModuleSelect
              value={chartType}
              onChange={(e) => setChartType(e.target.value)}
              moduleColor="abaplus"
            >
              <option value="bar">Gráfico de Barras</option>
              <option value="pie">Gráfico de Pizza</option>
            </ModuleSelect>
          </ModuleFormGroup>

          <div className="flex gap-4 w-full md:w-auto">
            <button
              onClick={handleRefresh}
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
            >
              <Filter size={18} />
              Filtrar
            </button>
          </div>
        </div>
      </div>

      {/* Cards de Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total de Habilidades */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border-l-4 border-teal-500 dark:border-teal-400">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Habilidades</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">78</h3>
              <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-2">
                <TrendingUp size={14} className="mr-1" />
                <span>+12% no último mês</span>
              </p>
            </div>
            <div className="p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
              <Activity className="text-teal-600 dark:text-teal-400" size={24} />
            </div>
          </div>
        </div>

        {/* Total de Programas */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border-l-4 border-blue-500 dark:border-blue-400">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Programas</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">45</h3>
              <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-2">
                <TrendingUp size={14} className="mr-1" />
                <span>+8% no último mês</span>
              </p>
            </div>
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
              <BookOpen className="text-blue-600 dark:text-blue-400" size={24} />
            </div>
          </div>
        </div>

        {/* Total de Avaliações */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border-l-4 border-purple-500 dark:border-purple-400">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Avaliações</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">32</h3>
              <p className="text-xs text-red-600 dark:text-red-400 flex items-center mt-2">
                <TrendingDown size={14} className="mr-1" />
                <span>-5% no último mês</span>
              </p>
            </div>
            <div className="p-3 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
              <ClipboardList className="text-purple-600 dark:text-purple-400" size={24} />
            </div>
          </div>
        </div>

        {/* Total de Pacientes */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md border-l-4 border-orange-500 dark:border-orange-400">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm text-gray-500 dark:text-gray-400">Total de Pacientes</p>
              <h3 className="text-2xl font-bold text-gray-800 dark:text-white mt-1">24</h3>
              <p className="text-xs text-green-600 dark:text-green-400 flex items-center mt-2">
                <TrendingUp size={14} className="mr-1" />
                <span>+15% no último mês</span>
              </p>
            </div>
            <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <Users className="text-orange-600 dark:text-orange-400" size={24} />
            </div>
          </div>
        </div>
      </div>

      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Habilidades por Categoria */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Habilidades por Categoria</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setChartType("bar")}
                className={`p-1.5 rounded-md ${
                  chartType === "bar"
                    ? "bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400"
                    : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <BarChart2 size={18} />
              </button>
              <button
                onClick={() => setChartType("pie")}
                className={`p-1.5 rounded-md ${
                  chartType === "pie"
                    ? "bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400"
                    : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <PieChartIcon size={18} />
              </button>
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === "bar" ? (
                <BarChart
                  data={sampleData.skillsByCategory}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" name="Quantidade" fill="#14b8a6" />
                </BarChart>
              ) : (
                <PieChart>
                  <Pie
                    data={sampleData.skillsByCategory}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {sampleData.skillsByCategory.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              )}
            </ResponsiveContainer>
          </div>
        </div>

        {/* Programas por Status */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Programas por Status</h3>
            <div className="flex gap-2">
              <button
                onClick={() => setChartType("bar")}
                className={`p-1.5 rounded-md ${
                  chartType === "bar"
                    ? "bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400"
                    : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <BarChart2 size={18} />
              </button>
              <button
                onClick={() => setChartType("pie")}
                className={`p-1.5 rounded-md ${
                  chartType === "pie"
                    ? "bg-teal-100 dark:bg-teal-900/30 text-teal-600 dark:text-teal-400"
                    : "text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <PieChartIcon size={18} />
              </button>
            </div>
          </div>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              {chartType === "bar" ? (
                <BarChart
                  data={sampleData.programsByStatus}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="value" name="Quantidade" fill="#0ea5e9" />
                </BarChart>
              ) : (
                <PieChart>
                  <Pie
                    data={sampleData.programsByStatus}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {sampleData.programsByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              )}
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Tabela de Programas Recentes */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-white">Programas Recentes</h3>
        </div>
        <div className="p-6">
          <ModuleTable
            columns={[
              { key: "learnerName", header: "Paciente" },
              { key: "skillName", header: "Habilidade" },
              { key: "status", header: "Status" },
              { key: "progress", header: "Progresso" },
              { key: "lastUpdate", header: "Última Atualização" }
            ]}
            data={sampleData.recentPrograms}
            isLoading={isLoading}
            emptyMessage="Nenhum programa encontrado"
            emptyIcon={<BookOpen size={24} />}
            tableId="recent-programs-table"
            defaultSortField="lastUpdate"
            defaultSortDirection="desc"
            renderRow={(program, _index, moduleColors, visibleColumns) => (
              <tr key={program.id} className={moduleColors.hoverBg}>
                {visibleColumns.includes("learnerName") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10 bg-teal-100 dark:bg-teal-900 rounded-full flex items-center justify-center text-teal-600 dark:text-teal-400">
                        {program.learnerName.charAt(0)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {program.learnerName}
                        </div>
                      </div>
                    </div>
                  </td>
                )}
                {visibleColumns.includes("skillName") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">{program.skillName}</div>
                  </td>
                )}
                {visibleColumns.includes("status") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        program.status === "Em Andamento"
                          ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                          : program.status === "Concluído"
                          ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
                          : program.status === "Não Iniciado"
                          ? "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300"
                          : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"
                      }`}
                    >
                      {program.status}
                    </span>
                  </td>
                )}
                {visibleColumns.includes("progress") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                      <div
                        className={`h-2.5 rounded-full ${
                          program.progress >= 80
                            ? "bg-green-500"
                            : program.progress >= 40
                            ? "bg-blue-500"
                            : "bg-yellow-500"
                        }`}
                        style={{ width: `${program.progress}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                      {program.progress}%
                    </div>
                  </td>
                )}
                {visibleColumns.includes("lastUpdate") && (
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(program.lastUpdate)}
                  </td>
                )}
              </tr>
            )}
            moduleColor="abaplus"
          />
        </div>
      </div>
    </div>
  );
};

export default ABAPlusDashboard;
