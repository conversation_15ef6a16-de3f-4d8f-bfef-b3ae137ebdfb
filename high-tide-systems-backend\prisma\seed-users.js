const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Iniciando seed de usuários...');

  // Senha padrão para todos os usuários: 123456 (hash pré-gerado)
  const defaultPassword = '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm';

  // Buscar todas as empresas
  const companies = await prisma.company.findMany({
    where: {
      NOT: {
        id: '00000000-0000-0000-0000-000000000001' // Excluir a empresa de teste
      }
    },
    include: {
      branches: true
    }
  });

  console.log(`Encontradas ${companies.length} empresas para criar usuários`);

  // Definição de módulos e permissões por tipo de profissão
  const professionModules = {
    // Área médica
    'Médico': {
      modules: ['BASIC', 'SCHEDULING'],
      permissions: ['view_appointments', 'view_patients', 'create_medical_records', 'edit_medical_records']
    },
    'Dentista': {
      modules: ['BASIC', 'SCHEDULING'],
      permissions: ['view_appointments', 'view_patients', 'create_medical_records', 'edit_medical_records']
    },
    'Enfermeiro': {
      modules: ['BASIC', 'SCHEDULING'],
      permissions: ['view_appointments', 'view_patients', 'create_medical_records', 'edit_medical_records', 'manage_supplies']
    },
    'Fisioterapeuta': {
      modules: ['BASIC', 'SCHEDULING'],
      permissions: ['view_appointments', 'view_patients', 'create_medical_records', 'edit_medical_records']
    },
    'Psicólogo': {
      modules: ['BASIC', 'SCHEDULING'],
      permissions: ['view_appointments', 'view_patients', 'create_medical_records', 'edit_medical_records']
    },

    // Área administrativa
    'Recepcionista': {
      modules: ['BASIC', 'SCHEDULING', 'ADMIN'],
      permissions: ['view_appointments', 'create_appointments', 'edit_appointments', 'view_patients', 'create_patients', 'edit_patients']
    },
    'Secretário(a)': {
      modules: ['BASIC', 'SCHEDULING', 'ADMIN'],
      permissions: ['view_appointments', 'create_appointments', 'edit_appointments', 'view_patients', 'create_patients', 'edit_patients', 'view_reports']
    },
    'Assistente Administrativo': {
      modules: ['BASIC', 'ADMIN'],
      permissions: ['view_patients', 'create_patients', 'edit_patients', 'view_reports']
    },

    // Área financeira
    'Contador': {
      modules: ['BASIC', 'FINANCIAL'],
      permissions: ['view_financial_reports', 'create_financial_entries', 'edit_financial_entries', 'view_reports']
    },
    'Analista Financeiro': {
      modules: ['BASIC', 'FINANCIAL'],
      permissions: ['view_financial_reports', 'create_financial_entries', 'edit_financial_entries', 'view_reports']
    },
    'Tesoureiro': {
      modules: ['BASIC', 'FINANCIAL'],
      permissions: ['view_financial_reports', 'create_financial_entries', 'edit_financial_entries', 'approve_payments']
    },

    // Área de RH
    'Analista de RH': {
      modules: ['BASIC', 'RH', 'ADMIN'],
      permissions: ['view_employees', 'create_employees', 'edit_employees', 'view_reports']
    },
    'Recrutador': {
      modules: ['BASIC', 'RH'],
      permissions: ['view_employees', 'create_employees', 'view_reports']
    },
    'Gestor de Treinamento': {
      modules: ['BASIC', 'RH'],
      permissions: ['view_employees', 'view_reports', 'create_training', 'edit_training']
    },

    // Área de TI
    'Analista de Sistemas': {
      modules: ['BASIC', 'ADMIN'],
      permissions: ['view_system_logs', 'view_reports', 'manage_system_settings']
    },
    'Suporte Técnico': {
      modules: ['BASIC', 'ADMIN'],
      permissions: ['view_system_logs', 'view_reports']
    }
  };

  // Nomes e sobrenomes para gerar nomes aleatórios
  const firstNames = [
    'Ana', 'João', 'Maria', 'Pedro', 'Carla', 'Lucas', 'Juliana', 'Rafael', 'Fernanda', 'Bruno',
    'Mariana', 'Carlos', 'Patrícia', 'Ricardo', 'Camila', 'Gustavo', 'Aline', 'Rodrigo', 'Daniela', 'Felipe',
    'Beatriz', 'André', 'Larissa', 'Marcelo', 'Natália', 'Thiago', 'Amanda', 'Eduardo', 'Letícia', 'Vinícius'
  ];

  const lastNames = [
    'Silva', 'Santos', 'Oliveira', 'Souza', 'Pereira', 'Costa', 'Rodrigues', 'Almeida', 'Nascimento', 'Lima',
    'Araújo', 'Fernandes', 'Carvalho', 'Gomes', 'Martins', 'Rocha', 'Ribeiro', 'Alves', 'Monteiro', 'Mendes',
    'Barros', 'Freitas', 'Barbosa', 'Pinto', 'Moura', 'Cavalcanti', 'Dias', 'Castro', 'Campos', 'Cardoso'
  ];

  // Função para gerar um nome aleatório
  const getRandomName = () => {
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    return `${firstName} ${lastName}`;
  };

  // Função para gerar um email a partir do nome
  const generateEmail = (name, domain) => {
    const normalizedName = name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/\s+/g, '.');
    return `${normalizedName}@${domain}`;
  };

  // Função para gerar um login a partir do nome
  const generateLogin = (name) => {
    const parts = name.toLowerCase().split(' ');
    const firstName = parts[0];
    const lastName = parts[parts.length - 1];
    return `${firstName}.${lastName}`;
  };

  // Função para gerar um número de telefone aleatório
  const generatePhone = () => {
    const ddd = Math.floor(Math.random() * 89) + 11; // DDD entre 11 e 99
    const part1 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos
    const part2 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos
    return `(${ddd}) 9${part1}-${part2}`;
  };

  console.log(`Encontradas ${companies.length} empresas: ${companies.map(c => c.name).join(', ')}`);

  // Para cada empresa, criar usuários com diferentes profissões
  for (const company of companies) {
    console.log(`\nCriando usuários para a empresa: ${company.name} (ID: ${company.id})`);

    // Obter o domínio de email da empresa
    const domain = company.website ? company.website.replace('www.', '') : 'empresa.com.br';

    // Buscar todas as profissões desta empresa
    const professions = await prisma.profession.findMany({
      where: {
        companyId: company.id,
        active: true
      },
      include: {
        group: true
      }
    });

    if (professions.length === 0) {
      console.log(`Nenhuma profissão encontrada para a empresa ${company.name}. Pulando...`);
      continue;
    }

    console.log(`Encontradas ${professions.length} profissões para a empresa ${company.name}`);

    // Para cada unidade da empresa, criar alguns usuários
    for (const branch of company.branches) {
      console.log(`\nCriando usuários para a unidade: ${branch.name}`);

      // Selecionar algumas profissões aleatoriamente para esta unidade
      const selectedProfessions = professions
        .sort(() => 0.5 - Math.random()) // Embaralhar array
        .slice(0, Math.min(professions.length, 10)); // Pegar até 10 profissões

      // Para cada profissão selecionada, criar 1-3 usuários
      for (const profession of selectedProfessions) {
        // Determinar quantos usuários criar para esta profissão (1-3)
        const numUsers = Math.floor(Math.random() * 3) + 1;

        // Obter os módulos e permissões para esta profissão
        const professionType = profession.name.split(' ')[0]; // Pegar o primeiro nome (ex: "Médico" de "Médico Cardiologista")
        const moduleConfig = professionModules[professionType] || {
          modules: ['BASIC'],
          permissions: []
        };

        console.log(`Criando ${numUsers} usuários para a profissão: ${profession.name}`);

        // Criar os usuários
        for (let i = 0; i < numUsers; i++) {
          const fullName = getRandomName();
          const email = generateEmail(fullName, domain);
          const login = generateLogin(fullName);
          const phone = generatePhone();

          try {
            // Verificar se o usuário já existe pelo email ou login
            const existingUser = await prisma.user.findFirst({
              where: {
                OR: [
                  { email },
                  { login }
                ]
              }
            });

            if (existingUser) {
              console.log(`Usuário com email ${email} ou login ${login} já existe. Pulando...`);
              continue;
            }

            // Criar o usuário
            const user = await prisma.user.create({
              data: {
                fullName,
                email,
                login,
                password: defaultPassword,
                phone,
                role: 'EMPLOYEE',
                active: true,
                modules: moduleConfig.modules,
                permissions: moduleConfig.permissions,
                company: {
                  connect: { id: company.id }
                },
                professionObj: {
                  connect: { id: profession.id }
                }
              }
            });

            console.log(`Usuário criado: ${user.fullName} (${user.email}) - ${profession.name}`);
          } catch (error) {
            console.error(`Erro ao criar usuário ${fullName}:`, error);
          }
        }
      }
    }
  }

  console.log('\nSeed de usuários concluído com sucesso!');
}

main()
  .catch((e) => {
    console.error('Erro durante o seed de usuários:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
