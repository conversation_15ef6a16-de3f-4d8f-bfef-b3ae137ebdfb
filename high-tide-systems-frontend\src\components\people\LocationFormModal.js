"use client";

import React, { useState, useEffect } from "react";
import { MapPin, Phone, Building, Check } from "lucide-react";
import ModuleModal from "@/components/ui/ModuleModal";
import ModalButton from "@/components/ui/ModalButton";
import { ModuleInput, ModuleSelect, ModuleFormGroup } from "@/components/ui";
import { locationService } from "@/app/modules/scheduler/services/locationService";
import { branchService } from "@/app/modules/admin/services/branchService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import MaskedInput from "@/components/common/MaskedInput";

const LocationFormModal = ({ isOpen, onClose, location = null, onSuccess }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    address: "",
    phone: "",
    branchId: "",
    companyId: "",
  });

  const [branchOptions, setBranchOptions] = useState([]);
  const [companyOptions, setCompanyOptions] = useState([]);
  const [loading, setLoading] = useState({ branches: false, companies: false });
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isSystemAdmin = user?.role === "SYSTEM_ADMIN";

  // Carregar empresas e unidades ao abrir o modal
  useEffect(() => {
    if (isOpen) {
      // Definir a companyId para usuários não-admin
      if (!isSystemAdmin && user?.companyId) {
        setFormData((prev) => ({ ...prev, companyId: user.companyId }));
        loadBranches(user.companyId);
      } else if (isSystemAdmin) {
        // Apenas carrega empresas para administradores do sistema
        loadCompanies();
      }

      if (location) {
        setFormData({
          name: location.name || "",
          address: location.address || "",
          phone: location.phone || "",
          branchId: location.branchId || "",
          // Se não for admin, sempre usa a empresa do usuário
          companyId: !isSystemAdmin
            ? user?.companyId
            : location.companyId || "",
        });

        // Se tiver uma empresa definida, carrega as unidades correspondentes
        if (location.companyId) {
          loadBranches(location.companyId);
        }
      } else {
        // Reset do formulário para nova localização
        resetForm();
      }
    }
  }, [isOpen, location, user]);

  const resetForm = () => {
    setFormData({
      name: "",
      address: "",
      phone: "",
      branchId: "",
      // Se não for admin, sempre usa a empresa do usuário
      companyId: !isSystemAdmin ? user?.companyId : "",
    });
    setError(null);
  };

  const loadCompanies = async () => {
    // Apenas administradores do sistema podem carregar empresas
    if (!isSystemAdmin) return;

    setLoading((prev) => ({ ...prev, companies: true }));
    try {
      const response = await companyService.getCompanies({
        active: true,
        limit: 100,
      });

      setCompanyOptions(response.companies || []);
    } catch (err) {
      console.error("Erro ao carregar empresas:", err);
    } finally {
      setLoading((prev) => ({ ...prev, companies: false }));
    }
  };

  const loadBranches = async (companyId) => {
    if (!companyId) return;

    setLoading((prev) => ({ ...prev, branches: true }));
    try {
      const response = await branchService.getBranches({
        companyId,
        active: true,
        limit: 100,
      });

      setBranchOptions(response.branches || []);
    } catch (err) {
      console.error("Erro ao carregar unidades:", err);
    } finally {
      setLoading((prev) => ({ ...prev, branches: false }));
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Se um branch for selecionado, encontrar e configurar o company correspondente
    if (name === "branchId" && value) {
      const selectedBranch = branchOptions.find(
        (branch) => branch.id === value
      );
      if (selectedBranch && isSystemAdmin) {
        // Apenas admin pode mudar a empresa
        setFormData((prev) => ({
          ...prev,
          [name]: value,
          companyId: selectedBranch.companyId,
        }));
        return;
      }
    }

    // Se uma empresa for selecionada, carregar as unidades dessa empresa
    if (name === "companyId" && value && isSystemAdmin) {
      // Apenas admin pode mudar a empresa
      loadBranches(value);
      // Resetar o branchId quando a empresa muda
      setFormData((prev) => ({
        ...prev,
        [name]: value,
        branchId: "",
      }));
      return;
    }

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = () => {
    // Validação básica
    if (!formData.name) return "Nome da localização é obrigatório";
    if (!formData.address) return "Endereço é obrigatório";
    if (!formData.companyId) return "Empresa é obrigatória";

    return null; // Sem erros
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validar formulário
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Preparar dados
      const payload = {
        name: formData.name,
        address: formData.address,
        phone: formData.phone ? formData.phone.replace(/\D/g, "") : undefined,
        branchId: formData.branchId || undefined,
        // Garantir que sempre tenha uma empresa
        companyId: formData.companyId || user?.companyId,
      };

      if (location) {
        // Atualizar localização existente
        await locationService.updateLocation(location.id, payload);
      } else {
        // Criar nova localização
        await locationService.createLocation(payload);
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar localização:", err);
      setError(
        err.response?.data?.message ||
          err.message ||
          "Ocorreu um erro ao salvar a localização."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // CSS classes
  const inputClasses =
    "block w-full px-3 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg focus:ring-primary-500 focus:border-primary-500 bg-white dark:bg-gray-700 text-neutral-900 dark:text-gray-100";
  const labelClasses = "block text-sm font-medium text-neutral-700 dark:text-gray-300 mb-1";
  const errorClasses = "mt-1 text-xs text-red-600 dark:text-red-400";

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="scheduler"
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="scheduler"
        type="submit"
        form="location-form"
        isLoading={isSubmitting}
      >
        {location ? "Atualizar" : "Salvar"}
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={location ? "Editar Localização" : "Nova Localização"}
      icon={<MapPin size={22} />}
      moduleColor="scheduler"
      size="md"
      footer={modalFooter}
    >

        <form id="location-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6">
          {error && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg">
              {error}
            </div>
          )}

          <div className="space-y-6">
            {/* Informações Básicas */}
            <div>
              <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Informações da Localização
              </h4>
            </div>

            {/* Nome */}
            <ModuleFormGroup
              moduleColor="scheduler"
              label="Nome *"
              htmlFor="name"
              icon={<MapPin size={16} />}
            >
              <ModuleInput
                moduleColor="scheduler"
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
                placeholder="Digite o nome da localização"
              />
            </ModuleFormGroup>

            {/* Endereço */}
            <ModuleFormGroup
              moduleColor="scheduler"
              label="Endereço *"
              htmlFor="address"
              icon={<MapPin size={16} />}
            >
              <ModuleInput
                moduleColor="scheduler"
                type="text"
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                required
                placeholder="Digite o endereço completo"
              />
            </ModuleFormGroup>

            {/* Telefone */}
            <ModuleFormGroup
              moduleColor="scheduler"
              label="Telefone"
              htmlFor="phone"
              icon={<Phone size={16} />}
            >
              <ModuleInput
                moduleColor="scheduler"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={(e) =>
                  handleChange({
                    target: { name: "phone", value: e.target.value },
                  })
                }
                placeholder="(00) 00000-0000"
                leftIcon={<Phone size={16} />}
              />
            </ModuleFormGroup>

            {/* Relacionamentos */}
            <div>
              <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2">
                <Building className="w-4 h-4" />
                Vínculo Empresarial
              </h4>
            </div>

            {/* Empresa (apenas para admin do sistema) */}
            {isSystemAdmin && (
              <ModuleFormGroup
                moduleColor="scheduler"
                label="Empresa *"
                htmlFor="companyId"
                icon={<Building size={16} />}
              >
                <ModuleSelect
                  moduleColor="scheduler"
                  id="companyId"
                  name="companyId"
                  value={formData.companyId}
                  onChange={handleChange}
                  required
                  disabled={loading.companies}
                  placeholder="Selecione uma empresa"
                >
                  <option value="">Selecione uma empresa</option>
                  {loading.companies ? (
                    <option value="" disabled>
                      Carregando empresas...
                    </option>
                  ) : (
                    companyOptions.map((company) => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))
                  )}
                </ModuleSelect>
              </ModuleFormGroup>
            )}

            {/* Informação da empresa para usuários não-admin */}
            {!isSystemAdmin && user?.companyId && (
              <div className="space-y-2">
                <label className={labelClasses}>Empresa</label>
                <div className="px-3 py-2 border border-neutral-200 dark:border-gray-700 bg-neutral-50 dark:bg-gray-900 rounded-lg text-neutral-700 dark:text-gray-300">
                  <div className="flex items-center">
                    <Building className="h-4 w-4 mr-2 text-neutral-500 dark:text-gray-400" />
                    <span>{user?.companyName || "Sua empresa"}</span>
                  </div>
                </div>
                <input type="hidden" name="companyId" value={user.companyId} />
                <p className="text-xs text-neutral-500 dark:text-gray-400">
                  A localização será associada à sua empresa
                </p>
              </div>
            )}

            {/* Unidade */}
            <ModuleFormGroup
              moduleColor="scheduler"
              label="Unidade (opcional)"
              htmlFor="branchId"
              icon={<Building size={16} />}
            >
              <ModuleSelect
                moduleColor="scheduler"
                id="branchId"
                name="branchId"
                value={formData.branchId}
                onChange={handleChange}
                disabled={loading.branches || !formData.companyId}
                placeholder="Selecione uma unidade"
              >
                <option value="">Selecione uma unidade</option>
                {loading.branches ? (
                  <option value="" disabled>
                    Carregando unidades...
                  </option>
                ) : (
                  branchOptions.map((branch) => (
                    <option key={branch.id} value={branch.id}>
                      {branch.name} {branch.code && `(${branch.code})`}
                    </option>
                  ))
                )}
              </ModuleSelect>
            </ModuleFormGroup>
          </div>

        </form>
    </ModuleModal>
  );
};

export default LocationFormModal;