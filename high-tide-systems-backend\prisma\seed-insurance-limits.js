// prisma/seed-insurance-limits.js
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');
const prisma = new PrismaClient();

// Função para gerar um número aleatório entre min e max (inclusive)
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Função para embaralhar um array
function shuffleArray(array) {
  return [...array].sort(() => 0.5 - Math.random());
}

// Função principal
async function main() {
  console.log('Iniciando seed de limites de convênio...');
  
  try {
    // Buscar todas as pessoas que têm convênios associados
    const personsWithInsurances = await prisma.personInsurance.findMany({
      include: {
        person: true,
        insurance: {
          include: {
            company: true
          }
        }
      }
    });
    
    console.log(`Encontradas ${personsWithInsurances.length} associações pessoa-convênio para adicionar limites`);
    
    // Agrupar por pessoa para facilitar o processamento
    const personInsuranceMap = {};
    
    personsWithInsurances.forEach(pi => {
      if (!personInsuranceMap[pi.personId]) {
        personInsuranceMap[pi.personId] = [];
      }
      personInsuranceMap[pi.personId].push(pi);
    });
    
    // Para cada pessoa com convênios
    for (const personId in personInsuranceMap) {
      const personInsurances = personInsuranceMap[personId];
      const person = personInsurances[0].person;
      
      console.log(`\nProcessando pessoa: ${person.fullName} (ID: ${person.id})`);
      console.log(`Possui ${personInsurances.length} convênios associados`);
      
      // Para cada convênio da pessoa
      for (const personInsurance of personInsurances) {
        const insurance = personInsurance.insurance;
        
        console.log(`Processando convênio: ${insurance.name} (ID: ${insurance.id})`);
        
        // Determinar se esta pessoa terá limites para este convênio (80% de chance)
        if (Math.random() < 0.8) {
          // Buscar tipos de serviço disponíveis para a empresa do convênio
          const serviceTypes = await prisma.serviceType.findMany({
            where: {
              companyId: insurance.companyId
            }
          });
          
          if (serviceTypes.length === 0) {
            console.log(`Nenhum tipo de serviço encontrado para a empresa do convênio. Pulando...`);
            continue;
          }
          
          console.log(`Encontrados ${serviceTypes.length} tipos de serviço para a empresa do convênio`);
          
          // Determinar quantos tipos de serviço terão limites (entre 1 e o total disponível, com maior probabilidade para valores menores)
          const numServiceTypes = Math.min(
            Math.floor(Math.random() * 0.7 * serviceTypes.length) + 1,
            serviceTypes.length
          );
          
          console.log(`Criando limites para ${numServiceTypes} tipos de serviço`);
          
          // Selecionar aleatoriamente os tipos de serviço
          const selectedServiceTypes = shuffleArray(serviceTypes).slice(0, numServiceTypes);
          
          // Criar limites para cada tipo de serviço selecionado
          for (const serviceType of selectedServiceTypes) {
            try {
              // Verificar se já existe um limite para esta combinação
              const existingLimit = await prisma.personInsuranceServiceLimit.findFirst({
                where: {
                  personId: person.id,
                  insuranceId: insurance.id,
                  serviceTypeId: serviceType.id
                }
              });
              
              if (existingLimit) {
                console.log(`Limite já existe para o serviço ${serviceType.name}. Pulando...`);
                continue;
              }
              
              // Determinar o limite mensal (entre 1 e 20, com maior probabilidade entre 4 e 12)
              let monthlyLimit;
              const rand = Math.random();
              if (rand < 0.1) {
                // 10% de chance para 1-3
                monthlyLimit = getRandomInt(1, 3);
              } else if (rand < 0.8) {
                // 70% de chance para 4-12
                monthlyLimit = getRandomInt(4, 12);
              } else {
                // 20% de chance para 13-20
                monthlyLimit = getRandomInt(13, 20);
              }
              
              // Criar o limite
              const limit = await prisma.personInsuranceServiceLimit.create({
                data: {
                  id: crypto.randomUUID(), // Gerar um UUID para o ID
                  personId: person.id,
                  insuranceId: insurance.id,
                  serviceTypeId: serviceType.id,
                  monthlyLimit,
                  yearlyLimit: monthlyLimit * 12, // Limite anual como 12x o mensal
                  notes: `Limite gerado automaticamente para ${person.fullName}`
                }
              });
              
              console.log(`✅ Limite criado: ${serviceType.name} - ${monthlyLimit} sessões mensais`);
            } catch (error) {
              console.error(`Erro ao criar limite para o serviço ${serviceType.name}:`, error);
            }
          }
        } else {
          console.log(`Pessoa não terá limites para este convênio (aleatório)`);
        }
      }
    }
    
    console.log('\nSeed de limites de convênio concluído com sucesso!');
  } catch (error) {
    console.error('Erro durante o seed de limites de convênio:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
