// test-cache-middleware.js
require('dotenv').config();
const axios = require('axios');
const cacheService = require('../../src/services/cacheService');

// Configurações
const API_URL = 'http://localhost:3000';
const API_TOKEN = process.env.TEST_TOKEN; // Token JWT para autenticação

// Função para obter um token de autenticação
async function getAuthToken() {
  try {
    // Se já temos um token de teste no .env, usá-lo
    if (API_TOKEN) {
      return API_TOKEN;
    }

    // <PERSON><PERSON><PERSON> contrá<PERSON>, fazer login para obter um token
    const response = await axios.post(`${API_URL}/api/auth/login`, {
      email: '<EMAIL>',
      password: 'password123'
    });

    return response.data.token;
  } catch (error) {
    console.error('Erro ao obter token de autenticação:', error.message);
    throw error;
  }
}

// Função para medir o tempo de resposta
async function measureResponseTime(fn) {
  const start = Date.now();
  const result = await fn();
  const end = Date.now();
  return {
    result,
    time: end - start
  };
}

// Teste do middleware de cache na rota de tipos de serviço
async function testServiceTypeCache() {
  console.log('=== TESTE DO MIDDLEWARE DE CACHE ===\n');
  console.log('Testando cache na rota de tipos de serviço...\n');

  try {
    // Inicializar o serviço de cache
    await cacheService.initialize();

    // Obter token de autenticação
    const token = await getAuthToken();
    console.log('Token de autenticação obtido');

    // Configurar headers para as requisições
    const headers = {
      Authorization: `Bearer ${token}`
    };

    // Limpar qualquer cache existente para garantir um teste limpo
    await cacheService.clear('service-types:*');
    console.log('Cache de tipos de serviço limpo');

    // Primeira requisição - deve ser um cache miss
    console.log('\n1. Primeira requisição (deve ser um cache miss)...');
    const firstCall = await measureResponseTime(async () => {
      return await axios.get(`${API_URL}/api/service-types`, { headers });
    });

    console.log(`   Tempo de resposta: ${firstCall.time}ms`);
    console.log(`   Número de tipos de serviço: ${firstCall.result.data.length}`);

    // Segunda requisição - deve ser um cache hit
    console.log('\n2. Segunda requisição (deve ser um cache hit)...');
    const secondCall = await measureResponseTime(async () => {
      return await axios.get(`${API_URL}/api/service-types`, { headers });
    });

    console.log(`   Tempo de resposta: ${secondCall.time}ms`);
    console.log(`   Número de tipos de serviço: ${secondCall.result.data.length}`);

    // Verificar se a segunda chamada foi mais rápida (cache hit)
    if (secondCall.time < firstCall.time) {
      console.log('\n✅ Segunda chamada foi mais rápida que a primeira (cache hit)');
      console.log(`   Melhoria de desempenho: ${Math.round((1 - secondCall.time / firstCall.time) * 100)}%`);
    } else {
      console.log('\n❌ Segunda chamada não foi mais rápida que a primeira');
      console.log(`   Primeira chamada: ${firstCall.time}ms`);
      console.log(`   Segunda chamada: ${secondCall.time}ms`);
    }

    // Verificar se os dados são idênticos
    if (JSON.stringify(firstCall.result.data) === JSON.stringify(secondCall.result.data)) {
      console.log('\n✅ Os dados retornados são idênticos');
    } else {
      console.log('\n❌ Os dados retornados são diferentes');
    }

    // Criar um novo tipo de serviço para testar a invalidação de cache
    console.log('\n3. Criando um novo tipo de serviço para testar invalidação de cache...');
    const newServiceType = {
      name: `Teste Cache ${Date.now()}`,
      description: 'Tipo de serviço criado para teste de cache',
      price: 99.99,
      duration: 60
    };

    const createResponse = await axios.post(`${API_URL}/api/service-types`, newServiceType, { headers });
    console.log(`   Tipo de serviço criado com ID: ${createResponse.data.id}`);

    // Terceira requisição - deve ser um cache miss devido à invalidação
    console.log('\n4. Terceira requisição após criação (deve ser um cache miss)...');
    const thirdCall = await measureResponseTime(async () => {
      return await axios.get(`${API_URL}/api/service-types`, { headers });
    });

    console.log(`   Tempo de resposta: ${thirdCall.time}ms`);
    console.log(`   Número de tipos de serviço: ${thirdCall.result.data.length}`);

    // Verificar se o novo tipo de serviço está na lista
    const newServiceTypeInList = thirdCall.result.data.some(item => item.id === createResponse.data.id);
    if (newServiceTypeInList) {
      console.log('\n✅ O novo tipo de serviço está na lista (cache foi invalidado)');
    } else {
      console.log('\n❌ O novo tipo de serviço não está na lista (cache não foi invalidado)');
    }

    // Limpar o tipo de serviço criado para o teste
    console.log('\n5. Limpando o tipo de serviço criado para o teste...');
    await axios.delete(`${API_URL}/api/service-types/${createResponse.data.id}`, { headers });
    console.log(`   Tipo de serviço com ID ${createResponse.data.id} excluído`);

    // Fechar a conexão com o Redis
    await cacheService.close();
    console.log('\nConexão com Redis fechada.');

    console.log('\n=== TESTE CONCLUÍDO ===');

  } catch (error) {
    console.error('Erro durante o teste:', error.message);
    if (error.response) {
      console.error('Detalhes da resposta:', error.response.data);
    }

    // Fechar a conexão com o Redis
    await cacheService.close();
    process.exit(1);
  }
}

// Executar o teste
testServiceTypeCache().catch(error => {
  console.error('Erro fatal durante o teste:', error);
  process.exit(1);
});
