/**
 * Script para corrigir a importação e uso do middleware de autenticação em todos os arquivos de rotas
 * 
 * Este script:
 * 1. Substitui "const authMiddleware = require('../middlewares/auth');" por "const { authenticate } = require('../middlewares/auth');"
 * 2. Substitui "router.use(authMiddleware);" por "router.use(authenticate);"
 * 3. Substitui "authMiddleware," por "authenticate," em todas as rotas
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// Função para percorrer diretórios recursivamente
async function walkDir(dir) {
  const files = await readdir(dir);
  const result = [];

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stats = await stat(filePath);

    if (stats.isDirectory()) {
      const subDirFiles = await walkDir(filePath);
      result.push(...subDirFiles);
    } else if (file.endsWith('.js')) {
      result.push(filePath);
    }
  }

  return result;
}

// Função para corrigir um arquivo
async function fixFile(filePath) {
  try {
    let content = await readFile(filePath, 'utf8');
    let modified = false;

    // Verificar se o arquivo importa o middleware de autenticação
    if (content.includes('require') && content.includes('middlewares/auth')) {
      console.log(`Processando arquivo: ${filePath}`);

      // 1. Substituir a importação
      const importRegex = /const\s+authMiddleware\s*=\s*require\(['"](.+?)\/middlewares\/auth['"]\);/g;
      const newImport = (match, p1) => `const { authenticate } = require('${p1}/middlewares/auth');`;
      const newContent = content.replace(importRegex, newImport);
      
      if (newContent !== content) {
        content = newContent;
        modified = true;
        console.log(`  - Importação corrigida`);
      }

      // 2. Substituir o uso global
      const useRegex = /router\.use\(authMiddleware\);/g;
      const newUse = 'router.use(authenticate);';
      const afterUseReplace = content.replace(useRegex, newUse);
      
      if (afterUseReplace !== content) {
        content = afterUseReplace;
        modified = true;
        console.log(`  - Uso global corrigido`);
      }

      // 3. Substituir o uso em rotas individuais
      const routeRegex = /(\s*)(authMiddleware)(,)/g;
      const newRoute = '$1authenticate$3';
      const afterRouteReplace = content.replace(routeRegex, newRoute);
      
      if (afterRouteReplace !== content) {
        content = afterRouteReplace;
        modified = true;
        console.log(`  - Uso em rotas individuais corrigido`);
      }

      // Salvar o arquivo se foi modificado
      if (modified) {
        await writeFile(filePath, content, 'utf8');
        console.log(`  ✓ Arquivo salvo com sucesso`);
      } else {
        console.log(`  ✓ Nenhuma modificação necessária`);
      }
    }
  } catch (error) {
    console.error(`Erro ao processar o arquivo ${filePath}:`, error);
  }
}

// Função principal
async function main() {
  try {
    console.log('Iniciando correção do middleware de autenticação...');
    
    // Obter todos os arquivos de rotas
    const routesDir = path.join(__dirname, '../src/routes');
    const files = await walkDir(routesDir);
    
    console.log(`Encontrados ${files.length} arquivos JavaScript para verificar`);
    
    // Processar cada arquivo
    for (const file of files) {
      await fixFile(file);
    }
    
    console.log('\nCorreção concluída com sucesso!');
  } catch (error) {
    console.error('Erro durante o processo de correção:', error);
    process.exit(1);
  }
}

main();
