// src/controllers/aba/taskController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createTaskValidation = [
  body("order").isInt({ min: 1 }).withMessage("Ordem deve ser um número inteiro positivo"),
  body("name").notEmpty().withMessage("Nome da tarefa é obrigatório"),
  body("milestone").optional(),
  body("item").optional(),
  body("question").optional(),
  body("example").optional(),
  body("criteria").optional(),
  body("objective").optional(),
  body("evaluationId").notEmpty().withMessage("ID da avaliação é obrigatório"),
  body("skillId").optional(),
  body("levelId").optional(),
];

const updateTaskValidation = [
  body("order").optional().isInt({ min: 1 }).withMessage("Ordem deve ser um número inteiro positivo"),
  body("name").optional().notEmpty().withMessage("Nome da tarefa é obrigatório"),
  body("milestone").optional(),
  body("item").optional(),
  body("question").optional(),
  body("example").optional(),
  body("criteria").optional(),
  body("objective").optional(),
  body("skillId").optional(),
  body("levelId").optional(),
];

class TaskController {
  /**
   * Cria uma nova tarefa
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        order,
        name,
        milestone,
        item,
        question,
        example,
        criteria,
        objective,
        evaluationId,
        skillId,
        levelId,
      } = req.body;

      // Verificar se a avaliação existe e pertence à empresa do usuário
      const evaluation = await prisma.evaluation.findUnique({
        where: { id: evaluationId },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para adicionar tarefas a esta avaliação" });
      }

      // Verificar se a habilidade existe e pertence à mesma empresa
      if (skillId) {
        const skill = await prisma.skill.findUnique({
          where: { id: skillId },
        });

        if (!skill) {
          return res.status(404).json({ message: "Habilidade não encontrada" });
        }

        if (skill.companyId !== evaluation.companyId) {
          return res.status(400).json({ message: "A habilidade não pertence à mesma empresa da avaliação" });
        }
      }

      // Verificar se o nível existe e pertence à mesma avaliação
      if (levelId) {
        const level = await prisma.level.findUnique({
          where: { id: levelId },
        });

        if (!level) {
          return res.status(404).json({ message: "Nível não encontrado" });
        }

        if (level.evaluationId !== evaluationId) {
          return res.status(400).json({ message: "O nível não pertence a esta avaliação" });
        }
      }

      // Criar tarefa
      const task = await prisma.task.create({
        data: {
          order,
          name,
          milestone,
          item,
          question,
          example,
          criteria,
          objective,
          evaluationId,
          skillId: skillId || null,
          levelId: levelId || null,
        },
      });

      res.status(201).json(task);
    } catch (error) {
      console.error("Erro ao criar tarefa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todas as tarefas com filtros e paginação
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search = "",
        evaluationId,
        skillId,
        levelId,
      } = req.query;

      // Construir filtros
      const where = {};

      // Filtro por avaliação
      if (evaluationId) {
        where.evaluationId = evaluationId;

        // Verificar se a avaliação pertence à empresa do usuário
        const evaluation = await prisma.evaluation.findUnique({
          where: { id: evaluationId },
        });

        if (!evaluation) {
          return res.status(404).json({ message: "Avaliação não encontrada" });
        }

        if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
          return res.status(403).json({ message: "Você não tem permissão para visualizar tarefas desta avaliação" });
        }
      } else {
        // Se não for filtrado por avaliação, filtrar por empresa
        where.evaluation = {
          companyId: req.user.companyId,
          deletedAt: null,
        };
      }

      // Filtro por habilidade
      if (skillId) {
        where.skillId = skillId;
      }

      // Filtro por nível
      if (levelId) {
        where.levelId = levelId;
      }

      // Filtro de busca
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { milestone: { contains: search, mode: "insensitive" } },
          { item: { contains: search, mode: "insensitive" } },
          { question: { contains: search, mode: "insensitive" } },
        ];
      }

      // Contar total de registros
      const total = await prisma.task.count({ where });

      // Buscar tarefas com paginação
      const tasks = await prisma.task.findMany({
        where,
        include: {
          evaluation: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
          skill: {
            select: {
              id: true,
              code: true,
              description: true,
            },
          },
          level: {
            select: {
              id: true,
              order: true,
              description: true,
              ageRange: true,
            },
          },
        },
        orderBy: [
          { evaluationId: "asc" },
          { order: "asc" },
        ],
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar resposta
      const response = formatSuccessResponse(
        tasks,
        "tasks",
        total,
        Math.ceil(total / Number(limit))
      );

      res.json(response);
    } catch (error) {
      console.error("Erro ao listar tarefas:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém uma tarefa específica pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const task = await prisma.task.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              id: true,
              name: true,
              type: true,
              companyId: true,
            },
          },
          skill: {
            select: {
              id: true,
              code: true,
              description: true,
            },
          },
          level: {
            select: {
              id: true,
              order: true,
              description: true,
              ageRange: true,
            },
          },
        },
      });

      if (!task) {
        return res.status(404).json({ message: "Tarefa não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && task.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para visualizar esta tarefa" });
      }

      res.json(task);
    } catch (error) {
      console.error("Erro ao buscar tarefa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza uma tarefa existente
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Verificar se a tarefa existe
      const existingTask = await prisma.task.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              id: true,
              companyId: true,
            },
          },
        },
      });

      if (!existingTask) {
        return res.status(404).json({ message: "Tarefa não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingTask.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para atualizar esta tarefa" });
      }

      const {
        order,
        name,
        milestone,
        item,
        question,
        example,
        criteria,
        objective,
        skillId,
        levelId,
      } = req.body;

      // Verificar se a habilidade existe e pertence à mesma empresa
      if (skillId) {
        const skill = await prisma.skill.findUnique({
          where: { id: skillId },
        });

        if (!skill) {
          return res.status(404).json({ message: "Habilidade não encontrada" });
        }

        if (skill.companyId !== existingTask.evaluation.companyId) {
          return res.status(400).json({ message: "A habilidade não pertence à mesma empresa da avaliação" });
        }
      }

      // Verificar se o nível existe e pertence à mesma avaliação
      if (levelId) {
        const level = await prisma.level.findUnique({
          where: { id: levelId },
        });

        if (!level) {
          return res.status(404).json({ message: "Nível não encontrado" });
        }

        if (level.evaluationId !== existingTask.evaluation.id) {
          return res.status(400).json({ message: "O nível não pertence a esta avaliação" });
        }
      }

      // Atualizar tarefa
      const updatedTask = await prisma.task.update({
        where: { id },
        data: {
          order: order !== undefined ? order : undefined,
          name: name !== undefined ? name : undefined,
          milestone: milestone !== undefined ? milestone : undefined,
          item: item !== undefined ? item : undefined,
          question: question !== undefined ? question : undefined,
          example: example !== undefined ? example : undefined,
          criteria: criteria !== undefined ? criteria : undefined,
          objective: objective !== undefined ? objective : undefined,
          skillId: skillId !== undefined ? skillId : undefined,
          levelId: levelId !== undefined ? levelId : undefined,
          updatedAt: new Date(),
        },
      });

      res.json(updatedTask);
    } catch (error) {
      console.error("Erro ao atualizar tarefa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove uma tarefa
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a tarefa existe
      const task = await prisma.task.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              companyId: true,
            },
          },
        },
      });

      if (!task) {
        return res.status(404).json({ message: "Tarefa não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && task.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para remover esta tarefa" });
      }

      // Remover tarefa
      await prisma.task.delete({
        where: { id },
      });

      res.json({ message: "Tarefa removida com sucesso" });
    } catch (error) {
      console.error("Erro ao remover tarefa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  TaskController,
  createTaskValidation,
  updateTaskValidation,
};
