# Migração de Profissões

Este documento descreve o processo de migração da coluna `profession` do modelo `User` para o novo modelo `Profession`.

## Visão Geral

A migração consiste em duas etapas:

1. **Migração do Schema**: <PERSON><PERSON>r as novas tabelas `Profession` e `ProfessionGroup` e adicionar a coluna `professionId` ao modelo `User`.
2. **Migração de Dados**: Transferir os dados da coluna `profession` para o novo modelo `Profession` e atualizar as referências nos usuários.

## Instruções para Migração

### 1. Migração do Schema

Execute o comando do Prisma para aplicar a migração do schema:

```bash
npx prisma migrate dev --name add_profession_models
```

Este comando irá:
- <PERSON><PERSON>r as tabelas `Profession` e `ProfessionGroup`
- Adicionar a coluna `professionId` à tabela `User`
- <PERSON><PERSON><PERSON> as chaves estrangeiras e índices necessários

### 2. Migração de Dados

Após a migração do schema, execute o script para migrar os dados:

```bash
node scripts/migrate-professions.js
```

Este script irá:
- Buscar todos os usuários com profissão definida
- Agrupar usuários por profissão e empresa
- Criar as profissões no novo modelo
- Atualizar os usuários para usar a nova profissão

## Verificação da Migração

Após a migração, você pode verificar se os dados foram migrados corretamente executando as seguintes consultas:

```sql
-- Verificar profissões criadas
SELECT * FROM "Profession";

-- Verificar usuários com profissões
SELECT u.id, u.full_name, u.profession, u.profession_id, p.name as profession_name
FROM "User" u
LEFT JOIN "Profession" p ON u.profession_id = p.id
WHERE u.profession IS NOT NULL OR u.profession_id IS NOT NULL;
```

## Rollback

Se for necessário reverter a migração, você pode executar:

```bash
npx prisma migrate reset
```

**Atenção**: Este comando irá resetar todo o banco de dados e aplicar todas as migrações novamente. Use com cuidado em ambientes de produção.

## Notas Adicionais

- A coluna `profession` no modelo `User` não será removida imediatamente para garantir compatibilidade com código existente.
- Recomenda-se atualizar todo o código que usa a coluna `profession` para usar a nova relação `professionId` antes de remover a coluna.
- Uma futura migração será necessária para remover a coluna `profession` quando não for mais utilizada.
