"use client";

import React, { useState, useEffect } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleTable } from "@/components/ui";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Tag,
  DollarSign,
} from "lucide-react";
import { serviceTypeService } from "@/app/modules/scheduler/services/serviceTypeService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import ServiceTypeFormModal from "@/components/people/ServiceTypeFormModal";
import ExportMenu from "@/components/ui/ExportMenu";
import MultiSelect from "@/components/ui/multi-select";


// Tutorial steps para a página de tipos de serviço
const serviceTypeTutorialSteps = [
  {
    title: "Tipos de Serviço",
    content: "Esta tela permite gerenciar os tipos de serviço disponíveis para agendamentos no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Serviço",
    content: "Clique aqui para adicionar um novo tipo de serviço.",
    selector: "button:has(span:contains('Novo Serviço'))",
    position: "left"
  },
  {
    title: "Filtrar Tipos de Serviço",
    content: "Use esta barra de pesquisa para encontrar tipos de serviço específicos pelo nome.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de tipos de serviço em diferentes formatos usando este botão.",
    selector: "button:has(span:contains('Exportar'))",
    position: "left"
  },
  {
    title: "Gerenciar Tipos de Serviço",
    content: "Edite ou exclua tipos de serviço existentes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const ServiceTypePage = () => {
  const { user } = useAuth();
  const [serviceTypes, setServiceTypes] = useState([]);
  const [serviceTypeOptions, setServiceTypeOptions] = useState([]);
  const [companies, setCompanies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [selectedServiceTypeIds, setSelectedServiceTypeIds] = useState([]);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedServiceType, setSelectedServiceType] = useState(null);
  const [serviceTypeFormOpen, setServiceTypeFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [isLoadingOptions, setIsLoadingOptions] = useState(false);

  const isSystemAdmin = user?.role === "SYSTEM_ADMIN";

  const loadServiceTypes = async (searchQuery = search, serviceTypeIds = selectedServiceTypeIds) => {
    setIsLoading(true);
    try {
      const params = {
        search: searchQuery || undefined,
        companyId: !isSystemAdmin ? user?.companyId : undefined,
        serviceTypeIds: serviceTypeIds.length > 0 ? serviceTypeIds : undefined,
      };

      const response = await serviceTypeService.getServiceTypes(params);
      setServiceTypes(response.serviceTypes || []);
    } catch (error) {
      console.error("Erro ao carregar tipos de serviço:", error);
      setServiceTypes([]);
    } finally {
      setIsLoading(false);
    }
  };

  const loadServiceTypeOptions = async () => {
    setIsLoadingOptions(true);
    try {
      const params = {
        companyId: !isSystemAdmin ? user?.companyId : undefined,
      };

      const response = await serviceTypeService.getServiceTypes(params);
      const options = (response.serviceTypes || []).map(serviceType => ({
        value: serviceType.id,
        label: serviceType.name
      }));
      setServiceTypeOptions(options);
    } catch (error) {
      console.error("Erro ao carregar opções de tipos de serviço:", error);
      setServiceTypeOptions([]);
    } finally {
      setIsLoadingOptions(false);
    }
  };

  const loadCompanies = async () => {
    try {
      const response = await companyService.getCompanies({ limit: 100 });
      setCompanies(response.companies || []);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    }
  };

  useEffect(() => {
    loadServiceTypes();
    loadServiceTypeOptions();
    if (isSystemAdmin) {
      loadCompanies();
    }
  }, []);

  const handleSearch = (e) => {
    e.preventDefault();
    loadServiceTypes(search, selectedServiceTypeIds);
  };

  const handleServiceTypeFilterChange = (selected) => {
    setSelectedServiceTypeIds(selected);
    loadServiceTypes(search, selected);
  };

  const handleResetFilters = () => {
    setSearch("");
    setSelectedServiceTypeIds([]);
    loadServiceTypes("", []);
  };

  const handleEditServiceType = (serviceType) => {
    setSelectedServiceType(serviceType);
    setServiceTypeFormOpen(true);
  };

  const handleDeleteServiceType = (serviceType) => {
    setSelectedServiceType(serviceType);
    setConfirmationDialogOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Exportar usando os mesmos filtros da tabela atual
      await serviceTypeService.exportServiceTypes({
        search: search || undefined,
        companyId: !isSystemAdmin ? user?.companyId : undefined,
        serviceTypeIds: selectedServiceTypeIds.length > 0 ? selectedServiceTypeIds : undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar tipos de serviço:", error);
      // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast
    } finally {
      setIsExporting(false);
    }
  };

  const confirmDeleteServiceType = async () => {
    try {
      await serviceTypeService.deleteServiceType(selectedServiceType.id);
      loadServiceTypes();
      setConfirmationDialogOpen(false);
    } catch (error) {
      console.error("Erro ao excluir tipo de serviço:", error);
    }
  };

  // Função para formatar valores monetários
  const formatCurrency = (value) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  };

  return (
    <div className="space-y-6">
      {/* Título e botões de ação */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Tag size={24} className="mr-2 text-purple-600 dark:text-purple-400" />
          Tipos de Serviço
        </h1>

        <div className="flex items-center gap-2">
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || serviceTypes.length === 0}
            className="text-purple-600 dark:text-purple-400"
          />

          <button
            onClick={() => {
              setSelectedServiceType(null);
              setServiceTypeFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white rounded-lg hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700 shadow-md transition-all"
          >
            <Plus size={18} />
            <span className="font-medium">Novo Serviço</span>
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />}
        description="Gerencie os tipos de serviço disponíveis para agendamentos no sistema."
        tutorialSteps={serviceTypeTutorialSteps}
        tutorialName="service-type-overview"
        moduleColor="scheduler"
        filters={
          <form
            onSubmit={handleSearch}
            className="flex flex-col gap-4"
          >
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Buscar por nome do serviço..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <FilterButton type="submit" moduleColor="scheduler" variant="primary">
                  <Filter size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Filtrar</span>
                </FilterButton>

                <FilterButton
                  type="button"
                  onClick={handleResetFilters}
                  moduleColor="scheduler"
                  variant="secondary"
                >
                  <RefreshCw size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Limpar</span>
                </FilterButton>
              </div>
            </div>

            {/* Multi-select para filtrar por múltiplos tipos de serviço */}
            <div className="w-full">
              <MultiSelect
                label="Filtrar por Nome do Serviço"
                value={selectedServiceTypeIds}
                onChange={handleServiceTypeFilterChange}
                options={serviceTypeOptions}
                placeholder="Selecione um ou mais tipos de serviço..."
                loading={isLoadingOptions}
                moduleOverride="scheduler"
              />
            </div>
          </form>
        }
      />

      {/* Tabela de Tipos de Serviço */}
      <ModuleTable
        moduleColor="scheduler"
        columns={[
          { header: 'Nome do Serviço', field: 'name', width: '40%' },
          { header: 'Valor', field: 'value', width: '20%', className: 'text-right', dataType: 'number' },
          ...(isSystemAdmin ? [{ header: 'Empresa', field: 'company', width: '25%' }] : []),
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={serviceTypes}
        isLoading={isLoading}
        emptyMessage="Nenhum tipo de serviço encontrado"
        emptyIcon={<Tag size={24} />}
        tableId="scheduler-service-types-table"
        enableColumnToggle={true}
        defaultSortField="name"
        defaultSortDirection="asc"
        renderRow={(serviceType, index, moduleColors, visibleColumns) => (
          <tr key={serviceType.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('name') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 bg-primary-100 dark:bg-primary-900/30 text-primary-600 dark:text-primary-400 rounded-full flex items-center justify-center">
                    <Tag size={20} />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                      {serviceType.name}
                    </div>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('value') && (
              <td className="px-6 py-4 whitespace-nowrap text-right">
                <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                  {formatCurrency(serviceType.value)}
                </div>
              </td>
            )}

            {isSystemAdmin && visibleColumns.includes('company') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-neutral-600 dark:text-neutral-300">
                  {serviceType.company?.name || "N/A"}
                </div>
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => handleEditServiceType(serviceType)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={18} />
                  </button>
                  <button
                    onClick={() => handleDeleteServiceType(serviceType)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={18} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmDeleteServiceType}
        title="Excluir Tipo de Serviço"
        message={`Tem certeza que deseja excluir o tipo de serviço "${selectedServiceType?.name}"? Esta ação não pode ser desfeita.`}
        variant="danger"
        confirmText="Excluir"
        cancelText="Cancelar"
      />

      {/* Service Type Form Modal */}
      {serviceTypeFormOpen && (
        <ServiceTypeFormModal
          isOpen={serviceTypeFormOpen}
          onClose={() => setServiceTypeFormOpen(false)}
          serviceType={selectedServiceType}
          onSuccess={() => {
            setServiceTypeFormOpen(false);
            loadServiceTypes();
          }}
          companies={companies}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default ServiceTypePage;