const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { InsuranceController, createInsuranceValidation } = require('../../controllers/insuranceController');

// Rotas protegidas
router.use(authenticate);

// Rotas de Insurance
router.post('/', createInsuranceValidation, InsuranceController.create);
router.get('/', InsuranceController.list);
router.get('/:id', InsuranceController.get);
router.put('/:id', createInsuranceValidation, InsuranceController.update);
router.delete('/:id', InsuranceController.delete);

// Rotas de PersonInsurance
router.post('/person', InsuranceController.addPersonInsurance);
router.delete('/person/:personId/:insuranceId', InsuranceController.removePersonInsurance);
router.get('/person/:personId', InsuranceController.listPersonInsurances);
router.put('/person/:personId/:insuranceId', InsuranceController.updatePersonInsurance);

module.exports = router;