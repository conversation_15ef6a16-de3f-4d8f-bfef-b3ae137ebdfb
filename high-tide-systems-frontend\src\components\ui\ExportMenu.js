"use client";

import React, { useState, useRef, useEffect } from "react";
import { createPortal } from 'react-dom';
import { Download, FileText, FileSpreadsheet, ChevronDown, Loader2, Construction, Image } from "lucide-react";
import { useConstructionMessage } from '@/hooks/useConstructionMessage';
import { ConstructionButton } from '@/components/construction';

const ExportMenu = ({ onExport, isExporting = false, disabled = false, underConstruction = false, className = '' }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, right: 0, width: 0 });
  const buttonRef = useRef(null);
  const dropdownRef = useRef(null);

  // Montar o componente apenas no cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Calcular a posição do dropdown quando aberto
  useEffect(() => {
    if (dropdownOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + window.scrollY,
        right: window.innerWidth - rect.right,
        width: Math.max(rect.width, 192) // Mínimo de 192px (w-48)
      });
    }
  }, [dropdownOpen]);

  // Fecha o dropdown ao clicar fora dele
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleExport = (format) => {
    onExport(format);
    setDropdownOpen(false);
  };

  // Se estiver em construção, mostrar o botão de construção
  if (underConstruction) {
    return (
      <ConstructionButton
        className="flex items-center gap-2 px-4 py-2 border border-neutral-300 dark:border-gray-600 rounded-lg hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-neutral-300 transition-colors"
        title="Exportação em Construção"
        content="A funcionalidade de exportação está em desenvolvimento e estará disponível em breve."
        icon="FileText"
      >
        <Download size={16} />
        <span>Exportar</span>
        <ChevronDown size={14} />
      </ConstructionButton>
    );
  }

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={() => setDropdownOpen(!dropdownOpen)}
        className={`flex items-center gap-2 px-3 py-1 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${className.includes('text-white') ? 'bg-white/20 hover:bg-white/30 text-white' : 'border border-neutral-300 dark:border-gray-600 hover:bg-neutral-50 dark:hover:bg-gray-700'} ${className}`}
        disabled={isExporting || (disabled && !underConstruction)}
        title={disabled ? "Não há dados para exportar" : "Exportar dados"}
      >
        {isExporting ? (
          <Loader2 size={16} className="animate-spin" />
        ) : (
          <Download size={16} />
        )}
        <span>{isExporting ? "Exportando..." : "Exportar"}</span>
        <ChevronDown size={14} className={`transform transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Dropdown - renderizado via portal para evitar problemas de overflow */}
      {dropdownOpen && mounted && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[9999] w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border border-neutral-200 dark:border-gray-700"
          style={{
            top: `${dropdownPosition.top}px`,
            right: `${dropdownPosition.right}px`,
            width: `${dropdownPosition.width}px`,
          }}
        >
          <div className="p-2 bg-neutral-50 dark:bg-gray-700 border-b border-neutral-200 dark:border-gray-600">
            <h4 className="text-sm font-medium text-neutral-700 dark:text-gray-200">Formato de exportação</h4>
          </div>
          <div className="p-1">
            <button
              onClick={() => handleExport('image')}
              className="w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md"
            >
              <Image size={16} className="text-blue-500 dark:text-blue-400" />
              <span>Imagem (PNG)</span>
            </button>
            <button
              onClick={() => handleExport('pdf')}
              className="w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md"
            >
              <FileText size={16} className="text-red-500 dark:text-red-400" />
              <span>PDF</span>
            </button>
            <button
              onClick={() => handleExport('xlsx')}
              className="w-full text-left px-4 py-2 hover:bg-neutral-50 dark:hover:bg-gray-700 text-neutral-700 dark:text-gray-200 flex items-center gap-2 transition-colors rounded-md"
            >
              <FileSpreadsheet size={16} className="text-green-500 dark:text-green-400" />
              <span>Excel (XLSX)</span>
            </button>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

export default ExportMenu;