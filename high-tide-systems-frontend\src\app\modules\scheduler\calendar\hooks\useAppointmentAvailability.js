"use client";

import { useState, useEffect } from 'react';
import workingHoursService from '@/app/modules/scheduler/services/workingHoursService';

const useAppointmentAvailability = (filters, currentView) => {
  const [providersAvailability, setProvidersAvailability] = useState({});
  const [errorMessage, setErrorMessage] = useState(null);

  // Função auxiliar para obter o ID do provedor
  const getProviderId = (provider) => {
    if (!provider) return null;
    // Tenta todas as propriedades possíveis que podem conter o ID
    return provider.id || provider.value || provider;
  };

  // Pré-carrega dados de disponibilidade quando os filtros de provedores mudam
  useEffect(() => {
    if (filters.providers && filters.providers.length > 0) {
      preloadAvailabilityData();
    }
  }, [filters.providers, currentView]);

  // Pré-carrega dados de disponibilidade para os provedores selecionados
  const preloadAvailabilityData = async () => {
    // Só pré-carregamos se tivermos provedores selecionados e estamos em visualização semanal/diária
    if (
      (currentView !== "timeGridWeek" && currentView !== "timeGridDay") ||
      !filters.providers ||
      filters.providers.length === 0
    ) {
      return;
    }

    console.log("[PRELOAD] Iniciando pré-carregamento de disponibilidade");

    // Para cada provedor selecionado, carregamos os dados para os próximos 7 dias
    for (const provider of filters.providers) {
      const providerId = getProviderId(provider);
      if (!providerId) continue;

      // Carrega dados para os 7 dias da semana
      for (let dayOfWeek = 0; dayOfWeek < 7; dayOfWeek++) {
        // Verifica se já temos os dados em cache
        if (
          providersAvailability[providerId] &&
          providersAvailability[providerId][dayOfWeek] !== undefined
        ) {
          console.log(
            `[PRELOAD] Dados já em cache para provider ${providerId}, dia ${dayOfWeek}`
          );
          continue;
        }

        try {
          console.log(
            `[PRELOAD] Buscando dados para provider ${providerId}, dia ${dayOfWeek}`
          );
          const response = await workingHoursService.getDayGrid(
            providerId,
            dayOfWeek
          );

          if (response && response.timeGrid) {
            // Atualiza o cache
            setProvidersAvailability((prev) => ({
              ...prev,
              [providerId]: {
                ...(prev[providerId] || {}),
                [dayOfWeek]: response.timeGrid,
              },
            }));
            console.log(
              `[PRELOAD] Dados armazenados para provider ${providerId}, dia ${dayOfWeek}`
            );
          }
        } catch (error) {
          console.error(
            `[PRELOAD] Erro ao buscar dados para provider ${providerId}, dia ${dayOfWeek}:`,
            error
          );
        }
      }
    }

    console.log("[PRELOAD] Pré-carregamento finalizado");
  };

  // Verifica disponibilidade para um único provedor
  const checkSingleProviderAvailability = async (providerId, selectedDate) => {
    console.log("[CHECK] Verificando disponibilidade para:", {
      providerId,
      start: selectedDate?.start?.toLocaleString(),
      end: selectedDate?.end?.toLocaleString(),
    });

    if (!providerId) {
      console.log("[CHECK] ProviderId inválido, retornando false");
      return false;
    }

    if (!selectedDate || !selectedDate.start) {
      console.log("[CHECK] Data inválida, retornando false");
      return false;
    }

    try {
      // Converter strings ISO para objetos Date, se necessário
      // Importante: new Date() de uma string ISO cria um objeto Date no fuso horário local
      const startDate = typeof selectedDate.start === 'string' ? new Date(selectedDate.start) : selectedDate.start;
      const endDate = selectedDate.end ? (typeof selectedDate.end === 'string' ? new Date(selectedDate.end) : selectedDate.end) : null;

      console.log("[CHECK] Datas convertidas (horário local):", {
        startOriginal: selectedDate.start,
        startLocal: startDate.toLocaleString(),
        endOriginal: selectedDate.end,
        endLocal: endDate?.toLocaleString()
      });

      // Obtém detalhes da data/hora no horário local
      const dayOfWeek = startDate.getDay();
      const startHour = startDate.getHours();
      const startMinutes = startDate.getMinutes();

      console.log("[CHECK] Dados calculados:", {
        dayOfWeek,
        startHour,
        startMinutes,
        diaDaSemana: [
          "Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"
        ][dayOfWeek],
      });

      // Se não tiver horário de fim, consideramos duração de 1 hora
      let endHour, endMinutes;
      if (endDate) {
        endHour = endDate.getHours();
        endMinutes = endDate.getMinutes();

        // Se terminar em 0 minutos exatos e não for meia-noite, não incluímos a última hora
        if (endMinutes === 0 && endHour > 0) {
          console.log("[CHECK] Fim exato na hora, não incluindo última hora:", endHour);
        } else {
          endHour = endHour + 1; // Arredondamos para a próxima hora para garantir
          console.log("[CHECK] Arredondando para próxima hora:", endHour);
        }
      } else {
        endHour = startHour + 1;
        endMinutes = startMinutes;
        console.log("[CHECK] Sem horário final, usando padrão 1h");
      }

      // Busca ou usa disponibilidade em cache
      let timeGrid;
      const hasCachedData =
        providersAvailability[providerId] &&
        providersAvailability[providerId][dayOfWeek];

      if (!hasCachedData) {
        // Busca disponibilidade do provedor para este dia
        try {
          const response = await workingHoursService.getDayGrid(
            providerId,
            dayOfWeek
          );

          if (!response || !response.timeGrid) {
            console.log("[CHECK] Resposta inválida ou sem timeGrid, retornando false");
            return false;
          }

          timeGrid = response.timeGrid;
          console.log("[CHECK] timeGrid obtido da API:", timeGrid);

          // Atualiza o cache
          setProvidersAvailability((prev) => ({
            ...prev,
            [providerId]: {
              ...(prev[providerId] || {}),
              [dayOfWeek]: timeGrid,
            },
          }));
        } catch (error) {
          console.error("[CHECK] Erro ao buscar grade do dia:", error);
          return false;
        }
      } else {
        timeGrid = providersAvailability[providerId][dayOfWeek];
        console.log("[CHECK] Usando timeGrid do cache:", timeGrid);
      }

      // Verifica se todas as horas inteiras no intervalo estão disponíveis
      console.log("[CHECK] Verificando cada hora no intervalo...");
      for (let hour = startHour; hour < endHour; hour++) {
        // Normalizamos a hora para o índice do array (caso exceda 24h)
        const normalizedHour = hour % 24;

        console.log(`[CHECK] Verificando hora ${hour} (normalizada: ${normalizedHour}):`, {
          disponivel: timeGrid[normalizedHour] ? "Sim" : "Não",
          valorNoArray: timeGrid[normalizedHour],
        });

        // Se qualquer hora no intervalo estiver indisponível, retorna falso
        if (!timeGrid[normalizedHour]) {
          console.log(`[CHECK] Hora ${hour} indisponível, retornando false`);
          return false;
        }
      }

      // Se chegou até aqui, todas as horas no intervalo estão disponíveis
      console.log("[CHECK] Todas as horas disponíveis, retornando true");
      return true;
    } catch (error) {
      console.error("[CHECK] Erro ao verificar disponibilidade:", error);
      return false;
    }
  };

  // Verifica disponibilidade para múltiplos provedores
  const checkProvidersAvailability = async (selectedDate) => {
    console.log("[MULTI-CHECK] Iniciando verificação para múltiplos provedores");

    // Verificar se selectedDate contém datas em formato string ou objeto Date
    if (selectedDate?.start) {
      console.log("[MULTI-CHECK] Tipo da data de início:", typeof selectedDate.start);
    }

    if (!filters.providers || filters.providers.length === 0 || !selectedDate) {
      console.log("[MULTI-CHECK] Sem provedores ou data inválida, retornando true");
      return true; // Sem filtros, consideramos disponível
    }

    // Verificar cada um dos provedores filtrados
    for (const provider of filters.providers) {
      const providerId = getProviderId(provider);
      console.log(`[MULTI-CHECK] Verificando provider: ${providerId}`);

      if (!providerId) {
        console.log("[MULTI-CHECK] ID de provedor inválido, continuando para o próximo");
        continue;
      }

      const isAvailable = await checkSingleProviderAvailability(providerId, selectedDate);

      if (isAvailable) {
        console.log(`[MULTI-CHECK] Pelo menos um provedor disponível, retornando true`);
        return true; // Se pelo menos um estiver disponível, retorna true
      }
    }

    console.log("[MULTI-CHECK] Nenhum provedor disponível, retornando false");
    return false;
  };

  // Verifica se um slot está disponível (usado para classes CSS)
  const isSlotAvailable = (slotInfo) => {
    // Só aplicamos essa lógica nas visualizações de semana e dia
    if (currentView !== "timeGridWeek" && currentView !== "timeGridDay") {
      return true;
    }

    // Se não temos provedores selecionados, todos os slots estão disponíveis
    if (!filters.providers || filters.providers.length === 0) {
      return true;
    }

    // No slotInfo do dayCellClassNames, a data é zerada na meia-noite
    // Precisamos usar o dayOfWeek e extrair a hora do elemento
    const dayOfWeek = slotInfo.date.getDay();

    // Extrai a hora do formato do elemento (ou do atributo data-time se disponível)
    // Como alternativa, usamos a primeira hora visível do dia (8h)
    let hour = 8; // Horário padrão (primeira hora visível no calendário)

    // Tentamos extrair do seletor HTML
    if (slotInfo.el && slotInfo.el.dataset && slotInfo.el.dataset.time) {
      // Se o elemento tem data-time, usamos ele
      const timeMatch = slotInfo.el.dataset.time.match(/T(\d{2}):/);
      if (timeMatch && timeMatch[1]) {
        hour = parseInt(timeMatch[1], 10);
      }
    }

    // Verifica se pelo menos um provedor está disponível neste horário
    return filters.providers.some((providerId) => {
      // Verifica disponibilidade no cache
      if (
        providersAvailability[providerId] &&
        providersAvailability[providerId][dayOfWeek] !== undefined
      ) {
        // TRUE significa DISPONÍVEL, FALSE significa INDISPONÍVEL
        return providersAvailability[providerId][dayOfWeek][hour];
      }

      // Se não temos dados em cache, consideramos disponível por padrão
      return true;
    });
  };

  // Função para validar agendamento antes de abrir o modal
  const validateAppointment = async (providerId, date) => {
    console.log("[VALIDATE] validateAppointment chamado:", {
      providerId,
      start: typeof date?.start === 'string' ? date.start : date?.start?.toLocaleString(),
      end: typeof date?.end === 'string' ? date.end : date?.end?.toLocaleString(),
    });

    if (!providerId) {
      console.log("[VALIDATE] ProviderId inválido, retornando false");
      return false;
    }

    if (!date || !date.start) {
      console.log("[VALIDATE] Data inválida, retornando false");
      return false;
    }

    // Verifica se o horário selecionado está dentro dos horários de trabalho
    const isAvailable = await checkSingleProviderAvailability(providerId, date);
    console.log(`[VALIDATE] Resultado da validação: ${isAvailable ? "disponível" : "indisponível"}`);
    return isAvailable;
  };

  return {
    providersAvailability,
    errorMessage,
    setErrorMessage,
    checkSingleProviderAvailability,
    checkProvidersAvailability,
    isSlotAvailable,
    validateAppointment
  };
};

export default useAppointmentAvailability;