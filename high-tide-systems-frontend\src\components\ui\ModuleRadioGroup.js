'use client';

import React from 'react';
import { ModuleRadio } from '@/components/ui';

/**
 * Radio group component with module-specific styling
 * 
 * @param {Object} props - Component props
 * @param {string} props.moduleColor - Color theme based on module (people, scheduler, admin, financial, abaplus)
 * @param {string} props.name - Name attribute for the radio group
 * @param {string} props.value - Currently selected value
 * @param {Function} props.onChange - Function to call when the radio value changes
 * @param {boolean} props.disabled - Whether the radio group is disabled
 * @param {Array} props.options - Array of options [{value: 'value1', label: 'Label 1'}, ...]
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.layout - Layout of the radio group ('horizontal' or 'vertical')
 */
const ModuleRadioGroup = ({
  moduleColor = 'default',
  name,
  value,
  onChange,
  disabled = false,
  options = [],
  className = '',
  layout = 'horizontal',
  ...rest
}) => {
  // Layout classes
  const layoutClasses = layout === 'vertical' 
    ? 'flex flex-col space-y-2' 
    : 'flex items-center space-x-4';

  return (
    <div className={`${layoutClasses} ${className}`}>
      {options.map((option) => (
        <ModuleRadio
          key={`${name}-${option.value}`}
          name={name}
          value={option.value}
          selectedValue={value}
          onChange={onChange}
          disabled={disabled}
          moduleColor={moduleColor}
          label={option.label}
        />
      ))}
    </div>
  );
};

export default ModuleRadioGroup;
