'use client';

import React, { useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { X, Maximize2, MessageCircle, Search, Plus, Users } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import ChatList from './ChatList';
import ChatConversation from './ChatConversation';
import UserSearch from './UserSearch';
import GroupCreation from './GroupCreation';
import { ModuleInput } from '@/components/ui';

const ChatPanel = () => {
  const {
    isPanelOpen,
    toggleChatPanel,
    toggleChatModal,
    activeConversation,
    setActiveConversation,
    createOrGetConversation,
    createConversation,
    isLoading
  } = useChat();
  const { user } = useAuth();

  const [searchQuery, setSearchQuery] = useState('');
  const [showUserSearch, setShowUserSearch] = useState(false);
  const [showGroupCreation, setShowGroupCreation] = useState(false);

  // Variantes de animação
  const panelVariants = {
    hidden: { opacity: 0, y: 20, scale: 0.95 },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: { type: 'spring', stiffness: 300, damping: 24 }
    },
    exit: {
      opacity: 0,
      y: 20,
      scale: 0.95,
      transition: { duration: 0.2 }
    }
  };

  // Se o usuário não estiver logado e o painel estiver aberto, fechar o painel
  useEffect(() => {
    if (!user && isPanelOpen) {
      toggleChatPanel();
    }
  }, [user, isPanelOpen, toggleChatPanel]);



  return (
    <AnimatePresence>
      {isPanelOpen && user && !user.isClient && (
        <motion.div
          initial="hidden"
          animate="visible"
          exit="exit"
          variants={panelVariants}
          className="fixed bottom-4 right-4 w-[30rem] h-[90vh] bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-orange-300 dark:border-orange-700 flex flex-col z-[9998]"
        >
          {/* Cabeçalho */}
          <div className="p-4 border-b border-orange-200 dark:border-orange-700 flex items-center justify-between bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-t-xl">
            <div className="flex items-center gap-2">
              <MessageCircle size={20} className="text-orange-100" />
              <h3 className="font-semibold text-lg">Mensagens</h3>
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={toggleChatModal}
                className="p-1.5 hover:bg-white/20 rounded-full transition-colors"
                aria-label="Expandir chat"
              >
                <Maximize2 size={16} />
              </button>
              <button
                onClick={toggleChatPanel}
                className="p-1.5 hover:bg-white/20 rounded-full transition-colors"
                aria-label="Fechar chat"
              >
                <X size={16} />
              </button>
            </div>
          </div>

          {/* Conteúdo */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {showUserSearch ? (
              <UserSearch
                onSelectUser={(selectedUser) => {
                  console.log('ChatPanel: Selecionou usuário para criar conversa:', selectedUser);
                  createOrGetConversation(selectedUser)
                    .then((conversation) => {
                      console.log('ChatPanel: Conversa criada/obtida com sucesso:', conversation);
                      // Forçar atualização da lista de conversas
                      setTimeout(() => {
                        console.log('ChatPanel: Forçando atualização da lista de conversas');
                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                          detail: { type: 'conversations', timestamp: Date.now() }
                        }));
                      }, 500);
                      setShowUserSearch(false);
                    })
                    .catch(error => {
                      console.error('Erro ao criar/obter conversa:', error);
                      setShowUserSearch(false);
                    });
                }}
                onClose={() => setShowUserSearch(false)}
              />
            ) : showGroupCreation ? (
              <GroupCreation
                onCreateGroup={(participantIds, title) => {
                  console.log('ChatPanel: Chamando createConversation');
                  console.log('createConversation existe?', typeof createConversation === 'function');

                  if (typeof createConversation !== 'function') {
                    console.error('createConversation não é uma função');
                    return;
                  }

                  createConversation(participantIds, title)
                    .then((result) => {
                      console.log('Grupo criado com sucesso:', result);
                      // Forçar atualização da lista de conversas
                      setTimeout(() => {
                        console.log('ChatPanel: Forçando atualização da lista de conversas após criar grupo');
                        window.dispatchEvent(new CustomEvent('chat:websocket:update', {
                          detail: { type: 'conversations', timestamp: Date.now() }
                        }));
                      }, 500);
                      setShowGroupCreation(false);
                    })
                    .catch(error => {
                      console.error('Erro ao criar grupo:', error);
                      setShowGroupCreation(false);
                    });
                }}
                onBack={() => setShowGroupCreation(false)}
              />
            ) : activeConversation ? (
              <ChatConversation
                conversationId={activeConversation}
                onBack={() => setActiveConversation(null)}
                compact={true}
              />
            ) : (
              <>
                {/* Barra de pesquisa */}
                <div className="p-4 border-b border-gray-200 dark:border-gray-700">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Pesquisar conversas..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full py-2 pl-10 pr-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 dark:focus:ring-orange-600 text-gray-900 dark:text-gray-100 text-sm border border-orange-200 dark:border-orange-800"
                    />
                    <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-orange-500 dark:text-orange-400" />
                  </div>
                </div>

                {/* Lista de conversas */}
                <div className="flex-1 overflow-y-auto relative">
                  {isLoading ? (
                    <div className="absolute inset-0 flex items-center justify-center bg-white/80 dark:bg-gray-800/80 z-10">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                    </div>
                  ) : null}
                  <ChatList searchQuery={searchQuery} />
                </div>

                {/* Botões de nova conversa */}
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  {user ? (
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => setShowUserSearch(true)}
                        className="flex items-center justify-center gap-2 py-2.5 bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 dark:from-orange-600 dark:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 text-white rounded-lg transition-colors shadow-sm"
                      >
                        <Plus size={18} />
                        <span className="font-medium">Nova Conversa</span>
                      </button>
                      <button
                        onClick={() => setShowGroupCreation(true)}
                        className="flex items-center justify-center gap-2 py-2.5 bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 dark:from-purple-600 dark:to-indigo-600 dark:hover:from-purple-700 dark:hover:to-indigo-700 text-white rounded-lg transition-colors shadow-sm"
                      >
                        <Users size={18} />
                        <span className="font-medium">Novo Grupo</span>
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => window.location.href = '/login'}
                      className="w-full flex items-center justify-center gap-2 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
                    >
                      <span>Faça login para conversar</span>
                    </button>
                  )}
                </div>
              </>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ChatPanel;
