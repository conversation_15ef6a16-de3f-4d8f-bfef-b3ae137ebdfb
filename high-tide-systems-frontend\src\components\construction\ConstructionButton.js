"use client";

import React from 'react';
import { useConstructionMessage } from '@/hooks/useConstructionMessage';

/**
 * Botão que exibe uma mensagem "Em Construção" quando clicado
 * 
 * @param {Object} props - Propriedades do componente
 * @param {string} props.title - <PERSON><PERSON><PERSON><PERSON> da mensagem
 * @param {string|React.ReactNode} props.content - Conteúdo/descrição da mensagem
 * @param {string} props.position - Posição do diálogo ('top', 'right', 'bottom', 'left', 'auto')
 * @param {string} props.icon - Ícone a ser exibido ('Construction', 'HardHat', 'Hammer', 'Wrench', 'AlertTriangle')
 * @param {React.ReactNode} props.children - Conteúdo do botão
 * @param {string} props.className - Classes CSS adicionais
 * @param {Object} props.buttonProps - Propriedades adicionais para o botão
 */
const ConstructionButton = ({
  title = 'Em Construção',
  content = 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',
  position = 'auto',
  icon = 'Construction',
  children,
  className = '',
  ...buttonProps
}) => {
  const { constructionProps } = useConstructionMessage();

  // Obter props para o botão "Em Construção"
  const constructionButtonProps = constructionProps({
    title,
    content,
    position,
    icon
  });

  return (
    <button
      className={className}
      {...buttonProps}
      {...constructionButtonProps}
    >
      {children}
    </button>
  );
};

export default ConstructionButton;
