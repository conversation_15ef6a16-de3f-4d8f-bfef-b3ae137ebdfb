// src/app/modules/admin/logs/auditLogService.js
import { api } from '@/utils/api';

export const auditLogService = {
  /**
   * Buscar logs de auditoria com paginação e filtros
   * @param {Object} filters - Filtros para a busca
   * @returns {Promise} - Lista de logs e metadados de paginação
   */
  getLogs: async (filters = {}) => {
    const { page = 1, limit = 20, search, startDate, endDate, action, entityType, userId } = filters;
    
    const response = await api.get('/audit-logs', {
      params: {
        page,
        limit,
        search: search || undefined,
        startDate: startDate || undefined,
        endDate: endDate || undefined,
        action: action || undefined,
        entityType: entityType || undefined,
        userId: userId || undefined,
      },
    });
    
    return response.data;
  },
  
  /**
   * Obter URL para exportação de logs
   * @param {Object} filters - Filtros para a exportação
   * @returns {String} - URL para download do CSV
   */
  getExportUrl: (filters = {}) => {
    const { startDate, endDate, action, entityType, userId } = filters;
    const baseUrl = `${api.defaults.baseURL}/audit-logs/export`;
    
    const params = new URLSearchParams();
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    if (action) params.append('action', action);
    if (entityType) params.append('entityType', entityType);
    if (userId) params.append('userId', userId);
    
    return `${baseUrl}?${params.toString()}`;
  },
  
  /**
   * Buscar detalhes de um log específico
   * @param {String} id - ID do log
   * @returns {Promise} - Detalhes do log
   */
  getLogDetails: async (id) => {
    const response = await api.get(`/audit-logs/${id}`);
    return response.data;
  },
  
  /**
   * Obter estatísticas de logs de auditoria
   * @param {Number} days - Número de dias para analisar
   * @returns {Promise} - Estatísticas de logs
   */
  getStats: async (days = 30) => {
    const response = await api.get('/audit-logs/stats', {
      params: { days }
    });
    return response.data;
  },
  
  /**
   * Limpar logs antigos (apenas para administradores)
   * @param {Number} olderThan - Remover logs mais antigos que X dias
   * @returns {Promise} - Resultado da operação
   */
  cleanupOldLogs: async (olderThan = 365) => {
    const response = await api.post('/audit-logs/cleanup', {
      olderThan
    });
    return response.data;
  }
};