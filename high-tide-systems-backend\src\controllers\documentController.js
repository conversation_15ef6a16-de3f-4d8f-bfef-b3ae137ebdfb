// src/controllers/documentController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const path = require("path");
const fs = require("fs").promises;
const prisma = require("../utils/prisma");

// Caminho de uploads adaptado para funcionar em produção
const UPLOAD_PATH =
  process.env.NODE_ENV === "production"
    ? path.resolve("/data/uploads")
    : path.resolve("/usr/src/app/uploads");

// Verifica e cria o diretório de uploads se não existir
const ensureDirectoryExists = async () => {
  try {
    await fs.access(UPLOAD_PATH);
  } catch (error) {
    await fs.mkdir(UPLOAD_PATH, { recursive: true });
  }
};

// Executa a verificação do diretório
ensureDirectoryExists().catch((err) => {
  console.error(`Erro ao criar diretório de upload: ${err}`);
});

const uploadDocumentValidation = [
  body("types").custom((value, { req }) => {
    try {
      // Se for string, tenta fazer o parse para array
      const types = typeof value === "string" ? JSON.parse(value) : value;

      // Verifica se é um array
      if (!Array.isArray(types)) {
        throw new Error("Types deve ser um array");
      }

      // Verifica se o número de tipos corresponde ao número de arquivos
      if (!req.files || types.length !== req.files.length) {
        throw new Error(
          "Número de tipos deve corresponder ao número de arquivos"
        );
      }

      // Verifica se os tipos são válidos
      const validTypes = ["RG", "CPF", "CNH", "COMP_RESIDENCIA", "OUTROS"];
      const allValid = types.every((type) => validTypes.includes(type));

      if (!allValid) {
        throw new Error("Um ou mais tipos de documento são inválidos");
      }

      // Adiciona o array parseado ao request para uso posterior
      req.body.parsedTypes = types;

      return true;
    } catch (error) {
      if (error instanceof SyntaxError) {
        throw new Error("Formato inválido para types");
      }
      throw error;
    }
  }),
];

class DocumentController {
  static async upload(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ message: "Nenhum arquivo enviado" });
      }

      const types = req.body.parsedTypes;
      const userId = req.user.id;
      const targetId = req.query.targetId;
      const targetType = req.query.targetType;

      // Prepara os dados base para a criação do documento
      let createData;

      // Se não houver targetId, o documento é para o próprio usuário
      if (!targetId) {
        createData = {
          user: {
            connect: {
              id: userId,
            },
          },
          ownerType: "USER",
        };
      }
      // Se for documento para outro usuário
      else if (targetType === "user") {
        // Verificar se o usuário tem permissão para gerenciar usuários
        const canManageUsers = req.user.role === "SYSTEM_ADMIN" ||
                              req.user.role === "COMPANY_ADMIN" ||
                              req.user.permissions.includes("admin.users.create") ||
                              req.user.permissions.includes("admin.users.edit");

        if (!canManageUsers) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res.status(403).json({
            message:
              "Você não tem permissão para adicionar documentos para usuários",
          });
        }

        const targetUser = await prisma.user.findUnique({
          where: { id: targetId },
        });

        if (!targetUser) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res.status(404).json({ message: "Usuário não encontrado" });
        }

        createData = {
          user: {
            connect: {
              id: targetId,
            },
          },
          ownerType: "USER",
        };
      }
      // Se for documento para cliente
      else if (targetType === "person") {
        const person = await prisma.person.findFirst({
          where: {
            id: targetId,
          },
        });

        if (!person) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res
            .status(404)
            .json({ message: "Pessoa não encontrada ou sem permissão" });
        }

        createData = {
          person: {
            connect: {
              id: targetId,
            },
          },
          ownerType: "PERSON",
        };
      }

      // Se for documento para empresa
      else if (targetType === "company") {
        const company = await prisma.company.findUnique({
          where: { id: targetId },
        });

        if (!company) {
          await Promise.all(req.files.map((file) => fs.unlink(file.path)));
          return res.status(404).json({ message: "Empresa não encontrada" });
        }

        createData = {
          company: {
            connect: {
              id: targetId,
            },
          },
          ownerType: "COMPANY",
        };
      }

      // Na criação dos documentos
      const documents = await prisma.$transaction(
        req.files.map((file, index) => {
          // Determinar o mimeType baseado na extensão ou usar o mime type do arquivo
          const mimeType = file.mimetype || "application/octet-stream";

          // Obter o tamanho do arquivo
          const fileSize = file.size || 0;

          return prisma.document.create({
            data: {
              ...createData,
              filename: file.originalname,
              path: path.relative(UPLOAD_PATH, file.path),
              type: types[index],
              mimeType: mimeType,
              size: fileSize,
              createdBy: {
                // Correção aqui - usar sintaxe de relação
                connect: {
                  id: req.user.id,
                },
              },
            },
            include: {
              person: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
              user: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
              company: {
                select: {
                  id: true,
                  name: true,
                },
              },
              createdBy: {
                select: {
                  id: true,
                  fullName: true,
                },
              },
            },
          });
        })
      );

      res.status(201).json(documents);
    } catch (error) {
      // Em caso de erro, remove todos os arquivos enviados
      if (req.files) {
        await Promise.all(
          req.files.map((file) =>
            fs
              .unlink(file.path)
              .catch((err) =>
                console.error(`Erro ao remover arquivo ${file.path}:`, err)
              )
          )
        );
      }

      console.error("Erro ao fazer upload dos documentos:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async getDocument(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      // Log para depuração
      console.log(`Buscando documento: ${id}, UPLOAD_PATH: ${UPLOAD_PATH}`);

      // Buscar o documento no banco de dados
      const document = await prisma.document.findUnique({
        where: { id },
      });

      if (!document) {
        console.log(`Documento não encontrado no banco: ${id}`);
        return res.status(404).json({ message: "Documento não encontrado" });
      }

      // Montar o caminho completo do arquivo
      const absolutePath = path.join(UPLOAD_PATH, document.path);
      console.log(`Caminho absoluto: ${absolutePath}`);

      // Verificar se o arquivo existe fisicamente
      try {
        await fs.access(absolutePath);
      } catch (error) {
        console.error(
          `Arquivo não encontrado no disco: ${absolutePath}`,
          error
        );
        return res
          .status(404)
          .json({ message: "Arquivo físico não encontrado" });
      }

      // Definir headers para download
      res.setHeader(
        "Content-Disposition",
        `inline; filename="${document.filename}"`
      );

      // Enviar o arquivo
      res.sendFile(absolutePath);
    } catch (error) {
      console.error("Erro ao buscar documento:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async delete(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      console.log(`Buscando documento: ${id}, UPLOAD_PATH: ${UPLOAD_PATH}`);

      // Cria a consulta base
      let whereClause = {
        id,
        OR: [
          // Documentos do próprio usuário
          { userId: userId },
          // Documentos criados pelo usuário
          { createdById: userId },
          // Documentos de pessoas onde o usuário é o criador
          {
            person: {
              createdById: userId,
            },
          },
        ],
      };

      // Verificar permissões para gerenciar usuários e empresas
      const canManageUsers = req.user.role === "SYSTEM_ADMIN" ||
                            req.user.role === "COMPANY_ADMIN" ||
                            req.user.permissions.includes("admin.users.create") ||
                            req.user.permissions.includes("admin.users.edit");

      const canManageCompanies = req.user.role === "SYSTEM_ADMIN" ||
                                req.user.role === "COMPANY_ADMIN" ||
                                req.user.permissions.includes("admin.companies.edit");

      // Adicionar permissões com base nas capacidades do usuário
      if (canManageUsers) {
        whereClause.OR.push({ userId: { not: null } }); // Qualquer usuário
      }

      if (canManageCompanies) {
        whereClause.OR.push({ companyId: { not: null } }); // Qualquer empresa
      }

      const document = await prisma.document.findFirst({
        where: whereClause,
      });

      if (!document) {
        return res
          .status(404)
          .json({ message: "Documento não encontrado ou sem permissão" });
      }

      const absolutePath = path.join(UPLOAD_PATH, document.path);
      console.log(`Caminho absoluto: ${absolutePath}`);

      try {
        await fs.unlink(absolutePath);
      } catch (unlinkError) {
        console.error("Erro ao remover arquivo físico:", unlinkError);
      }

      await prisma.document.delete({
        where: { id },
      });

      res.status(204).send();
    } catch (error) {
      console.error("Erro ao deletar documento:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async list(req, res) {
    try {
      const { type, targetId, targetType } = req.query;
      const userId = req.user.id;

      let whereClause = {};

      // Aplicar filtro por tipo se fornecido
      if (type) {
        whereClause.type = type;
      }

      // Aplicar filtros baseados no tipo de alvo e permissões
      if (targetId && targetType) {
        // Se for pessoa
        if (targetType === "person") {
          whereClause.personId = targetId;
        }
        // Se for usuário
        else if (targetType === "user") {
          // Verificar se o usuário tem permissão para gerenciar usuários
          const canManageUsers = req.user.role === "SYSTEM_ADMIN" ||
                                req.user.role === "COMPANY_ADMIN" ||
                                req.user.permissions.includes("admin.users.create") ||
                                req.user.permissions.includes("admin.users.edit") ||
                                req.user.permissions.includes("admin.users.view");

          // Se não for o próprio usuário e não tiver permissão
          if (targetId !== userId && !canManageUsers) {
            return res.status(403).json({
              message:
                "Você não tem permissão para ver documentos de outros usuários",
            });
          }
          whereClause.userId = targetId;
        }
        // Se for empresa
        else if (targetType === "company") {
          // Verificar se o usuário tem permissão para gerenciar empresas
          const canManageCompanies = req.user.role === "SYSTEM_ADMIN" ||
                                    req.user.role === "COMPANY_ADMIN" ||
                                    req.user.permissions.includes("admin.companies.view") ||
                                    req.user.permissions.includes("admin.companies.edit");

          if (!canManageCompanies) {
            return res.status(403).json({
              message:
                "Você não tem permissão para ver documentos de empresas",
            });
          }
          whereClause.companyId = targetId;
        }
      } else {
        // Se nenhum alvo específico foi fornecido, mostrar documentos com base nas permissões
        const canManageUsers = req.user.role === "SYSTEM_ADMIN" ||
                              req.user.role === "COMPANY_ADMIN" ||
                              req.user.permissions.includes("admin.users.create") ||
                              req.user.permissions.includes("admin.users.edit") ||
                              req.user.permissions.includes("admin.users.view");

        if (canManageUsers) {
          // Usuários com permissão podem ver todos os documentos
          // Não aplicar filtros adicionais
        } else {
          // Usuário comum só vê seus próprios documentos
          whereClause.createdById = userId;
        }
      }

      const documents = await prisma.document.findMany({
        where: whereClause,
        include: {
          person: {
            select: {
              id: true,
              fullName: true,
            },
          },
          user: {
            select: {
              id: true,
              fullName: true,
            },
          },
          company: {
            select: {
              id: true,
              name: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
      });

      res.json(documents);
    } catch (error) {
      console.error("Erro ao listar documentos:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  DocumentController,
  uploadDocumentValidation,
};
