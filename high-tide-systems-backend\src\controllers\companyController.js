// src/controllers/companyController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");

// Validações
const companyValidation = [
  body("name").notEmpty().withMessage("Nome é obrigatório"),
  body("cnpj")
    .optional()
    .custom((value, { req }) => {
      // Strip all non-digit characters
      const cleaned = value ? value.replace(/\D/g, "") : "";

      // Store cleaned value for the controller
      req.body.cleanedCnpj = cleaned;

      // Check if it's empty or has exactly 14 digits
      if (cleaned === "" || /^\d{14}$/.test(cleaned)) {
        return true;
      }
      throw new Error("CNPJ inválido");
    }),
  body("phone")
    .optional()
    // Make this more lenient or remove validation since frontend handles format
    .custom(() => true),
  body("socialMedia")
    .optional()
    .custom((value, { req }) => {
      try {
        // If it's a string, try to parse it
        if (typeof value === "string") {
          const parsed = JSON.parse(value);
          // Store the parsed object for the controller
          req.body.parsedSocialMedia = parsed;
          return true;
        }
        // If it's already an object, that's fine too
        if (typeof value === "object" && value !== null) {
          req.body.parsedSocialMedia = value;
          return true;
        }
        throw new Error("Redes sociais deve ser um objeto");
      } catch (error) {
        throw new Error("Redes sociais deve ser um objeto válido");
      }
    }),
  body("businessHours")
    .optional()
    .custom((value, { req }) => {
      try {
        // If it's a string, try to parse it
        if (typeof value === "string") {
          const parsed = JSON.parse(value);
          req.body.parsedBusinessHours = parsed;
          return true;
        }
        // If it's already an object, that's fine too
        if (typeof value === "object" && value !== null) {
          req.body.parsedBusinessHours = value;
          return true;
        }
        return true; // Allow null/undefined
      } catch (error) {
        throw new Error("Horário de funcionamento deve ser um objeto válido");
      }
    }),
];

class CompanyController {
  /**
   * Cria uma nova empresa no sistema
   */
  static async create(req, res) {
    try {
      // Verificar se o usuário é SYSTEM_ADMIN para poder criar empresas
      if (req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message: "Apenas administradores de sistema podem criar empresas",
        });
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        name,
        tradingName,
        cnpj,
        phone,
        phone2,
        address,
        city,
        state,
        postalCode,
        website,
        primaryColor,
        secondaryColor,
        description,
      } = req.body;

      // Use the pre-cleaned/parsed values from validation
      const cleanedCnpj =
        req.body.cleanedCnpj || (cnpj ? cnpj.replace(/\D/g, "") : null);
      const socialMedia = req.body.parsedSocialMedia || {};
      const businessHours = req.body.parsedBusinessHours || {};

      // Verify if CNPJ already exists
      if (cleanedCnpj) {
        const existingCompany = await prisma.company.findUnique({
          where: { cnpj: cleanedCnpj },
        });

        if (existingCompany) {
          return res.status(400).json({ message: "CNPJ já cadastrado" });
        }
      }

      // Create the company
      const company = await prisma.company.create({
        data: {
          name,
          tradingName,
          cnpj: cleanedCnpj,
          phone,
          phone2,
          address,
          city,
          state,
          postalCode,
          website,
          primaryColor,
          secondaryColor,
          description,
          socialMedia,
          businessHours,
        },
      });

      // Handle logo file upload if present
      if (req.file) {
        await prisma.document.create({
          data: {
            filename: req.file.originalname,
            path: req.file.path,
            type: "LOGO",
            mimeType: req.file.mimetype,
            size: req.file.size,
            ownerType: "COMPANY",
            companyId: company.id,
            createdById: req.user.id,
          },
        });
      }

      res.status(201).json(company);
    } catch (error) {
      console.error("Erro ao criar empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém uma empresa específica
   */
  static async get(req, res) {
    try {
      const { id } = req.params;
      console.log('[companyController] Buscando empresa com ID:', id);

      // Verificar se o usuário tem permissão para ver a empresa
      if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId !== id) {
        console.log('[companyController] Acesso negado: usuário não tem permissão');
        return res.status(403).json({
          message: "Você não tem permissão para acessar esta empresa",
        });
      }

      const company = await prisma.company.findUnique({
        where: { id },
        include: {
          emailConfigs: {
            where: { active: true },
            select: {
              id: true,
              smtpHost: true,
              smtpPort: true,
              smtpSecure: true,
              smtpUser: true,
              emailFromName: true,
              emailFromAddress: true,
              active: true,
              createdAt: true,
            },
          },
          documents: {
            where: { type: "LOGO" },
            select: {
              id: true,
              filename: true,
              path: true,
              createdAt: true,
            },
          },
        },
      });

      console.log('[companyController] Resultado da consulta:', company ? 'Empresa encontrada' : 'Empresa não encontrada');

      // Verificar se a empresa tem logo
      if (company && company.documents && company.documents.length > 0) {
        console.log('[companyController] Logo encontrado:', company.documents[0]);
        console.log('[companyController] Caminho do logo:', company.documents[0].path);

        // Construir URL pública para o logo
        const filename = company.documents[0].path.split('/').pop();
        const publicUrl = `/uploads/${filename}`;
        console.log('[companyController] URL pública sugerida:', publicUrl);
      } else {
        console.log('[companyController] Empresa não tem logo');
      }

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Processar o caminho do logo para facilitar o acesso no frontend
      if (company.documents && company.documents.length > 0) {
        const document = company.documents[0];
        console.log('[companyController] Processando caminho do logo para resposta:', document.path);

        // Manter o caminho original completo para referência
        document.originalPath = document.path;

        // Se o caminho começa com /usr/src/app/uploads, manter o caminho completo
        // para que o frontend possa extrair a parte relativa corretamente
        if (document.path.startsWith('/usr/src/app/uploads/')) {
          console.log('[companyController] Mantendo caminho completo do Docker para processamento no frontend');
        } else {
          // Caso contrário, extrair apenas o nome do arquivo
          const filename = document.path.split('/').pop();
          console.log('[companyController] Nome do arquivo extraído:', filename);
          document.path = filename; // Simplificar para apenas o nome do arquivo
        }
      }

      res.json(company);
    } catch (error) {
      console.error("Erro ao buscar empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todas as empresas
   * Para formulário de usuários, utilizar o método listForSelect
   */
  static async list(req, res) {
    try {
      const { page = 1, limit = 10, search, active } = req.query;

      // Verificar se o usuário é SYSTEM_ADMIN para ver todas as empresas
      // Caso contrário, limitar à empresa do usuário
      let where = {
        AND: [
          search
            ? {
                OR: [
                  { name: { contains: search, mode: "insensitive" } },
                  { tradingName: { contains: search, mode: "insensitive" } },
                  { cnpj: { contains: search } },
                ],
              }
            : {},
          active !== undefined ? { active: active === "true" } : {},
        ],
      };

      // Se não for SYSTEM_ADMIN, filtrar apenas pela empresa do usuário
      if (req.user.role !== "SYSTEM_ADMIN") {
        where.AND.push({ id: req.user.companyId });
      }

      const [companies, total] = await Promise.all([
        prisma.company.findMany({
          where,
          skip: (page - 1) * limit,
          take: Number(limit),
          orderBy: { createdAt: "desc" },
          include: {
            documents: {
              where: { type: "LOGO" },
              select: {
                id: true,
                filename: true,
                path: true,
              },
              take: 1,
            },
          },
        }),
        prisma.company.count({ where }),
      ]);

      // Processar o caminho do logo para cada empresa
      companies.forEach(company => {
        if (company.documents && company.documents.length > 0) {
          const document = company.documents[0];
          console.log(`[companyController] Processando caminho do logo para empresa ${company.id}:`, document.path);

          // Manter o caminho original completo para referência
          document.originalPath = document.path;

          // Se o caminho começa com /usr/src/app/uploads, manter o caminho completo
          // para que o frontend possa extrair a parte relativa corretamente
          if (document.path.startsWith('/usr/src/app/uploads/')) {
            console.log(`[companyController] Mantendo caminho completo do Docker para empresa ${company.id}`);
          } else {
            // Caso contrário, extrair apenas o nome do arquivo
            const filename = document.path.split('/').pop();
            console.log(`[companyController] Nome do arquivo extraído para empresa ${company.id}:`, filename);
            document.path = filename; // Simplificar para apenas o nome do arquivo
          }
        }
      });

      res.json({
        companies,
        total,
        pages: Math.ceil(total / limit),
      });
    } catch (error) {
      console.error("Erro ao listar empresas:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista empresas para select no formulário de usuários
   * Versão simplificada com apenas informações básicas
   */
  static async listForSelect(req, res) {
    try {
      // Definir o where baseado no role do usuário
      let where = { active: true };

      // Se não for SYSTEM_ADMIN, limitar à empresa do usuário
      if (req.user.role !== "SYSTEM_ADMIN") {
        where = { ...where, id: req.user.companyId };
      }

      const companies = await prisma.company.findMany({
        where,
        select: {
          id: true,
          name: true,
          tradingName: true,
          cnpj: true,
          active: true,
          city: true,
          state: true,
        },
        orderBy: {
          name: "asc",
        },
      });

      res.json({
        companies,
      });
    } catch (error) {
      console.error("Erro ao listar empresas para select:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza uma empresa
   */
  static async update(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário tem permissão para atualizar a empresa
      if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId !== id) {
        return res.status(403).json({
          message: "Você não tem permissão para atualizar esta empresa",
        });
      }

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        name,
        tradingName,
        cnpj,
        phone,
        phone2,
        address,
        city,
        state,
        postalCode,
        website,
        primaryColor,
        secondaryColor,
        description,
        socialMedia,
        businessHours,
      } = req.body;

      // Verificar se a empresa existe
      const existingCompany = await prisma.company.findUnique({
        where: { id },
      });

      if (!existingCompany) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Verificar se o CNPJ já está em uso por outra empresa
      if (cnpj && cnpj !== existingCompany.cnpj) {
        const companyWithCnpj = await prisma.company.findUnique({
          where: { cnpj },
        });

        if (companyWithCnpj && companyWithCnpj.id !== id) {
          return res.status(400).json({ message: "CNPJ já cadastrado" });
        }
      }

      const company = await prisma.company.update({
        where: { id },
        data: {
          name,
          tradingName,
          cnpj,
          phone,
          phone2,
          address,
          city,
          state,
          postalCode,
          website,
          primaryColor,
          secondaryColor,
          description,
          socialMedia,
          businessHours,
        },
      });

      // Handle logo file upload if present
      if (req.file) {
        console.log('[companyController] Arquivo de logo detectado:', req.file);
        console.log('[companyController] Caminho do arquivo:', req.file.path);

        // Check if company already has a logo
        const existingLogo = await prisma.document.findFirst({
          where: {
            companyId: id,
            type: "LOGO",
          },
        });

        console.log('[companyController] Logo existente:', existingLogo);

        if (existingLogo) {
          console.log('[companyController] Atualizando logo existente');
          // Update existing logo
          const updatedLogo = await prisma.document.update({
            where: { id: existingLogo.id },
            data: {
              filename: req.file.originalname,
              path: req.file.path,
              mimeType: req.file.mimetype,
              size: req.file.size,
              updatedAt: new Date(),
            },
          });
          console.log('[companyController] Logo atualizado:', updatedLogo);
        } else {
          console.log('[companyController] Criando novo documento de logo');
          // Create new logo document
          const newLogo = await prisma.document.create({
            data: {
              filename: req.file.originalname,
              path: req.file.path,
              type: "LOGO",
              mimeType: req.file.mimetype,
              size: req.file.size,
              ownerType: "COMPANY",
              companyId: id,
              createdById: req.user.id,
            },
          });
          console.log('[companyController] Novo logo criado:', newLogo);
        }
      } else {
        console.log('[companyController] Nenhum arquivo de logo enviado');
      }

      // Get updated company with logo
      const updatedCompany = await prisma.company.findUnique({
        where: { id },
        include: {
          documents: {
            where: { type: "LOGO" },
            select: {
              id: true,
              filename: true,
              path: true,
            },
            take: 1,
          },
        },
      });

      // Processar o caminho do logo para retornar apenas o nome do arquivo
      if (updatedCompany.documents && updatedCompany.documents.length > 0) {
        const document = updatedCompany.documents[0];
        console.log('[companyController] Processando caminho do logo para resposta:', document.path);

        // Extrair apenas o nome do arquivo para simplificar o acesso no frontend
        const filename = document.path.split('/').pop();
        console.log('[companyController] Nome do arquivo extraído:', filename);

        // Atualizar o caminho no objeto de resposta
        document.originalPath = document.path; // Manter o caminho original para referência
        document.path = filename; // Simplificar para apenas o nome do arquivo
      }

      res.json(updatedCompany);
    } catch (error) {
      console.error("Erro ao atualizar empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status de ativo/inativo de uma empresa
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário é SYSTEM_ADMIN
      if (req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message:
            "Apenas administradores de sistema podem alterar o status de empresas",
        });
      }

      const company = await prisma.company.findUnique({ where: { id } });
      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      const updatedCompany = await prisma.company.update({
        where: { id },
        data: { active: !company.active },
      });

      res.json(updatedCompany);
    } catch (error) {
      console.error("Erro ao alterar status da empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove uma empresa
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário é SYSTEM_ADMIN
      if (req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message: "Apenas administradores de sistema podem excluir empresas",
        });
      }

      // Verificar se a empresa existe
      const company = await prisma.company.findUnique({
        where: { id },
        include: {
          emailConfigs: true,
        },
      });

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      // Excluir configurações de email relacionadas
      if (company.emailConfigs.length > 0) {
        await prisma.emailConfig.deleteMany({
          where: { companyId: id },
        });
      }

      // Excluir empresa
      await prisma.company.delete({
        where: { id },
      });

      res.status(204).send();
    } catch (error) {
      console.error("Erro ao excluir empresa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém a empresa do usuário autenticado
   * Útil para obter informações da empresa atual sem precisar conhecer o ID
   */
  static async getCurrentCompany(req, res) {
    try {
      // Se o usuário não tem empresa associada
      if (!req.user.companyId) {
        // Para SYSTEM_ADMIN sem empresa, retornar resposta especial
        if (req.user.role === "SYSTEM_ADMIN") {
          return res.json({
            isSystemAdmin: true,
            message:
              "Usuário é um administrador de sistema sem empresa específica",
          });
        }
        return res
          .status(404)
          .json({ message: "Usuário não possui empresa associada" });
      }

      const company = await prisma.company.findUnique({
        where: { id: req.user.companyId },
        include: {
          documents: {
            where: { type: "LOGO" },
            select: {
              id: true,
              filename: true,
              path: true,
              createdAt: true,
            },
            take: 1,
          },
        },
      });

      if (!company) {
        return res.status(404).json({ message: "Empresa não encontrada" });
      }

      res.json(company);
    } catch (error) {
      console.error("Erro ao buscar empresa atual:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  CompanyController,
  companyValidation,
};
