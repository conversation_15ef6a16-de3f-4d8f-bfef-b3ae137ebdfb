const { validationResult } = require('express-validator');
const { body } = require('express-validator');
const prisma = require('../utils/prisma');
const { startOfMonth, endOfMonth } = require('date-fns');
const crypto = require('crypto');

// Validações
const createLimitValidation = [
  body('personId').notEmpty().isUUID().withMessage('ID da pessoa é obrigatório e deve ser um UUID válido'),
  body('insuranceId').notEmpty().isUUID().withMessage('ID do convênio é obrigatório e deve ser um UUID válido'),
  body('serviceTypeId').notEmpty().isUUID().withMessage('ID do tipo de serviço é obrigatório e deve ser um UUID válido'),
  body('monthlyLimit').isInt({ min: 0 }).withMessage('Limite mensal deve ser um número inteiro não negativo (0 = ilimitado)')
];

class InsuranceServiceLimitController {
  /**
   * Lista todos os limites de serviço com suporte a filtros
   */
  static async list(req, res) {
    try {
      const { search, personId, insuranceId, serviceTypeId, companyId, sortField, sortDirection } = req.query;

      // Log para depuração dos parâmetros recebidos
      console.log('Parâmetros recebidos:', {
        search,
        personId,
        personIds: req.query.personIds,
        insuranceId,
        serviceTypeId,
        companyId,
        sortField,
        sortDirection
      });

      // Construir filtro para o Prisma
      const filter = {};

      // Filtrar por pessoa (ID único)
      if (personId) {
        filter.personId = personId;
      }

      // Filtrar por múltiplas pessoas (array de IDs)
      // Verificar se há personIds nos parâmetros da requisição
      const personIds = req.query.personIds;

      // Se temos personIds, usamos o operador "in" do Prisma
      if (personIds) {
        // Garantir que personIds seja tratado como array mesmo quando há apenas um valor
        const personIdsArray = Array.isArray(personIds) ? personIds : [personIds];
        console.log('Filtrando por múltiplos IDs de pacientes:', personIdsArray);

        filter.personId = {
          in: personIdsArray
        };
      }

      // Filtrar por convênio
      if (insuranceId) {
        filter.insuranceId = insuranceId;
      }

      // Filtrar por tipo de serviço
      if (serviceTypeId) {
        filter.serviceTypeId = serviceTypeId;
      }

      // Filtrar por empresa (através do relacionamento com Insurance)
      if (companyId) {
        filter.Insurance = {
          companyId: companyId
        };
      }

      // Definir ordenação
      let orderBy = {};

      // Garantir que sortDirection seja uma string válida
      const direction = (sortDirection && ['asc', 'desc'].includes(sortDirection.toLowerCase()))
        ? sortDirection.toLowerCase()
        : 'asc';

      // Aplicar ordenação com base nos parâmetros recebidos
      if (sortField) {
        console.log(`Ordenando por ${sortField} em direção ${direction}`);

        // Mapear campos de ordenação para os campos reais no banco de dados
        switch (sortField) {
          case 'patient':
            // Ordenação por nome do paciente (será feita manualmente após a consulta)
            orderBy = { id: 'desc' }; // Ordenação padrão para depois ordenar manualmente
            break;
          case 'insurance':
            // Ordenação por nome do convênio (será feita manualmente após a consulta)
            orderBy = { id: 'desc' }; // Ordenação padrão para depois ordenar manualmente
            break;
          case 'serviceType':
            // Ordenação por nome do tipo de serviço (será feita manualmente após a consulta)
            orderBy = { id: 'desc' }; // Ordenação padrão para depois ordenar manualmente
            break;
          case 'monthlyLimit':
            orderBy = { monthlyLimit: direction };
            break;
          default:
            orderBy = { id: 'desc' };
        }
      } else {
        // Ordenação padrão
        orderBy = { id: 'desc' };
      }

      // Buscar limites com filtros
      const limits = await prisma.personInsuranceServiceLimit.findMany({
        where: filter,
        include: {
          Person: {
            select: {
              id: true,
              fullName: true
            }
          },
          Insurance: {
            include: {
              company: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          ServiceType: true
        },
        orderBy
      });

      // Filtrar por termo de busca (no nome da pessoa ou convênio)
      let filteredLimits = limits;
      if (search) {
        const searchLower = search.toLowerCase();
        filteredLimits = limits.filter(limit =>
          limit.Person.fullName.toLowerCase().includes(searchLower) ||
          limit.Insurance.name.toLowerCase().includes(searchLower) ||
          limit.ServiceType.name.toLowerCase().includes(searchLower)
        );
      }

      // Ordenação manual para campos relacionados
      if (sortField) {
        // Garantir que sortDirection seja uma string válida
        const direction = (sortDirection && ['asc', 'desc'].includes(sortDirection.toLowerCase()))
          ? sortDirection.toLowerCase()
          : 'asc';

        console.log(`Aplicando ordenação manual: campo=${sortField}, direção=${direction}`, {
          tipoSortField: typeof sortField,
          tipoSortDirection: typeof sortDirection,
          valorSortField: sortField,
          valorSortDirection: sortDirection
        });

        switch (sortField) {
          case 'patient':
            // Ordenar por nome do paciente
            filteredLimits = [...filteredLimits].sort((a, b) => {
              const aName = (a.Person?.fullName || '').toLowerCase();
              const bName = (b.Person?.fullName || '').toLowerCase();
              return direction === 'asc'
                ? aName.localeCompare(bName, 'pt-BR', { sensitivity: 'base' })
                : bName.localeCompare(aName, 'pt-BR', { sensitivity: 'base' });
            });
            break;
          case 'insurance':
            // Ordenar por nome do convênio
            filteredLimits = [...filteredLimits].sort((a, b) => {
              const aName = (a.Insurance?.name || '').toLowerCase();
              const bName = (b.Insurance?.name || '').toLowerCase();
              return direction === 'asc'
                ? aName.localeCompare(bName, 'pt-BR', { sensitivity: 'base' })
                : bName.localeCompare(aName, 'pt-BR', { sensitivity: 'base' });
            });
            break;
          case 'serviceType':
            // Ordenar por nome do tipo de serviço
            filteredLimits = [...filteredLimits].sort((a, b) => {
              const aName = (a.ServiceType?.name || '').toLowerCase();
              const bName = (b.ServiceType?.name || '').toLowerCase();
              return direction === 'asc'
                ? aName.localeCompare(bName, 'pt-BR', { sensitivity: 'base' })
                : bName.localeCompare(aName, 'pt-BR', { sensitivity: 'base' });
            });
            break;
        }
      }

      res.json(filteredLimits);
    } catch (error) {
      console.error('Erro ao listar limites de serviço:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Cria um novo limite de serviço para um convênio
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { personId, insuranceId, serviceTypeId, monthlyLimit } = req.body;

      // Verificar se a pessoa existe
      const person = await prisma.person.findUnique({
        where: { id: personId }
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Verificar se o convênio existe
      const insurance = await prisma.insurance.findUnique({
        where: { id: insuranceId }
      });

      if (!insurance) {
        return res.status(404).json({ message: 'Convênio não encontrado' });
      }

      // Verificar se o tipo de serviço existe
      const serviceType = await prisma.serviceType.findUnique({
        where: { id: serviceTypeId }
      });

      if (!serviceType) {
        return res.status(404).json({ message: 'Tipo de serviço não encontrado' });
      }

      // Criar o limite
      const limit = await prisma.personInsuranceServiceLimit.create({
        data: {
          id: crypto.randomUUID(), // Gerar um UUID para o ID
          personId,
          insuranceId,
          serviceTypeId,
          monthlyLimit: monthlyLimit || 0,
          yearlyLimit: 0, // Valor padrão
        },
        include: {
          Person: {
            select: {
              id: true,
              fullName: true
            }
          },
          Insurance: {
            include: {
              company: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          ServiceType: true
        }
      });

      res.status(201).json(limit);
    } catch (error) {
      if (error.code === 'P2002') {
        return res.status(400).json({ message: 'Já existe um limite para esta combinação de pessoa, convênio e tipo de serviço' });
      }
      console.error('Erro ao criar limite de serviço:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Atualiza um limite de serviço existente
   */
  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { monthlyLimit } = req.body;

      // Verificar se o limite existe
      const existingLimit = await prisma.personInsuranceServiceLimit.findUnique({
        where: { id }
      });

      if (!existingLimit) {
        return res.status(404).json({ message: 'Limite de serviço não encontrado' });
      }

      // Atualizar o limite
      const limit = await prisma.personInsuranceServiceLimit.update({
        where: { id },
        data: {
          monthlyLimit: monthlyLimit || 0,
        },
        include: {
          Person: {
            select: {
              id: true,
              fullName: true
            }
          },
          Insurance: {
            include: {
              company: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          ServiceType: true
        }
      });

      res.json(limit);
    } catch (error) {
      console.error('Erro ao atualizar limite de serviço:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Exclui um limite de serviço
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o limite existe
      const existingLimit = await prisma.personInsuranceServiceLimit.findUnique({
        where: { id }
      });

      if (!existingLimit) {
        return res.status(404).json({ message: 'Limite de serviço não encontrado' });
      }

      // Excluir o limite
      await prisma.personInsuranceServiceLimit.delete({
        where: { id }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Erro ao excluir limite de serviço:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Lista todos os limites de serviço para uma pessoa
   */
  static async listByPerson(req, res) {
    try {
      const { personId } = req.params;

      // Verificar se a pessoa existe
      const person = await prisma.person.findUnique({
        where: { id: personId }
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Buscar os limites
      const limits = await prisma.personInsuranceServiceLimit.findMany({
        where: { personId },
        include: {
          Insurance: {
            include: {
              company: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          ServiceType: true
        }
      });

      res.json(limits);
    } catch (error) {
      console.error('Erro ao listar limites de serviço:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Lista todos os limites de serviço para uma combinação de pessoa e convênio
   */
  static async listByPersonInsurance(req, res) {
    try {
      const { personId, insuranceId } = req.params;

      // Verificar se a pessoa existe
      const person = await prisma.person.findUnique({
        where: { id: personId }
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Verificar se o convênio existe
      const insurance = await prisma.insurance.findUnique({
        where: { id: insuranceId }
      });

      if (!insurance) {
        return res.status(404).json({ message: 'Convênio não encontrado' });
      }

      // Buscar os limites
      const limits = await prisma.personInsuranceServiceLimit.findMany({
        where: {
          personId,
          insuranceId
        },
        include: {
          ServiceType: true
        }
      });

      res.json(limits);
    } catch (error) {
      console.error('Erro ao listar limites de serviço por pessoa e convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Lista todos os limites de serviço para um convênio específico
   */
  static async listByInsurance(req, res) {
    try {
      const { insuranceId } = req.params;

      // Verificar se o convênio existe
      const insurance = await prisma.insurance.findUnique({
        where: { id: insuranceId }
      });

      if (!insurance) {
        return res.status(404).json({ message: 'Convênio não encontrado' });
      }

      // Buscar os limites
      const limits = await prisma.personInsuranceServiceLimit.findMany({
        where: { insuranceId },
        include: {
          Person: {
            select: {
              id: true,
              fullName: true
            }
          },
          ServiceType: true
        }
      });

      res.json(limits);
    } catch (error) {
      console.error('Erro ao listar limites de serviço por convênio:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Verifica se um agendamento está dentro dos limites de serviço
   */
  static async checkLimit(req, res) {
    try {
      const { personId, insuranceId, serviceTypeId, date } = req.body;

      console.log(`Verificando limite para: Pessoa=${personId}, Convênio=${insuranceId}, Serviço=${serviceTypeId}, Data=${date}`);

      // Se algum dos parâmetros estiver faltando, permitir o agendamento
      if (!personId || !insuranceId || !serviceTypeId) {
        console.log('Dados insuficientes para verificar limites');
        return res.json({
          allowed: true,
          message: 'Dados insuficientes para verificar limites',
          monthlyLimit: 0,
          monthlyUsed: 0,
          monthlyRemaining: 0,
          checkDate: null,
          checkMonth: null
        });
      }

      // Buscar o limite para esta combinação
      const limit = await prisma.personInsuranceServiceLimit.findFirst({
        where: {
          personId,
          insuranceId,
          serviceTypeId
        }
      });

      // Se não houver limite definido, permitir o agendamento
      if (!limit) {
        console.log('Sem limites definidos para esta combinação');
        return res.json({
          allowed: true,
          message: 'Sem limites definidos para esta combinação',
          monthlyLimit: 0,
          monthlyUsed: 0,
          monthlyRemaining: 0,
          checkDate: date ? new Date(date).toISOString() : new Date().toISOString(),
          checkMonth: null
        });
      }

      // Se o limite mensal for 0, significa ilimitado
      if (limit.monthlyLimit === 0) {
        console.log('Limite mensal ilimitado');
        return res.json({
          allowed: true,
          message: 'Limite mensal ilimitado',
          monthlyLimit: 0,
          monthlyUsed: 0,
          monthlyRemaining: 0,
          checkDate: date ? new Date(date).toISOString() : new Date().toISOString(),
          checkMonth: null
        });
      }

      // Determinar o mês para verificação com base na data fornecida ou na data atual
      const checkDate = date ? new Date(date) : new Date();
      const startMonth = startOfMonth(checkDate);
      const endMonth = endOfMonth(checkDate);

      const monthName = new Intl.DateTimeFormat('pt-BR', { month: 'long', year: 'numeric' }).format(checkDate);
      console.log(`Verificando limites para o mês de ${monthName} (${startMonth.toISOString()} até ${endMonth.toISOString()})`);

      // Contar quantos agendamentos a pessoa já tem neste mês para este convênio e tipo de serviço
      const monthlyUsed = await prisma.scheduling.count({
        where: {
          Person: {
            some: {
              id: personId
            }
          },
          insuranceId,
          serviceTypeId,
          startDate: {
            gte: startMonth,
            lte: endMonth
          },
          status: {
            notIn: ['CANCELLED']
          }
        }
      });

      console.log(`Agendamentos utilizados no mês: ${monthlyUsed}/${limit.monthlyLimit}`);

      // Verificar se está dentro do limite mensal
      const monthlyRemaining = limit.monthlyLimit - monthlyUsed;
      const allowed = monthlyRemaining > 0;

      // Obter detalhes da pessoa, convênio e serviço para logs
      const [person, insurance, serviceType] = await Promise.all([
        prisma.person.findUnique({ where: { id: personId }, select: { fullName: true } }),
        prisma.insurance.findUnique({ where: { id: insuranceId }, select: { name: true } }),
        prisma.serviceType.findUnique({ where: { id: serviceTypeId }, select: { name: true } })
      ]);

      console.log(`Resultado da verificação para ${person?.fullName || personId} - ${insurance?.name || insuranceId} - ${serviceType?.name || serviceTypeId}: ${allowed ? 'PERMITIDO' : 'NEGADO'}`);

      res.json({
        allowed,
        message: allowed
          ? `Dentro do limite mensal (${monthlyUsed}/${limit.monthlyLimit}) para ${monthName}`
          : `Limite mensal de ${limit.monthlyLimit} agendamentos atingido para ${monthName}. Você já utilizou ${monthlyUsed} agendamentos.`,
        monthlyLimit: limit.monthlyLimit,
        monthlyUsed,
        monthlyRemaining,
        checkDate: checkDate.toISOString(),
        checkMonth: monthName
      });
    } catch (error) {
      console.error('Erro ao verificar limite de serviço:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  InsuranceServiceLimitController,
  createLimitValidation
};
