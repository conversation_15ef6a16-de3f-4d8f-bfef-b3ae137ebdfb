'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useChat } from '../../contexts/ChatContext';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAuth } from '../../contexts/AuthContext';
import { Trash2 } from 'lucide-react';
import { ConfirmationDialog } from '../../components/ui';

const ChatMessages = ({ conversationId }) => {
  const {
    messages,
    loadMessages,
    isLoading,
    conversations,
    deleteMessages
  } = useChat();

  const { user } = useAuth();
  const messagesEndRef = useRef(null);
  const conversationMessages = messages[conversationId] || [];

  // Estados para gerenciar seleção e exclusão de mensagens
  const [selectedMessages, setSelectedMessages] = useState([]);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Função para alternar seleção de mensagem
  const toggleMessageSelection = useCallback((message) => {
    if (!message || !message.id) return;

    // Verificar se a mensagem é do usuário atual (só pode apagar as próprias mensagens)
    if (message.senderId !== user?.id) {
      alert('Você só pode apagar suas próprias mensagens');
      return;
    }

    // Verificar se a mensagem já está selecionada
    setSelectedMessages(prev => {
      const isSelected = prev.some(m => m.id === message.id);

      if (isSelected) {
        // Remover da seleção
        return prev.filter(m => m.id !== message.id);
      } else {
        // Adicionar à seleção
        return [...prev, message];
      }
    });
  }, [user?.id]);

  // Função para verificar se uma mensagem está selecionada
  const isMessageSelected = useCallback((messageId) => {
    return selectedMessages.some(m => m.id === messageId);
  }, [selectedMessages]);

  // Função para limpar seleção
  const clearSelection = useCallback(() => {
    setSelectedMessages([]);
  }, []);

  // Função para confirmar exclusão de mensagens
  const confirmDeleteMessages = useCallback(() => {
    if (selectedMessages.length === 0) return;

    setConfirmDialogOpen(true);
  }, [selectedMessages]);

  // Função para executar a exclusão de mensagens
  const handleDeleteMessages = useCallback(async () => {
    if (selectedMessages.length === 0) return;

    setIsDeleting(true);

    try {
      const messageIds = selectedMessages.map(m => m.id);
      // Passar o ID da conversa para a função deleteMessages
      const result = await deleteMessages(messageIds, conversationId);

      if (result.success) {
        console.log(`${result.successCount} mensagens apagadas com sucesso`);

        if (result.errorCount > 0) {
          console.warn(`${result.errorCount} mensagens não puderam ser apagadas`);
          alert(`${result.successCount} mensagens foram apagadas, mas ${result.errorCount} não puderam ser apagadas.`);
        }
      } else {
        console.error('Falha ao apagar mensagens');
        alert('Não foi possível apagar as mensagens. Tente novamente mais tarde.');
      }
    } catch (error) {
      console.error('Erro ao apagar mensagens:', error);
      alert('Ocorreu um erro ao apagar as mensagens. Tente novamente mais tarde.');
    } finally {
      setIsDeleting(false);
      setSelectedMessages([]);
      setConfirmDialogOpen(false);
    }
  }, [selectedMessages, deleteMessages, conversationId]);

  // Carregar mensagens quando o componente montar ou o conversationId mudar
  useEffect(() => {
    if (conversationId) {
      loadMessages(conversationId);
    }
  }, [conversationId, loadMessages]);

  // Rolar para a última mensagem quando novas mensagens chegarem
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversationMessages.length]);

  // Agrupar mensagens por data
  const groupMessagesByDate = (messages) => {
    if (!messages || messages.length === 0) {
      console.log("Nenhuma mensagem para agrupar");
      return [];
    }

    console.log(`Agrupando ${messages.length} mensagens`);

    try {
      const groups = {};

      // Ordenar mensagens por data (mais antigas primeiro)
      const sortedMessages = [...messages].sort((a, b) =>
        new Date(a.createdAt) - new Date(b.createdAt)
      );

      sortedMessages.forEach(message => {
        if (!message || !message.createdAt) {
          console.warn("Mensagem inválida encontrada:", message);
          return;
        }

        try {
          const messageDate = new Date(message.createdAt);
          const date = messageDate.toDateString();

          if (!groups[date]) {
            groups[date] = [];
          }

          groups[date].push(message);
        } catch (err) {
          console.error("Erro ao processar mensagem:", err, message);
        }
      });

      // Verificar se temos grupos
      if (Object.keys(groups).length === 0) {
        console.warn("Nenhum grupo criado após processamento");
        return [];
      }

      // Ordenar os grupos por data (mais antigos primeiro)
      const result = Object.entries(groups)
        .sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB))
        .map(([date, messages]) => ({
          date,
          messages
        }));

      console.log(`Criados ${result.length} grupos de mensagens`);
      return result;
    } catch (error) {
      console.error("Erro ao agrupar mensagens:", error);
      return [];
    }
  };

  const messageGroups = groupMessagesByDate(conversationMessages);

  // Formatar hora da mensagem
  const formatMessageTime = (dateString) => {
    return format(new Date(dateString), 'HH:mm', { locale: ptBR });
  };

  // Formatar data do grupo
  const formatGroupDate = (dateString) => {
    const messageDate = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (messageDate.toDateString() === today.toDateString()) {
      return 'Hoje';
    } else if (messageDate.toDateString() === yesterday.toDateString()) {
      return 'Ontem';
    } else {
      return format(messageDate, 'dd/MM/yyyy', { locale: ptBR });
    }
  };

  // Encontrar o nome do remetente
  const getSenderName = (senderId) => {
    if (senderId === user?.id) {
      return 'Você';
    }

    // Procurar o remetente na conversa atual
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return 'Usuário';

    const sender = conversation.participants?.find(p => p.userId === senderId);
    return sender?.user?.fullName || 'Usuário';
  };

  if (isLoading && conversationMessages.length === 0) {
    return (
      <div className="messages-loading flex flex-col items-center justify-center h-full">
        <div className="loading-spinner w-8 h-8 border-4 border-orange-200 border-t-orange-500 rounded-full animate-spin mb-2"></div>
        <p className="text-gray-500">Carregando mensagens...</p>
      </div>
    );
  }

  return (
    <div className="messages-container flex-1 p-4 overflow-y-auto bg-white dark:bg-gray-800 relative">
      {/* Barra de ações para mensagens selecionadas */}
      {selectedMessages.length > 0 && (
        <div className="fixed bottom-20 left-1/2 transform -translate-x-1/2 bg-white dark:bg-gray-800 shadow-lg rounded-lg px-4 py-2 flex items-center gap-3 border border-orange-200 dark:border-orange-700 z-10">
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {selectedMessages.length} {selectedMessages.length === 1 ? 'mensagem selecionada' : 'mensagens selecionadas'}
          </span>

          <button
            onClick={clearSelection}
            className="px-3 py-1 text-sm bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
          >
            Cancelar
          </button>

          <button
            onClick={confirmDeleteMessages}
            className="px-3 py-1 text-sm bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center gap-1"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-1"></span>
                Apagando...
              </>
            ) : (
              <>
                <Trash2 size={14} />
                Apagar
              </>
            )}
          </button>
        </div>
      )}

      {messageGroups.map(group => (
        <div key={group.date} className="message-group mb-4">
          <div className="date-divider flex items-center justify-center my-3">
            <div className="h-px bg-gray-200 dark:bg-gray-700 flex-1"></div>
            <span className="px-2 text-xs text-gray-500 dark:text-gray-400 bg-white dark:bg-gray-800">
              {formatGroupDate(group.date)}
            </span>
            <div className="h-px bg-gray-200 dark:bg-gray-700 flex-1"></div>
          </div>

          {group.messages.map(message => {
            const isCurrentUser = message.senderId === user?.id;
            const isSelected = isMessageSelected(message.id);

            // Verificar se é uma conversa de grupo
            const conversation = conversations.find(c => c.id === conversationId);
            const isGroupChat = conversation?.type === 'GROUP';

            // Obter iniciais do remetente
            const getSenderInitials = (senderId) => {
              const name = getSenderName(senderId);
              if (!name) return 'U';

              try {
                const names = name.split(' ');
                if (names.length === 1) return names[0].charAt(0);
                return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
              } catch (error) {
                console.error('Erro ao obter iniciais:', error);
                return 'U';
              }
            };

            return (
              <div
                key={message.id}
                className={`message mb-2 flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
              >
                {/* Avatar do remetente (apenas para mensagens de grupo e que não são do usuário atual) */}
                {isGroupChat && !isCurrentUser && (
                  <div className="mr-2 flex flex-col items-center justify-start">
                    <div className="h-8 w-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center text-gray-700 dark:text-gray-300 text-xs font-medium">
                      {getSenderInitials(message.senderId)}
                    </div>
                  </div>
                )}

                <div
                  className={`max-w-[75%] ${message.isTemp ? 'opacity-70' : ''} ${isSelected ? 'opacity-80' : ''}`}
                  onDoubleClick={() => toggleMessageSelection(message)}
                >
                  {/* Nome do remetente (apenas para mensagens de grupo e que não são do usuário atual) */}
                  {isGroupChat && !isCurrentUser && (
                    <div className="message-sender-name text-xs font-medium text-gray-600 dark:text-gray-400 ml-1 mb-1">
                      {getSenderName(message.senderId)}
                    </div>
                  )}

                  <div className={`message-bubble rounded-lg px-3 py-2 relative ${
                    isCurrentUser
                      ? `${isSelected ? 'bg-orange-200 dark:bg-orange-800' : 'bg-orange-100 dark:bg-orange-900'} text-orange-900 dark:text-orange-100`
                      : `${isSelected ? 'bg-gray-200 dark:bg-gray-600' : 'bg-gray-100 dark:bg-gray-700'} text-gray-900 dark:text-gray-100`
                  }`}>
                    {isSelected && (
                      <div className="absolute -top-2 -right-2 h-5 w-5 bg-orange-500 dark:bg-orange-600 rounded-full flex items-center justify-center text-white text-xs">
                        ✓
                      </div>
                    )}
                    <div className="message-content break-words">
                      {message.content || message.text}
                    </div>
                    <div className="message-time text-xs text-right mt-1 opacity-70">
                      {formatMessageTime(message.createdAt)}
                      {message.isTemp && (
                        <span className="message-status ml-1 italic">Enviando...</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ))}

      <div ref={messagesEndRef} />

      {/* Diálogo de confirmação para apagar mensagens */}
      <ConfirmationDialog
        isOpen={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        onConfirm={handleDeleteMessages}
        title="Apagar mensagens"
        message={`Tem certeza que deseja apagar ${selectedMessages.length} ${selectedMessages.length === 1 ? 'mensagem' : 'mensagens'}? Esta ação não pode ser desfeita.`}
        confirmText="Apagar"
        cancelText="Cancelar"
        variant="danger"
      />
    </div>
  );
};

export default ChatMessages;
