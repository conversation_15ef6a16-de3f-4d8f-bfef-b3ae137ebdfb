"use client";

import { useAuth } from '@/contexts/AuthContext';
import ProfilePage from '@/app/modules/profile/ProfilePage';
import ClientProfilePage from '@/app/modules/profile/ClientProfilePage';

export default function ProfileRoute() {
  const { user } = useAuth();

  // Verificar se o usuário é um cliente
  const isClient = user?.isClient || user?.role === 'CLIENT';

  // Renderizar o componente apropriado com base no tipo de usuário
  return isClient ? <ClientProfilePage /> : <ProfilePage />;
}
