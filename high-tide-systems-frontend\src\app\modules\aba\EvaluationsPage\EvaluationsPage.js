"use client";

import React, { useState, useEffect } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleSelect } from "@/components/ui";
import ModuleTable from "@/components/ui/ModuleTable";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  ClipboardList,
  Power,
  CheckCircle,
  XCircle,
  Eye,
  Layers
} from "lucide-react";
import { evaluationsService } from "@/app/modules/aba/services/evaluationsService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { useToast } from "@/contexts/ToastContext";
import EvaluationForm from "./EvaluationForm";

const EvaluationsPage = () => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const [evaluations, setEvaluations] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: "",
    active: true,
    page: 1,
    limit: 10
  });
  const [totalItems, setTotalItems] = useState(0);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedEvaluation, setSelectedEvaluation] = useState(null);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);

  // Carregar avaliações
  useEffect(() => {
    const loadEvaluations = async () => {
      try {
        setIsLoading(true);
        const response = await evaluationsService.getEvaluations(filters);
        console.log("Response from service:", response);
        setEvaluations(response.items);
        setTotalItems(response.total);
      } catch (error) {
        console.error("Erro ao carregar avaliações:", error);
        toast_error("Não foi possível carregar as avaliações");
      } finally {
        setIsLoading(false);
      }
    };

    loadEvaluations();
  }, [filters, toast_error]);

  // Manipuladores de eventos
  const handleSearch = (e) => {
    e.preventDefault();
    setFilters({ ...filters, page: 1 });
  };

  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFilters({
      ...filters,
      [name]: type === "checkbox" ? checked : value,
      page: 1
    });
  };

  const handlePageChange = (page) => {
    setFilters({ ...filters, page });
  };

  const handleRefresh = () => {
    setFilters({ ...filters });
  };

  const handleOpenFormModal = (evaluation = null) => {
    setSelectedEvaluation(evaluation);
    setIsFormModalOpen(true);
  };

  const handleCloseFormModal = () => {
    setSelectedEvaluation(null);
    setIsFormModalOpen(false);
  };

  const handleOpenDeleteModal = (evaluation) => {
    setSelectedEvaluation(evaluation);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setSelectedEvaluation(null);
    setIsDeleteModalOpen(false);
  };

  const handleOpenStatusModal = (evaluation) => {
    setSelectedEvaluation(evaluation);
    setIsStatusModalOpen(true);
  };

  const handleCloseStatusModal = () => {
    setSelectedEvaluation(null);
    setIsStatusModalOpen(false);
  };
  useEffect(() => {
    console.log("Dados atuais:", evaluations);
    if (evaluations && evaluations.length > 0) {
      console.log("Primeiro item:", evaluations[0]);
      console.log("Propriedades do primeiro item:", Object.keys(evaluations[0]));
    }
  }, [evaluations]);
  const handleDeleteEvaluation = async () => {
    if (!selectedEvaluation) return;

    try {
      setIsLoading(true);
      await evaluationsService.deleteEvaluation(selectedEvaluation.id);
      toast_success("Avaliação excluída com sucesso");
      setFilters({ ...filters });
    } catch (error) {
      console.error("Erro ao excluir avaliação:", error);
      toast_error("Não foi possível excluir a avaliação");
    } finally {
      setIsLoading(false);
      handleCloseDeleteModal();
    }
  };

  const handleToggleStatus = async () => {
    if (!selectedEvaluation) return;

    try {
      setIsLoading(true);
      await evaluationsService.toggleEvaluationStatus(selectedEvaluation.id);
      toast_success(`Avaliação ${selectedEvaluation.active ? "desativada" : "ativada"} com sucesso`);
      setFilters({ ...filters });
    } catch (error) {
      console.error("Erro ao alterar status da avaliação:", error);
      toast_error("Não foi possível alterar o status da avaliação");
    } finally {
      setIsLoading(false);
      handleCloseStatusModal();
    }
  };

  const handleFormSuccess = () => {
    handleCloseFormModal();
    handleRefresh();
    toast_success("Avaliação salva com sucesso");
  };

  // Função para formatar o tipo de avaliação
  const formatEvaluationType = (type) => {
    switch (type) {
      case "SKILL_ACQUISITION":
        return "Aquisição de Habilidades";
      case "BEHAVIOR_REDUCTION":
        return "Redução de Comportamentos";
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Avaliações"
        description="Gerencie as avaliações e protocolos do módulo ABA+"
        moduleColor="abaplus"
        tutorialName="abaplus-evaluations"
      >
        <TutorialTriggerButton tutorialName="abaplus-evaluations" />
        <button
          onClick={handleRefresh}
          className="p-2 text-teal-600 hover:bg-teal-50 dark:text-teal-400 dark:hover:bg-teal-900/20 rounded-lg transition-colors"
          aria-label="Atualizar lista"
        >
          <RefreshCw size={20} />
        </button>
        <FilterButton
          isOpen={isFilterOpen}
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          moduleColor="abaplus"
        />
        <button
          onClick={() => handleOpenFormModal()}
          className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
        >
          <Plus size={18} />
          Nova Avaliação
        </button>
      </ModuleHeader>

      {/* Tutorial Manager */}
      <TutorialManager
        name="abaplus-evaluations"
        steps={[
          {
            target: ".tutorial-search",
            content: "Utilize a barra de pesquisa para encontrar avaliações específicas.",
            placement: "bottom"
          },
          {
            target: ".tutorial-filters",
            content: "Aplique filtros para refinar sua busca de avaliações.",
            placement: "bottom"
          },
          {
            target: ".tutorial-table",
            content: "Visualize todas as avaliações cadastradas e gerencie-as através das ações disponíveis.",
            placement: "top"
          },
          {
            target: ".tutorial-new-evaluation",
            content: "Clique aqui para cadastrar uma nova avaliação.",
            placement: "left"
          }
        ]}
      />

      {/* Barra de pesquisa */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md tutorial-search">
        <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <ModuleInput
              name="search"
              value={filters.search}
              onChange={handleFilterChange}
              placeholder="Pesquisar por nome ou observações..."
              icon={<Search size={18} />}
              moduleColor="abaplus"
              className="w-full"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center justify-center gap-2"
          >
            <Search size={18} />
            Pesquisar
          </button>
        </form>
      </div>

      {/* Filtros */}
      {isFilterOpen && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md tutorial-filters">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <ModuleSelect
                name="active"
                value={filters.active === "" ? "" : filters.active ? "true" : "false"}
                onChange={(e) => {
                  const value = e.target.value;
                  setFilters({
                    ...filters,
                    active: value === "" ? "" : value === "true",
                    page: 1
                  });
                }}
                moduleColor="abaplus"
              >
                <option value="">Todos</option>
                <option value="true">Ativos</option>
                <option value="false">Inativos</option>
              </ModuleSelect>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Itens por página
              </label>
              <ModuleSelect
                name="limit"
                value={filters.limit}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </ModuleSelect>
            </div>
          </div>
        </div>
      )}

      {/* Tabela de Avaliações */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden tutorial-table">
        <ModuleTable
          columns={[
            { key: "name", field: "name", header: "Nome" },
            { key: "type", field: "type", header: "Tipo" },
            { key: "levels", field: "levels", header: "Níveis" },
            { key: "observations", field: "observations", header: "Observações" },
            { key: "active", field: "active", header: "Status" },
            { key: "actions", field: "actions", header: "Ações" }
          ]}
          data={evaluations}
          isLoading={isLoading}
          pagination={{
            currentPage: filters.page,
            totalItems,
            itemsPerPage: filters.limit,
            onPageChange: handlePageChange
          }}
          emptyMessage="Nenhuma avaliação encontrada"
          emptyIcon={<ClipboardList size={24} />}
          tableId="evaluations-table"
          defaultSortField="name"
          defaultSortDirection="asc"
          renderRow={(evaluation, _index, moduleColors, visibleColumns) => {
            console.log("Renderizando linha:", evaluation);
            console.log("Colunas visíveis:", visibleColumns);

            return (
              <tr key={evaluation.id} className={moduleColors.hoverBg}>
                {visibleColumns.includes("name") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{evaluation.name}</div>
                  </td>
                )}
                {visibleColumns.includes("type") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {formatEvaluationType(evaluation.type)}
                    </div>
                  </td>
                )}
                {visibleColumns.includes("levels") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Layers size={16} className="text-teal-500 mr-2" />
                      <span className="text-sm text-gray-900 dark:text-gray-100">
                        {evaluation.levels ? evaluation.levels.length : 0} níveis
                      </span>
                    </div>
                  </td>
                )}
                {visibleColumns.includes("observations") && (
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 dark:text-gray-400 max-w-xs truncate">
                      {evaluation.observations || "-"}
                    </div>
                  </td>
                )}
                {visibleColumns.includes("active") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    {evaluation.active ? (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        <CheckCircle size={16} className="mr-1" /> Ativo
                      </span>
                    ) : (
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                        <XCircle size={16} className="mr-1" /> Inativo
                      </span>
                    )}
                  </td>
                )}
                {visibleColumns.includes("actions") && (
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end gap-2">
                      <button
                        onClick={() => handleOpenFormModal(evaluation)}
                        className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        aria-label="Editar"
                      >
                        <Edit size={18} />
                      </button>
                      <button
                        onClick={() => handleOpenStatusModal(evaluation)}
                        className={`${
                          evaluation.active
                            ? "text-amber-600 hover:text-amber-900 dark:text-amber-400 dark:hover:text-amber-300"
                            : "text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                        }`}
                        aria-label={evaluation.active ? "Desativar" : "Ativar"}
                      >
                        <Power size={18} />
                      </button>
                      <button
                        onClick={() => handleOpenDeleteModal(evaluation)}
                        className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        aria-label="Excluir"
                      >
                        <Trash size={18} />
                      </button>
                    </div>
                  </td>
                )}
              </tr>
            );
          }}
          moduleColor="abaplus"
        />
      </div>

      {/* Modal de Confirmação de Exclusão */}
      <ConfirmationDialog
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleDeleteEvaluation}
        title="Excluir Avaliação"
        message={`Tem certeza que deseja excluir a avaliação "${selectedEvaluation?.name}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        confirmVariant="danger"
      />

      {/* Modal de Confirmação de Alteração de Status */}
      <ConfirmationDialog
        isOpen={isStatusModalOpen}
        onClose={handleCloseStatusModal}
        onConfirm={handleToggleStatus}
        title={selectedEvaluation?.active ? "Desativar Avaliação" : "Ativar Avaliação"}
        message={`Tem certeza que deseja ${
          selectedEvaluation?.active ? "desativar" : "ativar"
        } a avaliação "${selectedEvaluation?.name}"?`}
        confirmText={selectedEvaluation?.active ? "Desativar" : "Ativar"}
        cancelText="Cancelar"
        confirmVariant={selectedEvaluation?.active ? "warning" : "success"}
      />

      {/* Modal de Formulário de Avaliação */}
      {isFormModalOpen && (
        <EvaluationForm
          isOpen={isFormModalOpen}
          onClose={handleCloseFormModal}
          evaluation={selectedEvaluation}
          onSuccess={handleFormSuccess}
        />
      )}
    </div>
  );
};

export default EvaluationsPage;
