'use client';

import React, { forwardRef } from 'react';

/**
 * Componente de textarea que se adapta à cor do módulo
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {string} props.placeholder - Placeholder do textarea
 * @param {string} props.value - Valor do textarea
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {boolean} props.disabled - Se o textarea está desabilitado
 * @param {boolean} props.required - Se o textarea é obrigatório
 * @param {string} props.name - Nome do textarea
 * @param {string} props.id - ID do textarea
 * @param {boolean} props.error - Se o textarea tem erro
 * @param {string} props.errorMessage - Mensagem de erro
 * @param {number} props.rows - Númer<PERSON>
 * @param {string} props.className - Classes adicionais
 */
const ModuleTextarea = forwardRef(({
  moduleColor = 'default',
  placeholder,
  value,
  onChange,
  disabled = false,
  required = false,
  name,
  id,
  error = false,
  errorMessage,
  rows = 3,
  className = '',
  ...rest
}, ref) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-primary-500 focus-visible:!border-primary-500 dark:focus-visible:!ring-primary-400 dark:focus-visible:!border-primary-400',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    people: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-people-border focus-visible:!border-module-people-border dark:focus-visible:!ring-module-people-border-dark dark:focus-visible:!border-module-people-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    scheduler: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-scheduler-border focus:border-module-scheduler-border dark:focus:ring-module-scheduler-border-dark dark:focus:border-module-scheduler-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-scheduler-border focus-visible:!border-module-scheduler-border dark:focus-visible:!ring-module-scheduler-border-dark dark:focus-visible:!border-module-scheduler-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    admin: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-admin-border focus:border-module-admin-border dark:focus:ring-module-admin-border-dark dark:focus:border-module-admin-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-admin-border focus-visible:!border-module-admin-border dark:focus-visible:!ring-module-admin-border-dark dark:focus-visible:!border-module-admin-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
    financial: {
      focusRing: 'focus:outline-none focus:ring-1 focus:ring-module-financial-border focus:border-module-financial-border dark:focus:ring-module-financial-border-dark dark:focus:border-module-financial-border-dark focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-module-financial-border focus-visible:!border-module-financial-border dark:focus-visible:!ring-module-financial-border-dark dark:focus-visible:!border-module-financial-border-dark',
      errorRing: 'focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400 focus-visible:outline-none focus-visible:!ring-1 focus-visible:!ring-red-500 focus-visible:!border-red-500 dark:focus-visible:!ring-red-400 dark:focus-visible:!border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;

  // Classes base para todos os textareas
  const baseClasses = 'w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200 resize-none outline-none';

  // Classes para o estado de foco
  const focusClasses = error ? colors.errorRing : colors.focusRing;

  // Classes para o estado de erro
  const errorClasses = error ? colors.errorBorder : '';

  // Combinar todas as classes
  const textareaClasses = `${baseClasses} ${focusClasses} ${errorClasses} ${className}`;

  return (
    <div className="relative">
      <textarea
        ref={ref}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        name={name}
        id={id}
        rows={rows}
        className={textareaClasses}
        {...rest}
      />

      {error && errorMessage && (
        <p className="mt-1 text-xs text-red-600 dark:text-red-400">
          {errorMessage}
        </p>
      )}
    </div>
  );
});

ModuleTextarea.displayName = 'ModuleTextarea';

export default ModuleTextarea;
