// src/swagger/authRoutes.js

/**
 * @swagger
 * tags:
 *   name: Auth
 *   description: Autenticação e gestão de sessão
 */

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: Registra um novo usuário
 *     description: Cria um novo usuário no sistema. Por padr<PERSON>, o usuário é criado com o módulo BASIC.
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - login
 *               - email
 *               - fullName
 *               - password
 *             properties:
 *               login:
 *                 type: string
 *                 description: Nome de login do usuário
 *                 example: joao.silva
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email do usuário
 *                 example: <EMAIL>
 *               fullName:
 *                 type: string
 *                 description: Nome completo do usuário
 *                 example: <PERSON>
 *               password:
 *                 type: string
 *                 format: password
 *                 minLength: 6
 *                 description: Sen<PERSON> do usuário (mínimo 6 caracteres)
 *                 example: senha123
 *               cpf:
 *                 type: string
 *                 description: CPF do usuário (sem formatação)
 *                 example: "12345678900"
 *               cnpj:
 *                 type: string
 *                 description: CNPJ do usuário (sem formatação)
 *                 example: "12345678000199"
 *               birthDate:
 *                 type: string
 *                 format: date
 *                 description: Data de nascimento do usuário
 *                 example: "1990-01-01"
 *               address:
 *                 type: string
 *                 description: Endereço do usuário
 *                 example: Rua Exemplo, 123
 *               phone:
 *                 type: string
 *                 description: Telefone do usuário
 *                 example: "11999998888"
 *     responses:
 *       201:
 *         description: Usuário registrado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Dados inválidos ou usuário já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               validationError:
 *                 value:
 *                   errors: [
 *                     {
 *                       msg: "Email inválido",
 *                       param: "email",
 *                       location: "body"
 *                     }
 *                   ]
 *               duplicateError:
 *                 value:
 *                   message: "Email já cadastrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: Autentica um usuário
 *     description: Valida as credenciais do usuário e retorna um token JWT para autenticação
 *     tags: [Auth]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Auth'
 *     responses:
 *       200:
 *         description: Autenticação bem-sucedida
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AuthResponse'
 *       400:
 *         description: Credenciais inválidas ou conta desativada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               invalidCredentials:
 *                 value:
 *                   message: "Credenciais inválidas"
 *               deactivatedAccount:
 *                 value:
 *                   message: "Conta desativada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /auth/me:
 *   get:
 *     summary: Obtém os dados do usuário autenticado
 *     description: Retorna os dados do usuário atualmente autenticado
 *     tags: [Auth]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dados do usuário
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/UserResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Usuário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Usuário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */