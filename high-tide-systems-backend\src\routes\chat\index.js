// src/routes/chat/index.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const conversationRoutes = require('./conversationRoutes');
const messageRoutes = require('./messageRoutes');

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticate);

// Rota de status para verificar autenticação
router.get('/status', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Chat service is running',
    user: {
      id: req.user.id,
      name: req.user.fullName,
      role: req.user.role
    }
  });
});

// Rotas de conversas
router.use('/conversations', conversationRoutes);

// Rotas de mensagens
router.use('/messages', messageRoutes);

module.exports = router;
