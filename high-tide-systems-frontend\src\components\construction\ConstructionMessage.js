"use client";

import React from 'react';
import { useConstruction } from '@/contexts/ConstructionContext';
import ConstructionDialog from './ConstructionDialog';

/**
 * Componente que exibe a mensagem "Em Construção" quando ativada
 */
const ConstructionMessage = () => {
  const { isActive, messageInfo, closeMessage } = useConstruction();

  if (!isActive) return null;

  return (
    <ConstructionDialog
      title={messageInfo.title}
      content={messageInfo.content}
      position={messageInfo.position}
      targetSelector={messageInfo.targetSelector}
      icon={messageInfo.icon}
      onClose={closeMessage}
    />
  );
};

export default ConstructionMessage;
