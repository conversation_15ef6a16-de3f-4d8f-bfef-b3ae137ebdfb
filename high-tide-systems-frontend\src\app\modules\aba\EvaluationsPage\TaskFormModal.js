"use client";

import React, { useState, useEffect } from "react";
import { CheckSquare, Save } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleSelect, ModuleTextarea, ModuleFormGroup } from "@/components/ui";

const TaskFormModal = ({ isOpen, onClose, onSave, task, skills, levels }) => {
  const [formData, setFormData] = useState({
    skillId: "",
    levelId: "",
    order: "",
    name: "",
    milestone: "",
    item: "",
    question: "",
    example: "",
    criteria: "",
    objective: ""
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Carregar dados da tarefa para edição
  useEffect(() => {
    if (task) {
      setFormData({
        skillId: task.skillId || "",
        levelId: task.levelId || "",
        order: task.order || "",
        name: task.name || "",
        milestone: task.milestone || "",
        item: task.item || "",
        question: task.question || "",
        example: task.example || "",
        criteria: task.criteria || "",
        objective: task.objective || ""
      });
    }
  }, [task]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value, type } = e.target;
    
    // Validação especial para o campo "order" (apenas números)
    if (name === "order" && value !== "") {
      const numericValue = value.replace(/[^0-9]/g, "");
      setFormData({ ...formData, [name]: numericValue });
    } else {
      setFormData({ ...formData, [name]: value });
    }
    
    // Limpar erro do campo quando ele for alterado
    if (errors[name]) {
      setErrors({ ...errors, [name]: null });
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.order) {
      newErrors.order = "A ordem é obrigatória";
    } else if (isNaN(Number(formData.order))) {
      newErrors.order = "A ordem deve ser um número";
    }
    
    if (!formData.name || formData.name.trim() === "") {
      newErrors.name = "O nome é obrigatório";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      
      // Preparar dados para salvar
      const taskData = {
        skillId: formData.skillId || null,
        levelId: formData.levelId || null,
        order: Number(formData.order),
        name: formData.name,
        milestone: formData.milestone,
        item: formData.item,
        question: formData.question,
        example: formData.example,
        criteria: formData.criteria,
        objective: formData.objective
      };
      
      onSave(taskData);
    } catch (error) {
      console.error("Erro ao salvar tarefa:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={task ? "Editar Tarefa/Teste" : "Nova Tarefa/Teste"}
      size="lg"
      moduleColor="abaplus"
      icon={<CheckSquare size={20} />}
    >
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <ModuleFormGroup label="Habilidade">
              <ModuleSelect
                name="skillId"
                value={formData.skillId}
                onChange={handleChange}
                moduleColor="abaplus"
              >
                <option value="">Selecione uma habilidade</option>
                {skills.map(skill => (
                  <option key={skill.id} value={skill.id}>
                    {skill.description}
                  </option>
                ))}
              </ModuleSelect>
            </ModuleFormGroup>
          </div>
          
          <div>
            <ModuleFormGroup label="Nível">
              <ModuleSelect
                name="levelId"
                value={formData.levelId}
                onChange={handleChange}
                moduleColor="abaplus"
              >
                <option value="">Selecione um nível</option>
                {levels.map(level => (
                  <option key={level.id} value={level.id}>
                    {level.description}
                  </option>
                ))}
              </ModuleSelect>
            </ModuleFormGroup>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <ModuleFormGroup label="Ordem *" error={errors.order}>
              <ModuleInput
                name="order"
                type="text"
                value={formData.order}
                onChange={handleChange}
                placeholder="Ordem numérica"
                moduleColor="abaplus"
                error={!!errors.order}
              />
            </ModuleFormGroup>
          </div>
          
          <div>
            <ModuleFormGroup label="Nome *" error={errors.name}>
              <ModuleInput
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Nome da tarefa/teste"
                moduleColor="abaplus"
                error={!!errors.name}
              />
            </ModuleFormGroup>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <ModuleFormGroup label="Marco">
              <ModuleInput
                name="milestone"
                value={formData.milestone}
                onChange={handleChange}
                placeholder="Marco"
                moduleColor="abaplus"
              />
            </ModuleFormGroup>
            
            <ModuleFormGroup label="Item">
              <ModuleInput
                name="item"
                value={formData.item}
                onChange={handleChange}
                placeholder="Item"
                moduleColor="abaplus"
              />
            </ModuleFormGroup>
          </div>
        </div>

        <ModuleFormGroup label="Pergunta">
          <ModuleTextarea
            name="question"
            value={formData.question}
            onChange={handleChange}
            placeholder="Pergunta da tarefa/teste"
            rows={3}
            moduleColor="abaplus"
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Exemplo">
          <ModuleTextarea
            name="example"
            value={formData.example}
            onChange={handleChange}
            placeholder="Exemplo de aplicação"
            rows={3}
            moduleColor="abaplus"
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Critério">
          <ModuleTextarea
            name="criteria"
            value={formData.criteria}
            onChange={handleChange}
            placeholder="Critério de avaliação"
            rows={3}
            moduleColor="abaplus"
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Objetivo">
          <ModuleTextarea
            name="objective"
            value={formData.objective}
            onChange={handleChange}
            placeholder="Objetivo da tarefa/teste"
            rows={3}
            moduleColor="abaplus"
          />
        </ModuleFormGroup>

        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Save size={18} />
                <span>Salvar</span>
              </>
            )}
          </button>
        </div>
      </div>
    </ModuleModal>
  );
};

export default TaskFormModal;
