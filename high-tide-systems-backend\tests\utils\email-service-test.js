// tests/utils/email-service-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do serviço de e-mail...');
  console.log('⚠️ Nota: Este teste não envia e-mails reais, apenas verifica a configuração do serviço.');
  
  try {
    // Teste 1: Verificar configurações de e-mail
    console.log('\n1. Verificando configurações de e-mail...');
    
    // Obter configurações de e-mail
    const emailConfigsResponse = await api.get('/email-configs');
    
    if (emailConfigsResponse.status === 200) {
      console.log(`✅ ${emailConfigsResponse.data.length} configurações de e-mail encontradas`);
      
      if (emailConfigsResponse.data.length > 0) {
        const config = emailConfigsResponse.data[0];
        console.log('Detalhes da configuração:');
        console.log(`Host: ${config.smtpHost}`);
        console.log(`Porta: ${config.smtpPort}`);
        console.log(`Seguro: ${config.smtpSecure ? 'Sim' : 'Não'}`);
        console.log(`Usuário: ${config.smtpUser}`);
        console.log(`De (nome): ${config.emailFromName}`);
        console.log(`De (e-mail): ${config.emailFromAddress}`);
        console.log(`Ativo: ${config.active ? 'Sim' : 'Não'}`);
      } else {
        console.log('⚠️ Nenhuma configuração de e-mail encontrada');
      }
    } else {
      console.log('❌ Falha ao obter configurações de e-mail');
    }
    
    // Teste 2: Criar uma configuração de e-mail de teste (se não houver nenhuma)
    if (emailConfigsResponse.data.length === 0) {
      console.log('\n2. Criando configuração de e-mail de teste...');
      
      const testEmailConfig = {
        smtpHost: 'smtp.example.com',
        smtpPort: 587,
        smtpSecure: false,
        smtpUser: '<EMAIL>',
        smtpPassword: 'password123',
        emailFromName: 'Test System',
        emailFromAddress: '<EMAIL>',
        companyId: '00000000-0000-0000-0000-000000000001'
      };
      
      try {
        const createResponse = await api.post('/email-configs', testEmailConfig);
        
        if (createResponse.status === 201) {
          console.log('✅ Configuração de e-mail criada com sucesso!');
          console.log(`ID: ${createResponse.data.id}`);
        } else {
          console.log('❌ Falha ao criar configuração de e-mail');
        }
      } catch (error) {
        console.log('❌ Falha ao criar configuração de e-mail:', error.response?.data?.message || error.message);
      }
    } else {
      console.log('\n2. Pulando criação de configuração de e-mail (já existe)');
    }
    
    // Teste 3: Verificar serviço de lembretes (que usa o serviço de e-mail)
    console.log('\n3. Verificando serviço de lembretes...');
    
    // Obter agendamentos para verificar se há lembretes configurados
    const schedulingsResponse = await api.get('/schedulings');
    
    if (schedulingsResponse.status === 200) {
      console.log(`✅ ${schedulingsResponse.data.length} agendamentos encontrados`);
      
      // Verificar se há agendamentos com lembretes enviados
      const schedulingsWithReminders = schedulingsResponse.data.filter(s => s.reminderSentAt);
      
      if (schedulingsWithReminders.length > 0) {
        console.log(`✅ ${schedulingsWithReminders.length} agendamentos com lembretes enviados`);
        console.log('Exemplo de agendamento com lembrete:');
        console.log(`ID: ${schedulingsWithReminders[0].id}`);
        console.log(`Título: ${schedulingsWithReminders[0].title}`);
        console.log(`Data de início: ${schedulingsWithReminders[0].startDate}`);
        console.log(`Lembrete enviado em: ${schedulingsWithReminders[0].reminderSentAt}`);
      } else {
        console.log('⚠️ Nenhum agendamento com lembrete enviado encontrado');
      }
    } else {
      console.log('❌ Falha ao obter agendamentos');
    }
    
    console.log('\n✅ Testes do serviço de e-mail concluídos!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
