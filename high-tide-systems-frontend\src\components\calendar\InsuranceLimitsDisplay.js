import React from "react";
import { RotateCw, Info } from "lucide-react";

const InsuranceLimitsDisplay = ({ limitInfo, isLoading, isEditMode }) => {
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-3 bg-neutral-50 dark:bg-gray-700 rounded-md">
        <RotateCw className="w-4 h-4 mr-2 animate-spin text-primary-500" />
        <span className="text-sm text-neutral-700 dark:text-neutral-300">
          Verificando limites de convênio...
        </span>
      </div>
    );
  }

  if (!limitInfo) {
    return null;
  }

  // Verificar se existem informações de limite para exibir
  const hasMonthlyLimit = limitInfo.monthly && !limitInfo.monthly.unlimited;

  if (!hasMonthlyLimit) {
    return (
      <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-md">
        <p className="text-sm text-green-800 dark:text-green-300">
          Este convênio não possui limites definidos para este tipo de serviço.
        </p>
      </div>
    );
  }

  // Verificar status dos limites
  const isMonthlyLimitReached = hasMonthlyLimit && limitInfo.monthly.used >= limitInfo.monthly.limit;
  const hasReachedAnyLimit = isMonthlyLimitReached;

  // Determinar as cores e estilos com base no modo (edição ou criação) e no status do limite
  let containerClasses = "p-3 rounded-md border ";
  let titleClasses = "text-sm font-medium mb-2 ";

  if (isEditMode && hasReachedAnyLimit) {
    // Em modo de edição com limite atingido, usar cores de aviso (amarelo/laranja)
    containerClasses += "bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800/50";
    titleClasses += "text-yellow-800 dark:text-yellow-300";
  } else if (hasReachedAnyLimit) {
    // Em modo de criação com limite atingido, usar cores de erro (vermelho)
    containerClasses += "bg-error-50 dark:bg-red-900/20 border-error-200 dark:border-red-800/50";
    titleClasses += "text-error-800 dark:text-red-300";
  } else {
    // Limite não atingido, usar cores informativas (azul)
    containerClasses += "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700";
    titleClasses += "text-blue-800 dark:text-blue-300";
  }

  return (
    <div className={containerClasses}>
      <h4 className={titleClasses}>
        Limites do Convênio
      </h4>

      <div className="space-y-3">
        {hasMonthlyLimit && (
          <div className="flex justify-between">
            <span className="text-sm text-neutral-700 dark:text-neutral-300">
              Mensal:
            </span>
            <span className={`text-sm font-medium ${
              isMonthlyLimitReached
                ? isEditMode
                  ? "text-yellow-600 dark:text-yellow-400"
                  : "text-error-600 dark:text-red-400"
                : "text-neutral-700 dark:text-neutral-300"
            }`}>
              {limitInfo.monthly.used} / {limitInfo.monthly.limit}
              {isMonthlyLimitReached && " (Limite atingido)"}
            </span>
          </div>
        )}

        {/* Mensagem adicional para edição quando o limite foi atingido */}
        {isEditMode && hasReachedAnyLimit && (
          <div className="flex items-start mt-2 text-sm">
            <Info size={16} className="mr-2 flex-shrink-0 text-yellow-600 dark:text-yellow-400" />
            <p className="text-yellow-700 dark:text-yellow-300">
              O limite mensal foi atingido, mas você pode editar este agendamento existente.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InsuranceLimitsDisplay;