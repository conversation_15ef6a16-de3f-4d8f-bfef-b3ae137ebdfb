// src/controllers/aba/anamneseController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createAnamneseValidation = [
  body("date").isISO8601().withMessage("Data inválida"),
  body("personId").notEmpty().withMessage("ID do paciente é obrigatório"),
  body("diagnostico").optional(),
  body("cuidador").optional(),
  body("profissaoCuidador").optional(),
  body("telefone").optional(),
  body("historicoPersonal").optional(),
  body("patologiaAssociada").optional(),
  body("convulsoes").optional(),
  body("sentou").optional(),
  body("engatinhou").optional(),
  body("andou").optional(),
  body("estereotipiasMotoras").optional(),
  body("alimentacaoSolidos").optional().isBoolean().withMessage("Valor inválido para alimentação sólidos"),
  body("alimentacaoLiquidos").optional().isBoolean().withMessage("Valor inválido para alimentação líquidos"),
  body("alimentacaoPastosos").optional().isBoolean().withMessage("Valor inválido para alimentação pastosos"),
  body("alergiasIntolerancias").optional(),
  // Campos adicionais para informações médicas
  body("historicoMedico").optional(),
  body("medicacoes").optional(),
  body("alergias").optional(),
  body("historicoFamiliar").optional(),
  body("observacoesGerais").optional(),
  // AVD
  body("avdAlimentacao").optional(),
  body("avdBanho").optional(),
  body("avdVestuario").optional(),
  body("avdCuidadosPessoais").optional(),
  body("avdSono").optional(),
  body("avdEsfincter").optional(),
  // Desenvolvimento da Linguagem - Não Verbal
  body("gestosElementares").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para gestos elementares"),
  body("naoSimbolicosConvencionais").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para não simbólicos convencionais"),
  body("simbolicosRepresentacao").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para simbólicos representação"),
  // Desenvolvimento da Linguagem - Verbal
  body("verbal").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para verbal"),
  body("balbucio").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para balbucio"),
  body("palavrasIsoladas").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para palavras isoladas"),
  body("quaisPalavrasIsoladas").optional(),
  body("enunciadoDuasPalavras").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para enunciado duas palavras"),
  body("frases").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para frases"),
  body("estereotipiasVocais").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para estereotipias vocais"),
  body("quaisEstereotipiasVocais").optional(),
  // Interação Social
  body("faltaExpressaoFacialAdequada").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para falta expressão facial adequada"),
  body("apresentaAtencaoDiminuida").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para apresenta atenção diminuída"),
  body("apresentaPreferenciaIsolamento").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para apresenta preferência isolamento"),
  body("ageComoSeFosseSurdo").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para age como se fosse surdo"),
  body("olhaParaAlguemQueLheFala").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para olha para alguém que lhe fala"),
  body("olhaQuandoChamadoPeloNome").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para olha quando chamado pelo nome"),
  body("fazPedidoItensInteresse").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para faz pedido itens interesse"),
  body("realizaImitacao").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para realiza imitação"),
  body("brincaAdequadamenteBrinquedo").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para brinca adequadamente brinquedo"),
  body("preferenciasObjetosEspecificos").optional(),
  body("apresentaAversoes").optional(),
  body("autoEstimulacao").optional().isBoolean().withMessage("Valor inválido para auto estimulação"),
  body("apresentaAutoAgressaoHeteroAgressao").optional().isBoolean().withMessage("Valor inválido para auto agressão/hetero agressão"),
  body("apresentaBirrasIrritabilidade").optional().isBoolean().withMessage("Valor inválido para birras/irritabilidade"),
  body("apresentaManiasRituais").optional().isBoolean().withMessage("Valor inválido para manias/rituais"),
  // Escola
  body("estuda").optional().isBoolean().withMessage("Valor inválido para estuda"),
  body("nomeEscola").optional(),
  body("serie").optional(),
  body("escolaRegular").optional().isBoolean().withMessage("Valor inválido para escola regular"),
  body("professorApoio").optional().isBoolean().withMessage("Valor inválido para professor de apoio"),
  // Outros
  body("outroCasoFamilia").optional().isBoolean().withMessage("Valor inválido para outro caso na família"),
  body("outrosCasosDetalhamento").optional(),
  body("terapias").optional(),
  body("expectativasFamilia").optional(),
  // Campos para controle de versão
  body("versao").optional().isInt().withMessage("Valor inválido para versão"),
];

const updateAnamneseValidation = [
  body("date").optional().isISO8601().withMessage("Data inválida"),
  body("personId").optional().notEmpty().withMessage("ID do paciente é obrigatório"),
  body("diagnostico").optional(),
  body("cuidador").optional(),
  body("profissaoCuidador").optional(),
  body("telefone").optional(),
  body("historicoPersonal").optional(),
  body("patologiaAssociada").optional(),
  body("convulsoes").optional(),
  body("sentou").optional(),
  body("engatinhou").optional(),
  body("andou").optional(),
  body("estereotipiasMotoras").optional(),
  body("alimentacaoSolidos").optional().isBoolean().withMessage("Valor inválido para alimentação sólidos"),
  body("alimentacaoLiquidos").optional().isBoolean().withMessage("Valor inválido para alimentação líquidos"),
  body("alimentacaoPastosos").optional().isBoolean().withMessage("Valor inválido para alimentação pastosos"),
  body("alergiasIntolerancias").optional(),
  // Campos adicionais para informações médicas
  body("historicoMedico").optional(),
  body("medicacoes").optional(),
  body("alergias").optional(),
  body("historicoFamiliar").optional(),
  body("observacoesGerais").optional(),
  // AVD
  body("avdAlimentacao").optional(),
  body("avdBanho").optional(),
  body("avdVestuario").optional(),
  body("avdCuidadosPessoais").optional(),
  body("avdSono").optional(),
  body("avdEsfincter").optional(),
  // Desenvolvimento da Linguagem - Não Verbal
  body("gestosElementares").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para gestos elementares"),
  body("naoSimbolicosConvencionais").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para não simbólicos convencionais"),
  body("simbolicosRepresentacao").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para simbólicos representação"),
  // Desenvolvimento da Linguagem - Verbal
  body("verbal").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para verbal"),
  body("balbucio").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para balbucio"),
  body("palavrasIsoladas").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para palavras isoladas"),
  body("quaisPalavrasIsoladas").optional(),
  body("enunciadoDuasPalavras").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para enunciado duas palavras"),
  body("frases").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para frases"),
  body("estereotipiasVocais").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para estereotipias vocais"),
  body("quaisEstereotipiasVocais").optional(),
  // Interação Social
  body("faltaExpressaoFacialAdequada").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para falta expressão facial adequada"),
  body("apresentaAtencaoDiminuida").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para apresenta atenção diminuída"),
  body("apresentaPreferenciaIsolamento").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para apresenta preferência isolamento"),
  body("ageComoSeFosseSurdo").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para age como se fosse surdo"),
  body("olhaParaAlguemQueLheFala").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para olha para alguém que lhe fala"),
  body("olhaQuandoChamadoPeloNome").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para olha quando chamado pelo nome"),
  body("fazPedidoItensInteresse").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para faz pedido itens interesse"),
  body("realizaImitacao").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para realiza imitação"),
  body("brincaAdequadamenteBrinquedo").optional().isIn(["SIM", "NAO", "AS_VEZES"]).withMessage("Valor inválido para brinca adequadamente brinquedo"),
  body("preferenciasObjetosEspecificos").optional(),
  body("apresentaAversoes").optional(),
  body("autoEstimulacao").optional().isBoolean().withMessage("Valor inválido para auto estimulação"),
  body("apresentaAutoAgressaoHeteroAgressao").optional().isBoolean().withMessage("Valor inválido para auto agressão/hetero agressão"),
  body("apresentaBirrasIrritabilidade").optional().isBoolean().withMessage("Valor inválido para birras/irritabilidade"),
  body("apresentaManiasRituais").optional().isBoolean().withMessage("Valor inválido para manias/rituais"),
  // Escola
  body("estuda").optional().isBoolean().withMessage("Valor inválido para estuda"),
  body("nomeEscola").optional(),
  body("serie").optional(),
  body("escolaRegular").optional().isBoolean().withMessage("Valor inválido para escola regular"),
  body("professorApoio").optional().isBoolean().withMessage("Valor inválido para professor de apoio"),
  // Outros
  body("outroCasoFamilia").optional().isBoolean().withMessage("Valor inválido para outro caso na família"),
  body("outrosCasosDetalhamento").optional(),
  body("terapias").optional(),
  body("expectativasFamilia").optional(),
  // Campos para controle de versão
  body("versao").optional().isInt().withMessage("Valor inválido para versão"),
];

// Exportar o controlador e as validações
module.exports = {
  AnamneseController: class AnamneseController {
    /**
     * Cria uma nova anamnese
     */
    static async create(req, res) {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({ errors: errors.array() });
        }

        const {
          date,
          personId,
          diagnostico,
          cuidador,
          profissaoCuidador,
          telefone,
          historicoPersonal,
          patologiaAssociada,
          convulsoes,
          sentou,
          engatinhou,
          andou,
          estereotipiasMotoras,
          alimentacaoSolidos,
          alimentacaoLiquidos,
          alimentacaoPastosos,
          alergiasIntolerancias,
          // AVD
          avdAlimentacao,
          avdBanho,
          avdVestuario,
          avdCuidadosPessoais,
          avdSono,
          avdEsfincter,
          // Desenvolvimento da Linguagem - Não Verbal
          gestosElementares,
          naoSimbolicosConvencionais,
          simbolicosRepresentacao,
          // Desenvolvimento da Linguagem - Verbal
          verbal,
          balbucio,
          palavrasIsoladas,
          quaisPalavrasIsoladas,
          enunciadoDuasPalavras,
          frases,
          estereotipiasVocais,
          quaisEstereotipiasVocais,
          // Interação Social
          faltaExpressaoFacialAdequada,
          apresentaAtencaoDiminuida,
          apresentaPreferenciaIsolamento,
          ageComoSeFosseSurdo,
          olhaParaAlguemQueLheFala,
          olhaQuandoChamadoPeloNome,
          fazPedidoItensInteresse,
          realizaImitacao,
          brincaAdequadamenteBrinquedo,
          preferenciasObjetosEspecificos,
          apresentaAversoes,
          autoEstimulacao,
          apresentaAutoAgressaoHeteroAgressao,
          apresentaBirrasIrritabilidade,
          apresentaManiasRituais,
          // Escola
          estuda,
          nomeEscola,
          serie,
          escolaRegular,
          professorApoio,
          // Outros
          outroCasoFamilia,
          outrosCasosDetalhamento,
          terapias,
          expectativasFamilia,
        } = req.body;

        // Criar anamnese
        const anamnese = await prisma.anamnese.create({
          data: {
            date: new Date(date),
            personId,
            diagnostico,
            cuidador,
            profissaoCuidador,
            telefone,
            historicoPersonal,
            patologiaAssociada,
            convulsoes,
            sentou,
            engatinhou,
            andou,
            estereotipiasMotoras,
            alimentacaoSolidos: alimentacaoSolidos || false,
            alimentacaoLiquidos: alimentacaoLiquidos || false,
            alimentacaoPastosos: alimentacaoPastosos || false,
            alergiasIntolerancias,
            // Campos adicionais para informações médicas
            historicoMedico: req.body.historicoMedico,
            medicacoes: req.body.medicacoes,
            alergias: req.body.alergias,
            historicoFamiliar: req.body.historicoFamiliar,
            observacoesGerais: req.body.observacoesGerais,
            // AVD
            avdAlimentacao,
            avdBanho,
            avdVestuario,
            avdCuidadosPessoais,
            avdSono,
            avdEsfincter,
            // Desenvolvimento da Linguagem - Não Verbal
            gestosElementares,
            naoSimbolicosConvencionais,
            simbolicosRepresentacao,
            // Desenvolvimento da Linguagem - Verbal
            verbal,
            balbucio,
            palavrasIsoladas,
            quaisPalavrasIsoladas,
            enunciadoDuasPalavras,
            frases,
            estereotipiasVocais,
            quaisEstereotipiasVocais,
            // Interação Social
            faltaExpressaoFacialAdequada,
            apresentaAtencaoDiminuida,
            apresentaPreferenciaIsolamento,
            ageComoSeFosseSurdo,
            olhaParaAlguemQueLheFala,
            olhaQuandoChamadoPeloNome,
            fazPedidoItensInteresse,
            realizaImitacao,
            brincaAdequadamenteBrinquedo,
            preferenciasObjetosEspecificos,
            apresentaAversoes,
            autoEstimulacao: autoEstimulacao || false,
            apresentaAutoAgressaoHeteroAgressao: apresentaAutoAgressaoHeteroAgressao || false,
            apresentaBirrasIrritabilidade: apresentaBirrasIrritabilidade || false,
            apresentaManiasRituais: apresentaManiasRituais || false,
            // Escola
            estuda: estuda || false,
            nomeEscola,
            serie,
            escolaRegular: escolaRegular || false,
            professorApoio: professorApoio || false,
            // Outros
            outroCasoFamilia: outroCasoFamilia || false,
            outrosCasosDetalhamento,
            terapias,
            expectativasFamilia,
            // Campos para controle de versão
            versao: 1,
            ultimaAtualizacaoPorId: req.user.id,
            companyId: req.user.companyId,
            createdById: req.user.id,
          },
        });

        return res.status(201).json(formatSuccessResponse(anamnese));
      } catch (error) {
        console.error("Erro ao criar anamnese:", error);
        return res.status(500).json(formatErrorResponse("Erro interno do servidor"));
      }
    }

    /**
     * Lista todas as anamneses com filtros opcionais
     */
    static async list(req, res) {
      try {
        const { page = 1, limit = 10, search = "", personId, active } = req.query;
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Construir filtros
        const where = {
          companyId: req.user.companyId,
          deletedAt: null,
        };

        // Filtro por status ativo/inativo
        if (active !== undefined) {
          where.active = active === 'true';
        }

        // Filtro por paciente
        if (personId) {
          where.personId = personId;
        }

        // Filtro por termo de busca
        if (search) {
          where.OR = [
            { diagnostico: { contains: search, mode: 'insensitive' } },
            { cuidador: { contains: search, mode: 'insensitive' } },
            { profissaoCuidador: { contains: search, mode: 'insensitive' } },
            { person: { fullName: { contains: search, mode: 'insensitive' } } },
          ];
        }

        // Contar total de registros
        const total = await prisma.anamnese.count({ where });

        // Buscar anamneses com paginação
        const anamneses = await prisma.anamnese.findMany({
          where,
          include: {
            person: {
              select: {
                id: true,
                fullName: true,
                profileImageUrl: true,
              },
            },
            createdBy: {
              select: {
                id: true,
                fullName: true,
              },
            },
            ultimaAtualizacaoPor: {
              select: {
                id: true,
                fullName: true,
              },
            },
          },
          orderBy: { date: 'desc' },
          skip,
          take: parseInt(limit),
        });

        // Calcular número total de páginas
        const pages = Math.ceil(total / parseInt(limit));

        return res.json(formatSuccessResponse({
          items: anamneses,
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages,
        }));
      } catch (error) {
        console.error("Erro ao listar anamneses:", error);
        return res.status(500).json(formatErrorResponse("Erro interno do servidor"));
      }
    }

    /**
     * Obtém uma anamnese específica pelo ID
     */
    static async get(req, res) {
      try {
        const { id } = req.params;

        const anamnese = await prisma.anamnese.findUnique({
          where: {
            id,
            companyId: req.user.companyId,
            deletedAt: null,
          },
          include: {
            person: {
              select: {
                id: true,
                fullName: true,
                profileImageUrl: true,
              },
            },
            createdBy: {
              select: {
                id: true,
                fullName: true,
              },
            },
            ultimaAtualizacaoPor: {
              select: {
                id: true,
                fullName: true,
              },
            },
          },
        });

        if (!anamnese) {
          return res.status(404).json(formatErrorResponse("Anamnese não encontrada"));
        }

        return res.json(formatSuccessResponse(anamnese));
      } catch (error) {
        console.error("Erro ao obter anamnese:", error);
        return res.status(500).json(formatErrorResponse("Erro interno do servidor"));
      }
    }

    /**
     * Atualiza uma anamnese existente
     */
    static async update(req, res) {
      try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
          return res.status(400).json({ errors: errors.array() });
        }

        const { id } = req.params;
        const {
          date,
          personId,
          diagnostico,
          cuidador,
          profissaoCuidador,
          telefone,
          historicoPersonal,
          patologiaAssociada,
          convulsoes,
          sentou,
          engatinhou,
          andou,
          estereotipiasMotoras,
          alimentacaoSolidos,
          alimentacaoLiquidos,
          alimentacaoPastosos,
          alergiasIntolerancias,
          // AVD
          avdAlimentacao,
          avdBanho,
          avdVestuario,
          avdCuidadosPessoais,
          avdSono,
          avdEsfincter,
          // Desenvolvimento da Linguagem - Não Verbal
          gestosElementares,
          naoSimbolicosConvencionais,
          simbolicosRepresentacao,
          // Desenvolvimento da Linguagem - Verbal
          verbal,
          balbucio,
          palavrasIsoladas,
          quaisPalavrasIsoladas,
          enunciadoDuasPalavras,
          frases,
          estereotipiasVocais,
          quaisEstereotipiasVocais,
          // Interação Social
          faltaExpressaoFacialAdequada,
          apresentaAtencaoDiminuida,
          apresentaPreferenciaIsolamento,
          ageComoSeFosseSurdo,
          olhaParaAlguemQueLheFala,
          olhaQuandoChamadoPeloNome,
          fazPedidoItensInteresse,
          realizaImitacao,
          brincaAdequadamenteBrinquedo,
          preferenciasObjetosEspecificos,
          apresentaAversoes,
          autoEstimulacao,
          apresentaAutoAgressaoHeteroAgressao,
          apresentaBirrasIrritabilidade,
          apresentaManiasRituais,
          // Escola
          estuda,
          nomeEscola,
          serie,
          escolaRegular,
          professorApoio,
          // Outros
          outroCasoFamilia,
          outrosCasosDetalhamento,
          terapias,
          expectativasFamilia,
        } = req.body;

        // Verificar se a anamnese existe
        const existingAnamnese = await prisma.anamnese.findUnique({
          where: {
            id,
            companyId: req.user.companyId,
            deletedAt: null,
          },
        });

        if (!existingAnamnese) {
          return res.status(404).json(formatErrorResponse("Anamnese não encontrada"));
        }

        // Atualizar anamnese
        const updatedAnamnese = await prisma.anamnese.update({
          where: { id },
          data: {
            date: date ? new Date(date) : undefined,
            personId,
            diagnostico,
            cuidador,
            profissaoCuidador,
            telefone,
            historicoPersonal,
            patologiaAssociada,
            convulsoes,
            sentou,
            engatinhou,
            andou,
            estereotipiasMotoras,
            alimentacaoSolidos: alimentacaoSolidos !== undefined ? alimentacaoSolidos : undefined,
            alimentacaoLiquidos: alimentacaoLiquidos !== undefined ? alimentacaoLiquidos : undefined,
            alimentacaoPastosos: alimentacaoPastosos !== undefined ? alimentacaoPastosos : undefined,
            alergiasIntolerancias,
            // Campos adicionais para informações médicas
            historicoMedico: req.body.historicoMedico,
            medicacoes: req.body.medicacoes,
            alergias: req.body.alergias,
            historicoFamiliar: req.body.historicoFamiliar,
            observacoesGerais: req.body.observacoesGerais,
            // AVD
            avdAlimentacao,
            avdBanho,
            avdVestuario,
            avdCuidadosPessoais,
            avdSono,
            avdEsfincter,
            // Desenvolvimento da Linguagem - Não Verbal
            gestosElementares,
            naoSimbolicosConvencionais,
            simbolicosRepresentacao,
            // Desenvolvimento da Linguagem - Verbal
            verbal,
            balbucio,
            palavrasIsoladas,
            quaisPalavrasIsoladas,
            enunciadoDuasPalavras,
            frases,
            estereotipiasVocais,
            quaisEstereotipiasVocais,
            // Interação Social
            faltaExpressaoFacialAdequada,
            apresentaAtencaoDiminuida,
            apresentaPreferenciaIsolamento,
            ageComoSeFosseSurdo,
            olhaParaAlguemQueLheFala,
            olhaQuandoChamadoPeloNome,
            fazPedidoItensInteresse,
            realizaImitacao,
            brincaAdequadamenteBrinquedo,
            preferenciasObjetosEspecificos,
            apresentaAversoes,
            autoEstimulacao: autoEstimulacao !== undefined ? autoEstimulacao : undefined,
            apresentaAutoAgressaoHeteroAgressao: apresentaAutoAgressaoHeteroAgressao !== undefined ? apresentaAutoAgressaoHeteroAgressao : undefined,
            apresentaBirrasIrritabilidade: apresentaBirrasIrritabilidade !== undefined ? apresentaBirrasIrritabilidade : undefined,
            apresentaManiasRituais: apresentaManiasRituais !== undefined ? apresentaManiasRituais : undefined,
            // Escola
            estuda: estuda !== undefined ? estuda : undefined,
            nomeEscola,
            serie,
            escolaRegular: escolaRegular !== undefined ? escolaRegular : undefined,
            professorApoio: professorApoio !== undefined ? professorApoio : undefined,
            // Outros
            outroCasoFamilia: outroCasoFamilia !== undefined ? outroCasoFamilia : undefined,
            outrosCasosDetalhamento,
            terapias,
            expectativasFamilia,
            // Campos para controle de versão
            versao: {
              increment: 1
            },
            ultimaAtualizacaoPorId: req.user.id,
          },
          include: {
            person: {
              select: {
                id: true,
                fullName: true,
                profileImageUrl: true,
              },
            },
            createdBy: {
              select: {
                id: true,
                fullName: true,
              },
            },
            ultimaAtualizacaoPor: {
              select: {
                id: true,
                fullName: true,
              },
            },
          },
        });

        return res.json(formatSuccessResponse(updatedAnamnese));
      } catch (error) {
        console.error("Erro ao atualizar anamnese:", error);
        return res.status(500).json(formatErrorResponse("Erro interno do servidor"));
      }
    }

    /**
     * Alterna o status ativo/inativo de uma anamnese
     */
    static async toggleStatus(req, res) {
      try {
        const { id } = req.params;

        // Verificar se a anamnese existe
        const anamnese = await prisma.anamnese.findUnique({
          where: {
            id,
            companyId: req.user.companyId,
            deletedAt: null,
          },
        });

        if (!anamnese) {
          return res.status(404).json(formatErrorResponse("Anamnese não encontrada"));
        }

        // Alternar status
        const updatedAnamnese = await prisma.anamnese.update({
          where: { id },
          data: { active: !anamnese.active },
        });

        return res.json(formatSuccessResponse(updatedAnamnese));
      } catch (error) {
        console.error("Erro ao alternar status da anamnese:", error);
        return res.status(500).json(formatErrorResponse("Erro interno do servidor"));
      }
    }

    /**
     * Exclui uma anamnese (soft delete)
     */
    static async delete(req, res) {
      try {
        const { id } = req.params;

        // Verificar se a anamnese existe
        const anamnese = await prisma.anamnese.findUnique({
          where: {
            id,
            companyId: req.user.companyId,
            deletedAt: null,
          },
        });

        if (!anamnese) {
          return res.status(404).json(formatErrorResponse("Anamnese não encontrada"));
        }

        // Soft delete
        await prisma.anamnese.update({
          where: { id },
          data: { deletedAt: new Date() },
        });

        return res.json(formatSuccessResponse({ message: "Anamnese excluída com sucesso" }));
      } catch (error) {
        console.error("Erro ao excluir anamnese:", error);
        return res.status(500).json(formatErrorResponse("Erro interno do servidor"));
      }
    }
  },
  createAnamneseValidation,
  updateAnamneseValidation,
};
