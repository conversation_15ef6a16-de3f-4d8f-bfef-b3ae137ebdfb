/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       required:
 *         - login
 *         - email
 *         - fullName
 *         - password
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do usuário
 *           example: "550e8400-e29b-41d4-a716-************"
 *         login:
 *           type: string
 *           description: Nome de login do usuário
 *           example: "joao.silva"
 *         email:
 *           type: string
 *           format: email
 *           description: Email do usuário
 *           example: "<EMAIL>"
 *         fullName:
 *           type: string
 *           description: Nome completo do usuário
 *           example: "João Silva"
 *         cpf:
 *           type: string
 *           description: CPF do usuário (sem formatação)
 *           example: "12345678900"
 *         cnpj:
 *           type: string
 *           description: CNPJ do usuário (sem formatação)
 *           example: "12345678000199"
 *         birthDate:
 *           type: string
 *           format: date
 *           description: Data de nascimento do usuário
 *           example: "1990-01-01"
 *         password:
 *           type: string
 *           format: password
 *           description: Sen<PERSON> do usuário (mínimo 6 caracteres)
 *           example: "senha123"
 *         address:
 *           type: string
 *           description: Endereço do usuário
 *           example: "Rua Exemplo, 123"
 *         phone:
 *           type: string
 *           description: Telefone do usuário
 *           example: "11999998888"
 *         modules:
 *           type: array
 *           items:
 *             type: string
 *             enum: [ADMIN, RH, FINANCIAL, SCHEDULING, BASIC]
 *           description: Módulos de acesso do usuário
 *           example: ["BASIC", "SCHEDULING"]
 *         role:
 *           type: string
 *           enum: [SYSTEM_ADMIN, COMPANY_ADMIN, EMPLOYEE]
 *           description: Papel do usuário no sistema
 *           example: "EMPLOYEE"
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *           description: Permissões específicas do usuário
 *           example: []
 *         active:
 *           type: boolean
 *           description: Status do usuário (ativo/inativo)
 *           example: true
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa à qual o usuário pertence
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação do usuário
 *           example: "2023-01-01T00:00:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Data da última atualização do usuário
 *           example: "2023-01-01T00:00:00Z"
 *         createdById:
 *           type: string
 *           format: uuid
 *           description: ID do usuário que criou este usuário
 *           example: "550e8400-e29b-41d4-a716-************"
 *
 *     UserResponse:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         login:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         fullName:
 *           type: string
 *         cpf:
 *           type: string
 *         cnpj:
 *           type: string
 *         birthDate:
 *           type: string
 *           format: date
 *         address:
 *           type: string
 *         phone:
 *           type: string
 *         modules:
 *           type: array
 *           items:
 *             type: string
 *         role:
 *           type: string
 *         permissions:
 *           type: array
 *           items:
 *             type: string
 *         active:
 *           type: boolean
 *         companyId:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     Client:
 *       type: object
 *       required:
 *         - login
 *         - email
 *         - fullName
 *         - password
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do cliente
 *           example: "550e8400-e29b-41d4-a716-************"
 *         login:
 *           type: string
 *           description: Nome de login do cliente
 *           example: "maria.santos"
 *         email:
 *           type: string
 *           format: email
 *           description: Email do cliente
 *           example: "<EMAIL>"
 *         fullName:
 *           type: string
 *           description: Nome completo do cliente
 *           example: "Maria Santos"
 *         cpf:
 *           type: string
 *           description: CPF do cliente (sem formatação)
 *           example: "98765432100"
 *         cnpj:
 *           type: string
 *           description: CNPJ do cliente (sem formatação)
 *           example: "98765432000199"
 *         birthDate:
 *           type: string
 *           format: date
 *           description: Data de nascimento do cliente
 *           example: "1985-05-15"
 *         password:
 *           type: string
 *           format: password
 *           description: Senha do cliente (mínimo 6 caracteres)
 *           example: "senha456"
 *         address:
 *           type: string
 *           description: Endereço do cliente
 *           example: "Av. Exemplo, 456"
 *         phone:
 *           type: string
 *           description: Telefone do cliente
 *           example: "11988887777"
 *         active:
 *           type: boolean
 *           description: Status do cliente (ativo/inativo)
 *           example: true
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa à qual o cliente pertence
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdById:
 *           type: string
 *           format: uuid
 *           description: ID do usuário que criou este cliente
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação do cliente
 *           example: "2023-02-01T00:00:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Data da última atualização do cliente
 *           example: "2023-02-01T00:00:00Z"
 *
 *     ClientResponse:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         login:
 *           type: string
 *         email:
 *           type: string
 *           format: email
 *         fullName:
 *           type: string
 *         cpf:
 *           type: string
 *         cnpj:
 *           type: string
 *         birthDate:
 *           type: string
 *           format: date
 *         address:
 *           type: string
 *         phone:
 *           type: string
 *         active:
 *           type: boolean
 *         companyId:
 *           type: string
 *           format: uuid
 *         createdAt:
 *           type: string
 *           format: date-time
 *         updatedAt:
 *           type: string
 *           format: date-time
 *
 *     Scheduling:
 *       type: object
 *       required:
 *         - userId
 *         - clientId
 *         - creatorId
 *         - locationId
 *         - title
 *         - startDate
 *         - endDate
 *         - serviceTypeId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do agendamento
 *           example: "550e8400-e29b-41d4-a716-************"
 *         userId:
 *           type: string
 *           format: uuid
 *           description: ID do profissional responsável
 *           example: "550e8400-e29b-41d4-a716-************"
 *         clientId:
 *           type: string
 *           format: uuid
 *           description: ID do cliente
 *           example: "550e8400-e29b-41d4-a716-************"
 *         creatorId:
 *           type: string
 *           format: uuid
 *           description: ID do usuário que criou o agendamento
 *           example: "550e8400-e29b-41d4-a716-************"
 *         locationId:
 *           type: string
 *           format: uuid
 *           description: ID do local
 *           example: "550e8400-e29b-41d4-a716-************"
 *         title:
 *           type: string
 *           description: Título do agendamento
 *           example: "Consulta de Rotina"
 *         description:
 *           type: string
 *           description: Descrição do agendamento
 *           example: "Consulta para acompanhamento e renovação de prescrições"
 *         startDate:
 *           type: string
 *           format: date-time
 *           description: Data e hora de início
 *           example: "2023-03-15T14:00:00Z"
 *         endDate:
 *           type: string
 *           format: date-time
 *           description: Data e hora de término
 *           example: "2023-03-15T15:00:00Z"
 *         serviceTypeId:
 *           type: string
 *           format: uuid
 *           description: ID do tipo de serviço
 *           example: "550e8400-e29b-41d4-a716-************"
 *         insuranceId:
 *           type: string
 *           format: uuid
 *           description: ID do convênio (opcional)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         status:
 *           type: string
 *           enum: [PENDING, CONFIRMED, CANCELLED, COMPLETED]
 *           description: Status do agendamento
 *           example: "PENDING"
 *         recurrenceId:
 *           type: string
 *           format: uuid
 *           description: ID da recorrência (se for parte de uma série)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação do agendamento
 *           example: "2023-03-01T10:30:00Z"
 *
 *     SchedulingResponse:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         userId:
 *           type: string
 *           format: uuid
 *         clientId:
 *           type: string
 *           format: uuid
 *         locationId:
 *           type: string
 *           format: uuid
 *         title:
 *           type: string
 *         description:
 *           type: string
 *         startDate:
 *           type: string
 *           format: date-time
 *         endDate:
 *           type: string
 *           format: date-time
 *         serviceTypeId:
 *           type: string
 *           format: uuid
 *         status:
 *           type: string
 *           enum: [PENDING, CONFIRMED, CANCELLED, COMPLETED]
 *         provider:
 *           type: object
 *           properties:
 *             fullName:
 *               type: string
 *         client:
 *           type: object
 *           properties:
 *             fullName:
 *               type: string
 *         location:
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *         serviceType:
 *           type: object
 *           properties:
 *             name:
 *               type: string
 *
 *     Recurrence:
 *       type: object
 *       required:
 *         - title
 *         - clientId
 *         - userId
 *         - locationId
 *         - serviceTypeId
 *         - recurrenceType
 *         - patterns
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único da recorrência
 *           example: "550e8400-e29b-41d4-a716-************"
 *         title:
 *           type: string
 *           description: Título da recorrência
 *           example: "Fisioterapia Semanal"
 *         description:
 *           type: string
 *           description: Descrição da recorrência
 *           example: "Sessões semanais de fisioterapia"
 *         clientId:
 *           type: string
 *           format: uuid
 *           description: ID do cliente
 *           example: "550e8400-e29b-41d4-a716-************"
 *         userId:
 *           type: string
 *           format: uuid
 *           description: ID do profissional responsável
 *           example: "550e8400-e29b-41d4-a716-************"
 *         locationId:
 *           type: string
 *           format: uuid
 *           description: ID do local
 *           example: "550e8400-e29b-41d4-a716-************"
 *         serviceTypeId:
 *           type: string
 *           format: uuid
 *           description: ID do tipo de serviço
 *           example: "550e8400-e29b-41d4-a716-************"
 *         insuranceId:
 *           type: string
 *           format: uuid
 *           description: ID do convênio (opcional)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         recurrenceType:
 *           type: string
 *           enum: [OCCURRENCES, END_DATE]
 *           description: Tipo de recorrência (número de ocorrências ou data final)
 *           example: "OCCURRENCES"
 *         numberOfOccurrences:
 *           type: integer
 *           description: Número de ocorrências (se recorrenceType for OCCURRENCES)
 *           example: 10
 *         endDate:
 *           type: string
 *           format: date-time
 *           description: Data final (se recorrenceType for END_DATE)
 *           example: "2023-12-31T23:59:59Z"
 *         active:
 *           type: boolean
 *           description: Status da recorrência (ativa/inativa)
 *           example: true
 *         createdById:
 *           type: string
 *           format: uuid
 *           description: ID do usuário que criou a recorrência
 *           example: "550e8400-e29b-41d4-a716-************"
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação da recorrência
 *           example: "2023-04-01T09:00:00Z"
 *         patterns:
 *           type: array
 *           items:
 *             type: object
 *             required:
 *               - dayOfWeek
 *               - startTime
 *               - endTime
 *             properties:
 *               dayOfWeek:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 6
 *                 description: Dia da semana (0=Domingo, 6=Sábado)
 *                 example: 1
 *               startTime:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                 description: Hora de início (formato HH:MM)
 *                 example: "10:00"
 *               endTime:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                 description: Hora de término (formato HH:MM)
 *                 example: "11:00"
 *
 *     RecurrenceResponse:
 *       type: object
 *       properties:
 *         recurrence:
 *           $ref: '#/components/schemas/Recurrence'
 *         schedulings:
 *           type: array
 *           items:
 *             $ref: '#/components/schemas/SchedulingResponse'
 *
 *     Location:
 *       type: object
 *       required:
 *         - name
 *         - address
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do local
 *           example: "550e8400-e29b-41d4-a716-************"
 *         name:
 *           type: string
 *           description: Nome do local
 *           example: "Clínica Central"
 *         address:
 *           type: string
 *           description: Endereço do local
 *           example: "Rua das Clínicas, 100"
 *         phone:
 *           type: string
 *           description: Telefone do local
 *           example: "1133334444"
 *         active:
 *           type: boolean
 *           description: Status do local (ativo/inativo)
 *           example: true
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação do local
 *           example: "2023-01-15T12:00:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Data da última atualização do local
 *           example: "2023-01-15T12:00:00Z"
 *
 *     ServiceType:
 *       type: object
 *       required:
 *         - name
 *         - value
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do tipo de serviço
 *           example: "550e8400-e29b-41d4-a716-************"
 *         name:
 *           type: string
 *           description: Nome do tipo de serviço
 *           example: "Consulta Médica"
 *         value:
 *           type: number
 *           format: decimal
 *           description: Valor do serviço
 *           example: 150.00
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa
 *           example: "550e8400-e29b-41d4-a716-************"
 *
 *     Insurance:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do convênio
 *           example: "550e8400-e29b-41d4-a716-************"
 *         name:
 *           type: string
 *           description: Nome do convênio
 *           example: "Unimed"
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa
 *           example: "550e8400-e29b-41d4-a716-************"
 *
 *     ClientInsurance:
 *       type: object
 *       required:
 *         - clientId
 *         - insuranceId
 *       properties:
 *         clientId:
 *           type: string
 *           format: uuid
 *           description: ID do cliente
 *           example: "550e8400-e29b-41d4-a716-************"
 *         insuranceId:
 *           type: string
 *           format: uuid
 *           description: ID do convênio
 *           example: "550e8400-e29b-41d4-a716-************"
 *
 *     Document:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do documento
 *           example: "550e8400-e29b-41d4-a716-************"
 *         filename:
 *           type: string
 *           description: Nome original do arquivo
 *           example: "ficha-cadastral.pdf"
 *         path:
 *           type: string
 *           description: Caminho do arquivo no servidor
 *           example: "documents/user-123/ficha-cadastral-abc123.pdf"
 *         type:
 *           type: string
 *           description: Tipo do documento
 *           example: "CPF"
 *         userId:
 *           type: string
 *           format: uuid
 *           description: ID do usuário relacionado (opcional)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         clientId:
 *           type: string
 *           format: uuid
 *           description: ID do cliente relacionado (opcional)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa relacionada (opcional)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação do documento
 *           example: "2023-05-01T14:30:00Z"
 *
 *     WorkingHours:
 *       type: object
 *       required:
 *         - userId
 *         - dayOfWeek
 *         - startTime
 *         - endTime
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único do horário de trabalho
 *           example: "550e8400-e29b-41d4-a716-************"
 *         userId:
 *           type: string
 *           format: uuid
 *           description: ID do usuário (profissional)
 *           example: "550e8400-e29b-41d4-a716-************"
 *         dayOfWeek:
 *           type: integer
 *           minimum: 0
 *           maximum: 6
 *           description: Dia da semana (0=Domingo, 6=Sábado)
 *           example: 1
 *         startTime:
 *           type: string
 *           pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *           description: Hora de início (formato HH:MM)
 *           example: "08:00"
 *         endTime:
 *           type: string
 *           pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *           description: Hora de término (formato HH:MM)
 *           example: "18:00"
 *         breakStart:
 *           type: string
 *           pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *           description: Hora de início do intervalo (formato HH:MM)
 *           example: "12:00"
 *         breakEnd:
 *           type: string
 *           pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *           description: Hora de término do intervalo (formato HH:MM)
 *           example: "13:00"
 *         isActive:
 *           type: boolean
 *           description: Status do horário (ativo/inativo)
 *           example: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação do horário
 *           example: "2023-01-10T10:00:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Data da última atualização do horário
 *           example: "2023-01-10T10:00:00Z"
 *     Branch:
 *       type: object
 *       required:
 *         - name
 *         - address
 *         - companyId
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único da unidade
 *           example: "550e8400-e29b-41d4-a716-************"
 *         name:
 *           type: string
 *           description: Nome da unidade
 *           example: "Filial Centro"
 *         code:
 *           type: string
 *           description: Código único da unidade (dentro da empresa)
 *           example: "FL-001"
 *         description:
 *           type: string
 *           description: Descrição da unidade
 *           example: "Unidade principal localizada no centro da cidade"
 *         address:
 *           type: string
 *           description: Endereço completo da unidade
 *           example: "Av. Principal, 1000, Centro"
 *         city:
 *           type: string
 *           description: Cidade
 *           example: "São Paulo"
 *         state:
 *           type: string
 *           description: Estado
 *           example: "SP"
 *         postalCode:
 *           type: string
 *           description: CEP
 *           example: "01234-567"
 *         phone:
 *           type: string
 *           description: Telefone da unidade
 *           example: "1122223333"
 *         email:
 *           type: string
 *           format: email
 *           description: Email de contato da unidade
 *           example: "<EMAIL>"
 *         active:
 *           type: boolean
 *           description: Status da unidade (ativa/inativa)
 *           example: true
 *         isHeadquarters:
 *           type: boolean
 *           description: Indica se esta é a matriz da empresa
 *           example: false
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa à qual a unidade pertence
 *           example: "550e8400-e29b-41d4-a716-************"
 *         branchId:
 *           type: string
 *           format: uuid
 *           description: ID da unidade à qual o local pertence
 *           example: "550e8400-e29b-41d4-a716-************"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação da unidade
 *           example: "2023-01-15T12:00:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Data da última atualização da unidade
 *           example: "2023-01-15T12:00:00Z"
 *         deletedAt:
 *           type: string
 *           format: date-time
 *           nullable: true
 *           description: Data de exclusão (soft delete)
 *           example: null
 *
 *     Company:
 *       type: object
 *       required:
 *         - name
 *         - cnpj
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único da empresa
 *           example: "550e8400-e29b-41d4-a716-************"
 *         name:
 *           type: string
 *           description: Nome da empresa
 *           example: "Clínica Exemplo Ltda"
 *         tradingName:
 *           type: string
 *           description: Nome fantasia
 *           example: "Clínica Exemplo"
 *         legalName:
 *           type: string
 *           description: Razão social
 *           example: "Clínica Exemplo Serviços Médicos Ltda"
 *         industry:
 *           type: string
 *           description: Setor de atuação
 *           example: "Saúde"
 *         contactEmail:
 *           type: string
 *           format: email
 *           description: Email de contato
 *           example: "<EMAIL>"
 *         cnpj:
 *           type: string
 *           description: CNPJ da empresa (sem formatação)
 *           example: "12345678000199"
 *         phone:
 *           type: string
 *           description: Telefone principal
 *           example: "1122223333"
 *         phone2:
 *           type: string
 *           description: Telefone secundário
 *           example: "1122224444"
 *         address:
 *           type: string
 *           description: Endereço
 *           example: "Av. Principal, 1000"
 *         city:
 *           type: string
 *           description: Cidade
 *           example: "São Paulo"
 *         state:
 *           type: string
 *           description: Estado
 *           example: "SP"
 *         postalCode:
 *           type: string
 *           description: CEP
 *           example: "01234567"
 *         website:
 *           type: string
 *           description: Website
 *           example: "https://www.clinicaexemplo.com.br"
 *         primaryColor:
 *           type: string
 *           description: Cor primária (branding)
 *           example: "#4682B4"
 *         secondaryColor:
 *           type: string
 *           description: Cor secundária (branding)
 *           example: "#FFFFFF"
 *         description:
 *           type: string
 *           description: Descrição da empresa
 *           example: "Clínica especializada em atendimento multidisciplinar"
 *         active:
 *           type: boolean
 *           description: Status da empresa (ativa/inativa)
 *           example: true
 *         licenseValidUntil:
 *           type: string
 *           format: date-time
 *           description: Data de validade da licença
 *           example: "2024-12-31T23:59:59Z"
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação da empresa
 *           example: "2023-01-01T00:00:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Data da última atualização da empresa
 *           example: "2023-01-01T00:00:00Z"
 *
 *     EmailConfig:
 *       type: object
 *       required:
 *         - companyId
 *         - smtpHost
 *         - smtpPort
 *         - smtpUser
 *         - smtpPassword
 *         - emailFromName
 *         - emailFromAddress
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *           description: ID único da configuração de email
 *           example: "550e8400-e29b-41d4-a716-************"
 *         companyId:
 *           type: string
 *           format: uuid
 *           description: ID da empresa
 *           example: "550e8400-e29b-41d4-a716-************"
 *         smtpHost:
 *           type: string
 *           description: Servidor SMTP
 *           example: "smtp.gmail.com"
 *         smtpPort:
 *           type: integer
 *           description: Porta SMTP
 *           example: 587
 *         smtpSecure:
 *           type: boolean
 *           description: Usar conexão segura (TLS/SSL)
 *           example: false
 *         smtpUser:
 *           type: string
 *           description: Usuário SMTP
 *           example: "<EMAIL>"
 *         smtpPassword:
 *           type: string
 *           format: password
 *           description: Senha SMTP
 *           example: "senha123"
 *         emailFromName:
 *           type: string
 *           description: Nome do remetente
 *           example: "Sistema Clínica Exemplo"
 *         emailFromAddress:
 *           type: string
 *           format: email
 *           description: Email do remetente
 *           example: "<EMAIL>"
 *         active:
 *           type: boolean
 *           description: Status da configuração (ativa/inativa)
 *           example: true
 *         createdAt:
 *           type: string
 *           format: date-time
 *           description: Data de criação da configuração
 *           example: "2023-01-05T15:00:00Z"
 *         updatedAt:
 *           type: string
 *           format: date-time
 *           description: Data da última atualização da configuração
 *           example: "2023-01-05T15:00:00Z"
 *
 *     Auth:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: Email do usuário
 *           example: "<EMAIL>"
 *         password:
 *           type: string
 *           format: password
 *           description: Senha do usuário
 *           example: "senha123"
 *
 *     AuthResponse:
 *       type: object
 *       properties:
 *         user:
 *           $ref: '#/components/schemas/UserResponse'
 *         token:
 *           type: string
 *           description: Token JWT para autenticação
 *           example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *
 *     PaginatedResponse:
 *       type: object
 *       properties:
 *         total:
 *           type: integer
 *           description: Total de registros
 *           example: 100
 *         pages:
 *           type: integer
 *           description: Total de páginas
 *           example: 10
 */
