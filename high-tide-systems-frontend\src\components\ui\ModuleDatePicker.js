'use client';

import React, { forwardRef } from 'react';
import { Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Componente de seleção de data que se adapta à cor do módulo
 *
 * @param {Object} props - Propriedades do componente
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {string} props.value - Valor do input (formato YYYY-MM-DD)
 * @param {Function} props.onChange - Função chamada quando o valor muda
 * @param {boolean} props.disabled - Se o input está desabilitado
 * @param {boolean} props.required - Se o input é obrigatório
 * @param {string} props.name - Nome do input
 * @param {string} props.id - ID do input
 * @param {boolean} props.error - Se o input tem erro
 * @param {string} props.errorMessage - Mensagem de erro
 * @param {string} props.className - Classes adicionais
 */
const ModuleDatePicker = forwardRef(({
  moduleColor = 'default',
  value,
  onChange,
  disabled = false,
  required = false,
  name,
  id,
  error = false,
  errorMessage,
  className = '',
  ...rest
}, ref) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      borderColor: 'border-neutral-300 dark:border-neutral-600',
      focusRing: 'focus:ring-blue-500 dark:focus:ring-blue-400',
      iconColor: 'text-neutral-500 dark:text-neutral-400',
      hoverBorder: 'hover:border-neutral-400 dark:hover:border-neutral-500',
    },
    people: {
      borderColor: 'border-module-people-border dark:border-module-people-border-dark',
      focusRing: 'focus:ring-module-people-border dark:focus:ring-module-people-border-dark',
      iconColor: 'text-module-people-icon dark:text-module-people-icon-dark',
      hoverBorder: 'hover:border-module-people-border dark:hover:border-module-people-border-dark',
    },
    scheduler: {
      borderColor: 'border-module-scheduler-border dark:border-module-scheduler-border-dark',
      focusRing: 'focus:ring-module-scheduler-border dark:focus:ring-module-scheduler-border-dark',
      iconColor: 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark',
      hoverBorder: 'hover:border-module-scheduler-border dark:hover:border-module-scheduler-border-dark',
    },
    admin: {
      borderColor: 'border-module-admin-border dark:border-module-admin-border-dark',
      focusRing: 'focus:ring-module-admin-border dark:focus:ring-module-admin-border-dark',
      iconColor: 'text-module-admin-icon dark:text-module-admin-icon-dark',
      hoverBorder: 'hover:border-module-admin-border dark:hover:border-module-admin-border-dark',
    },
    financial: {
      borderColor: 'border-module-financial-border dark:border-module-financial-border-dark',
      focusRing: 'focus:ring-module-financial-border dark:focus:ring-module-financial-border-dark',
      iconColor: 'text-module-financial-icon dark:text-module-financial-icon-dark',
      hoverBorder: 'hover:border-module-financial-border dark:hover:border-module-financial-border-dark',
    },
  };

  // Obter as cores com base no módulo
  const colors = moduleColors[moduleColor] || moduleColors.default;

  // Classes para o input
  const inputClasses = cn(
    "w-full rounded-md border px-3 py-2 text-sm focus:outline-none focus:ring-1 transition-colors",
    "bg-white dark:bg-gray-800 text-neutral-900 dark:text-neutral-100",
    colors.borderColor,
    colors.focusRing,
    colors.hoverBorder,
    {
      "opacity-50 cursor-not-allowed": disabled,
      "border-red-500 dark:border-red-400 focus:ring-red-500 dark:focus:ring-red-400": error,
    },
    className
  );

  return (
    <div className="relative">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <Calendar className={`h-4 w-4 ${colors.iconColor}`} />
      </div>

      <input
        ref={ref}
        type="date"
        value={value}
        onChange={onChange}
        disabled={disabled}
        required={required}
        name={name}
        id={id}
        className={cn(inputClasses, "pl-10")}
        {...rest}
      />

      {error && errorMessage && (
        <p className="mt-1 text-xs text-red-600 dark:text-red-400">
          {errorMessage}
        </p>
      )}
    </div>
  );
});

ModuleDatePicker.displayName = 'ModuleDatePicker';

export default ModuleDatePicker;
