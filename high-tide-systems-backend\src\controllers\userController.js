// src/controllers/userController.js
const bcrypt = require("bcryptjs");
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");
const WorkingHoursHelper = require("../utils/workingHoursHelper");
const BranchWorkingHoursHelper = require("../utils/branchWorkingHoursHelper");
const path = require("path");
const fs = require("fs").promises;
const { formatSuccessResponse, formatErrorResponse } = require('../utils/responseUtil');
const { getAllPermissions, getAllModules } = require('../utils/permissionHelper');

// Caminho base para uploads
const UPLOAD_PATH = process.env.NODE_ENV === 'production'
  ? path.resolve("/tmp/uploads")
  : path.resolve("./uploads");

console.log('[userController] Caminho base para uploads:', UPLOAD_PATH);

// Garante que o diretório de uploads existe
const ensureUploadDirectoryExists = async () => {
  try {
    await fs.access(UPLOAD_PATH);
  } catch (error) {
    await fs.mkdir(UPLOAD_PATH, { recursive: true });
  }

  // Garantir que o diretório de imagens de perfil existe
  const profileImagesPath = path.join(UPLOAD_PATH, "user-profile-images");
  try {
    await fs.access(profileImagesPath);
  } catch (error) {
    await fs.mkdir(profileImagesPath, { recursive: true });
  }
};

// Chamar a função para garantir que os diretórios existam
ensureUploadDirectoryExists().catch(err => {
  console.error('[userController] Erro ao criar diretórios de upload:', err);
});

// Validações
const createUserValidation = [
  body("login").notEmpty().withMessage("Login é obrigatório"),
  body("email").isEmail().withMessage("Email inválido"),
  body("fullName").notEmpty().withMessage("Nome completo é obrigatório"),
  body("password")
    .isLength({ min: 6 })
    .withMessage("Senha deve ter no mínimo 6 caracteres"),
  body("cpf")
    .optional()
    .custom((value, { req }) => {
      if (value && req.body.cnpj) {
        throw new Error("Não é possível fornecer CPF e CNPJ simultaneamente");
      }
      if (value && !/^\d{11}$/.test(value)) {
        throw new Error("CPF inválido");
      }
      return true;
    }),
  body("cnpj")
    .optional()
    .custom((value, { req }) => {
      if (value && req.body.cpf) {
        throw new Error("Não é possível fornecer CPF e CNPJ simultaneamente");
      }
      if (value && !/^\d{14}$/.test(value)) {
        throw new Error("CNPJ inválido");
      }
      return true;
    }),
  // Validação para companyId quando fornecido explicitamente
  body("companyId")
    .optional()
    .custom(async (value, { req }) => {
      // Apenas verifica se existe quando fornecido
      if (value) {
        const company = await prisma.company.findUnique({
          where: { id: value },
        });
        if (!company) {
          throw new Error("Empresa não encontrada");
        }
      }
      return true;
    }),
];

const updateModulesValidation = [
  body("modules")
    .isArray()
    .withMessage("Módulos deve ser um array")
    .custom((value) => {
      const validModules = ["ADMIN", "RH", "FINANCIAL", "SCHEDULING", "BASIC"];
      const allValid = value.every((module) => validModules.includes(module));
      if (!allValid) {
        throw new Error("Módulo(s) inválido(s)");
      }
      return true;
    }),
];

const updatePermissionsValidation = [
  body("permissions")
    .isArray()
    .withMessage("Permissões deve ser um array")
    .custom((value) => {
      // Adicione validação personalizada aqui se necessário
      // Por exemplo, verificar se as permissões seguem o formato correto
      return true;
    }),
];

const updateRoleValidation = [
  body("role")
    .isIn(["SYSTEM_ADMIN", "COMPANY_ADMIN", "EMPLOYEE"])
    .withMessage(
      "Papel inválido. Deve ser SYSTEM_ADMIN, COMPANY_ADMIN ou EMPLOYEE"
    ),
];

// Validação para atualização de preferências de módulos
const updateModulePreferencesValidation = [
  body("modulePreferences")
    .notEmpty()
    .withMessage("Preferências de módulos são obrigatórias")
    .custom((value) => {
      if (typeof value !== 'object' || Array.isArray(value) || value === null) {
        throw new Error('Preferências de módulos devem ser um objeto');
      }
      return true;
    })
];

class UserController {
  /**
   * Upload profile image for a user
   */
  static async uploadProfileImage(req, res) {
    try {
      console.log('[userController] Upload de imagem de perfil iniciado');
      console.log('[userController] Headers:', req.headers);
      console.log('[userController] Params:', req.params);
      console.log('[userController] User:', req.user?.id);

      const { id } = req.params;

      // Verificar se o usuário existe
      const user = await prisma.user.findUnique({
        where: { id }
      });

      if (!user) {
        console.log(`[userController] Usuário com ID ${id} não encontrado`);
        return res.status(404).json({ message: 'Usuário não encontrado' });
      }

      console.log('[userController] Usuário encontrado:', user.fullName);

      // Verificar se um arquivo foi enviado
      if (!req.file) {
        console.log('[userController] Nenhum arquivo enviado');
        return res.status(400).json({ message: 'Nenhuma imagem enviada' });
      }

      console.log('[userController] Arquivo recebido:', req.file);
      console.log('[userController] Caminho do arquivo:', req.file.path);

      // Se o usuário já tinha uma imagem de perfil, excluir a antiga
      if (user.profileImageUrl) {
        try {
          console.log('[userController] Usuário já possui imagem de perfil:', user.profileImageUrl);
          const oldImagePath = path.join(UPLOAD_PATH, user.profileImageUrl);
          console.log('[userController] Caminho completo da imagem antiga:', oldImagePath);

          await fs.access(oldImagePath); // Verifica se o arquivo existe
          console.log('[userController] Arquivo antigo encontrado, excluindo...');
          await fs.unlink(oldImagePath); // Exclui o arquivo
          console.log('[userController] Arquivo antigo excluído com sucesso');
        } catch (error) {
          console.log('[userController] Arquivo antigo não encontrado ou erro ao excluir:', error);
          // Continua mesmo se não conseguir excluir o arquivo antigo
        }
      } else {
        console.log('[userController] Usuário não possui imagem de perfil anterior');
      }

      // Calcular o caminho relativo para armazenar no banco de dados
      const relativePath = path.relative(UPLOAD_PATH, req.file.path);
      console.log('[userController] Caminho relativo para armazenar no banco:', relativePath);

      // Atualizar o usuário com o novo caminho da imagem
      await prisma.user.update({
        where: { id },
        data: {
          profileImageUrl: relativePath
        }
      });

      console.log('[userController] Usuário atualizado com sucesso');
      console.log('[userController] Enviando resposta de sucesso');

      // Construir a URL completa para a imagem
      const baseUrl = `${req.protocol}://${req.get('host')}`;
      const fullImageUrl = `${baseUrl}/uploads/${relativePath}`;
      console.log('[userController] URL completa da imagem:', fullImageUrl);

      res.status(200).json({
        message: 'Imagem de perfil atualizada com sucesso',
        profileImageUrl: relativePath,
        fullImageUrl: fullImageUrl,
        filename: path.basename(req.file.path)
      });
    } catch (error) {
      console.error('[userController] Erro ao fazer upload de imagem de perfil:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Get profile image for a user
   */
  static async getProfileImage(req, res) {
    try {
      console.log('[userController] Obtendo imagem de perfil');
      console.log('[userController] Params:', req.params);

      const { id } = req.params;

      // Verificar se o usuário existe
      console.log(`[userController] Buscando usuário com ID ${id}`);
      const user = await prisma.user.findUnique({
        where: { id }
      });

      if (!user) {
        console.log(`[userController] Usuário com ID ${id} não encontrado`);
        return res.status(404).json({ message: 'Usuário não encontrado' });
      }
      console.log('[userController] Usuário encontrado:', user.fullName);

      // Verificar se o usuário tem uma imagem de perfil
      if (!user.profileImageUrl) {
        console.log('[userController] Usuário não possui imagem de perfil');
        return res.status(404).json({ message: 'Usuário não possui imagem de perfil' });
      }
      console.log('[userController] URL da imagem de perfil:', user.profileImageUrl);

      // Caminho completo da imagem
      const imagePath = path.join(UPLOAD_PATH, user.profileImageUrl);
      console.log('[userController] Caminho completo da imagem:', imagePath);

      // Verificar se o arquivo existe
      try {
        console.log('[userController] Verificando se o arquivo existe...');
        await fs.access(imagePath);
        console.log('[userController] Arquivo encontrado');
      } catch (error) {
        console.log('[userController] Arquivo de imagem não encontrado:', error);
        return res.status(404).json({ message: 'Arquivo de imagem não encontrado' });
      }

      // Obter o tipo MIME com base na extensão do arquivo
      const ext = path.extname(imagePath).toLowerCase();
      let contentType = 'image/jpeg'; // Padrão

      if (ext === '.png') contentType = 'image/png';
      else if (ext === '.gif') contentType = 'image/gif';
      else if (ext === '.webp') contentType = 'image/webp';

      // Enviar o arquivo
      res.setHeader('Content-Type', contentType);
      res.setHeader('Cache-Control', 'public, max-age=86400'); // Cache por 24 horas

      // Enviar o arquivo
      console.log('[userController] Enviando arquivo...');
      res.sendFile(imagePath);
    } catch (error) {
      console.error('[userController] Erro ao buscar imagem de perfil:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Cria um novo usuário no sistema
   * Recebe dados do usuário via body
   * Retorna o usuário criado
   * Por padrão, usuário é criado com módulo BASIC
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        login,
        email,
        fullName,
        password,
        cpf,
        cnpj,
        birthDate,
        address,
        neighborhood,
        city,
        state,
        postalCode,
        phone,
        companyId,
        branchId, // ID da unidade
        professionId, // ID da profissão
        role, // Função do usuário
        modules, // Módulos de acesso
        permissions, // Permissões do usuário
      } = req.body;

      // Verificar se o usuário atual tem permissão para criar o tipo de usuário solicitado
      if (role === "SYSTEM_ADMIN" && req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message: "Apenas administradores do sistema podem criar outros administradores do sistema",
          errors: [{ param: "role", msg: "Permissão negada para criar administrador do sistema" }]
        });
      }

      // Verificar se o usuário atual tem permissão para criar administradores de empresa
      if (role === "COMPANY_ADMIN" && req.user.role !== "SYSTEM_ADMIN" && req.user.role !== "COMPANY_ADMIN") {
        return res.status(403).json({
          message: "Como funcionário, você só pode criar outros funcionários",
          errors: [{ param: "role", msg: "Funcionários só podem criar outros funcionários" }]
        });
      }

      // Determinar a empresa - se fornecida e usuário é SYSTEM_ADMIN, use-a
      // Caso contrário, use a empresa do usuário que está criando
      let userCompanyId;

      if (companyId && req.user.role === "SYSTEM_ADMIN") {
        userCompanyId = companyId;
      } else {
        userCompanyId = req.user.companyId;
      }

      // Se ainda não tiver companyId, rejeitar a requisição
      if (!userCompanyId) {
        return res.status(400).json({ message: "CompanyId é obrigatório" });
      }

      const hashedPassword = await bcrypt.hash(password, 10);

      // Verificar se a unidade existe e pertence à empresa correta
      let userBranchId = null;
      if (branchId) {
        const branch = await prisma.branch.findUnique({
          where: { id: branchId }
        });

        if (!branch) {
          return res.status(400).json({ message: "Unidade não encontrada" });
        }

        if (branch.companyId !== userCompanyId) {
          return res.status(400).json({ message: "A unidade não pertence à empresa selecionada" });
        }

        userBranchId = branchId;
      }

      // Definir módulos e permissões com base no papel do usuário
      let defaultModules = modules || ["BASIC"];
      let defaultPermissions = permissions || [];

      // Se for SYSTEM_ADMIN ou COMPANY_ADMIN, atribuir todos os módulos e permissões
      if (role === "SYSTEM_ADMIN" || role === "COMPANY_ADMIN") {
        defaultModules = getAllModules();
        defaultPermissions = getAllPermissions();
        console.log(`Usuário ${role} criado com todos os módulos e permissões`);
        console.log(`Módulos: ${defaultModules.join(', ')}`);
        console.log(`Total de permissões: ${defaultPermissions.length}`);
      }
      // Se não for admin e tiver professionId, buscar permissões padrão do grupo
      else if (professionId) {
        try {
          const profession = await prisma.profession.findUnique({
            where: { id: professionId },
            include: {
              group: true
            }
          });

          if (profession?.group) {
            // Aplicar módulos padrão do grupo, se não foram especificados explicitamente
            if (!modules) {
              defaultModules = profession.group.defaultModules || ["BASIC"];
            }

            // Aplicar permissões padrão do grupo, se não foram especificadas explicitamente
            if (!permissions) {
              defaultPermissions = profession.group.defaultPermissions || [];
            }

            console.log(`Aplicando permissões padrão do grupo de profissão: ${profession.group.name}`);
            console.log(`Módulos padrão: ${defaultModules.join(', ')}`);
            console.log(`Permissões padrão: ${defaultPermissions.join(', ')}`);
          }
        } catch (error) {
          console.error('Erro ao buscar permissões padrão do grupo de profissão:', error);
          // Não falhar a criação do usuário se não conseguir obter as permissões padrão
        }
      }

      const user = await prisma.user.create({
        data: {
          login,
          email,
          fullName,
          password: hashedPassword,
          cpf,
          cnpj,
          birthDate: birthDate ? new Date(birthDate) : null,
          address,
          neighborhood,
          city,
          state,
          postalCode,
          phone,
          professionId, // Relação com a profissão
          branchId: userBranchId, // Relação com a unidade
          role: role || "EMPLOYEE", // Função do usuário (padrão: EMPLOYEE)
          modules: defaultModules, // Módulos de acesso (padrão ou do grupo de profissão)
          permissions: defaultPermissions, // Permissões do usuário (padrão ou do grupo de profissão)
          createdById: req.user?.id,
          companyId: userCompanyId,
          WorkingHours: {
            create: WorkingHoursHelper.generateDefaultWorkingHours()
          },
        },
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          cpf: true,
          cnpj: true,
          birthDate: true,
          address: true,
          neighborhood: true,
          city: true,
          state: true,
          postalCode: true,
          phone: true,
          professionObj: true, // Adicionado novo campo
          modules: true,
          permissions: true,
          active: true,
          createdAt: true,
          companyId: true,
          role: true,
          company: {
            select: {
              id: true,
              name: true,
              tradingName: true,
              cnpj: true,
              active: true,
            },
          },
          WorkingHours: {
            select: {
              id: true,
              dayOfWeek: true,
              startTimeMinutes: true,
              endTimeMinutes: true,
              breakStartMinutes: true,
              breakEndMinutes: true,
              isActive: true,
            },
          },
        },
      });

      res.status(201).json(user);
    } catch (error) {
      console.error("Erro ao criar usuário:", error);

      // Tratamento específico para erros de restrição única (unique constraint)
      if (error.code === 'P2002') {
        const field = error.meta?.target?.[0];
        let message = "Já existe um registro com este valor";

        // Mensagens específicas para cada campo
        if (field === 'email') {
          message = "Este email já está em uso por outro usuário";
        } else if (field === 'login') {
          message = "Este login já está em uso por outro usuário";
        } else if (field === 'cpf') {
          message = "Este CPF já está cadastrado no sistema";
        } else if (field === 'cnpj') {
          message = "Este CNPJ já está cadastrado no sistema";
        }

        return res.status(400).json({
          message,
          errors: [{ param: field, msg: message }]
        });
      }

      // Para outros erros, usar o tratamento padrão
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza dados de um usuário existente
   * Permite atualização de dados básicos e senha
   * Identificação do usuário via ID na URL
   * Retorna o usuário atualizado
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const {
        fullName,
        address,
        neighborhood,
        city,
        state,
        postalCode,
        phone,
        birthDate,
        email,
        password,
        companyId,
        branchId, // ID da unidade
        professionId, // ID da profissão
      } = req.body;

      // Verificar se está tentando alterar a empresa
      if (companyId) {
        // Apenas SYSTEM_ADMIN pode alterar a empresa
        if (req.user.role !== "SYSTEM_ADMIN") {
          return res.status(403).json({
            message:
              "Apenas administradores de sistema podem alterar a empresa do usuário",
          });
        }

        // Verificar se a empresa existe
        const company = await prisma.company.findUnique({
          where: { id: companyId },
        });

        if (!company) {
          return res.status(400).json({ message: "Empresa não encontrada" });
        }
      }

      // Verificar se a unidade existe e pertence à empresa correta
      if (branchId) {
        const branch = await prisma.branch.findUnique({
          where: { id: branchId }
        });

        if (!branch) {
          return res.status(400).json({ message: "Unidade não encontrada" });
        }

        // Se estiver alterando a empresa, verificar se a unidade pertence à nova empresa
        if (companyId && req.user.role === "SYSTEM_ADMIN" && branch.companyId !== companyId) {
          return res.status(400).json({ message: "A unidade não pertence à empresa selecionada" });
        }

        // Se não estiver alterando a empresa, verificar se a unidade pertence à empresa atual do usuário
        const targetUser = await prisma.user.findUnique({
          where: { id },
          select: { companyId: true }
        });

        if (targetUser && branch.companyId !== targetUser.companyId) {
          return res.status(400).json({ message: "A unidade não pertence à empresa do usuário" });
        }
      }

      let updateData = {
        fullName,
        address,
        neighborhood,
        city,
        state,
        postalCode,
        phone,
        birthDate: birthDate ? new Date(birthDate) : undefined,
        email,
        professionId, // Relação com a profissão
        branchId, // Relação com a unidade
      };

      // Adicionar companyId se fornecido e usuário for SYSTEM_ADMIN
      if (companyId && req.user.role === "SYSTEM_ADMIN") {
        updateData.companyId = companyId;
      }

      // Verificar se houve mudança de unidade para aplicar os horários de trabalho padrão
      const oldUser = await prisma.user.findUnique({
        where: { id },
        select: {
          branchId: true,
          professionId: true,
          modules: true,
          permissions: true
        }
      });

      const branchChanged = branchId && oldUser && oldUser.branchId !== branchId;
      const professionChanged = professionId && oldUser && oldUser.professionId !== professionId;

      // Se a profissão foi alterada, verificar se deve aplicar permissões padrão
      if (professionChanged) {
        try {
          const profession = await prisma.profession.findUnique({
            where: { id: professionId },
            include: {
              group: true
            }
          });

          if (profession?.group?.defaultModules || profession?.group?.defaultPermissions) {
            console.log(`Profissão alterada. Verificando permissões padrão do grupo: ${profession.group.name}`);

            // Aplicar módulos e permissões padrão do grupo apenas se não foram especificados na requisição
            if (!req.body.modules && profession.group.defaultModules?.length > 0) {
              updateData.modules = profession.group.defaultModules;
              console.log(`Aplicando módulos padrão: ${updateData.modules.join(', ')}`);
            }

            if (!req.body.permissions && profession.group.defaultPermissions?.length > 0) {
              updateData.permissions = profession.group.defaultPermissions;
              console.log(`Aplicando permissões padrão: ${updateData.permissions.join(', ')}`);
            }
          }
        } catch (error) {
          console.error('Erro ao buscar permissões padrão do grupo de profissão:', error);
          // Não falhar a atualização do usuário se não conseguir obter as permissões padrão
        }
      }

      if (password) {
        updateData.password = await bcrypt.hash(password, 10);
      }

      const user = await prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          cpf: true,
          cnpj: true,
          birthDate: true,
          address: true,
          neighborhood: true,
          city: true,
          state: true,
          postalCode: true,
          phone: true,
          profileImageUrl: true, // Adicionado campo para imagem de perfil
          professionObj: true, // Adicionado novo campo
          modules: true,
          permissions: true,
          active: true,
          updatedAt: true,
          role: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              tradingName: true,
              cnpj: true,
              address: true,
              phone: true,
              active: true,
            },
          },
        },
      });

      // Adicionar URL completa da imagem de perfil, se existir
      if (user.profileImageUrl) {
        // Obter a URL base da API a partir do request
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        user.profileImageFullUrl = `${baseUrl}/uploads/${user.profileImageUrl}`;
      }

      // Se a unidade foi alterada e a nova unidade tem horários padrão, aplicá-los
      if (branchChanged) {
        try {
          await BranchWorkingHoursHelper.applyBranchWorkingHoursToUser(id, branchId);
        } catch (error) {
          console.error('Erro ao aplicar horários de trabalho da unidade:', error);
          // Não falhar a requisição por causa disso
        }
      }

      res.json(user);
    } catch (error) {
      console.error("Erro ao atualizar usuário:", error);

      // Tratamento específico para erros de restrição única (unique constraint)
      if (error.code === 'P2002') {
        const field = error.meta?.target?.[0];
        let message = "Já existe um registro com este valor";

        // Mensagens específicas para cada campo
        if (field === 'email') {
          message = "Este email já está em uso por outro usuário";
        } else if (field === 'login') {
          message = "Este login já está em uso por outro usuário";
        } else if (field === 'cpf') {
          message = "Este CPF já está cadastrado no sistema";
        } else if (field === 'cnpj') {
          message = "Este CNPJ já está cadastrado no sistema";
        }

        return res.status(400).json({
          message,
          errors: [{ param: field, msg: message }]
        });
      }

      // Para outros erros, usar o tratamento padrão
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza os módulos de acesso de um usuário
   * Apenas administradores podem conceder acesso de admin
   * Identificação do usuário via ID na URL
   * Retorna os novos módulos do usuário
   */
  static async updateModules(req, res) {
    try {
      const { id } = req.params;
      const { modules } = req.body;

      if (modules.includes("ADMIN")) {
        if (!req.user.modules.includes("ADMIN")) {
          return res.status(403).json({
            message:
              "Apenas administradores podem conceder acesso de administrador",
          });
        }
      }

      const user = await prisma.user.update({
        where: { id },
        data: { modules },
        select: {
          id: true,
          fullName: true,
          profileImageUrl: true, // Adicionado campo para imagem de perfil
          professionObj: true, // Adicionado novo campo
          modules: true,
          role: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              tradingName: true,
              cnpj: true,
              active: true,
            },
          },
        },
      });

      // Adicionar URL completa da imagem de perfil, se existir
      if (user.profileImageUrl) {
        // Obter a URL base da API a partir do request
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        user.profileImageFullUrl = `${baseUrl}/uploads/${user.profileImageUrl}`;
      }

      res.json(user);
    } catch (error) {
      console.error("Erro ao atualizar módulos:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Busca o perfil completo de um usuário
   * Identificação do usuário via ID na URL
   * Retorna todos os dados não sensíveis do usuário
   */
  static async getProfile(req, res) {
    try {
      const { id } = req.params;

      const user = await prisma.user.findUnique({
        where: { id },
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          cpf: true,
          cnpj: true,
          birthDate: true,
          address: true,
          neighborhood: true,
          city: true,
          state: true,
          postalCode: true,
          phone: true,
          professionId: true,
          profileImageUrl: true, // Adicionado campo para imagem de perfil
          professionObj: {
            select: {
              id: true,
              name: true,
              description: true,
              groupId: true,
              group: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          },
          modules: true,
          permissions: true,
          active: true,
          createdAt: true,
          role: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              tradingName: true,
              cnpj: true,
              address: true,
              phone: true,
              active: true,
            },
          },
        },
      });

      if (!user) {
        return res.status(404).json({ message: "Usuário não encontrado" });
      }

      // Adicionar URL completa da imagem de perfil, se existir
      if (user.profileImageUrl) {
        // Obter a URL base da API a partir do request
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        user.profileImageFullUrl = `${baseUrl}/uploads/${user.profileImageUrl}`;
      }

      res.json(user);
    } catch (error) {
      console.error("Erro ao buscar perfil:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista usuários do sistema com paginação e filtros
   * Permite filtrar por busca textual, status e módulo
   * Retorna lista paginada de usuários
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        active,
        module,
        excludeSystemAdmin = false,
        forCalendar = false, // Novo parâmetro para filtrar profissionais para o calendário
        companyId, // Novo parâmetro para filtrar por empresa
        userIds, // Novo parâmetro para filtrar por IDs específicos de usuários
        sortField = 'fullName', // Parâmetro para ordenação
        sortDirection = 'asc', // Parâmetro para direção da ordenação
      } = req.query;

      // Validate and clean query parameters
      const cleanSearch = search && search !== 'undefined' ? search : undefined;
      const cleanActive = active && active !== 'undefined' ? active === 'true' : undefined;
      const cleanModule = module && module !== 'undefined' ? module : undefined;
      const cleanExcludeSystemAdmin =
        excludeSystemAdmin && excludeSystemAdmin !== 'undefined' ?
        excludeSystemAdmin === 'true' || excludeSystemAdmin === true : false;
      const isForCalendar = forCalendar === 'true' || forCalendar === true;
      const cleanCompanyId = companyId && companyId !== 'undefined' ? companyId : undefined;

      // Processar userIds se existir
      let userIdsArray = [];
      if (userIds) {
        // Se for um único valor, converter para array
        if (!Array.isArray(userIds)) {
          userIdsArray = [userIds];
        } else {
          userIdsArray = userIds;
        }
        console.log("Filtrando por IDs de usuários:", userIdsArray);
      }

      // Construir a cláusula where base
      const whereBase = [
        cleanSearch
          ? {
              OR: [
                { fullName: { contains: cleanSearch, mode: "insensitive" } },
                { email: { contains: cleanSearch, mode: "insensitive" } },
                { login: { contains: cleanSearch, mode: "insensitive" } },
              ],
            }
          : {},
        cleanActive !== undefined ? { active: cleanActive } : {},
        cleanModule ? { modules: { has: cleanModule } } : {},
        // Filtrar usuários SYSTEM_ADMIN se solicitado
        cleanExcludeSystemAdmin
          ? { NOT: { role: "SYSTEM_ADMIN" } }
          : {},
        // Filtrar por IDs específicos se fornecidos
        userIdsArray.length > 0
          ? { id: { in: userIdsArray } }
          : {},
      ];

      // Filtros específicos para o calendário
      if (isForCalendar) {
        console.log(`[USER-LIST] Filtrando profissionais para calendário. Usuário: ${req.user.id}, Papel: ${req.user.role}, isClient: ${req.user.isClient}`);

        if (req.user.isClient) {
          // Para clientes, buscar apenas profissionais relacionados aos seus agendamentos
          console.log(`[USER-LIST] Cliente ${req.user.id} buscando profissionais relacionados`);

          // Buscar IDs de profissionais que têm agendamentos com este cliente
          const clientSchedulings = await prisma.scheduling.findMany({
            where: {
              Person: {
                some: {
                  clientId: req.user.id
                }
              }
            },
            select: {
              userId: true
            },
            distinct: ['userId']
          });

          const providerIds = clientSchedulings.map(s => s.userId).filter(Boolean);
          console.log(`[USER-LIST] Profissionais encontrados para o cliente: ${providerIds.length}`);

          if (providerIds.length > 0) {
            whereBase.push({
              id: {
                in: providerIds
              }
            });
          } else {
            // Se não houver agendamentos, retornar lista vazia
            return res.json({
              users: [],
              total: 0,
              pages: 0
            });
          }
        } else if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
          // Para usuários não-admin, limitar à mesma empresa
          whereBase.push({ companyId: req.user.companyId });
        } else if (req.user.role === "SYSTEM_ADMIN") {
          // Para SYSTEM_ADMIN, não adicionar filtros adicionais de empresa
          console.log(`[USER-LIST] System Admin ${req.user.id} buscando todos os profissionais`);

          // Adicionar filtro para garantir que apenas usuários com o módulo SCHEDULING sejam retornados
          whereBase.push({
            modules: {
              has: "SCHEDULING"
            }
          });

          // Adicionar filtro para garantir que apenas usuários ativos sejam retornados
          whereBase.push({
            active: true
          });
        }
      } else {
        // Comportamento padrão para outras telas
        if (cleanCompanyId && req.user.role === "SYSTEM_ADMIN") {
          // Se for SYSTEM_ADMIN e tiver um filtro de empresa, aplicar o filtro
          whereBase.push({ companyId: cleanCompanyId });
        } else if (req.user.role !== "SYSTEM_ADMIN" && req.user.companyId) {
          // Para usuários não-admin, limitar à mesma empresa
          whereBase.push({ companyId: req.user.companyId });
        }
      }

      const where = {
        AND: whereBase
      };

      // Validar e limpar parâmetros de ordenação
      const cleanSortField = sortField && sortField !== 'undefined' ? sortField : 'fullName';
      const cleanSortDirection = sortDirection && sortDirection !== 'undefined' ? sortDirection : 'asc';

      // Construir objeto de ordenação
      const orderBy = {};
      orderBy[cleanSortField] = cleanSortDirection;

      console.log(`Ordenando por ${cleanSortField} em ordem ${cleanSortDirection}`);

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where,
          skip: (page - 1) * limit,
          take: Number(limit),
          orderBy,
          select: {
            id: true,
            login: true,
            email: true,
            fullName: true,
            cpf: true,
            cnpj: true,
            birthDate: true,
            address: true,
            neighborhood: true,
            city: true,
            state: true,
            postalCode: true,
            phone: true,
            professionId: true,
            profileImageUrl: true, // Adicionado campo para imagem de perfil
            professionObj: {
              select: {
                id: true,
                name: true,
                description: true,
                groupId: true,
                group: {
                  select: {
                    id: true,
                    name: true
                  }
                }
              }
            },
            modules: true,
            active: true,
            createdAt: true,
            permissions: true,
            role: true,
            companyId: true,
            company: {
              select: {
                id: true,
                name: true,
                tradingName: true,
                cnpj: true,
                active: true,
              },
            },
          },
        }),
        prisma.user.count({ where }),
      ]);

      // Adicionar URL completa da imagem de perfil para cada usuário
      const usersWithFullImageUrl = users.map(user => {
        if (user.profileImageUrl) {
          // Obter a URL base da API a partir do request
          const baseUrl = `${req.protocol}://${req.get('host')}`;
          return {
            ...user,
            profileImageFullUrl: `${baseUrl}/uploads/${user.profileImageUrl}`
          };
        }
        return user;
      });

      res.json({
        users: usersWithFullImageUrl,
        total,
        pages: Math.ceil(total / limit),
      });
    } catch (error) {
      console.error("Erro ao listar usuários:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status de ativo/inativo de um usuário
   * Identificação do usuário via ID na URL
   * Retorna o novo status do usuário
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      const user = await prisma.user.findUnique({ where: { id } });
      if (!user) {
        return res.status(404).json({ message: "Usuário não encontrado" });
      }

      // Não permitir desativar SYSTEM_ADMIN a menos que o solicitante também seja SYSTEM_ADMIN
      if (user.role === "SYSTEM_ADMIN" && req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message:
            "Você não tem permissão para alterar o status de um administrador de sistema",
        });
      }

      const updatedUser = await prisma.user.update({
        where: { id },
        data: { active: !user.active },
        select: {
          id: true,
          fullName: true,
          profileImageUrl: true, // Adicionado campo para imagem de perfil
          professionObj: true, // Adicionado novo campo
          active: true,
          role: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              tradingName: true,
              cnpj: true,
              active: true,
            },
          },
        },
      });

      // Adicionar URL completa da imagem de perfil, se existir
      if (updatedUser.profileImageUrl) {
        // Obter a URL base da API a partir do request
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        updatedUser.profileImageFullUrl = `${baseUrl}/uploads/${updatedUser.profileImageUrl}`;
      }

      res.json(updatedUser);
    } catch (error) {
      console.error("Erro ao alterar status do usuário:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove um usuário do sistema
   * Identificação do usuário via ID na URL
   * Retorna 204 em caso de sucesso
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o usuário existe e obter seu role
      const user = await prisma.user.findUnique({
        where: { id },
        select: { role: true },
      });

      if (!user) {
        return res.status(404).json({ message: "Usuário não encontrado" });
      }

      // Não permitir excluir SYSTEM_ADMIN a menos que o solicitante também seja SYSTEM_ADMIN
      if (user.role === "SYSTEM_ADMIN" && req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message:
            "Você não tem permissão para excluir um administrador de sistema",
        });
      }

      await prisma.user.delete({
        where: { id },
      });

      res.status(204).send();
    } catch (error) {
      console.error("Erro ao deletar usuário:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza as permissões específicas de um usuário
   * Permite controle granular sobre o que cada usuário pode fazer
   * Identificação do usuário via ID na URL
   * Retorna as novas permissões do usuário
   */
  static async updatePermissions(req, res) {
    try {
      const { id } = req.params;
      const { permissions } = req.body;

      // Apenas administradores podem atualizar permissões
      if (!req.user.modules.includes("ADMIN")) {
        return res.status(403).json({
          message: "Apenas administradores podem gerenciar permissões",
        });
      }

      // Verificar se o usuário alvo é SYSTEM_ADMIN e o solicitante não é
      const targetUser = await prisma.user.findUnique({
        where: { id },
        select: { role: true },
      });

      if (
        targetUser.role === "SYSTEM_ADMIN" &&
        req.user.role !== "SYSTEM_ADMIN"
      ) {
        return res.status(403).json({
          message:
            "Você não tem permissão para alterar as permissões de um administrador de sistema",
        });
      }

      // Se o usuário for SYSTEM_ADMIN, não alterar as permissões
      // System Admin sempre tem todas as permissões automaticamente
      if (targetUser.role === "SYSTEM_ADMIN") {
        return res.status(200).json({
          message: "Administradores do sistema têm todas as permissões automaticamente",
          user: targetUser
        });
      }

      // Atualiza as permissões do usuário
      const user = await prisma.user.update({
        where: { id },
        data: { permissions },
        select: {
          id: true,
          fullName: true,
          profileImageUrl: true, // Adicionado campo para imagem de perfil
          professionObj: true, // Adicionado novo campo
          permissions: true,
          role: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              tradingName: true,
              cnpj: true,
              active: true,
            },
          },
        },
      });

      // Adicionar URL completa da imagem de perfil, se existir
      if (user.profileImageUrl) {
        // Obter a URL base da API a partir do request
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        user.profileImageFullUrl = `${baseUrl}/uploads/${user.profileImageUrl}`;
      }

      // Registra a ação no log de auditoria
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: "UPDATE_PERMISSIONS",
          entityType: "User",
          entityId: id,
          details: {
            permissions,
            updatedAt: new Date(),
          },
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
          companyId: req.user.companyId,
        },
      });

      res.json(user);
    } catch (error) {
      console.error("Erro ao atualizar permissões:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
  /**
   * Atualiza o papel (role) de um usuário
   * Apenas administradores podem atribuir papéis administrativos
   * Identificação do usuário via ID na URL
   * Retorna o usuário com o novo papel
   */
  static async updateRole(req, res) {
    try {
      const { id } = req.params;
      const { role } = req.body;

      // Verificar se o usuário autenticado tem permissão para atribuir papéis administrativos
      if (
        (role === "SYSTEM_ADMIN" || role === "COMPANY_ADMIN") &&
        !req.user.modules.includes("ADMIN")
      ) {
        return res.status(403).json({
          message:
            "Apenas administradores podem atribuir papéis administrativos",
        });
      }

      // Verificar se estão tentando alterar um SYSTEM_ADMIN e o solicitante não é um SYSTEM_ADMIN
      const targetUser = await prisma.user.findUnique({
        where: { id },
        select: { role: true },
      });

      if (
        targetUser.role === "SYSTEM_ADMIN" &&
        req.user.role !== "SYSTEM_ADMIN"
      ) {
        return res.status(403).json({
          message:
            "Você não tem permissão para alterar o papel de um administrador de sistema",
        });
      }

      // Apenas SYSTEM_ADMIN pode atribuir o papel SYSTEM_ADMIN
      if (role === "SYSTEM_ADMIN" && req.user.role !== "SYSTEM_ADMIN") {
        return res.status(403).json({
          message:
            "Apenas administradores de sistema podem atribuir o papel de administrador de sistema",
        });
      }

      // Apenas administradores (SYSTEM_ADMIN ou COMPANY_ADMIN) podem atribuir o papel COMPANY_ADMIN
      if (role === "COMPANY_ADMIN" && req.user.role !== "SYSTEM_ADMIN" && req.user.role !== "COMPANY_ADMIN") {
        return res.status(403).json({
          message:
            "Apenas administradores podem atribuir o papel de administrador de empresa",
        });
      }

      // Se estiver alterando para COMPANY_ADMIN, atribuir todos os módulos e permissões
      let updateData = { role };

      if (role === "COMPANY_ADMIN") {
        updateData.modules = getAllModules();
        updateData.permissions = getAllPermissions();
        console.log(`Usuário atualizado para COMPANY_ADMIN com todos os módulos e permissões`);
        console.log(`Módulos: ${updateData.modules.join(', ')}`);
        console.log(`Total de permissões: ${updateData.permissions.length}`);
      }

      const user = await prisma.user.update({
        where: { id },
        data: updateData,
        select: {
          id: true,
          fullName: true,
          profileImageUrl: true, // Adicionado campo para imagem de perfil
          professionObj: true, // Adicionado novo campo
          role: true,
          modules: true,
          permissions: true,
          companyId: true,
          company: {
            select: {
              id: true,
              name: true,
              tradingName: true,
              cnpj: true,
              active: true,
            },
          },
        },
      });

      // Adicionar URL completa da imagem de perfil, se existir
      if (user.profileImageUrl) {
        // Obter a URL base da API a partir do request
        const baseUrl = `${req.protocol}://${req.get('host')}`;
        user.profileImageFullUrl = `${baseUrl}/uploads/${user.profileImageUrl}`;
      }

      // Registrar a ação no log de auditoria
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: "UPDATE_ROLE",
          entityType: "User",
          entityId: id,
          details: {
            previousRole: targetUser.role,
            newRole: role,
            updatedAt: new Date(),
          },
          ipAddress: req.ip,
          userAgent: req.headers["user-agent"],
          companyId: req.user.companyId,
        },
      });

      res.json(user);
    } catch (error) {
      console.error("Erro ao atualizar papel do usuário:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém as preferências de módulos do usuário
   */
  static async getModulePreferences(req, res) {
    try {
      console.log('[userController] getModulePreferences - req.user:', req.user ? 'Existe' : 'Não existe');
      if (req.user) {
        console.log('[userController] getModulePreferences - req.user.id:', req.user.id);
      }
      console.log('[userController] getModulePreferences - req.params:', req.params);

      // Verificar se req.user existe e tem id
      if (!req.user || !req.user.id) {
        console.log('[userController] getModulePreferences - req.user ou req.user.id não existe');
        return res.status(401).json(formatErrorResponse('Usuário não autenticado'));
      }

      const userId = req.params.userId || req.user.id;
      console.log('[userController] getModulePreferences - userId:', userId);

      // Verificar se o usuário tem permissão para acessar as preferências
      if (userId !== req.user.id && req.user.role !== 'SYSTEM_ADMIN' && req.user.role !== 'COMPANY_ADMIN') {
        return res.status(403).json(formatErrorResponse('Você não tem permissão para acessar as preferências deste usuário'));
      }

      // Buscar o usuário com suas preferências
      console.log('[userController] getModulePreferences - Buscando usuário com ID:', userId);
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          modulePreferences: true
        }
      });
      console.log('[userController] getModulePreferences - Resultado da busca:', user ? 'Usuário encontrado' : 'Usuário não encontrado');

      if (!user) {
        return res.status(404).json(formatErrorResponse('Usuário não encontrado'));
      }

      // Log das preferências para debug
      console.log('[userController] getModulePreferences - Preferências do usuário:', user.modulePreferences);

      // Retornar as preferências ou um objeto vazio se não houver
      return res.json(formatSuccessResponse(user.modulePreferences || {}));
    } catch (error) {
      console.error('Erro ao obter preferências de módulos:', error);
      return res.status(500).json(formatErrorResponse('Erro ao obter preferências de módulos'));
    }
  }

  /**
   * Atualiza as preferências de módulos do usuário
   */
  static async updateModulePreferences(req, res) {
    try {
      console.log('[userController] updateModulePreferences - req.user:', req.user);
      console.log('[userController] updateModulePreferences - req.params:', req.params);
      console.log('[userController] updateModulePreferences - req.body:', req.body);

      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json(formatErrorResponse('Dados inválidos', errors.array()));
      }

      const userId = req.params.userId || req.user.id;
      console.log('[userController] updateModulePreferences - userId:', userId);
      const { modulePreferences } = req.body;

      // Verificar se o usuário tem permissão para atualizar as preferências
      if (userId !== req.user.id && req.user.role !== 'SYSTEM_ADMIN' && req.user.role !== 'COMPANY_ADMIN') {
        return res.status(403).json(formatErrorResponse('Você não tem permissão para atualizar as preferências deste usuário'));
      }

      // Verificar se o usuário existe
      console.log('[userController] updateModulePreferences - Verificando se o usuário existe:', userId);
      const userExists = await prisma.user.findUnique({
        where: { id: userId },
        select: { id: true }
      });
      console.log('[userController] updateModulePreferences - Resultado da verificação:', userExists ? 'Usuário encontrado' : 'Usuário não encontrado');

      if (!userExists) {
        return res.status(404).json(formatErrorResponse('Usuário não encontrado'));
      }

      // Atualizar as preferências do usuário
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          modulePreferences
        },
        select: {
          id: true,
          modulePreferences: true
        }
      });

      return res.json(formatSuccessResponse(updatedUser.modulePreferences));
    } catch (error) {
      console.error('Erro ao atualizar preferências de módulos:', error);
      return res.status(500).json(formatErrorResponse('Erro ao atualizar preferências de módulos'));
    }
  }
}

module.exports = {
  UserController,
  createUserValidation,
  updateModulesValidation,
  updatePermissionsValidation,
  updateRoleValidation,
  updateModulePreferencesValidation,
};