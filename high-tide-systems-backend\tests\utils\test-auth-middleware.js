// tests/utils/test-auth-middleware.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do middleware de autenticação...');
  
  try {
    // Teste 1: Verificar se o token de teste é aceito
    console.log('\n1. Verificando se o token de teste é aceito...');
    
    // Usar uma rota protegida simples para testar
    const response = await api.get('/users');
    
    if (response.status === 200) {
      console.log('✅ Token de teste aceito com sucesso!');
      console.log(`Número de usuários retornados: ${response.data.length}`);
    } else {
      console.log('❌ Falha ao usar token de teste');
    }
    
    // Teste 2: Verificar rejeição sem token
    console.log('\n2. Verificando rejeição sem token...');
    
    try {
      // Criar cliente sem token
      const noTokenApi = axios.create({
        baseURL: API_URL,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      await noTokenApi.get('/users');
      console.log('❌ Requisição sem token foi aceita (deveria ser rejeitada)');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Requisição sem token foi rejeitada corretamente');
        console.log(`Mensagem: ${error.response.data.message}`);
      } else {
        throw error;
      }
    }
    
    // Teste 3: Verificar rejeição com token inválido
    console.log('\n3. Verificando rejeição com token inválido...');
    
    try {
      // Criar cliente com token inválido
      const invalidTokenApi = axios.create({
        baseURL: API_URL,
        headers: {
          'Authorization': 'Bearer INVALID_TOKEN',
          'Content-Type': 'application/json'
        }
      });
      
      await invalidTokenApi.get('/users');
      console.log('❌ Requisição com token inválido foi aceita (deveria ser rejeitada)');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Requisição com token inválido foi rejeitada corretamente');
        console.log(`Mensagem: ${error.response.data.message}`);
      } else {
        throw error;
      }
    }
    
    console.log('\n✅ Todos os testes do middleware de autenticação concluídos com sucesso!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
