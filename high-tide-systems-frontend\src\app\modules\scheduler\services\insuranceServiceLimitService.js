import { api } from "@/utils/api";

/**
 * Função auxiliar para extrair limites da resposta da API
 * @param {Object} response - Resposta da API
 * @returns {Array} - Array de limites
 */
const extractLimitsFromResponse = (response) => {
  // A resposta pode vir diretamente como um array ou dentro de um objeto
  let limits = [];
  if (Array.isArray(response.data)) {
    limits = response.data;
  } else if (response.data && Array.isArray(response.data.limits)) {
    limits = response.data.limits;
  } else if (response.data) {
    // Tentar extrair de outros campos possíveis
    const possibleFields = ['data', 'items', 'results'];
    for (const field of possibleFields) {
      if (response.data[field] && Array.isArray(response.data[field])) {
        limits = response.data[field];
        break;
      }
    }
  }
  return limits;
};

const insuranceServiceLimitService = {
  // Buscar limites de serviço para uma combinação de pessoa e convênio
  getLimitsByPersonInsurance: async (personId, insuranceId) => {
    try {
      console.log(`Buscando limites de serviço para pessoa ${personId} e convênio ${insuranceId}`);

      const response = await api.get(`/insurance-service-limits/person/${personId}/insurance/${insuranceId}`);
      const limits = extractLimitsFromResponse(response);

      if (limits.length === 0) {
        console.warn("Nenhum limite de serviço encontrado para esta pessoa e convênio");
      } else {
        console.log(`Encontrados ${limits.length} limites de serviço para esta pessoa e convênio`);
      }

      return limits;
    } catch (error) {
      console.error("Erro ao buscar limites de serviço por pessoa e convênio:", error);
      return [];
    }
  },

  // Buscar limites de serviço para um convênio específico (sem pessoa específica)
  getLimitsByInsurance: async (insuranceId) => {
    try {
      console.log(`Buscando limites de serviço para convênio ${insuranceId}`);

      // Usamos a nova rota específica para buscar limites por convênio
      const response = await api.get(`/insurance-service-limits/insurance/${insuranceId}`);
      const limits = extractLimitsFromResponse(response);

      if (limits.length === 0) {
        console.warn("Nenhum limite de serviço encontrado para este convênio");
      } else {
        console.log(`Encontrados ${limits.length} limites de serviço para este convênio`);
      }

      return limits;
    } catch (error) {
      console.error("Erro ao buscar limites de serviço por convênio:", error);
      return [];
    }
  },

  // Buscar tipos de serviço disponíveis para um convênio e pessoa específica
  getServiceTypesByInsurance: async (insuranceId, personId = null) => {
    try {
      console.log(`Buscando tipos de serviço disponíveis para convênio ${insuranceId}${personId ? ` e pessoa ${personId}` : ''}`);

      // Se não tiver pessoa ou convênio, retornar lista vazia
      if (!personId || !insuranceId) {
        console.warn("Pessoa ou convênio não informados");
        return [];
      }

      // Buscar apenas limites específicos para a pessoa+convênio
      const limits = await insuranceServiceLimitService.getLimitsByPersonInsurance(personId, insuranceId);

      // Extrair os tipos de serviço dos limites
      const serviceTypes = limits.map(limit => limit.ServiceType).filter(Boolean);

      if (serviceTypes.length === 0) {
        console.warn("Nenhum tipo de serviço disponível para este convênio e pessoa");
      } else {
        console.log(`Encontrados ${serviceTypes.length} tipos de serviço disponíveis para este convênio e pessoa`);
      }

      return serviceTypes;
    } catch (error) {
      console.error("Erro ao buscar tipos de serviço por convênio e pessoa:", error);
      return [];
    }
  },

  // Buscar um tipo de serviço pelo ID
  getServiceTypeById: async (serviceTypeId) => {
    try {
      console.log(`Buscando tipo de serviço com ID: ${serviceTypeId}`);
      const response = await api.get(`/service-types/${serviceTypeId}`);

      if (!response?.data) {
        console.error("Resposta inválida da API ao buscar tipo de serviço:", response);
        return null;
      }

      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar tipo de serviço com ID ${serviceTypeId}:`, error);
      return null;
    }
  }
};

export default insuranceServiceLimitService;
