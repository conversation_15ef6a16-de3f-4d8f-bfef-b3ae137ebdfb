// Função auxiliar para obter o token atual
const getCurrentToken = () => {
  return localStorage.getItem('token');
};

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

// Cache para armazenar dados temporariamente
const cache = {
  conversations: { data: null, timestamp: 0 },
  messages: {}, // { conversationId: { data, timestamp } }
  unreadCount: { data: 0, timestamp: 0 }
};

// Tempo de expiração do cache (5 minutos)
const CACHE_EXPIRATION = 5 * 60 * 1000;

// Função auxiliar para fazer requisições HTTP
const fetchWithAuth = async (endpoint, options = {}) => {
  const token = getCurrentToken();
  if (!token) {
    throw new Error('Usuário não autenticado');
  }

  const response = await fetch(`${API_URL}${endpoint}`, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      ...options.headers
    }
  });

  if (!response.ok) {
    if (response.status === 401) {
      // Tratar erro de autenticação
      window.dispatchEvent(new CustomEvent('auth:error'));
      throw new Error('Sessão expirada');
    }
    throw new Error(`Erro na requisição: ${response.status}`);
  }

  return response.json();
};

// Obter ID do usuário atual
const getCurrentUserId = () => {
  // Implementar lógica para obter ID do usuário atual
  // Pode ser do localStorage, de um contexto de autenticação, etc.
  return localStorage.getItem('userId');
};

// Obter lista de conversas
const getConversations = async (forceRefresh = false) => {
  const now = Date.now();

  // Verificar cache
  if (!forceRefresh &&
      cache.conversations.data &&
      now - cache.conversations.timestamp < CACHE_EXPIRATION) {
    return cache.conversations.data;
  }

  try {
    const data = await fetchWithAuth('/chat/conversations');

    // Extrair array de conversas da resposta
    const conversations = data.success && data.data && data.data.conversations
      ? data.data.conversations
      : [];

    // Atualizar cache
    cache.conversations.data = conversations;
    cache.conversations.timestamp = now;

    return conversations;
  } catch (error) {
    console.error('Erro ao buscar conversas:', error);
    throw error;
  }
};

// Obter mensagens de uma conversa
const getMessages = async (conversationId, forceRefresh = false) => {
  if (!conversationId) return [];

  const now = Date.now();
  const cacheKey = conversationId;

  // Verificar cache
  if (!forceRefresh &&
      cache.messages[cacheKey]?.data &&
      now - cache.messages[cacheKey]?.timestamp < CACHE_EXPIRATION) {
    return cache.messages[cacheKey].data;
  }

  try {
    const data = await fetchWithAuth(`/chat/conversations/${conversationId}/messages`);

    // Extrair array de mensagens da resposta
    const messages = data.success && data.data
      ? data.data
      : [];

    // Atualizar cache
    cache.messages[cacheKey] = {
      data: messages,
      timestamp: now
    };

    return messages;
  } catch (error) {
    console.error(`Erro ao buscar mensagens da conversa ${conversationId}:`, error);
    throw error;
  }
};

// Enviar mensagem
const sendMessage = async (conversationId, text) => {
  if (!conversationId || !text) {
    throw new Error('ID da conversa e texto são obrigatórios');
  }

  try {
    const data = await fetchWithAuth(`/chat/conversations/${conversationId}/messages`, {
      method: 'POST',
      body: JSON.stringify({ text })
    });

    return data.success && data.data
      ? data.data
      : null;
  } catch (error) {
    console.error('Erro ao enviar mensagem:', error);
    throw error;
  }
};

// Adicionar participante a um grupo
const addParticipant = async (conversationId, participantId) => {
  if (!conversationId || !participantId) {
    throw new Error('ID da conversa e ID do participante são obrigatórios');
  }

  try {
    const data = await fetchWithAuth(`/chat/conversations/${conversationId}/participants`, {
      method: 'POST',
      body: JSON.stringify({ participantId })
    });

    return data.success && data.data
      ? data.data
      : null;
  } catch (error) {
    console.error('Erro ao adicionar participante:', error);
    throw error;
  }
};

// Remover participante de um grupo (ou sair do grupo)
const removeParticipant = async (conversationId, participantId) => {
  if (!conversationId || !participantId) {
    throw new Error('ID da conversa e ID do participante são obrigatórios');
  }

  try {
    const data = await fetchWithAuth(`/chat/conversations/${conversationId}/participants/${participantId}`, {
      method: 'DELETE'
    });

    return data.success && data.data
      ? data.data
      : null;
  } catch (error) {
    console.error('Erro ao remover participante:', error);
    throw error;
  }
};

// Apagar uma mensagem
const deleteMessage = async (messageId) => {
  if (!messageId) {
    throw new Error('ID da mensagem é obrigatório');
  }

  try {
    const data = await fetchWithAuth(`/chat/messages/${messageId}`, {
      method: 'DELETE'
    });

    return data.success && data.data
      ? data.data
      : null;
  } catch (error) {
    console.error('Erro ao apagar mensagem:', error);
    throw error;
  }
};

// Criar nova conversa
const createConversation = async (userIds) => {
  if (!userIds || !userIds.length) {
    throw new Error('IDs de usuários são obrigatórios');
  }

  try {
    const data = await fetchWithAuth('/chat/conversations', {
      method: 'POST',
      body: JSON.stringify({ userIds })
    });

    return data.success && data.data
      ? data.data
      : null;
  } catch (error) {
    console.error('Erro ao criar conversa:', error);
    throw error;
  }
};

// Obter contagem de mensagens não lidas
const getUnreadCount = async (forceRefresh = false) => {
  const now = Date.now();

  // Verificar cache
  if (!forceRefresh &&
      now - cache.unreadCount.timestamp < CACHE_EXPIRATION) {
    return cache.unreadCount.data;
  }

  try {
    const data = await fetchWithAuth('/chat/unread');

    const count = data.success && data.data
      ? data.data.count
      : 0;

    // Atualizar cache
    cache.unreadCount.data = count;
    cache.unreadCount.timestamp = now;

    return count;
  } catch (error) {
    console.error('Erro ao buscar contagem de mensagens não lidas:', error);
    throw error;
  }
};

export default {
  getConversations,
  getMessages,
  sendMessage,
  createConversation,
  getUnreadCount,
  getCurrentUserId,
  getCurrentToken,
  addParticipant,
  removeParticipant,
  deleteMessage
};
