// Enums para o sistema de chat
enum ConversationType {
  INDIVIDUAL
  GROUP
}

enum MessageContentType {
  TEXT
  IMAGE
  FILE
  LINK
  SYSTEM
}

enum MessageDeliveryStatus {
  SENT
  DELIVERED
  READ
}

// Modelos para o sistema de chat
model Conversation {
  id              String           @id @default(uuid())
  type            ConversationType
  title           String?          // Título para conversas em grupo
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @updatedAt
  companyId       String           // Escopo da empresa (obrigatório)
  branchId        String?          // Escopo da unidade (opcional)
  lastMessageAt   DateTime?
  isActive        Boolean          @default(true)
  createdById     String           // Usuário que criou a conversa
  
  // Relações
  company         Company          @relation(fields: [companyId], references: [id])
  branch          Branch?          @relation(fields: [branchId], references: [id])
  createdBy       User             @relation("ConversationCreator", fields: [createdById], references: [id])
  participants    ConversationParticipant[]
  messages        Message[]

  @@index([companyId])
  @@index([branchId])
  @@index([isActive])
  @@index([lastMessageAt])
}

model ConversationParticipant {
  id                String      @id @default(uuid())
  conversationId    String
  userId            String
  joinedAt          DateTime    @default(now())
  leftAt            DateTime?
  isAdmin           Boolean     @default(false)
  lastReadMessageId String?
  
  // Relações
  conversation      Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user              User         @relation(fields: [userId], references: [id])
  lastReadMessage   Message?     @relation("LastReadMessage", fields: [lastReadMessageId], references: [id])
  messageStatuses   MessageStatus[]

  @@unique([conversationId, userId])
  @@index([userId])
  @@index([conversationId])
}

model Message {
  id                  String              @id @default(uuid())
  conversationId      String
  senderId            String
  content             String
  contentType         MessageContentType  @default(TEXT)
  createdAt           DateTime            @default(now())
  updatedAt           DateTime            @updatedAt
  isDeleted           Boolean             @default(false)
  referencedMessageId String?
  metadata            Json?               // Para dados adicionais como links, coordenadas de imagem, etc.
  
  // Relações
  conversation        Conversation        @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender              User                @relation(fields: [senderId], references: [id])
  referencedMessage   Message?            @relation("MessageReference", fields: [referencedMessageId], references: [id])
  referencingMessages Message[]           @relation("MessageReference")
  statuses            MessageStatus[]
  readByParticipants  ConversationParticipant[] @relation("LastReadMessage")

  @@index([conversationId])
  @@index([senderId])
  @@index([createdAt])
  @@index([conversationId, createdAt])
}

model MessageStatus {
  id            String               @id @default(uuid())
  messageId     String
  participantId String
  status        MessageDeliveryStatus
  timestamp     DateTime             @default(now())
  
  // Relações
  message       Message              @relation(fields: [messageId], references: [id], onDelete: Cascade)
  participant   ConversationParticipant @relation(fields: [participantId], references: [id], onDelete: Cascade)

  @@unique([messageId, participantId])
  @@index([messageId])
  @@index([participantId])
}

// Adicionar estas relações ao modelo User existente:
// model User {
//   // Campos existentes...
//   
//   // Novas relações para o chat
//   createdConversations    Conversation[]             @relation("ConversationCreator")
//   participatedConversations ConversationParticipant[]
//   sentMessages            Message[]
// }
