'use client';

import React from 'react';
import { Loader2 } from 'lucide-react';
import { motion } from 'framer-motion';

/**
 * Componente de botão para uso em modais, com suporte para diferentes temas de módulos
 *
 * @param {Object} props
 * @param {string} props.type - Tipo do botão (button, submit, reset)
 * @param {Function} props.onClick - Função para lidar com cliques
 * @param {React.ReactNode} props.children - Conteú<PERSON> do botão
 * @param {string} props.variant - Varian<PERSON> do botão (primary, secondary, danger, success, warning)
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, etc)
 * @param {boolean} props.isLoading - Se o botão está em estado de carregamento
 * @param {boolean} props.disabled - Se o botão está desabilitado
 * @param {string} props.className - Classes adicionais
 * @param {string} props.size - Tam<PERSON><PERSON> do botão (sm, md, lg)
 * @param {string} props.form - ID do formulário associado ao botão
 * @param {boolean} props.fullWidth - Se o botão deve ocupar toda a largura disponível
 */
const ModalButton = ({
  type = 'button',
  onClick,
  children,
  variant = 'primary',
  moduleColor = 'default',
  isLoading = false,
  disabled = false,
  className = '',
  size = 'md',
  form,
  fullWidth = false,
}) => {
  // Mapeamento de cores por módulo e variante
  const moduleVariants = {
    default: {
      primary: 'bg-primary-500 hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700 text-white',
      secondary: 'bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-gray-600',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
      success: 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white',
      warning: 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white',
    },
    people: {
      primary: 'bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700',
      secondary: 'border border-orange-200 dark:border-orange-800/30 text-orange-700 dark:text-orange-300 hover:bg-orange-50 dark:hover:bg-orange-900/20',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
      success: 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white',
      warning: 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white',
    },
    scheduler: {
      primary: 'bg-gradient-to-r from-purple-600 to-violet-400 dark:from-purple-700 dark:to-violet-600 text-white hover:from-purple-700 hover:to-violet-500 dark:hover:from-purple-800 dark:hover:to-violet-700',
      secondary: 'border border-purple-200 dark:border-purple-800/30 text-purple-700 dark:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/20',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
      success: 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white',
      warning: 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white',
    },
    admin: {
      primary: 'bg-gradient-to-r from-slate-500 to-slate-600 dark:from-slate-600 dark:to-slate-700 text-white hover:from-slate-600 hover:to-slate-700 dark:hover:from-slate-700 dark:hover:to-slate-800',
      secondary: 'border border-slate-200 dark:border-slate-700/30 text-slate-700 dark:text-slate-300 hover:bg-slate-50 dark:hover:bg-slate-800/20',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
      success: 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white',
      warning: 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white',
    },
    financial: {
      primary: 'bg-gradient-to-r from-green-500 to-emerald-500 dark:from-green-600 dark:to-emerald-600 text-white hover:from-green-600 hover:to-emerald-600 dark:hover:from-green-700 dark:hover:to-emerald-700',
      secondary: 'border border-green-200 dark:border-green-800/30 text-green-700 dark:text-green-300 hover:bg-green-50 dark:hover:bg-green-900/20',
      danger: 'bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700 text-white',
      success: 'bg-green-500 hover:bg-green-600 dark:bg-green-600 dark:hover:bg-green-700 text-white',
      warning: 'bg-amber-500 hover:bg-amber-600 dark:bg-amber-600 dark:hover:bg-amber-700 text-white',
    },
  };

  // Obter as classes de estilo para o módulo e variante
  const variantClasses = moduleVariants[moduleColor]?.[variant] || moduleVariants.default[variant];

  // Tamanhos de botão
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-xs',
    md: 'px-4 py-2 text-sm',
    lg: 'px-5 py-2.5 text-base'
  };

  // Classes base para todos os botões
  const baseClasses = `${sizeClasses[size] || sizeClasses.md} rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 flex items-center justify-center gap-2`;

  // Classes para estado desabilitado
  const disabledClasses = (disabled || isLoading) ? 'opacity-60 cursor-not-allowed' : '';

  // Classes para sombra (apenas para botões primários)
  const shadowClasses = variant === 'primary' ? 'shadow-md hover:shadow-lg active:shadow-sm' : '';

  // Classes para largura total
  const widthClasses = fullWidth ? 'w-full' : '';

  // Efeito de ripple para feedback tátil
  const buttonVariants = {
    hover: { scale: 1.02 },
    tap: { scale: 0.98 },
    disabled: { scale: 1 }
  };

  return (
    <motion.button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`${baseClasses} ${variantClasses} ${disabledClasses} ${shadowClasses} ${widthClasses} ${className}`}
      whileHover={disabled || isLoading ? "disabled" : "hover"}
      whileTap={disabled || isLoading ? "disabled" : "tap"}
      variants={buttonVariants}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
      aria-busy={isLoading}
      form={form}
    >
      {isLoading && <Loader2 className="h-4 w-4 animate-spin" />}
      {children}
    </motion.button>
  );
};

export default ModalButton;
