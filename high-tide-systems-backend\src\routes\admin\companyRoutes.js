// src/routes/companyRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const systemAdminMiddleware = require('../../middlewares/systemAdmin');
const upload = require('../../middlewares/upload');
const { CompanyController, companyValidation } = require('../../controllers/companyController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// IMPORTANTE: Rotas específicas precisam vir ANTES das rotas com parâmetros (:id)
// para evitar que o Express interprete 'select' como um ID

router.get('/current', CompanyController.getCurrentCompany);

// Rota para listar empresas para selecionar no formulário
router.get('/select',systemAdminMiddleware, CompanyController.listForSelect);

// Rota para listar todas as empresas (com paginação)
router.get('/', CompanyController.list);

// Rotas que exigem permissão de SYSTEM_ADMIN
// Criação de nova empresa
router.post('/', systemAdminMiddleware, upload.single('logo'), companyValidation, CompanyController.create);

// Alternar status de empresa (ativar/desativar)
router.patch('/:id/status', systemAdminMiddleware, CompanyController.toggleStatus);

// Excluir empresa
router.delete('/:id', systemAdminMiddleware, CompanyController.delete);

// Rotas com parâmetros que não exigem permissão de SYSTEM_ADMIN
// (a segurança é implementada no controller que verifica o papel do usuário)
router.get('/:id', CompanyController.get);
router.put('/:id', upload.single('logo'), companyValidation, CompanyController.update);

module.exports = router;