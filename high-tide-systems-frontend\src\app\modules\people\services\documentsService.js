import { api } from "@/utils/api";

export const documentsService = {
  // Listar documentos com filtros opcionais
  getDocuments: async ({ targetId, targetType, type }) => {
    try {
      const response = await api.get("/documents", {
        params: {
          targetId,
          targetType,
          type
        }
      });
      
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar documentos:", error);
      throw error;
    }
  },

  // Obter um documento específico pelo ID
  getDocument: async (id) => {
    try {
      // Usamos axios para obter o blob diretamente
      const response = await api.get(`/documents/${id}`, {
        responseType: 'blob'
      });
      
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar documento ${id}:`, error);
      throw error;
    }
  },

  // Baixar um documento
  downloadDocument: async (id) => {
    try {
      const response = await api.get(`/documents/${id}?download=true`, {
        responseType: 'blob'
      });
      
      // Criar URL para download
      const url = window.URL.createObjectURL(response.data);
      
      // Criar link de download e clicar automaticamente
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', ''); // O nome virá do Content-Disposition
      document.body.appendChild(link);
      link.click();
      
      // Limpar
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
      
      return true;
    } catch (error) {
      console.error(`Erro ao baixar documento ${id}:`, error);
      throw error;
    }
  },

  // Fazer upload de documentos
  uploadDocument: async (formData, targetType, targetId, onProgress) => {
    try {
      const response = await api.post(`/documents/upload?targetType=${targetType}&targetId=${targetId}`, 
        formData, 
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          },
          onUploadProgress: (progressEvent) => {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
            if (onProgress) {
              onProgress(percentCompleted);
            }
          }
        }
      );
      
      return response.data;
    } catch (error) {
      console.error("Erro ao fazer upload de documento:", error);
      throw error;
    }
  },

  // Excluir um documento
  deleteDocument: async (id) => {
    try {
      const response = await api.delete(`/documents/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao excluir documento ${id}:`, error);
      throw error;
    }
  },

  // Abrir documento em nova aba
  openDocument: (id) => {
    // Obtém a URL base da API
    const baseUrl = api.defaults.baseURL || 'http://localhost:5000';
    
    // Abre em nova aba
    window.open(`${baseUrl}/documents/${id}`, '_blank');
  },

  // Obter URL para preview de documento
  getDocumentPreviewUrl: async (id) => {
    try {
      const blob = await documentsService.getDocument(id);
      return URL.createObjectURL(blob);
    } catch (error) {
      console.error(`Erro ao obter URL de preview para documento ${id}:`, error);
      throw error;
    }
  }
};

export default documentsService;