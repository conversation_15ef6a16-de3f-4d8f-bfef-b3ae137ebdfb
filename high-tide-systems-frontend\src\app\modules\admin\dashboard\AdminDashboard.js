"use client";

import React, { useState, useEffect, useMemo } from "react";
import {
  BarChart,
  Bar,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import {
  Users,
  Activity,
  Briefcase,
  Calendar,
  Clock,
  TrendingUp,
  Settings,
  Search,
  Bell,
  Zap,
  Server,
  Database,
  Shield,
  CreditCard,
  Building,
  LayoutDashboard
} from "lucide-react";
import ExportMenu from "@/components/ui/ExportMenu";
import { adminDashboardService } from "@/app/modules/admin";
import { APP_VERSION } from "@/config/appConfig";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import { ModuleHeader, ModuleSelect, ModuleInput } from "@/components/ui";

const AdminDashboard = () => {
  // Obter contexto de autenticação
  const { user, isSystemAdmin } = useAuth();

  // Estados para armazenar os dados
  const [statsData, setStatsData] = useState({
    usersTotal: 0,
    activeUsers: 0,
    clientsTotal: 0,
    appointmentsTotal: 0,
    professionsTotal: 0,
    groupsTotal: 0,
  });
  const [growthData, setGrowthData] = useState({
    usersGrowth: 0,
    activeUsersGrowth: 0,
    clientsGrowth: 0,
    appointmentsGrowth: 0,
    professionsGrowth: 0,
    groupsGrowth: 0,
  });
  const [activityData, setActivityData] = useState([]);
  const [userModuleData, setUserModuleData] = useState([]);
  const [professionDistribution, setProfessionDistribution] = useState([]);
  const [usersData, setUsersData] = useState([]);
  const [recentActivity, setRecentActivity] = useState([]);
  const [systemInfo, setSystemInfo] = useState({
    version: APP_VERSION,
    lastBackup: "Carregando...",
    securityStatus: "Carregando...",
    currentPlan: "Carregando...",
  });
  const [isLoading, setIsLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState("7dias");
  const [isExporting, setIsExporting] = useState(false);

  // Estados para gerenciar empresas
  const [companies, setCompanies] = useState([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState(null);
  const [currentCompany, setCurrentCompany] = useState(null);

  // Cores para os gráficos de pizza
  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#FF6B6B", "#4CAF50", "#9C27B0"];
  const PROFESSION_COLORS = ["#FF9933", "#3B82F6", "#8B5CF6", "#10B981", "#EC4899", "#F59E0B", "#6366F1", "#EF4444"];

  // Efeito para carregar lista de empresas (apenas para SYSTEM_ADMIN)
  useEffect(() => {
    const loadCompanies = async () => {
      if (isSystemAdmin()) {
        try {
          const companiesData = await companyService.getCompaniesForSelect();
          setCompanies(companiesData);

          // Se houver empresas, selecionar a primeira por padrão
          if (companiesData.length > 0 && !selectedCompanyId) {
            setSelectedCompanyId(companiesData[0].id);
          }
        } catch (error) {
          console.error("Erro ao carregar empresas:", error);
        }
      } else if (user?.companyId) {
        // Para usuários não-admin, definir a empresa atual
        try {
          const company = await companyService.getCurrentCompany();
          setCurrentCompany(company);
          setSelectedCompanyId(user.companyId);
        } catch (error) {
          console.error("Erro ao carregar empresa atual:", error);
          setSelectedCompanyId(user.companyId);
        }
      }
    };

    if (user) {
      loadCompanies();
    }
  }, [user, isSystemAdmin]);

  // Função para carregar todos os dados do dashboard
  const loadDashboardData = async () => {
    if (!selectedCompanyId) return;

    setIsLoading(true);
    try {
      // Carregar todos os dados de uma vez, passando o ID da empresa selecionada
      const allData = await adminDashboardService.getAllDashboardData({
        companyId: selectedCompanyId,
      });

      setStatsData(allData.stats);

      // Verificar se temos dados de crescimento
      if (allData.growth) {
        setGrowthData(allData.growth);
      }

      setActivityData(allData.activityData);
      setUserModuleData(allData.userModuleData);
      setProfessionDistribution(allData.professionDistribution || []);
      setUsersData(allData.usersData);
      setRecentActivity(allData.recentActivity);
      setSystemInfo(allData.systemInfo);
    } catch (error) {
      console.error("Erro ao carregar dados do dashboard:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para carregar apenas os dados de atividade quando o período muda
  const loadActivityData = async () => {
    if (!selectedCompanyId) return;

    try {
      const activity = await adminDashboardService.getActivityData(
        selectedPeriod,
        { companyId: selectedCompanyId }
      );
      setActivityData(activity);
    } catch (error) {
      console.error("Erro ao carregar dados de atividade:", error);
    }
  };

  // Carregar dados quando a empresa selecionada ou o componente mudar
  useEffect(() => {
    if (selectedCompanyId) {
      loadDashboardData();
    }
  }, [selectedCompanyId]);

  // Recarregar dados de atividade quando o período muda
  useEffect(() => {
    if (selectedCompanyId) {
      loadActivityData();
    }
  }, [selectedPeriod, selectedCompanyId]);

  // Manipulador para mudança de empresa
  const handleCompanyChange = (e) => {
    setSelectedCompanyId(e.target.value);
  };

  // Função para exportar os dados do dashboard
  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Obter o nome da empresa selecionada
      let companyName = "";
      if (isSystemAdmin() && selectedCompanyId) {
        const company = companies.find(c => c.id === selectedCompanyId);
        if (company) companyName = company.name;
      } else if (currentCompany) {
        companyName = currentCompany.name;
      }

      // Exportar os dados
      await adminDashboardService.exportDashboardData(
        {
          statsData,
          growthData,
          activityData,
          userModuleData
        },
        companyName,
        selectedPeriod,
        format
      );
    } catch (error) {
      console.error("Erro ao exportar dados do dashboard:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Import tutorial steps from tutorialMapping
  const adminDashboardTutorialSteps = useMemo(() => {
    // Import dynamically to avoid circular dependencies
    const tutorialMap = require('@/tutorials/tutorialMapping').default;
    return tutorialMap['/dashboard/admin/dashboard'] || [];
  }, []);

  // Renderização do componente
  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <LayoutDashboard size={24} className="mr-2 text-slate-600 dark:text-slate-400" />
          Dashboard de Administrador
          {currentCompany && !isSystemAdmin() && (
            <span className="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
              {currentCompany.name}
            </span>
          )}
        </h1>

        <div className="flex items-center gap-4">
          {/* Botão de exportar */}
          <div className="export-button">
            <ExportMenu
              onExport={handleExport}
              isExporting={isExporting}
              disabled={isLoading}
              className="text-slate-700 dark:text-slate-300"
            />
          </div>

          {/* Seletor de empresas (apenas para SYSTEM_ADMIN) */}
          {isSystemAdmin() && companies.length > 0 && (
            <div className="flex items-center">
              <Building className="h-5 w-5 text-neutral-500 dark:text-neutral-400 mr-2" />
              <ModuleSelect
                moduleColor="admin"
                value={selectedCompanyId}
                onChange={handleCompanyChange}
              >
                {companies.map((company) => (
                  <option key={company.id} value={company.id}>
                    {company.name}
                  </option>
                ))}
              </ModuleSelect>
            </div>
          )}

          <div className="flex items-center gap-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-4 w-4" />
              <ModuleInput
                moduleColor="admin"
                type="text"
                placeholder="Buscar..."
                className="pl-9"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Estado de carregamento */}
      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500 dark:border-primary-400"></div>
        </div>
      ) : (
        <>
          {/* Cards de Estatísticas */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Total de Usuários
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {statsData.usersTotal}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-primary-100 dark:bg-primary-900/30 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-primary-600 dark:text-primary-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span
                  className={`flex items-center ${
                    growthData.usersGrowth >= 0
                      ? "text-success-500 dark:text-success-400"
                      : "text-red-500 dark:text-red-400"
                  }`}
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {growthData.usersGrowth >= 0 ? "+" : ""}
                  {growthData.usersGrowth.toFixed(1)}%
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  desde o mês passado
                </span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Usuários Ativos
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {statsData.activeUsers}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                  <Activity className="h-6 w-6 text-green-600 dark:text-green-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span
                  className={`flex items-center ${
                    growthData.activeUsersGrowth >= 0
                      ? "text-success-500 dark:text-success-400"
                      : "text-red-500 dark:text-red-400"
                  }`}
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {growthData.activeUsersGrowth >= 0 ? "+" : ""}
                  {growthData.activeUsersGrowth.toFixed(1)}%
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  desde o mês passado
                </span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Total de Clientes
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {statsData.clientsTotal}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                  <Briefcase className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span
                  className={`flex items-center ${
                    growthData.clientsGrowth >= 0
                      ? "text-success-500 dark:text-success-400"
                      : "text-red-500 dark:text-red-400"
                  }`}
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {growthData.clientsGrowth >= 0 ? "+" : ""}
                  {growthData.clientsGrowth.toFixed(1)}%
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  desde o mês passado
                </span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Agendamentos
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {statsData.appointmentsTotal}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span
                  className={`flex items-center ${
                    growthData.appointmentsGrowth >= 0
                      ? "text-success-500 dark:text-success-400"
                      : "text-red-500 dark:text-red-400"
                  }`}
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {growthData.appointmentsGrowth >= 0 ? "+" : ""}
                  {growthData.appointmentsGrowth.toFixed(1)}%
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  desde o mês passado
                </span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Profissões
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {statsData.professionsTotal}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-amber-100 dark:bg-amber-900/30 rounded-full flex items-center justify-center">
                  <Briefcase className="h-6 w-6 text-amber-600 dark:text-amber-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span
                  className={`flex items-center ${
                    growthData.professionsGrowth >= 0
                      ? "text-success-500 dark:text-success-400"
                      : "text-red-500 dark:text-red-400"
                  }`}
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {growthData.professionsGrowth >= 0 ? "+" : ""}
                  {growthData.professionsGrowth.toFixed(1)}%
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  desde o mês passado
                </span>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between">
                <div>
                  <p className="text-sm font-medium text-neutral-500 dark:text-neutral-400">
                    Grupos
                  </p>
                  <h3 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2">
                    {statsData.groupsTotal}
                  </h3>
                </div>
                <div className="h-12 w-12 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-teal-600 dark:text-teal-400" />
                </div>
              </div>
              <div className="mt-4 flex items-center text-xs">
                <span
                  className={`flex items-center ${
                    growthData.groupsGrowth >= 0
                      ? "text-success-500 dark:text-success-400"
                      : "text-red-500 dark:text-red-400"
                  }`}
                >
                  <TrendingUp className="h-3 w-3 mr-1" />
                  {growthData.groupsGrowth >= 0 ? "+" : ""}
                  {growthData.groupsGrowth.toFixed(1)}%
                </span>
                <span className="text-neutral-500 dark:text-neutral-400 ml-2">
                  desde o mês passado
                </span>
              </div>
            </div>
          </div>

          {/* Gráficos */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6"
          id="gradedashboards">
            {/* Gráfico de Atividade no Sistema */}
            <div className="lg:col-span-2 bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Atividade no Sistema
                </h3>
                <select
                  className="text-sm border border-neutral-200 dark:border-gray-600 rounded-md px-3 py-1.5 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                  value={selectedPeriod}
                  onChange={(e) => setSelectedPeriod(e.target.value)}
                >
                  <option value="7dias">Últimos 7 dias</option>
                  <option value="30dias">Últimos 30 dias</option>
                  <option value="3meses">Últimos 3 meses</option>
                </select>
              </div>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={activityData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" className="dark:stroke-gray-700" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                      tickLine={false}
                      axisLine={{ stroke: "#e5e7eb", className: "dark:stroke-gray-600" }}
                    />
                    <YAxis
                      tick={{ fontSize: 12, fill: "#71717a", className: "dark:fill-gray-400" }}
                      tickLine={false}
                      axisLine={{ stroke: "#e5e7eb", className: "dark:stroke-gray-600" }}
                    />
                    <Tooltip contentStyle={{ backgroundColor: '#fff', borderColor: '#e5e7eb', className: "dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" }} />
                    <Area
                      type="monotone"
                      dataKey="usuarios"
                      stackId="1"
                      stroke="#FF9933"
                      fill="#FFC285"
                      name="Usuários"
                    />
                    <Area
                      type="monotone"
                      dataKey="clientes"
                      stackId="1"
                      stroke="#3B82F6"
                      fill="#93C5FD"
                      name="Clientes"
                    />
                    <Area
                      type="monotone"
                      dataKey="agendamentos"
                      stackId="1"
                      stroke="#8B5CF6"
                      fill="#C4B5FD"
                      name="Agendamentos"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Distribuição por Módulo */}
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Usuários por Módulo
                </h3>
              </div>
              <div className="h-80 flex flex-col justify-center">
                <ResponsiveContainer width="100%" height="70%">
                  <PieChart>
                    <Pie
                      data={userModuleData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={90}
                      fill="#8884d8"
                      paddingAngle={5}
                      dataKey="value"
                      // Remover os labels do gráfico e movê-los para uma legenda separada
                      label={false}
                    >
                      {userModuleData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={COLORS[index % COLORS.length]}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value, name) => [`${value} usuários`, name]}
                      contentStyle={{ fontSize: "12px", backgroundColor: "#fff", borderColor: "#e5e7eb", className: "dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" }}
                    />
                  </PieChart>
                </ResponsiveContainer>

                {/* Legenda melhorada */}
                <div className="mt-4 grid grid-cols-2 gap-4">
                  {userModuleData.map((entry, index) => (
                    <div key={index} className="flex items-center">
                      <div
                        className="h-4 w-4 rounded-full mr-2 flex-shrink-0"
                        style={{
                          backgroundColor: COLORS[index % COLORS.length],
                        }}
                      ></div>
                      <span className="text-sm text-neutral-800 dark:text-neutral-200">
                        {entry.name}:{" "}
                        {Math.round(
                          (entry.value /
                            userModuleData.reduce((a, b) => a + b.value, 0)) *
                            100
                        )}
                        %
                      </span>
                    </div>
                  ))}
                </div>

                {/* Mostrar total de usuários */}
                <div className="mt-4 text-center text-xs text-neutral-500 dark:text-neutral-400">
                  Total:{" "}
                  {userModuleData.reduce((sum, item) => sum + item.value, 0)}{" "}
                  usuários
                </div>
              </div>
            </div>
          </div>

          {/* Gráfico de Distribuição de Profissões por Grupo */}
          <div className="grid grid-cols-1 gap-6">
            <div className="bg-white dark:bg-gray-800 p-6 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Distribuição de Profissões por Grupo
                </h3>
              </div>
              <div className="h-80">
                {professionDistribution.length > 0 ? (
                  <div className="flex flex-col md:flex-row">
                    <div className="w-full md:w-1/2">
                      <ResponsiveContainer width="100%" height={300}>
                        <PieChart>
                          <Pie
                            data={[...professionDistribution].sort((a, b) => b.value - a.value)}
                            cx="50%"
                            cy="50%"
                            innerRadius={60}
                            outerRadius={100}
                            fill="#8884d8"
                            paddingAngle={5}
                            dataKey="value"
                            label={false}
                          >
                            {professionDistribution.map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={PROFESSION_COLORS[index % PROFESSION_COLORS.length]}
                              />
                            ))}
                          </Pie>
                          <Tooltip
                            formatter={(value, name) => [`${value} profissões`, name]}
                            contentStyle={{ fontSize: "12px", backgroundColor: "#fff", borderColor: "#e5e7eb", className: "dark:bg-gray-800 dark:border-gray-700 dark:text-gray-200" }}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="w-full md:w-1/2 flex flex-col justify-center">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 md:mt-0 max-h-60 overflow-y-auto pr-2">
                        {[...professionDistribution].sort((a, b) => b.value - a.value).map((entry, index) => (
                          <div key={index} className="flex items-center">
                            <div
                              className="h-4 w-4 rounded-full mr-2 flex-shrink-0"
                              style={{
                                backgroundColor: PROFESSION_COLORS[index % PROFESSION_COLORS.length],
                              }}
                            ></div>
                            <span className="text-sm text-neutral-800 dark:text-neutral-200 truncate" title={`${entry.name}: ${entry.value} profissões (${Math.round(
                                (entry.value /
                                  professionDistribution.reduce((a, b) => a + b.value, 0)) *
                                  100
                              )}%)`}>
                              {entry.name}:{" "}
                              {Math.round(
                                (entry.value /
                                  professionDistribution.reduce((a, b) => a + b.value, 0)) *
                                  100
                              )}
                              %
                            </span>
                          </div>
                        ))}
                      </div>
                      <div className="mt-6 text-center text-xs text-neutral-500 dark:text-neutral-400">
                        Total:{" "}
                        {professionDistribution.reduce((sum, item) => sum + item.value, 0)}{" "}
                        profissões
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-neutral-500 dark:text-neutral-400">
                      Nenhuma profissão cadastrada
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Tabelas */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Usuários Ativos */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="px-6 py-4 border-b border-neutral-100 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Usuários Ativos
                </h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-neutral-50 dark:bg-gray-700">
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                        Usuário
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                        Função
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                        Última Atividade
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-neutral-200 dark:divide-gray-700">
                    {usersData.map((user) => (
                      <tr key={user.id} className="hover:bg-neutral-50 dark:hover:bg-gray-700">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-8 w-8 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium">
                              {user.name.charAt(0)}
                            </div>
                            <div className="ml-3">
                              <p className="text-sm font-medium text-neutral-700 dark:text-neutral-200">
                                {user.name}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="px-2 py-1 text-xs rounded-full bg-neutral-100 dark:bg-gray-600 text-neutral-700 dark:text-neutral-300">
                            {user.role}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div
                              className={`h-2 w-2 rounded-full mr-2 ${
                                user.status === "online"
                                  ? "bg-green-500"
                                  : "bg-neutral-400 dark:bg-neutral-500"
                              }`}
                            ></div>
                            <span className="text-sm text-neutral-600 dark:text-neutral-300 capitalize">
                              {user.status}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-neutral-300">
                          {new Date(user.lastActive).toLocaleString("pt-BR", {
                            day: "2-digit",
                            month: "2-digit",
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="px-6 py-3 border-t border-neutral-100 dark:border-gray-700 flex justify-center">
                <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                  Ver todos os usuários
                </button>
              </div>
            </div>

            {/* Atividades Recentes */}
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700">
              <div className="px-6 py-4 border-b border-neutral-100 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">
                  Atividades Recentes
                </h3>
              </div>
              <div className="overflow-y-auto max-h-96">
                <div className="divide-y divide-neutral-100 dark:divide-gray-700">
                  {recentActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className="px-6 py-4 hover:bg-neutral-50 dark:hover:bg-gray-700"
                    >
                      <div className="flex items-start">
                        <div
                          className={`h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0 ${getActivityIconBg(
                            activity.type
                          )}`}
                        >
                          {getActivityIcon(activity.type)}
                        </div>
                        <div className="ml-3 flex-1">
                          <p className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                            {activity.action}
                          </p>
                          <div className="flex justify-between mt-1">
                            <p className="text-xs text-neutral-500 dark:text-neutral-400">
                              por {activity.user}
                            </p>
                            <p className="text-xs text-neutral-500 dark:text-neutral-400">
                              {adminDashboardService.formatTimeAgo(
                                activity.timestamp
                              )}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="px-6 py-3 border-t border-neutral-100 dark:border-gray-700 flex justify-center">
                <button className="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300">
                  Ver todas as atividades
                </button>
              </div>
            </div>
          </div>

          {/* Informações do Sistema */}
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border border-neutral-100 dark:border-gray-700 p-6">
            <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100 mb-4">
              Informações do Sistema
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="flex items-center space-x-3">
                <Server className="h-10 w-10 text-neutral-500 dark:text-neutral-400" />
                <div>
                  <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400">
                    Versão do Sistema
                  </p>
                  <p className="text-sm font-medium text-neutral-800 dark:text-neutral-200">
                    {systemInfo.version}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Database className="h-10 w-10 text-neutral-500 dark:text-neutral-400" />
                <div>
                  <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400">
                    Último Backup
                  </p>
                  <p className="text-sm font-medium text-neutral-800 dark:text-neutral-200">
                    {typeof systemInfo.lastBackup === "string"
                      ? systemInfo.lastBackup
                      : new Date(systemInfo.lastBackup).toLocaleString(
                          "pt-BR",
                          {
                            day: "2-digit",
                            month: "2-digit",
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Shield className="h-10 w-10 text-neutral-500 dark:text-neutral-400" />
                <div>
                  <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400">
                    Status Segurança
                  </p>
                  <p
                    className={`text-sm font-medium ${
                      systemInfo.securityStatus === "Protegido"
                        ? "text-green-600 dark:text-green-400"
                        : systemInfo.securityStatus === "Atenção Necessária"
                        ? "text-amber-500 dark:text-amber-400"
                        : "text-red-500 dark:text-red-400"
                    }`}
                  >
                    {systemInfo.securityStatus}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CreditCard className="h-10 w-10 text-neutral-500 dark:text-neutral-400" />
                <div>
                  <p className="text-xs font-medium text-neutral-500 dark:text-neutral-400">
                    Plano Atual
                  </p>
                  <p className="text-sm font-medium text-neutral-800 dark:text-neutral-200">
                    {systemInfo.currentPlan}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

// Funções auxiliares
function getActivityIcon(type) {
  switch (type) {
    case "auth":
      return <Users className="h-4 w-4 text-white" />;
    case "client":
      return <Briefcase className="h-4 w-4 text-white" />;
    case "security":
      return <Shield className="h-4 w-4 text-white" />;
    case "scheduling":
      return <Calendar className="h-4 w-4 text-white" />;
    case "settings":
      return <Settings className="h-4 w-4 text-white" />;
    case "system":
      return <Server className="h-4 w-4 text-white" />;
    case "financial":
      return <CreditCard className="h-4 w-4 text-white" />;
    default:
      return <Activity className="h-4 w-4 text-white" />;
  }
}

function getActivityIconBg(type) {
  switch (type) {
    case "auth":
      return "bg-blue-500";
    case "client":
      return "bg-green-500";
    case "security":
      return "bg-red-500";
    case "scheduling":
      return "bg-purple-500";
    case "settings":
      return "bg-neutral-500";
    case "system":
      return "bg-gray-500";
    case "financial":
      return "bg-amber-500";
    default:
      return "bg-primary-500";
  }
}

export default AdminDashboard;