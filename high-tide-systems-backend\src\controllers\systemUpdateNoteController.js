const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * Controller for system update notes
 */
class SystemUpdateNoteController {
  /**
   * Get the latest update note
   */
  static async getLatest(req, res) {
    try {
      const latestNote = await prisma.systemUpdateNote.findFirst({
        orderBy: {
          createdAt: 'desc'
        }
      });

      if (!latestNote) {
        return res.status(404).json({ message: 'Nenhuma nota de atualização encontrada' });
      }

      res.json(latestNote);
    } catch (error) {
      console.error('Error fetching latest update note:', error);
      res.status(500).json({ message: 'Erro ao buscar nota de atualização', error: error.message });
    }
  }

  /**
   * Create a new update note
   */
  static async create(req, res) {
    try {
      const { content } = req.body;

      if (!content) {
        return res.status(400).json({ message: 'Conteú<PERSON> da nota é obrigatório' });
      }

      const newNote = await prisma.systemUpdateNote.create({
        data: {
          content,
          createdBy: req.user.id
        }
      });

      res.status(201).json(newNote);
    } catch (error) {
      console.error('Error creating update note:', error);
      res.status(500).json({ message: 'Erro ao criar nota de atualização', error: error.message });
    }
  }

  /**
   * Update an existing update note
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const { content } = req.body;

      if (!content) {
        return res.status(400).json({ message: 'Conteúdo da nota é obrigatório' });
      }

      const existingNote = await prisma.systemUpdateNote.findUnique({
        where: { id }
      });

      if (!existingNote) {
        return res.status(404).json({ message: 'Nota de atualização não encontrada' });
      }

      const updatedNote = await prisma.systemUpdateNote.update({
        where: { id },
        data: { content }
      });

      res.json(updatedNote);
    } catch (error) {
      console.error('Error updating update note:', error);
      res.status(500).json({ message: 'Erro ao atualizar nota de atualização', error: error.message });
    }
  }

  /**
   * Delete an update note
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      const existingNote = await prisma.systemUpdateNote.findUnique({
        where: { id }
      });

      if (!existingNote) {
        return res.status(404).json({ message: 'Nota de atualização não encontrada' });
      }

      await prisma.systemUpdateNote.delete({
        where: { id }
      });

      res.json({ message: 'Nota de atualização excluída com sucesso' });
    } catch (error) {
      console.error('Error deleting update note:', error);
      res.status(500).json({ message: 'Erro ao excluir nota de atualização', error: error.message });
    }
  }

  /**
   * Get all update notes
   */
  static async getAll(req, res) {
    try {
      const { page = 1, limit = 10 } = req.query;
      const skip = (page - 1) * limit;

      const notes = await prisma.systemUpdateNote.findMany({
        orderBy: {
          createdAt: 'desc'
        },
        skip: parseInt(skip),
        take: parseInt(limit),
        include: {
          user: {
            select: {
              id: true,
              fullName: true
            }
          }
        }
      });

      const total = await prisma.systemUpdateNote.count();

      res.json({
        data: notes,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      console.error('Error fetching update notes:', error);
      res.status(500).json({ message: 'Erro ao buscar notas de atualização', error: error.message });
    }
  }
}

module.exports = { SystemUpdateNoteController };
