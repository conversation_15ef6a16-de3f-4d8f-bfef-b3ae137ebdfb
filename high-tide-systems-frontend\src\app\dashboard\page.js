'use client';

import React, { useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import { useRouter } from 'next/navigation';
import { Calendar, UserCheck, Clock, Sparkles } from 'lucide-react';
import { modules } from './components';

// Componentes extraídos
import BackgroundDecoration from '@/components/dashboard/Background';
import WelcomeCard from '@/components/dashboard/WelcomeCard';
import ModuleCard from '@/components/dashboard/ModuleCard';
import AppointmentList from '@/components/dashboard/AppointmentList';
import QuickActionsCard from '@/components/dashboard/QuickActions';
import UpdateNotesCard from '@/components/dashboard/UpdateNotesCard';
import ClientDashboard from '@/components/dashboard/ClientDashboard';

// Hooks personalizados
import { useAppointments } from '@/hooks/useAppointments';
import { useModules } from '@/hooks/useModules';

const DashboardPage = () => {
  const { user } = useAuth();
  const router = useRouter();
  const { isClient } = usePermissions();

  // Usar o hook de agendamentos com dados reais
  const { appointments, isLoading, error } = useAppointments(false, {
    limit: 5, // Limitar a 5 agendamentos para melhor performance
    status: ['CONFIRMED', 'PENDING'] // Apenas agendamentos confirmados ou pendentes
  });

  // Usar o hook de módulos para verificar acesso
  const { hasAccessToModule } = useModules();

  // If user is a client, show the client dashboard
  if (isClient()) {
    return <ClientDashboard />;
  }

  // Memoizar handlers para evitar recriações desnecessárias
  const handleModuleClick = useCallback((moduleId) => {
    router.push(`/dashboard/${moduleId}`);
  }, [router]);

  const handleActionClick = useCallback((path) => {
    router.push(path);
  }, [router]);

  // Ações rápidas com base no papel do usuário
  const quickActions = [
    {
      title: 'Novo Agendamento',
      description: 'Agendar um paciente com um profissional',
      icon: Calendar,
      path: '/dashboard/scheduler/calendar'
    },
    {
      title: 'Cadastrar Paciente',
      description: 'Adicionar uma nova pessoa ao sistema',
      icon: UserCheck,
      path: '/dashboard/people/persons'
    },
    {
      title: 'Ver Agenda Completa',
      description: 'Visualizar calendário de agendamentos',
      icon: Clock,
      path: '/dashboard/scheduler/calendar'
    }
  ];

  return (
    <div id='Main-Container-Dashboard' className="p-6 min-h-screen relative">
      {/* CSS personalizado para sombras e animações */}
      <style jsx global>{`
        :root {
          --color-slate-rgb: 100, 116, 139;
          --color-emerald-rgb: 16, 185, 129;
          --color-red-rgb: 239, 68, 68;
          --color-orange-rgb: 249, 115, 22;
          --color-violet-rgb: 139, 92, 246;
          --color-blue-rgb: 59, 130, 246;
          --color-amber-rgb: 245, 158, 11;
        }

        @keyframes pulse-slow {
          0%, 100% {
            opacity: 0.7;
          }
          50% {
            opacity: 0.4;
          }
        }

        .animate-pulse-slow {
          animation: pulse-slow 6s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
      `}</style>

      {/* Decoração de fundo */}
      <BackgroundDecoration />

      {/* Cartão de Boas-vindas */}
      <WelcomeCard user={user} />

      {/* Módulos do Sistema */}
      <h2 className="text-2xl dark:text-white font-bold text-gray-800 mb-5 flex items-center">
        <Sparkles className="mr-3 text-primary-500" size={22} aria-hidden="true" />
        Módulos do Sistema
      </h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
        {modules.map((module) => (
          <ModuleCard
            key={module.id}
            title={module.title}
            icon={module.icon}
            description={module.description}
            onClick={() => handleModuleClick(module.id)}
            isAccessible={hasAccessToModule(module.role)}
            moduleId={module.id}
          />
        ))}
      </div>

      {/* Notas de Atualização */}
      <UpdateNotesCard />

      {/* Dashboard com agendamentos e ações rápidas */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Agendamentos */}
        <div className="lg:col-span-2">
          <AppointmentList
            appointments={appointments}
            isLoading={isLoading}
            error={error}
          />
        </div>

        {/* Ações rápidas */}
        <div>
          <QuickActionsCard
            actions={quickActions}
            onActionClick={handleActionClick}
          />
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;