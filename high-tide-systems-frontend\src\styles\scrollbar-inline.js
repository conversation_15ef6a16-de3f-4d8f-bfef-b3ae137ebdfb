'use client';

import React, { useEffect } from 'react';
import { usePathname } from 'next/navigation';

/**
 * Componente que aplica estilos de scrollbar diretamente via CSS inline
 */
const ScrollbarInlineStyles = () => {
  const pathname = usePathname();

  useEffect(() => {
    // Determinar o módulo atual com base no pathname
    let currentModule = 'default';
    let scrollbarColor = 'rgba(156, 163, 175, 0.5)';
    let scrollbarHoverColor = 'rgba(156, 163, 175, 0.7)';

    if (pathname.includes('/modules/people') || pathname.includes('/pessoas')) {
      currentModule = 'people';
      scrollbarColor = 'rgba(249, 115, 22, 0.4)';
      scrollbarHoverColor = 'rgba(249, 115, 22, 0.6)';
    } else if (pathname.includes('/modules/scheduler') || pathname.includes('/agendamento')) {
      currentModule = 'scheduler';
      scrollbarColor = 'rgba(147, 51, 234, 0.4)';
      scrollbarHoverColor = 'rgba(147, 51, 234, 0.6)';
    } else if (pathname.includes('/modules/admin') || pathname.includes('/admin')) {
      currentModule = 'admin';
      scrollbarColor = 'rgba(100, 116, 139, 0.4)';
      scrollbarHoverColor = 'rgba(100, 116, 139, 0.6)';
    } else if (pathname.includes('/modules/financial') || pathname.includes('/financeiro')) {
      currentModule = 'financial';
      scrollbarColor = 'rgba(5, 150, 105, 0.4)';
      scrollbarHoverColor = 'rgba(5, 150, 105, 0.6)';
    }

    // Criar um elemento de estilo
    const styleElement = document.createElement('style');
    styleElement.id = 'custom-scrollbar-styles';

    // Definir o CSS inline
    styleElement.innerHTML = `
      body::-webkit-scrollbar {
        width: 12px;
        height: 12px;
      }

      body::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
        border-radius: 0;
      }

      body::-webkit-scrollbar-thumb {
        border-radius: 10px;
        border: 3px solid transparent;
        background-clip: content-box;
        background-color: ${scrollbarColor} !important;
      }

      body::-webkit-scrollbar-thumb:hover {
        background-color: ${scrollbarHoverColor} !important;
      }
    `;

    // Remover qualquer estilo anterior
    const existingStyle = document.getElementById('custom-scrollbar-styles');
    if (existingStyle) {
      existingStyle.remove();
    }

    // Adicionar o novo estilo ao head
    document.head.appendChild(styleElement);

    // Limpar ao desmontar
    return () => {
      const styleToRemove = document.getElementById('custom-scrollbar-styles');
      if (styleToRemove) {
        styleToRemove.remove();
      }
    };
  }, [pathname]);

  return null;
};

export default ScrollbarInlineStyles;
