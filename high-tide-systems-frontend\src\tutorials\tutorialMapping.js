/**
 * Este arquivo mapeia tutoriais específicos para diferentes rotas da aplicação
 * Cada rota principal tem seu próprio tutorial com passos específicos
 */

// Tutorial para o Dashboard principal
const dashboardTutorial = [
    {
      title: "Bem-vindo ao Dashboard",
      content: "Este é o seu painel central, onde você pode visualizar informações importantes e acessar todos os módulos do sistema.",
      selector: "#Main-Container-Dashboard", // Use o ID principal do dashboard
      position: "bottom"
    },
    {
      title: "Cartão de Boas-vindas",
      content: "Aqui você visualiza informações personalizadas conforme o horário do dia e seu perfil de usuário.",
      selector: ".bg-gradient-to-r.from-white.to-gray-50",
      position: "bottom"
    },
    {
      title: "Módulos Disponíveis",
      content: "Cada card representa um módulo do sistema. Clique em um deles para acessar suas funcionalidades específicas.",
      selector: ".grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-3.xl\\:grid-cols-5 > div:first-child",
      position: "right"
    },
    {
      title: "Próximos Agendamentos",
      content: "Visualize seus próximos agendamentos com informações detalhadas sobre pacientes, profissionais e horários.",
      selector: ".bg-white.dark\\:bg-gray-800.rounded-xl h3:has(.calendar)",
      position: "top"
    },
    {
      title: "Ações Rápidas",
      content: "Acesse diretamente as funcionalidades mais utilizadas sem precisar navegar pelos diferentes módulos.",
      selector: "div:has(> h3:has(.activity))",
      position: "left",
      highlightPadding: 15
    },
    // {
    //   title: "Funcionalidades Principais",
    //   content: "Você pode agendar consultas, cadastrar pacientes ou visualizar sua agenda completa através destes atalhos.",
    //   selector: ".grid.grid-cols-1 button:first-child",
    //   position: "right"
    // }
  ];

//----------------------------------------------AGENDAMENTO--------------------------------------------------------------------------------------
  // Tutorial para Calendário de Agendamentos
  const calendarTutorial = [
    {
      title: "Calendário de Agendamentos",
      content: "Aqui você pode visualizar, criar e gerenciar todos os seus compromissos.",
      selector: ".fc-view-harness",
      position: "top"
    },
    {
      title: "Filtros",
      content: "Use estes filtros para encontrar agendamentos específicos por profissional, paciente, tipo de serviço ou local.",
      selector: ".filter-section",
      position: "bottom"
    },
    {
      title: "Criar Agendamento",
      content: "Para criar um novo agendamento, basta clicar em qualquer espaço vazio no calendário.",
      selector: ".fc-daygrid-day:not(.fc-day-other):nth-child(3)",
      position: "bottom"
    },
    {
      title: "Visualizações",
      content: "Alterne entre visualizações de mês, semana ou dia para ver seus agendamentos com diferentes níveis de detalhe.",
      selector: ".fc-toolbar-chunk:last-child",
      position: "bottom"
    }
  ];

  // Tutorial para página de horários de trabalho
  const workingHoursTutorial = [
    {
      title: "Horários de Trabalho",
      content: "Gerencie os Horários de Trabalho dos funcionários cadastrados no sistema.",
      selector: "table",
      position: "top"
    },
    {
      title: "Exportar Dados",
      content: "Exporte os horários de trabalho em diferentes formatos (Excel ou PDF) usando este botão.",
      selector: ".export-button",
      position: "bottom"
    },
    {
      title: "Horários de Trabalho pelo dia da Semana",
      content: "Clique aqui para mudar os dias a serem preenchidos.",
      selector: "#bordaDiaDaSemana",
      position: "left"
    },
    {
      title: "Horários selecionáveis",
      content: "Clique aqui para selecionar os horários daquele dia da semana que o profissional está disponível.",
      selector: "#bordaSelecaoHorario",
      position: "left"
    },
    {
      title: "Horários por Funcionário",
      content: "Clique aqui para selecionar os horários da semana completa de um único funcionário.",
      selector: "#bordaNomeFuncionario",
      position: "left"
    },
    {
      title: "Salve seus Horários e Utilize os Filtros",
      content: "Clique aqui para salvar os horários, e para visualizar utilize os filtros.",
      selector: "#bordaSalvarHorarioTrabalho",
      position: "right"
    }
  ];

  // Tutorial para página de localizações
  const locationsTutorial = [
    {
      title: "Localizações",
      content: "Gerencie os locais de atendimento disponíveis no sistema.",
      selector: "table",
      position: "top"
    },
    {
      title: "Exportar Dados",
      content: "Exporte a lista de localizações em diferentes formatos (Excel ou PDF) usando este botão.",
      selector: ".export-button",
      position: "bottom"
    },
    {
      title: "Adicionar Localização",
      content: "Clique aqui para cadastrar um novo local de atendimento.",
      selector: "button:has(.lucide-plus)",
      position: "left"
    }
  ];

    // Tutorial para página de ocupações
    const ocuppancyTutorial = [
      {
        title: "Análise de Ocupação",
        content: "Visualize a taxa de Ocupação de locais, profissionais e até pelo tipo do serviço caso deseje limitar.",
        selector: "#bordaTaxaOcupacao",
        position: "top"
      },
      {
        title: "Exportar Dados",
        content: "Exporte os dados de ocupação em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
      },
      {
        title: "Use os filtros!",
        content: "Clique para selecionar o periodo do tempo, a data referência, os profissionais, serviços ou até os locais.",
        selector: "#bordaFiltroOcupacao",
        position: "left"
      }
    ];

  // Tutorial para tipos de serviço
  const serviceTypesTutorial = [
    {
      title: "Tipos de Serviço",
      content: "Gerencie os serviços oferecidos pela sua clínica ou consultório.",
      selector: "table",
      position: "top"
    },
    {
      title: "Exportar Dados",
      content: "Exporte a lista de tipos de serviço em diferentes formatos (Excel ou PDF) usando este botão.",
      selector: ".export-button",
      position: "bottom"
    },
    {
      title: "Adicionar Tipo de Serviço",
      content: "Clique aqui para cadastrar um novo tipo de serviço.",
      selector: "button:has(.lucide-plus)",
      position: "left"
    }
  ];

//----------------------------------------------PESSOAS--------------------------------------------------------------------------------------
  // Tutorial para página de Clientes
  const clientsListTutorial = [
    {
      title: "Lista de Clientes",
      content: "Aqui você pode visualizar, filtrar e gerenciar todos os clientes cadastrados no sistema.",
      selector: "table",
      position: "top"
    },
    {
      title: "Pesquisa e Filtros",
      content: "Use estes campos para buscar clientes por nome ou email, e filtrar por status.",
      selector: "form",
      position: "bottom"
    },
    {
      title: "Adicionar Cliente",
      content: "Clique aqui para cadastrar um novo cliente no sistema.",
      selector: "button:has(.lucide-plus)",
      position: "left"
    }
  ];

  // Tutorial para página de Pacientes
  const patientsListTutorial = [
    {
      title: "Lista de Pacientes",
      content: "Aqui você pode visualizar, filtrar e gerenciar todos os pacientes cadastrados no sistema.",
      selector: "table",
      position: "top"
    },
    {
      title: "Pesquisa e Filtros",
      content: "Use estes campos para buscar pacientes por nome, email ou CPF, e filtrar por status.",
      selector: "form",
      position: "bottom"
    },
    {
      title: "Adicionar Paciente",
      content: "Clique aqui para cadastrar um novo paciente no sistema.",
      selector: "button:has(.lucide-plus)",
      position: "left"
    },
    {
      title: "Ações por Paciente",
      content: "Nesta coluna você pode visualizar detalhes, editar, ativar/desativar ou excluir um paciente.",
      selector: "td:last-child",
      position: "left"
    }
  ];

  // Tutorial para página de detalhes do paciente
  const patientDetailsTutorial = [
    {
      title: "Detalhes do Paciente",
      content: "Esta página mostra informações detalhadas sobre o paciente selecionado.",
      selector: "h1",
      position: "bottom"
    },
    {
      title: "Informações Pessoais",
      content: "Aqui você encontra os dados pessoais e de contato do paciente.",
      selector: "h2",
      position: "right"
    },
    {
      title: "Documentos",
      content: "Nesta seção você pode visualizar e gerenciar documentos relacionados ao paciente.",
      selector: "h3",
      position: "bottom"
    }
  ];

  // Tutorial para página de convênios
  const insurancesTutorial = [
    {
      title: "Convênios",
      content: "Gerencie os convênios disponíveis para seus pacientes nesta página.",
      selector: "table",
      position: "top"
    },
    {
      title: "Adicionar Convênio",
      content: "Clique aqui para cadastrar um novo convênio no sistema.",
      selector: "button:has(.lucide-plus)",
      position: "left"
    }
  ];

    // Tutorial para página de Usuários
    const admUsersTutorial = [
      {
        title: "Gerenciamento de Usuários",
        content: "Esta página permite gerenciar todos os usuários do sistema. Aqui você pode criar, editar, ativar/desativar e excluir usuários, além de gerenciar suas permissões e acesso aos módulos.",
        selector: ".ModuleHeader h1",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Exportar Dados",
        content: "Exporte a lista de usuários em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
      },
      {
        title: "Adicionar Novo Usuário",
        content: "Clique aqui para criar um novo usuário no sistema. Você poderá definir informações pessoais, credenciais de acesso, profissão, função e permissões.",
        selector: "button:has(.lucide-plus)",
        position: "left",
        dialogOffsetX: 15,
        highlightPadding: 5
      },
      {
        title: "Pesquisa e Filtros",
        content: "Use estes campos para buscar usuários por nome, email ou profissão. Você também pode filtrar por status (ativo/inativo) e por função (administrador, funcionário, etc).",
        selector: "#filtroUsuario",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Tabela de Usuários",
        content: "Esta tabela exibe todos os usuários do sistema com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.",
        selector: "#admin-users-table",
        position: "top",
        dialogOffsetY: 10
      },
      {
        title: "Ações de Usuário",
        content: "Aqui você encontra botões para editar informações, gerenciar módulos, permissões, função, ativar/desativar ou excluir usuários.",
        selector: "td:last-child .flex.justify-end",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Editar Usuário",
        content: "Clique neste botão para modificar as informações pessoais e credenciais do usuário selecionado.",
        selector: "#edicaoUsuario",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Gerenciar Módulos",
        content: "Este botão permite definir a quais módulos do sistema o usuário terá acesso. Administradores de sistema e de empresa têm acesso automático a todos os módulos.",
        selector: "#gerenciarModulo",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Gerenciar Permissões",
        content: "Controle detalhado das permissões do usuário dentro de cada módulo. Você pode definir o que o usuário pode visualizar, criar, editar ou excluir.",
        selector: "#gerenciarPermissoes",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Fluxo de Criação de Usuário",
        content: "O fluxo recomendado para criar um usuário é: 1) Informações pessoais, 2) Documentos, 3) Definir função, 4) Atribuir módulos (para funcionários), 5) Configurar permissões específicas.",
        selector: "#admin-users-table",
        position: "bottom",
        dialogOffsetY: 10
      }
    ];

//----------------------------------------------ADMINISTRAÇÃO--------------------------------------------------------------------------------------
    // Tutorial para página de Profissões/Grupos
    const professionsGroupsTutorial = [
      {
        title: "Gerenciamento de Profissões e Grupos",
        content: "Esta página permite gerenciar as profissões e grupos de profissões disponíveis no sistema. Estas informações são essenciais para a criação de usuários e definição de permissões.",
        selector: ".text-2xl.font-bold",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Abas de Navegação",
        content: "Alterne entre as abas 'Profissões' e 'Grupos de Profissões' para gerenciar cada tipo de cadastro separadamente.",
        selector: "button[role='tab']",
        position: "bottom",
        dialogOffsetY: 15
      },
      {
        title: "Exportar Dados",
        content: "Exporte a lista de profissões ou grupos em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
      },
      {
        title: "Adicionar Nova Profissão/Grupo",
        content: "Clique neste botão para criar uma nova profissão ou grupo no sistema. Você poderá definir nome, descrição, associações e status.",
        selector: "button:has(.lucide-plus)",
        position: "left",
        highlightPadding: 5,
        dialogOffsetX: 15
      },
      {
        title: "Filtros de Busca",
        content: "Use estes campos para buscar profissões ou grupos por nome, filtrar por status ou outras propriedades.",
        selector: "input[type='text']",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Tabela de Profissões/Grupos",
        content: "Esta tabela exibe todas as profissões ou grupos cadastrados com suas informações principais. Você pode ordenar por qualquer coluna clicando no cabeçalho.",
        selector: "table",
        position: "top",
        dialogOffsetY: 10
      },
      {
        title: "Ações Disponíveis",
        content: "Aqui você encontra botões para ver usuários associados, editar ou excluir profissões e grupos.",
        selector: "td:last-child .flex.justify-end",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Visibilidade de Empresa",
        content: "Administradores de sistema podem ver a qual empresa cada profissão ou grupo pertence. Administradores de empresa só veem registros da própria empresa.",
        selector: "th:nth-child(3)",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Dica Importante",
        content: "Configure primeiro os grupos de profissões antes de criar as profissões individuais. Isso facilitará a organização e atribuição de permissões posteriormente.",
        selector: "table",
        position: "bottom",
        dialogOffsetY: 10
      }
    ];

    // Tutorial para página de Configurações
    const settingsTutorial = [
      {
        title: "Configurações do Sistema",
        content: "Esta página permite personalizar diversos aspectos do sistema de acordo com as necessidades da sua empresa ou organização.",
        selector: "h1, .text-2xl.font-bold",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Navegação por Abas",
        content: "Use estas abas para navegar entre diferentes categorias de configurações. As opções disponíveis dependem do seu nível de acesso no sistema.",
        selector: "button.flex.items-center.gap-2",
        position: "bottom",
        dialogOffsetY: 15
      },
      {
        title: "Configurações Gerais",
        content: "Defina informações básicas como nome do site, URL, email administrativo, formato de data e hora, e outras configurações globais.",
        selector: "button:has(.lucide-cog), button:has(.lucide-settings)",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Configurações de Empresa",
        content: "Gerencie informações da empresa como nome, CNPJ, endereço, contatos e configurações específicas. Administradores de sistema podem gerenciar múltiplas empresas.",
        selector: "button:has(.lucide-building)",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Configurações de Unidades",
        content: "Administre as unidades ou filiais da empresa, incluindo endereços, horários de funcionamento e configurações específicas de cada local.",
        selector: "button:has(.lucide-map-pin)",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Configurações de Email",
        content: "Configure os servidores de email, modelos de mensagens e notificações automáticas enviadas pelo sistema.",
        selector: "button:has(.lucide-mail)",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Configurações de Backup",
        content: "Configure as opções de backup automático do sistema, incluindo frequência, horário e tipos de arquivos a serem salvos.",
        selector: "button:has(.lucide-database), button:has(.lucide-hard-drive)",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Configurações de Segurança",
        content: "Defina políticas de senha, autenticação de dois fatores e outras configurações de segurança para proteger o sistema.",
        selector: "button:has(.lucide-shield), button:has(.lucide-lock)",
        position: "left",
        dialogOffsetX: 15
      },
      {
        title: "Formulários de Configuração",
        content: "Preencha os formulários com as informações necessárias para configurar o sistema de acordo com as necessidades da sua empresa.",
        selector: "input[type='text'], select",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Opções de Localização",
        content: "Configure o fuso horário, formato de data e hora para todo o sistema de acordo com as necessidades da sua região.",
        selector: "select",
        position: "bottom",
        dialogOffsetY: 10
      },
      {
        title: "Salvar Alterações",
        content: "Lembre-se de clicar em 'Salvar' após fazer modificações em cada seção para que as alterações sejam aplicadas.",
        selector: "button.bg-primary-500, button.bg-orange-500, button[type='submit']",
        position: "left",
        dialogOffsetX: 15
      }
    ];

//----------------------------------------------DASHBOARDS NO GERAL--------------------------------------------------------------------------------------
    // Tutorial para o Dashboard de Administração
    const adminDashboardTutorial = [
      {
        title: "Dashboard de Administração",
        content: "Este painel oferece uma visão geral do sistema com métricas importantes, estatísticas de uso e informações sobre usuários e atividades recentes.",
        selector: "h1",
        position: "bottom"
      },
      {
        title: "Exportar Dados",
        content: "Exporte os dados do dashboard em diferentes formatos (Excel ou PDF) usando este botão.",
        selector: ".export-button",
        position: "bottom"
      },
      {
        title: "Seletor de Empresa",
        content: "Administradores de sistema podem selecionar diferentes empresas para visualizar dados específicos de cada uma. Administradores de empresa veem apenas dados da própria empresa.",
        selector: "select",
        position: "bottom"
      },
      {
        title: "Cartões de Estatísticas",
        content: "Estes cartões mostram métricas importantes como total de usuários, usuários ativos, clientes e agendamentos, com indicadores de crescimento.",
        selector: ".grid",
        position: "bottom"
      },
      {
        title: "Gráficos e Visualizações",
        content: "Visualize dados importantes do sistema através de gráficos interativos que mostram tendências e distribuições.",
        selector: ".lg\\:col-span-2",
        position: "bottom"
      },
      {
        title: "Seletor de Período",
        content: "Filtre os dados do gráfico por diferentes períodos de tempo para análises mais específicas.",
        selector: "select:nth-of-type(2)",
        position: "bottom"
      },
      {
        title: "Distribuição de Usuários",
        content: "Este gráfico mostra a distribuição de usuários por módulo, ajudando a identificar quais áreas do sistema são mais utilizadas.",
        selector: ".h-80",
        position: "left"
      },
      {
        title: "Tabelas de Informações",
        content: "Visualize informações detalhadas sobre usuários ativos e atividades recentes no sistema.",
        selector: "table",
        position: "bottom"
      },
      {
        title: "Informações do Sistema",
        content: "Visualize detalhes técnicos como versão do sistema, status do servidor, uso de recursos e outras informações relevantes para administradores.",
        selector: ".grid:last-child",
        position: "bottom"
      }
    ];

    // Tutorial para todos os tipos de Dashboards
    const dashboardsTutorial = [
      {
        title: "Dashboard",
        content: "Use filtros no seu Dashboard ou busque para visualizar as informações que deseja",
        selector: "#bordadashboards",
        position: "bottom"
      },
      {
        title: "Tipo de Dashboard",
        content: "Cada Dashboard tem suas informações retiradas dependendo de seu módulo.",
        selector: "#gradedashboards",
        position: "top"
      }
    ];

  // Mapa de rotas para tutoriais
  // Cada chave é um padrão de URL, e o valor é o tutorial correspondente
  const tutorialMap = {
    '/dashboard': dashboardTutorial,
    '/dashboard/admin/users': admUsersTutorial,
    '/dashboard/admin/dashboard': adminDashboardTutorial,
    '/dashboard/admin/professions': professionsGroupsTutorial,
    '/dashboard/admin/settings': settingsTutorial,
    '/dashboard/scheduler/appointments-dashboard': dashboardsTutorial,
    '/dashboard/scheduler/working-hours': workingHoursTutorial,
    '/dashboard/scheduler/occupancy': ocuppancyTutorial,
    '/dashboard/scheduler/calendar': calendarTutorial,
    '/dashboard/people/persons': patientsListTutorial,
    '/dashboard/people/clients': clientsListTutorial,
    '/dashboard/people/persons/[id]': patientDetailsTutorial,
    '/dashboard/people/insurances': insurancesTutorial,
    '/dashboard/scheduler/locations': locationsTutorial,
    '/dashboard/scheduler/service-types': serviceTypesTutorial,
  };

  /**
   * Função para obter o tutorial apropriado com base na rota atual
   * @param {string} pathname - Caminho da URL atual
   * @returns {Object} Tutorial correspondente ou null se não houver correspondência
   */
  export const getTutorialForRoute = (pathname) => {
    // Tenta encontrar uma correspondência exata primeiro
    if (tutorialMap[pathname]) {
      return {
        steps: tutorialMap[pathname],
        name: pathname.replace(/\//g, '-').replace(/^-/, '') // Converte '/dashboard' para 'dashboard'
      };
    }

    // Verifica rotas dinâmicas com parâmetros (ex: /dashboard/people/persons/123)
    for (const route in tutorialMap) {
      if (route.includes('[id]') && pathname.match(new RegExp(route.replace('[id]', '\\d+')))) {
        return {
          steps: tutorialMap[route],
          name: route.replace(/\//g, '-').replace(/^-/, '').replace('[id]', 'details')
        };
      }
    }

    // Se não encontrou nenhuma correspondência
    return null;
  };

  export default tutorialMap;