"use client";

import React, { useState, useEffect, useCallback } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleSelect, ModuleTable } from "@/components/ui";
import MultiSelect from "@/components/ui/multi-select";
import ExportMenu from "@/components/ui/ExportMenu";
import {
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Power,
  CheckCircle,
  XCircle,
  Mail,
  Users,
  Eye,
  Plus
} from "lucide-react";
import { clientsService } from "@/app/modules/people/services/clientsService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import ClientFormModal from "@/components/people/ClientFormModal";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { useRouter } from "next/navigation";

import { companyService } from "@/app/modules/admin/services/companyService";

// Tutorial steps para a página de clientes
const clientsTutorialSteps = [
  {
    title: "Clientes",
    content: "Esta tela permite gerenciar o cadastro de clientes no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Cliente",
    content: "Clique aqui para adicionar um novo cliente.",
    selector: "button:has(span:contains('Novo Cliente'))",
    position: "left"
  },
  {
    title: "Filtrar Clientes",
    content: "Use esta barra de pesquisa para encontrar clientes específicos pelo login ou email.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtrar por Status",
    content: "Filtre os clientes por status (ativos ou inativos).",
    selector: "select:first-of-type",
    position: "bottom"
  },
  {
    title: "Filtrar por Empresa",
    content: "Filtre os clientes pela empresa a que pertencem.",
    selector: "select:nth-of-type(2)",
    position: "bottom"
  },
  {
    title: "Filtrar por Múltiplos Clientes",
    content: "Selecione um ou mais clientes pelo nome completo para filtrar a lista.",
    selector: "div:has(> label:contains('Filtrar por clientes específicos'))",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de clientes em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Clientes",
    content: "Visualize, edite, ative/desative ou exclua clientes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const ClientsPage = () => {
  const { user: currentUser } = useAuth();
  const router = useRouter();
  const [clients, setClients] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalClients, setTotalClients] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [clientsFilter, setClientsFilter] = useState([]);
  const [clientOptions, setClientOptions] = useState([]);
  const [isLoadingClientOptions, setIsLoadingClientOptions] = useState(false);
  const [statusFilter, setStatusFilter] = useState("");
  const [companyFilter, setCompanyFilter] = useState("");
  const [companies, setCompanies] = useState([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedClient, setSelectedClient] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [clientFormOpen, setClientFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Constants
  const ITEMS_PER_PAGE = 10;

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (currentUser?.role !== "SYSTEM_ADMIN") return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Função para carregar opções de clientes para o multi-select
  const loadClientOptions = useCallback(async () => {
    setIsLoadingClientOptions(true);
    try {
      // Carregar todos os clientes para o multi-select (com limite maior)
      const response = await clientsService.getClients({
        limit: 100, // Limite maior para ter mais opções
        active: true, // Apenas clientes ativos por padrão
        include_persons: true // Incluir informações das pessoas associadas
      });

      const options = response?.clients?.map(client => {
        // Buscar a pessoa titular associada ao cliente
        const titularPerson = client.clientPersons?.find(cp =>
          cp.relationship === 'Titular' || cp.relationship === 'titular'
        )?.person;

        // Usar o nome completo da pessoa titular, ou o login do cliente se não encontrar
        const label = titularPerson?.fullName || client.login;

        return {
          value: client.id,
          label: label,
          // Guardar o nome para ordenação
          sortName: label.toLowerCase()
        };
      }) || [];

      // Ordenar as opções alfabeticamente pelo nome
      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName));

      setClientOptions(sortedOptions);
    } catch (error) {
      console.error("Erro ao carregar opções de clientes:", error);
      setClientOptions([]);
    } finally {
      setIsLoadingClientOptions(false);
    }
  }, []);

  const loadClients = async (
    page = currentPage,
    searchQuery = search,
    clientIds = clientsFilter,
    status = statusFilter,
    company = companyFilter,
    sortField = 'fullName', // Ordenar pelo nome completo do titular por padrão
    sortDirection = 'asc'
  ) => {
    setIsLoading(true);
    try {
      // Ensure page is a number
      const pageNumber = parseInt(page, 10);

      // Importante: Atualizamos o estado currentPage ANTES de fazer a requisição
      // para garantir que o ModuleTable use o valor correto
      setCurrentPage(pageNumber);

      const response = await clientsService.getClients({
        page: pageNumber,
        limit: ITEMS_PER_PAGE,
        search: searchQuery || undefined,
        clientIds: clientIds.length > 0 ? clientIds : undefined,
        active: status === "" ? undefined : status === "active",
        companyId: company || undefined,
        sortField,
        sortDirection,
      });

      // Make sure we always have an array even if the API response is different
      const clientsData = response?.clients || response?.data || [];

      if (!Array.isArray(clientsData)) {
        // Garantir que temos um array mesmo se a resposta for inválida
        const safeClientsData = [];
        setClients(safeClientsData);
      } else {
        // Update state with the new data
        setClients(clientsData);
      }

      setTotalClients(response?.total || 0);
      setTotalPages(response?.pages || 1);
    } catch (error) {
      console.error("Erro ao carregar clientes:", error);
      setClients([]);
      setTotalClients(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // Carregar clientes com ordenação padrão pelo nome completo
    loadClients(1, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');
    loadClientOptions();
    // Carregar empresas se o usuário for system_admin
    if (currentUser?.role === "SYSTEM_ADMIN") {
      loadCompanies();
    }
  }, [loadClientOptions]);

  const handleSearch = (e) => {
    e.preventDefault();
    loadClients(1, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');
  };

  const handleClientsFilterChange = (value) => {
    setClientsFilter(value);
    loadClients(1, search, value, statusFilter, companyFilter, 'fullName', 'asc');
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    loadClients(1, search, clientsFilter, value, companyFilter, 'fullName', 'asc');
  };

  const handleCompanyFilterChange = (value) => {
    setCompanyFilter(value);
    loadClients(1, search, clientsFilter, statusFilter, value, 'fullName', 'asc');
  };

  const handlePageChange = (page) => {
    // Importante: Não atualizamos o estado currentPage aqui, isso será feito dentro de loadClients
    // para garantir que o ModuleTable use o valor correto
    loadClients(page, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');
  };

  const handleResetFilters = () => {
    setSearch("");
    setClientsFilter([]);
    setStatusFilter("");
    setCompanyFilter("");
    loadClients(1, "", [], "", "", 'fullName', 'asc');
  };

  const handleEditClient = (client) => {
    setSelectedClient(client);
    setClientFormOpen(true);
  };

  const handleToggleStatus = (client) => {
    setSelectedClient(client);
    // Obter o nome completo do titular ou usar o login como fallback
    const clientName = client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName
      ? client.clientPersons[0].person.fullName
      : client.login;

    setActionToConfirm({
      type: "toggle-status",
      message: `${client.active ? "Desativar" : "Ativar"} o cliente ${clientName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeleteClient = (client) => {
    setSelectedClient(client);
    // Obter o nome completo do titular ou usar o login como fallback
    const clientName = client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName
      ? client.clientPersons[0].person.fullName
      : client.login;

    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente o cliente ${clientName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Encontrar o nome da empresa selecionada para o subtítulo da exportação
      let companyName;
      if (companyFilter) {
        const selectedCompany = companies.find(c => c.id === companyFilter);
        companyName = selectedCompany ? selectedCompany.name : undefined;
      }

      // Exportar usando os mesmos filtros da tabela atual
      await clientsService.exportClients({
        search: search || undefined,
        clientIds: clientsFilter.length > 0 ? clientsFilter : undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        companyId: companyFilter || undefined,
        companyName
      }, format);
    } catch (error) {
      console.error("Erro ao exportar clientes:", error);
      // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast
    } finally {
      setIsExporting(false);
    }
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await clientsService.toggleClientStatus(selectedClient.id);
        loadClients(currentPage, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');
      } catch (error) {
        console.error("Erro ao alterar status do cliente:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await clientsService.deleteClient(selectedClient.id);
        loadClients(currentPage, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');
      } catch (error) {
        console.error("Erro ao excluir cliente:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";

    try {
      return format(new Date(dateString), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return "Data inválida";
    }
  };

  // Verificar se o usuário é system_admin para mostrar o filtro de empresa
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  return (
    <div className="space-y-6">
      {/* Título e botões de exportar e adicionar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Users size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Clientes
        </h1>

        <div className="flex items-center gap-2">
          {/* Botão de exportar */}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || clients.length === 0}
            className="text-orange-700 dark:text-orange-300"
          />

          {/* Botão de adicionar */}
          <button
            onClick={() => {
              setSelectedClient(null);
              setClientFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all"
          >
            <Plus size={18} />
            <span className="font-medium">Novo Cliente</span>
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Gerencie o cadastro de clientes no sistema. Utilize os filtros abaixo para encontrar clientes específicos."
        tutorialSteps={clientsTutorialSteps}
        tutorialName="clients-overview"
        moduleColor="people"
        filters={
          <form
            onSubmit={handleSearch}
            className="flex flex-col gap-4"
          >
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5 z-10" />
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  placeholder="Buscar por login ou email..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full pl-10"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                <ModuleSelect
                  moduleColor="people"
                  value={statusFilter}
                  onChange={(e) => handleStatusFilterChange(e.target.value)}
                >
                  <option value="">Todos os status</option>
                  <option value="active">Ativos</option>
                  <option value="inactive">Inativos</option>
                </ModuleSelect>

                {/* Filtro de empresa (apenas para system_admin) */}
                {isSystemAdmin && (
                  <ModuleSelect
                    moduleColor="people"
                    value={companyFilter}
                    onChange={(e) => handleCompanyFilterChange(e.target.value)}
                    placeholder="Empresa"
                    disabled={isLoadingCompanies}
                  >
                    <option value="">Todas as empresas</option>
                    {companies.map((company) => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))}
                  </ModuleSelect>
                )}

                <FilterButton type="submit" moduleColor="people" variant="primary">
                  <Filter size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Filtrar</span>
                </FilterButton>

                <FilterButton
                  type="button"
                  onClick={handleResetFilters}
                  moduleColor="people"
                  variant="secondary"
                >
                  <RefreshCw size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Limpar</span>
                </FilterButton>
              </div>
            </div>

            {/* Multi-select para filtrar por múltiplos clientes */}
            <div className="w-full">
              <MultiSelect
                label="Filtrar por Clientes"
                value={clientsFilter}
                onChange={handleClientsFilterChange}
                options={clientOptions}
                placeholder="Selecione um ou mais clientes pelo nome..."
                loading={isLoadingClientOptions}
                moduleOverride="people"
              />
            </div>
          </form>
        }
      />

      {/* Tabela de Clientes */}
      <ModuleTable
        moduleColor="people"
        columns={[
          { header: 'Cliente', field: 'login', width: '20%' },
          { header: 'Email', field: 'email', width: '20%' },
          { header: 'Pessoas', field: 'persons', width: '15%' },
          { header: 'Status', field: 'active', width: '15%' },
          { header: 'Cadastro', field: 'createdAt', width: '15%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={clients}
        isLoading={isLoading}
        emptyMessage="Nenhum cliente encontrado"
        emptyIcon={<Users size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalClients}
        onPageChange={handlePageChange}
        showPagination={totalPages > 1}
        tableId="people-clients-table"
        enableColumnToggle={true}
        defaultSortField="login"
        defaultSortDirection="asc"
        onSort={(field, direction) => {
          // Quando a ordenação mudar, recarregar os clientes com os novos parâmetros de ordenação
          loadClients(
            currentPage,
            search,
            clientsFilter,
            statusFilter,
            companyFilter,
            field,
            direction
          );
        }}
        renderRow={(client, _, moduleColors, visibleColumns) => (
          <tr key={client.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('login') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden">
                    {/* Obter a primeira letra do nome do titular ou do login */}
                    {(client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName
                      ? client.clientPersons[0].person.fullName.charAt(0)
                      : client.login.charAt(0)).toUpperCase()}
                  </div>
                  <div className="ml-3 min-w-0">
                    <p className="text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate">
                      {/* Mostrar o nome completo do titular ou o login como fallback */}
                      {client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName
                        ? client.clientPersons[0].person.fullName
                        : client.login}
                    </p>
                    <p className="text-xs text-neutral-500 dark:text-neutral-400 truncate">
                      {client.login}
                    </p>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('email') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                <div className="flex items-center gap-1">
                  <Mail className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                  <span className="truncate">{client.email}</span>
                </div>
              </td>
            )}

            {visibleColumns.includes('persons') && (
              <td className="px-4 py-4">
                <div className="flex items-center gap-1">
                  <Users className="h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0" />
                  <span className="text-neutral-600 dark:text-neutral-300 text-sm">
                    {client.clientPersons?.length || 0} {(client.clientPersons?.length || 0) === 1 ? "pessoa" : "pessoas"}
                  </span>
                </div>
              </td>
            )}

            {visibleColumns.includes('active') && (
              <td className="px-4 py-4">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${client.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                    }`}
                >
                  {client.active ? (
                    <>
                      <CheckCircle size={12} className="flex-shrink-0" />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} className="flex-shrink-0" />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}

            {visibleColumns.includes('createdAt') && (
              <td className="px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300">
                {formatDate(client.createdAt)}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-4 py-4 text-right text-sm font-medium">
                <div className="flex justify-end gap-1">
                  <button
                    onClick={() => router.push(`/dashboard/people/clients/${client.id}`)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors"
                    title="Ver detalhes"
                  >
                    <Eye size={16} />
                  </button>

                  <button
                    onClick={() => handleEditClient(client)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>

                  <button
                    onClick={() => handleToggleStatus(client)}
                    className={`p-1 transition-colors ${client.active
                        ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                        : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                      }`}
                    title={client.active ? "Desativar" : "Ativar"}
                  >
                    <Power size={16} />
                  </button>

                  <button
                    onClick={() => handleDeleteClient(client)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
      />

      {/* Client Form Modal */}
      {clientFormOpen && (
        <ClientFormModal
          isOpen={clientFormOpen}
          onClose={() => setClientFormOpen(false)}
          client={selectedClient}
          onSuccess={() => {
            setClientFormOpen(false);
            loadClients(1, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default ClientsPage;