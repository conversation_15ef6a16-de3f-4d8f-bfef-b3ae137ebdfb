import React, { useState, useEffect } from "react";
import { AlertCircle, CreditCard, FileText } from "lucide-react";
import { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from "@/components/ui";
import { insuranceServiceLimitService } from "@/app/modules/people/services/insuranceServiceLimitService";
import { appointmentService } from "@/app/modules/scheduler/services/appointmentService";
import { insurancesService } from "@/app/modules/people/services/insurancesService";

const InsuranceServiceLimitFormModal = ({ isOpen, onClose, limit = null, personId, onSuccess }) => {
  const [formData, setFormData] = useState({
    personId: personId,
    insuranceId: "",
    serviceTypeId: "",
    monthlyLimit: 0,
    notes: ""
  });

  const [insurances, setInsurances] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [personInsurances, setPersonInsurances] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Carregar tipos de serviço
        const serviceTypesData = await appointmentService.getServiceTypes();
        setServiceTypes(serviceTypesData);

        // Carregar convênios da pessoa
        const insurancesResponse = await insurancesService.listPersonInsurances(personId);
        console.log("Resposta da API (convênios):", insurancesResponse);

        // Verifica e adapta os dados com base na estrutura retornada
        if (Array.isArray(insurancesResponse)) {
          setPersonInsurances(insurancesResponse);
        } else if (insurancesResponse && typeof insurancesResponse === 'object') {
          // Se for um objeto, procura por array em propriedades comuns
          const possibleArrays = ['personInsurances', 'insurances', 'data', 'items', 'results'];
          for (const prop of possibleArrays) {
            if (Array.isArray(insurancesResponse[prop])) {
              setPersonInsurances(insurancesResponse[prop]);
              break;
            }
          }
        } else {
          // Se não conseguiu encontrar, define como array vazio
          setPersonInsurances([]);
        }

      } catch (err) {
        console.error("Erro ao carregar dados:", err);
        setError("Erro ao carregar dados. Por favor, tente novamente.");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadData();
    }
  }, [isOpen, personId]);

  // Preencher formulário quando editando
  useEffect(() => {
    if (limit) {
      setFormData({
        personId: personId,
        insuranceId: limit.insuranceId,
        serviceTypeId: limit.serviceTypeId,
        monthlyLimit: limit.monthlyLimit,
        notes: limit.notes || ""
      });
    } else {
      setFormData({
        personId: personId,
        insuranceId: "",
        serviceTypeId: "",
        monthlyLimit: 0,
        notes: ""
      });
    }
  }, [limit, personId]);

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name === 'monthlyLimit') {
      setFormData(prev => ({
        ...prev,
        [name]: parseInt(value, 10) || 0
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      if (limit) {
        await insuranceServiceLimitService.updateLimit(limit.id, formData);
      } else {
        await insuranceServiceLimitService.createLimit(formData);
      }

      onSuccess();
    } catch (err) {
      console.error("Erro ao salvar limite:", err);
      setError(err.response?.data?.message || "Ocorreu um erro ao salvar o limite de serviço.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Detecta automaticamente a estrutura dos dados
  let availableInsurances = [];
  if (personInsurances.length > 0) {
    // Se o primeiro item já tiver propriedades name/id, é um array de convênios direto
    if (personInsurances[0].name && personInsurances[0].id) {
      availableInsurances = personInsurances;
    }
    // Se tiver propriedade insurance, é o formato personalInsurance
    else if (personInsurances[0].insurance) {
      availableInsurances = personInsurances.map(pi => pi.insurance);
    }
    // Se tiver propriedade insuranceId e não tiver insurance, precisa montar o objeto
    else if (personInsurances[0].insuranceId) {
      availableInsurances = personInsurances.map(pi => ({
        id: pi.insuranceId,
        name: pi.insuranceName || `Convênio ${pi.insuranceId}`
      }));
    }

    console.log("Convênios disponíveis:", availableInsurances);
  }

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="people"
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="people"
        type="submit"
        form="insurance-service-limit-form"
        isLoading={isSubmitting}
        disabled={isSubmitting || !formData.insuranceId || !formData.serviceTypeId}
      >
        {limit ? "Atualizar" : "Salvar"}
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={limit ? "Editar Limite de Serviço" : "Novo Limite de Serviço"}
      icon={<CreditCard size={22} />}
      moduleColor="people"
      size="md"
      animateExit={true}
      footer={modalFooter}
    >
      <form id="insurance-service-limit-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6">
          {error && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start gap-2">
              <AlertCircle size={16} className="mt-0.5" />
              <span>{error}</span>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
            </div>
          ) : (
            <>
              <div>
                <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4">
                  <CreditCard className="w-4 h-4" />
                  Informações do Limite de Serviço
                </h4>
              </div>

              <ModuleFormGroup
                moduleColor="people"
                label="Convênio"
                htmlFor="insuranceId"
                icon={<CreditCard size={16} />}
                required
                helpText={personInsurances.length === 0 && !isLoading ? "Esta pessoa não possui convênios cadastrados. Adicione convênios primeiro." : ""}
              >
                <ModuleSelect
                  moduleColor="people"
                  id="insuranceId"
                  name="insuranceId"
                  value={formData.insuranceId}
                  onChange={handleChange}
                  required
                  disabled={isSubmitting || (limit !== null)}
                  placeholder="Selecione um convênio"
                >
                  {availableInsurances.length === 0 ? (
                    <option disabled>Nenhum convênio disponível</option>
                  ) : (
                    availableInsurances.map(insurance => (
                      <option key={insurance.id} value={insurance.id}>
                        {insurance.name}
                      </option>
                    ))
                  )}
                </ModuleSelect>
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Tipo de Serviço"
                htmlFor="serviceTypeId"
                icon={<FileText size={16} />}
                required
              >
                <ModuleSelect
                  moduleColor="people"
                  id="serviceTypeId"
                  name="serviceTypeId"
                  value={formData.serviceTypeId}
                  onChange={handleChange}
                  required
                  disabled={isSubmitting || (limit !== null)}
                  placeholder="Selecione um tipo de serviço"
                >
                  {serviceTypes.length === 0 ? (
                    <option disabled>Nenhum tipo de serviço disponível</option>
                  ) : (
                    serviceTypes.map(serviceType => (
                      <option key={serviceType.id} value={serviceType.id}>
                        {serviceType.name}
                      </option>
                    ))
                  )}
                </ModuleSelect>
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Limite Mensal"
                htmlFor="monthlyLimit"
                icon={<CreditCard size={16} />}
                helpText="0 = Ilimitado"
              >
                <ModuleInput
                  moduleColor="people"
                  type="number"
                  id="monthlyLimit"
                  name="monthlyLimit"
                  value={formData.monthlyLimit}
                  onChange={handleChange}
                  min="0"
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Observações"
                htmlFor="notes"
                icon={<FileText size={16} />}
              >
                <ModuleTextarea
                  moduleColor="people"
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                />
              </ModuleFormGroup>

            </>
          )}
        </form>
    </ModuleModal>
  );
};

export default InsuranceServiceLimitFormModal;