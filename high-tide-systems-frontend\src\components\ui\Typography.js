/**
 * Componentes de Tipografia
 * 
 * Este arquivo contém componentes para tipografia consistente em todo o sistema.
 * Todos os componentes seguem o sistema de design definido.
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * Componente de título principal (h1)
 */
export const Heading1 = ({ children, className, ...props }) => {
  return (
    <h1 
      className={cn(
        "text-3xl md:text-4xl font-bold text-neutral-900 dark:text-white tracking-tight",
        className
      )}
      {...props}
    >
      {children}
    </h1>
  );
};

/**
 * Componente de título secundário (h2)
 */
export const Heading2 = ({ children, className, ...props }) => {
  return (
    <h2 
      className={cn(
        "text-2xl md:text-3xl font-semibold text-neutral-800 dark:text-neutral-100 tracking-tight",
        className
      )}
      {...props}
    >
      {children}
    </h2>
  );
};

/**
 * Componente de título terciário (h3)
 */
export const Heading3 = ({ children, className, ...props }) => {
  return (
    <h3 
      className={cn(
        "text-xl md:text-2xl font-semibold text-neutral-800 dark:text-neutral-100",
        className
      )}
      {...props}
    >
      {children}
    </h3>
  );
};

/**
 * Componente de título quaternário (h4)
 */
export const Heading4 = ({ children, className, ...props }) => {
  return (
    <h4 
      className={cn(
        "text-lg md:text-xl font-medium text-neutral-800 dark:text-neutral-100",
        className
      )}
      {...props}
    >
      {children}
    </h4>
  );
};

/**
 * Componente de subtítulo (h5)
 */
export const Heading5 = ({ children, className, ...props }) => {
  return (
    <h5 
      className={cn(
        "text-base md:text-lg font-medium text-neutral-700 dark:text-neutral-200",
        className
      )}
      {...props}
    >
      {children}
    </h5>
  );
};

/**
 * Componente de subtítulo menor (h6)
 */
export const Heading6 = ({ children, className, ...props }) => {
  return (
    <h6 
      className={cn(
        "text-sm md:text-base font-medium text-neutral-700 dark:text-neutral-200",
        className
      )}
      {...props}
    >
      {children}
    </h6>
  );
};

/**
 * Componente de parágrafo grande
 */
export const LargeParagraph = ({ children, className, ...props }) => {
  return (
    <p 
      className={cn(
        "text-lg text-neutral-700 dark:text-neutral-300 leading-relaxed",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
};

/**
 * Componente de parágrafo padrão
 */
export const Paragraph = ({ children, className, ...props }) => {
  return (
    <p 
      className={cn(
        "text-base text-neutral-700 dark:text-neutral-300 leading-normal",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
};

/**
 * Componente de parágrafo pequeno
 */
export const SmallParagraph = ({ children, className, ...props }) => {
  return (
    <p 
      className={cn(
        "text-sm text-neutral-600 dark:text-neutral-400 leading-normal",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
};

/**
 * Componente de texto de legenda
 */
export const Caption = ({ children, className, ...props }) => {
  return (
    <p 
      className={cn(
        "text-xs text-neutral-500 dark:text-neutral-500 leading-normal",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
};

/**
 * Componente de texto em destaque
 */
export const Lead = ({ children, className, ...props }) => {
  return (
    <p 
      className={cn(
        "text-xl text-neutral-700 dark:text-neutral-300 leading-relaxed",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
};

/**
 * Componente de texto em negrito
 */
export const Bold = ({ children, className, ...props }) => {
  return (
    <span 
      className={cn(
        "font-semibold text-neutral-900 dark:text-white",
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
};

/**
 * Componente de texto em itálico
 */
export const Italic = ({ children, className, ...props }) => {
  return (
    <em 
      className={cn(
        "italic text-neutral-800 dark:text-neutral-200",
        className
      )}
      {...props}
    >
      {children}
    </em>
  );
};

/**
 * Componente de texto sublinhado
 */
export const Underline = ({ children, className, ...props }) => {
  return (
    <span 
      className={cn(
        "underline underline-offset-4 decoration-neutral-400 dark:decoration-neutral-600",
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
};

/**
 * Componente de texto com marca (highlight)
 */
export const Highlight = ({ children, className, ...props }) => {
  return (
    <mark 
      className={cn(
        "bg-yellow-100 dark:bg-yellow-900/30 text-neutral-900 dark:text-yellow-100 px-1 py-0.5 rounded",
        className
      )}
      {...props}
    >
      {children}
    </mark>
  );
};

/**
 * Componente de texto com código
 */
export const InlineCode = ({ children, className, ...props }) => {
  return (
    <code 
      className={cn(
        "bg-neutral-100 dark:bg-neutral-800 text-neutral-800 dark:text-neutral-200 px-1.5 py-0.5 rounded font-mono text-sm",
        className
      )}
      {...props}
    >
      {children}
    </code>
  );
};

/**
 * Componente de texto com link
 */
export const Link = ({ href, children, className, ...props }) => {
  return (
    <a 
      href={href}
      className={cn(
        "text-primary-600 dark:text-primary-400 hover:underline underline-offset-4 transition-colors",
        className
      )}
      {...props}
    >
      {children}
    </a>
  );
};

/**
 * Componente de texto com lista não ordenada
 */
export const UnorderedList = ({ children, className, ...props }) => {
  return (
    <ul 
      className={cn(
        "list-disc list-outside pl-6 space-y-2 text-neutral-700 dark:text-neutral-300",
        className
      )}
      {...props}
    >
      {children}
    </ul>
  );
};

/**
 * Componente de texto com lista ordenada
 */
export const OrderedList = ({ children, className, ...props }) => {
  return (
    <ol 
      className={cn(
        "list-decimal list-outside pl-6 space-y-2 text-neutral-700 dark:text-neutral-300",
        className
      )}
      {...props}
    >
      {children}
    </ol>
  );
};

/**
 * Componente de item de lista
 */
export const ListItem = ({ children, className, ...props }) => {
  return (
    <li 
      className={cn(
        "text-base leading-normal",
        className
      )}
      {...props}
    >
      {children}
    </li>
  );
};

/**
 * Componente de bloco de citação
 */
export const Blockquote = ({ children, className, ...props }) => {
  return (
    <blockquote 
      className={cn(
        "border-l-4 border-neutral-300 dark:border-neutral-700 pl-4 py-1 text-neutral-700 dark:text-neutral-300 italic",
        className
      )}
      {...props}
    >
      {children}
    </blockquote>
  );
};
