const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const path = require('path');
const swaggerUi = require('swagger-ui-express');
const swaggerFile = require('./swagger-output.json');

// Importar rotas

//Admin module
const userRoutes = require('./routes/admin/userRoutes');
const emailConfigRoutes = require('./routes/admin/emailConfigRoutes');
const companyRoutes = require('./routes/admin/companyRoutes');
const auditLogRoutes = require('./routes/admin/auditLogRoutes');
const branchRoutes = require('./routes/admin/branchRoutes');
const adminDashboardRoutes = require('./routes/admin/adminDashboardRoutes');
const settingsRoutes = require('./routes/admin/settingsRoutes');

//Person module
const documentRoutes = require('./routes/person/documentRoutes');
const locationRoutes = require('./routes/person/locationRoutes');
const clientRoutes = require('./routes/person/clientRoutes');
const insuranceRoutes = require('./routes/person/insuranceRoutes');
const insuranceServiceLimitRoutes = require('./routes/person/insuranceServiceLimitRoutes');
const serviceTypeRoutes = require('./routes/scheduling/serviceTypeRoutes');
const personRoutes = require('./routes/person/personRoutes');
const contactRoutes = require('./routes/person/contactRoutes');

//Scheduling Module
const schedulingRoutes = require('./routes/scheduling/schedulingRoutes');
const recurrenceRoutes = require('./routes/scheduling/recurrenceRoutes');
const workingHoursRoutes = require('./routes/scheduling/workingHoursRoutes');
const notificationRoutes = require('./routes/notificationRoutes');
const occupancyRoutes = require('./routes/person/occupancyRoutes');

//Reports Module
const reportRoutes = require('./routes/reports/reportRoutes');

//ABA+ Module
const abaRoutes = require('./routes/aba');

const authRoutes = require('./routes/authRoutes');
const cepRoutes = require('./routes/cepRoutes');
const professionRoutes = require('./routes/professionRoutes');
const modulePreferencesRoutes = require('./routes/modulePreferencesRoutes');

// Criar aplicação Express
const app = express();

// Configurar middleware
app.use(cors());
app.use(helmet());
app.use(express.json());

// Middleware para padronizar respostas da API
// Ativado apenas para o endpoint de agendamentos para testar
const responseFormatter = require('./middlewares/responseFormatter');
app.use('/schedulings', responseFormatter());
app.use(express.urlencoded({ extended: true }));

// Configurar logging
if (process.env.NODE_ENV !== 'test') {
  app.use(morgan('dev'));
}

// Servir arquivos estáticos
const uploadsPath = path.join(__dirname, '..', 'uploads');
const containerUploadsPath = '/usr/src/app/uploads';

console.log('[app.js] Caminhos para uploads:');
console.log('[app.js] - Local uploads path:', uploadsPath);
console.log('[app.js] - Container uploads path:', containerUploadsPath);

// Middleware para logar acessos a arquivos estáticos
const logStaticAccess = (req, res, next) => {
  console.log('[app.js] Acessando arquivo estático:', req.url);
  next();
};

// Middleware para extrair apenas o nome do arquivo
const extractFilename = (req, res, next) => {
  // Se a URL contiver um caminho completo, extrair apenas o nome do arquivo
  if (req.url.includes('/')) {
    const segments = req.url.split('/');
    const filename = segments[segments.length - 1];

    // Verificar se o arquivo existe diretamente na pasta uploads
    const fs = require('fs');
    const directPath = path.join(uploadsPath, filename);

    if (fs.existsSync(directPath)) {
      console.log('[app.js] Arquivo encontrado diretamente em uploads:', filename);
      req.url = `/${filename}`;
    } else {
      // Procurar o arquivo em subdiretórios
      console.log('[app.js] Procurando arquivo em subdiretórios:', filename);

      // Procurar recursivamente em uploads/documents
      const findFileRecursive = (dir, targetFile) => {
        const files = fs.readdirSync(dir);

        for (const file of files) {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);

          if (stat.isDirectory()) {
            const found = findFileRecursive(filePath, targetFile);
            if (found) return found;
          } else if (file === targetFile) {
            return filePath;
          }
        }

        return null;
      };

      const foundPath = findFileRecursive(uploadsPath, filename);
      if (foundPath) {
        console.log('[app.js] Arquivo encontrado em:', foundPath);
        // Usar o caminho relativo a partir de uploads
        const relativePath = path.relative(uploadsPath, foundPath);
        req.url = `/${relativePath}`;
      }
    }
  }

  next();
};

// Servir arquivos da pasta uploads com extração de nome de arquivo
app.use('/uploads', logStaticAccess, extractFilename, express.static(uploadsPath, {
  fallthrough: false, // Retornar 404 se o arquivo não for encontrado
  maxAge: '1d' // Cache de 1 dia
}));

// Rota especial para servir arquivos pelo nome, ignorando o caminho
app.get('/uploads/file/:filename', (req, res) => {
  const { filename } = req.params;
  console.log('[app.js] Acessando arquivo pelo nome:', filename);

  const fs = require('fs');

  // Procurar o arquivo diretamente em uploads
  const directPath = path.join(uploadsPath, filename);
  if (fs.existsSync(directPath)) {
    console.log('[app.js] Arquivo encontrado diretamente em uploads:', directPath);
    return res.sendFile(directPath);
  }

  // Procurar o arquivo em subdiretórios
  const findFileRecursive = (dir, targetFile) => {
    if (!fs.existsSync(dir)) {
      console.log('[app.js] Diretório não existe:', dir);
      return null;
    }

    try {
      const files = fs.readdirSync(dir);

      for (const file of files) {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);

        if (stat.isDirectory()) {
          const found = findFileRecursive(filePath, targetFile);
          if (found) return found;
        } else if (file === targetFile) {
          return filePath;
        }
      }
    } catch (error) {
      console.error('[app.js] Erro ao ler diretório:', error);
    }

    return null;
  };

  // Procurar em todos os subdiretórios
  const foundPath = findFileRecursive(uploadsPath, filename);
  if (foundPath) {
    console.log('[app.js] Arquivo encontrado em:', foundPath);
    return res.sendFile(foundPath);
  }

  // Procurar especificamente no diretório documents
  const documentsPath = path.join(uploadsPath, 'documents');
  if (fs.existsSync(documentsPath)) {
    console.log('[app.js] Procurando em documents...');
    const foundInDocuments = findFileRecursive(documentsPath, filename);
    if (foundInDocuments) {
      console.log('[app.js] Arquivo encontrado em documents:', foundInDocuments);
      return res.sendFile(foundInDocuments);
    }
  }

  // Listar todos os arquivos disponíveis para debug
  console.log('[app.js] Arquivo não encontrado. Listando arquivos disponíveis:');
  const listFilesRecursive = (dir, prefix = '') => {
    if (!fs.existsSync(dir)) return;

    try {
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        if (stat.isDirectory()) {
          listFilesRecursive(filePath, `${prefix}${file}/`);
        } else {
          console.log(`${prefix}${file}`);
        }
      });
    } catch (error) {
      console.error('[app.js] Erro ao listar arquivos:', error);
    }
  };

  listFilesRecursive(uploadsPath);

  // Arquivo não encontrado
  res.status(404).json({
    error: 'Arquivo não encontrado',
    filename,
    searchedPaths: [
      directPath,
      path.join(uploadsPath, 'documents')
    ]
  });
});

// Adicionar rota para verificar se o arquivo existe
app.get('/check-file', (req, res) => {
  const { filepath } = req.query;
  if (!filepath) {
    return res.status(400).json({ exists: false, message: 'Caminho do arquivo não fornecido' });
  }

  const fullPath = path.join(uploadsPath, filepath);
  console.log('[app.js] Verificando se o arquivo existe:', fullPath);

  const fs = require('fs');
  const exists = fs.existsSync(fullPath);

  // Listar arquivos no diretório uploads
  let files = [];
  try {
    files = fs.readdirSync(uploadsPath);
  } catch (error) {
    console.error('[app.js] Erro ao listar arquivos:', error);
  }

  res.json({
    exists,
    requestedPath: filepath,
    fullPath,
    uploadsPath,
    files
  });
});

// Configurar rotas
app.use('/auth', authRoutes);
app.use('/users', userRoutes);
app.use('/documents', documentRoutes);
app.use('/schedulings', schedulingRoutes);
app.use('/locations', locationRoutes);
app.use('/service-types', serviceTypeRoutes);
app.use('/insurances', insuranceRoutes);
app.use('/insurance-service-limits', insuranceServiceLimitRoutes);
app.use('/clients', clientRoutes);
app.use('/recurrences', recurrenceRoutes);
app.use('/working-hours', workingHoursRoutes);
app.use('/notifications', notificationRoutes);
app.use('/email-configs', emailConfigRoutes);
app.use('/branches', branchRoutes);
app.use('/companies', companyRoutes);
app.use('/audit-logs', auditLogRoutes);
app.use('/persons', personRoutes);
app.use('/contacts', contactRoutes);
app.use('/adminDashboard', adminDashboardRoutes);
app.use('/occupancy', occupancyRoutes);
app.use('/cep', cepRoutes);
app.use('/settings', settingsRoutes);
app.use('/reports', reportRoutes);
app.use('/professions', professionRoutes);
app.use('/module-preferences', modulePreferencesRoutes);
app.use('/aba', abaRoutes);

// Configurar Swagger
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerFile));

// Rota de status
app.get('/', (req, res) => {
  res.json({
    status: 'API running',
    version: '1.0.0',
    documentation: `${req.protocol}://${req.get('host')}/api-docs`
  });
});

// Middleware de tratamento de erros
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Erro interno do servidor',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

module.exports = app;
