import React, { useState, useEffect } from "react";
import { AlertCircle, Calendar, CreditCard, FileText } from "lucide-react";
import { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleTextarea, ModuleFormGroup } from "@/components/ui";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { format } from "date-fns";

const PersonInsuranceFormModal = ({ isOpen, onClose, personId, personInsurance, onSuccess, isCreating = false, onAddTempInsurance }) => {
  const [formData, setFormData] = useState({
    personId: personId,
    insuranceId: "",
    policyNumber: "",
    validUntil: "",
    notes: ""
  });

  const [allInsurances, setAllInsurances] = useState([]);
  const [availableInsurances, setAvailableInsurances] = useState([]);
  const [personInsurances, setPersonInsurances] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Carregar todos os convênios disponíveis no sistema
        const insurancesData = await insurancesService.getInsurances();
        setAllInsurances(insurancesData);

        if (!isCreating && personId) {
          // Carregar convênios já associados a esta pessoa (apenas no modo de edição)
          const personInsurancesData = await insurancesService.listPersonInsurances(personId);
          setPersonInsurances(personInsurancesData);

          // Filtrar convênios disponíveis (que ainda não estão associados à pessoa)
          filterAvailableInsurances(insurancesData, personInsurancesData);
        } else {
          // No modo de criação, todos os convênios estão disponíveis
          setAvailableInsurances(insurancesData);
          setPersonInsurances([]);
        }
      } catch (err) {
        console.error("Erro ao carregar dados:", err);
        setError("Erro ao carregar dados. Por favor, tente novamente.");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadData();
    }
  }, [isOpen, personId, isCreating]);

  const filterAvailableInsurances = (allIns, personIns) => {
    // Extrair IDs dos convênios da pessoa
    const personInsuranceIds = personIns.map(pi => {
      if (pi.insuranceId) return pi.insuranceId;
      if (pi.insurance && pi.insurance.id) return pi.insurance.id;
      return pi.id;
    });

    // Filtrar convênios não associados (para o modo de adição)
    const available = allIns.filter(insurance =>
      !personInsuranceIds.includes(insurance.id)
    );

    setAvailableInsurances(available);
  };

  // Preencher formulário quando editando
  useEffect(() => {
    if (personInsurance) {
      let insuranceId = personInsurance.insuranceId ||
                       (personInsurance.insurance ? personInsurance.insurance.id : personInsurance.id);

      let validUntilFormatted = "";
      try {
        if (personInsurance.validUntil) {
          validUntilFormatted = format(new Date(personInsurance.validUntil), "yyyy-MM-dd");
        }
      } catch (e) {
        console.error("Error formatting date:", e);
      }

      setFormData({
        personId: personId,
        insuranceId: insuranceId,
        policyNumber: personInsurance.policyNumber || "",
        validUntil: validUntilFormatted,
        notes: personInsurance.notes || ""
      });
    } else {
      setFormData({
        personId: personId,
        insuranceId: "",
        policyNumber: "",
        validUntil: "",
        notes: ""
      });
    }
  }, [personInsurance, personId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Converter validUntil para formato ISO se fornecido
      const validUntilFormatted = formData.validUntil
        ? new Date(formData.validUntil + 'T00:00:00Z').toISOString()
        : null;

      // Preparar o payload
      const payload = {
        personId: formData.personId,
        insuranceId: formData.insuranceId,
        policyNumber: formData.policyNumber,
        validUntil: validUntilFormatted,
        notes: formData.notes
      };

      if (isCreating) {
        // Modo de criação - adicionar convênio temporário
        if (onAddTempInsurance) {
          // Adicionar informações do convênio selecionado
          const selectedInsurance = allInsurances.find(ins => ins.id === formData.insuranceId);

          onAddTempInsurance({
            ...payload,
            name: selectedInsurance?.name || "Convênio",
            isTemp: true
          });

          // Exibir toast de sucesso
          if (window.showToast) {
            window.showToast({
              type: "success",
              message: "Convênio adicionado temporariamente"
            });
          }

          // Fechar o modal
          onSuccess();
        }
      } else if (personInsurance) {
        // Modo de edição
        await insurancesService.updatePersonInsurance(personId, formData.insuranceId, {
          policyNumber: formData.policyNumber,
          validUntil: validUntilFormatted,
          notes: formData.notes
        });

        // Chamar callback de sucesso
        onSuccess();

        // Exibir toast de sucesso
        if (window.showToast) {
          window.showToast({
            type: "success",
            message: "Convênio atualizado com sucesso"
          });
        }
      } else {
        // Modo de adição normal
        await insurancesService.addPersonInsurance(payload);

        // Chamar callback de sucesso
        onSuccess();

        // Exibir toast de sucesso
        if (window.showToast) {
          window.showToast({
            type: "success",
            message: "Convênio adicionado com sucesso"
          });
        }
      }
    } catch (err) {
      console.error("Erro ao salvar convênio:", err);
      const errorMessage = err.response?.data?.message || "Ocorreu um erro ao salvar o convênio.";
      setError(errorMessage);

      // Exibir toast de erro se disponível
      if (window.showToast) {
        window.showToast({
          type: "error",
          message: errorMessage
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Função para buscar o nome de um convênio pelo ID
  const getInsuranceName = (id) => {
    const insurance = allInsurances.find(ins => ins.id === id);
    return insurance ? insurance.name : "Convênio";
  };

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="people"
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="people"
        type="submit"
        form="person-insurance-form"
        isLoading={isSubmitting}
        disabled={isSubmitting || (!personInsurance && !formData.insuranceId)}
      >
        {personInsurance ? "Atualizar" : "Adicionar"}
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={personInsurance ? `Editar ${getInsuranceName(formData.insuranceId)}` : "Adicionar Convênio"}
      icon={<CreditCard size={22} />}
      moduleColor="people"
      size="md"
      animateExit={true}
      footer={modalFooter}
    >
      <form id="person-insurance-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6">
          {error && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start gap-2">
              <AlertCircle size={16} className="mt-0.5" />
              <span>{error}</span>
            </div>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
            </div>
          ) : (
            <>
              <div>
                <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-4">
                  <CreditCard className="w-4 h-4" />
                  Informações do Convênio
                </h4>
              </div>

              {!personInsurance && (
                <ModuleFormGroup
                  moduleColor="people"
                  label="Convênio"
                  htmlFor="insuranceId"
                  icon={<CreditCard size={16} />}
                  required
                  helpText={availableInsurances.length === 0 && !isLoading ? "Todos os convênios já estão associados a esta pessoa." : ""}
                >
                  <ModuleSelect
                    moduleColor="people"
                    id="insuranceId"
                    name="insuranceId"
                    value={formData.insuranceId}
                    onChange={handleChange}
                    required
                    disabled={isSubmitting}
                    placeholder="Selecione um convênio"
                  >
                    {availableInsurances.length === 0 ? (
                      <option disabled>Nenhum convênio disponível</option>
                    ) : (
                      availableInsurances.map(insurance => (
                        <option key={insurance.id} value={insurance.id}>
                          {insurance.name}
                        </option>
                      ))
                    )}
                  </ModuleSelect>
                </ModuleFormGroup>
              )}

              <ModuleFormGroup
                moduleColor="people"
                label="Número da Carteirinha"
                htmlFor="policyNumber"
                icon={<CreditCard size={16} />}
              >
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  id="policyNumber"
                  name="policyNumber"
                  value={formData.policyNumber}
                  onChange={handleChange}
                />
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Validade"
                htmlFor="validUntil"
                icon={<Calendar size={16} />}
              >
                <div className="relative">
                  <ModuleInput
                    moduleColor="people"
                    type="date"
                    id="validUntil"
                    name="validUntil"
                    value={formData.validUntil}
                    onChange={handleChange}
                  />
                  <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-400 dark:text-neutral-500 pointer-events-none" size={18} />
                </div>
              </ModuleFormGroup>

              <ModuleFormGroup
                moduleColor="people"
                label="Observações"
                htmlFor="notes"
                icon={<FileText size={16} />}
              >
                <ModuleTextarea
                  moduleColor="people"
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                />
              </ModuleFormGroup>

            </>
          )}
        </form>
    </ModuleModal>
  );
};

export default PersonInsuranceFormModal;