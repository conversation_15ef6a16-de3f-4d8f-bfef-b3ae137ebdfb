// tests/controllers/auth-controller-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Dados para testes
const testUser = {
  login: `test_auth_${Date.now()}`,
  email: `test_auth_${Date.now()}@example.com`,
  fullName: 'Test Auth User',
  password: 'Test@123',
  role: 'EMPLOYEE',
  modules: ['BASIC']
};

// Variáveis para armazenar dados durante os testes
let authToken;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do controlador de autenticação...');
  
  try {
    // Teste 1: Registrar um novo usuário
    console.log('\n1. Registrando novo usuário...');
    
    try {
      const registerResponse = await api.post('/auth/register', testUser);
      
      if (registerResponse.status === 201) {
        console.log('✅ Usuário registrado com sucesso!');
        console.log(`ID: ${registerResponse.data.user.id}`);
        console.log(`Login: ${registerResponse.data.user.login}`);
        console.log(`Token: ${registerResponse.data.token.substring(0, 20)}...`);
        
        // Salvar o token para os próximos testes
        authToken = registerResponse.data.token;
      } else {
        console.log('❌ Falha ao registrar usuário');
      }
    } catch (error) {
      console.log('❌ Falha ao registrar usuário:', error.response?.data?.message || error.message);
      
      // Se o usuário já existe, tentar fazer login
      if (error.response?.data?.message?.includes('já existe')) {
        console.log('⚠️ Usuário já existe, tentando fazer login...');
      }
    }
    
    // Teste 2: Login
    console.log('\n2. Fazendo login...');
    
    const loginData = {
      login: testUser.login,
      password: testUser.password
    };
    
    try {
      const loginResponse = await api.post('/auth/login', loginData);
      
      if (loginResponse.status === 200) {
        console.log('✅ Login realizado com sucesso!');
        console.log(`Token: ${loginResponse.data.token.substring(0, 20)}...`);
        
        // Salvar o token para os próximos testes
        authToken = loginResponse.data.token;
      } else {
        console.log('❌ Falha ao fazer login');
      }
    } catch (error) {
      console.log('❌ Falha ao fazer login:', error.response?.data?.message || error.message);
    }
    
    // Se não conseguimos obter um token, não podemos continuar com os testes
    if (!authToken) {
      console.log('❌ Não foi possível obter um token de autenticação, encerrando testes');
      return;
    }
    
    // Configurar cliente com o token obtido
    const authApi = axios.create({
      baseURL: API_URL,
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    // Teste 3: Verificar perfil do usuário
    console.log('\n3. Verificando perfil do usuário...');
    
    try {
      const profileResponse = await authApi.get('/auth/profile');
      
      if (profileResponse.status === 200) {
        console.log('✅ Perfil obtido com sucesso!');
        console.log(`Nome: ${profileResponse.data.fullName}`);
        console.log(`Email: ${profileResponse.data.email}`);
        console.log(`Módulos: ${profileResponse.data.modules.join(', ')}`);
      } else {
        console.log('❌ Falha ao obter perfil');
      }
    } catch (error) {
      console.log('❌ Falha ao obter perfil:', error.response?.data?.message || error.message);
    }
    
    // Teste 4: Alterar senha
    console.log('\n4. Alterando senha...');
    
    const changePasswordData = {
      currentPassword: testUser.password,
      newPassword: 'NewTest@123'
    };
    
    try {
      const changePasswordResponse = await authApi.post('/auth/change-password', changePasswordData);
      
      if (changePasswordResponse.status === 200) {
        console.log('✅ Senha alterada com sucesso!');
      } else {
        console.log('❌ Falha ao alterar senha');
      }
    } catch (error) {
      console.log('❌ Falha ao alterar senha:', error.response?.data?.message || error.message);
    }
    
    // Teste 5: Logout
    console.log('\n5. Fazendo logout...');
    
    try {
      const logoutResponse = await authApi.post('/auth/logout');
      
      if (logoutResponse.status === 200) {
        console.log('✅ Logout realizado com sucesso!');
      } else {
        console.log('❌ Falha ao fazer logout');
      }
    } catch (error) {
      console.log('❌ Falha ao fazer logout:', error.response?.data?.message || error.message);
    }
    
    // Teste 6: Verificar se o token foi revogado
    console.log('\n6. Verificando se o token foi revogado...');
    
    try {
      await authApi.get('/auth/profile');
      console.log('❌ Token ainda é válido (deveria ter sido revogado)');
    } catch (error) {
      if (error.response && error.response.status === 401) {
        console.log('✅ Token foi revogado corretamente');
      } else {
        console.log('❌ Erro inesperado ao verificar token:', error.message);
      }
    }
    
    console.log('\n✅ Testes do controlador de autenticação concluídos!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
