"use client";

import React from 'react';
import ExportMenu from '@/components/ui/ExportMenu';

const CalendarExportButton = ({ onExport, isExporting, disabled, inHeader = false }) => {
  if (inHeader) {
    return (
      <div className="export-button">
        <ExportMenu
          onExport={onExport}
          isExporting={isExporting}
          disabled={disabled}
          className="text-white"
        />
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-soft dark:shadow-lg dark:shadow-black/20 border border-module-scheduler-border dark:border-module-scheduler-border-dark p-3 flex justify-end">
      <div className="export-button">
        <ExportMenu
          onExport={onExport}
          isExporting={isExporting}
          disabled={disabled}
          className="text-module-scheduler-text dark:text-module-scheduler-text-dark"
        />
      </div>
    </div>
  );
};

export default CalendarExportButton;