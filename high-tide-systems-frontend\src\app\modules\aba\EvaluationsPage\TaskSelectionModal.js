"use client";

import React, { useState, useEffect } from "react";
import { CheckSquare, Search, Check, X } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleTable, ModuleSelect } from "@/components/ui";
import { evaluationsService } from "@/app/modules/aba/services/evaluationsService";
import { useToast } from "@/contexts/ToastContext";

const TaskSelectionModal = ({ isOpen, onClose, onSelect, currentTasks = [], skills = [], levels = [] }) => {
  const { toast_error } = useToast();
  const [tasks, setTasks] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTasks, setSelectedTasks] = useState([]);
  const [filters, setFilters] = useState({
    search: "",
    skillId: "",
    levelId: "",
    page: 1,
    limit: 10
  });
  const [totalItems, setTotalItems] = useState(0);

  // Carregar tarefas disponíveis
  useEffect(() => {
    const loadTasks = async () => {
      try {
        setIsLoading(true);
        console.log("TaskSelectionModal - Carregando tarefas com filtros:", filters);
        const response = await evaluationsService.getAllTasks(filters);
        console.log("TaskSelectionModal - Resposta do serviço:", response);

        // Filtrar tarefas que já estão na avaliação
        const currentTaskIds = currentTasks.map(task => task.id);
        console.log("TaskSelectionModal - IDs de tarefas atuais:", currentTaskIds);

        const filteredTasks = response.items.filter(task => !currentTaskIds.includes(task.id));
        console.log("TaskSelectionModal - Tarefas filtradas:", filteredTasks);

        setTasks(filteredTasks);
        setTotalItems(response.total - currentTaskIds.length);
      } catch (error) {
        console.error("Erro ao carregar tarefas:", error);
        toast_error("Não foi possível carregar as tarefas");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadTasks();
    }
  }, [isOpen, filters, currentTasks, toast_error]);

  // Manipuladores de eventos
  const handleSearch = (e) => {
    e.preventDefault();
    setFilters({ ...filters, page: 1 });
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
      page: 1
    });
  };

  const handlePageChange = (page) => {
    setFilters({ ...filters, page });
  };

  const handleSelectTask = (task) => {
    const isSelected = selectedTasks.some(t => t.id === task.id);

    if (isSelected) {
      setSelectedTasks(selectedTasks.filter(t => t.id !== task.id));
    } else {
      setSelectedTasks([...selectedTasks, task]);
    }
  };

  const handleConfirmSelection = () => {
    onSelect(selectedTasks);
  };

  // Função para obter o nome da habilidade a partir do ID
  const getSkillName = (skillId) => {
    if (!skillId) return "-";
    const skill = skills.find(s => s.id === skillId);
    return skill ? skill.description : "-";
  };

  // Função para obter o nome do nível a partir do ID
  const getLevelName = (levelId) => {
    if (!levelId) return "-";
    const level = levels.find(l => l.id === levelId);
    return level ? level.description : "-";
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title="Selecionar Tarefas/Testes"
      size="lg"
      moduleColor="abaplus"
      icon={<CheckSquare size={20} />}
    >
      <div className="p-6 space-y-6">
        {/* Barra de pesquisa */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <ModuleInput
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Pesquisar por nome ou descrição..."
                icon={<Search size={18} />}
                moduleColor="abaplus"
                className="w-full"
              />
            </div>
            <div className="w-full md:w-1/4">
              <ModuleSelect
                name="skillId"
                value={filters.skillId}
                onChange={handleFilterChange}
                moduleColor="abaplus"
                placeholder="Filtrar por habilidade"
              >
                <option value="">Todas as habilidades</option>
                {skills.map(skill => (
                  <option key={skill.id} value={skill.id}>
                    {skill.description}
                  </option>
                ))}
              </ModuleSelect>
            </div>
            <div className="w-full md:w-1/4">
              <ModuleSelect
                name="levelId"
                value={filters.levelId}
                onChange={handleFilterChange}
                moduleColor="abaplus"
                placeholder="Filtrar por nível"
              >
                <option value="">Todos os níveis</option>
                {levels.map(level => (
                  <option key={level.id} value={level.id}>
                    {level.description}
                  </option>
                ))}
              </ModuleSelect>
            </div>
            <button
              type="submit"
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center justify-center gap-2"
            >
              <Search size={18} />
              Pesquisar
            </button>
          </form>
        </div>

        {/* Tabela de Tarefas */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <ModuleTable
            columns={[
              { key: "select", field: "select", header: "Selecionar" },
              { key: "order", field: "order", header: "Ordem" },
              { key: "name", field: "name", header: "Nome" },
              { key: "skill", field: "skill", header: "Habilidade" },
              { key: "level", field: "level", header: "Nível" }
            ]}
            data={tasks}
            isLoading={isLoading}
            pagination={{
              currentPage: filters.page,
              totalItems,
              itemsPerPage: filters.limit,
              onPageChange: handlePageChange
            }}
            emptyMessage="Nenhuma tarefa encontrada"
            emptyIcon={<CheckSquare size={24} />}
            tableId="tasks-selection-table"
            defaultSortField="order"
            defaultSortDirection="asc"
            renderRow={(task, _index, moduleColors, visibleColumns) => (
              <tr
                key={task.id}
                className={`${moduleColors.hoverBg} ${
                  selectedTasks.some(t => t.id === task.id) ? "bg-teal-50 dark:bg-teal-900/20" : ""
                }`}
                onClick={() => handleSelectTask(task)}
              >
                {visibleColumns.includes("select") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center justify-center">
                      <div
                        className={`w-6 h-6 rounded-md flex items-center justify-center cursor-pointer ${
                          selectedTasks.some(t => t.id === task.id)
                            ? "bg-teal-500 text-white"
                            : "border border-gray-300 dark:border-gray-600"
                        }`}
                      >
                        {selectedTasks.some(t => t.id === task.id) && <Check size={16} />}
                      </div>
                    </div>
                  </td>
                )}
                {visibleColumns.includes("order") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">{task.order}</div>
                  </td>
                )}
                {visibleColumns.includes("name") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">{task.name}</div>
                  </td>
                )}
                {visibleColumns.includes("skill") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {getSkillName(task.skillId)}
                    </div>
                  </td>
                )}
                {visibleColumns.includes("level") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {getLevelName(task.levelId)}
                    </div>
                  </td>
                )}
              </tr>
            )}
            moduleColor="abaplus"
          />
        </div>

        {/* Resumo da seleção */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {selectedTasks.length} tarefa(s) selecionada(s)
              </span>
            </div>
            <div className="flex gap-2">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-2"
              >
                <X size={18} />
                Cancelar
              </button>
              <button
                onClick={handleConfirmSelection}
                className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
                disabled={selectedTasks.length === 0}
              >
                <Check size={18} />
                Confirmar Seleção
              </button>
            </div>
          </div>
        </div>
      </div>
    </ModuleModal>
  );
};

export default TaskSelectionModal;
