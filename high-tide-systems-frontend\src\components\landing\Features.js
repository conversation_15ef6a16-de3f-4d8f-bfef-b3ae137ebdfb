'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  Calendar,
  Users,
  Clock,
  Bell,
  CreditCard,
  BarChart4,
  MessageSquare,
  Shield,
  Smartphone,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

const features = [
  {
    id: 'scheduling',
    icon: <Calendar className="h-6 w-6" />,
    title: 'Agendamento Inteligente',
    description: 'Gerencie consultas e compromissos com facilidade. Visualização em calendário diário, semanal e mensal com detecção automática de conflitos.',
    color: 'purple',
    benefits: [
      'Redução de 70% no tempo gasto com agendamentos',
      'Eliminação de conflitos de horários',
      'Visualização clara da agenda de cada profissional',
      'Agendamentos recorrentes com um clique',
      'Confirmação automática por e-mail e SMS'
    ],
    image: 'landing/features/scheduling.png'
  },
  {
    id: 'patients',
    icon: <Users className="h-6 w-6" />,
    title: 'Gestão de Pacientes',
    description: 'Cadastro completo de pacientes com histórico médico, documentos, convênios e relacionamentos familiares.',
    color: 'amber',
    benefits: [
      'Prontuário eletrônico completo e seguro',
      'Histórico de atendimentos e evolução',
      'Gestão de documentos digitalizados',
      'Controle de convênios e planos de saúde',
      'Relacionamentos familiares e contatos de emergência'
    ],
    image: '/features/patients.png'
  },
  {
    id: 'working-hours',
    icon: <Clock className="h-6 w-6" />,
    title: 'Horários de Trabalho',
    description: 'Configure a disponibilidade de cada profissional, incluindo intervalos, folgas e feriados para otimizar o agendamento.',
    color: 'blue',
    benefits: [
      'Configuração flexível de horários por profissional',
      'Definição de intervalos e pausas',
      'Calendário de folgas e feriados',
      'Bloqueio de horários para reuniões e eventos',
      'Visualização da ocupação em tempo real'
    ],
    image: '/features/working-hours.png'
  },
  {
    id: 'notifications',
    icon: <Bell className="h-6 w-6" />,
    title: 'Notificações Automáticas',
    description: 'Lembretes de consultas por e-mail e sistema para reduzir faltas e aumentar a satisfação dos pacientes.',
    color: 'red',
    benefits: [
      'Redução de até 40% nas faltas de pacientes',
      'Lembretes personalizáveis por tipo de atendimento',
      'Confirmação de presença com um clique',
      'Notificações para a equipe sobre alterações',
      'Alertas de aniversários e datas importantes'
    ],
    image: '/features/notifications.png'
  },
  {
    id: 'financial',
    icon: <CreditCard className="h-6 w-6" />,
    title: 'Gestão Financeira',
    description: 'Controle de pagamentos, faturamento de convênios, relatórios financeiros e integração com sistemas contábeis.',
    color: 'emerald',
    benefits: [
      'Controle completo de recebimentos e pagamentos',
      'Faturamento automático para convênios',
      'Relatórios financeiros detalhados',
      'Controle de comissões de profissionais',
      'Integração com sistemas contábeis'
    ],
    image: '/features/financial.png'
  },
  {
    id: 'reports',
    icon: <BarChart4 className="h-6 w-6" />,
    title: 'Relatórios e Dashboards',
    description: 'Análises detalhadas de desempenho, ocupação, faturamento e outros indicadores importantes para sua clínica.',
    color: 'indigo',
    benefits: [
      'Dashboard interativo com KPIs em tempo real',
      'Relatórios personalizáveis por período',
      'Análise de ocupação e produtividade',
      'Indicadores financeiros e de desempenho',
      'Exportação em diversos formatos (PDF, Excel, CSV)'
    ],
    image: '/features/reports.png'
  },
  {
    id: 'chat',
    icon: <MessageSquare className="h-6 w-6" />,
    title: 'Chat Integrado',
    description: 'Comunicação interna entre profissionais e equipe administrativa para agilizar processos e melhorar a colaboração.',
    color: 'orange',
    benefits: [
      'Comunicação em tempo real entre a equipe',
      'Grupos por departamento ou função',
      'Compartilhamento de arquivos e imagens',
      'Histórico completo de conversas',
      'Notificações de mensagens importantes'
    ],
    image: '/features/chat.png'
  },
  {
    id: 'security',
    icon: <Shield className="h-6 w-6" />,
    title: 'Segurança e Privacidade',
    description: 'Controle de acesso por perfil, registro de atividades e conformidade com LGPD para proteger dados sensíveis.',
    color: 'rose',
    benefits: [
      'Conformidade total com a LGPD',
      'Controle de acesso granular por perfil',
      'Registro detalhado de todas as atividades',
      'Criptografia de dados sensíveis',
      'Backups automáticos e redundantes'
    ],
    image: '/features/security.png'
  },
  {
    id: 'patient-portal',
    icon: <Smartphone className="h-6 w-6" />,
    title: 'Acesso para Pacientes',
    description: 'Portal do paciente para visualização de agendamentos, histórico e atualização de dados pessoais.',
    color: 'cyan',
    benefits: [
      'Agendamento online de consultas',
      'Visualização de histórico de atendimentos',
      'Acesso a resultados de exames',
      'Atualização de dados cadastrais',
      'Comunicação direta com a clínica'
    ],
    image: '/features/patient-portal.png'
  }
];

const Features = () => {
  const [activeFeature, setActiveFeature] = useState(null);

  const getColorClasses = (color, isActive) => {
    const colorMap = {
      purple: {
        bg: isActive ? 'bg-purple-500' : 'bg-purple-100 dark:bg-purple-900/30',
        text: isActive ? 'text-white' : 'text-purple-500 dark:text-purple-400',
        border: 'border-purple-200 dark:border-purple-800',
        hover: 'hover:bg-purple-50 dark:hover:bg-purple-900/40',
        shadow: 'shadow-purple-100 dark:shadow-purple-900/20'
      },
      amber: {
        bg: isActive ? 'bg-amber-500' : 'bg-amber-100 dark:bg-amber-900/30',
        text: isActive ? 'text-white' : 'text-amber-500 dark:text-amber-400',
        border: 'border-amber-200 dark:border-amber-800',
        hover: 'hover:bg-amber-50 dark:hover:bg-amber-900/40',
        shadow: 'shadow-amber-100 dark:shadow-amber-900/20'
      },
      blue: {
        bg: isActive ? 'bg-blue-500' : 'bg-blue-100 dark:bg-blue-900/30',
        text: isActive ? 'text-white' : 'text-blue-500 dark:text-blue-400',
        border: 'border-blue-200 dark:border-blue-800',
        hover: 'hover:bg-blue-50 dark:hover:bg-blue-900/40',
        shadow: 'shadow-blue-100 dark:shadow-blue-900/20'
      },
      red: {
        bg: isActive ? 'bg-red-500' : 'bg-red-100 dark:bg-red-900/30',
        text: isActive ? 'text-white' : 'text-red-500 dark:text-red-400',
        border: 'border-red-200 dark:border-red-800',
        hover: 'hover:bg-red-50 dark:hover:bg-red-900/40',
        shadow: 'shadow-red-100 dark:shadow-red-900/20'
      },
      emerald: {
        bg: isActive ? 'bg-emerald-500' : 'bg-emerald-100 dark:bg-emerald-900/30',
        text: isActive ? 'text-white' : 'text-emerald-500 dark:text-emerald-400',
        border: 'border-emerald-200 dark:border-emerald-800',
        hover: 'hover:bg-emerald-50 dark:hover:bg-emerald-900/40',
        shadow: 'shadow-emerald-100 dark:shadow-emerald-900/20'
      },
      indigo: {
        bg: isActive ? 'bg-indigo-500' : 'bg-indigo-100 dark:bg-indigo-900/30',
        text: isActive ? 'text-white' : 'text-indigo-500 dark:text-indigo-400',
        border: 'border-indigo-200 dark:border-indigo-800',
        hover: 'hover:bg-indigo-50 dark:hover:bg-indigo-900/40',
        shadow: 'shadow-indigo-100 dark:shadow-indigo-900/20'
      },
      orange: {
        bg: isActive ? 'bg-orange-500' : 'bg-orange-100 dark:bg-orange-900/30',
        text: isActive ? 'text-white' : 'text-orange-500 dark:text-orange-400',
        border: 'border-orange-200 dark:border-orange-800',
        hover: 'hover:bg-orange-50 dark:hover:bg-orange-900/40',
        shadow: 'shadow-orange-100 dark:shadow-orange-900/20'
      },
      rose: {
        bg: isActive ? 'bg-rose-500' : 'bg-rose-100 dark:bg-rose-900/30',
        text: isActive ? 'text-white' : 'text-rose-500 dark:text-rose-400',
        border: 'border-rose-200 dark:border-rose-800',
        hover: 'hover:bg-rose-50 dark:hover:bg-rose-900/40',
        shadow: 'shadow-rose-100 dark:shadow-rose-900/20'
      },
      cyan: {
        bg: isActive ? 'bg-cyan-500' : 'bg-cyan-100 dark:bg-cyan-900/30',
        text: isActive ? 'text-white' : 'text-cyan-500 dark:text-cyan-400',
        border: 'border-cyan-200 dark:border-cyan-800',
        hover: 'hover:bg-cyan-50 dark:hover:bg-cyan-900/40',
        shadow: 'shadow-cyan-100 dark:shadow-cyan-900/20'
      }
    };

    return colorMap[color] || colorMap.purple;
  };

  return (
    <section id="features" className="py-20 bg-white dark:bg-gray-900">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Recursos completos para sua clínica
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Tudo o que você precisa para gerenciar sua clínica ou consultório em uma única plataforma intuitiva e poderosa.
          </p>
        </motion.div>

        {/* Feature Detail View */}
        {activeFeature && (
          <motion.div
            className="mb-16 bg-white dark:bg-gray-800 rounded-2xl shadow-xl overflow-hidden border border-gray-100 dark:border-gray-700"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="grid grid-cols-1 lg:grid-cols-2">
              {/* Feature Image */}
              <div className="bg-gray-50 dark:bg-gray-900 p-8 flex items-center justify-center">
                <div className="relative w-full h-64 md:h-80 lg:h-full rounded-xl overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                  {activeFeature.image ? (
                    <img
                      src={activeFeature.image}
                      alt={activeFeature.title}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.style.display = 'none';
                        e.target.parentElement.innerHTML = `
                          <div class="text-center p-8">
                            <div class="${getColorClasses(activeFeature.color, false).text} mx-auto mb-4">
                              ${activeFeature.icon.props.children}
                            </div>
                            <p class="text-gray-500 dark:text-gray-400">Visualização do recurso</p>
                          </div>
                        `;
                      }}
                    />
                  ) : (
                    <div className="text-center p-8">
                      <div className={`${getColorClasses(activeFeature.color, false).text} mx-auto mb-4`}>
                        {activeFeature.icon}
                      </div>
                      <p className="text-gray-500 dark:text-gray-400">Visualização do recurso</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Feature Details */}
              <div className="p-8">
                <div className="flex items-center mb-6">
                  <div className={`w-12 h-12 rounded-lg ${getColorClasses(activeFeature.color, true).bg} flex items-center justify-center mr-4`}>
                    <div className="text-white">
                      {activeFeature.icon}
                    </div>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">
                    {activeFeature.title}
                  </h3>
                </div>

                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  {activeFeature.description}
                </p>

                <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Principais benefícios:
                </h4>

                <ul className="space-y-3 mb-8">
                  {activeFeature.benefits.map((benefit, i) => (
                    <li key={i} className="flex items-start">
                      <CheckCircle className={`h-5 w-5 mt-0.5 mr-3 ${getColorClasses(activeFeature.color, false).text}`} />
                      <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                    </li>
                  ))}
                </ul>

                <button
                  onClick={() => setActiveFeature(null)}
                  className="text-primary-500 dark:text-primary-400 hover:underline flex items-center"
                >
                  <ArrowRight className="h-4 w-4 mr-1 rotate-180" />
                  Voltar para todos os recursos
                </button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Features Grid */}
        <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 ${activeFeature ? 'hidden lg:grid' : ''}`}>
          {features.map((feature, index) => {
            const colorClasses = getColorClasses(feature.color, false);

            return (
              <motion.div
                key={feature.id}
                className={`bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm hover:shadow-lg transition-all duration-300 border ${colorClasses.border} ${colorClasses.hover} cursor-pointer`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                onClick={() => setActiveFeature(feature)}
                whileHover={{ y: -5 }}
              >
                <div className={`w-12 h-12 ${colorClasses.bg} rounded-lg flex items-center justify-center mb-4`}>
                  <div className={colorClasses.text}>
                    {feature.icon}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-4">
                  {feature.description}
                </p>
                <div className="flex items-center text-sm font-medium text-primary-500 dark:text-primary-400">
                  Ver detalhes
                  <ArrowRight className="ml-1 h-4 w-4" />
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default Features;
