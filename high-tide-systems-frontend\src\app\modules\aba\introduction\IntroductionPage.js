"use client";

import React from "react";
import { Brain, Info, Activity, BookOpen, ClipboardList, LayoutDashboard } from "lucide-react";
import ModuleHeader from "@/components/ui/ModuleHeader";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";

const IntroductionPage = () => {
  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Brain size={24} className="mr-2 text-teal-600 dark:text-teal-400" />
          Introdução ao ABA+
        </h1>
        <TutorialTriggerButton tutorialName="abaplus-introduction" />
      </div>

      {/* Tutorial Manager */}
      <TutorialManager
        name="abaplus-introduction"
        steps={[
          {
            target: ".tutorial-intro",
            content: "Bem-vindo ao módulo ABA+, onde você pode gerenciar programas terapêuticos, acompanhamento de pacientes.",
            placement: "bottom"
          },
          {
            target: ".tutorial-skills",
            content: "Na seção de Habilidades, você pode cadastrar e gerenciar as habilidades e competências que serão trabalhadas nos programas.",
            placement: "bottom"
          },
          {
            target: ".tutorial-programs",
            content: "Na seção de Programas, você pode criar e gerenciar programas terapêuticos personalizados para cada paciente.",
            placement: "bottom"
          },
          {
            target: ".tutorial-evaluations",
            content: "Na seção de Avaliações, você pode criar e aplicar protocolos de avaliação para acompanhar o progresso dos pacientes.",
            placement: "bottom"
          },
          {
            target: ".tutorial-dashboard",
            content: "No Dashboard, você encontra análises e estatísticas sobre o progresso dos pacientes e a eficácia dos programas.",
            placement: "bottom"
          }
        ]}
      />

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-abaplus-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-teal-600 to-teal-400 dark:from-teal-700 dark:to-teal-600 px-6 py-4">
          <div className="flex items-center">
            <Brain className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo ABA+</h2>
          </div>
        </div>

        {/* Introduction text and video */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8 tutorial-intro">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo ABA+ do High Tide Systems. Este módulo é dedicado à Análise do Comportamento Aplicada,
                permitindo o gerenciamento completo de programas terapêuticos, avaliações e acompanhamento de pacientes.
                Aqui você encontrará todas as ferramentas necessárias para implementar e monitorar intervenções comportamentais eficazes.
              </p>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                O ABA+ foi desenvolvido para profissionais que trabalham com intervenções comportamentais, especialmente
                para o tratamento de Transtorno do Espectro Autista (TEA) e outros transtornos do desenvolvimento.
                O módulo permite o registro detalhado de habilidades, criação de programas personalizados, aplicação de
                avaliações padronizadas e acompanhamento do progresso terapêutico.
              </p>
            </div>
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 flex items-center justify-center">
              <div className="text-center">
                <Brain size={64} className="mx-auto mb-4 text-teal-500 dark:text-teal-400" />
                <p className="text-gray-600 dark:text-gray-300">
                  Vídeo de demonstração do módulo ABA+ em breve
                </p>
              </div>
            </div>
          </div>

          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-teal-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Skills section */}
            <div className="bg-teal-50 dark:bg-gray-700 rounded-lg border border-teal-200 dark:border-gray-600 overflow-hidden shadow-md tutorial-skills">
              <div className="bg-teal-100 dark:bg-gray-600 px-4 py-3 border-b border-teal-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Activity className="mr-2 text-teal-600 dark:text-teal-300" size={20} />
                  <h3 className="font-semibold text-teal-800 dark:text-white">Habilidades</h3>
                </div>
              </div>
              <div className="p-4">
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Cadastre e gerencie habilidades e competências que serão trabalhadas nos programas terapêuticos.
                  Organize-as por categorias e níveis de desenvolvimento.
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Cadastro de habilidades por área de desenvolvimento
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Organização por níveis de complexidade
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Vinculação com programas e avaliações
                  </li>
                </ul>
              </div>
            </div>

            {/* Programs section */}
            <div className="bg-teal-50 dark:bg-gray-700 rounded-lg border border-teal-200 dark:border-gray-600 overflow-hidden shadow-md tutorial-programs">
              <div className="bg-teal-100 dark:bg-gray-600 px-4 py-3 border-b border-teal-200 dark:border-gray-500">
                <div className="flex items-center">
                  <BookOpen className="mr-2 text-teal-600 dark:text-teal-300" size={20} />
                  <h3 className="font-semibold text-teal-800 dark:text-white">Programas</h3>
                </div>
              </div>
              <div className="p-4">
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Crie e gerencie programas terapêuticos personalizados para cada paciente, definindo objetivos,
                  procedimentos de ensino e critérios de aprendizagem.
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Criação de programas individualizados
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Definição de procedimentos de ensino
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Acompanhamento de progresso
                  </li>
                </ul>
              </div>
            </div>

            {/* Evaluations section */}
            <div className="bg-teal-50 dark:bg-gray-700 rounded-lg border border-teal-200 dark:border-gray-600 overflow-hidden shadow-md tutorial-evaluations">
              <div className="bg-teal-100 dark:bg-gray-600 px-4 py-3 border-b border-teal-200 dark:border-gray-500">
                <div className="flex items-center">
                  <ClipboardList className="mr-2 text-teal-600 dark:text-teal-300" size={20} />
                  <h3 className="font-semibold text-teal-800 dark:text-white">Avaliações</h3>
                </div>
              </div>
              <div className="p-4">
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Aplique protocolos de avaliação padronizados para identificar habilidades e déficits,
                  permitindo o planejamento de intervenções baseadas em evidências.
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Protocolos de avaliação padronizados
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Registro de resultados e evolução
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Comparação entre avaliações
                  </li>
                </ul>
              </div>
            </div>

            {/* Dashboard section */}
            <div className="bg-teal-50 dark:bg-gray-700 rounded-lg border border-teal-200 dark:border-gray-600 overflow-hidden shadow-md tutorial-dashboard">
              <div className="bg-teal-100 dark:bg-gray-600 px-4 py-3 border-b border-teal-200 dark:border-gray-500">
                <div className="flex items-center">
                  <LayoutDashboard className="mr-2 text-teal-600 dark:text-teal-300" size={20} />
                  <h3 className="font-semibold text-teal-800 dark:text-white">Dashboard</h3>
                </div>
              </div>
              <div className="p-4">
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  Visualize análises e estatísticas sobre o progresso dos pacientes, eficácia dos programas
                  e desempenho geral das intervenções.
                </p>
                <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Gráficos de progresso individual
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Estatísticas de eficácia de programas
                  </li>
                  <li className="flex items-center">
                    <span className="w-2 h-2 bg-teal-500 rounded-full mr-2"></span>
                    Relatórios personalizados
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Getting Started */}
          <div className="mt-8 bg-teal-50 dark:bg-gray-700 rounded-lg p-6 border border-teal-200 dark:border-gray-600">
            <h3 className="text-xl font-bold text-teal-800 dark:text-white mb-4">Começando com o ABA+</h3>
            <ol className="space-y-4 text-gray-700 dark:text-gray-300 list-decimal pl-5">
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 mr-3 flex-shrink-0">1</span>
                <span>Comece <strong>cadastrando as habilidades</strong> que serão trabalhadas nos programas terapêuticos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 mr-3 flex-shrink-0">2</span>
                <span>Crie <strong>programas personalizados</strong> para cada paciente, definindo objetivos e procedimentos.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 mr-3 flex-shrink-0">3</span>
                <span>Aplique <strong>avaliações</strong> para identificar habilidades e déficits.</span>
              </li>
              <li className="flex items-start">
                <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-teal-100 dark:bg-teal-900 text-teal-600 dark:text-teal-300 mr-3 flex-shrink-0">4</span>
                <span>Acompanhe o <strong>progresso</strong> através do dashboard e relatórios.</span>
              </li>
            </ol>
            <div className="mt-6 flex justify-center">
              <button
                onClick={() => window.location.href = '/dashboard/abaplus/skills'}
                className="px-5 py-2.5 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
              >
                <Activity size={18} />
                Ir para Habilidades
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
