import React from "react";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import {
  Calendar,
  Clock,
  User,
  Users,
  MapPin,
  Briefcase,
  RefreshCw
} from "lucide-react";
import { ModuleTable } from "@/components/ui";

// Componente para badge de status
const StatusBadge = ({ status }) => {
  const statusMap = {
    "PENDING": {
      text: "Pendente",
      className: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
    },
    "CONFIRMED": {
      text: "Confirmado",
      className: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
    },
    "CANCELLED": {
      text: "Cancelado",
      className: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
    },
    "COMPLETED": {
      text: "Concluí<PERSON>",
      className: "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200"
    },
    "NO_SHOW": {
      text: "Não Compareceu",
      className: "bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200"
    }
  };

  const config = statusMap[status] || {
    text: status,
    className: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200"
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.className}`}>
      {config.text}
    </span>
  );
};

const AppointmentTable = ({
  appointments,
  renderActions,
  isLoading,
  currentPage,
  totalPages,
  setCurrentPage,
  onRefresh
}) => {
  // Não precisamos de funções de navegação personalizadas
  // pois o ModuleTable já lida com a paginação internamente

  return (
    <ModuleTable
      moduleColor="scheduler"
      columns={[
        { header: 'Título', field: 'title', width: '15%' },
        {
          header: 'Data/Hora',
          field: 'startDate',
          width: '15%',
          dataType: 'date',
          // Função de acesso personalizada para garantir que a ordenação use o objeto Date
          accessor: (appointment) => new Date(appointment.startDate)
        },
        {
          header: 'Paciente',
          field: 'personfullName',
          width: '15%',
          // Função de acesso personalizada para ordenação de strings
          accessor: (appointment) => appointment.personfullName || ''
        },
        {
          header: 'Profissional',
          field: 'providerfullName',
          width: '15%',
          // Função de acesso personalizada para ordenação de strings
          accessor: (appointment) => appointment.providerfullName || ''
        },
        {
          header: 'Local',
          field: 'locationName',
          width: '10%',
          // Função de acesso personalizada para ordenação de strings
          accessor: (appointment) => appointment.locationName || ''
        },
        {
          header: 'Tipo de Serviço',
          field: 'serviceTypefullName',
          width: '15%',
          // Função de acesso personalizada para ordenação de strings
          accessor: (appointment) => appointment.serviceTypefullName || ''
        },
        { header: 'Status', field: 'status', width: '10%' },
        { header: 'Ações', field: 'actions', className: 'text-right', width: '5%', sortable: false }
      ]}
      data={appointments}
      isLoading={isLoading}
      emptyMessage="Nenhum agendamento encontrado"
      emptyIcon={<Calendar size={24} />}
      currentPage={currentPage}
      totalPages={totalPages}
      onPageChange={setCurrentPage}
      showPagination={true}
      title="Lista de Agendamentos"
      headerContent={
        <button
          onClick={onRefresh}
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          disabled={isLoading}
          title="Atualizar lista"
        >
          <RefreshCw
            size={18}
            className={`text-gray-600 dark:text-gray-300 ${isLoading ? 'animate-spin' : ''}`}
          />
        </button>
      }
      tableId="scheduler-appointments-table"
      enableColumnToggle={true}
      defaultSortField="startDate"
      defaultSortDirection="desc"
      renderRow={(appointment, _index, moduleColors, visibleColumns) => (
        <tr key={appointment.id} className={moduleColors.hoverBg}>
          {visibleColumns.includes('title') && (
            <td className="px-4 py-3 whitespace-nowrap">
              <div className="flex items-center">
                <Calendar className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="font-medium text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.title}>
                  {appointment.title}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('startDate') && (
            <td className="px-4 py-3 whitespace-nowrap">
              <div className="text-sm text-gray-900 dark:text-white">
                {dateFormat(new Date(appointment.startDate), "dd/MM/yyyy", { locale: ptBR })}
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center mt-1">
                <Clock className="h-3 w-3 mr-1" />
                {dateFormat(new Date(appointment.startDate), "HH:mm", { locale: ptBR })} -
                {dateFormat(new Date(appointment.endDate), "HH:mm", { locale: ptBR })}
              </div>
            </td>
          )}

          {visibleColumns.includes('personfullName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <User className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.personfullName}>
                  {appointment.personfullName}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('providerfullName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <Users className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.providerfullName}>
                  {appointment.providerfullName}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('locationName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <MapPin className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]">
                  {appointment.locationName || "Sem localização"}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('serviceTypefullName') && (
            <td className="px-4 py-3">
              <div className="flex items-center">
                <Briefcase className="flex-shrink-0 h-4 w-4 text-gray-500 dark:text-gray-400 mr-2" />
                <div className="text-sm text-gray-900 dark:text-white truncate max-w-[150px]" title={appointment.serviceTypefullName}>
                  {appointment.serviceTypefullName}
                </div>
              </div>
            </td>
          )}

          {visibleColumns.includes('status') && (
            <td className="px-4 py-3">
              <StatusBadge status={appointment.status} />
            </td>
          )}

          {visibleColumns.includes('actions') && (
            <td className="px-4 py-3 text-right">
              {renderActions(appointment)}
            </td>
          )}
        </tr>
      )}
    />
  );
};

export default AppointmentTable;