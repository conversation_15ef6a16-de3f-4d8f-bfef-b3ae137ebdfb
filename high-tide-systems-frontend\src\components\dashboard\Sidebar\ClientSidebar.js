// src/components/dashboard/Sidebar/ClientSidebar.js
'use client';

import React from 'react';
import { ChevronLeft, Users, Calendar, User, Clock, FileText } from 'lucide-react';
import CustomScrollArea from '@/components/ui/CustomScrollArea';

// Client-specific submenu configuration
const clientModuleSubmenus = {
  people: [
    { id: 'persons', title: '<PERSON><PERSON><PERSON><PERSON>', icon: Users, description: 'Gerenciar pessoas relacionadas' },
  ],
  scheduler: [
    { id: 'calendar', title: 'Calendário', icon: Calendar, description: 'Visualizar agenda' },
    { id: 'appointments', title: 'Meus Agendamentos', icon: Clock, description: 'Visualizar meus agendamentos' },
  ],
  profile: [
    { id: 'profile', title: 'Meu Perfil', icon: User, description: 'Gerenciar meu perfil e dados pessoais' },
  ]
};

const ModuleTitle = ({ moduleId, title, icon: Icon }) => {
  return (
    <div className="mb-6">
      {/* Card com gradiente e borda temática */}
      <div className={`
        relative overflow-hidden rounded-xl p-4 mb-4
        bg-gradient-to-r from-white dark:from-gray-800 to-module-${moduleId}-bg/30 dark:to-module-${moduleId}-bg-dark/30
        border-l-4 border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark
        shadow-sm dark:shadow-md dark:shadow-black/20
      `}>
        <div className="flex items-center">
          {/* Ícone grande do módulo */}
          <div className={`
            w-12 h-12 rounded-lg mr-3 flex items-center justify-center
            bg-module-${moduleId}-bg/70 dark:bg-module-${moduleId}-bg-dark/70
            text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark
          `}>
            <Icon size={26} />
          </div>

          {/* Título e subtítulo */}
          <div>
            <h2 className="text-xl font-bold text-gray-800 dark:text-white">{title}</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">Área do Cliente</p>
          </div>
        </div>
      </div>
    </div>
  );
};

const ClientSidebar = ({
  activeModule,
  activeModuleTitle,
  isSubmenuActive,
  handleModuleSubmenuClick,
  handleBackToModules,
  isSidebarOpen
}) => {
  // Encontrar o objeto do módulo ativo para acessar seu ícone
  const ModuleIcon = activeModule === 'people' ? Users :
                     activeModule === 'scheduler' ? Calendar :
                     activeModule === 'profile' ? User : User;

  return (
    <aside
      className={`w-72 bg-white dark:bg-gray-800 border-r dark:border-gray-700 h-screen sticky top-0 transition-all duration-300 flex flex-col ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      }`}
      aria-label="Navegação lateral"
    >
      {/* Conteúdo principal da sidebar */}
      <CustomScrollArea className="flex-1 p-5" moduleColor={activeModule}>
        {/* Título do módulo estilizado */}
        {activeModule && (
          <ModuleTitle
            moduleId={activeModule}
            title={activeModuleTitle}
            icon={ModuleIcon}
          />
        )}

        <nav
          className="space-y-2"
          aria-labelledby="sidebar-heading"
        >
          {activeModule && clientModuleSubmenus[activeModule]?.map((submenu) => {
            const isActive = isSubmenuActive(activeModule, submenu.id);

            return (
              <button
                key={submenu.id}
                onClick={() => handleModuleSubmenuClick(activeModule, submenu.id)}
                className={`
                  group w-full flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-300
                  ${isActive
                    ? `rounded-xl border border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
                       bg-module-${activeModule}-bg dark:bg-gray-700 dark:bg-opacity-90
                       shadow-md dark:shadow-black/20`
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                `}
                aria-current={isActive ? 'page' : undefined}
              >
                {submenu.icon && (
                  <div className={`
                    ${isActive
                      ? `text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark`
                      : `text-gray-500 dark:text-gray-400 group-hover:text-module-${activeModule}-icon dark:group-hover:text-module-${activeModule}-icon-dark transition-colors duration-200`
                    }
                  `}>
                    <submenu.icon
                      size={20}
                      aria-hidden="true"
                    />
                  </div>
                )}
                <span className={`font-medium text-left ${isActive ? 'dark:text-white' : ''}`}>{submenu.title}</span>
              </button>
            );
          })}
        </nav>
      </CustomScrollArea>

      {/* Botão de voltar fixo na parte inferior */}
      <div className="p-5 border-t border-gray-100 dark:border-gray-700 mt-auto">
        <button
          onClick={handleBackToModules}
          className={`
            group w-full py-3 px-4 rounded-lg flex items-center gap-3
            border-2 border-module-${activeModule}-border dark:border-module-${activeModule}-border-dark
            bg-transparent dark:bg-gray-800 hover:bg-module-${activeModule}-bg/10 dark:hover:bg-module-${activeModule}-bg-dark/10
            transition-all duration-300
          `}
          aria-label="Voltar para o dashboard principal"
        >
          <div className={`
            flex items-center justify-center w-8 h-8 rounded-full
            bg-module-${activeModule}-bg dark:bg-module-${activeModule}-bg-dark/70
            text-module-${activeModule}-icon dark:text-module-${activeModule}-icon-dark
            shadow-sm dark:shadow-md dark:shadow-black/20
            group-hover:scale-110 transition-transform duration-200
          `}>
            <ChevronLeft size={20} aria-hidden="true" />
          </div>
          <span className="font-medium text-gray-800 dark:text-gray-200">Voltar a Tela Inicial</span>
        </button>
      </div>
    </aside>
  );
};

export default ClientSidebar;
