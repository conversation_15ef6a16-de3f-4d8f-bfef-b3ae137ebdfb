-- Cria<PERSON> <PERSON>Ms primeiro
CREATE TYPE "SystemModule" AS ENUM ('ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'BASIC');
CREATE TYPE "SchedulingStatus" AS ENUM ('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED', 'NO_SHOW');
CREATE TYPE "RecurrenceType" AS ENUM ('OCCURRENCES', 'END_DATE');
CREATE TYPE "UserRole" AS ENUM ('SYSTEM_ADMIN', 'COMPANY_ADMIN', 'EMPLOYEE');

-- <PERSON><PERSON><PERSON> tabel<PERSON> principais (sem chaves estrangeiras primeiro para evitar conflitos)
CREATE TABLE "Company" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "tradingName" TEXT,
    "legalName" TEXT,
    "industry" TEXT,
    "contactEmail" TEXT,
    "cnpj" TEXT NOT NULL,
    "phone" TEXT,
    "privacyPolicyUrl" TEXT,
    "termsOfServiceUrl" TEXT,
    "phone2" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "website" TEXT,
    "primaryColor" TEXT,
    "secondaryColor" TEXT,
    "description" TEXT,
    "socialMedia" JSONB,
    "businessHours" JSONB,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "defaultCurrency" TEXT NOT NULL DEFAULT 'BRL',
    "deletedAt" TIMESTAMP(3),
    "licenseValidUntil" TIMESTAMP(3),
    "plan" TEXT,
    "timeZone" TEXT NOT NULL DEFAULT 'America/Sao_Paulo',

    CONSTRAINT "Company_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "User" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "address" TEXT,
    "birthDate" TIMESTAMP(3),
    "cnpj" TEXT,
    "cpf" TEXT,
    "createdById" TEXT,
    "fullName" TEXT NOT NULL,
    "login" TEXT NOT NULL,
    "modules" "SystemModule"[],
    "permissions" TEXT[],
    "phone" TEXT,
    "companyId" TEXT,
    "deletedAt" TIMESTAMP(3),
    "deletedById" TEXT,
    "failedLoginAttempts" INTEGER NOT NULL DEFAULT 0,
    "lastLoginAt" TIMESTAMP(3),
    "lastLoginIp" TEXT,
    "passwordChangedAt" TIMESTAMP(3),
    "role" "UserRole" NOT NULL DEFAULT 'EMPLOYEE',
    "professionId" TEXT,
    "profileImageUrl" TEXT,
    "profession" TEXT NOT NULL DEFAULT 'Sem profissão',

    CONSTRAINT "User_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "ProfessionGroup" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "companyId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "ProfessionGroup_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Profession" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "groupId" TEXT,
    "companyId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),

    CONSTRAINT "Profession_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Client" (
    "id" TEXT NOT NULL,
    "login" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "createdById" TEXT NOT NULL,
    "companyId" TEXT,
    "deletedAt" TIMESTAMP(3),
    "deletedById" TEXT,

    CONSTRAINT "Client_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Person" (
    "id" TEXT NOT NULL,
    "email" TEXT,
    "phone" TEXT,
    "address" TEXT,
    "city" TEXT,
    "state" TEXT,
    "birthDate" TIMESTAMP(3),
    "notes" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "neighborhood" TEXT,
    "postalCode" TEXT,
    "clientId" TEXT,
    "cpf" TEXT,
    "createdById" TEXT NOT NULL,
    "deletedById" TEXT,
    "fullName" TEXT NOT NULL,
    "gender" TEXT,
    "profileImageUrl" TEXT,
    "relationship" TEXT,

    CONSTRAINT "Person_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Branch" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT,
    "description" TEXT,
    "address" TEXT NOT NULL,
    "city" TEXT,
    "state" TEXT,
    "postalCode" TEXT,
    "phone" TEXT,
    "email" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "isHeadquarters" BOOLEAN NOT NULL DEFAULT false,
    "deletedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "companyId" TEXT NOT NULL,
    "neighborhood" TEXT,

    CONSTRAINT "Branch_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Location" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "phone" TEXT,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "companyId" TEXT,
    "deletedAt" TIMESTAMP(3),
    "branchId" TEXT,

    CONSTRAINT "Location_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "ServiceType" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "value" DECIMAL(10,2) NOT NULL,
    "companyId" TEXT,

    CONSTRAINT "ServiceType_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Insurance" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "companyId" TEXT,

    CONSTRAINT "Insurance_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Document" (
    "id" TEXT NOT NULL,
    "filename" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "clientId" TEXT,
    "userId" TEXT,
    "companyId" TEXT,
    "createdById" TEXT,
    "externalUrl" TEXT,
    "mimeType" TEXT NOT NULL,
    "ownerType" TEXT NOT NULL,
    "size" INTEGER NOT NULL,
    "personId" TEXT,

    CONSTRAINT "Document_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Scheduling" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "creatorId" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "insuranceId" TEXT,
    "status" "SchedulingStatus" NOT NULL DEFAULT 'PENDING',
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "recurrenceId" TEXT,
    "companyId" TEXT,
    "confirmationSentAt" TIMESTAMP(3),
    "reminderSentAt" TIMESTAMP(3),
    "branchId" TEXT,

    CONSTRAINT "Scheduling_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Recurrence" (
    "id" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT,
    "clientId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "locationId" TEXT NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "insuranceId" TEXT,
    "recurrenceType" "RecurrenceType" NOT NULL,
    "numberOfOccurrences" INTEGER,
    "endDate" TIMESTAMP(3),
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "createdById" TEXT NOT NULL,
    "companyId" TEXT,
    "branchId" TEXT,
    "personId" TEXT NOT NULL,

    CONSTRAINT "Recurrence_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "RecurrencePattern" (
    "id" TEXT NOT NULL,
    "recurrenceId" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "endTimeMinutes" INTEGER NOT NULL,
    "startTimeMinutes" INTEGER NOT NULL,

    CONSTRAINT "RecurrencePattern_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "WorkingHours" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "dayOfWeek" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "breakEndMinutes" INTEGER,
    "breakStartMinutes" INTEGER,
    "endTimeMinutes" INTEGER NOT NULL,
    "startTimeMinutes" INTEGER NOT NULL,

    CONSTRAINT "WorkingHours_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "EmailConfig" (
    "id" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "smtpHost" TEXT NOT NULL,
    "smtpPort" INTEGER NOT NULL,
    "smtpSecure" BOOLEAN NOT NULL DEFAULT false,
    "smtpUser" TEXT NOT NULL,
    "smtpPassword" TEXT NOT NULL,
    "emailFromName" TEXT NOT NULL,
    "emailFromAddress" TEXT NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EmailConfig_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Subscription" (
    "id" TEXT NOT NULL,
    "companyId" TEXT NOT NULL,
    "plan" TEXT NOT NULL,
    "pricePerMonth" DECIMAL(10,2) NOT NULL,
    "billingCycle" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "autoRenew" BOOLEAN NOT NULL DEFAULT true,
    "status" TEXT NOT NULL,
    "lastBillingDate" TIMESTAMP(3),
    "nextBillingDate" TIMESTAMP(3),
    "maxUsers" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Subscription_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "AuditLog" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "action" TEXT NOT NULL,
    "entityType" TEXT NOT NULL,
    "entityId" TEXT NOT NULL,
    "details" JSONB,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "companyId" TEXT,

    CONSTRAINT "AuditLog_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "PasswordReset" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "token" TEXT NOT NULL,
    "expiresAt" TIMESTAMP(3) NOT NULL,
    "usedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PasswordReset_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "Contact" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "relationship" TEXT,
    "email" TEXT,
    "phone" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "personId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,
    "deletedById" TEXT,

    CONSTRAINT "Contact_pkey" PRIMARY KEY ("id")
);

CREATE TABLE "ClientInsurance" (
    "clientId" TEXT NOT NULL,
    "insuranceId" TEXT NOT NULL,
    "notes" TEXT,
    "policyNumber" TEXT,
    "validUntil" TIMESTAMP(3),

    CONSTRAINT "ClientInsurance_pkey" PRIMARY KEY ("clientId","insuranceId")
);

CREATE TABLE "PersonInsurance" (
    "personId" TEXT NOT NULL,
    "insuranceId" TEXT NOT NULL,
    "policyNumber" TEXT,
    "validUntil" TIMESTAMP(3),
    "notes" TEXT,

    CONSTRAINT "PersonInsurance_pkey" PRIMARY KEY ("personId","insuranceId")
);

CREATE TABLE "PersonInsuranceServiceLimit" (
    "id" TEXT NOT NULL,
    "personId" TEXT NOT NULL,
    "insuranceId" TEXT NOT NULL,
    "serviceTypeId" TEXT NOT NULL,
    "monthlyLimit" INTEGER NOT NULL,
    "yearlyLimit" INTEGER NOT NULL DEFAULT 0,
    "notes" TEXT,

    CONSTRAINT "PersonInsuranceServiceLimit_pkey" PRIMARY KEY ("id")
);

-- Criar tabela de relação muitos-para-muitos
CREATE TABLE "_PersonToScheduling" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,
    
    CONSTRAINT "_PersonToScheduling_AB_pkey" PRIMARY KEY ("A","B")
);

-- Adicionar chaves estrangeiras
ALTER TABLE "User" ADD CONSTRAINT "User_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "User" ADD CONSTRAINT "User_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "User" ADD CONSTRAINT "User_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "User" ADD CONSTRAINT "User_professionId_fkey" FOREIGN KEY ("professionId") REFERENCES "Profession"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "ProfessionGroup" ADD CONSTRAINT "ProfessionGroup_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Profession" ADD CONSTRAINT "Profession_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Profession" ADD CONSTRAINT "Profession_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "ProfessionGroup"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Client" ADD CONSTRAINT "Client_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Client" ADD CONSTRAINT "Client_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Client" ADD CONSTRAINT "Client_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Person" ADD CONSTRAINT "Person_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Person" ADD CONSTRAINT "Person_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Person" ADD CONSTRAINT "Person_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Branch" ADD CONSTRAINT "Branch_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "Location" ADD CONSTRAINT "Location_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Location" ADD CONSTRAINT "Location_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "ServiceType" ADD CONSTRAINT "ServiceType_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Insurance" ADD CONSTRAINT "Insurance_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Document" ADD CONSTRAINT "Document_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Document" ADD CONSTRAINT "Document_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Document" ADD CONSTRAINT "Document_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Document" ADD CONSTRAINT "Document_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Document" ADD CONSTRAINT "Document_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_creatorId_fkey" FOREIGN KEY ("creatorId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "ServiceType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_recurrenceId_fkey" FOREIGN KEY ("recurrenceId") REFERENCES "Recurrence"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Scheduling" ADD CONSTRAINT "Scheduling_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_locationId_fkey" FOREIGN KEY ("locationId") REFERENCES "Location"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "ServiceType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_branchId_fkey" FOREIGN KEY ("branchId") REFERENCES "Branch"("id") ON DELETE SET NULL ON UPDATE CASCADE;
ALTER TABLE "Recurrence" ADD CONSTRAINT "Recurrence_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "RecurrencePattern" ADD CONSTRAINT "RecurrencePattern_recurrenceId_fkey" FOREIGN KEY ("recurrenceId") REFERENCES "Recurrence"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "WorkingHours" ADD CONSTRAINT "WorkingHours_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "EmailConfig" ADD CONSTRAINT "EmailConfig_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "Subscription" ADD CONSTRAINT "Subscription_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "Contact" ADD CONSTRAINT "Contact_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Contact" ADD CONSTRAINT "Contact_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "Contact" ADD CONSTRAINT "Contact_deletedById_fkey" FOREIGN KEY ("deletedById") REFERENCES "User"("id") ON DELETE SET NULL ON UPDATE CASCADE;

ALTER TABLE "ClientInsurance" ADD CONSTRAINT "ClientInsurance_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "ClientInsurance" ADD CONSTRAINT "ClientInsurance_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "PersonInsurance" ADD CONSTRAINT "PersonInsurance_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "PersonInsurance" ADD CONSTRAINT "PersonInsurance_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "PersonInsuranceServiceLimit" ADD CONSTRAINT "PersonInsuranceServiceLimit_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "PersonInsuranceServiceLimit" ADD CONSTRAINT "PersonInsuranceServiceLimit_insuranceId_fkey" FOREIGN KEY ("insuranceId") REFERENCES "Insurance"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
ALTER TABLE "PersonInsuranceServiceLimit" ADD CONSTRAINT "PersonInsuranceServiceLimit_serviceTypeId_fkey" FOREIGN KEY ("serviceTypeId") REFERENCES "ServiceType"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "_PersonToScheduling" ADD CONSTRAINT "_PersonToScheduling_A_fkey" FOREIGN KEY ("A") REFERENCES "Person"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "_PersonToScheduling" ADD CONSTRAINT "_PersonToScheduling_B_fkey" FOREIGN KEY ("B") REFERENCES "Scheduling"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Criar índices únicos
CREATE UNIQUE INDEX "Company_cnpj_key" ON "Company"("cnpj");
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");
CREATE UNIQUE INDEX "User_login_key" ON "User"("login");
CREATE UNIQUE INDEX "User_cpf_key" ON "User"("cpf");
CREATE UNIQUE INDEX "User_cnpj_key" ON "User"("cnpj");
CREATE UNIQUE INDEX "Client_login_key" ON "Client"("login");
CREATE UNIQUE INDEX "Client_email_key" ON "Client"("email");
CREATE UNIQUE INDEX "Person_cpf_key" ON "Person"("cpf");
CREATE UNIQUE INDEX "Branch_companyId_code_key" ON "Branch"("companyId", "code");
CREATE UNIQUE INDEX "ServiceType_name_companyId_key" ON "ServiceType"("name", "companyId");
CREATE UNIQUE INDEX "Insurance_name_companyId_key" ON "Insurance"("name", "companyId");
CREATE UNIQUE INDEX "Subscription_companyId_key" ON "Subscription"("companyId");
CREATE UNIQUE INDEX "PasswordReset_token_key" ON "PasswordReset"("token");
CREATE UNIQUE INDEX "PersonInsuranceServiceLimit_personId_insuranceId_serviceTyp_key" ON "PersonInsuranceServiceLimit"("personId", "insuranceId", "serviceTypeId");

-- Criar outros índices para performance
CREATE INDEX "Company_active_idx" ON "Company"("active");
CREATE INDEX "User_companyId_idx" ON "User"("companyId");
CREATE INDEX "User_email_idx" ON "User"("email");
CREATE INDEX "User_login_idx" ON "User"("login");
CREATE INDEX "User_role_idx" ON "User"("role");
CREATE INDEX "ProfessionGroup_companyId_idx" ON "ProfessionGroup"("companyId");
CREATE INDEX "ProfessionGroup_active_idx" ON "ProfessionGroup"("active");
CREATE INDEX "ProfessionGroup_deletedAt_idx" ON "ProfessionGroup"("deletedAt");
CREATE INDEX "ProfessionGroup_name_idx" ON "ProfessionGroup"("name");
CREATE INDEX "Profession_companyId_idx" ON "Profession"("companyId");
CREATE INDEX "Profession_groupId_idx" ON "Profession"("groupId");
CREATE INDEX "Profession_active_idx" ON "Profession"("active");
CREATE INDEX "Profession_deletedAt_idx" ON "Profession"("deletedAt");
CREATE INDEX "Profession_name_idx" ON "Profession"("name");
CREATE INDEX "Client_companyId_idx" ON "Client"("companyId");
CREATE INDEX "Client_email_idx" ON "Client"("email");
CREATE INDEX "Client_login_idx" ON "Client"("login");
CREATE INDEX "Person_fullName_idx" ON "Person"("fullName");
CREATE INDEX "Person_cpf_idx" ON "Person"("cpf");
CREATE INDEX "Person_clientId_idx" ON "Person"("clientId");
CREATE INDEX "Branch_companyId_idx" ON "Branch"("companyId");
CREATE INDEX "Branch_active_idx" ON "Branch"("active");
CREATE INDEX "Location_companyId_idx" ON "Location"("companyId");
CREATE INDEX "Location_active_idx" ON "Location"("active");
CREATE INDEX "Location_branchId_idx" ON "Location"("branchId");
CREATE INDEX "ServiceType_companyId_idx" ON "ServiceType"("companyId");
CREATE INDEX "Insurance_companyId_idx" ON "Insurance"("companyId");
CREATE INDEX "Document_personId_idx" ON "Document"("personId");
CREATE INDEX "Document_type_idx" ON "Document"("type");
CREATE INDEX "Document_clientId_idx" ON "Document"("clientId");
CREATE INDEX "Document_companyId_idx" ON "Document"("companyId");
CREATE INDEX "Document_ownerType_clientId_idx" ON "Document"("ownerType", "clientId");
CREATE INDEX "Document_ownerType_companyId_idx" ON "Document"("ownerType", "companyId");
CREATE INDEX "Document_ownerType_userId_idx" ON "Document"("ownerType", "userId");
CREATE INDEX "Document_userId_idx" ON "Document"("userId");
CREATE INDEX "Scheduling_companyId_idx" ON "Scheduling"("companyId");
CREATE INDEX "Scheduling_branchId_idx" ON "Scheduling"("branchId");
CREATE INDEX "Scheduling_userId_startDate_idx" ON "Scheduling"("userId", "startDate");
CREATE INDEX "Scheduling_clientId_startDate_idx" ON "Scheduling"("clientId", "startDate");
CREATE INDEX "Scheduling_status_startDate_idx" ON "Scheduling"("status", "startDate");
CREATE INDEX "Scheduling_locationId_startDate_idx" ON "Scheduling"("locationId", "startDate");
CREATE INDEX "Scheduling_serviceTypeId_idx" ON "Scheduling"("serviceTypeId");
CREATE INDEX "Recurrence_companyId_idx" ON "Recurrence"("companyId");
CREATE INDEX "Recurrence_branchId_idx" ON "Recurrence"("branchId");
CREATE INDEX "Recurrence_personId_idx" ON "Recurrence"("personId");
CREATE INDEX "Recurrence_userId_idx" ON "Recurrence"("userId");
CREATE INDEX "Recurrence_active_idx" ON "Recurrence"("active");
CREATE INDEX "Recurrence_clientId_idx" ON "Recurrence"("clientId");
CREATE INDEX "RecurrencePattern_recurrenceId_idx" ON "RecurrencePattern"("recurrenceId");
CREATE INDEX "RecurrencePattern_dayOfWeek_idx" ON "RecurrencePattern"("dayOfWeek");
CREATE INDEX "WorkingHours_userId_dayOfWeek_idx" ON "WorkingHours"("userId", "dayOfWeek");
CREATE INDEX "WorkingHours_isActive_idx" ON "WorkingHours"("isActive");
CREATE INDEX "EmailConfig_companyId_idx" ON "EmailConfig"("companyId");
CREATE INDEX "EmailConfig_active_idx" ON "EmailConfig"("active");
CREATE INDEX "Subscription_status_idx" ON "Subscription"("status");
CREATE INDEX "AuditLog_userId_idx" ON "AuditLog"("userId");
CREATE INDEX "AuditLog_entityType_entityId_idx" ON "AuditLog"("entityType", "entityId");
CREATE INDEX "AuditLog_companyId_idx" ON "AuditLog"("companyId");
CREATE INDEX "AuditLog_createdAt_idx" ON "AuditLog"("createdAt");
CREATE INDEX "PasswordReset_token_idx" ON "PasswordReset"("token");
CREATE INDEX "PasswordReset_userId_idx" ON "PasswordReset"("userId");
CREATE INDEX "PasswordReset_expiresAt_idx" ON "PasswordReset"("expiresAt");
CREATE INDEX "Contact_personId_idx" ON "Contact"("personId");
CREATE INDEX "ClientInsurance_insuranceId_idx" ON "ClientInsurance"("insuranceId");
CREATE INDEX "PersonInsurance_insuranceId_idx" ON "PersonInsurance"("insuranceId");
CREATE INDEX "PersonInsuranceServiceLimit_personId_insuranceId_idx" ON "PersonInsuranceServiceLimit"("personId", "insuranceId");
CREATE INDEX "PersonInsuranceServiceLimit_serviceTypeId_idx" ON "PersonInsuranceServiceLimit"("serviceTypeId");
CREATE INDEX "_PersonToScheduling_B_index" ON "_PersonToScheduling"("B");