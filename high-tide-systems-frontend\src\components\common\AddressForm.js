'use client';

import React, { useEffect } from 'react';
import { MapPin, Search, Loader2 } from 'lucide-react';
import { useCep } from '@/hooks/useCep';
import MaskedInput from './MaskedInput';
import { ModuleInput, ModuleMaskedInput } from '@/components/ui';

/**
 * Componente reutilizável para formulários de endereço com busca automática por CEP
 *
 * @param {Object} props - Propriedades do componente
 * @param {Object} props.formData - Dados do formulário
 * @param {Function} props.setFormData - Função para atualizar os dados do formulário
 * @param {Object} props.errors - Erros de validação
 * @param {boolean} props.isLoading - Indica se o formulário está em carregamento
 * @param {Object} props.fieldMapping - Mapeamento dos campos do endereço para os campos do formulário
 * @param {string} props.prefix - Prefixo para campos aninhados (ex: "person.")
 * @param {Object} props.classes - Classes CSS personalizadas
 * @param {string} props.moduleColor - Cor do módulo (default, people, scheduler, admin, financial)
 */
const AddressForm = ({
  formData,
  setFormData,
  errors = {},
  isLoading = false,
  fieldMapping = {},
  prefix = '',
  classes = {},
  moduleColor = 'default'
}) => {
  // Hook para busca de CEP
  const { searchAddressByCep, isLoading: isCepLoading, error: cepError } = useCep();

  // Classes CSS padrão
  const defaultClasses = {
    label: 'block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1',
    input: 'w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-800 dark:text-white',
    error: 'text-sm text-red-600 dark:text-red-400 mt-1',
    iconContainer: 'absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none',
    button: 'absolute inset-y-0 right-0 px-3 flex items-center bg-primary-500 hover:bg-primary-600 text-white rounded-r-md transition-colors'
  };

  // Mescla as classes padrão com as classes personalizadas
  const mergedClasses = {
    label: classes.label || defaultClasses.label,
    input: classes.input || defaultClasses.input,
    error: classes.error || defaultClasses.error,
    iconContainer: classes.iconContainer || defaultClasses.iconContainer,
    button: classes.button || defaultClasses.button
  };

  // Mapeamento padrão dos campos
  const defaultMapping = {
    cep: `${prefix}postalCode`,
    logradouro: `${prefix}address`,
    bairro: `${prefix}neighborhood`,
    localidade: `${prefix}city`,
    uf: `${prefix}state`
  };

  // Log para debug
  console.log('Prefix:', prefix);
  console.log('Default mapping:', defaultMapping);

  // Mescla o mapeamento padrão com o mapeamento personalizado
  const mergedMapping = { ...defaultMapping, ...fieldMapping };
  console.log('Mapeamento mesclado:', mergedMapping);

  // Função para lidar com a mudança de valores nos campos
  const handleChange = (e) => {
    const { name, value } = e.target;
    console.log(`Alterando campo: ${name} para valor: ${value}`);

    // Suporte para campos aninhados (ex: "person.address")
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      console.log(`Campo aninhado: ${parent}.${child}. Estado atual:`, formData[parent]);

      const newFormData = {
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value
        }
      };

      console.log('Novo estado do formulário:', newFormData);
      setFormData(newFormData);
    } else {
      const newFormData = {
        ...formData,
        [name]: value
      };

      console.log('Novo estado do formulário:', newFormData);
      setFormData(newFormData);
    }
  };

  // Função para buscar endereço pelo CEP
  const handleCepSearch = async () => {
    // Obtém o valor do CEP do campo correspondente
    const cepField = mergedMapping.cep;
    let cepValue;

    if (cepField.includes('.')) {
      const [parent, child] = cepField.split('.');
      cepValue = formData[parent]?.[child];
    } else {
      cepValue = formData[cepField];
    }

    console.log('Buscando endereço para o CEP:', cepValue);

    if (cepValue) {
      try {
        const result = await searchAddressByCep(cepValue, setFormData, mergedMapping);
        console.log('Resultado da busca de CEP:', result);

        // Forçar a atualização do formulário com os dados recebidos
        if (result) {
          console.log('Dados de endereço recebidos com sucesso, atualizando formulário');

          // Criar um objeto com os campos mapeados
          const updatedFields = {};

          // Para cada campo retornado pela API, mapeia para o campo correspondente no formulário
          Object.keys(result).forEach(apiField => {
            const formField = mergedMapping[apiField];
            if (formField && result[apiField]) {
              updatedFields[formField] = result[apiField];
            }
          });

          console.log('Campos a serem atualizados:', updatedFields);

          // Atualizar o formulário com os dados do endereço
          setFormData(prevData => ({
            ...prevData,
            ...updatedFields
          }));
        }
      } catch (error) {
        console.error('Erro ao buscar CEP:', error);
      }
    }
  };

  // Efeito para buscar endereço automaticamente quando o CEP for preenchido completamente
  useEffect(() => {
    const cepField = mergedMapping.cep;
    let cepValue;

    if (cepField.includes('.')) {
      const [parent, child] = cepField.split('.');
      cepValue = formData[parent]?.[child];
    } else {
      cepValue = formData[cepField];
    }

    // Formatar o CEP se necessário
    if (cepValue && !cepValue.includes('-') && cepValue.replace(/\D/g, '').length === 8) {
      // Formatar o CEP no formato 00000-000
      const cleanCep = cepValue.replace(/\D/g, '');
      const formattedCep = cleanCep.replace(/(\d{5})(\d{3})/, '$1-$2');

      // Atualizar o formData com o CEP formatado
      if (cepField.includes('.')) {
        const [parent, child] = cepField.split('.');
        setFormData(prev => ({
          ...prev,
          [parent]: {
            ...prev[parent],
            [child]: formattedCep
          }
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          [cepField]: formattedCep
        }));
      }
    }

    // Se o CEP tiver 8 dígitos (sem contar a máscara), busca o endereço
    if (cepValue && cepValue.replace(/\D/g, '').length === 8) {
      handleCepSearch();
    }
  }, [formData[mergedMapping.cep]]);

  // Função para obter o valor de um campo, considerando campos aninhados
  const getFieldValue = (fieldName) => {
    console.log(`Obtendo valor para o campo: ${fieldName}`);

    if (fieldName.includes('.')) {
      const [parent, child] = fieldName.split('.');
      console.log(`Campo aninhado: ${parent}.${child}, valor:`, formData[parent]?.[child]);
      return formData[parent]?.[child] || '';
    }

    console.log(`Campo simples: ${fieldName}, valor:`, formData[fieldName]);
    return formData[fieldName] || '';
  };

  // Função para verificar se um campo tem erro
  const hasError = (fieldName) => {
    return errors[fieldName] ? true : false;
  };

  // Função para obter a mensagem de erro de um campo
  const getErrorMessage = (fieldName) => {
    return errors[fieldName] || '';
  };

  return (
    <div className="space-y-4">
      {/* Seção de Endereço */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* CEP com botão de busca */}
        <div>
          <label className={mergedClasses.label} htmlFor={mergedMapping.cep}>
            CEP
          </label>
          <div className="relative">
            <div className={mergedClasses.iconContainer}>
              <MapPin className="h-5 w-5 text-gray-400 dark:text-gray-500" />
            </div>
            {moduleColor ? (
              <div className="relative">
                <ModuleMaskedInput
                  moduleColor={moduleColor}
                  mask="99999-999"
                  replacement={{ 9: /[0-9]/ }}
                  id={mergedMapping.cep}
                  name={mergedMapping.cep}
                  value={getFieldValue(mergedMapping.cep)}
                  onChange={handleChange}
                  placeholder="00000-000"
                  className="pl-10 pr-12"
                  disabled={isLoading || isCepLoading}
                  error={hasError(mergedMapping.cep)}
                />
              </div>
            ) : (
              <MaskedInput
                type="cep"
                id={mergedMapping.cep}
                name={mergedMapping.cep}
                value={getFieldValue(mergedMapping.cep)}
                onChange={handleChange}
                placeholder="00000-000"
                className={`${mergedClasses.input} pl-10 pr-12 ${hasError(mergedMapping.cep) ? 'border-red-500 dark:border-red-700' : ''}`}
                disabled={isLoading || isCepLoading}
              />
            )}
            <button
              type="button"
              onClick={handleCepSearch}
              className={mergedClasses.button}
              disabled={isLoading || isCepLoading}
              aria-label="Buscar CEP"
            >
              {isCepLoading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <Search className="h-5 w-5" />
              )}
            </button>
          </div>
          {hasError(mergedMapping.cep) && (
            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.cep)}</p>
          )}
          {cepError && (
            <p className={mergedClasses.error}>{cepError}</p>
          )}
        </div>

        {/* Estado */}
        <div>
          <label className={mergedClasses.label} htmlFor={mergedMapping.uf}>
            Estado
          </label>
          {moduleColor ? (
            <ModuleInput
              moduleColor={moduleColor}
              id={mergedMapping.uf}
              name={mergedMapping.uf}
              type="text"
              value={getFieldValue(mergedMapping.uf)}
              onChange={handleChange}
              placeholder="UF"
              disabled={isLoading || isCepLoading}
              maxLength={2}
              error={hasError(mergedMapping.uf)}
            />
          ) : (
            <input
              id={mergedMapping.uf}
              name={mergedMapping.uf}
              type="text"
              value={getFieldValue(mergedMapping.uf)}
              onChange={handleChange}
              placeholder="UF"
              className={`${mergedClasses.input} ${hasError(mergedMapping.uf) ? 'border-red-500 dark:border-red-700' : ''}`}
              disabled={isLoading || isCepLoading}
              maxLength={2}
            />
          )}
          {hasError(mergedMapping.uf) && (
            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.uf)}</p>
          )}
        </div>

        {/* Cidade */}
        <div>
          <label className={mergedClasses.label} htmlFor={mergedMapping.localidade}>
            Cidade
          </label>
          {moduleColor ? (
            <ModuleInput
              moduleColor={moduleColor}
              id={mergedMapping.localidade}
              name={mergedMapping.localidade}
              type="text"
              value={getFieldValue(mergedMapping.localidade)}
              onChange={handleChange}
              placeholder="Cidade"
              disabled={isLoading || isCepLoading}
              error={hasError(mergedMapping.localidade)}
            />
          ) : (
            <input
              id={mergedMapping.localidade}
              name={mergedMapping.localidade}
              type="text"
              value={getFieldValue(mergedMapping.localidade)}
              onChange={handleChange}
              placeholder="Cidade"
              className={`${mergedClasses.input} ${hasError(mergedMapping.localidade) ? 'border-red-500 dark:border-red-700' : ''}`}
              disabled={isLoading || isCepLoading}
            />
          )}
          {hasError(mergedMapping.localidade) && (
            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.localidade)}</p>
          )}
        </div>

        {/* Bairro */}
        <div>
          <label className={mergedClasses.label} htmlFor={mergedMapping.bairro}>
            Bairro
          </label>
          {moduleColor ? (
            <ModuleInput
              moduleColor={moduleColor}
              id={mergedMapping.bairro}
              name={mergedMapping.bairro}
              type="text"
              value={getFieldValue(mergedMapping.bairro)}
              onChange={handleChange}
              placeholder="Bairro"
              disabled={isLoading || isCepLoading}
              error={hasError(mergedMapping.bairro)}
            />
          ) : (
            <input
              id={mergedMapping.bairro}
              name={mergedMapping.bairro}
              type="text"
              value={getFieldValue(mergedMapping.bairro)}
              onChange={handleChange}
              placeholder="Bairro"
              className={`${mergedClasses.input} ${hasError(mergedMapping.bairro) ? 'border-red-500 dark:border-red-700' : ''}`}
              disabled={isLoading || isCepLoading}
            />
          )}
          {hasError(mergedMapping.bairro) && (
            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.bairro)}</p>
          )}
        </div>

        {/* Endereço (logradouro) */}
        <div className="md:col-span-2">
          <label className={mergedClasses.label} htmlFor={mergedMapping.logradouro}>
            Logradouro
          </label>
          {moduleColor ? (
            <ModuleInput
              moduleColor={moduleColor}
              id={mergedMapping.logradouro}
              name={mergedMapping.logradouro}
              type="text"
              value={getFieldValue(mergedMapping.logradouro)}
              onChange={handleChange}
              placeholder="Rua, Avenida, etc."
              disabled={isLoading || isCepLoading}
              error={hasError(mergedMapping.logradouro)}
            />
          ) : (
            <input
              id={mergedMapping.logradouro}
              name={mergedMapping.logradouro}
              type="text"
              value={getFieldValue(mergedMapping.logradouro)}
              onChange={handleChange}
              placeholder="Rua, Avenida, etc."
              className={`${mergedClasses.input} ${hasError(mergedMapping.logradouro) ? 'border-red-500 dark:border-red-700' : ''}`}
              disabled={isLoading || isCepLoading}
            />
          )}
          {hasError(mergedMapping.logradouro) && (
            <p className={mergedClasses.error}>{getErrorMessage(mergedMapping.logradouro)}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddressForm;
