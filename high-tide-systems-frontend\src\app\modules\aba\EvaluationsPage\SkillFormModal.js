"use client";

import React, { useState, useEffect } from "react";
import { Activity, Save } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleFormGroup } from "@/components/ui";

const SkillFormModal = ({ isOpen, onClose, onSave, skill }) => {
  const [formData, setFormData] = useState({
    code: "",
    order: "",
    description: ""
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Carregar dados da habilidade para edição
  useEffect(() => {
    if (skill) {
      setFormData({
        code: skill.code || "",
        order: skill.order || "",
        description: skill.description || ""
      });
    }
  }, [skill]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    
    // Limpar erro do campo quando ele for alterado
    if (errors[name]) {
      setErrors({ ...errors, [name]: null });
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.order) {
      newErrors.order = "A ordem é obrigatória";
    } else if (isNaN(Number(formData.order))) {
      newErrors.order = "A ordem deve ser um número";
    }
    
    if (!formData.description || formData.description.trim() === "") {
      newErrors.description = "A descrição é obrigatória";
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);
      
      // Preparar dados para salvar
      const skillData = {
        code: formData.code,
        order: Number(formData.order),
        description: formData.description
      };
      
      onSave(skillData);
    } catch (error) {
      console.error("Erro ao salvar habilidade:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={skill ? "Editar Habilidade" : "Nova Habilidade"}
      size="md"
      moduleColor="abaplus"
      icon={<Activity size={20} />}
    >
      <div className="p-6 space-y-6">
        <ModuleFormGroup label="Código">
          <ModuleInput
            name="code"
            value={formData.code}
            onChange={handleChange}
            placeholder="Código da habilidade (opcional)"
            moduleColor="abaplus"
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Ordem *" error={errors.order}>
          <ModuleInput
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="Ordem da habilidade"
            moduleColor="abaplus"
            error={!!errors.order}
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Descrição *" error={errors.description}>
          <ModuleInput
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Descrição da habilidade"
            moduleColor="abaplus"
            error={!!errors.description}
          />
        </ModuleFormGroup>

        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Save size={18} />
                <span>Salvar</span>
              </>
            )}
          </button>
        </div>
      </div>
    </ModuleModal>
  );
};

export default SkillFormModal;
