// src/app/modules/aba/services/evaluationsService.js
import { api } from "@/utils/api";

export const evaluationsService = {
  // Get evaluations with optional filters
  getEvaluations: async (filters = {}) => {
    const { page = 1, limit = 10, search = "", active, type } = filters;

    try {
      const response = await api.get("/aba/evaluations", {
        params: {
          page,
          limit,
          search: search || undefined,
          active: active === undefined ? undefined : active,
          type: type || undefined
        }
      });

      console.log("API Response:", response.data);

      // The backend returns data in the format { evaluations: [...], total: n, pages: n }
      // But our frontend component expects { items: [...], total: n, pages: n }
      return {
        items: response.data.evaluations || [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching evaluations:", error);
      throw error;
    }
  },

  // Get a single evaluation by ID
  getEvaluation: async (id) => {
    try {
      const response = await api.get(`/aba/evaluations/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching evaluation ${id}:`, error);
      throw error;
    }
  },

  // Create a new evaluation
  createEvaluation: async (evaluationData) => {
    try {
      const response = await api.post("/aba/evaluations", evaluationData);
      return response.data;
    } catch (error) {
      console.error("Error creating evaluation:", error);
      throw error;
    }
  },

  // Update an existing evaluation
  updateEvaluation: async (id, evaluationData) => {
    try {
      const response = await api.put(`/aba/evaluations/${id}`, evaluationData);
      return response.data;
    } catch (error) {
      console.error(`Error updating evaluation ${id}:`, error);
      throw error;
    }
  },

  // Toggle evaluation active status
  toggleEvaluationStatus: async (id) => {
    try {
      const response = await api.patch(`/aba/evaluations/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling status for evaluation ${id}:`, error);
      throw error;
    }
  },

  // Delete an evaluation
  deleteEvaluation: async (id) => {
    try {
      const response = await api.delete(`/aba/evaluations/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting evaluation ${id}:`, error);
      throw error;
    }
  },

  // Add skills to an evaluation
  addSkillsToEvaluation: async (evaluationId, skillIds) => {
    try {
      // Adicionar cada habilidade individualmente
      const promises = skillIds.map(skillId =>
        api.post(`/aba/skills/evaluations/${evaluationId}/skills/${skillId}`)
      );

      await Promise.all(promises);
      return { success: true };
    } catch (error) {
      console.error(`Error adding skills to evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Remove a skill from an evaluation
  removeSkillFromEvaluation: async (evaluationId, skillId) => {
    try {
      const response = await api.delete(`/aba/skills/evaluations/${evaluationId}/skills/${skillId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing skill ${skillId} from evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Get all levels from all evaluations
  getAllLevels: async (filters = {}) => {
    try {
      console.log("evaluationsService.getAllLevels - Enviando requisição com parâmetros:", {
        page: filters.page || 1,
        limit: filters.limit || 10,
        search: filters.search || undefined,
        evaluationId: filters.evaluationId || undefined
      });

      const response = await api.get("/aba/levels", {
        params: {
          page: filters.page || 1,
          limit: filters.limit || 10,
          search: filters.search || undefined,
          evaluationId: filters.evaluationId || undefined
        }
      });

      console.log("evaluationsService.getAllLevels - Resposta da API:", response.data);

      // Verificar a estrutura da resposta
      const items = response.data.levels || response.data.items || [];
      const total = response.data.total || 0;

      console.log("evaluationsService.getAllLevels - Itens extraídos:", items);

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching all levels:", error);
      throw error;
    }
  },

  // Add scores to an evaluation
  addScoresToEvaluation: async (evaluationId, scores) => {
    try {
      // Adicionar cada pontuação individualmente
      const promises = scores.map(score =>
        api.post(`/aba/scores`, { ...score, evaluationId })
      );

      await Promise.all(promises);
      return { success: true };
    } catch (error) {
      console.error(`Error adding scores to evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Update a score
  updateScore: async (evaluationId, scoreId, scoreData) => {
    try {
      const response = await api.put(`/aba/scores/${scoreId}`, scoreData);
      return response.data;
    } catch (error) {
      console.error(`Error updating score ${scoreId} of evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Remove a score from an evaluation
  removeScoreFromEvaluation: async (evaluationId, scoreId) => {
    try {
      const response = await api.delete(`/aba/scores/${scoreId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing score ${scoreId} from evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Add tasks to an evaluation
  addTasksToEvaluation: async (evaluationId, tasks) => {
    try {
      // Adicionar cada tarefa individualmente
      const promises = tasks.map(task =>
        api.post(`/aba/tasks`, { ...task, evaluationId })
      );

      await Promise.all(promises);
      return { success: true };
    } catch (error) {
      console.error(`Error adding tasks to evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Update a task
  updateTask: async (evaluationId, taskId, taskData) => {
    try {
      const response = await api.put(`/aba/tasks/${taskId}`, taskData);
      return response.data;
    } catch (error) {
      console.error(`Error updating task ${taskId} of evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Remove a task from an evaluation
  removeTaskFromEvaluation: async (evaluationId, taskId) => {
    try {
      const response = await api.delete(`/aba/tasks/${taskId}`);
      return response.data;
    } catch (error) {
      console.error(`Error removing task ${taskId} from evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Get all tasks from all evaluations
  getAllTasks: async (filters = {}) => {
    try {
      console.log("evaluationsService.getAllTasks - Enviando requisição com parâmetros:", {
        page: filters.page || 1,
        limit: filters.limit || 10,
        search: filters.search || undefined,
        skillId: filters.skillId || undefined,
        levelId: filters.levelId || undefined,
        evaluationId: filters.evaluationId || undefined
      });

      const response = await api.get("/aba/tasks", {
        params: {
          page: filters.page || 1,
          limit: filters.limit || 10,
          search: filters.search || undefined,
          skillId: filters.skillId || undefined,
          levelId: filters.levelId || undefined,
          evaluationId: filters.evaluationId || undefined
        }
      });

      console.log("evaluationsService.getAllTasks - Resposta da API:", response.data);

      // Verificar a estrutura da resposta
      const items = response.data.tasks || response.data.items || [];
      const total = response.data.total || 0;

      console.log("evaluationsService.getAllTasks - Itens extraídos:", items);

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching all tasks:", error);
      throw error;
    }
  },

  // Add a level to an evaluation
  addLevel: async (evaluationId, levelData) => {
    try {
      const response = await api.post(`/aba/levels`, {
        ...levelData,
        evaluationId
      });
      return response.data;
    } catch (error) {
      console.error(`Error adding level to evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Update a level
  updateLevel: async (evaluationId, levelId, levelData) => {
    try {
      const response = await api.put(`/aba/levels/${levelId}`, levelData);
      return response.data;
    } catch (error) {
      console.error(`Error updating level ${levelId} of evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Delete a level
  deleteLevel: async (evaluationId, levelId) => {
    try {
      const response = await api.delete(`/aba/levels/${levelId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting level ${levelId} of evaluation ${evaluationId}:`, error);
      throw error;
    }
  },

  // Get all tasks from an evaluation (mock implementation)
  getMockTasks: async (filters = {}) => {
    try {
      // Quando a API estiver pronta, descomentar este código
      /*
      const response = await api.get("/tasks", {
        params: {
          page: filters.page || 1,
          limit: filters.limit || 10,
          search: filters.search || undefined,
          skillId: filters.skillId || undefined,
          levelId: filters.levelId || undefined
        }
      });
      return response.data;
      */

      // Dados de exemplo para demonstração
      const mockTasks = [
        {
          id: "1",
          skillId: "1",
          levelId: "1",
          order: 1,
          name: "Solicitação Verbal",
          milestone: "M1",
          item: "A",
          question: "A criança consegue fazer solicitações verbais básicas?",
          example: "Pedir água, comida ou brinquedo preferido",
          criteria: "Deve fazer pelo menos 3 solicitações diferentes em contextos apropriados",
          objective: "Desenvolver comunicação funcional básica"
        },
        {
          id: "2",
          skillId: "2",
          levelId: "2",
          order: 2,
          name: "Seguir Instruções",
          milestone: "M2",
          item: "B",
          question: "A criança consegue seguir instruções de duas etapas?",
          example: "Pegue o livro e coloque na mesa",
          criteria: "Deve completar a sequência sem dicas adicionais",
          objective: "Desenvolver compreensão de linguagem e sequenciamento"
        },
        {
          id: "3",
          skillId: "3",
          levelId: "4",
          order: 1,
          name: "Análise ABC",
          milestone: "F1",
          item: "C",
          question: "Quais são os antecedentes e consequências do comportamento?",
          example: "Comportamento de birra durante transições de atividades",
          criteria: "Identificar padrões consistentes em pelo menos 3 ocorrências",
          objective: "Identificar gatilhos e reforçadores do comportamento"
        },
        {
          id: "4",
          skillId: "5",
          levelId: "5",
          order: 2,
          name: "Estratégias de Intervenção",
          milestone: "F2",
          item: "D",
          question: "Quais estratégias são eficazes para reduzir o comportamento?",
          example: "Uso de reforço diferencial, extinção, redirecionamento",
          criteria: "Redução de 50% na frequência do comportamento em 2 semanas",
          objective: "Desenvolver plano de intervenção baseado na função do comportamento"
        },
        {
          id: "5",
          skillId: "1",
          levelId: "3",
          order: 3,
          name: "Imitação Verbal",
          milestone: "M3",
          item: "E",
          question: "A criança consegue imitar sons e palavras?",
          example: "Repetir palavras simples após modelo do terapeuta",
          criteria: "Deve imitar pelo menos 10 palavras diferentes com precisão",
          objective: "Desenvolver habilidades de imitação verbal para expansão do vocabulário"
        },
        {
          id: "6",
          skillId: "4",
          levelId: "1",
          order: 4,
          name: "Interação Social Básica",
          milestone: "S1",
          item: "F",
          question: "A criança inicia e responde a interações sociais básicas?",
          example: "Cumprimentar, compartilhar, revezar turnos",
          criteria: "Deve demonstrar pelo menos 5 comportamentos sociais diferentes",
          objective: "Desenvolver habilidades sociais fundamentais"
        }
      ];

      // Filtrar por pesquisa
      let filteredTasks = mockTasks;
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredTasks = filteredTasks.filter(
          task =>
            task.name.toLowerCase().includes(searchLower) ||
            (task.question && task.question.toLowerCase().includes(searchLower))
        );
      }

      // Filtrar por habilidade
      if (filters.skillId) {
        filteredTasks = filteredTasks.filter(task => task.skillId === filters.skillId);
      }

      // Filtrar por nível
      if (filters.levelId) {
        filteredTasks = filteredTasks.filter(task => task.levelId === filters.levelId);
      }

      // Paginação
      const page = filters.page || 1;
      const limit = filters.limit || 10;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedTasks = filteredTasks.slice(startIndex, endIndex);

      return {
        items: paginatedTasks,
        total: filteredTasks.length,
        page,
        limit,
        pages: Math.ceil(filteredTasks.length / limit)
      };
    } catch (error) {
      console.error("Error fetching all tasks:", error);
      throw error;
    }
  }
};

export default evaluationsService;
