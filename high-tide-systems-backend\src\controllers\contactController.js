// src/controllers/contactController.js
const { validationResult } = require('express-validator');
const { body } = require('express-validator');
const prisma = require('../utils/prisma');

// Validation rules
const contactValidation = [
  body('name').notEmpty().withMessage('Nome do contato é obrigatório'),
  body('relationship').optional(),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Email inválido'),
  body('phone')
    .optional()
    .matches(/^\d{10,11}$/)
    .withMessage('Telefone inválido. Digite apenas números (10 ou 11 dígitos)'),
  body('notes').optional(),
  body('personId')
    .notEmpty()
    .withMessage('ID da pessoa é obrigatório')
    .isUUID()
    .withMessage('ID da pessoa inválido'),
];

class ContactController {
  /**
   * Create a new contact
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, relationship, email, phone, notes, personId } = req.body;

      // Check if person exists
      const person = await prisma.person.findUnique({
        where: { id: personId },
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // Get person with client relationships
        const personWithClients = await prisma.person.findUnique({
          where: { id: personId },
          include: {
            clientPersons: {
              include: {
                client: {
                  select: {
                    createdBy: {
                      select: {
                        companyId: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        // For company users, check if the person belongs to this company
        if (personWithClients.clientPersons && personWithClients.clientPersons.length > 0) {
          const hasCompanyAccess = personWithClients.clientPersons.some(cp =>
            cp.client?.createdBy?.companyId === req.user.companyId
          );

          if (!hasCompanyAccess) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        }
      }

      // Create the contact
      const contact = await prisma.contact.create({
        data: {
          name,
          relationship,
          email,
          phone,
          notes,
          person: {
            connect: { id: personId }
          },
          createdBy: {
            connect: { id: req.user.id }
          }
        },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'CREATE',
          entityType: 'Contact',
          entityId: contact.id,
          details: { requestData: req.body },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.status(201).json(contact);
    } catch (error) {
      console.error('Erro ao criar contato:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Get a list of contacts for a person
   */
  static async list(req, res) {
    try {
      const { personId } = req.query;

      if (!personId) {
        return res.status(400).json({ message: 'ID da pessoa é obrigatório' });
      }

      // Check if person exists
      const person = await prisma.person.findUnique({
        where: { id: personId },
      });

      if (!person) {
        return res.status(404).json({ message: 'Pessoa não encontrada' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // Get person with client relationships
        const personWithClients = await prisma.person.findUnique({
          where: { id: person.id },
          include: {
            clientPersons: {
              include: {
                client: {
                  select: {
                    createdBy: {
                      select: {
                        companyId: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        // For company users, check if the person belongs to this company
        if (personWithClients.clientPersons && personWithClients.clientPersons.length > 0) {
          const hasCompanyAccess = personWithClients.clientPersons.some(cp =>
            cp.client?.createdBy?.companyId === req.user.companyId
          );

          if (!hasCompanyAccess) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a esta pessoa' });
          }
        }
      }

      const contacts = await prisma.contact.findMany({
        where: { personId },
        orderBy: { createdAt: 'desc' },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      res.json(contacts);
    } catch (error) {
      console.error('Erro ao listar contatos:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Get a single contact by ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const contact = await prisma.contact.findUnique({
        where: { id },
        include: {
          person: {
            select: {
              id: true,
              fullName: true,
              clientId: true,
              createdById: true,
            },
          },
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
        },
      });

      if (!contact) {
        return res.status(404).json({ message: 'Contato não encontrado' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // Get person with client relationships
        const personWithClients = await prisma.person.findUnique({
          where: { id: contact.person.id },
          include: {
            clientPersons: {
              include: {
                client: {
                  select: {
                    createdBy: {
                      select: {
                        companyId: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        // For company users, check if the person belongs to this company
        if (personWithClients.clientPersons && personWithClients.clientPersons.length > 0) {
          const hasCompanyAccess = personWithClients.clientPersons.some(cp =>
            cp.client?.createdBy?.companyId === req.user.companyId
          );

          if (!hasCompanyAccess) {
            return res.status(403).json({ message: 'Acesso negado a este contato' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: contact.person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a este contato' });
          }
        }
      }

      res.json(contact);
    } catch (error) {
      console.error('Erro ao buscar contato:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Update a contact
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Check if contact exists
      const existingContact = await prisma.contact.findUnique({
        where: { id },
        include: {
          person: {
            select: {
              id: true,
              clientId: true,
              createdById: true,
            },
          },
        },
      });

      if (!existingContact) {
        return res.status(404).json({ message: 'Contato não encontrado' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // Get person with client relationships
        const personWithClients = await prisma.person.findUnique({
          where: { id: existingContact.person.id },
          include: {
            clientPersons: {
              include: {
                client: {
                  select: {
                    createdBy: {
                      select: {
                        companyId: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        // For company users, check if the person belongs to this company
        if (personWithClients.clientPersons && personWithClients.clientPersons.length > 0) {
          const hasCompanyAccess = personWithClients.clientPersons.some(cp =>
            cp.client?.createdBy?.companyId === req.user.companyId
          );

          if (!hasCompanyAccess) {
            return res.status(403).json({ message: 'Acesso negado a este contato' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: existingContact.person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a este contato' });
          }
        }
      }

      const { name, relationship, email, phone, notes } = req.body;

      // Update the contact
      const contact = await prisma.contact.update({
        where: { id },
        data: {
          name,
          relationship,
          email,
          phone,
          notes,
        },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Contact',
          entityId: id,
          details: {
            before: {
              name: existingContact.name,
              relationship: existingContact.relationship,
              email: existingContact.email,
              phone: existingContact.phone,
              notes: existingContact.notes
            },
            after: {
              name,
              relationship,
              email,
              phone,
              notes
            },
            changes: Object.keys(req.body)
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.json(contact);
    } catch (error) {
      console.error('Erro ao atualizar contato:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }

  /**
   * Delete a contact
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Check if contact exists
      const contact = await prisma.contact.findUnique({
        where: { id },
        include: {
          person: {
            select: {
              id: true,
              fullName: true,
              clientId: true,
              createdById: true,
            },
          },
        },
      });

      if (!contact) {
        return res.status(404).json({ message: 'Contato não encontrado' });
      }

      // Check access rights
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // Get person with client relationships
        const personWithClients = await prisma.person.findUnique({
          where: { id: contact.person.id },
          include: {
            clientPersons: {
              include: {
                client: {
                  select: {
                    createdBy: {
                      select: {
                        companyId: true,
                      },
                    },
                  },
                },
              },
            },
          },
        });

        // For company users, check if the person belongs to this company
        if (personWithClients.clientPersons && personWithClients.clientPersons.length > 0) {
          const hasCompanyAccess = personWithClients.clientPersons.some(cp =>
            cp.client?.createdBy?.companyId === req.user.companyId
          );

          if (!hasCompanyAccess) {
            return res.status(403).json({ message: 'Acesso negado a este contato' });
          }
        } else {
          // Check if the person was created by a user from this company
          const creator = await prisma.user.findUnique({
            where: { id: contact.person.createdById },
            select: { companyId: true },
          });

          if (creator?.companyId !== req.user.companyId) {
            return res.status(403).json({ message: 'Acesso negado a este contato' });
          }
        }
      }

      // Delete the contact
      await prisma.contact.delete({
        where: { id },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'DELETE',
          entityType: 'Contact',
          entityId: id,
          details: {
            contactData: {
              name: contact.name,
              relationship: contact.relationship,
              email: contact.email,
              phone: contact.phone,
              personId: contact.person.id,
              personName: contact.person.fullName
            }
          },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.status(204).send();
    } catch (error) {
      console.error('Erro ao excluir contato:', error);
      res.status(500).json({ message: 'Erro interno do servidor' });
    }
  }
}

module.exports = {
  ContactController,
  contactValidation,
};