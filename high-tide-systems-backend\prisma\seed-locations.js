// prisma/seed-locations.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Tipos de localizações por categoria de serviço
const locationTypes = {
  // Localizações para serviços odontológicos
  dental: [
    { name: 'Consultório Odontológico 1', suffix: 'Principal' },
    { name: 'Consultório Odontológico 2', suffix: 'Especialidades' },
    { name: 'Sala de Raio-X', suffix: 'Diagnós<PERSON><PERSON>' },
    { name: 'Sala de Cirurgia', suffix: 'Procedimentos' },
    { name: 'Sala de Ortodontia', suffix: 'Aparelhos' },
    { name: 'Sala de Endodontia', suffix: 'Canal' },
    { name: 'Sala de Prótese', suffix: 'Reabilitação' },
    { name: '<PERSON><PERSON> de Implante<PERSON>', suffix: 'Implantod<PERSON><PERSON>' },
    { name: '<PERSON><PERSON> de Pediatria', suffix: 'Odontopediatria' },
    { name: 'Sala de Estética', suffix: 'Harmonização' }
  ],
  
  // Localizações para serviços médicos
  medical: [
    { name: 'Consultório Médico 1', suffix: 'Clínica Geral' },
    { name: 'Consultório Médico 2', suffix: 'Especialidades' },
    { name: 'Sala de Exames', suffix: 'Diagnóstico' },
    { name: 'Sala de Procedimentos', suffix: 'Pequenas Cirurgias' },
    { name: 'Sala de Cardiologia', suffix: 'Exames Cardíacos' },
    { name: 'Sala de Dermatologia', suffix: 'Procedimentos Estéticos' },
    { name: 'Sala de Ginecologia', suffix: 'Saúde da Mulher' },
    { name: 'Sala de Pediatria', suffix: 'Saúde Infantil' },
    { name: 'Sala de Ortopedia', suffix: 'Traumatologia' },
    { name: 'Sala de Oftalmologia', suffix: 'Exames Visuais' },
    { name: 'Sala de Neurologia', suffix: 'Exames Neurológicos' },
    { name: 'Sala de Ultrassonografia', suffix: 'Imagem' },
    { name: 'Sala de Endocrinologia', suffix: 'Metabólico' },
    { name: 'Sala de Urologia', suffix: 'Saúde Masculina' },
    { name: 'Sala de Geriatria', suffix: 'Saúde do Idoso' }
  ],
  
  // Localizações para serviços psicológicos
  psychological: [
    { name: 'Consultório Psicológico 1', suffix: 'Adulto' },
    { name: 'Consultório Psicológico 2', suffix: 'Infantil' },
    { name: 'Sala de Terapia Individual', suffix: 'Privativa' },
    { name: 'Sala de Terapia em Grupo', suffix: 'Coletiva' },
    { name: 'Sala de Avaliação Psicológica', suffix: 'Diagnóstico' },
    { name: 'Sala de Terapia Familiar', suffix: 'Família' },
    { name: 'Sala de Terapia Cognitivo-Comportamental', suffix: 'TCC' },
    { name: 'Sala de Mindfulness', suffix: 'Meditação' },
    { name: 'Sala de Orientação Vocacional', suffix: 'Carreira' },
    { name: 'Sala de Neuropsicologia', suffix: 'Avaliação Cognitiva' }
  ],
  
  // Localizações para serviços de fisioterapia
  physiotherapy: [
    { name: 'Sala de Fisioterapia 1', suffix: 'Geral' },
    { name: 'Sala de Fisioterapia 2', suffix: 'Ortopédica' },
    { name: 'Sala de Fisioterapia Neurológica', suffix: 'Neuro' },
    { name: 'Sala de Fisioterapia Respiratória', suffix: 'Respiratória' },
    { name: 'Sala de Fisioterapia Esportiva', suffix: 'Esportiva' },
    { name: 'Sala de RPG', suffix: 'Postural' },
    { name: 'Sala de Pilates', suffix: 'Equipamentos' },
    { name: 'Sala de Hidroterapia', suffix: 'Piscina Terapêutica' },
    { name: 'Sala de Eletroterapia', suffix: 'Eletroestimulação' },
    { name: 'Sala de Terapia Manual', suffix: 'Manipulação' },
    { name: 'Sala de Reabilitação', suffix: 'Pós-Cirúrgica' },
    { name: 'Sala de Acupuntura', suffix: 'Terapias Alternativas' }
  ],
  
  // Localizações para serviços de autismo e desenvolvimento
  autism: [
    { name: 'Sala de Avaliação TEA', suffix: 'Diagnóstico' },
    { name: 'Sala de Terapia ABA', suffix: 'Comportamental' },
    { name: 'Sala de Terapia Ocupacional', suffix: 'Habilidades' },
    { name: 'Sala de Fonoaudiologia', suffix: 'Comunicação' },
    { name: 'Sala de Psicomotricidade', suffix: 'Movimento' },
    { name: 'Sala de Musicoterapia', suffix: 'Expressão' },
    { name: 'Sala de Integração Sensorial', suffix: 'Sensorial' },
    { name: 'Sala de Psicopedagogia', suffix: 'Aprendizagem' },
    { name: 'Sala de Orientação Familiar', suffix: 'Família' },
    { name: 'Sala de Habilidades Sociais', suffix: 'Socialização' },
    { name: 'Sala Multissensorial', suffix: 'Snoezelen' },
    { name: 'Sala de Atividades da Vida Diária', suffix: 'AVD' }
  ],
  
  // Localizações para serviços de nutrição
  nutrition: [
    { name: 'Consultório Nutricional 1', suffix: 'Avaliação' },
    { name: 'Consultório Nutricional 2', suffix: 'Acompanhamento' },
    { name: 'Sala de Bioimpedância', suffix: 'Composição Corporal' },
    { name: 'Sala de Nutrição Esportiva', suffix: 'Desempenho' },
    { name: 'Sala de Nutrição Clínica', suffix: 'Patologias' },
    { name: 'Sala de Nutrição Materno-Infantil', suffix: 'Gestantes e Crianças' },
    { name: 'Sala de Reeducação Alimentar', suffix: 'Mudança de Hábitos' },
    { name: 'Sala de Nutrição Funcional', suffix: 'Alimentos Funcionais' },
    { name: 'Cozinha Experimental', suffix: 'Oficinas Culinárias' },
    { name: 'Sala de Orientação em Grupo', suffix: 'Palestras' }
  ]
};

// Mapeamento de indústrias para categorias de localizações
const industryToLocationCategories = {
  'Saúde': ['medical', 'physiotherapy', 'nutrition'],
  'Odontologia': ['dental'],
  'Psicologia': ['psychological'],
  'Fisioterapia': ['physiotherapy'],
  'Nutrição': ['nutrition'],
  'Terapia': ['psychological', 'physiotherapy', 'autism'],
  'Medicina': ['medical'],
  'Tecnologia': ['medical', 'psychological'], // Clínicas tecnológicas podem oferecer telemedicina
  'Educação': ['autism', 'psychological'], // Instituições educacionais com foco em desenvolvimento
  'Bem-estar': ['nutrition', 'physiotherapy'],
  'Serviços': ['medical', 'dental', 'psychological', 'physiotherapy', 'autism', 'nutrition'] // Empresas de serviços gerais podem oferecer diversos tipos
};

// Função para gerar um número aleatório entre min e max (inclusive)
function getRandomInt(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

// Função para embaralhar um array
function shuffleArray(array) {
  return [...array].sort(() => 0.5 - Math.random());
}

// Função principal
async function main() {
  console.log('Iniciando seed de localizações...');
  
  try {
    // Buscar todas as empresas ativas exceto a empresa de teste
    const companies = await prisma.company.findMany({
      where: {
        NOT: {
          id: '00000000-0000-0000-0000-000000000001' // Excluir a empresa de teste
        },
        active: true
      },
      include: {
        branches: true // Incluir as unidades de cada empresa
      }
    });
    
    console.log(`Encontradas ${companies.length} empresas para adicionar localizações`);
    
    // Para cada empresa, criar entre 5 e 20 localizações
    for (const company of companies) {
      console.log(`\nProcessando empresa: ${company.name} (ID: ${company.id})`);
      
      // Determinar a indústria da empresa ou usar 'Serviços' como padrão
      const industry = company.industry || 'Serviços';
      console.log(`Indústria: ${industry}`);
      
      // Determinar as categorias de localizações com base na indústria
      const locationCategories = industryToLocationCategories[industry] || ['medical', 'dental', 'psychological'];
      console.log(`Categorias de localizações selecionadas: ${locationCategories.join(', ')}`);
      
      // Criar um pool de localizações possíveis para esta empresa
      let locationPool = [];
      for (const category of locationCategories) {
        locationPool = [...locationPool, ...locationTypes[category]];
      }
      
      // Embaralhar o pool de localizações
      locationPool = shuffleArray(locationPool);
      
      // Determinar quantas localizações criar (entre 5 e 20)
      const numLocations = getRandomInt(5, 20);
      console.log(`Criando ${numLocations} localizações para a empresa ${company.name}`);
      
      // Selecionar as localizações a serem criadas
      const locationsToCreate = locationPool.slice(0, numLocations);
      
      // Verificar se a empresa tem unidades
      if (company.branches && company.branches.length > 0) {
        console.log(`A empresa tem ${company.branches.length} unidades`);
        
        // Distribuir as localizações entre as unidades
        for (let i = 0; i < locationsToCreate.length; i++) {
          const location = locationsToCreate[i];
          // Selecionar uma unidade aleatória para esta localização
          const branch = company.branches[i % company.branches.length];
          
          try {
            // Verificar se a localização já existe para esta unidade
            const existingLocation = await prisma.location.findFirst({
              where: {
                name: `${location.name} - ${location.suffix}`,
                branchId: branch.id
              }
            });
            
            if (existingLocation) {
              console.log(`Localização "${location.name} - ${location.suffix}" já existe para a unidade ${branch.name}. Pulando...`);
              continue;
            }
            
            // Criar a localização
            const createdLocation = await prisma.location.create({
              data: {
                name: `${location.name} - ${location.suffix}`,
                address: branch.address || company.address || 'Endereço não especificado',
                phone: branch.phone || company.phone || null,
                branchId: branch.id,
                companyId: company.id
              }
            });
            
            console.log(`✅ Localização criada: ${createdLocation.name} (Unidade: ${branch.name})`);
          } catch (error) {
            console.error(`Erro ao criar localização "${location.name}" para unidade ${branch.name}:`, error);
          }
        }
      } else {
        console.log(`A empresa não tem unidades. Criando localizações diretamente para a empresa.`);
        
        // Criar localizações diretamente para a empresa
        for (const location of locationsToCreate) {
          try {
            // Verificar se a localização já existe para esta empresa
            const existingLocation = await prisma.location.findFirst({
              where: {
                name: `${location.name} - ${location.suffix}`,
                companyId: company.id,
                branchId: null
              }
            });
            
            if (existingLocation) {
              console.log(`Localização "${location.name} - ${location.suffix}" já existe para a empresa ${company.name}. Pulando...`);
              continue;
            }
            
            // Criar a localização
            const createdLocation = await prisma.location.create({
              data: {
                name: `${location.name} - ${location.suffix}`,
                address: company.address || 'Endereço não especificado',
                phone: company.phone || null,
                companyId: company.id
              }
            });
            
            console.log(`✅ Localização criada: ${createdLocation.name} (Empresa: ${company.name})`);
          } catch (error) {
            console.error(`Erro ao criar localização "${location.name}" para empresa ${company.name}:`, error);
          }
        }
      }
    }
    
    console.log('\nSeed de localizações concluído com sucesso!');
  } catch (error) {
    console.error('Erro durante o seed de localizações:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
