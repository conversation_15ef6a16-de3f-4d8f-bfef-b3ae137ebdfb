'use client';

import React, { useState, useEffect } from 'react';
import { Search, RefreshCw, Filter } from 'lucide-react';
import { FilterButton } from '@/components/ui/ModuleHeader';
import MultiSelect from '@/components/ui/multi-select';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';

export const CalendarFilters = ({ filters = {}, onFiltersChange, onSearch }) => {
  const [providers, setProviders] = useState([]);
  const [persons, setPersons] = useState([]); // Changed from clients to persons
  const [locations, setLocations] = useState([]);
  const [serviceTypes, setServiceTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasChanges, setHasChanges] = useState(false);
  const [error, setError] = useState(null);

  // Log do estado atual para debug
  useEffect(() => {
    console.log("[CalendarFilters] Estado atual:", {
      providers: providers.length,
      persons: persons.length,
      locations: locations.length,
      serviceTypes: serviceTypes.length,
      isLoading,
      hasChanges,
      error
    });
  }, [providers, persons, locations, serviceTypes, isLoading, hasChanges, error]);

  useEffect(() => {
    const loadData = async () => {
      try {
        setError(null);
        setIsLoading(true);
        console.log("[CalendarFilters] Carregando dados para filtros...");

        // Load data separately to better debug issues
        console.log("[CalendarFilters] Carregando profissionais...");
        const providersData = await appointmentService.getProviders();
        console.log(`[CalendarFilters] Carregados ${providersData?.length || 0} profissionais`);

        console.log("[CalendarFilters] Carregando pessoas...");
        const personsData = await appointmentService.getPersons();
        console.log(`[CalendarFilters] Carregadas ${personsData?.length || 0} pessoas`);

        console.log("[CalendarFilters] Carregando locais...");
        const locationsData = await appointmentService.getLocations();
        console.log(`[CalendarFilters] Carregados ${locationsData?.length || 0} locais`);

        console.log("[CalendarFilters] Carregando tipos de serviço...");
        const serviceTypesData = await appointmentService.getServiceTypes();
        console.log("[CalendarFilters] Dados de tipos de serviço:", serviceTypesData);

        // Log detalhado dos dados recebidos
        console.log("[CalendarFilters] Dados brutos de providers:", providersData);

        // Verificar se há dados inválidos
        const invalidProviders = (providersData || []).filter(p => !p || !p.id || !p.fullName);
        if (invalidProviders.length > 0) {
          console.warn("[CalendarFilters] Providers inválidos encontrados:", invalidProviders);
        }

        // Garantir que temos um array válido
        const validProvidersData = Array.isArray(providersData) ? providersData : [];

        // Map providers to MultiSelect format
        const formattedProviders = validProvidersData
          .filter(p => p && p.id && p.fullName)
          .map(provider => ({
            value: provider.id,
            label: provider.fullName
          }));

        console.log("[CalendarFilters] Providers formatados para MultiSelect:", formattedProviders);

        // Se não houver providers, criar alguns de teste
        if (formattedProviders.length === 0) {
          console.warn("[CalendarFilters] Nenhum provider formatado. Criando lista de teste...");
          const testProviders = [
            { value: "test-provider-1", label: "Dr. Teste 1" },
            { value: "test-provider-2", label: "Dr. Teste 2" },
            { value: "test-provider-3", label: "Dr. Teste 3" }
          ];
          setProviders(testProviders);
        } else {
          setProviders(formattedProviders);
        }

        // Map persons to MultiSelect format (changed from clients)
        setPersons((personsData || [])
          .filter(p => p && p.id && p.fullName)
          .map(person => ({
            value: person.id,
            label: person.fullName
          }))
        );

        // Map locations to MultiSelect format
        setLocations((locationsData || [])
          .filter(l => l && l.id && l.name)
          .map(location => ({
            value: location.id,
            label: location.name
          }))
        );

        // Map service types to MultiSelect format
        const formattedServiceTypes = (serviceTypesData || [])
          .filter(st => st && st.id && st.name)
          .map(type => ({
            value: type.id,
            label: type.name
          }));

        console.log("Formatted service types:", formattedServiceTypes);
        setServiceTypes(formattedServiceTypes);
      } catch (error) {
        console.error('Erro ao carregar dados:', error);
        setError('Falha ao carregar opções. Por favor, tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSearch = () => {
    onSearch(filters);
  };

  useEffect(() => {
    // Changed clients to persons
    const hasActiveFilters =
      filters.providers?.length > 0 ||
      filters.persons?.length > 0 ||
      filters.locations?.length > 0 ||
      filters.serviceTypes?.length > 0;
    setHasChanges(hasActiveFilters);
  }, [filters]);

  const handleFilterChange = (newFilters) => {
    onFiltersChange(newFilters);
  };

  const handleClearFilters = () => {
    // Changed clients to persons
    const clearedFilters = {
      providers: [],
      persons: [],
      locations: [],
      serviceTypes: []
    };
    onFiltersChange(clearedFilters);
    // Dispara a busca imediatamente após limpar os filtros
    onSearch(clearedFilters);
  };

  return (
    <div className="rounded-lg">
      {error && (
        <div className="p-3 mb-4 bg-error-50 dark:bg-error-900/20 text-error-700 dark:text-error-300 rounded-lg border border-error-200 dark:border-error-700">
          {error}
        </div>
      )}

      <div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <MultiSelect
              label="Profissionais"
              value={filters.providers || []}
              onChange={(value) => handleFilterChange({ ...filters, providers: value })}
              options={providers}
              placeholder="Selecione os profissionais"
              loading={isLoading}
              moduleOverride="scheduler"
            />
            {providers.length === 0 && !isLoading && (
              <button
                className="absolute top-0 right-0 mt-1 mr-1 p-1 text-xs bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark text-module-scheduler-text dark:text-module-scheduler-text-dark rounded"
                onClick={() => {
                  console.log("[DEBUG] Adicionando profissionais de teste...");
                  const testProviders = [
                    { value: "test-provider-1", label: "Dr. Teste 1" },
                    { value: "test-provider-2", label: "Dr. Teste 2" },
                    { value: "test-provider-3", label: "Dr. Teste 3" }
                  ];
                  setProviders(testProviders);
                }}
              >
                Adicionar teste
              </button>
            )}
          </div>

          <MultiSelect
            label="Pacientes"
            value={filters.persons || []}
            onChange={(value) => handleFilterChange({ ...filters, persons: value })}
            options={persons}
            placeholder="Selecione os pacientes"
            loading={isLoading}
            moduleOverride="scheduler"
          />

          <MultiSelect
            label="Locais"
            value={filters.locations || []}
            onChange={(value) => handleFilterChange({ ...filters, locations: value })}
            options={locations}
            placeholder="Selecione os locais"
            loading={isLoading}
            moduleOverride="scheduler"
          />

          <MultiSelect
            label="Tipos de Serviço"
            value={filters.serviceTypes || []}
            onChange={(value) => handleFilterChange({ ...filters, serviceTypes: value })}
            options={serviceTypes}
            placeholder="Selecione os tipos"
            loading={isLoading}
            moduleOverride="scheduler"
          />
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-neutral-200 dark:border-gray-700 flex justify-between items-center">
        {/* Botão de debug para recarregar dados */}
        <FilterButton
          type="button"
          onClick={() => {
            console.log("[DEBUG] Forçando recarregamento de dados...");
            const loadData = async () => {
              try {
                setIsLoading(true);
                const providersData = await appointmentService.getProviders();
                console.log(`[DEBUG] Recarregados ${providersData?.length || 0} profissionais`);

                // Map providers to MultiSelect format
                const formattedProviders = (providersData || [])
                  .filter(p => p && p.id && p.fullName)
                  .map(provider => ({
                    value: provider.id,
                    label: provider.fullName
                  }));

                console.log("[DEBUG] Providers formatados:", formattedProviders);
                setProviders(formattedProviders);
              } catch (error) {
                console.error("[DEBUG] Erro ao recarregar dados:", error);
              } finally {
                setIsLoading(false);
              }
            };
            loadData();
          }}
          moduleColor="scheduler"
          variant="secondary"
        >
          <div className="flex items-center gap-2">
            <RefreshCw size={16} />
            <span>Recarregar Dados</span>
          </div>
        </FilterButton>

        <div className="flex items-center gap-2">
          <FilterButton
            type="button"
            onClick={handleClearFilters}
            moduleColor="scheduler"
            variant="secondary"
          >
            <div className="flex items-center gap-2">
              <RefreshCw size={16} />
              <span>Limpar Filtros</span>
            </div>
          </FilterButton>

          <FilterButton
            type="button"
            onClick={handleSearch}
            moduleColor="scheduler"
            variant="primary"
            disabled={!hasChanges}
            className={hasChanges ? '' : 'opacity-50 cursor-not-allowed'}
          >
            <div className="flex items-center gap-2">
              <Search size={16} />
              <span>Buscar</span>
            </div>
          </FilterButton>
        </div>
      </div>
    </div>
  );
};