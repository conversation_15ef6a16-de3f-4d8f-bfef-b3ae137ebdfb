// src/app/modules/aba/services/standardCriteriaService.js
import { api } from "@/utils/api";

export const standardCriteriaService = {
  // Get standard criteria with optional filters
  getStandardCriteria: async (filters = {}) => {
    const { page = 1, limit = 10, search = "", active } = filters;

    try {
      console.log("standardCriteriaService.getStandardCriteria - Enviando requisição com parâmetros:", {
        page,
        limit,
        search: search || undefined,
        active: active === undefined ? undefined : active
      });

      const response = await api.get("/aba/standard-criteria", {
        params: {
          page,
          limit,
          search: search || undefined,
          active: active === undefined ? undefined : active
        }
      });

      console.log("standardCriteriaService.getStandardCriteria - Resposta da API:", response.data);

      // Verificar a estrutura da resposta
      const items = response.data.standardCriteria || response.data.items || [];
      const total = response.data.total || 0;

      console.log("standardCriteriaService.getStandardCriteria - Itens extraídos:", items);

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching standard criteria:", error);
      throw error;
    }
  },

  // Get a specific standard criterion by ID
  getStandardCriterion: async (id) => {
    try {
      const response = await api.get(`/aba/standard-criteria/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching standard criterion ${id}:`, error);
      throw error;
    }
  },

  // Create a new standard criterion
  createStandardCriterion: async (criterionData) => {
    try {
      const response = await api.post("/aba/standard-criteria", criterionData);
      return response.data;
    } catch (error) {
      console.error("Error creating standard criterion:", error);
      throw error;
    }
  },

  // Update an existing standard criterion
  updateStandardCriterion: async (id, criterionData) => {
    try {
      const response = await api.put(`/aba/standard-criteria/${id}`, criterionData);
      return response.data;
    } catch (error) {
      console.error(`Error updating standard criterion ${id}:`, error);
      throw error;
    }
  },

  // Toggle the active status of a standard criterion
  toggleStandardCriterionStatus: async (id) => {
    try {
      const response = await api.patch(`/aba/standard-criteria/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling status of standard criterion ${id}:`, error);
      throw error;
    }
  },

  // Delete a standard criterion
  deleteStandardCriterion: async (id) => {
    try {
      const response = await api.delete(`/aba/standard-criteria/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting standard criterion ${id}:`, error);
      throw error;
    }
  },

  // Helper function to get teaching type label
  getTeachingTypeLabel: (type) => {
    const types = {
      DISCRETE_TRIAL_STRUCTURED: "Tentativa Discreta - Estruturada",
      TASK_ANALYSIS: "Análise de Tarefas",
      NATURALISTIC_TEACHING: "Ensino Naturalístico",
      DISCRETE_TRIAL_INTERSPERSED: "Tentativa Discreta - Intercalada"
    };
    return types[type] || type;
  },

  // Helper function to get degree label
  getDegreeLabel: (degree) => {
    const degrees = {
      OMISSION: "Omissão",
      ERROR: "Erro",
      MORE_INTRUSIVE: "Mais Intrusiva",
      PARTIALLY_INTRUSIVE: "Parcialmente Intrusiva",
      LESS_INTRUSIVE: "Menos Intrusiva",
      INDEPENDENT: "Independente"
    };
    return degrees[degree] || degree;
  },

  // Get teaching type options for select
  getTeachingTypeOptions: () => {
    return [
      { value: "DISCRETE_TRIAL_STRUCTURED", label: "Tentativa Discreta - Estruturada" },
      { value: "TASK_ANALYSIS", label: "Análise de Tarefas" },
      { value: "NATURALISTIC_TEACHING", label: "Ensino Naturalístico" },
      { value: "DISCRETE_TRIAL_INTERSPERSED", label: "Tentativa Discreta - Intercalada" }
    ];
  },

  // Get degree options for select
  getDegreeOptions: () => {
    return [
      { value: "OMISSION", label: "Omissão" },
      { value: "ERROR", label: "Erro" },
      { value: "MORE_INTRUSIVE", label: "Mais Intrusiva" },
      { value: "PARTIALLY_INTRUSIVE", label: "Parcialmente Intrusiva" },
      { value: "LESS_INTRUSIVE", label: "Menos Intrusiva" },
      { value: "INDEPENDENT", label: "Independente" }
    ];
  }
};

export default standardCriteriaService;
