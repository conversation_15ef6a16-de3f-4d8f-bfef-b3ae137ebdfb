// components/calendar/AppointmentEventContentEnhanced.js
import React from 'react';
import { APPOINTMENT_STATUS } from '../utils/appointmentConstants';
import { CreditCard, AlertCircle } from 'lucide-react';

const AppointmentEventContentEnhanced = ({ eventInfo, isDarkMode }) => {
  const event = eventInfo.event;
  const status = event.extendedProps.status || "PENDING";
  const statusConfig = APPOINTMENT_STATUS[status];

  // Extrair informações de limite de convênio
  const insurance = event.extendedProps.insurance || {};
  const hasLimitWarning = insurance.limitWarning;

  // Se for visualização de mês, renderiza versão compacta
  if (eventInfo.view.type === "dayGridMonth") {
    return (
      <div
        className="flex flex-col w-full h-full relative"
        style={{
          backgroundColor: isDarkMode ? statusConfig.darkColor : statusConfig.color,
          border: "none",
          color: "#ffffff",
        }}
      >
        {/* Prioridade 1: Paciente */}
        <div className="flex items-baseline gap-1 p-1">
          <span className="text-xs font-medium">{event.extendedProps.personfullName}</span>
        </div>

        {/* Prioridade 2: Profissional */}
        {event.extendedProps.providerfullName && (
          <div className="flex items-baseline gap-1 px-1 -mt-1">
            <span className="text-[10px] font-medium opacity-90">{event.extendedProps.providerfullName}</span>
          </div>
        )}

        {/* Prioridade 3: Horário */}
        <div className="flex items-baseline gap-1 px-1 mt-0.5">
          <span className="text-xs font-medium">{eventInfo.timeText}</span>
        </div>

        {/* Prioridade 4: Status */}
        <div className="flex gap-1 px-1 pb-1 mt-0.5">
          <span className="text-xs px-1 rounded bg-white bg-opacity-20">
            {statusConfig.label}
          </span>

          {/* Prioridade 5: Serviço (Indicador de convênio) */}
          {insurance.name && (
            <span className="text-xs px-1 rounded flex items-center gap-1 bg-white bg-opacity-20">
              <CreditCard size={10} />
              {insurance.name}
            </span>
          )}
        </div>

        {/* Indicador de alerta de limite */}
        {hasLimitWarning && (
          <div className="absolute top-0 right-0 p-0.5 bg-red-500 rounded-full transform translate-x-1 -translate-y-1">
            <AlertCircle size={12} className="text-white" />
          </div>
        )}
      </div>
    );
  }

  // Visualização semanal ou diária - mais detalhes
  return (
    <div className="h-full flex flex-col p-2 relative">
      {/* Prioridade 1: Paciente */}
      <div className="text-xs font-medium mb-2">{event.extendedProps.personfullName}</div>

      {/* Prioridade 2: Profissional */}
      <div className="text-xs font-medium mb-1">{event.extendedProps.providerfullName}</div>

      {/* Prioridade 3: Horário */}
      <div className="text-sm font-semibold mb-1 opacity-90">
        {eventInfo.timeText}
      </div>

      {/* Prioridade 4: Status */}
      <div className="flex items-center gap-2 text-xs mb-1">
        <span className="px-2 py-1 rounded-full bg-opacity-20 bg-white font-medium">
          {statusConfig.label}
        </span>
      </div>

      {/* Prioridade 5: Serviço */}
      <div className="text-xs font-medium">{event.extendedProps.serviceTypefullName}</div>

      {/* Informações do convênio - versão detalhada */}
      {insurance.name && (
        <div className="mt-1 flex items-center gap-1 text-xs">
          <CreditCard size={12} />
          <span>{insurance.name}</span>

          {/* Mostrar uso do limite se disponível */}
          {insurance.usage && (
            <span className={`ml-1 px-1 py-0.5 rounded-full text-xs ${hasLimitWarning ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
              {insurance.usage}
            </span>
          )}
        </div>
      )}

      {/* Prioridade 6: Título (Descrição) */}
      {event.extendedProps.description && (
        <div className="text-xs mt-1 opacity-75 truncate">
          {event.extendedProps.description}
        </div>
      )}

      {/* Indicador de alerta de limite - versão detalhada */}
      {hasLimitWarning && (
        <div className="absolute top-2 right-2 bg-red-500 dark:bg-red-600 text-white rounded-full p-1">
          <AlertCircle size={14} />
        </div>
      )}
    </div>
  );
};

export default AppointmentEventContentEnhanced;