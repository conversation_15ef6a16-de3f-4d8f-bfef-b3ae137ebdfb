import React from 'react';
import ModalButton from '@/components/ui/ModalButton';

const ModuleModalFooter = ({
  isEditMode,
  canDelete,
  hasPermission,
  formData,
  isLoading,
  limitInfo,
  handleDelete,
  onClose
}) => {
  // Verificar se o limite mensal foi atingido
  const isLimitReached = limitInfo &&
    (limitInfo.monthly && !limitInfo.monthly.unlimited && limitInfo.monthly.used >= limitInfo.monthly.limit);

  // Em modo de edição, permitir atualização mesmo com limite atingido
  const shouldDisableButton = isLimitReached && !isEditMode;

  return (
    <div className="flex justify-between gap-2">
      <div className="flex gap-2">
        {/* Botão de exclusão - visível apenas para edição e com permissão */}
        {isEditMode && canDelete && (
          <ModalButton
            type="button"
            onClick={handleDelete}
            variant="danger"
            moduleColor="scheduler"
            isLoading={isLoading && formData.id}
            disabled={isLoading}
          >
            {isLoading && formData.id ? "Excluindo..." : "Excluir"}
          </ModalButton>
        )}
      </div>

      <div className="flex gap-2 ml-auto">
        <ModalButton
          type="button"
          onClick={(e) => {
            // Prevenir propagação do evento
            e.preventDefault();
            e.stopPropagation();

            if (onClose) {
              onClose();
            }
          }}
          variant="secondary"
          moduleColor="scheduler"
          disabled={isLoading}
        >
          Cancelar
        </ModalButton>

        {/* Botão de salvar - visível apenas com permissão */}
        {hasPermission && (
          <ModalButton
            type="button"
            onClick={() => {
              // Encontrar o formulário e disparar o evento submit
              const form = document.getElementById('appointment-form');
              if (form) {
                const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                form.dispatchEvent(submitEvent);
              }
            }}
            variant="primary"
            moduleColor="scheduler"
            isLoading={isLoading && !formData.id}
            disabled={
              isLoading ||
              (formData.recurrence.enabled &&
                formData.recurrence.patterns.length === 0) ||
              !hasPermission ||
              shouldDisableButton
            }
          >
            {isLoading && !formData.id
              ? "Salvando..."
              : isEditMode
                ? "Atualizar"
                : formData.recurrence.enabled
                  ? "Criar Recorrência"
                  : "Criar"}
          </ModalButton>
        )}
      </div>
    </div>
  );
};

export default ModuleModalFooter;
