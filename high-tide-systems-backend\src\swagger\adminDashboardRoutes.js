// src/swagger/adminDashboardRoutes.js

/**
 * @swagger
 * tags:
 *   name: Dashboard Administrativo
 *   description: Estatísticas e informações para o dashboard administrativo
 */

/**
 * @swagger
 * /adminDashboard/all:
 *   get:
 *     summary: Obtém todos os dados do dashboard administrativo
 *     description: |
 *       Retorna todos os dados necessários para o dashboard administrativo em uma única requisição.
 *       Inclui estatísticas gerais, distribuição de módulos, usuários ativos, atividades recentes e informações do sistema.
 *     tags: [Dashboard Administrativo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dados do dashboard obtidos com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 stats:
 *                   type: object
 *                   properties:
 *                     usersTotal:
 *                       type: integer
 *                       description: Total de usuários ativos no sistema
 *                     activeUsers:
 *                       type: integer
 *                       description: Usuários que fizeram login nas últimas 24 horas
 *                     clientsTotal:
 *                       type: integer
 *                       description: Total de clientes ativos
 *                     appointmentsTotal:
 *                       type: integer
 *                       description: Total de agendamentos
 *                 growth:
 *                   type: object
 *                   properties:
 *                     users:
 *                       type: object
 *                       properties:
 *                         current:
 *                           type: integer
 *                           description: Usuários no período atual
 *                         previous:
 *                           type: integer
 *                           description: Usuários no período anterior
 *                         percentage:
 *                           type: number
 *                           format: float
 *                           description: Percentual de crescimento
 *                     clients:
 *                       type: object
 *                       properties:
 *                         current:
 *                           type: integer
 *                         previous:
 *                           type: integer
 *                         percentage:
 *                           type: number
 *                           format: float
 *                     appointments:
 *                       type: object
 *                       properties:
 *                         current:
 *                           type: integer
 *                         previous:
 *                           type: integer
 *                         percentage:
 *                           type: number
 *                           format: float
 *                 activity:
 *                   type: object
 *                   properties:
 *                     logins:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           date:
 *                             type: string
 *                             format: date
 *                           count:
 *                             type: integer
 *                     registrations:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           date:
 *                             type: string
 *                             format: date
 *                           count:
 *                             type: integer
 *                 moduleDistribution:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       module:
 *                         type: string
 *                         description: Nome do módulo
 *                       count:
 *                         type: integer
 *                         description: Quantidade de usuários com acesso ao módulo
 *                       percentage:
 *                         type: number
 *                         format: float
 *                         description: Percentual do total de usuários
 *                 activeUsers:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       fullName:
 *                         type: string
 *                       email:
 *                         type: string
 *                         format: email
 *                       lastLoginAt:
 *                         type: string
 *                         format: date-time
 *                       modules:
 *                         type: array
 *                         items:
 *                           type: string
 *                 recentActivity:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       action:
 *                         type: string
 *                       entityType:
 *                         type: string
 *                       entityId:
 *                         type: string
 *                       userId:
 *                         type: string
 *                         format: uuid
 *                       userName:
 *                         type: string
 *                       timestamp:
 *                         type: string
 *                         format: date-time
 *                       details:
 *                         type: object
 *                 systemInfo:
 *                   type: object
 *                   properties:
 *                     version:
 *                       type: string
 *                     uptime:
 *                       type: number
 *                     environment:
 *                       type: string
 *                     nodeVersion:
 *                       type: string
 *                     memoryUsage:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: number
 *                         free:
 *                           type: number
 *                         used:
 *                           type: number
 *                         percentUsed:
 *                           type: number
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Acesso negado - Usuário não tem permissão para acessar o dashboard administrativo
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /adminDashboard/stats:
 *   get:
 *     summary: Obtém estatísticas gerais
 *     description: Retorna estatísticas gerais para o dashboard administrativo
 *     tags: [Dashboard Administrativo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Estatísticas obtidas com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 usersTotal:
 *                   type: integer
 *                   description: Total de usuários ativos no sistema
 *                 activeUsers:
 *                   type: integer
 *                   description: Usuários que fizeram login nas últimas 24 horas
 *                 clientsTotal:
 *                   type: integer
 *                   description: Total de clientes ativos
 *                 appointmentsTotal:
 *                   type: integer
 *                   description: Total de agendamentos
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Acesso negado - Usuário não tem permissão para acessar o dashboard administrativo
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /adminDashboard/activity:
 *   get:
 *     summary: Obtém dados de atividade
 *     description: Retorna dados de atividade do sistema para o dashboard administrativo
 *     tags: [Dashboard Administrativo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Dados de atividade obtidos com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 logins:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                       count:
 *                         type: integer
 *                 registrations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         format: date
 *                       count:
 *                         type: integer
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Acesso negado - Usuário não tem permissão para acessar o dashboard administrativo
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /adminDashboard/module-distribution:
 *   get:
 *     summary: Obtém distribuição de módulos
 *     description: Retorna a distribuição de usuários por módulo de acesso
 *     tags: [Dashboard Administrativo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Distribuição de módulos obtida com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   module:
 *                     type: string
 *                     description: Nome do módulo
 *                   count:
 *                     type: integer
 *                     description: Quantidade de usuários com acesso ao módulo
 *                   percentage:
 *                     type: number
 *                     format: float
 *                     description: Percentual do total de usuários
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Acesso negado - Usuário não tem permissão para acessar o dashboard administrativo
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /adminDashboard/active-users:
 *   get:
 *     summary: Obtém usuários ativos
 *     description: Retorna a lista de usuários ativos recentemente no sistema
 *     tags: [Dashboard Administrativo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de usuários ativos obtida com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     format: uuid
 *                   fullName:
 *                     type: string
 *                   email:
 *                     type: string
 *                     format: email
 *                   lastLoginAt:
 *                     type: string
 *                     format: date-time
 *                   modules:
 *                     type: array
 *                     items:
 *                       type: string
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Acesso negado - Usuário não tem permissão para acessar o dashboard administrativo
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /adminDashboard/recent-activity:
 *   get:
 *     summary: Obtém atividades recentes
 *     description: Retorna as atividades recentes registradas no sistema
 *     tags: [Dashboard Administrativo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de atividades recentes obtida com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     format: uuid
 *                   action:
 *                     type: string
 *                   entityType:
 *                     type: string
 *                   entityId:
 *                     type: string
 *                   userId:
 *                     type: string
 *                     format: uuid
 *                   userName:
 *                     type: string
 *                   timestamp:
 *                     type: string
 *                     format: date-time
 *                   details:
 *                     type: object
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Acesso negado - Usuário não tem permissão para acessar o dashboard administrativo
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /adminDashboard/system-info:
 *   get:
 *     summary: Obtém informações do sistema
 *     description: Retorna informações técnicas sobre o sistema
 *     tags: [Dashboard Administrativo]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Informações do sistema obtidas com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 version:
 *                   type: string
 *                 uptime:
 *                   type: number
 *                 environment:
 *                   type: string
 *                 nodeVersion:
 *                   type: string
 *                 memoryUsage:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: number
 *                     free:
 *                       type: number
 *                     used:
 *                       type: number
 *                     percentUsed:
 *                       type: number
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Acesso negado - Usuário não tem permissão para acessar o dashboard administrativo
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */
