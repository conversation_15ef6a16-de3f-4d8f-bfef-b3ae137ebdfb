'use client';

import './globals.css';
import { AuthProvider } from '@/contexts/AuthContext';
import { PermissionProvider } from '@/contexts/PermissionContext';
import { ToastProvider } from '@/contexts/ToastContext';
import { ThemeProvider } from '@/contexts/ThemeContext';
import { ToastContainer } from '@/components/ui/ToastContainer';
import { TutorialProvider } from '@/contexts/TutorialContext';
import { ConstructionProvider } from '@/contexts/ConstructionContext';
import { QuickNavProvider } from '@/contexts/QuickNavContext';
import { ChatProvider } from '@/contexts/ChatContext';
import ConstructionMessage from '@/components/construction/ConstructionMessage';
import ModuleScrollbar from '@/components/ui/ModuleScrollbar';
import ScrollbarInlineStyles from '@/styles/scrollbar-inline';
import QuickNav from '@/components/navigation/QuickNav';
import { ChatPanel, ChatModal } from '@/components/chat';

export default function RootLayout({ children }) {
  return (
    <html lang="pt-BR">
      <body className="bg-background text-foreground transition-colors custom-scrollbar">
        <ThemeProvider>
          <AuthProvider>
            <PermissionProvider>
              <ToastProvider>
                <TutorialProvider>
                  <ConstructionProvider>
                    <QuickNavProvider>
                      <ChatProvider>
                        {children}
                        <ToastContainer />
                        <ConstructionMessage />
                        <ModuleScrollbar />
                        <ScrollbarInlineStyles />
                        <QuickNav />
                        <ChatPanel />
                        <ChatModal />
                      </ChatProvider>
                    </QuickNavProvider>
                  </ConstructionProvider>
                </TutorialProvider>
              </ToastProvider>
            </PermissionProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}