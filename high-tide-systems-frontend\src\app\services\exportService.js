// services/exportService.js
import { saveAs } from "file-saver";
import { utils, write } from "xlsx";
import { jsPDF } from "jspdf";
import autoTable from "jspdf-autotable"; // Alterado para importar como função
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { api } from "@/utils/api";
import html2canvas from "html2canvas";

// Formatação para campos comuns
const formatters = {
  // Formata datas para o padrão brasileiro
  date: (value) => {
    if (!value) return "";
    try {
      return format(new Date(value), "dd/MM/yyyy", { locale: ptBR });
    } catch (error) {
      return value;
    }
  },

  // Formata CPF (000.000.000-00)
  cpf: (value) => {
    if (!value) return "";
    const cpfNumbers = value.replace(/\D/g, "");
    return cpfNumbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  },

  // Formata telefone (00) 00000-0000
  phone: (value) => {
    if (!value) return "";
    const phoneNumbers = value.replace(/\D/g, "");
    return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  },

  // Formata valores booleanos
  boolean: (value, options = { trueText: "Sim", falseText: "Não" }) => {
    return value ? options.trueText : options.falseText;
  },

  // Formata valores monetários
  currency: (value) => {
    if (value === null || value === undefined) return "";
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL",
    }).format(value);
  }
};

export const exportService = {
  /**
   * Exporta dados para XLSX ou PDF
   * @param {Object[]|Object} data - Dados a serem exportados (array de objetos para tabela única ou objeto com arrays para múltiplas tabelas)
   * @param {Object} options - Opções de exportação
   * @param {string} options.format - Formato de exportação ('xlsx', 'pdf' ou 'image')
   * @param {string} options.filename - Nome do arquivo sem extensão
   * @param {Object[]} options.columns - Definição das colunas (para tabela única)
   * @param {Object} options.formatOptions - Opções adicionais de formatação
   * @param {string} options.title - Título principal do documento
   * @param {string} options.subtitle - Subtítulo do documento
   * @param {boolean} options.multiTable - Indica se os dados contêm múltiplas tabelas
   * @param {Object[]} options.tables - Definição das tabelas para exportação múltipla
   * @param {string} options.tables[].name - Nome da propriedade no objeto data que contém os dados da tabela
   * @param {string} options.tables[].title - Título da tabela
   * @param {Object[]} options.tables[].columns - Definição das colunas para esta tabela
   * @returns {Promise<boolean>} - Sucesso da exportação
   */
  exportData: async (data, options = {}) => {
    try {
      const {
        format = "xlsx",
        filename = "export",
        columns = [],
        formatOptions = {},
        title = null,
        subtitle = null,
        multiTable = false,
        tables = []
      } = options;

      // Verifica se estamos lidando com múltiplas tabelas
      if (multiTable && !Array.isArray(data)) {
        // Caso de múltiplas tabelas (objeto com arrays)

        if (format === "xlsx") {
          // Criar um workbook para múltiplas tabelas
          const workbook = utils.book_new();

          // Data atual formatada para o cabeçalho
          const currentDate = new Date();
          const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

          // Processar cada tabela definida
          for (const table of tables) {
            const tableData = data[table.name];

            if (!tableData || !Array.isArray(tableData)) {
              console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);
              continue;
            }

            // Formatar os dados desta tabela
            const formattedTableData = tableData.map(item => {
              const formattedItem = {};

              table.columns.forEach(col => {
                let value = item[col.key];

                // Aplica formatação personalizada se especificada
                if (col.format && typeof col.format === 'function') {
                  formattedItem[col.key] = col.format(value, item);
                }
                // Aplica formatação padrão se especificada
                else if (col.type && formatters[col.type]) {
                  formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);
                }
                // Sem formatação
                else {
                  formattedItem[col.key] = value !== null && value !== undefined ? value : '';
                }
              });

              return formattedItem;
            });

            // Preparar dados para a worksheet
            let worksheetData = [];

            // Adiciona título e subtítulo se existirem
            if (title) {
              worksheetData.push([title]);
              if (table.title) {
                worksheetData.push([table.title]);
              }
              worksheetData.push([`Gerado em: ${formattedDate}`]);
              if (subtitle) {
                worksheetData.push([subtitle]);
              }
              worksheetData.push([]); // Linha em branco
            }

            // Adiciona os cabeçalhos
            const headers = table.columns.map(col => col.header || col.key);
            worksheetData.push(headers);

            // Adiciona os dados
            formattedTableData.forEach(item => {
              const row = table.columns.map(col => item[col.key]);
              worksheetData.push(row);
            });

            // Cria a worksheet
            const worksheet = utils.aoa_to_sheet(worksheetData);

            // Configura os estilos
            if (title) {
              // Mescla células para o título
              worksheet['!merges'] = [
                { s: {r: 0, c: 0}, e: {r: 0, c: headers.length - 1} }, // Título
              ];

              if (table.title) {
                worksheet['!merges'].push(
                  { s: {r: 1, c: 0}, e: {r: 1, c: headers.length - 1} } // Subtítulo da tabela
                );
                worksheet['!merges'].push(
                  { s: {r: 2, c: 0}, e: {r: 2, c: headers.length - 1} } // Data
                );
              } else {
                worksheet['!merges'].push(
                  { s: {r: 1, c: 0}, e: {r: 1, c: headers.length - 1} } // Data
                );
              }

              if (subtitle) {
                worksheet['!merges'].push(
                  { s: {r: table.title ? 3 : 2, c: 0}, e: {r: table.title ? 3 : 2, c: headers.length - 1} } // Subtítulo
                );
              }

              // Ajusta largura das colunas
              if (!worksheet['!cols']) worksheet['!cols'] = [];
              table.columns.forEach((col, idx) => {
                // Calcula largura ideal para cada coluna
                const headerWidth = (col.header || col.key).length * 1.2;
                let maxDataWidth = 0;

                // Verifica o tamanho máximo dos dados em cada coluna
                formattedTableData.forEach(item => {
                  const cellValue = item[col.key];
                  const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';
                  maxDataWidth = Math.max(maxDataWidth, cellText.length);
                });

                worksheet['!cols'][idx] = {
                  wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))
                };
              });
            }

            // Limita o nome da planilha a 31 caracteres (limite do Excel)
            let sheetName = table.title || table.name;

            // Se o nome for muito longo, cria um nome curto
            if (sheetName.length > 31) {
              // Tenta usar apenas a primeira palavra do título
              const firstWord = sheetName.split(' ')[0];
              if (firstWord.length <= 28) {
                sheetName = firstWord + "...";
              } else {
                // Se ainda for muito longo, trunca para 28 caracteres e adiciona "..."
                sheetName = sheetName.substring(0, 28) + "...";
              }
            }

            // Adiciona a worksheet ao workbook
            utils.book_append_sheet(workbook, worksheet, sheetName);
          }

          try {
            // Verifica se há pelo menos uma planilha no workbook
            if (workbook.SheetNames && workbook.SheetNames.length > 0) {
              // Verifica se todos os nomes de planilhas estão dentro do limite
              let allNamesValid = true;
              for (const sheetName of workbook.SheetNames) {
                if (sheetName.length > 31) {
                  console.error(`Nome de planilha muito longo: "${sheetName}" (${sheetName.length} caracteres)`);
                  allNamesValid = false;
                  break;
                }
              }

              if (!allNamesValid) {
                // Criar uma planilha padrão com mensagem de erro
                workbook.SheetNames = []; // Limpa as planilhas existentes
                workbook.Sheets = {};     // Limpa as planilhas existentes

                const worksheet = utils.aoa_to_sheet([
                  ["Erro na exportação"],
                  ["Um ou mais nomes de planilhas excedem o limite de 31 caracteres"],
                  ["Por favor, contate o suporte técnico"]
                ]);

                utils.book_append_sheet(workbook, worksheet, "Erro");
              }

              // Converte para binário e salva
              const excelBuffer = write(workbook, {
                bookType: "xlsx",
                type: "array",
                bookSST: false,
                compression: true
              });

              const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
              saveAs(blob, `${filename}.xlsx`);

              return allNamesValid;
            } else {
              console.error("Nenhuma planilha foi criada no workbook");

              // Criar uma planilha padrão com mensagem de erro
              const worksheet = utils.aoa_to_sheet([
                ["Erro na exportação"],
                ["Não foi possível gerar as planilhas com os dados fornecidos"],
                ["Por favor, tente novamente ou contate o suporte"]
              ]);

              utils.book_append_sheet(workbook, worksheet, "Erro");

              const excelBuffer = write(workbook, {
                bookType: "xlsx",
                type: "array",
                bookSST: false,
                compression: true
              });

              const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
              saveAs(blob, `${filename}.xlsx`);

              return false;
            }
          } catch (error) {
            console.error("Erro ao gerar arquivo Excel:", error);

            try {
              // Tentar criar um arquivo de erro
              const workbook = utils.book_new();
              const worksheet = utils.aoa_to_sheet([
                ["Erro na exportação"],
                ["Ocorreu um erro ao gerar o arquivo Excel"],
                ["Detalhes do erro: " + (error.message || "Erro desconhecido")],
                ["Por favor, tente novamente ou contate o suporte"]
              ]);

              utils.book_append_sheet(workbook, worksheet, "Erro");

              const excelBuffer = write(workbook, {
                bookType: "xlsx",
                type: "array",
                bookSST: false,
                compression: true
              });

              const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
              saveAs(blob, `${filename}-erro.xlsx`);
            } catch (fallbackError) {
              console.error("Erro ao gerar arquivo de erro:", fallbackError);
            }

            return false;
          }
        }
        // Exportação em formato PDF para múltiplas tabelas
        else if (format === "pdf") {
          // Cria um novo documento PDF no tamanho A4
          const doc = new jsPDF({
            orientation: "portrait",
            unit: "mm",
            format: "a4"
          });

          // Configurações da página
          const pageWidth = doc.internal.pageSize.width;
          const pageHeight = doc.internal.pageSize.height;
          const margin = 10;

          // Define cores do tema
          const themeColors = {
            primary: {
              light: [255, 153, 51],    // #FF9933 (primary-500)
              dark: [255, 127, 0],       // #FF7F00 (primary-600)
              text: [255, 255, 255]      // #FFFFFF (texto branco)
            },
            secondary: {
              light: [255, 237, 213],    // #FFEDD5 (orange-100)
              dark: [154, 52, 18],       // #9A3412 (orange-800)
              border: [251, 146, 60]     // #FB923C (orange-400)
            }
          };

          // Desenha cabeçalho com título principal
          const headerHeight = subtitle ? 30 : 25;

          // Simulando um gradiente com múltiplos retângulos coloridos
          const gradientSteps = 20;
          const stepHeight = headerHeight / gradientSteps;

          for (let i = 0; i < gradientSteps; i++) {
            const ratio = i / gradientSteps;
            // Interpola as cores para criar efeito de gradiente
            const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);
            const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);
            const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);

            doc.setFillColor(r, g, b);
            doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');
          }

          // Adiciona título no cabeçalho
          if (title) {
            doc.setFont("helvetica", "bold");
            doc.setFontSize(18);
            doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho
            doc.text(title, pageWidth / 2, 15, { align: "center" });
          }

          // Adiciona subtítulo no cabeçalho se existir
          if (subtitle) {
            doc.setFont("helvetica", "normal");
            doc.setFontSize(10);
            doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência
            doc.text(subtitle, pageWidth / 2, 22, { align: "center" });
          }

          // Data de geração do relatório
          const currentDate = new Date();
          const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

          doc.setFontSize(8);
          doc.text(`Gerado em: ${formattedDate}`, pageWidth - margin - 2, headerHeight - 3, { align: "right" });

          // Posição vertical atual para adicionar tabelas
          let yPosition = headerHeight + 10;

          // Processar cada tabela
          for (let tableIndex = 0; tableIndex < tables.length; tableIndex++) {
            const table = tables[tableIndex];
            const tableData = data[table.name];

            if (!tableData || !Array.isArray(tableData)) {
              console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);
              continue;
            }

            // Formatar os dados desta tabela
            const formattedTableData = tableData.map(item => {
              const formattedItem = {};

              table.columns.forEach(col => {
                let value = item[col.key];

                // Aplica formatação personalizada se especificada
                if (col.format && typeof col.format === 'function') {
                  formattedItem[col.key] = col.format(value, item);
                }
                // Aplica formatação padrão se especificada
                else if (col.type && formatters[col.type]) {
                  formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);
                }
                // Sem formatação
                else {
                  formattedItem[col.key] = value !== null && value !== undefined ? value : '';
                }
              });

              return formattedItem;
            });

            // Adiciona título da tabela
            if (table.title) {
              // Verifica se precisa adicionar uma nova página
              if (yPosition > pageHeight - 40) {
                doc.addPage();
                yPosition = 20;
              }

              doc.setFont("helvetica", "bold");
              doc.setFontSize(14);
              doc.setTextColor(50, 50, 50);
              doc.text(table.title, margin, yPosition);
              yPosition += 10;
            }

            // Prepara os dados para a tabela
            const headers = table.columns.map(col => col.header || col.key);
            const rows = formattedTableData.map(item => {
              return table.columns.map(col => item[col.key]);
            });

            // Prepara cabeçalhos mais curtos para prevenir quebras
            const shortHeaders = headers.map(header => {
              // Substituições específicas para cabeçalhos problemáticos
              const replacements = {
                'Nome Completo': 'Nome',
                'Data de Nascimento': 'Nascimento',
                'Data de Cadastro': 'Cadastro',
                'Relacionamento': 'Relação',
                'Telefone': 'Telefone'
              };

              return replacements[header] || header;
            });

            // Adiciona a tabela
            autoTable(doc, {
              startY: yPosition,
              head: [shortHeaders],
              body: rows,
              theme: "grid",
              headStyles: {
                fillColor: themeColors.primary.dark,
                textColor: themeColors.primary.text,
                fontStyle: "bold",
                halign: "center",
                fontSize: 9,
                cellPadding: { top: 3, right: 2, bottom: 3, left: 2 },
                lineWidth: 0.1,
                minCellWidth: 15,
                overflow: 'linebreak'
              },
              styles: {
                fontSize: 9,
                cellPadding: { top: 2, right: 2, bottom: 2, left: 2 },
                overflow: "linebreak",
                lineColor: [220, 220, 220],
                lineWidth: 0.1
              },
              tableWidth: 'auto',
              bodyStyles: {
                minCellHeight: 10
              },
              alternateRowStyles: {
                fillColor: [252, 252, 252]
              },
              columnStyles: table.columns.reduce((styles, col, index) => {
                // Definir larguras mínimas específicas para evitar quebras nos títulos
                const minWidths = {
                  'fullName': 30,
                  'cpf': 20,
                  'email': 28,
                  'phone': 20,
                  'birthDate': 20,
                  'gender': 15,
                  'relationship': 20,
                  'createdAt': 20,
                  'active': 15
                };

                // Define a largura baseada na configuração ou no mínimo predefinido
                if (col.key && minWidths[col.key]) {
                  styles[index] = {
                    ...styles[index],
                    cellWidth: minWidths[col.key],
                    overflow: 'linebreak'
                  };
                } else if (col.width) {
                  styles[index] = {
                    ...styles[index],
                    cellWidth: col.width
                  };
                }

                // Aplica o alinhamento se definido
                if (col.align) {
                  styles[index] = {
                    ...styles[index],
                    halign: col.align
                  };
                }

                return styles;
              }, {}),
              margin: { top: yPosition, left: margin, right: margin, bottom: margin + 15 },
              didDrawPage: function(data) {
                // Adiciona rodapé colorido em cada página
                doc.setFillColor(240, 240, 240);
                doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');

                // Linha sutil acima do rodapé
                doc.setDrawColor(200, 200, 200);
                doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);

                // Adiciona numeração de páginas no rodapé
                doc.setFontSize(8);
                doc.setTextColor(100, 100, 100);
                doc.text(
                  `Página ${data.pageNumber} de ${data.pageCount}`,
                  pageWidth - margin - 2,
                  pageHeight - 5,
                  { align: "right" }
                );

                // Adiciona nome do sistema no rodapé
                doc.setTextColor(80, 80, 80);
                doc.setFontSize(8);
                doc.text("High Tide Systems", margin + 2, pageHeight - 5);
              }
            });

            // Atualiza a posição Y para a próxima tabela
            // Obtém a última posição Y após desenhar a tabela
            yPosition = doc.lastAutoTable.finalY + 15;
          }

          // Salva o documento
          doc.save(`${filename}.pdf`);

          return true;
        }
        // Exportação em formato de imagem (PNG) para múltiplas tabelas
        else if (format === "image") {
          try {
            // Criar um elemento temporário para renderizar todas as tabelas
            const tempContainer = document.createElement('div');
            tempContainer.style.position = 'absolute';
            tempContainer.style.left = '-9999px';
            tempContainer.style.top = '-9999px';
            tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade
            tempContainer.style.backgroundColor = '#ffffff';
            tempContainer.style.padding = '20px';
            tempContainer.style.fontFamily = 'Arial, sans-serif';
            tempContainer.style.color = '#333333';
            tempContainer.style.boxSizing = 'border-box';

            // Adicionar título principal e subtítulo
            if (title) {
              const titleElement = document.createElement('h1');
              titleElement.textContent = title;
              titleElement.style.color = '#FF7F00'; // Cor laranja do tema
              titleElement.style.marginBottom = '5px';
              titleElement.style.fontSize = '28px';
              titleElement.style.fontWeight = 'bold';
              titleElement.style.textAlign = 'center';
              tempContainer.appendChild(titleElement);
            }

            if (subtitle) {
              const subtitleElement = document.createElement('p');
              subtitleElement.textContent = subtitle;
              subtitleElement.style.color = '#666666';
              subtitleElement.style.marginBottom = '20px';
              subtitleElement.style.fontSize = '16px';
              subtitleElement.style.textAlign = 'center';
              tempContainer.appendChild(subtitleElement);
            }

            // Data atual formatada
            const currentDate = new Date();
            const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

            const dateElement = document.createElement('p');
            dateElement.textContent = `Gerado em: ${formattedDate}`;
            dateElement.style.color = '#666666';
            dateElement.style.marginBottom = '30px';
            dateElement.style.fontSize = '12px';
            dateElement.style.textAlign = 'center';
            tempContainer.appendChild(dateElement);

            // Processar cada tabela
            for (const table of tables) {
              const tableData = data[table.name];

              if (!tableData || !Array.isArray(tableData)) {
                console.warn(`Dados para tabela ${table.name} não encontrados ou não são um array`);
                continue;
              }

              // Adicionar título da seção
              const sectionTitle = document.createElement('h2');
              sectionTitle.textContent = table.title || table.name;
              sectionTitle.style.color = '#FF7F00'; // Cor laranja do tema
              sectionTitle.style.marginTop = '30px';
              sectionTitle.style.marginBottom = '15px';
              sectionTitle.style.fontSize = '20px';
              sectionTitle.style.fontWeight = 'bold';
              tempContainer.appendChild(sectionTitle);

              // Criar a tabela
              const tableElement = document.createElement('table');
              tableElement.style.width = '100%';
              tableElement.style.borderCollapse = 'collapse';
              tableElement.style.marginBottom = '30px';
              tableElement.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';

              // Criar o cabeçalho da tabela
              const thead = document.createElement('thead');
              const headerRow = document.createElement('tr');
              headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema
              headerRow.style.color = '#ffffff';

              table.columns.forEach(col => {
                const th = document.createElement('th');
                th.textContent = col.header || col.key;
                th.style.padding = '10px';
                th.style.textAlign = col.align || 'left';
                th.style.fontWeight = 'bold';
                th.style.fontSize = '14px';
                th.style.borderBottom = '2px solid #FF9933';
                headerRow.appendChild(th);
              });

              thead.appendChild(headerRow);
              tableElement.appendChild(thead);

              // Criar o corpo da tabela
              const tbody = document.createElement('tbody');

              tableData.forEach((item, rowIndex) => {
                const row = document.createElement('tr');
                row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                row.style.borderBottom = '1px solid #eeeeee';

                table.columns.forEach(col => {
                  const td = document.createElement('td');

                  // Formatar o valor da célula
                  let value = item[col.key];

                  // Aplica formatação personalizada se especificada
                  if (col.format && typeof col.format === 'function') {
                    value = col.format(value, item);
                  }
                  // Aplica formatação padrão se especificada
                  else if (col.type && formatters[col.type]) {
                    value = formatters[col.type](value, {});
                  }

                  td.textContent = value !== undefined && value !== null ? value : '';
                  td.style.padding = '8px 10px';
                  td.style.fontSize = '13px';
                  td.style.textAlign = col.align || 'left';

                  // Estilização especial para status
                  if (col.key === 'active' || col.key === 'status') {
                    if (td.textContent === 'Ativo') {
                      td.style.color = '#10B981'; // Verde para ativo
                      td.style.fontWeight = 'bold';
                    } else if (td.textContent === 'Inativo') {
                      td.style.color = '#DC2626'; // Vermelho para inativo
                      td.style.fontWeight = 'bold';
                    } else if (td.textContent === 'Crítica') {
                      td.style.color = '#DC2626'; // Vermelho para crítica
                      td.style.fontWeight = 'bold';
                    } else if (td.textContent === 'Alta') {
                      td.style.color = '#F59E0B'; // Âmbar para alta
                      td.style.fontWeight = 'bold';
                    } else if (td.textContent === 'Média') {
                      td.style.color = '#10B981'; // Verde para média
                      td.style.fontWeight = 'bold';
                    } else if (td.textContent === 'Baixa') {
                      td.style.color = '#3B82F6'; // Azul para baixa
                      td.style.fontWeight = 'bold';
                    }
                  }

                  row.appendChild(td);
                });

                tbody.appendChild(row);
              });

              tableElement.appendChild(tbody);
              tempContainer.appendChild(tableElement);
            }

            // Adicionar rodapé com data de geração
            const footer = document.createElement('div');
            footer.style.fontSize = '12px';
            footer.style.color = '#666666';
            footer.style.textAlign = 'right';
            footer.style.marginTop = '20px';
            footer.style.borderTop = '1px solid #eeeeee';
            footer.style.paddingTop = '10px';

            footer.textContent = `High Tide Systems`;
            tempContainer.appendChild(footer);

            // Adicionar o container temporário ao DOM
            document.body.appendChild(tempContainer);

            // Usar html2canvas para converter a tabela em uma imagem
            return new Promise((resolve) => {
              // Adicionar um pequeno atraso para garantir que o DOM esteja pronto
              setTimeout(() => {
                html2canvas(tempContainer, {
                  scale: 2, // Aumenta a escala para melhor qualidade
                  useCORS: true,
                  allowTaint: true,
                  backgroundColor: '#ffffff',
                  logging: false,
                  letterRendering: true
                }).then(canvas => {
                  // Remover o container temporário
                  document.body.removeChild(tempContainer);

                  // Converter o canvas para blob e salvar
                  canvas.toBlob(blob => {
                    saveAs(blob, `${filename}.png`);
                    resolve(true);
                  }, 'image/png');
                }).catch(error => {
                  console.error("Erro ao converter tabelas para imagem:", error);
                  // Remover o container temporário em caso de erro
                  if (document.body.contains(tempContainer)) {
                    document.body.removeChild(tempContainer);
                  }
                  resolve(false);
                });
              }, 100); // Pequeno atraso para garantir que o DOM esteja pronto
            });
          } catch (error) {
            console.error("Erro na exportação para imagem:", error);
            return false;
          }
        }
        // Outros formatos não suportados para múltiplas tabelas
        else {
          console.error("Formato não suportado para exportação de múltiplas tabelas");
          return false;
        }
      } else {
        // Caso de tabela única (array de objetos)
        // Se não forem fornecidas colunas, usa as chaves dos objetos
        const tableColumns = columns.length > 0
          ? columns
          : data.length > 0
            ? Object.keys(data[0]).map(key => ({
                key,
                header: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
                format: null
              }))
            : [];

        // Formata os dados com base nas colunas definidas
        const formattedData = data.map(item => {
          const formattedItem = {};

          tableColumns.forEach(col => {
            let value = item[col.key];

            // Aplica formatação personalizada se especificada
            if (col.format && typeof col.format === 'function') {
              formattedItem[col.key] = col.format(value, item);
            }
            // Aplica formatação padrão se especificada
            else if (col.type && formatters[col.type]) {
              formattedItem[col.key] = formatters[col.type](value, formatOptions[col.key]);
            }
            // Sem formatação
            else {
              formattedItem[col.key] = value !== null && value !== undefined ? value : '';
            }
          });

          return formattedItem;
        });

        // Exportação em formato XLSX
        if (format === "xlsx") {
          return exportExcel(formattedData, tableColumns, filename, title);
        }
        // Exportação em formato PDF
        else if (format === "pdf") {
          return exportPdf(formattedData, tableColumns, filename, title, subtitle);
        }
        // Exportação em formato de imagem (PNG)
        else if (format === "image") {
          // Para exportação de imagem, precisamos de um elemento DOM
          // Vamos criar uma tabela temporária e convertê-la em imagem
          return exportImage(formattedData, tableColumns, filename, title, subtitle);
        }
        else {
          console.error("Formato de exportação não suportado");
          return false;
        }
      }
    } catch (error) {
      console.error("Erro na exportação:", error);
      return false;
    }
  },

  /**
   * Exporta dados da API com os filtros atuais
   * @param {string} endpoint - Endpoint da API
   * @param {Object} filters - Filtros a serem aplicados
   * @param {Object} options - Opções de exportação
   * @returns {Promise<boolean>} - Sucesso da exportação
   */
  exportFromApi: async (endpoint, filters = {}, options = {}) => {
    try {
      const {
        format = "xlsx",
        filename = "export",
        columns = [],
      } = options;

      // Se a API suporta exportação direta
      if (options.useApiExport) {
        const response = await api.get(`${endpoint}/export`, {
          params: { ...filters, format },
          responseType: format === "pdf" ? "arraybuffer" : "blob",
        });

        const blob = new Blob(
          [response.data],
          { type: format === "pdf" ? "application/pdf" : "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" }
        );

        saveAs(blob, `${filename}.${format}`);
        return true;
      }

      // Se a API não suporta exportação, faz uma consulta normal e exporta os dados no cliente
      const response = await api.get(endpoint, { params: filters });

      // Tenta obter os dados de diferentes formatos de resposta
      const data = response.data?.data || response.data?.items || response.data || [];

      // Exporta os dados
      return exportService.exportData(data, {
        ...options,
        format,
        filename,
        columns,
      });
    } catch (error) {
      console.error("Erro ao exportar dados da API:", error);
      return false;
    }
  }
};

// Funções auxiliares de exportação

/**
 * Exporta dados para Excel com formatação aprimorada
 */
function exportExcel(data, columns, filename, title) {
  try {
    // Cria um novo workbook
    const workbook = utils.book_new();

    // Se tiver título, adiciona como primeira linha
    let worksheetData = [];

    // Data atual formatada
    const currentDate = new Date();
    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

    // Adiciona título e data
    if (title) {
      worksheetData.push([title]);
      worksheetData.push([`Gerado em: ${formattedDate}`]);
      worksheetData.push([]); // Linha em branco
    }

    // Adiciona os cabeçalhos
    const headers = columns.map(col => col.header || col.key);
    worksheetData.push(headers);

    // Adiciona os dados
    data.forEach(item => {
      const row = columns.map(col => item[col.key]);
      worksheetData.push(row);
    });

    // Cria a worksheet
    const worksheet = utils.aoa_to_sheet(worksheetData);

    // Configura os estilos (apenas as propriedades básicas são suportadas no xlsx)
    if (title) {
      // Mescla células para o título
      worksheet['!merges'] = [
        { s: {r: 0, c: 0}, e: {r: 0, c: headers.length - 1} }, // Título
        { s: {r: 1, c: 0}, e: {r: 1, c: headers.length - 1} }, // Data
      ];

      // Ajusta largura das colunas
      if (!worksheet['!cols']) worksheet['!cols'] = [];
      columns.forEach((col, idx) => {
        // Calcula largura ideal para cada coluna
        const headerWidth = (col.header || col.key).length * 1.2;
        let maxDataWidth = 0;

        // Verifica o tamanho máximo dos dados em cada coluna
        data.forEach(item => {
          const cellValue = item[col.key];
          const cellText = cellValue !== null && cellValue !== undefined ? cellValue.toString() : '';
          maxDataWidth = Math.max(maxDataWidth, cellText.length);
        });

        worksheet['!cols'][idx] = {
          wch: Math.max(10, Math.min(50, Math.max(headerWidth, maxDataWidth * 1.1)))
        };
      });
    }

    // Adiciona a worksheet ao workbook
    utils.book_append_sheet(workbook, worksheet, "Dados");

    // Converte para binário e salva
    const excelBuffer = write(workbook, {
      bookType: "xlsx",
      type: "array",
      bookSST: false,
      compression: true
    });

    const blob = new Blob([excelBuffer], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });
    saveAs(blob, `${filename}.xlsx`);

    return true;
  } catch (error) {
    console.error("Erro na exportação Excel:", error);
    return false;
  }
}

/**
 * Exporta dados para PDF com design aprimorado
 */
function exportPdf(data, columns, filename, title, subtitle) {
  try {
    // Cria um novo documento PDF no tamanho A4
    const doc = new jsPDF({
      orientation: "portrait",
      unit: "mm",
      format: "a4"
    });

    // Configurações da página
    const pageWidth = doc.internal.pageSize.width;
    const pageHeight = doc.internal.pageSize.height;
    const margin = 10;
    const contentWidth = pageWidth - (margin * 2);

    // Define cores do tema baseadas no estilo do módulo people (laranja)
    const themeColors = {
      primary: {
        light: [255, 153, 51],    // #FF9933 (primary-500)
        dark: [255, 127, 0],       // #FF7F00 (primary-600)
        text: [255, 255, 255]      // #FFFFFF (texto branco)
      },
      secondary: {
        light: [255, 237, 213],    // #FFEDD5 (orange-100)
        dark: [154, 52, 18],       // #9A3412 (orange-800)
        border: [251, 146, 60]     // #FB923C (orange-400)
      }
    };

    // ===== CABEÇALHO COM GRADIENTE =====

    // Desenha um retângulo para o cabeçalho com gradiente
    const headerHeight = subtitle ? 30 : 25;

    // Simulando um gradiente com múltiplos retângulos coloridos
    const gradientSteps = 20;
    const stepHeight = headerHeight / gradientSteps;

    for (let i = 0; i < gradientSteps; i++) {
      const ratio = i / gradientSteps;
      // Interpola as cores para criar efeito de gradiente
      const r = themeColors.primary.light[0] + ratio * (themeColors.primary.dark[0] - themeColors.primary.light[0]);
      const g = themeColors.primary.light[1] + ratio * (themeColors.primary.dark[1] - themeColors.primary.light[1]);
      const b = themeColors.primary.light[2] + ratio * (themeColors.primary.dark[2] - themeColors.primary.light[2]);

      doc.setFillColor(r, g, b);
      doc.rect(0, i * stepHeight, pageWidth, stepHeight, 'F');
    }

    // Adiciona um pequeno ícone ou logotipo
    doc.setDrawColor(255, 255, 255);
    doc.setFillColor(255, 255, 255);

    // Adiciona título no cabeçalho
    if (title) {
      doc.setFont("helvetica", "bold");
      doc.setFontSize(18);
      doc.setTextColor(255, 255, 255); // Texto branco para o cabeçalho
      doc.text(title, pageWidth / 2, 15, { align: "center" });
    }

    // Adiciona subtítulo no cabeçalho se existir
    if (subtitle) {
      doc.setFont("helvetica", "normal");
      doc.setFontSize(10);
      doc.setTextColor(255, 255, 255, 0.9); // Texto branco com transparência
      doc.text(subtitle, pageWidth / 2, 22, { align: "center" });
    }

    // Data de geração do relatório
    const currentDate = new Date();
    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

    doc.setFontSize(8);
    doc.text(`Gerado em: ${formattedDate}`, pageWidth - margin - 2, headerHeight - 3, { align: "right" });

    // ===== CONTEÚDO DA TABELA =====

    // Prepara os dados para a tabela
    const headers = columns.map(col => col.header || col.key);
    const rows = data.map(item => {
      return columns.map(col => item[col.key]);
    });

    // Prepara cabeçalhos mais curtos para prevenir quebras
    const shortHeaders = headers.map(header => {
      // Substituições específicas para cabeçalhos problemáticos
      const replacements = {
        'Nome Completo': 'Nome',
        'Data de Nascimento': 'Nascimento',
        'Data de Cadastro': 'Cadastro',
        'Relacionamento': 'Relação',
        'Telefone': 'Telefone'
      };

      return replacements[header] || header;
    });

    // Adiciona a tabela com estilo aprimorado
    autoTable(doc, {
      startY: headerHeight + 5, // Inicia após o cabeçalho
      head: [shortHeaders],
      body: rows,
      theme: "grid",
      headStyles: {
        fillColor: themeColors.primary.dark,
        textColor: themeColors.primary.text,
        fontStyle: "bold",
        halign: "center",
        fontSize: 9,
        cellPadding: { top: 3, right: 2, bottom: 3, left: 2 },
        lineWidth: 0.1,
        minCellWidth: 15,
        overflow: 'linebreak'  // Evita quebra de linha nos cabeçalhos
      },
      styles: {
        fontSize: 9,
        cellPadding: { top: 2, right: 2, bottom: 2, left: 2 },
        overflow: "linebreak",
        lineColor: [220, 220, 220],
        lineWidth: 0.1
      },
      tableWidth: 'auto',
      bodyStyles: {
        minCellHeight: 10
      },
      alternateRowStyles: {
        fillColor: [252, 252, 252]
      },
      columnStyles: columns.reduce((styles, col, index) => {
        // Definir larguras mínimas específicas para evitar quebras nos títulos
        const minWidths = {
          'fullName': 30,
          'cpf': 20,
          'email': 28,
          'phone': 20,
          'birthDate': 20,
          'gender': 15,
          'relationship': 20,
          'createdAt': 20,
          'active': 15
        };

        // Define a largura baseada na configuração ou no mínimo predefinido
        if (col.key && minWidths[col.key]) {
          styles[index] = {
            ...styles[index],
            cellWidth: minWidths[col.key],
            overflow: 'linebreak'
          };
        } else if (col.width) {
          styles[index] = {
            ...styles[index],
            cellWidth: col.width
          };
        }

        // Aplica o alinhamento se definido
        if (col.align) {
          styles[index] = {
            ...styles[index],
            halign: col.align
          };
        }

        return styles;
      }, {}),
      margin: { top: headerHeight + 5, left: margin, right: margin, bottom: margin + 15 },
      didDrawPage: function(data) {
        // Adiciona rodapé colorido em cada página
        doc.setFillColor(240, 240, 240);
        doc.rect(0, pageHeight - 15, pageWidth, 15, 'F');

        // Linha sutil acima do rodapé
        doc.setDrawColor(200, 200, 200);
        doc.line(0, pageHeight - 15, pageWidth, pageHeight - 15);

        // Adiciona numeração de páginas no rodapé
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(
          `Página ${data.pageNumber} de ${data.pageCount}`,
          pageWidth - margin - 2,
          pageHeight - 5,
          { align: "right" }
        );

        // Adiciona nome do sistema no rodapé
        doc.setTextColor(80, 80, 80);
        doc.setFontSize(8);
        doc.text("High Tide Systems", margin + 2, pageHeight - 5);
      },
      didParseCell: function(data) {
        // Aplicar formatação específica para células com status
        const col = columns[data.column.index];
        if (col && col.key === 'active') {
          if (data.cell.section === 'body') {
            if (data.cell.raw === 'Ativo') {
              data.cell.styles.fontStyle = 'bold';
              data.cell.styles.textColor = [16, 185, 129]; // Cor verde para Ativo
            } else if (data.cell.raw === 'Inativo') {
              data.cell.styles.fontStyle = 'bold';
              data.cell.styles.textColor = [220, 38, 38];  // Cor vermelha para Inativo
            }
          }
        }
      }
    });

    // Salva o documento
    doc.save(`${filename}.pdf`);

    return true;
  } catch (error) {
    console.error("Erro na exportação PDF:", error);
    return false;
  }
}

/**
 * Exporta dados para imagem (PNG) criando uma tabela HTML temporária
 */
function exportImage(data, columns, filename, title, subtitle) {
  try {
    // Criar um elemento temporário para renderizar a tabela
    const tempContainer = document.createElement('div');
    tempContainer.style.position = 'absolute';
    tempContainer.style.left = '-9999px';
    tempContainer.style.top = '-9999px';
    tempContainer.style.width = '1000px'; // Largura fixa para melhor qualidade
    tempContainer.style.backgroundColor = '#ffffff';
    tempContainer.style.padding = '20px';
    tempContainer.style.fontFamily = 'Arial, sans-serif';
    tempContainer.style.color = '#333333';
    tempContainer.style.boxSizing = 'border-box';

    // Adicionar título e subtítulo
    if (title) {
      const titleElement = document.createElement('h2');
      titleElement.textContent = title;
      titleElement.style.color = '#FF7F00'; // Cor laranja do tema
      titleElement.style.marginBottom = '5px';
      titleElement.style.fontSize = '24px';
      titleElement.style.fontWeight = 'bold';
      titleElement.style.textAlign = 'center';
      tempContainer.appendChild(titleElement);
    }

    if (subtitle) {
      const subtitleElement = document.createElement('p');
      subtitleElement.textContent = subtitle;
      subtitleElement.style.color = '#666666';
      subtitleElement.style.marginBottom = '20px';
      subtitleElement.style.fontSize = '14px';
      subtitleElement.style.textAlign = 'center';
      tempContainer.appendChild(subtitleElement);
    }

    // Criar a tabela
    const table = document.createElement('table');
    table.style.width = '100%';
    table.style.borderCollapse = 'collapse';
    table.style.marginBottom = '20px';
    table.style.boxShadow = '0 2px 5px rgba(0,0,0,0.1)';

    // Criar o cabeçalho da tabela
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    headerRow.style.backgroundColor = '#FF7F00'; // Cor laranja do tema
    headerRow.style.color = '#ffffff';

    columns.forEach(col => {
      const th = document.createElement('th');
      th.textContent = col.header || col.key;
      th.style.padding = '10px';
      th.style.textAlign = 'left';
      th.style.fontWeight = 'bold';
      th.style.fontSize = '14px';
      th.style.borderBottom = '2px solid #FF9933';
      headerRow.appendChild(th);
    });

    thead.appendChild(headerRow);
    table.appendChild(thead);

    // Criar o corpo da tabela
    const tbody = document.createElement('tbody');

    data.forEach((item, rowIndex) => {
      const row = document.createElement('tr');
      row.style.backgroundColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
      row.style.borderBottom = '1px solid #eeeeee';

      columns.forEach(col => {
        const td = document.createElement('td');
        td.textContent = item[col.key] !== undefined && item[col.key] !== null ? item[col.key] : '';
        td.style.padding = '8px 10px';
        td.style.fontSize = '13px';

        // Estilização especial para status
        if (col.key === 'active') {
          if (item[col.key] === 'Ativo') {
            td.style.color = '#10B981'; // Verde para ativo
            td.style.fontWeight = 'bold';
          } else if (item[col.key] === 'Inativo') {
            td.style.color = '#DC2626'; // Vermelho para inativo
            td.style.fontWeight = 'bold';
          }
        }

        row.appendChild(td);
      });

      tbody.appendChild(row);
    });

    table.appendChild(tbody);
    tempContainer.appendChild(table);

    // Adicionar rodapé com data de geração
    const footer = document.createElement('div');
    footer.style.fontSize = '12px';
    footer.style.color = '#666666';
    footer.style.textAlign = 'right';
    footer.style.marginTop = '10px';

    const currentDate = new Date();
    const formattedDate = `${currentDate.getDate().toString().padStart(2, '0')}/${(currentDate.getMonth() + 1).toString().padStart(2, '0')}/${currentDate.getFullYear()} às ${currentDate.getHours().toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}`;

    footer.textContent = `Gerado em: ${formattedDate} | High Tide Systems`;
    tempContainer.appendChild(footer);

    // Adicionar o container temporário ao DOM
    document.body.appendChild(tempContainer);

    // Usar html2canvas para converter a tabela em uma imagem
    return new Promise((resolve) => {
      // Adicionar um pequeno atraso para garantir que o DOM esteja pronto
      setTimeout(() => {
        html2canvas(tempContainer, {
          scale: 2, // Aumenta a escala para melhor qualidade
          useCORS: true,
          allowTaint: true,
          backgroundColor: '#ffffff',
          logging: false,
          letterRendering: true
        }).then(canvas => {
          // Remover o container temporário
          document.body.removeChild(tempContainer);

          // Converter o canvas para blob e salvar
          canvas.toBlob(blob => {
            saveAs(blob, `${filename}.png`);
            resolve(true);
          }, 'image/png');
        }).catch(error => {
          console.error("Erro ao converter tabela para imagem:", error);
          // Remover o container temporário em caso de erro
          if (document.body.contains(tempContainer)) {
            document.body.removeChild(tempContainer);
          }
          resolve(false);
        });
      }, 100); // Pequeno atraso para garantir que o DOM esteja pronto
    });
  } catch (error) {
    console.error("Erro na exportação para imagem:", error);
    return false;
  }
}