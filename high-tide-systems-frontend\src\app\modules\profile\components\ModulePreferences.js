"use client";

import React, { useState, useEffect } from "react";
import { LayoutDashboard, Settings, Info } from "lucide-react";
import { ModuleFormGroup, ModuleSelect } from "@/components/ui";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import { getModulePageOptions, getPreferredLandingPage, setPreferredLandingPage } from "@/utils/moduleRedirection";
import { api } from "@/utils/api";

const ModulePreferences = () => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const [preferences, setPreferences] = useState({
    admin: '',
    scheduler: '',
    people: '',
    financial: ''
  });
  const [isLoading, setIsLoading] = useState(true);

  // Carregar preferências salvas
  useEffect(() => {
    if (user) {
      setIsLoading(true);

      const loadPreferences = async () => {
        try {
          // Primeiro tenta carregar do backend
          try {
            const response = await api.get('/module-preferences');
            console.log('Resposta completa do backend:', response);

            // Verificar o formato da resposta
            let serverPreferences;
            if (response.data && response.data.success && response.data.data) {
              // Formato com wrapper de sucesso
              serverPreferences = response.data.data;
            } else {
              // Formato direto
              serverPreferences = response.data || {};
            }

            console.log('Preferências extraídas da resposta:', serverPreferences);

            // Garantir que as preferências sejam atualizadas corretamente
            const updatedPreferences = {
              admin: serverPreferences.admin || '',
              scheduler: serverPreferences.scheduler || '',
              people: serverPreferences.people || '',
              financial: serverPreferences.financial || ''
            };

            console.log('Preferências formatadas para o estado:', updatedPreferences);
            setPreferences(updatedPreferences);

            // Atualizar também o localStorage para manter sincronizado
            Object.keys(updatedPreferences).forEach(moduleId => {
              if (updatedPreferences[moduleId]) {
                localStorage.setItem(
                  'modulePreferences',
                  JSON.stringify({
                    ...JSON.parse(localStorage.getItem('modulePreferences') || '{}'),
                    [moduleId]: updatedPreferences[moduleId]
                  })
                );
              }
            });

            console.log('Preferências carregadas do backend com sucesso');
          } catch (apiError) {
            console.error('Erro ao carregar preferências do backend:', apiError);

            // Se falhar, usa o localStorage como fallback
            const adminPage = getPreferredLandingPage('admin');
            const schedulerPage = getPreferredLandingPage('scheduler');
            const peoplePage = getPreferredLandingPage('people');
            const financialPage = getPreferredLandingPage('financial');

            const localPreferences = {
              admin: adminPage || '',
              scheduler: schedulerPage || '',
              people: peoplePage || '',
              financial: financialPage || ''
            };

            console.log('Preferências carregadas do localStorage:', localPreferences);
            setPreferences(localPreferences);

            console.log('Preferências carregadas do localStorage como fallback');
          }
        } catch (error) {
          console.error("Erro ao carregar preferências:", error);
        } finally {
          setIsLoading(false);
        }
      };

      loadPreferences();
    }
  }, [user]);

  // Manipular mudanças nas preferências
  const handlePreferenceChange = async (moduleId, value) => {
    // Atualizar o estado local imediatamente para feedback visual
    setPreferences(prev => ({
      ...prev,
      [moduleId]: value
    }));

    try {
      // Salvar a preferência usando a função que atualiza tanto localStorage quanto backend
      await setPreferredLandingPage(moduleId, value);

      // Tentar atualizar diretamente no backend também
      try {
        // Obter as preferências atuais do backend
        const response = await api.get('/module-preferences');
        console.log('Resposta completa do backend (update):', response);

        // Verificar o formato da resposta
        let serverPreferences;
        if (response.data && response.data.success && response.data.data) {
          // Formato com wrapper de sucesso
          serverPreferences = response.data.data;
        } else {
          // Formato direto
          serverPreferences = response.data || {};
        }

        console.log('Preferências extraídas da resposta (update):', serverPreferences);

        // Atualizar a preferência do módulo
        serverPreferences[moduleId] = value;

        console.log(`Enviando preferências atualizadas para o backend:`, serverPreferences);

        // Enviar as preferências atualizadas para o backend
        await api.put('/module-preferences', {
          modulePreferences: serverPreferences
        });

        // Atualizar o localStorage para manter sincronizado
        const localPreferences = JSON.parse(localStorage.getItem('modulePreferences') || '{}');
        localPreferences[moduleId] = value;
        localStorage.setItem('modulePreferences', JSON.stringify(localPreferences));

        console.log(`Preferências atualizadas no localStorage:`, localPreferences);

        toast_success(`Página inicial do módulo ${moduleId} atualizada com sucesso!`);
      } catch (apiError) {
        console.error(`Erro ao atualizar preferência no backend para ${moduleId}:`, apiError);
        // Se falhar no backend, pelo menos o localStorage foi atualizado
        toast_success(`Página inicial do módulo ${moduleId} atualizada localmente.`);
      }
    } catch (error) {
      console.error(`Erro ao salvar preferência para ${moduleId}:`, error);
      toast_error(`Erro ao salvar preferência para ${moduleId}`);
    }
  };

  // Verificar se o usuário tem acesso ao módulo
  const hasModuleAccess = (moduleId) => {
    if (!user || !user.modules) return false;

    // Se o usuário é admin, tem acesso a todos os módulos
    if (user.role === 'SYSTEM_ADMIN' || user.role === 'COMPANY_ADMIN') {
      return true;
    }

    // Verificar se o módulo está na lista de módulos do usuário
    return user.modules.includes(moduleId.toUpperCase());
  };

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4 flex items-center">
          <LayoutDashboard size={20} className="mr-2 text-slate-500 dark:text-slate-400" />
          Páginas Iniciais dos Módulos
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
          Configure qual página será exibida quando você acessar cada módulo. Estas configurações são pessoais e não afetam outros usuários.
        </p>

        <div className="space-y-4">
          {/* Módulo Admin */}
          {hasModuleAccess('admin') && (
            <ModuleFormGroup
              moduleColor="admin"
              label="Módulo Administração"
              htmlFor="adminPage"
              icon={<Settings size={16} />}
              description="Página inicial ao acessar o módulo de Administração"
            >
              <ModuleSelect
                moduleColor="admin"
                id="adminPage"
                name="adminPage"
                value={preferences.admin}
                onChange={(e) => handlePreferenceChange('admin', e.target.value)}
                disabled={isLoading}
              >
                <option value="">Padrão do Sistema</option>
                {getModulePageOptions('admin').map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </ModuleSelect>
            </ModuleFormGroup>
          )}

          {/* Módulo Scheduler */}
          {hasModuleAccess('scheduler') && (
            <ModuleFormGroup
              moduleColor="scheduler"
              label="Módulo Agendamento"
              htmlFor="schedulerPage"
              icon={<LayoutDashboard size={16} />}
              description="Página inicial ao acessar o módulo de Agendamento"
            >
              <ModuleSelect
                moduleColor="scheduler"
                id="schedulerPage"
                name="schedulerPage"
                value={preferences.scheduler}
                onChange={(e) => handlePreferenceChange('scheduler', e.target.value)}
                disabled={isLoading}
              >
                <option value="">Padrão do Sistema</option>
                {getModulePageOptions('scheduler').map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </ModuleSelect>
            </ModuleFormGroup>
          )}

          {/* Módulo People */}
          {hasModuleAccess('people') && (
            <ModuleFormGroup
              moduleColor="people"
              label="Módulo Pessoas"
              htmlFor="peoplePage"
              icon={<LayoutDashboard size={16} />}
              description="Página inicial ao acessar o módulo de Pessoas"
            >
              <ModuleSelect
                moduleColor="people"
                id="peoplePage"
                name="peoplePage"
                value={preferences.people}
                onChange={(e) => handlePreferenceChange('people', e.target.value)}
                disabled={isLoading}
              >
                <option value="">Padrão do Sistema</option>
                {getModulePageOptions('people').map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </ModuleSelect>
            </ModuleFormGroup>
          )}

          {/* Módulo Financial */}
          {hasModuleAccess('financial') && (
            <ModuleFormGroup
              moduleColor="financial"
              label="Módulo Financeiro"
              htmlFor="financialPage"
              icon={<LayoutDashboard size={16} />}
              description="Página inicial ao acessar o módulo Financeiro"
            >
              <ModuleSelect
                moduleColor="financial"
                id="financialPage"
                name="financialPage"
                value={preferences.financial}
                onChange={(e) => handlePreferenceChange('financial', e.target.value)}
                disabled={isLoading}
              >
                <option value="">Padrão do Sistema</option>
                {getModulePageOptions('financial').map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </ModuleSelect>
            </ModuleFormGroup>
          )}
        </div>

        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800">
          <div className="flex items-start">
            <Info size={18} className="text-blue-500 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h4 className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">Sobre as páginas iniciais</h4>
              <p className="text-xs text-blue-600 dark:text-blue-400">
                Na primeira vez que você acessa um módulo, a página de introdução será exibida. Após isso, a página configurada aqui será usada como padrão.
                Se nenhuma página for selecionada, o sistema usará a página padrão do módulo.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModulePreferences;
