-- CreateTable
CREATE TABLE "CurriculumFolder" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "personId" TEXT NOT NULL,
    "shareWithParents" BOOLEAN NOT NULL DEFAULT false,
    "shareWithSchools" BOOLEAN NOT NULL DEFAULT false,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "CurriculumFolder_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "CurriculumFolder_companyId_idx" ON "CurriculumFolder"("companyId");

-- CreateIndex
CREATE INDEX "CurriculumFolder_active_idx" ON "CurriculumFolder"("active");

-- CreateIndex
CREATE INDEX "CurriculumFolder_personId_idx" ON "CurriculumFolder"("personId");

-- CreateIndex
CREATE INDEX "CurriculumFolder_createdById_idx" ON "CurriculumFolder"("createdById");

-- AddForeignKey
ALTER TABLE "CurriculumFolder" ADD CONSTRAINT "CurriculumFolder_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolder" ADD CONSTRAINT "CurriculumFolder_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "CurriculumFolder" ADD CONSTRAINT "CurriculumFolder_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
