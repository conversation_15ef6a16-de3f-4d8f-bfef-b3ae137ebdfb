const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const checkPermission = require('../../middlewares/permissionCheck');
const systemAdminMiddleware = require('../../middlewares/systemAdmin');
const userProfileImageUpload = require('../../middlewares/userProfileImageUpload');
const {
  UserController,
  createUserValidation,
  updateModulesValidation,
  updatePermissionsValidation,
  updateRoleValidation,
  updateModulePreferencesValidation
} = require('../../controllers/userController');


// Rotas protegidas
// Rota especial para clientes acessarem usuários com módulo SCHEDULING
router.get('/', authenticate, (req, res, next) => {
  // Se for um cliente e estiver buscando usuários com módulo SCHEDULING, permitir acesso
  if (req.user.isClient && req.query.modules &&
      (Array.isArray(req.query.modules) && req.query.modules.includes('SCHEDULING') ||
       req.query.modules === 'SCHEDULING')) {
    return UserController.list(req, res);
  }

  // Caso contrário, verificar permissão normal
  checkPermission('admin.users.view')(req, res, next);
}, UserController.list);

// Rota especial para system_admin buscar todos os usuários (para o chat)
// Removendo temporariamente a rota /all que estava causando erro
router.get('/:id', authenticate, checkPermission('admin.users.view'), UserController.getProfile);
router.post('/', authenticate, checkPermission('admin.users.create'), createUserValidation, UserController.create);
router.put('/:id', authenticate, checkPermission('admin.users.edit'), UserController.update);

router.patch('/:id/modules', authenticate, checkPermission('admin.permissions.manage'), updateModulesValidation, UserController.updateModules);
router.patch('/:id/permissions', authenticate, checkPermission('admin.permissions.manage'), updatePermissionsValidation, UserController.updatePermissions);
router.patch('/:id/status', authenticate, checkPermission('admin.users.edit'), UserController.toggleStatus);
router.patch('/:id/role', authenticate, checkPermission('admin.users.edit'), updateRoleValidation, UserController.updateRole);

router.delete('/:id', authenticate, checkPermission('admin.users.delete'), UserController.delete);

// Rotas para gerenciamento de imagem de perfil
router.post('/:id/profile-image', authenticate, checkPermission('admin.users.edit'), userProfileImageUpload.single('profileImage'), UserController.uploadProfileImage);
router.get('/:id/profile-image', UserController.getProfileImage);

// Rotas para gerenciamento de preferências de módulos
// Importante: rotas mais específicas devem vir antes das rotas com parâmetros dinâmicos
console.log('[userRoutes] Registrando rota GET /module-preferences');
router.get('/module-preferences', authenticate, (req, res, next) => {
  console.log('[userRoutes] Rota GET /module-preferences chamada');
  UserController.getModulePreferences(req, res, next);
});

console.log('[userRoutes] Registrando rota PUT /module-preferences');
router.put('/module-preferences', authenticate, updateModulePreferencesValidation, (req, res, next) => {
  console.log('[userRoutes] Rota PUT /module-preferences chamada');
  UserController.updateModulePreferences(req, res, next);
});

// Rotas com parâmetros dinâmicos devem vir depois
console.log('[userRoutes] Registrando rota GET /:id/module-preferences');
router.get('/:id/module-preferences', authenticate, (req, res, next) => {
  console.log('[userRoutes] Rota GET /:id/module-preferences chamada com id:', req.params.id);
  UserController.getModulePreferences(req, res, next);
});

console.log('[userRoutes] Registrando rota PUT /:id/module-preferences');
router.put('/:id/module-preferences', authenticate, checkPermission('admin.users.edit'), updateModulePreferencesValidation, (req, res, next) => {
  console.log('[userRoutes] Rota PUT /:id/module-preferences chamada com id:', req.params.id);
  UserController.updateModulePreferences(req, res, next);
});

module.exports = router;