// src/app/modules/aba/services/abaPlusDashboardService.js
import { api } from "@/utils/api";

export const abaPlusDashboardService = {
  /**
   * Get all dashboard data for ABA+ module
   * @param {Object} params - Parameters for filtering dashboard data
   * @returns {Promise<Object>} Dashboard data
   */
  getABAPlusDashboardData: async (params = {}) => {
    try {
      const response = await api.get("/aba/dashboard", {
        params: {
          startDate: params.startDate ? params.startDate.toISOString() : undefined,
          endDate: params.endDate ? params.endDate.toISOString() : undefined
        }
      });

      if (response.data) {
        return response.data;
      }

      // Dados de fallback caso a API não retorne dados
      console.warn("API não retornou dados do dashboard, usando dados de fallback");
      return {
        skillsByCategory: [
          { name: "Comunicação", value: 5 },
          { name: "Habilidades Sociais", value: 3 },
          { name: "Comportamento", value: 2 }
        ],
        programsByStatus: [
          { name: "<PERSON> Andament<PERSON>", value: 2 },
          { name: "Não Iniciado", value: 1 }
        ],
        evaluationsByMonth: [
          { name: "Atual", count: 1 }
        ],
        recentPrograms: [],
        summary: {
          totalSkills: 10,
          totalPrograms: 3,
          totalEvaluations: 1,
          totalLearners: 0
        }
      };
    } catch (error) {
      console.error("Error fetching ABA+ dashboard data:", error);
      throw error;
    }
  }
};

export default abaPlusDashboardService;
