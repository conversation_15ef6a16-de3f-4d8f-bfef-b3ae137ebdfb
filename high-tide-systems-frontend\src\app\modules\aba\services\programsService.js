// src/app/modules/aba/services/programsService.js
import { api } from "@/utils/api";

export const programsService = {
  // Get programs with optional filters
  getPrograms: async (filters = {}) => {
    const { page = 1, limit = 10, search = "", type, active } = filters;

    try {
      console.log("programsService.getPrograms - Enviando requisição com parâmetros:", {
        page,
        limit,
        search: search || undefined,
        type: type || undefined,
        active: active === undefined ? undefined : active
      });

      const response = await api.get("/aba/programs", {
        params: {
          page,
          limit,
          search: search || undefined,
          type: type || undefined,
          active: active === undefined ? undefined : active
        }
      });

      console.log("programsService.getPrograms - Resposta da API:", response.data);

      // Verificar a estrutura da resposta
      const items = response.data.programs || response.data.items || [];
      const total = response.data.total || 0;

      console.log("programsService.getPrograms - Itens extraídos:", items);

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching programs:", error);
      throw error;
    }
  },

  // Get a single program by ID
  getProgram: async (id) => {
    try {
      const response = await api.get(`/aba/programs/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching program ${id}:`, error);
      throw error;
    }
  },

  // Create a new program
  createProgram: async (programData) => {
    try {
      const response = await api.post("/aba/programs", programData);
      return response.data;
    } catch (error) {
      console.error("Error creating program:", error);
      throw error;
    }
  },

  // Update an existing program
  updateProgram: async (id, programData) => {
    try {
      const response = await api.put(`/aba/programs/${id}`, programData);
      return response.data;
    } catch (error) {
      console.error(`Error updating program ${id}:`, error);
      throw error;
    }
  },

  // Toggle program status (active/inactive)
  toggleProgramStatus: async (id) => {
    try {
      const response = await api.patch(`/aba/programs/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling status for program ${id}:`, error);
      throw error;
    }
  },

  // Delete a program
  deleteProgram: async (id) => {
    try {
      const response = await api.delete(`/aba/programs/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting program ${id}:`, error);
      throw error;
    }
  },

  // Get program targets
  getProgramTargets: async (programId, filters = {}) => {
    const { page = 1, limit = 10 } = filters;

    try {
      const response = await api.get(`/aba/programs/${programId}/targets`, {
        params: {
          page,
          limit
        }
      });

      // Verificar a estrutura da resposta
      const items = response.data.targets || response.data.items || [];
      const total = response.data.total || 0;

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error(`Error fetching targets for program ${programId}:`, error);
      throw error;
    }
  },

  // Create a new program target
  createProgramTarget: async (targetData) => {
    try {
      const response = await api.post("/aba/programs/targets", targetData);
      return response.data;
    } catch (error) {
      console.error("Error creating program target:", error);
      throw error;
    }
  },

  // Update an existing program target
  updateProgramTarget: async (id, targetData) => {
    try {
      const response = await api.put(`/aba/programs/targets/${id}`, targetData);
      return response.data;
    } catch (error) {
      console.error(`Error updating program target ${id}:`, error);
      throw error;
    }
  },

  // Delete a program target
  deleteProgramTarget: async (id) => {
    try {
      const response = await api.delete(`/aba/programs/targets/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting program target ${id}:`, error);
      throw error;
    }
  },

  // Helper function to get program type label
  getProgramTypeLabel: (type) => {
    const types = {
      PROGRAM_CATALOG: "Catálogo de Programas",
      LEARNING_PROGRAM: "Programa de Aprendizagem"
    };
    return types[type] || type;
  },

  // Get program type options for select
  getProgramTypeOptions: () => {
    return [
      { value: "PROGRAM_CATALOG", label: "Catálogo de Programas" },
      { value: "LEARNING_PROGRAM", label: "Programa de Aprendizagem" }
    ];
  }
};

export default programsService;
