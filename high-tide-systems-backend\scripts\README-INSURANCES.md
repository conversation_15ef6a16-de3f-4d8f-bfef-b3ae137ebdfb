# Script de Cadastro de Convênios

Este script cadastra convênios de saúde reais para todas as empresas existentes no sistema.

## Características

- Cria entre 4 e 10 convênios aleatórios para cada empresa
- Utiliza nomes de convênios de saúde reais do Brasil
- Verifica duplicidade para não criar convênios repetidos na mesma empresa
- Associa cada convênio à sua respectiva empresa

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-insurance-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed-insurances.js
```

## Lista de Convênios Incluídos

O script inclui os seguintes convênios de saúde reais:

1. Amil
2. Bradesco Saúde
3. SulAmérica
4. Unimed
5. Porto Seguro Saúde
6. NotreDame Intermédica
7. Hapvida
8. Golden Cross
9. Prevent Senior
10. São Francisco Saúde
11. Mediservice
12. Omint
13. Care Plus
14. Allianz Saúde
15. Amil One
16. Assim Saúde
17. Biovida Saúde
18. Central Nacional Unimed
19. Economus
20. Gama Saúde

## Observações

- O script verifica se o convênio já existe para a empresa antes de criar um novo
- Cada empresa terá um conjunto diferente de convênios, selecionados aleatoriamente da lista acima
- Os convênios são associados às empresas através do campo `companyId` na tabela `Insurance`
