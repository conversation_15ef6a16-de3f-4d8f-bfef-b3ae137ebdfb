// src/controllers/chat/conversationController.js
const chatService = require('../../services/chat/chatService');
const errorHandler = require('../../utils/errorHandler');

/**
 * Cria uma nova conversa
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const createConversation = async (req, res) => {
  try {
    const userId = req.user.id;
    const conversation = await chatService.createConversation(req.body, userId);
    
    res.status(201).json({
      success: true,
      data: conversation
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Busca todas as conversas do usuário
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const getUserConversations = async (req, res) => {
  try {
    const userId = req.user.id;
    const { search, limit, offset, includeMessages } = req.query;
    
    const result = await chatService.getUserConversations(userId, {
      search,
      limit: limit ? parseInt(limit) : undefined,
      offset: offset ? parseInt(offset) : undefined,
      includeMessages: includeMessages === 'true'
    });
    
    res.status(200).json({
      success: true,
      data: result
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Busca uma conversa pelo ID
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const getConversationById = async (req, res) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.id;
    
    const conversation = await chatService.getConversationById(conversationId, userId);
    
    res.status(200).json({
      success: true,
      data: conversation
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Adiciona um participante a uma conversa
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const addParticipant = async (req, res) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.id;
    const { participantId } = req.body;
    
    if (!participantId) {
      return res.status(400).json({
        success: false,
        error: 'participantId é obrigatório'
      });
    }
    
    const participant = await chatService.addParticipant(conversationId, participantId, userId);
    
    res.status(200).json({
      success: true,
      data: participant
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Remove um participante de uma conversa
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const removeParticipant = async (req, res) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.id;
    const participantId = req.params.participantId;
    
    const participant = await chatService.removeParticipant(conversationId, participantId, userId);
    
    res.status(200).json({
      success: true,
      data: participant
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

/**
 * Atualiza uma conversa
 * @param {Object} req - Requisição Express
 * @param {Object} res - Resposta Express
 */
const updateConversation = async (req, res) => {
  try {
    const userId = req.user.id;
    const conversationId = req.params.id;
    
    const conversation = await chatService.updateConversation(conversationId, req.body, userId);
    
    res.status(200).json({
      success: true,
      data: conversation
    });
  } catch (error) {
    const errorResponse = errorHandler.handleError(error);
    res.status(errorResponse.status).json({
      success: false,
      error: errorResponse.message,
      errors: errorResponse.errors
    });
  }
};

module.exports = {
  createConversation,
  getUserConversations,
  getConversationById,
  addParticipant,
  removeParticipant,
  updateConversation
};
