// src/app/modules/people/services/clientsService.js
import { exportService } from "@/app/services/exportService";
import { api } from "@/utils/api";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import { extractData, extractEntity } from "@/utils/apiResponseAdapter";

export const clientsService = {
  // Get clients with optional filters
  getClients: async (filters = {}) => {
    // Ensure page is a number and at least 1
    const page = parseInt(filters.page, 10) || 1;
    const limit = parseInt(filters.limit, 10) || 10;
    const {
      search = "",
      clientIds,
      active,
      companyId,
      include_persons,
      sortField = 'fullName',  // Default sort by fullName (backend controller will handle this)
      sortDirection = 'asc'    // Default sort direction
    } = filters;

    try {
      // Construir parâmetros para a API
      const params = {
        page,
        limit,
        search: search || undefined,
        active: active === undefined ? undefined : active,
        companyId: companyId || undefined,
        include_persons: include_persons || undefined,
        sortField,
        sortDirection
      };

      // Adicionar clientIds como parâmetros separados com notação de array
      if (clientIds && clientIds.length > 0) {
        // Garantir que clientIds seja um array
        const clientIdsArray = Array.isArray(clientIds) ? clientIds : [clientIds];

        // Adicionar cada ID como um parâmetro separado
        clientIdsArray.forEach((id, index) => {
          // Usar a notação de array para compatibilidade com a API
          params[`clientIds[${index}]`] = id;
        });

        console.log("Filtrando por múltiplos IDs de clientes:", clientIdsArray);
      }

      const response = await api.get("/clients", { params });

      // Usar o adaptador para extrair os dados de forma consistente
      return extractData(response.data, 'clients', ['data']);
    } catch (error) {
      console.error("Error fetching clients:", error);
      throw error;
    }
  },

  // Get a single client by ID
  getClient: async (id) => {
    try {
      const response = await api.get(`/clients/${id}`);
      // Usar o adaptador para extrair a entidade
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error fetching client ${id}:`, error);
      throw error;
    }
  },

  // Create a new client
  createClient: async (clientData) => {
    try {
      // Ajustar payload para incluir dados da pessoa
      const response = await api.post("/clients", clientData);
      return response.data;
    } catch (error) {
      console.error("Error creating client:", error);
      throw error;
    }
  },

  // Update an existing client
  updateClient: async (id, clientData) => {
    try {
      const response = await api.put(`/clients/${id}`, clientData);
      return response.data;
    } catch (error) {
      console.error(`Error updating client ${id}:`, error);
      throw error;
    }
  },

  // Toggle client active status
  toggleClientStatus: async (id) => {
    try {
      const response = await api.patch(`/clients/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling status for client ${id}:`, error);
      throw error;
    }
  },

  // Delete a client (soft delete)
  deleteClient: async (id) => {
    try {
      const response = await api.delete(`/clients/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting client ${id}:`, error);
      throw error;
    }
  },

  // Get persons associated with a client
  getClientPersons: async (clientId) => {
    try {
      const response = await api.get(`/clients/${clientId}/persons`);
      // Usar o adaptador para extrair os dados de forma consistente
      const { persons } = extractData(response.data, 'persons', ['people']);
      return persons;
    } catch (error) {
      console.error(`Error fetching persons for client ${clientId}:`, error);
      return [];
    }
  },
  /**
 * Exporta a lista de clientes com os filtros aplicados
 * @param {Object} filters - Filtros atuais (busca, status, etc)
 * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
 * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
 */
  exportClients: async (filters, exportFormat = "xlsx") => {
    try {
      // Obter os dados filtrados da API
      const response = await clientsService.getClients({
        ...filters,
        limit: 1000, // Aumentamos o limite para exportar mais dados
      });

      // Extrair os dados dos clientes usando o adaptador
      const { clients } = extractData(response, 'clients', ['data']);
      const data = clients;

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "fullName", header: "Cliente" },
        { key: "login", header: "Login" },
        { key: "email", header: "Email" },
        {
          key: "personsCount",
          header: "Nº de Pessoas",
          format: (_, item) => item.persons ? item.persons.length : 0
        },
        {
          key: "active",
          header: "Status",
          format: (value) => value ? "Ativo" : "Inativo",
          align: "center",
          width: 20
        },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(client => {
        // Obter o nome completo do titular ou usar o login como fallback
        const fullName = client.persons && client.persons[0] && client.persons[0].fullName
          ? client.persons[0].fullName
          : client.login;

        return {
          fullName: fullName,
          login: client.login || "",
          email: client.email || "",
          personsCount: client.persons ? client.persons.length : 0,
          active: client.active,
          createdAt: client.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.clientIds && filters.clientIds.length > 0) {
        subtitleParts.push(`Clientes específicos: ${filters.clientIds.length} selecionados`);
      }
      if (filters.active !== undefined) {
        subtitleParts.push(`Status: ${filters.active ? "Ativos" : "Inativos"}`);
      }
      if (filters.companyId) {
        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "clientes",
        columns,
        title: "Lista de Clientes",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar clientes:", error);
      return false;
    }
  }
};

export default clientsService;