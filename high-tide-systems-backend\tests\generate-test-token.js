// generate-test-token.js
require('dotenv').config();
const jwt = require('jsonwebtoken');
const prisma = require('../src/utils/prisma');

async function generateTestToken() {
  try {
    // Buscar um usuário administrador do banco de dados
    const admin = await prisma.user.findFirst({
      where: {
        role: 'ADMIN'
      }
    });
    
    if (!admin) {
      console.error('Nenhum usuário administrador encontrado no banco de dados.');
      process.exit(1);
    }
    
    // Gerar um token JWT para o usuário
    const token = jwt.sign(
      { 
        id: admin.id,
        email: admin.email,
        role: admin.role,
        companyId: admin.companyId
      },
      process.env.JWT_SECRET,
      { expiresIn: '1h' }
    );
    
    console.log('Token de teste gerado com sucesso:');
    console.log(token);
    console.log('\nAdicione este token ao seu arquivo .env como TEST_TOKEN=<token>');
    
    process.exit(0);
  } catch (error) {
    console.error('Erro ao gerar token de teste:', error);
    process.exit(1);
  }
}

generateTestToken();
