// utils/api.js
import axios from 'axios';

export const api = axios.create({
	baseURL: 'http://localhost:5000',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Interceptor para adicionar token de autenticação em todas as requisições
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  error => Promise.reject(error)
);

// Interceptor para tratamento de erros
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Funções auxiliares para construir URLs
export const getApiUrl = (path) => {
  // Remover barras iniciais extras se necessário
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  return `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000'}/${cleanPath}`;
};

export const DOCUMENT_URLS = {
  get: (id) => getApiUrl(`documents/${id}`),
  download: (id) => getApiUrl(`documents/${id}?download=true`),
  upload: (targetType, targetId) => getApiUrl(`documents/upload?targetType=${targetType}&targetId=${targetId}`),
  list: (targetType, targetId) => getApiUrl(`documents?targetType=${targetType}&targetId=${targetId}`)
};
