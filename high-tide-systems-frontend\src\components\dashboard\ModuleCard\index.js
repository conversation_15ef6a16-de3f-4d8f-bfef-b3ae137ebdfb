'use client';

import React, { memo } from 'react';
import { ChevronRight, Construction } from 'lucide-react';
import { isUnderConstruction, getConstructionMessage } from '@/utils/constructionUtils';
import { useConstructionMessage } from '@/hooks/useConstructionMessage';

export const ModuleCard = memo(({ title, icon: Icon, description, onClick, isAccessible, moduleId }) => {
  const { constructionProps } = useConstructionMessage();
  const moduleUnderConstruction = isUnderConstruction(moduleId);
  const constructionMessage = getConstructionMessage(moduleId);
  return (
    <div
      role="button"
      tabIndex={isAccessible ? 0 : -1}
      aria-disabled={!isAccessible}
      aria-label={`Acessar módulo ${title}`}
      className={`
        relative max-h-48 rounded-xl transition-all duration-300 shadow-lg dark:shadow-md dark:shadow-black/20
        overflow-hidden bg-white dark:bg-gray-800 flex flex-col justify-between
        ${isAccessible
          ? `border border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark
             hover:shadow-xl dark:hover:shadow-lg dark:hover:shadow-black/30 cursor-pointer
             transform hover:-translate-y-2`
          : 'border border-gray-200 dark:border-gray-700 opacity-60 cursor-not-allowed'}
      `}
      {...(isAccessible && moduleUnderConstruction
        ? constructionProps({
            title: constructionMessage.title,
            content: constructionMessage.content,
            icon: constructionMessage.icon,
            position: 'auto'
          })
        : { onClick: isAccessible ? onClick : undefined }
      )}
      onKeyDown={(e) => {
        if (isAccessible && (e.key === 'Enter' || e.key === ' ')) {
          e.preventDefault();
          if (moduleUnderConstruction) {
            // Simular clique para mostrar mensagem de construção
            e.currentTarget.click();
          } else {
            onClick();
          }
        }
      }}
    >
      {/* Cabeçalho*/}
      <div className={`p-2 relative`}>
        <div className="flex items-center gap-3">
          {/* Ícone em círculo*/}
          <div className={`p-2.5 bg-module-${moduleId}-bg dark:bg-module-${moduleId}-bg-dark/70 rounded-full shadow-sm dark:shadow-md dark:shadow-black/10`} aria-hidden="true">
            <Icon
              size={24}
              className={isAccessible
                ? `text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark`
                : 'text-gray-400 dark:text-gray-500'}
            />
          </div>

          <h3 className="text-lg font-bold text-gray-800 dark:text-gray-100">{title}</h3>
        </div>
      </div>

      {/* Corpo do cartão*/}
      <div className="relative bg-white dark:bg-gray-800 p-5">
        <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">{description}</p>

        {isAccessible && (
          <div className="flex justify-end items-center mt-1">
            <span
              className={`
                flex items-center text-sm font-medium
                text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark hover:underline group
              `}
              aria-hidden="true"
            >
              Acessar
              <ChevronRight size={16} className="ml-1 transition-transform group-hover:translate-x-1" />
            </span>
          </div>
        )}
      </div>
    </div>
  );
});
ModuleCard.displayName = 'ModuleCard';

export default ModuleCard;