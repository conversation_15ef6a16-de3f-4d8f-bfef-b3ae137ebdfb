/*
  Warnings:

  - You are about to drop the column `clientId` on the `Person` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "Person" DROP CONSTRAINT "Person_clientId_fkey";

-- DropIndex
DROP INDEX "Person_clientId_idx";

-- AlterTable
ALTER TABLE "Person" DROP COLUMN "clientId";

-- CreateTable
CREATE TABLE "ClientPerson" (
    "id" TEXT NOT NULL,
    "clientId" TEXT NOT NULL,
    "personId" TEXT NOT NULL,
    "relationship" TEXT,
    "isPrimary" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "ClientPerson_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "ClientPerson_clientId_idx" ON "ClientPerson"("clientId");

-- CreateIndex
CREATE INDEX "ClientPerson_personId_idx" ON "ClientPerson"("personId");

-- CreateIndex
CREATE INDEX "ClientPerson_personId_isPrimary_idx" ON "ClientPerson"("personId", "isPrimary");

-- CreateIndex
CREATE UNIQUE INDEX "ClientPerson_clientId_personId_key" ON "ClientPerson"("clientId", "personId");

-- AddForeignKey
ALTER TABLE "ClientPerson" ADD CONSTRAINT "ClientPerson_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "Client"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ClientPerson" ADD CONSTRAINT "ClientPerson_personId_fkey" FOREIGN KEY ("personId") REFERENCES "Person"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
