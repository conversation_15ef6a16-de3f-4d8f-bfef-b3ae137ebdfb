// src/swagger/recurrenceRoutes.js

/**
 * @swagger
 * tags:
 *   name: Recorrências
 *   description: Gestão de agendamentos recorrentes
 */

/**
 * @swagger
 * /recurrences:
 *   post:
 *     summary: Cria uma nova recorrência de agendamentos
 *     description: |
 *       Cria uma série de agendamentos recorrentes baseados em padrões de dias da semana e horários.
 *       Permite configurar recorrência por número de ocorrências ou data final.
 *     tags: [Recorrências]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - personId
 *               - userId
 *               - locationId
 *               - serviceTypeId
 *               - recurrenceType
 *               - recurrenceValue
 *               - patterns
 *             properties:
 *               title:
 *                 type: string
 *                 description: Tí<PERSON><PERSON> da recorrência
 *                 example: "Consulta semanal"
 *               description:
 *                 type: string
 *                 description: Descrição detalhada da recorrência
 *                 example: "Consulta de acompanhamento semanal"
 *               personId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da pessoa (cliente) para o agendamento
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do profissional responsável pelo atendimento
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               locationId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do local onde ocorrerá o atendimento
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               serviceTypeId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do tipo de serviço a ser prestado
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               insuranceId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do convênio (opcional)
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               branchId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da unidade/filial (opcional)
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               recurrenceType:
 *                 type: string
 *                 enum: [OCCURRENCES, END_DATE]
 *                 description: Tipo de recorrência (por número de ocorrências ou data final)
 *                 example: "OCCURRENCES"
 *               recurrenceValue:
 *                 type: string
 *                 description: |
 *                   Valor da recorrência. Se recurrenceType for OCCURRENCES, deve ser um número.
 *                   Se for END_DATE, deve ser uma data no formato ISO.
 *                 example: "10"
 *               sequentialAppointments:
 *                 type: integer
 *                 description: Número de agendamentos sequenciais (padrão é 1)
 *                 default: 1
 *                 example: 1
 *               patterns:
 *                 type: array
 *                 description: Padrões de dias da semana e horários para a recorrência
 *                 items:
 *                   type: object
 *                   required:
 *                     - dayOfWeek
 *                     - startTime
 *                     - endTime
 *                   properties:
 *                     dayOfWeek:
 *                       type: integer
 *                       minimum: 0
 *                       maximum: 6
 *                       description: Dia da semana (0 = Domingo, 1 = Segunda, ..., 6 = Sábado)
 *                       example: 1
 *                     startTime:
 *                       type: string
 *                       pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
 *                       description: Hora de início no formato HH:MM
 *                       example: "14:00"
 *                     endTime:
 *                       type: string
 *                       pattern: "^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$"
 *                       description: Hora de término no formato HH:MM
 *                       example: "15:00"
 *     responses:
 *       201:
 *         description: Recorrência criada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 recurrence:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     title:
 *                       type: string
 *                     description:
 *                       type: string
 *                     recurrenceType:
 *                       type: string
 *                     numberOfOccurrences:
 *                       type: integer
 *                     endDate:
 *                       type: string
 *                       format: date-time
 *                     patterns:
 *                       type: array
 *                       items:
 *                         type: object
 *                 schedulings:
 *                   type: array
 *                   description: Lista de agendamentos criados
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       startDate:
 *                         type: string
 *                         format: date-time
 *                       endDate:
 *                         type: string
 *                         format: date-time
 *       400:
 *         description: Dados inválidos ou conflito de horários
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 conflictDate:
 *                   type: string
 *                   format: date-time
 *                 reason:
 *                   type: string
 *                   enum: [CONFLICT, HOLIDAY]
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista recorrências
 *     description: Retorna uma lista paginada de recorrências
 *     tags: [Recorrências]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Número de itens por página
 *     responses:
 *       200:
 *         description: Lista de recorrências
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 recurrences:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       title:
 *                         type: string
 *                       description:
 *                         type: string
 *                       recurrenceType:
 *                         type: string
 *                       numberOfOccurrences:
 *                         type: integer
 *                       endDate:
 *                         type: string
 *                         format: date-time
 *                       patterns:
 *                         type: array
 *                         items:
 *                           type: object
 *                       person:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           fullName:
 *                             type: string
 *                           phone:
 *                             type: string
 *                           email:
 *                             type: string
 *                       schedulings:
 *                         type: array
 *                         items:
 *                           type: object
 *                 total:
 *                   type: integer
 *                   description: Total de registros
 *                 pages:
 *                   type: integer
 *                   description: Total de páginas
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /recurrences/{id}:
 *   put:
 *     summary: Atualiza um agendamento de recorrência
 *     description: |
 *       Atualiza um agendamento específico de uma recorrência.
 *       Permite atualizar apenas o agendamento específico ou todos os agendamentos futuros da recorrência.
 *     tags: [Recorrências]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do agendamento a ser atualizado
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               updateAll:
 *                 type: boolean
 *                 description: Se true, atualiza todos os agendamentos futuros da recorrência
 *                 example: false
 *               title:
 *                 type: string
 *                 description: Novo título para o agendamento
 *               description:
 *                 type: string
 *                 description: Nova descrição para o agendamento
 *               locationId:
 *                 type: string
 *                 format: uuid
 *                 description: Novo local para o agendamento
 *               serviceTypeId:
 *                 type: string
 *                 format: uuid
 *                 description: Novo tipo de serviço para o agendamento
 *               insuranceId:
 *                 type: string
 *                 format: uuid
 *                 description: Novo convênio para o agendamento
 *               startDate:
 *                 type: string
 *                 format: date-time
 *                 description: Nova data/hora de início
 *               endDate:
 *                 type: string
 *                 format: date-time
 *                 description: Nova data/hora de término
 *     responses:
 *       200:
 *         description: Agendamento(s) atualizado(s) com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                 updatedCount:
 *                   type: integer
 *                   description: Número de agendamentos atualizados
 *       400:
 *         description: Dados inválidos ou conflito de horários
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Agendamento não encontrado
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Cancela uma recorrência
 *     description: |
 *       Cancela uma recorrência inteira e todos os seus agendamentos futuros.
 *       Os agendamentos passados não são afetados.
 *     tags: [Recorrências]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da recorrência a ser cancelada
 *     responses:
 *       200:
 *         description: Recorrência cancelada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Recorrência cancelada com sucesso"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Recorrência não encontrada
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */