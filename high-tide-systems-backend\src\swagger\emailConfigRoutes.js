// src/swagger/emailConfigRoutes.js

/**
 * @swagger
 * tags:
 *   name: Config. Email
 *   description: Configurações de envio de email
 */

/**
 * @swagger
 * /email-config:
 *   post:
 *     summary: Cria uma nova configuração de email
 *     description: |
 *       Cria uma nova configuração de email para o sistema.
 *       A configuração é testada antes de ser salva.
 *       Se uma configuração existente estiver ativa para a mesma empresa, ela será desativada.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - smtpHost
 *               - smtpPort
 *               - smtpUser
 *               - smtpPassword
 *               - emailFromName
 *               - emailFromAddress
 *             properties:
 *               smtpHost:
 *                 type: string
 *                 description: Servidor SMTP
 *                 example: "smtp.gmail.com"
 *               smtpPort:
 *                 type: integer
 *                 description: Porta SMTP
 *                 example: 587
 *               smtpSecure:
 *                 type: boolean
 *                 description: Usar conexão segura (TLS/SSL)
 *                 default: false
 *               smtpUser:
 *                 type: string
 *                 description: Usuário SMTP
 *                 example: "<EMAIL>"
 *               smtpPassword:
 *                 type: string
 *                 format: password
 *                 description: Senha SMTP
 *               emailFromName:
 *                 type: string
 *                 description: Nome do remetente
 *                 example: "Sistema Clínica Exemplo"
 *               emailFromAddress:
 *                 type: string
 *                 format: email
 *                 description: Email do remetente
 *                 example: "<EMAIL>"
 *               companyId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da empresa (opcional)
 *     responses:
 *       201:
 *         description: Configuração criada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/EmailConfig'
 *                 - type: object
 *                   properties:
 *                     smtpPassword:
 *                       not: {}
 *       400:
 *         description: Dados inválidos ou erro de conexão SMTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Não foi possível conectar ao servidor SMTP"
 *                 error:
 *                   type: string
 *                   example: "Authentication failed"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Empresa não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Empresa não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista configurações de email
 *     description: |
 *       Retorna uma lista de configurações de email.
 *       Se o parâmetro companyId for fornecido, filtra por empresa.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: companyId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da empresa (opcional)
 *     responses:
 *       200:
 *         description: Lista de configurações de email
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 allOf:
 *                   - $ref: '#/components/schemas/EmailConfig'
 *                   - type: object
 *                     properties:
 *                       smtpPassword:
 *                         not: {}
 *                       company:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           name:
 *                             type: string
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /email-config/active:
 *   get:
 *     summary: Obtém a configuração de email ativa
 *     description: |
 *       Retorna a configuração de email ativa.
 *       Se o parâmetro companyId for fornecido, busca a configuração ativa para aquela empresa.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: companyId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da empresa (opcional)
 *     responses:
 *       200:
 *         description: Configuração de email ativa
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/EmailConfig'
 *                 - type: object
 *                   properties:
 *                     smtpPassword:
 *                       not: {}
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Nenhuma configuração ativa encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Nenhuma configuração de email ativa encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /email-config/test:
 *   post:
 *     summary: Testa envio de email com a configuração ativa
 *     description: |
 *       Envia um email de teste utilizando a configuração ativa.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - testEmail
 *             properties:
 *               testEmail:
 *                 type: string
 *                 format: email
 *                 description: Email para envio do teste
 *     responses:
 *       200:
 *         description: Email enviado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 messageId:
 *                   type: string
 *                   example: "<************.5678@localhost>"
 *                 message:
 *                   type: string
 *                   example: "Email de teste enviado com sucesso"
 *       400:
 *         description: Parâmetros inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Email de teste é obrigatório"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Nenhuma configuração ativa encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Nenhuma configuração ativa encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /email-config/{id}:
 *   put:
 *     summary: Atualiza uma configuração de email
 *     description: |
 *       Atualiza uma configuração de email existente.
 *       A configuração é testada antes de ser atualizada.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da configuração de email
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - smtpHost
 *               - smtpPort
 *               - smtpUser
 *               - smtpPassword
 *               - emailFromName
 *               - emailFromAddress
 *             properties:
 *               smtpHost:
 *                 type: string
 *                 description: Servidor SMTP
 *               smtpPort:
 *                 type: integer
 *                 description: Porta SMTP
 *               smtpSecure:
 *                 type: boolean
 *                 description: Usar conexão segura (TLS/SSL)
 *               smtpUser:
 *                 type: string
 *                 description: Usuário SMTP
 *               smtpPassword:
 *                 type: string
 *                 format: password
 *                 description: Senha SMTP
 *               emailFromName:
 *                 type: string
 *                 description: Nome do remetente
 *               emailFromAddress:
 *                 type: string
 *                 format: email
 *                 description: Email do remetente
 *               active:
 *                 type: boolean
 *                 description: Status da configuração (ativa/inativa)
 *               companyId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da empresa (opcional)
 *     responses:
 *       200:
 *         description: Configuração atualizada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/EmailConfig'
 *                 - type: object
 *                   properties:
 *                     smtpPassword:
 *                       not: {}
 *       400:
 *         description: Dados inválidos ou erro de conexão SMTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Não foi possível conectar ao servidor SMTP"
 *                 error:
 *                   type: string
 *                   example: "Authentication failed"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Configuração ou empresa não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Configuração não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove uma configuração de email
 *     description: |
 *       Remove uma configuração de email.
 *       Não é possível remover a configuração ativa.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da configuração de email
 *     responses:
 *       204:
 *         description: Configuração removida com sucesso
 *       400:
 *         description: Não é possível excluir a configuração ativa
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Não é possível excluir a configuração ativa. Ative outra configuração primeiro."
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Configuração não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Configuração não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /email-config/{id}/test:
 *   post:
 *     summary: Testa uma configuração de email específica
 *     description: |
 *       Envia um email de teste utilizando uma configuração específica.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da configuração de email
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - testEmail
 *             properties:
 *               testEmail:
 *                 type: string
 *                 format: email
 *                 description: Email para envio do teste
 *     responses:
 *       200:
 *         description: Email enviado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 messageId:
 *                   type: string
 *                   example: "<************.5678@localhost>"
 *                 message:
 *                   type: string
 *                   example: "Email de teste enviado com sucesso"
 *       400:
 *         description: Parâmetros inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Email de teste é obrigatório"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Configuração não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Configuração não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /email-config/{id}/activate:
 *   patch:
 *     summary: Ativa uma configuração de email
 *     description: |
 *       Ativa uma configuração de email específica.
 *       A conexão é testada antes da ativação.
 *       Desativa todas as outras configurações da mesma empresa.
 *     tags: [Config. Email]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da configuração de email
 *     responses:
 *       200:
 *         description: Configuração ativada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               allOf:
 *                 - $ref: '#/components/schemas/EmailConfig'
 *                 - type: object
 *                   properties:
 *                     smtpPassword:
 *                       not: {}
 *       400:
 *         description: Erro de conexão SMTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Não foi possível conectar ao servidor SMTP"
 *                 error:
 *                   type: string
 *                   example: "Authentication failed"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Configuração não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Configuração não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */