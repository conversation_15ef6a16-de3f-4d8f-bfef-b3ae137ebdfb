module.exports = {

"[project]/src/utils/moduleRedirection.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "getModulePageOptions": (()=>getModulePageOptions),
    "getModuleRedirectionPath": (()=>getModuleRedirectionPath),
    "getPreferredLandingPage": (()=>getPreferredLandingPage),
    "isFirstVisit": (()=>isFirstVisit),
    "markModuleAsVisited": (()=>markModuleAsVisited),
    "setPreferredLandingPage": (()=>setPreferredLandingPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$api$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/api.js [app-ssr] (ecmascript)");
"use client";
;
const isFirstVisit = (moduleId)=>{
    if ("TURBOPACK compile-time truthy", 1) return true;
    "TURBOPACK unreachable";
};
const markModuleAsVisited = (moduleId)=>{
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
};
const getPreferredLandingPage = (moduleId)=>{
    if ("TURBOPACK compile-time truthy", 1) return null;
    "TURBOPACK unreachable";
};
const setPreferredLandingPage = async (moduleId, pagePath)=>{
    if ("TURBOPACK compile-time truthy", 1) return;
    "TURBOPACK unreachable";
};
const getModuleRedirectionPath = (moduleId, introductionPath, defaultPath)=>{
    // Se for a primeira visita, redireciona para a introdução
    if (isFirstVisit(moduleId)) {
        markModuleAsVisited(moduleId);
        return introductionPath;
    }
    // Verifica se há uma página inicial preferida
    const preferredPage = getPreferredLandingPage(moduleId);
    if (preferredPage) {
        return preferredPage;
    }
    // Caso contrário, usa o caminho padrão
    return defaultPath;
};
const getModulePageOptions = (moduleId)=>{
    switch(moduleId){
        case 'admin':
            return [
                {
                    value: '/dashboard/admin/introduction',
                    label: 'Introdução'
                },
                {
                    value: '/dashboard/admin/dashboard',
                    label: 'Dashboard'
                },
                {
                    value: '/dashboard/admin/users',
                    label: 'Usuários'
                },
                {
                    value: '/dashboard/admin/professions',
                    label: 'Profissões'
                },
                {
                    value: '/dashboard/admin/logs',
                    label: 'Logs'
                },
                {
                    value: '/dashboard/admin/settings',
                    label: 'Configurações'
                }
            ];
        case 'scheduler':
            return [
                {
                    value: '/dashboard/scheduler/introduction',
                    label: 'Introdução'
                },
                {
                    value: '/dashboard/scheduler/calendar',
                    label: 'Calendário'
                },
                {
                    value: '/dashboard/scheduler/working-hours',
                    label: 'Horários de Trabalho'
                },
                {
                    value: '/dashboard/scheduler/service-types',
                    label: 'Tipos de Serviço'
                },
                {
                    value: '/dashboard/scheduler/locations',
                    label: 'Locais'
                },
                {
                    value: '/dashboard/scheduler/appointments-report',
                    label: 'Relatório'
                }
            ];
        case 'people':
            return [
                {
                    value: '/dashboard/people/clients',
                    label: 'Clientes'
                },
                {
                    value: '/dashboard/people/persons',
                    label: 'Pessoas'
                }
            ];
        case 'financial':
            return [
                {
                    value: '/dashboard/financial/invoices',
                    label: 'Faturas'
                },
                {
                    value: '/dashboard/financial/payments',
                    label: 'Pagamentos'
                }
            ];
        default:
            return [];
    }
};
}}),
"[project]/src/app/dashboard/people/page.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "default": (()=>PeopleModuleRoute)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$moduleRedirection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/utils/moduleRedirection.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
function PeopleModuleRoute() {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Obtém o caminho de redirecionamento com base nas preferências do usuário
        const redirectPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$moduleRedirection$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getModuleRedirectionPath"])('people', '/dashboard/people/introduction', '/dashboard/people/clients');
        // Redireciona para o caminho determinado
        router.push(redirectPath);
    }, [
        router
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: "Carregando..."
    }, void 0, false, {
        fileName: "[project]/src/app/dashboard/people/page.js",
        lineNumber: 22,
        columnNumber: 10
    }, this);
}
}}),
"[project]/src/app/dashboard/people/page.js [app-rsc] (ecmascript, Next.js server component, client modules ssr)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=src_780af0._.js.map