// test-cache-service.js
require('dotenv').config();
const cacheService = require('../../src/services/cacheService');

async function testCacheService() {
  console.log('=== TESTE DO SERVIÇO DE CACHE ===\n');

  try {
    // Inicializar o serviço de cache
    console.log('Inicializando serviço de cache...');
    const result = await cacheService.initialize();

    if (result.success) {
      console.log('✅ Serviço de cache inicializado com sucesso');
    } else {
      console.error('❌ Falha ao inicializar serviço de cache:', result.error);
      return;
    }

    // Testar operações básicas
    console.log('\nTestando operações básicas...');

    // SET
    const key = 'test:service';
    const value = { message: 'Teste do serviço de cache', timestamp: new Date().toISOString() };
    console.log(`Armazenando valor com chave "${key}"...`);
    const setResult = await cacheService.set(key, value, 60);

    if (setResult) {
      console.log('✅ Valor armazenado com sucesso');
    } else {
      console.error('❌ Falha ao armazenar valor');
      return;
    }

    // GET
    console.log(`\nRecuperando valor com chave "${key}"...`);
    const storedValue = await cacheService.get(key);

    if (storedValue) {
      console.log('Valor recuperado:', storedValue);
      console.log('✅ Valor recuperado com sucesso');

      // Verificar se o valor recuperado é igual ao valor armazenado
      if (JSON.stringify(storedValue) === JSON.stringify(value)) {
        console.log('✅ Valores são idênticos');
      } else {
        console.error('❌ Valores são diferentes');
        console.log('Valor original:', value);
        console.log('Valor recuperado:', storedValue);
      }
    } else {
      console.error('❌ Falha ao recuperar valor');
      return;
    }

    // Testar getOrSet
    console.log('\nTestando função getOrSet...');
    const getOrSetKey = 'test:getOrSet';

    // Primeira chamada - deve executar a função
    console.log('Primeira chamada - deve executar a função...');
    const firstCallValue = await cacheService.getOrSet(
      getOrSetKey,
      async () => {
#        console.log('✅ Fun
o executada como esperado');
        return { generated: true, value: 'Valor gerado pela função', timestamp: Date.now() };
      },
      60
    );

    console.log('Valor retornado:', firstCallValue);

    // Segunda chamada - deve retornar do cache
    console.log('\nSegunda chamada - deve retornar do cache...');
    const secondCallValue = await cacheService.getOrSet(
      getOrSetKey,
      async () => {
#        console.log('❌ Funç
o executada novamente (não deveria)');
        return { generated: true, value: 'Novo valor gerado', timestamp: Date.now() };
      },
      60
    );

    console.log('Valor retornado:', secondCallValue);

    if (JSON.stringify(firstCallValue) === JSON.stringify(secondCallValue)) {
      console.log('✅ Segunda chamada retornou o valor em cache como esperado');
    } else {
      console.error('❌ Segunda chamada retornou um valor diferente');
    }

    // Testar geração de chave
    console.log('\nTestando geração de chave...');
    const prefix = 'users';
    const params = { userId: 123, role: 'admin', filter: 'active' };

    const generatedKey = cacheService.generateKey(prefix, params);
    console.log(`Chave gerada: ${generatedKey}`);

    if (generatedKey.startsWith(prefix) && generatedKey.includes('userId:123') && generatedKey.includes('role:admin')) {
      console.log('✅ Chave gerada corretamente');
    } else {
      console.error('❌ Chave gerada incorretamente');
    }

    // Testar limpeza por padrão
#    console.log('\nTestando limpeza por padr
o...');

    // Criar várias chaves com o mesmo prefixo
    const patternPrefix = 'test:clear';
    for (let i = 1; i <= 5; i++) {
      await cacheService.set(`${patternPrefix}:${i}`, { index: i }, 60);
    }

    console.log('✅ Criadas 5 chaves com o padrão "test:clear:*"');

    // Limpar todas as chaves com o padrão
    console.log('Limpando todas as chaves com o padrão "test:clear:*"...');
    const clearResult = await cacheService.clear(`${patternPrefix}:*`);

    if (clearResult) {
      console.log('✅ Chaves limpas com sucesso');

      // Verificar se as chaves foram realmente excluídas
      let allDeleted = true;
      for (let i = 1; i <= 5; i++) {
        const value = await cacheService.get(`${patternPrefix}:${i}`);
        if (value !== null) {
          console.error(`❌ Chave "${patternPrefix}:${i}" ainda existe no cache`);
          allDeleted = false;
        }
      }

      if (allDeleted) {
        console.log('✅ Todas as chaves foram excluídas');
      }
    } else {
      console.error('❌ Falha ao limpar chaves');
    }

    // Fechar conexão
    console.log('\nFechando conexão com Redis...');
    await cacheService.close();
    console.log('✅ Conexão fechada com sucesso');

    console.log('\n=== TESTE CONCLUÍDO COM SUCESSO ===');
  } catch (error) {
    console.error('Erro durante o teste:', error);
  }
}

// Executar o teste
testCacheService().catch(error => {
  console.error('Erro fatal durante o teste:', error);
});
