"use client";

import React from 'react';
import { MoreHorizontal } from 'lucide-react';

const MultipleEventsIndicator = ({ count, onClick, isDarkMode }) => {
  return (
    <button
      onClick={onClick}
      className={`absolute bottom-1 right-1 z-10 flex items-center justify-center px-2 py-1 rounded-full text-xs font-medium shadow-sm ${
        isDarkMode
          ? 'bg-indigo-600 text-white hover:bg-indigo-700'
          : 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200'
      }`}
    >
      <MoreHorizontal size={14} className="mr-1" />
      <span>{count}</span>
    </button>
  );
};

export default MultipleEventsIndicator;
