const prisma = require('../utils/prisma');
const timezoneHelper = require('./timezoneHelper');

class RecurrenceHelper {
  /**
   * Verifica se há conflitos de horário para um profissional
   */
  static async checkAvailability(userId, startDate, endDate, excludeSchedulingId = null) {
    console.log(`[checkAvailability] Verificando disponibilidade para usuário ${userId}`);
    console.log(`[checkAvailability] Período: ${startDate.toISOString()} - ${endDate.toISOString()}`);

    if (excludeSchedulingId) {
      console.log(`[checkAvailability] Excluindo agendamento ${excludeSchedulingId} da verificação`);
    }

    const whereCondition = {
      userId,
      OR: [
        {
          AND: [
            { startDate: { lte: endDate } },
            { endDate: { gte: startDate } }
          ]
        }
      ],
      status: {
        notIn: ['CANCELLED', 'COMPLETED']
      }
    };

    // Só adiciona a condição de id se excludeSchedulingId não for null
    if (excludeSchedulingId) {
      whereCondition.id = { not: excludeSchedulingId };
    }

    console.log(`[checkAvailability] Condição de busca: ${JSON.stringify(whereCondition)}`);

    const conflicts = await prisma.scheduling.findFirst({
      where: whereCondition
    });

    if (conflicts) {
      console.log(`[checkAvailability] CONFLITO ENCONTRADO: Agendamento ${conflicts.id} (${conflicts.startDate} - ${conflicts.endDate})`);
    } else {
      console.log(`[checkAvailability] Nenhum conflito encontrado, horário disponível`);
    }

    return !conflicts;
  }

  /**
   * Funções auxiliares para comparações de data
   */
  static isSameDay(date1, date2) {
    const isSame = date1.getFullYear() === date2.getFullYear() &&
           date1.getMonth() === date2.getMonth() &&
           date1.getDate() === date2.getDate();

    if (isSame) {
      console.log(`[isSameDay] As datas ${date1.toISOString()} e ${date2.toISOString()} são o mesmo dia`);
    }

    return isSame;
  }

  /**
   * Gera todas as datas de uma recorrência baseado nos padrões
   * @param {Array} patterns - Padrões de dias e horários
   * @param {Date|string} baseDate - Data base (data do agendamento original)
   * @param {string} recurrenceType - 'OCCURRENCES' ou 'END_DATE'
   * @param {number|string} recurrenceValue - Número de ocorrências ou data final
   * @returns {Array} Array de datas para agendamentos
   */

static async generateRecurrenceDates(patterns, baseDate, recurrenceType, recurrenceValue) {
  console.log(`\n========== INÍCIO: GERAÇÃO DE DATAS RECORRENTES ==========`);
  console.log(`[generateRecurrenceDates] Parâmetros recebidos:`);
  console.log(`- Data base: ${baseDate}`);
  console.log(`- Tipo: ${recurrenceType}`);
  console.log(`- Valor: ${recurrenceValue}`);
  console.log(`- Quantidade de padrões: ${patterns.length}`);

  for (const [i, pattern] of patterns.entries()) {
    if (pattern.startTime && pattern.endTime) {
      console.log(`Padrão ${i+1}: Dia ${pattern.dayOfWeek}, Horário ${pattern.startTime}-${pattern.endTime}`);
    } else if (pattern.startTimeMinutes !== undefined && pattern.endTimeMinutes !== undefined) {
      const startHour = Math.floor(pattern.startTimeMinutes / 60);
      const startMinute = pattern.startTimeMinutes % 60;
      const endHour = Math.floor(pattern.endTimeMinutes / 60);
      const endMinute = pattern.endTimeMinutes % 60;

      console.log(`Padrão ${i+1}: Dia ${pattern.dayOfWeek}, Horário ${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}-${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')} (${pattern.startTimeMinutes}-${pattern.endTimeMinutes} minutos)`);
    } else {
      console.log(`Padrão ${i+1}: Dia ${pattern.dayOfWeek}, Horário não especificado`);
    }
  }

  // Garantir que baseDate seja um objeto Date
  const originalDate = baseDate instanceof Date ? new Date(baseDate) : new Date(baseDate);
  console.log(`[generateRecurrenceDates] Data original normalizada: ${originalDate.toISOString()}`);

  // Obter o companyId para o usuário associado ao primeiro padrão
  let companyId = null;
  if (patterns && patterns.length > 0) {
    try {
      const userId = patterns[0].userId || patterns[0].createdById;
      console.log(`[generateRecurrenceDates] Buscando companyId para userId: ${userId}`);

      if (!userId) {
        console.log(`[generateRecurrenceDates] ALERTA: userId não encontrado nos padrões`);
        // Tentar extrair userId do primeiro padrão
        console.log(`[generateRecurrenceDates] Conteúdo do primeiro padrão:`, JSON.stringify(patterns[0]));
      }

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true }
      });
      companyId = user?.companyId;
      console.log(`[generateRecurrenceDates] CompanyId encontrado: ${companyId}`);
    } catch (error) {
      console.error('[generateRecurrenceDates] Erro ao buscar companyId:', error);
    }
  }

  // Obter o timezone da empresa
  const timezone = await timezoneHelper.getCompanyTimezone(companyId, prisma);
  console.log(`[generateRecurrenceDates] Usando timezone: ${timezone}`);

  // Obter o dia da semana no timezone da empresa
  const dayOfWeek = timezoneHelper.getDayOfWeekInTimezone(originalDate, timezone);
  console.log(`[generateRecurrenceDates] Dia da semana original: ${dayOfWeek} (0=Domingo, 1=Segunda, ...)`);

  // Obter hora e minuto no timezone da empresa
  const dateInTimezone = timezoneHelper.convertToTimezone(originalDate, timezone);
  const originalHour = dateInTimezone.getHours();
  const originalMinute = dateInTimezone.getMinutes();

  // Calcular a duração do agendamento original
  const endDate = baseDate instanceof Date ? baseDate : new Date(baseDate);
  const durationMinutes = Math.round((endDate.getTime() - originalDate.getTime()) / 60000);

  console.log(`[generateRecurrenceDates] Horário no timezone ${timezone}: ${originalHour}:${originalMinute} (duração: ${durationMinutes} minutos)`);

  let dates = [];
  let maxCycles;
  let endDateLimit;

  // Determinar limites baseados no tipo de recorrência
  if (recurrenceType === 'END_DATE') {
    endDateLimit = new Date(recurrenceValue);
    maxCycles = 100; // Limitado pela data final
    console.log(`[generateRecurrenceDates] Limite de data final: ${endDateLimit.toISOString()}`);
    console.log(`[generateRecurrenceDates] Máximo de ciclos definido como 100 (limitado por data final)`);
  } else if (recurrenceType === 'OCCURRENCES') {
    // Use exatamente o número de ciclos especificado
    maxCycles = parseInt(recurrenceValue, 10);
    console.log(`[generateRecurrenceDates] Máximo de ciclos definido como ${maxCycles} (valor especificado)`);

    // Para OCCURRENCES, definimos uma data limite distante
    endDateLimit = new Date(originalDate);
    endDateLimit.setFullYear(endDateLimit.getFullYear() + 5); // 5 anos é suficiente
    console.log(`[generateRecurrenceDates] Limite de data final (5 anos): ${endDateLimit.toISOString()}`);
  }

  // Encontrar a data de início da semana atual (Domingo)
  const currentWeekStart = new Date(originalDate);
  currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay());
  currentWeekStart.setHours(0, 0, 0, 0);

  console.log(`[generateRecurrenceDates] Data de início da semana atual: ${currentWeekStart.toISOString()}`);

  // Ciclo atual começa da semana atual
  let currentCycleStart = new Date(currentWeekStart);

  // Enquanto não atingirmos o número de ciclos ou a data limite
  console.log(`\n[generateRecurrenceDates] Iniciando processamento de ciclos semanais...`);

  let cycleCount = 0;

  while (cycleCount < maxCycles && currentCycleStart <= endDateLimit) {
    console.log(`\n[generateRecurrenceDates] Processando ciclo ${cycleCount + 1}/${maxCycles}`);
    console.log(`[generateRecurrenceDates] Início do ciclo: ${currentCycleStart.toISOString()}`);

    // Para cada padrão (dia da semana) neste ciclo
    for (const [patternIndex, pattern] of patterns.entries()) {
      const dayOfWeek = pattern.dayOfWeek;
      console.log(`\n[generateRecurrenceDates] Processando padrão ${patternIndex + 1}/${patterns.length}: dia ${dayOfWeek}`);

      // Calcular a data para este dia da semana no ciclo atual
      const dateForPattern = new Date(currentCycleStart);
      dateForPattern.setDate(dateForPattern.getDate() + dayOfWeek);
      console.log(`[generateRecurrenceDates] Data calculada para este padrão: ${dateForPattern.toISOString()}`);

      // CORREÇÃO CRÍTICA: Sempre pular o dia original
      if (this.isSameDay(dateForPattern, originalDate)) {
        console.log(`[generateRecurrenceDates] *** PULANDO: É a data original (${dateForPattern.toISOString()}) ***`);
        continue;
      }

      // Pular datas antes da data original
      if (dateForPattern < originalDate) {
        console.log(`[generateRecurrenceDates] *** PULANDO: Data anterior à original (${dateForPattern.toISOString()} < ${originalDate.toISOString()}) ***`);
        continue;
      }

      // Se esta data for posterior à data limite, pule
      if (recurrenceType === 'END_DATE' && dateForPattern > endDateLimit) {
        console.log(`[generateRecurrenceDates] *** PULANDO: Data além do limite final (${dateForPattern.toISOString()} > ${endDateLimit.toISOString()}) ***`);
        continue;
      }

      console.log(`[generateRecurrenceDates] ✓ Data válida para agendamento: ${dateForPattern.toISOString()}`);

      // Usar o horário do padrão de recorrência
      let patternHour = originalHour;
      let patternMinute = originalMinute;

      // Se o padrão tem horário específico, usar ele
      if (pattern.startTime) {
        const timeParts = pattern.startTime.split(':');
        if (timeParts.length === 2) {
          patternHour = parseInt(timeParts[0], 10);
          patternMinute = parseInt(timeParts[1], 10);
        }
      } else if (pattern.startTimeMinutes !== undefined) {
        // Se temos startTimeMinutes, calcular hora e minuto
        patternHour = Math.floor(pattern.startTimeMinutes / 60);
        patternMinute = pattern.startTimeMinutes % 60;
      }

      console.log(`[generateRecurrenceDates] Usando horário do padrão: ${patternHour}:${patternMinute}`);

      // Criar a data com o horário do padrão
      // Importante: Precisamos ajustar o horário para UTC para manter a consistência
      const startDateTime = new Date(dateForPattern);

      // Ajustar para UTC: Se estamos no fuso -3, e queremos 13:45 local, precisamos armazenar 16:45 UTC
      // Obter o offset do fuso horário em minutos
      // IMPORTANTE: Estamos forçando o offset para -180 minutos (UTC-3) para o Brasil
      // porque o servidor pode estar em um fuso horário diferente
      const timezoneOffsetMinutes = -180; // Forçando para UTC-3 (Brasil)

      // Ajustar o horário considerando o fuso horário
      // getTimezoneOffset() retorna a diferença em minutos entre UTC e hora local
      // Um valor positivo significa que o horário local está atrás do UTC
      // Para o Brasil (UTC-3), o offset é -180 minutos
      // Isso significa que para converter de horário local para UTC, precisamos adicionar 3 horas
      // Por exemplo, 13:45 no Brasil é 16:45 em UTC

      // Calcular o ajuste de horas e minutos
      // Como timezoneOffsetMinutes é negativo (-180), precisamos subtrair
      const utcHour = patternHour - Math.floor(timezoneOffsetMinutes / 60);
      const utcMinute = patternMinute - (timezoneOffsetMinutes % 60);

      console.log(`[generateRecurrenceDates] Ajustando horário local ${patternHour}:${patternMinute} para UTC ${utcHour}:${utcMinute} (offset: ${timezoneOffsetMinutes} minutos)`);
      console.log(`[generateRecurrenceDates] Hora local: ${patternHour}, Minuto local: ${patternMinute}`);
      console.log(`[generateRecurrenceDates] Ajuste de horas: ${-Math.floor(timezoneOffsetMinutes / 60)}, Ajuste de minutos: ${-(timezoneOffsetMinutes % 60)}`);
      console.log(`[generateRecurrenceDates] Hora UTC: ${utcHour}, Minuto UTC: ${utcMinute}`);

      // Definir o horário UTC diretamente
      startDateTime.setUTCHours(utcHour, utcMinute, 0, 0);

      // Calcular a data de término
      let endDateTime;

      // Se o padrão tem horário de término específico, usar ele
      if (pattern.endTime) {
        const timeParts = pattern.endTime.split(':');
        if (timeParts.length === 2) {
          const endHour = parseInt(timeParts[0], 10);
          const endMinute = parseInt(timeParts[1], 10);

          endDateTime = new Date(dateForPattern);

          // Ajustar para UTC da mesma forma que fizemos com a data de início
          // Usando o mesmo offset forçado para UTC-3 (Brasil)
          const utcEndHour = endHour - Math.floor(timezoneOffsetMinutes / 60);
          const utcEndMinute = endMinute - (timezoneOffsetMinutes % 60);

          console.log(`[generateRecurrenceDates] Ajustando horário de término local ${endHour}:${endMinute} para UTC ${utcEndHour}:${utcEndMinute} (offset: ${timezoneOffsetMinutes} minutos)`);
          console.log(`[generateRecurrenceDates] Hora local de término: ${endHour}, Minuto local de término: ${endMinute}`);
          console.log(`[generateRecurrenceDates] Ajuste de horas: ${-Math.floor(timezoneOffsetMinutes / 60)}, Ajuste de minutos: ${-(timezoneOffsetMinutes % 60)}`);
          console.log(`[generateRecurrenceDates] Hora UTC de término: ${utcEndHour}, Minuto UTC de término: ${utcEndMinute}`);

          endDateTime.setUTCHours(utcEndHour, utcEndMinute, 0, 0);
        } else {
          // Fallback: calcular com base na duração
          endDateTime = new Date(startDateTime.getTime() + durationMinutes * 60000);
        }
      } else if (pattern.endTimeMinutes !== undefined) {
        // Se temos endTimeMinutes, calcular hora e minuto
        const endHour = Math.floor(pattern.endTimeMinutes / 60);
        const endMinute = pattern.endTimeMinutes % 60;

        endDateTime = new Date(dateForPattern);

        // Ajustar para UTC da mesma forma que fizemos com a data de início
        // Usando o mesmo offset forçado para UTC-3 (Brasil)
        const utcEndHour = endHour - Math.floor(timezoneOffsetMinutes / 60);
        const utcEndMinute = endMinute - (timezoneOffsetMinutes % 60);

        console.log(`[generateRecurrenceDates] Ajustando horário de término local ${endHour}:${endMinute} para UTC ${utcEndHour}:${utcEndMinute} (offset: ${timezoneOffsetMinutes} minutos)`);
        console.log(`[generateRecurrenceDates] Hora local de término: ${endHour}, Minuto local de término: ${endMinute}`);
        console.log(`[generateRecurrenceDates] Ajuste de horas: ${-Math.floor(timezoneOffsetMinutes / 60)}, Ajuste de minutos: ${-(timezoneOffsetMinutes % 60)}`);
        console.log(`[generateRecurrenceDates] Hora UTC de término: ${utcEndHour}, Minuto UTC de término: ${utcEndMinute}`);

        endDateTime.setUTCHours(utcEndHour, utcEndMinute, 0, 0);
      } else {
        // Fallback: calcular com base na duração
        endDateTime = new Date(startDateTime.getTime() + durationMinutes * 60000);
      }

      console.log(`[generateRecurrenceDates] Data/hora final calculada:`);
      console.log(`- Início: ${startDateTime.toISOString()} (${startDateTime.toString()})`);
      console.log(`- Fim:    ${endDateTime.toISOString()} (${endDateTime.toString()})`);

      // Adicionar à lista de datas
      dates.push({
        startDate: startDateTime,
        endDate: endDateTime,
        dayOfWeek: pattern.dayOfWeek
      });

      console.log(`[generateRecurrenceDates] ✓ Data adicionada à lista (ciclo ${cycleCount + 1}, total ${dates.length} datas)`);
    }

    // Avançar para o próximo ciclo (próxima semana)
    cycleCount++;
    currentCycleStart.setDate(currentCycleStart.getDate() + 7);
    console.log(`[generateRecurrenceDates] Avançando para próximo ciclo, nova data de início: ${currentCycleStart.toISOString()}`);
  }

  console.log(`\n[generateRecurrenceDates] Processamento de ciclos concluído após ${cycleCount} ciclos`);

  // Ordenar as datas cronologicamente
  dates.sort((a, b) => a.startDate.getTime() - b.startDate.getTime());

  console.log(`\n[generateRecurrenceDates] Datas ordenadas:`);
  for (const [i, date] of dates.entries()) {
    console.log(`Data ${i+1}: ${date.startDate.toISOString()} - ${date.endDate.toISOString()} (${date.dayOfWeek})`);
  }

  console.log(`\n[generateRecurrenceDates] RESULTADO: ${dates.length} datas geradas em ${cycleCount} ciclos`);
  console.log(`========== FIM: GERAÇÃO DE DATAS RECORRENTES ==========\n`);

  return dates;
}

  /**
   * Verifica se uma data é feriado
   */
  static async isHoliday(date) {
    // IMPLEMENTAR UMA LÓGICA DE FERIADOS
    return false;
  }

  /**
   * Verifica se há conflitos de horário para um paciente
   */
  static async checkPatientAvailability(personId, startDate, endDate, excludeSchedulingId = null) {
    console.log(`[checkPatientAvailability] Verificando disponibilidade para paciente ${personId}`);
    console.log(`[checkPatientAvailability] Período: ${startDate.toISOString()} - ${endDate.toISOString()}`);

    if (excludeSchedulingId) {
      console.log(`[checkPatientAvailability] Excluindo agendamento ${excludeSchedulingId} da verificação`);
    }

    const whereCondition = {
      Person: {
        some: {
          id: personId
        }
      },
      OR: [
        {
          AND: [
            { startDate: { lte: endDate } },
            { endDate: { gte: startDate } }
          ]
        }
      ],
      status: {
        notIn: ['CANCELLED', 'COMPLETED']
      }
    };

    // Só adiciona a condição de id se excludeSchedulingId não for null
    if (excludeSchedulingId) {
      whereCondition.id = { not: excludeSchedulingId };
    }

    console.log(`[checkPatientAvailability] Condição de busca: ${JSON.stringify(whereCondition)}`);

    const conflicts = await prisma.scheduling.findFirst({
      where: whereCondition
    });

    if (conflicts) {
      console.log(`[checkPatientAvailability] CONFLITO ENCONTRADO: Agendamento ${conflicts.id} (${conflicts.startDate} - ${conflicts.endDate})`);
    } else {
      console.log(`[checkPatientAvailability] Nenhum conflito encontrado, horário disponível`);
    }

    return !conflicts;
  }

  /**
   * Verifica disponibilidade para toda uma recorrência
   */
  static async checkRecurrenceAvailability(userId, personId, dates, excludeId = null) {
    console.log(`\n========== INÍCIO: VERIFICAÇÃO DE DISPONIBILIDADE ==========`);
    console.log(`[checkRecurrenceAvailability] Verificando disponibilidade para usuário ${userId} e paciente ${personId} em ${dates.length} datas`);

    if (excludeId) {
      console.log(`[checkRecurrenceAvailability] Excluindo agendamento ${excludeId} da verificação`);
    }

    for (const [i, date] of dates.entries()) {
      console.log(`\n[checkRecurrenceAvailability] Verificando data ${i+1}/${dates.length}: ${date.startDate.toISOString()} - ${date.endDate.toISOString()}`);

      // Verificar disponibilidade do profissional
      const isProviderAvailable = await this.checkAvailability(
        userId,
        date.startDate,
        date.endDate,
        excludeId
      );

      // Verificar disponibilidade do paciente
      const isPatientAvailable = await this.checkPatientAvailability(
        personId,
        date.startDate,
        date.endDate,
        excludeId
      );

      const isHoliday = await this.isHoliday(date.startDate);

      if (isHoliday) {
        console.log(`[checkRecurrenceAvailability] ✗ CONFLITO: Data ${date.startDate.toISOString()} é um feriado`);
        console.log(`========== FIM: VERIFICAÇÃO DE DISPONIBILIDADE ==========\n`);
        return {
          available: false,
          conflictDate: date.startDate,
          reason: 'HOLIDAY'
        };
      }

      if (!isProviderAvailable) {
        console.log(`[checkRecurrenceAvailability] ✗ CONFLITO: Usuário ${userId} já tem agendamento para ${date.startDate.toISOString()}`);
        console.log(`========== FIM: VERIFICAÇÃO DE DISPONIBILIDADE ==========\n`);
        return {
          available: false,
          conflictDate: date.startDate,
          reason: 'PROVIDER_SCHEDULE_CONFLICT'
        };
      }

      if (!isPatientAvailable) {
        console.log(`[checkRecurrenceAvailability] ✗ CONFLITO: Paciente ${personId} já tem agendamento para ${date.startDate.toISOString()}`);
        console.log(`========== FIM: VERIFICAÇÃO DE DISPONIBILIDADE ==========\n`);
        return {
          available: false,
          conflictDate: date.startDate,
          reason: 'PATIENT_SCHEDULE_CONFLICT'
        };
      }

      console.log(`[checkRecurrenceAvailability] ✓ Data ${i+1} disponível`);
    }

    console.log(`\n[checkRecurrenceAvailability] ✓ SUCESSO: Todas as ${dates.length} datas estão disponíveis`);
    console.log(`========== FIM: VERIFICAÇÃO DE DISPONIBILIDADE ==========\n`);

    return {
      available: true
    };
  }

  /**
   * Verifica a disponibilidade de um profissional para um período específico
   * Também verifica horário de trabalho e pausas
   */
  static async checkProviderAvailability(userId, startDate, endDate, excludeId = null) {
    console.log(`\n[checkProviderAvailability] Verificando disponibilidade avançada para usuário ${userId}`);
    console.log(`[checkProviderAvailability] Período: ${startDate.toISOString()} - ${endDate.toISOString()}`);

    if (excludeId) {
      console.log(`[checkProviderAvailability] Excluindo agendamento ${excludeId} da verificação`);
    }

    try {
      // 1. Verificar se já existe agendamento
      console.log(`[checkProviderAvailability] Verificando conflitos de agendamento...`);
      const isAvailable = await this.checkAvailability(userId, startDate, endDate, excludeId);
      if (!isAvailable) {
        console.log(`[checkProviderAvailability] ✗ Conflito de agendamento encontrado`);
        return {
          available: false,
          reason: "Já existe um agendamento neste horário"
        };
      }

      // 2. Verificar se é feriado
      console.log(`[checkProviderAvailability] Verificando feriados...`);
      const isHoliday = await this.isHoliday(startDate);
      if (isHoliday) {
        console.log(`[checkProviderAvailability] ✗ Data é um feriado`);
        return {
          available: false,
          reason: "A data selecionada é um feriado"
        };
      }

      // 3. Verificar horário de trabalho
      console.log(`[checkProviderAvailability] Verificando horário de trabalho...`);
      const dayOfWeek = startDate.getDay();
      const startTimeMinutes = startDate.getHours() * 60 + startDate.getMinutes();
      const endTimeMinutes = endDate.getHours() * 60 + endDate.getMinutes();

      console.log(`[checkProviderAvailability] Dia da semana: ${dayOfWeek}, Horário: ${startTimeMinutes} - ${endTimeMinutes} minutos`);

      const workingHours = await prisma.workingHours.findFirst({
        where: {
          userId,
          dayOfWeek,
          isActive: true
        }
      });

      if (!workingHours) {
        console.log(`[checkProviderAvailability] ✗ Usuário não trabalha neste dia da semana`);
        return {
          available: false,
          reason: "O profissional não trabalha neste dia da semana"
        };
      }

      console.log(`[checkProviderAvailability] Horário de trabalho encontrado: ${workingHours.startTimeMinutes} - ${workingHours.endTimeMinutes} minutos`);

      if (startTimeMinutes < workingHours.startTimeMinutes ||
          endTimeMinutes > workingHours.endTimeMinutes) {
        console.log(`[checkProviderAvailability] ✗ Horário solicitado está fora da jornada de trabalho`);
        return {
          available: false,
          reason: "O horário está fora da jornada de trabalho do profissional"
        };
      }

      // 4. Verificar se não está em período de pausa
      if (workingHours.breakStartMinutes && workingHours.breakEndMinutes) {
        console.log(`[checkProviderAvailability] Verificando conflito com pausa: ${workingHours.breakStartMinutes} - ${workingHours.breakEndMinutes} minutos`);

        if (!(endTimeMinutes <= workingHours.breakStartMinutes ||
            startTimeMinutes >= workingHours.breakEndMinutes)) {
          console.log(`[checkProviderAvailability] ✗ Horário conflita com período de pausa`);
          return {
            available: false,
            reason: "O horário conflita com o período de pausa do profissional"
          };
        }
      }

      console.log(`[checkProviderAvailability] ✓ Horário disponível para agendamento`);
      return {
        available: true
      };
    } catch (error) {
      console.error(`[checkProviderAvailability] ERRO ao verificar disponibilidade:`, error);
      return {
        available: false,
        reason: "Erro ao verificar disponibilidade"
      };
    }
  }
}

module.exports = RecurrenceHelper;