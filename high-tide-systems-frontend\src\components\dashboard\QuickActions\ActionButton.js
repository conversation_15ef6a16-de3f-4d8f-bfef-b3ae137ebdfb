'use client';

import React from 'react';
import { ChevronRight } from 'lucide-react';

// Array de módulos para rotacionar entre estilos de ações
const actionModules = ['scheduler', 'people', 'scheduler'];

export const ActionButton = ({ action, index, onClick }) => {
  // Utiliza um dos módulos para o estilo desta ação
  const moduleId = actionModules[index % actionModules.length];
  
  return (
    <button
      onClick={() => onClick(action.path)}
      className={`
        p-4 rounded-xl flex items-center justify-between
        shadow-md dark:shadow-lg dark:shadow-black/20 transition-all duration-300 
        border-2 border-module-${moduleId}-border dark:border-module-${moduleId}-border-dark
        hover:shadow-xl dark:hover:shadow-lg dark:hover:shadow-black/30 transform hover:-translate-y-1
        bg-white dark:bg-gray-800
      `}
      aria-label={action.title}
    >
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg bg-module-${moduleId}-bg dark:bg-module-${moduleId}-bg-dark/70`} aria-hidden="true">
          <action.icon size={20} className={`text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark`} />
        </div>
        <div className="text-left">
          <span className="font-bold text-lg text-gray-800 dark:text-gray-100">{action.title}</span>
          <p className="text-xs mt-0.5 text-gray-600 dark:text-gray-300">{action.description}</p>
        </div>
      </div>
      <ChevronRight 
        size={18} 
        className={`text-module-${moduleId}-icon dark:text-module-${moduleId}-icon-dark transition-transform group-hover:translate-x-1`} 
        aria-hidden="true" 
      />
    </button>
  );
};

export default ActionButton;