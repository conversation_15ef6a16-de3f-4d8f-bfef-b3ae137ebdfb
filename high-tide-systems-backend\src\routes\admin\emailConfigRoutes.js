// src/routes/emailConfigRoutes.js
const express = require("express");
const router = express.Router();
const {
  EmailConfigController,
  emailConfigValidation,
} = require("../../controllers/emailConfigController");
const { authenticate } = require('../../middlewares/auth');

// All routes require authentication
router.use(authenticate);

// Email configuration routes
router.get("/", EmailConfigController.list);
router.get("/:id", EmailConfigController.get);
router.post("/", emailConfigValidation, EmailConfigController.create);
router.put("/:id", emailConfigValidation, EmailConfigController.update);
router.delete("/:id", EmailConfigController.delete);
router.patch("/:id/toggle-status", EmailConfigController.toggleStatus);
router.post("/:id/test", EmailConfigController.testEmail);

module.exports = router;
