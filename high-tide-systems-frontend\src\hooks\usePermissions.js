'use client';

import { useCallback } from 'react';
import { usePermission } from '@/contexts/PermissionContext';

export function usePermissions() {
  const permission = usePermission();

  // Verificar se a rota é acessível baseado em permissões
  const isRouteAccessible = useCallback(
    (requiredPermission, requireAll = false) => {
      if (!requiredPermission) return true;

      if (Array.isArray(requiredPermission)) {
        return requireAll
          ? permission.canAll(requiredPermission)
          : permission.canAny(requiredPermission);
      }

      return permission.can(requiredPermission);
    },
    [permission]
  );

  // Verificar se o módulo é acessível
  const isModuleAccessible = useCallback(
    (requiredModule, requireAll = false) => {
      // Admin tem acesso a todos os módulos
      if (permission.isAdmin()) return true;

      if (!requiredModule) return true;

      if (Array.isArray(requiredModule)) {
        return requireAll
          ? requiredModule.every(m => permission.hasModule(m))
          : requiredModule.some(m => permission.hasModule(m));
      }

      return permission.hasModule(requiredModule);
    },
    [permission]
  );

  // Função para verificar permissão específica dentro de um módulo
  const checkModuleAction = useCallback(
    (moduleId, action) => {
      // Verificar primeiro se o usuário tem acesso ao módulo
      if (!permission.hasModule(moduleId) && !permission.isAdmin()) {
        return false;
      }

      // Verificar permissão específica
      const permissionId = `${moduleId.toLowerCase()}.${action}`;
      return permission.can(permissionId);
    },
    [permission]
  );

  return {
    ...permission,
    isRouteAccessible,
    isModuleAccessible,
    checkModuleAction
  };
}