'use client';

import React from 'react';
import { Calendar, Loader2, AlertCircle, PlusCircle, ArrowRight } from 'lucide-react';
import AppointmentItem from './AppointmentItem';

export const AppointmentList = ({ appointments, isLoading, error }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-xl dark:shadow-lg dark:shadow-black/30 overflow-hidden"
      style={{ boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(0, 0, 0, 0.05)' }}>
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 dark:from-primary-700 dark:to-primary-800 px-6 py-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center">
            <Calendar className="mr-3" size={22} aria-hidden="true" />
            Próximos Agendamentos
          </h3>
          {appointments.length > 0 && (
            <span className="px-3 py-1 rounded-full bg-white bg-opacity-20 text-white text-sm font-medium backdrop-blur-sm">
              {appointments.length} {appointments.length === 1 ? 'agendamento' : 'agendamentos'}
            </span>
          )}
        </div>
      </div>

      <div className="p-6">
        {isLoading ? (
          <div className="flex justify-center items-center py-10" aria-live="polite">
            <Loader2 className="animate-spin text-primary-500 dark:text-primary-400 mr-3" size={28} aria-hidden="true" />
            <p className="text-gray-600 dark:text-gray-300 text-lg">Carregando agendamentos...</p>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center p-6 bg-rose-50 dark:bg-rose-900/20 rounded-lg border border-rose-200 dark:border-rose-800/50" 
               role="alert" 
               aria-live="assertive">
            <AlertCircle className="text-rose-500 dark:text-rose-400 mr-3" size={22} aria-hidden="true" />
            <p className="text-rose-700 dark:text-rose-300">Erro ao carregar agendamentos: {error}</p>
          </div>
        ) : appointments.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center" aria-live="polite">
            <div className="w-20 h-20 mb-4 rounded-full bg-primary-50 dark:bg-primary-900/30 flex items-center justify-center" aria-hidden="true">
              <Calendar className="text-primary-400 dark:text-primary-300" size={32} />
            </div>
            <p className="text-gray-800 dark:text-gray-100 text-lg font-medium mb-2">Sua agenda está vazia</p>
            <p className="text-gray-500 dark:text-gray-400 max-w-md mb-6">Não há agendamentos nos próximos dias. Que tal adicionar um novo agendamento?</p>
            <button 
              className="px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-full flex items-center hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
              aria-label="Criar novo agendamento"
            >
              <PlusCircle size={18} className="mr-2" aria-hidden="true" />
              Novo Agendamento
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {appointments.map((appointment) => (
              <AppointmentItem key={appointment.id} appointment={appointment} />
            ))}

            <div className="pt-4 text-center">
              <button 
                className="px-4 py-2 bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 text-primary-600 dark:text-primary-400 rounded-lg text-sm font-medium flex items-center justify-center mx-auto transition-colors"
                aria-label="Ver todos os agendamentos"
              >
                Ver todos os agendamentos
                <ArrowRight size={16} className="ml-2" aria-hidden="true" />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppointmentList;