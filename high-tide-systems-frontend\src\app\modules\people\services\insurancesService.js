// app/modules/people/services/insurancesService.js
import { api } from "@/utils/api";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import { extractData, extractEntity } from "@/utils/apiResponseAdapter";
import { exportService } from "@/app/services/exportService";

export const insurancesService = {
  // Obter lista de convênios (com suporte a filtragem)
  getInsurances: async ({ search, companyId, insuranceIds, page = 1, limit = 10 } = {}) => {
    try {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (companyId) params.append('companyId', companyId);
      if (page) params.append('page', page);
      if (limit) params.append('limit', limit);

      // Adicionar insuranceIds como parâmetros separados com notação de array
      if (insuranceIds && insuranceIds.length > 0) {
        // Garantir que insuranceIds seja um array
        const insuranceIdsArray = Array.isArray(insuranceIds) ? insuranceIds : [insuranceIds];

        // Adicionar cada ID como um parâmetro separado
        insuranceIdsArray.forEach((id, index) => {
          // Usar a notação de array para compatibilidade com a API
          params.append(`insuranceIds[${index}]`, id);
        });

        console.log("Filtrando por múltiplos IDs de convênios:", insuranceIdsArray);
      }

      const response = await api.get(`/insurances?${params.toString()}`);

      // Usar o adaptador para extrair os dados de forma consistente
      // Primeiro tentamos extrair usando o formato padrão
      const extracted = extractData(response.data, 'insurances', ['data']);

      // Se não houver insurances no formato padrão, processamos o array diretamente
      if (extracted.insurances && extracted.insurances.length > 0) {
        return extracted;
      } else {
        // Processar o array diretamente se a API retornar apenas um array
        const insurances = Array.isArray(response.data) ? response.data : [];
        return {
          insurances,
          total: insurances.length,
          pages: Math.ceil(insurances.length / limit)
        };
      }
    } catch (error) {
      console.error("Erro ao buscar convênios:", error);
      throw error;
    }
  },

  // Obter um convênio específico
  getInsurance: async (id) => {
    try {
      const response = await api.get(`/insurances/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar convênio ${id}:`, error);
      throw error;
    }
  },

  // Obter um convênio pelo ID (versão simplificada que não lança erro)
  getInsuranceById: async (id) => {
    try {
      console.log(`Buscando convênio com ID: ${id}`);
      const response = await api.get(`/insurances/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar convênio com ID ${id}:`, error);
      return null;
    }
  },

  // Criar um novo convênio
  createInsurance: async (data) => {
    try {
      const response = await api.post('/insurances', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar convênio:", error);
      throw error;
    }
  },

  // Atualizar um convênio existente
  updateInsurance: async (id, data) => {
    try {
      const response = await api.put(`/insurances/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar convênio ${id}:`, error);
      throw error;
    }
  },

  // Excluir um convênio
  deleteInsurance: async (id) => {
    try {
      await api.delete(`/insurances/${id}`);
      return true;
    } catch (error) {
      console.error(`Erro ao excluir convênio ${id}:`, error);
      throw error;
    }
  },

  // Adicionar um convênio a uma pessoa
  addPersonInsurance: async (data) => {
    try {
      const response = await api.post('/insurances/person', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao adicionar convênio à pessoa:", error);
      throw error;
    }
  },

  // Remover um convênio de uma pessoa
  removePersonInsurance: async (personId, insuranceId) => {
    try {
      await api.delete(`/insurances/person/${personId}/${insuranceId}`);
      return true;
    } catch (error) {
      console.error("Erro ao remover convênio da pessoa:", error);
      throw error;
    }
  },

  // Listar convênios de uma pessoa - VERSÃO MELHORADA
  listPersonInsurances: async (personId) => {
    try {
      console.log(`Buscando convênios para a pessoa ID: ${personId}`);
      const response = await api.get(`/insurances/person/${personId}`);

      // Detecta a estrutura retornada e normaliza
      const data = response.data;

      // Log para debug da estrutura de dados
      console.log(`Resposta original da API (convenios pessoa ${personId}):`, data);

      // Se for array, retorna diretamente
      if (Array.isArray(data)) {
        console.log(`Encontrados ${data.length} convênios no formato de array`);
        return data;
      }
      // Se for objeto, procura por arrays
      else if (data && typeof data === 'object') {
        // Tenta encontrar um array dentro do objeto
        const possibleArrayProps = ['personInsurances', 'insurances', 'data', 'items', 'results'];

        // Primeiro procura nas propriedades comuns
        for (const prop of possibleArrayProps) {
          if (Array.isArray(data[prop])) {
            console.log(`Encontrados ${data[prop].length} convênios na propriedade "${prop}"`);
            return data[prop];
          }
        }

        // Se não encontrou nas propriedades comuns, verifica todas as propriedades
        for (const prop of Object.keys(data)) {
          if (Array.isArray(data[prop])) {
            console.log(`Encontrados ${data[prop].length} convênios na propriedade "${prop}"`);
            return data[prop];
          }
        }

        // Tenta extrair informações de convênios mesmo que não estejam em um array direto
        if (data.insurance || data.insuranceId) {
          console.log(`Encontrado um único convênio em formato não-array`);
          const insurance = {
            id: data.insuranceId || data.insurance?.id,
            name: data.insuranceName || data.insurance?.name,
            policyNumber: data.policyNumber,
            validUntil: data.validUntil
          };

          return [insurance];
        }
      }

      // Se não conseguir encontrar nenhum array, retorna array vazio
      console.warn("Estrutura de resposta inesperada:", data);
      return [];
    } catch (error) {
      console.error(`Erro ao listar convênios da pessoa ${personId}:`, error);
      // Em caso de erro, retorna array vazio em vez de lançar exceção para melhor UX
      return [];
    }
  },

  /**
   * Exporta a lista de convênios com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  exportInsurances: async (filters, exportFormat = "xlsx") => {
    try {
      // Obter os dados filtrados da API
      const response = await insurancesService.getInsurances({
        ...filters,
        limit: 1000 // Aumentamos o limite para exportar mais dados
      });

      // Extrair os dados dos convênios
      const insurancesArray = response.insurances || [];

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "name", header: "Nome" },
        {
          key: "companyName",
          header: "Empresa"
        },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = insurancesArray.map(insurance => {
        return {
          name: insurance.name || "",
          companyName: insurance.company && insurance.company.name ? insurance.company.name : "",
          createdAt: insurance.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.insuranceIds && filters.insuranceIds.length > 0) {
        subtitleParts.push(`Convênios específicos: ${filters.insuranceIds.length} selecionados`);
      }
      if (filters.companyId) {
        // Tentar encontrar o nome da empresa nos dados
        const companyName = insurancesArray.find(i => i.company && i.company.id === filters.companyId)?.company?.name;
        subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "convenios",
        columns,
        title: "Lista de Convênios",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar convênios:", error);
      return false;
    }
  },

  updatePersonInsurance: async (personId, insuranceId, data) => {
    try {
      const response = await api.put(`/insurances/person/${personId}/${insuranceId}`, data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar convênio ${insuranceId} da pessoa ${personId}:`, error);
      throw error;
    }
  },

  // Obter detalhes de um convênio específico de uma pessoa
  getPersonInsurance: async (personId, insuranceId) => {
    try {
      const response = await api.get(`/insurances/person/${personId}/${insuranceId}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar detalhes do convênio ${insuranceId} da pessoa ${personId}:`, error);
      throw error;
    }
  }
};

export default insurancesService;