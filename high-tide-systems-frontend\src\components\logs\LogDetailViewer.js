"use client";

import React, { useState, useEffect } from "react";
import { 
  X, 
  User, 
  Calendar, 
  Clock, 
  MapPin, 
  Monitor, 
  FileText,
  ArrowLeft
} from "lucide-react";
import { format, parseISO } from "date-fns";
import { ptBR } from "date-fns/locale";
import { auditLogService } from "@/app/modules/admin";

const LogDetailViewer = ({ logId, onClose }) => {
  const [log, setLog] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (logId) {
      fetchLogDetails();
    }
  }, [logId]);

  const fetchLogDetails = async () => {
    setIsLoading(true);
    setError("");
    try {
      const logData = await auditLogService.getLogDetails(logId);
      setLog(logData);
    } catch (error) {
      console.error("Erro ao buscar detalhes do log:", error);
      setError("Não foi possível carregar os detalhes deste log.");
    } finally {
      setIsLoading(false);
    }
  };

  // Formatação de data para exibição
  const formatDate = (dateString) => {
    try {
      return format(parseISO(dateString), "dd 'de' MMMM 'de' yyyy 'às' HH:mm:ss", {
        locale: ptBR
      });
    } catch (e) {
      return dateString;
    }
  };

  // Renderizar o conteúdo baseado no estado atual
  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
        </div>
      );
    }

    if (error) {
      return (
        <div className="p-6 text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button 
            onClick={fetchLogDetails}
            className="px-4 py-2 bg-primary-500 text-white rounded-lg hover:bg-primary-600"
          >
            Tentar novamente
          </button>
        </div>
      );
    }

    if (!log) {
      return (
        <div className="p-6 text-center text-neutral-500">
          Selecione um log para ver os detalhes
        </div>
      );
    }

    return (
      <div className="p-6 space-y-6">
        {/* Informações básicas */}
        <div>
          <h3 className="text-lg font-semibold text-neutral-800 mb-4">Detalhes do Log</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <div>
                <span className="text-sm font-medium text-neutral-500">Ação:</span>
                <p className="text-neutral-800">{log.action}</p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-neutral-500">Entidade:</span>
                <p className="text-neutral-800">{log.entityType}</p>
              </div>
              
              <div>
                <span className="text-sm font-medium text-neutral-500">ID da Entidade:</span>
                <p className="text-neutral-800 font-mono text-sm">{log.entityId}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-neutral-400" />
                <span className="text-sm font-medium text-neutral-500">Usuário:</span>
                <p className="text-neutral-800">{log.userName || log.userId}</p>
              </div>
              
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-neutral-400" />
                <span className="text-sm font-medium text-neutral-500">Data:</span>
                <p className="text-neutral-800">{formatDate(log.createdAt).split(" às")[0]}</p>
              </div>
              
              <div className="flex items-center gap-2">
                <Clock className="h-4 w-4 text-neutral-400" />
                <span className="text-sm font-medium text-neutral-500">Hora:</span>
                <p className="text-neutral-800">{formatDate(log.createdAt).split(" às")[1]}</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Informações do cliente */}
        <div className="space-y-2 border-t border-neutral-200 pt-4">
          <div className="flex items-center gap-2">
            <MapPin className="h-4 w-4 text-neutral-400" />
            <span className="text-sm font-medium text-neutral-500">Endereço IP:</span>
            <p className="text-neutral-800 font-mono">{log.ipAddress || "N/A"}</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Monitor className="h-4 w-4 text-neutral-400" />
            <span className="text-sm font-medium text-neutral-500">User Agent:</span>
            <p className="text-neutral-800 text-sm overflow-hidden overflow-ellipsis">{log.userAgent || "N/A"}</p>
          </div>
        </div>
        
        {/* Dados alterados (se existirem) */}
        {log.details && (
          <div className="space-y-2 border-t border-neutral-200 pt-4">
            <div className="flex items-center gap-2 mb-2">
              <FileText className="h-4 w-4 text-neutral-400" />
              <span className="text-sm font-medium text-neutral-500">Detalhes / Dados alterados:</span>
            </div>
            <pre className="bg-neutral-100 p-4 rounded-lg text-xs overflow-x-auto font-mono text-neutral-800 max-h-96 overflow-y-auto">
              {JSON.stringify(log.details, null, 2)}
            </pre>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 z-50 flex justify-center items-center bg-black/50">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] flex flex-col relative">
        {/* Header */}
        <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200">
          <button 
            onClick={onClose}
            className="flex items-center gap-2 text-neutral-600 hover:text-neutral-800"
          >
            <ArrowLeft size={16} />
            <span>Voltar para a lista</span>
          </button>
          
          <button
            onClick={onClose}
            className="text-neutral-400 hover:text-neutral-600"
            aria-label="Fechar"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Content */}
        <div className="overflow-y-auto">
          {renderContent()}
        </div>
      </div>
    </div>
  );
};

export default LogDetailViewer;