// src/controllers/authController.js
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { validationResult } = require('express-validator');
const prisma = require('../utils/prisma');
const authMiddleware = require('../middlewares/auth');
const { isTokenBlacklisted, addToBlacklist } = require('../middlewares/auth');

// Constantes
const SALT_ROUNDS = 12; // Número maior = mais seguro mas mais lento
const JWT_EXPIRY = '24h'; // Tempo de expiração do token
const MAX_LOGIN_ATTEMPTS = 5; // Máximo de tentativas de login falhas
const PASSWORD_RESET_EXPIRY = 60 * 60 * 1000; // 1 hora em milissegundos

class AuthController {
  /**
   * Registra um novo usuário no sistema
   * <PERSON>r padr<PERSON>, o usuário é criado com o módulo BASIC
   */
  static async register(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { login, email, fullName, password, cpf, cnpj, birthDate, address, phone, companyId } = req.body;

      // Check if user already exists (email or login)
      const userExists = await prisma.user.findFirst({
        where: {
          OR: [
            { email },
            { login },
            ...(cpf ? [{ cpf }] : []),
            ...(cnpj ? [{ cnpj }] : []),
          ],
        },
      });

      if (userExists) {
        // Don't reveal which field caused the conflict for security
        return res.status(400).json({ message: 'User with these credentials already exists' });
      }

      // Check password strength
      if (!AuthController.isStrongPassword(password)) {
        return res.status(400).json({
          message: 'Password is too weak. It must have at least 8 characters, including uppercase, lowercase, number, and special character.'
        });
      }

      // Hash password with stronger salt
      const hashedPassword = await bcrypt.hash(password, SALT_ROUNDS);

      // Create user
      const user = await prisma.user.create({
        data: {
          login,
          email,
          fullName,
          password: hashedPassword,
          cpf,
          cnpj,
          birthDate: birthDate ? new Date(birthDate) : null,
          address,
          phone,
          modules: ['BASIC'], // Default module
          permissions: [], // No initial permissions
          active: true,
          companyId: companyId || null,
          createdById: req.user?.id || null
        },
        select: {
          id: true,
          login: true,
          email: true,
          fullName: true,
          cpf: true,
          cnpj: true,
          birthDate: true,
          address: true,
          phone: true,
          modules: true,
          permissions: true,
          role: true,
          active: true,
          createdAt: true,
          companyId: true
        },
      });

      // Log to audit trail
      await prisma.auditLog.create({
        data: {
          userId: req.user?.id || user.id,
          action: 'CREATE',
          entityType: 'User',
          entityId: user.id,
          details: { method: 'register' },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: user.companyId
        }
      });

      // Generate token
      const token = jwt.sign(
        {
          id: user.id,
          modules: user.modules,
          role: user.role,
          iat: Math.floor(Date.now() / 1000)
        },
        process.env.JWT_SECRET,
        { expiresIn: JWT_EXPIRY }
      );

      res.status(201).json({
        user,
        token
      });
    } catch (error) {
      console.error('Registration error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Authenticate a user or client in the system
   * Validates credentials and checks if account is active
   */
  static async login(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { email, username, password, loginType = 'email' } = req.body;

      // Determine which identifier to use (email or username)
      const identifier = loginType === 'email' ? email : username;

      if (!identifier) {
        return res.status(400).json({ message: 'Email or username is required' });
      }

      let user = null;
      let isClient = false;

      // First, try to find a user
      if (loginType === 'email') {
        user = await prisma.user.findUnique({
          where: { email: identifier }
        });
      } else {
        user = await prisma.user.findUnique({
          where: { login: identifier }
        });
      }

      // If user not found, try to find a client
      if (!user) {
        if (loginType === 'email') {
          const client = await prisma.client.findUnique({
            where: { email: identifier }
          });

          if (client && client.active) {
            user = client;
            isClient = true;
          }
        } else {
          const client = await prisma.client.findUnique({
            where: { login: identifier }
          });

          if (client && client.active) {
            user = client;
            isClient = true;
          }
        }
      }

      // If neither user nor client found, or account is inactive, return generic message
      if (!user || !user.active) {
        return res.status(400).json({ message: 'Invalid credentials' });
      }

      // Check for too many failed login attempts (only for User model)
      if (!isClient && user.failedLoginAttempts >= MAX_LOGIN_ATTEMPTS) {
        return res.status(429).json({
          message: 'Account temporarily locked due to too many failed login attempts. Please reset your password or try again later.'
        });
      }

      // Verify password
      const isValidPassword = await bcrypt.compare(password, user.password);

      if (!isValidPassword) {
        // Increment failed login attempts (only for User model)
        if (!isClient) {
          await prisma.user.update({
            where: { id: user.id },
            data: {
              failedLoginAttempts: user.failedLoginAttempts + 1
            }
          });
        }

        return res.status(400).json({ message: 'Invalid credentials' });
      }

      // For User model: Reset failed login attempts on successful login
      if (!isClient) {
        await prisma.user.update({
          where: { id: user.id },
          data: {
            failedLoginAttempts: 0,
            lastLoginAt: new Date(),
            lastLoginIp: req.ip || req.connection.remoteAddress
          }
        });

        // Log successful login for User
        await prisma.auditLog.create({
          data: {
            userId: user.id,
            action: 'LOGIN',
            entityType: 'User',
            entityId: user.id,
            ipAddress: req.ip || req.connection.remoteAddress,
            userAgent: req.headers['user-agent'],
            companyId: user.companyId
          }
        });
      }

      // Generate token with appropriate data based on user type
      const tokenData = {
        id: user.id,
        iat: Math.floor(Date.now() / 1000),
        isClient: isClient
      };

      // Add modules and role for User type
      if (!isClient) {
        tokenData.modules = user.modules;
        tokenData.role = user.role;
      }

      const token = jwt.sign(
        tokenData,
        process.env.JWT_SECRET,
        { expiresIn: JWT_EXPIRY }
      );

      // Remove sensitive fields
      let userWithoutSensitiveData;
      if (isClient) {
        const { password: _, ...clientData } = user;
        userWithoutSensitiveData = {
          ...clientData,
          isClient: true
        };
      } else {
        const { password: _, failedLoginAttempts: __, ...userData } = user;
        userWithoutSensitiveData = userData;
      }

      res.json({
        user: userWithoutSensitiveData,
        token
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Logout user and invalidate token
   */
  static async logout(req, res) {
    try {
      const authHeader = req.headers.authorization;

      if (authHeader) {
        const [scheme, token] = authHeader.split(' ');

        if (token && scheme === 'Bearer') {
          // Add token to blacklist
          await authMiddleware.revokeToken(token);

          console.log(`[LOGOUT] Token adicionado à lista negra para o usuário ${req.user.id}`);

          // Log logout
          await prisma.auditLog.create({
            data: {
              userId: req.user.id,
              action: 'LOGOUT',
              entityType: 'User',
              entityId: req.user.id,
              ipAddress: req.ip || req.connection.remoteAddress,
              userAgent: req.headers['user-agent'],
              companyId: req.user.companyId
            }
          });
        }
      }

      res.status(200).json({ message: 'Logout successful' });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Return authenticated user or client data
   */
  static async me(req, res) {
    try {
      const userId = req.user.id;
      const isClient = req.user.isClient === true;

      if (isClient) {
        // Return client data
        const client = await prisma.client.findUnique({
          where: { id: userId },
          select: {
            id: true,
            login: true,
            email: true,
            active: true,
            createdAt: true,
            companyId: true,
            Company: {
              select: {
                id: true,
                name: true,
                tradingName: true
              }
            },
            // Include related persons
            persons: {
              where: { active: true },
              select: {
                id: true,
                fullName: true,
                email: true,
                phone: true,
                profileImageUrl: true
              }
            }
          },
        });

        if (!client) {
          return res.status(404).json({ message: 'Client not found' });
        }

        // Format response
        const clientData = {
          ...client,
          isClient: true,
          role: 'CLIENT',
          company: client.Company,
          // Add empty arrays for compatibility with frontend
          modules: [],
          permissions: []
        };

        // Remove Company field (already mapped to company)
        delete clientData.Company;

        res.json(clientData);
      } else {
        // Return user data
        const user = await prisma.user.findUnique({
          where: { id: userId },
          select: {
            id: true,
            login: true,
            email: true,
            fullName: true,
            cpf: true,
            cnpj: true,
            birthDate: true,
            address: true,
            phone: true,
            modules: true,
            permissions: true,
            role: true,
            active: true,
            createdAt: true,
            lastLoginAt: true,
            companyId: true,
            profileImageUrl: true,
            company: {
              select: {
                id: true,
                name: true,
                tradingName: true
              }
            }
          },
        });

        if (!user) {
          return res.status(404).json({ message: 'User not found' });
        }

        // Adicionar URL completa da imagem de perfil, se existir
        if (user.profileImageUrl) {
          // Obter a URL base da API a partir do request
          const baseUrl = `${req.protocol}://${req.get('host')}`;
          user.profileImageFullUrl = `${baseUrl}/uploads/${user.profileImageUrl}`;
        }

        res.json(user);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(req, res) {
    try {
      const { email } = req.body;

      // Find user by email
      const user = await prisma.user.findUnique({
        where: { email }
      });

      // Always return success even if user not found (security)
      if (!user) {
        return res.status(200).json({
          message: 'If your email is registered, you will receive password reset instructions shortly'
        });
      }

      // Generate unique token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const tokenExpiry = new Date(Date.now() + PASSWORD_RESET_EXPIRY);

      // Save token to database
      await prisma.passwordReset.create({
        data: {
          userId: user.id,
          token: resetToken,
          expiresAt: tokenExpiry
        }
      });

      // IMPLEMENTAR ENVIO DE EMAIL COM LINK DE REDEFINIÇÃO DE SENHA

      // Log password reset request
      await prisma.auditLog.create({
        data: {
          userId: user.id,
          action: 'PASSWORD_RESET_REQUEST',
          entityType: 'User',
          entityId: user.id,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: user.companyId
        }
      });

      res.status(200).json({
        message: 'If your email is registered, you will receive password reset instructions shortly'
      });
    } catch (error) {
      console.error('Password reset request error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Reset password with token
   */
  static async resetPassword(req, res) {
    try {
      const { token, newPassword } = req.body;

      // Validate new password
      if (!AuthController.isStrongPassword(newPassword)) {
        return res.status(400).json({
          message: 'Password is too weak. It must have at least 8 characters, including uppercase, lowercase, number, and special character.'
        });
      }

      // Find token in database
      const resetRecord = await prisma.passwordReset.findUnique({
        where: { token }
      });

      // Check if token exists and is valid
      if (!resetRecord || resetRecord.expiresAt < new Date() || resetRecord.usedAt) {
        return res.status(400).json({ message: 'Invalid or expired token' });
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);

      // Update user password
      await prisma.user.update({
        where: { id: resetRecord.userId },
        data: {
          password: hashedPassword,
          passwordChangedAt: new Date(),
          failedLoginAttempts: 0
        }
      });

      // Mark token as used
      await prisma.passwordReset.update({
        where: { id: resetRecord.id },
        data: { usedAt: new Date() }
      });

      // Log password reset
      await prisma.auditLog.create({
        data: {
          userId: resetRecord.userId,
          action: 'PASSWORD_RESET',
          entityType: 'User',
          entityId: resetRecord.userId,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent']
        }
      });

      res.status(200).json({ message: 'Password reset successful' });
    } catch (error) {
      console.error('Password reset error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Change password (for authenticated users)
   */
  static async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body;
      const userId = req.user.id;

      // Find user
      const user = await prisma.user.findUnique({
        where: { id: userId }
      });

      // Verify current password
      const isValidPassword = await bcrypt.compare(currentPassword, user.password);
      if (!isValidPassword) {
        return res.status(400).json({ message: 'Current password is incorrect' });
      }

      // Validate new password
      if (!AuthController.isStrongPassword(newPassword)) {
        return res.status(400).json({
          message: 'Password is too weak. It must have at least 8 characters, including uppercase, lowercase, number, and special character.'
        });
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(newPassword, SALT_ROUNDS);

      // Update user password
      await prisma.user.update({
        where: { id: userId },
        data: {
          password: hashedPassword,
          passwordChangedAt: new Date()
        }
      });

      // Log password change
      await prisma.auditLog.create({
        data: {
          userId: userId,
          action: 'PASSWORD_CHANGE',
          entityType: 'User',
          entityId: userId,
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: req.user.companyId
        }
      });

      res.status(200).json({ message: 'Password changed successfully' });
    } catch (error) {
      console.error('Password change error:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }

  /**
   * Helper to validate password strength
   */
  static isStrongPassword(password) {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, and 1 special character
    const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()\-_=+{};:,<.>]).{8,}$/;
    return passwordRegex.test(password);
  }
}

module.exports = AuthController;