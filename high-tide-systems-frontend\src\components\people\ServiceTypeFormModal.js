"use client";

import React, { useState, useEffect } from "react";
import { Tag, DollarSign, Building } from "lucide-react";
import { serviceTypeService } from "@/app/modules/scheduler/services/serviceTypeService";
import { useAuth } from "@/contexts/AuthContext";
import {
  ModuleModal,
  ModalButton,
  ModuleInput,
  ModuleSelect,
  ModuleFormGroup,
  ModuleLabel
} from "@/components/ui";

const ServiceTypeFormModal = ({ isOpen, onClose, serviceType = null, onSuccess, companies = [] }) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    value: "",
    companyId: ""
  });
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isSystemAdmin = user?.role === 'SYSTEM_ADMIN';

  // Load data when the modal opens
  useEffect(() => {
    if (isOpen) {
      // Set company ID for non-admin users
      if (!isSystemAdmin && user?.companyId) {
        setFormData(prev => ({ ...prev, companyId: user.companyId }));
      }

      if (serviceType) {
        setFormData({
          name: serviceType.name || "",
          value: serviceType.value ? serviceType.value.toString() : "",
          // If not admin, always use the user's company
          companyId: !isSystemAdmin ? user?.companyId : (serviceType.companyId || "")
        });
      } else {
        // Reset form for new service type
        resetForm();
      }
    }
  }, [isOpen, serviceType, user]);

  const resetForm = () => {
    setFormData({
      name: "",
      value: "",
      // If not admin, always use the user's company
      companyId: !isSystemAdmin ? user?.companyId : ""
    });
    setError(null);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // For the value field, ensure it's a valid number
    if (name === "value") {
      // Allow only numbers, comma and period
      const sanitizedValue = value.replace(/[^\d.,]/g, "");
      // Replace comma with period
      const normalizedValue = sanitizedValue.replace(",", ".");

      setFormData(prev => ({
        ...prev,
        [name]: normalizedValue
      }));
      return;
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const validateForm = () => {
    // Basic validation
    if (!formData.name) return "Nome do serviço é obrigatório";
    if (!formData.value) return "Valor do serviço é obrigatório";

    // Validate if value is a number
    const numericValue = parseFloat(formData.value);
    if (isNaN(numericValue) || numericValue < 0) return "Valor deve ser um número positivo";

    if (!formData.companyId) return "Empresa é obrigatória";

    return null; // No errors
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Prepare data
      const payload = {
        name: formData.name,
        value: parseFloat(formData.value),
        // Ensure there's always a company
        companyId: formData.companyId || user?.companyId
      };

      if (serviceType) {
        // Update existing service type
        await serviceTypeService.updateServiceType(serviceType.id, payload);
      } else {
        // Create new service type
        await serviceTypeService.createServiceType(payload);
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar tipo de serviço:", err);
      setError(err.response?.data?.message || err.message || "Ocorreu um erro ao salvar o tipo de serviço.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Módulo a ser usado nos componentes
  const moduleColor = "scheduler";

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="scheduler"
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="scheduler"
        type="submit"
        form="service-type-form"
        isLoading={isSubmitting}
      >
        {serviceType ? "Atualizar" : "Salvar"}
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={serviceType ? "Editar Tipo de Serviço" : "Novo Serviço"}
      icon={<Tag size={22} />}
      moduleColor="scheduler"
      size="md"
      footer={modalFooter}
    >
      <form id="service-type-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6">
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-start">
              <span className="flex-1">{error}</span>
            </div>
          )}

          <div className="space-y-6">

            <ModuleFormGroup
              moduleColor={moduleColor}
              label="Nome do Serviço"
              htmlFor="name"
              icon={<Tag size={16} />}
              required
            >
              <ModuleInput
                moduleColor={moduleColor}
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Ex: Consulta Padrão, Avaliação, etc."
                required
                leftIcon={<Tag size={16} />}
              />
            </ModuleFormGroup>

            {/* Valor */}
            <ModuleFormGroup
              moduleColor={moduleColor}
              label="Valor (R$)"
              htmlFor="value"
              icon={<DollarSign size={16} />}
              required
              helpText="Use ponto ou vírgula para separar os centavos (Ex: 150,00 ou 150.00)"
            >
              <ModuleInput
                moduleColor={moduleColor}
                id="value"
                name="value"
                value={formData.value}
                onChange={handleChange}
                placeholder="0,00"
                required
                leftIcon={<DollarSign size={16} />}
              />
            </ModuleFormGroup>

            {/* Company Dropdown for System Admin */}
            {isSystemAdmin ? (
              <ModuleFormGroup
                moduleColor={moduleColor}
                label="Empresa"
                htmlFor="companyId"
                icon={<Building size={16} />}
                required
              >
                <ModuleSelect
                  moduleColor={moduleColor}
                  id="companyId"
                  name="companyId"
                  value={formData.companyId}
                  onChange={handleChange}
                  required
                  placeholder="Selecione uma empresa"
                >
                  <option value="">Selecione uma empresa</option>
                  {companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </ModuleFormGroup>
            ) : (
              /* Display Company Info for non-admin users */
              <ModuleFormGroup
                moduleColor={moduleColor}
                label="Empresa"
                icon={<Building size={16} />}
                helpText="O tipo de serviço será associado à sua empresa"
              >
                <div className="px-3 py-2 border border-neutral-200 dark:border-gray-700 bg-neutral-50 dark:bg-gray-900 rounded-lg text-neutral-700 dark:text-gray-300 flex items-center">
                  <Building className="h-5 w-5 text-gray-400 dark:text-gray-500 mr-2" />
                  {user?.companyName || "Sua empresa"}
                </div>
                <input type="hidden" name="companyId" value={user?.companyId || ""} />
              </ModuleFormGroup>
            )}
          </div>

        </form>
    </ModuleModal>
  );
};

export default ServiceTypeFormModal;