name: Deploy Frontend

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_API_URL: https://hightide.site/api

      - name: Deploy to VPS
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            echo "===== INICIANDO DEPLOY DO FRONTEND ====="
            echo "[1/6] Navegando para o diretório do projeto..."
            cd ${{ secrets.VPS_PROJECT_PATH }}
            
            # Verificar se o diretório existe, se não, cloná-lo
            if [ ! -d "inside-the-houses-frontend" ]; then
              echo "[2/6] Repositório não encontrado. Clonando repositório..."
              git clone https://github.com/gabrielbr619/inside-the-houses-frontend.git
              cd inside-the-houses-frontend
            else
              echo "[2/6] Repositório encontrado. Atualizando código..."
              cd inside-the-houses-frontend
              git fetch --all
              git reset --hard origin/main
            fi
            
            # Pull das alterações mais recentes
            echo "[3/6] Baixando as alterações mais recentes..."
            git pull origin main
            
            # Voltar para o diretório raiz
            echo "[4/6] Voltando para o diretório raiz..."
            cd ${{ secrets.VPS_PROJECT_PATH }}
            
            # Reconstruir e reiniciar contêiner do frontend
            echo "[5/6] Reconstruindo e reiniciando o contêiner do frontend..."
            docker compose build frontend
            docker compose up -d frontend
            
            # Limpar imagens Docker não utilizadas para economizar espaço
            echo "[6/6] Limpando imagens Docker não utilizadas..."
            docker image prune -f
            
            echo "===== DEPLOY DO FRONTEND CONCLUÍDO COM SUCESSO ====="
