// src/swagger/companyRoutes.js

/**
 * @swagger
 * tags:
 *   name: Empresas
 *   description: Gerenciamento de empresas no sistema (multi-tenancy)
 */

/**
 * @swagger
 * /companies:
 *   post:
 *     summary: Cria uma nova empresa
 *     description: |
 *       Cria uma nova empresa no sistema.
 *       Requer privilégios de administrador do sistema.
 *     tags: [Empresas]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - cnpj
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome da empresa
 *                 example: "Clínica Exemplo Ltda"
 *               tradingName:
 *                 type: string
 *                 description: Nome fantasia
 *                 example: "Clínica Exemplo"
 *               cnpj:
 *                 type: string
 *                 description: CNPJ da empresa (sem formatação)
 *                 example: "12345678000199"
 *               phone:
 *                 type: string
 *                 description: Telefone principal
 *                 example: "1122223333"
 *               phone2:
 *                 type: string
 *                 description: Telefone secundário
 *                 example: "1122224444"
 *               address:
 *                 type: string
 *                 description: Endereço
 *                 example: "Av. Principal, 1000"
 *               city:
 *                 type: string
 *                 description: Cidade
 *                 example: "São Paulo"
 *               state:
 *                 type: string
 *                 description: Estado
 *                 example: "SP"
 *               postalCode:
 *                 type: string
 *                 description: CEP
 *                 example: "01234567"
 *               website:
 *                 type: string
 *                 description: Website
 *                 example: "https://www.clinicaexemplo.com.br"
 *               primaryColor:
 *                 type: string
 *                 description: Cor primária (branding)
 *                 example: "#4682B4"
 *               secondaryColor:
 *                 type: string
 *                 description: Cor secundária (branding)
 *                 example: "#FFFFFF"
 *               description:
 *                 type: string
 *                 description: Descrição da empresa
 *                 example: "Clínica especializada em atendimento multidisciplinar"
 *               socialMedia:
 *                 type: object
 *                 description: Redes sociais
 *                 example: {
 *                   "facebook": "https://www.facebook.com/clinicaexemplo",
 *                   "instagram": "https://www.instagram.com/clinicaexemplo"
 *                 }
 *               businessHours:
 *                 type: object
 *                 description: Horário de funcionamento
 *                 example: {
 *                   "weekdays": "08:00 às 20:00",
 *                   "saturday": "08:00 às 14:00",
 *                   "sunday": "Fechado"
 *                 }
 *     responses:
 *       201:
 *         description: Empresa criada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       400:
 *         description: Dados inválidos ou CNPJ já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               validationError:
 *                 value:
 *                   errors: [
 *                     {
 *                       msg: "Nome é obrigatório",
 *                       param: "name",
 *                       location: "body"
 *                     }
 *                   ]
 *               duplicateError:
 *                 value:
 *                   message: "CNPJ já cadastrado"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista empresas
 *     description: |
 *       Retorna uma lista paginada de empresas.
 *       Requer privilégios de administrador do sistema.
 *     tags: [Empresas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Número de itens por página
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Texto para busca (nome, nome fantasia, CNPJ)
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filtrar por status (ativa/inativa)
 *     responses:
 *       200:
 *         description: Lista de empresas
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 companies:
 *                   type: array
 *                   items:
 *                     allOf:
 *                       - $ref: '#/components/schemas/Company'
 *                       - type: object
 *                         properties:
 *                           documents:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: string
 *                                   format: uuid
 *                                 filename:
 *                                   type: string
 *                                 path:
 *                                   type: string
 *                 total:
 *                   type: integer
 *                   description: Total de registros
 *                   example: 25
 *                 pages:
 *                   type: integer
 *                   description: Total de páginas
 *                   example: 3
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /companies/{id}:
 *   get:
 *     summary: Obtém detalhes de uma empresa
 *     description: |
 *       Retorna os detalhes completos de uma empresa específica, incluindo configurações de email e logo.
 *       Requer privilégios de administrador do sistema.
 *     tags: [Empresas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da empresa
 *     responses:
 *       200:
 *         description: Detalhes da empresa
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Company'
 *                 - type: object
 *                   properties:
 *                     emailConfigs:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           smtpHost:
 *                             type: string
 *                           smtpPort:
 *                             type: integer
 *                           smtpSecure:
 *                             type: boolean
 *                           smtpUser:
 *                             type: string
 *                           emailFromName:
 *                             type: string
 *                           emailFromAddress:
 *                             type: string
 *                           active:
 *                             type: boolean
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                     documents:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           filename:
 *                             type: string
 *                           path:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: Empresa não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Empresa não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   put:
 *     summary: Atualiza uma empresa
 *     description: |
 *       Atualiza os dados de uma empresa existente.
 *       Requer privilégios de administrador do sistema.
 *     tags: [Empresas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da empresa
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome da empresa
 *               tradingName:
 *                 type: string
 *                 description: Nome fantasia
 *               cnpj:
 *                 type: string
 *                 description: CNPJ da empresa (sem formatação)
 *               phone:
 *                 type: string
 *                 description: Telefone principal
 *               phone2:
 *                 type: string
 *                 description: Telefone secundário
 *               address:
 *                 type: string
 *                 description: Endereço
 *               city:
 *                 type: string
 *                 description: Cidade
 *               state:
 *                 type: string
 *                 description: Estado
 *               postalCode:
 *                 type: string
 *                 description: CEP
 *               website:
 *                 type: string
 *                 description: Website
 *               primaryColor:
 *                 type: string
 *                 description: Cor primária (branding)
 *               secondaryColor:
 *                 type: string
 *                 description: Cor secundária (branding)
 *               description:
 *                 type: string
 *                 description: Descrição da empresa
 *               socialMedia:
 *                 type: object
 *                 description: Redes sociais
 *               businessHours:
 *                 type: object
 *                 description: Horário de funcionamento
 *     responses:
 *       200:
 *         description: Empresa atualizada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       400:
 *         description: Dados inválidos ou CNPJ já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "CNPJ já cadastrado"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: Empresa não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Empresa não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove uma empresa
 *     description: |
 *       Remove uma empresa e suas configurações relacionadas.
 *       Requer privilégios de administrador do sistema.
 *     tags: [Empresas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da empresa
 *     responses:
 *       204:
 *         description: Empresa removida com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: Empresa não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Empresa não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /companies/{id}/status:
 *   patch:
 *     summary: Alterna o status de uma empresa
 *     description: |
 *       Ativa ou desativa uma empresa no sistema.
 *       Requer privilégios de administrador do sistema.
 *     tags: [Empresas]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da empresa
 *     responses:
 *       200:
 *         description: Status alterado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Company'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         $ref: '#/components/responses/ForbiddenError'
 *       404:
 *         description: Empresa não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Empresa não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */