"use client";

import React, { useState, useEffect } from "react";
import { Layers, Search, Check, X } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleTable, ModuleSelect } from "@/components/ui";
import { evaluationsService } from "@/app/modules/aba/services/evaluationsService";
import { useToast } from "@/contexts/ToastContext";

const LevelSelectionModal = ({ isOpen, onClose, onSelect, currentLevels = [] }) => {
  const { toast_error } = useToast();
  const [levels, setLevels] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedLevels, setSelectedLevels] = useState([]);
  const [filters, setFilters] = useState({
    search: "",
    page: 1,
    limit: 10
  });
  const [totalItems, setTotalItems] = useState(0);

  // Carregar níveis disponíveis
  useEffect(() => {
    const loadLevels = async () => {
      try {
        setIsLoading(true);
        console.log("LevelSelectionModal - Carregando níveis com filtros:", filters);
        const response = await evaluationsService.getAllLevels(filters);
        console.log("LevelSelectionModal - Resposta da API:", response);

        // Filtrar níveis que já estão na avaliação
        const currentLevelIds = currentLevels.map(level => level.id);
        console.log("LevelSelectionModal - IDs de níveis atuais:", currentLevelIds);

        const filteredLevels = response.items.filter(level => !currentLevelIds.includes(level.id));
        console.log("LevelSelectionModal - Níveis filtrados:", filteredLevels);

        setLevels(filteredLevels);
        setTotalItems(response.total - currentLevelIds.length);
      } catch (error) {
        console.error("Erro ao carregar níveis:", error);
        toast_error("Não foi possível carregar os níveis");
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      loadLevels();
    }
  }, [isOpen, filters, currentLevels, toast_error]);

  // Manipuladores de eventos
  const handleSearch = (e) => {
    e.preventDefault();
    setFilters({ ...filters, page: 1 });
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
      page: 1
    });
  };

  const handlePageChange = (page) => {
    setFilters({ ...filters, page });
  };

  const handleSelectLevel = (level) => {
    const isSelected = selectedLevels.some(l => l.id === level.id);

    if (isSelected) {
      setSelectedLevels(selectedLevels.filter(l => l.id !== level.id));
    } else {
      setSelectedLevels([...selectedLevels, level]);
    }
  };

  const handleConfirmSelection = () => {
    onSelect(selectedLevels);
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title="Selecionar Níveis"
      size="lg"
      moduleColor="abaplus"
      icon={<Layers size={20} />}
    >
      <div className="p-6 space-y-6">
        {/* Barra de pesquisa */}
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-sm border border-gray-200 dark:border-gray-700">
          <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <ModuleInput
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Pesquisar por descrição..."
                icon={<Search size={18} />}
                moduleColor="abaplus"
                className="w-full"
              />
            </div>
            <div className="w-full md:w-1/4">
              <ModuleSelect
                name="limit"
                value={filters.limit}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </ModuleSelect>
            </div>
            <button
              type="submit"
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center justify-center gap-2"
            >
              <Search size={18} />
              Pesquisar
            </button>
          </form>
        </div>

        {/* Tabela de Níveis */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
          <ModuleTable
            columns={[
              { key: "select", field: "select", header: "Selecionar" },
              { key: "order", field: "order", header: "Ordem" },
              { key: "description", field: "description", header: "Descrição" },
              { key: "ageRange", field: "ageRange", header: "Faixa Etária" }
            ]}
            data={levels}
            isLoading={isLoading}
            pagination={{
              currentPage: filters.page,
              totalItems,
              itemsPerPage: filters.limit,
              onPageChange: handlePageChange
            }}
            emptyMessage="Nenhum nível encontrado"
            emptyIcon={<Layers size={24} />}
            tableId="levels-selection-table"
            defaultSortField="order"
            defaultSortDirection="asc"
            renderRow={(level, _index, moduleColors, visibleColumns) => (
              <tr
                key={level.id}
                className={`${moduleColors.hoverBg} ${
                  selectedLevels.some(l => l.id === level.id) ? "bg-teal-50 dark:bg-teal-900/20" : ""
                }`}
                onClick={() => handleSelectLevel(level)}
              >
                {visibleColumns.includes("select") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center justify-center">
                      <div
                        className={`w-6 h-6 rounded-md flex items-center justify-center cursor-pointer ${
                          selectedLevels.some(l => l.id === level.id)
                            ? "bg-teal-500 text-white"
                            : "border border-gray-300 dark:border-gray-600"
                        }`}
                      >
                        {selectedLevels.some(l => l.id === level.id) && <Check size={16} />}
                      </div>
                    </div>
                  </td>
                )}
                {visibleColumns.includes("order") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">{level.order}</div>
                  </td>
                )}
                {visibleColumns.includes("description") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">{level.description}</div>
                  </td>
                )}
                {visibleColumns.includes("ageRange") && (
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500 dark:text-gray-400">{level.ageRange || "-"}</div>
                  </td>
                )}
              </tr>
            )}
            moduleColor="abaplus"
          />
        </div>

        {/* Resumo da seleção */}
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
          <div className="flex justify-between items-center">
            <div>
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {selectedLevels.length} nível(is) selecionado(s)
              </span>
            </div>
            <div className="flex gap-2">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors flex items-center gap-2"
              >
                <X size={18} />
                Cancelar
              </button>
              <button
                onClick={handleConfirmSelection}
                className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
                disabled={selectedLevels.length === 0}
              >
                <Check size={18} />
                Confirmar Seleção
              </button>
            </div>
          </div>
        </div>
      </div>
    </ModuleModal>
  );
};

export default LevelSelectionModal;
