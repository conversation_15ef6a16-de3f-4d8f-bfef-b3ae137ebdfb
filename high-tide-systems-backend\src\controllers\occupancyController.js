// src/controllers/occupancyController.js
const prisma = require("../utils/prisma");

/**
 * Controlador para análise de ocupação de agendamentos
 */
class OccupancyController {
  /**
   * Obtém dados de ocupação geral
   * @param {Request} req Requisição
   * @param {Response} res Resposta
   */
  static async getOccupancyData(req, res) {
    try {
      const {
        period = 'month',
        startDate: startDateParam,
        endDate: endDateParam,
        providers,
        serviceTypes,
        locations,
        limit = 1000
      } = req.query;

      // Determinar período de datas
      let startDate, endDate;

      if (startDateParam && endDateParam) {
        // Usar datas explícitas se fornecidas
        startDate = new Date(startDateParam);
        endDate = new Date(endDateParam);

        // Ajustar horas para pegar o dia completo
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        console.log(`Usando período personalizado: ${startDate.toISOString()} até ${endDate.toISOString()}`);
      } else {
        // Calcular intervalo de datas baseado no período
        const dateParam = req.query.date ? new Date(req.query.date) : new Date();
        startDate = new Date(dateParam);
        endDate = new Date(dateParam);

        console.log(`Calculando período a partir de: ${dateParam.toISOString()}`);

        switch (period) {
          case 'day':
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'week':
            const day = startDate.getDay();
            const diff = startDate.getDate() - day + (day === 0 ? -6 : 1);
            startDate.setDate(diff);
            startDate.setHours(0, 0, 0, 0);
            endDate.setDate(startDate.getDate() + 6);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'month':
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(endDate.getMonth() + 1);
            endDate.setDate(0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'year':
            startDate.setMonth(0, 1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(11, 31);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'custom':
            // Para período personalizado, deveria ter fornecido startDate e endDate
            console.error('Período personalizado selecionado, mas datas não foram fornecidas');
            return res.status(400).json({
              message: "Para período personalizado, é necessário fornecer startDate e endDate"
            });
          default:
            // Default: este mês
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(endDate.getMonth() + 1);
            endDate.setDate(0);
            endDate.setHours(23, 59, 59, 999);
        }

        console.log(`Período calculado: ${startDate.toISOString()} até ${endDate.toISOString()}`);
      }

      // Construir WHERE para agendamentos
      const where = {
        startDate: { gte: startDate, lte: endDate },
        // Se o usuário não for SYSTEM_ADMIN, limitar aos agendamentos da empresa dele
        ...((req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) ?
          { companyId: req.user.companyId } : {})
      };

      // Adicionar filtros se fornecidos
      if (providers) {
        const providerIds = Array.isArray(providers) ? providers : [providers];
        where.userId = { in: providerIds };
      }

      if (serviceTypes) {
        const serviceTypeIds = Array.isArray(serviceTypes) ? serviceTypes : [serviceTypes];
        where.serviceTypeId = { in: serviceTypeIds };
      }

      if (locations) {
        const locationIds = Array.isArray(locations) ? locations : [locations];
        where.locationId = { in: locationIds };
      }

      // Buscar agendamentos
      const appointments = await prisma.scheduling.findMany({
        where,
        include: {
          provider: { select: { id: true, fullName: true } },
          serviceType: { select: { id: true, name: true } },
          location: { select: { id: true, name: true } },
          Person: { select: { id: true, fullName: true } }
        },
        orderBy: { startDate: 'asc' },
        take: parseInt(limit)
      });

      // Se não houver agendamentos, retornar estrutura vazia
      if (appointments.length === 0) {
        return res.json({
          overallOccupancy: 0,
          providerOccupancy: [],
          serviceTypeDistribution: [],
          locationOccupancy: [],
          timeDistribution: [],
          weekdayDistribution: []
        });
      }

      // Processar provedores únicos
      const uniqueProviderIds = [...new Set(appointments.map(a => a.userId))];

      // Buscar informações de horários de trabalho para cada provedor
      const providerOccupancy = await Promise.all(uniqueProviderIds.map(async (providerId) => {
        // Buscar agendamentos deste provedor
        const providerAppointments = appointments.filter(a => a.userId === providerId);

        // Calcular horas ocupadas
        let totalBookedHours = 0;
        providerAppointments.forEach(appointment => {
          const start = new Date(appointment.startDate);
          const end = new Date(appointment.endDate);
          const durationHours = (end - start) / (1000 * 60 * 60);
          totalBookedHours += durationHours;
        });

        // Buscar horas de trabalho para este provedor
        let totalAvailableHours = 0;
        const daysInPeriod = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));

        // Para cada dia no período
        for (let i = 0; i < daysInPeriod; i++) {
          const currentDate = new Date(startDate);
          currentDate.setDate(currentDate.getDate() + i);
          const dayOfWeek = currentDate.getDay();

          // Buscar horários de trabalho para este dia da semana
          const workingHours = await prisma.workingHours.findMany({
            where: {
              userId: providerId,
              dayOfWeek,
              isActive: true
            }
          });

          // Somar horas disponíveis
          workingHours.forEach(period => {
            const startMinutes = period.startTimeMinutes;
            const endMinutes = period.endTimeMinutes;

            // Verificar se há intervalo
            if (period.breakStartMinutes && period.breakEndMinutes) {
              // Calcular horas trabalhadas menos o intervalo
              const beforeBreakHours = (period.breakStartMinutes - startMinutes) / 60;
              const afterBreakHours = (endMinutes - period.breakEndMinutes) / 60;
              totalAvailableHours += beforeBreakHours + afterBreakHours;
            } else {
              // Calcular horas trabalhadas sem intervalo
              totalAvailableHours += (endMinutes - startMinutes) / 60;
            }
          });
        }

        // Calcular taxa de ocupação
        const occupancyRate = totalAvailableHours > 0
          ? (totalBookedHours / totalAvailableHours) * 100
          : 0;

        // Obter informações do provedor
        const providerInfo = providerAppointments[0]?.provider || {
          id: providerId,
          fullName: `Profissional ${providerId.substring(0, 8)}`
        };

        return {
          providerId,
          providerName: providerInfo.fullName,
          totalAvailableHours,
          totalBookedHours,
          occupancyRate: Math.min(occupancyRate, 100),
          appointmentCount: providerAppointments.length
        };
      }));

      // Ordenar por ocupação
      providerOccupancy.sort((a, b) => b.occupancyRate - a.occupancyRate);

      // Calcular taxa de ocupação geral
      const totalAvailableHours = providerOccupancy.reduce((sum, p) => sum + p.totalAvailableHours, 0);
      const totalBookedHours = providerOccupancy.reduce((sum, p) => sum + p.totalBookedHours, 0);
      const overallOccupancy = totalAvailableHours > 0
        ? (totalBookedHours / totalAvailableHours) * 100
        : 0;

      // Distribuição por tipo de serviço
      const serviceTypeCounts = {};
      appointments.forEach(appointment => {
        const serviceTypeId = appointment.serviceTypeId;
        const serviceTypeName = appointment.serviceType?.name ||
                                `Serviço ${serviceTypeId.substring(0, 8)}`;

        if (!serviceTypeCounts[serviceTypeId]) {
          serviceTypeCounts[serviceTypeId] = {
            serviceTypeId,
            serviceTypeName,
            count: 0,
            providerDistribution: {}
          };
        }

        serviceTypeCounts[serviceTypeId].count++;

        // Contagem por provedor
        const providerId = appointment.userId;

        if (!serviceTypeCounts[serviceTypeId].providerDistribution[providerId]) {
          serviceTypeCounts[serviceTypeId].providerDistribution[providerId] = {
            providerId,
            providerName: appointment.provider?.fullName || `Profissional ${providerId.substring(0, 8)}`,
            count: 0
          };
        }

        serviceTypeCounts[serviceTypeId].providerDistribution[providerId].count++;
      });

      // Processar distribuição por tipo de serviço
      const serviceTypeDistribution = Object.values(serviceTypeCounts)
        .map(service => {
          const providers = Object.values(service.providerDistribution);
          const topProvider = providers.sort((a, b) => b.count - a.count)[0] || null;

          return {
            serviceTypeId: service.serviceTypeId,
            serviceTypeName: service.serviceTypeName,
            count: service.count,
            topProvider,
            percentageOfTotal: (service.count / appointments.length) * 100
          };
        })
        .sort((a, b) => b.count - a.count);

      // Distribuição por local
      const locationCounts = {};
      appointments.forEach(appointment => {
        const locationId = appointment.locationId;
        const locationName = appointment.location?.name || `Local ${locationId.substring(0, 8)}`;

        if (!locationCounts[locationId]) {
          locationCounts[locationId] = {
            locationId,
            locationName,
            count: 0
          };
        }

        locationCounts[locationId].count++;
      });

      const locationOccupancy = Object.values(locationCounts)
        .map(location => ({
          ...location,
          percentageOfTotal: (location.count / appointments.length) * 100
        }))
        .sort((a, b) => b.count - a.count);

      // Distribuição por hora do dia
      const hourCounts = Array(24).fill(0);
      appointments.forEach(appointment => {
        const startHour = new Date(appointment.startDate).getHours();
        hourCounts[startHour]++;
      });

      const timeDistribution = hourCounts.map((count, hour) => ({
        hour,
        label: `${String(hour).padStart(2, '0')}:00`,
        count,
        percentageOfTotal: (count / appointments.length) * 100
      }));

      // Distribuição por dia da semana
      const weekdayCounts = Array(7).fill(0);
      const weekdayLabels = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

      appointments.forEach(appointment => {
        const weekday = new Date(appointment.startDate).getDay();
        weekdayCounts[weekday]++;
      });

      const weekdayDistribution = weekdayCounts.map((count, day) => ({
        day,
        label: weekdayLabels[day],
        count,
        percentageOfTotal: (count / appointments.length) * 100
      }));

      // Retornar dados processados
      res.json({
        overallOccupancy,
        providerOccupancy,
        serviceTypeDistribution,
        locationOccupancy,
        timeDistribution,
        weekdayDistribution
      });
    } catch (error) {
      console.error("Erro ao calcular dados de ocupação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém detalhes de ocupação de um provedor específico
   * @param {Request} req Requisição
   * @param {Response} res Resposta
   */
  static async getProviderOccupancyDetails(req, res) {
    try {
      const { providerId } = req.params;
      const {
        period = 'month',
        startDate: startDateParam,
        endDate: endDateParam
      } = req.query;

      // Verificar se provider existe
      const provider = await prisma.user.findUnique({
        where: { id: providerId },
        select: { id: true, fullName: true }
      });

      if (!provider) {
        return res.status(404).json({ message: "Profissional não encontrado" });
      }

      // Determinar período de datas
      let startDate, endDate;

      if (startDateParam && endDateParam) {
        // Usar datas explícitas se fornecidas
        startDate = new Date(startDateParam);
        endDate = new Date(endDateParam);

        // Ajustar horas para pegar o dia completo
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        console.log(`Usando período personalizado: ${startDate.toISOString()} até ${endDate.toISOString()}`);
      } else {
        // Calcular intervalo de datas baseado no período
        const dateParam = req.query.date ? new Date(req.query.date) : new Date();
        startDate = new Date(dateParam);
        endDate = new Date(dateParam);

        console.log(`Calculando período a partir de: ${dateParam.toISOString()}`);

        switch (period) {
          case 'day':
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'week':
            const day = startDate.getDay();
            const diff = startDate.getDate() - day + (day === 0 ? -6 : 1);
            startDate.setDate(diff);
            startDate.setHours(0, 0, 0, 0);
            endDate.setDate(startDate.getDate() + 6);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'month':
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(endDate.getMonth() + 1);
            endDate.setDate(0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'year':
            startDate.setMonth(0, 1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(11, 31);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'custom':
            // Para período personalizado, deveria ter fornecido startDate e endDate
            console.error('Período personalizado selecionado, mas datas não foram fornecidas');
            return res.status(400).json({
              message: "Para período personalizado, é necessário fornecer startDate e endDate"
            });
          default:
            // Default: este mês
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(endDate.getMonth() + 1);
            endDate.setDate(0);
            endDate.setHours(23, 59, 59, 999);
        }

        console.log(`Período calculado: ${startDate.toISOString()} até ${endDate.toISOString()}`);
      }

      // Buscar agendamentos deste provedor no período
      const appointments = await prisma.scheduling.findMany({
        where: {
          userId: providerId,
          startDate: { gte: startDate, lte: endDate },
          // Se o usuário não for SYSTEM_ADMIN, limitar aos agendamentos da empresa dele
          ...((req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) ?
            { companyId: req.user.companyId } : {})
        },
        include: {
          serviceType: { select: { id: true, name: true } },
          location: { select: { id: true, name: true } }
        }
      });

      // Analisar ocupação por dia da semana
      const weekdayOccupancy = await Promise.all([0, 1, 2, 3, 4, 5, 6].map(async (dayOfWeek) => {
        // Buscar horários deste dia da semana
        const workingHours = await prisma.workingHours.findMany({
          where: {
            userId: providerId,
            dayOfWeek,
            isActive: true
          }
        });

        // Calcular horas disponíveis para este dia da semana
        let availableHoursPerDay = 0;

        workingHours.forEach(period => {
          // Verificar se há intervalo
          if (period.breakStartMinutes && period.breakEndMinutes) {
            const beforeBreakHours = (period.breakStartMinutes - period.startTimeMinutes) / 60;
            const afterBreakHours = (period.endTimeMinutes - period.breakEndMinutes) / 60;
            availableHoursPerDay += beforeBreakHours + afterBreakHours;
          } else {
            availableHoursPerDay += (period.endTimeMinutes - period.startTimeMinutes) / 60;
          }
        });

        // Contar quantos dias deste dia da semana existem no período
        const daysInPeriod = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        const daysOfThisWeekday = Array.from({ length: daysInPeriod })
          .map((_, i) => {
            const date = new Date(startDate);
            date.setDate(date.getDate() + i);
            return date.getDay();
          })
          .filter(day => day === dayOfWeek)
          .length;

        const totalAvailableHours = availableHoursPerDay * daysOfThisWeekday;

        // Contar agendamentos neste dia da semana
        const dayAppointments = appointments.filter(
          appointment => new Date(appointment.startDate).getDay() === dayOfWeek
        );

        // Calcular horas ocupadas
        let bookedHours = 0;
        dayAppointments.forEach(appointment => {
          const start = new Date(appointment.startDate);
          const end = new Date(appointment.endDate);
          const durationHours = (end - start) / (1000 * 60 * 60);
          bookedHours += durationHours;
        });

        // Calcular taxa de ocupação
        const occupancyRate = totalAvailableHours > 0
          ? (bookedHours / totalAvailableHours) * 100
          : 0;

        const weekdays = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

        return {
          day: dayOfWeek,
          label: weekdays[dayOfWeek],
          availableHours: totalAvailableHours,
          bookedHours,
          appointmentCount: dayAppointments.length,
          occupancyRate: Math.min(occupancyRate, 100)
        };
      }));

      // Contar tipos de serviço mais realizados
      const serviceTypeCounts = {};
      appointments.forEach(appointment => {
        const serviceTypeId = appointment.serviceTypeId;
        const serviceTypeName = appointment.serviceType?.name ||
                                `Serviço ${serviceTypeId.substring(0, 8)}`;

        if (!serviceTypeCounts[serviceTypeId]) {
          serviceTypeCounts[serviceTypeId] = {
            serviceTypeId,
            serviceTypeName,
            count: 0
          };
        }

        serviceTypeCounts[serviceTypeId].count++;
      });

      const topServices = Object.values(serviceTypeCounts)
        .map(service => ({
          ...service,
          percentageOfTotal: appointments.length > 0
            ? (service.count / appointments.length) * 100
            : 0
        }))
        .sort((a, b) => b.count - a.count);

      // Contar locais mais utilizados
      const locationCounts = {};
      appointments.forEach(appointment => {
        const locationId = appointment.locationId;
        const locationName = appointment.location?.name ||
                             `Local ${locationId.substring(0, 8)}`;

        if (!locationCounts[locationId]) {
          locationCounts[locationId] = {
            locationId,
            locationName,
            count: 0
          };
        }

        locationCounts[locationId].count++;
      });

      const topLocations = Object.values(locationCounts)
        .map(location => ({
          ...location,
          percentageOfTotal: appointments.length > 0
            ? (location.count / appointments.length) * 100
            : 0
        }))
        .sort((a, b) => b.count - a.count);

      // Calcular estatísticas gerais
      let totalAvailableHours = 0;
      let totalBookedHours = 0;

      weekdayOccupancy.forEach(day => {
        totalAvailableHours += day.availableHours;
        totalBookedHours += day.bookedHours;
      });

      const overallOccupancyRate = totalAvailableHours > 0
        ? (totalBookedHours / totalAvailableHours) * 100
        : 0;

      // Retornar dados
      res.json({
        providerId,
        providerName: provider.fullName,
        period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        appointmentCount: appointments.length,
        totalAvailableHours,
        totalBookedHours,
        overallOccupancyRate: Math.min(overallOccupancyRate, 100),
        weekdayOccupancy,
        topServices,
        topLocations
      });
    } catch (error) {
      console.error(`Erro ao obter detalhes de ocupação do provedor ${req.params.providerId}:`, error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Compara provedores para um tipo de serviço específico
   * @param {Request} req Requisição
   * @param {Response} res Resposta
   */
  static async getServiceTypeProviderComparison(req, res) {
    try {
      const { serviceTypeId } = req.params;
      const {
        period = 'month',
        startDate: startDateParam,
        endDate: endDateParam
      } = req.query;

      // Verificar se o tipo de serviço existe
      const serviceType = await prisma.serviceType.findUnique({
        where: { id: serviceTypeId }
      });

      if (!serviceType) {
        return res.status(404).json({ message: "Tipo de serviço não encontrado" });
      }

      // Determinar período de datas
      let startDate, endDate;

      if (startDateParam && endDateParam) {
        // Usar datas explícitas se fornecidas
        startDate = new Date(startDateParam);
        endDate = new Date(endDateParam);

        // Ajustar horas para pegar o dia completo
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        console.log(`Usando período personalizado: ${startDate.toISOString()} até ${endDate.toISOString()}`);
      } else {
        // Calcular intervalo de datas baseado no período
        const dateParam = req.query.date ? new Date(req.query.date) : new Date();
        startDate = new Date(dateParam);
        endDate = new Date(dateParam);

        console.log(`Calculando período a partir de: ${dateParam.toISOString()}`);

        switch (period) {
          case 'day':
            startDate.setHours(0, 0, 0, 0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'week':
            const day = startDate.getDay();
            const diff = startDate.getDate() - day + (day === 0 ? -6 : 1);
            startDate.setDate(diff);
            startDate.setHours(0, 0, 0, 0);
            endDate.setDate(startDate.getDate() + 6);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'month':
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(endDate.getMonth() + 1);
            endDate.setDate(0);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'year':
            startDate.setMonth(0, 1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(11, 31);
            endDate.setHours(23, 59, 59, 999);
            break;
          case 'custom':
            // Para período personalizado, deveria ter fornecido startDate e endDate
            console.error('Período personalizado selecionado, mas datas não foram fornecidas');
            return res.status(400).json({
              message: "Para período personalizado, é necessário fornecer startDate e endDate"
            });
          default:
            // Default: este mês
            startDate.setDate(1);
            startDate.setHours(0, 0, 0, 0);
            endDate.setMonth(endDate.getMonth() + 1);
            endDate.setDate(0);
            endDate.setHours(23, 59, 59, 999);
        }

        console.log(`Período calculado: ${startDate.toISOString()} até ${endDate.toISOString()}`);
      }

      // Buscar agendamentos deste tipo de serviço
      const appointments = await prisma.scheduling.findMany({
        where: {
          serviceTypeId,
          startDate: { gte: startDate, lte: endDate },
          // Se o usuário não for SYSTEM_ADMIN, limitar aos agendamentos da empresa dele
          ...((req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) ?
            { companyId: req.user.companyId } : {})
        },
        include: {
          provider: { select: { id: true, fullName: true } }
        }
      });

      if (appointments.length === 0) {
        return res.json({
          serviceTypeId,
          serviceTypeName: serviceType.name,
          providerComparison: []
        });
      }

      // Agrupar por provedor
      const providerCounts = {};

      appointments.forEach(appointment => {
        const providerId = appointment.userId;
        const providerName = appointment.provider?.fullName ||
                            `Profissional ${providerId.substring(0, 8)}`;

        if (!providerCounts[providerId]) {
          providerCounts[providerId] = {
            providerId,
            providerName,
            count: 0,
            duration: 0
          };
        }

        providerCounts[providerId].count++;

        // Calcular duração
        const start = new Date(appointment.startDate);
        const end = new Date(appointment.endDate);
        const durationHours = (end - start) / (1000 * 60 * 60);
        providerCounts[providerId].duration += durationHours;
      });

      // Processar dados
      const providerComparison = Object.values(providerCounts)
        .map(provider => ({
          ...provider,
          percentageOfCount: (provider.count / appointments.length) * 100,
          averageDuration: provider.count > 0 ? provider.duration / provider.count : 0
        }))
        .sort((a, b) => b.count - a.count);

      // Retornar resultados
      res.json({
        serviceTypeId,
        serviceTypeName: serviceType.name,
        totalAppointments: appointments.length,
        period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        providerComparison
      });
    } catch (error) {
      console.error(`Erro ao comparar provedores para o serviço ${req.params.serviceTypeId}:`, error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  OccupancyController
};