// src/services/usersService.js
import { api } from "@/utils/api";

/**
 * Serviço para gerenciar operações relacionadas a usuários
 */
export const usersService = {
  /**
   * Obtém a lista de usuários com filtros
   * @param {Object} params - Parâmetros de filtro
   * @param {number} params.page - Página atual
   * @param {number} params.limit - Limite de itens por página
   * @param {string} params.search - Termo de busca
   * @param {boolean} params.active - Filtro por status (ativo/inativo)
   * @param {string} params.module - Filtro por módulo
   * @param {boolean} params.excludeSystemAdmin - Se deve excluir usuários system_admin
   * @returns {Promise<Object>} Lista de usuários com metadados
   */
  async getUsers(params = {}) {
    try {
      const response = await api.get("/users", { params });
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar usuários:", error);
      throw error;
    }
  },

  /**
   * Obtém um usuário pelo ID
   * @param {string} userId - ID do usuário
   * @returns {Promise<Object>} Dados do usuário
   */
  async getUserById(userId) {
    try {
      const response = await api.get(`/users/${userId}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar usuário ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Cria um novo usuário
   * @param {Object} userData - Dados do usuário
   * @returns {Promise<Object>} Usuário criado
   */
  async createUser(userData) {
    try {
      const response = await api.post("/users", userData);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar usuário:", error);
      throw error;
    }
  },

  /**
   * Atualiza um usuário existente
   * @param {string} userId - ID do usuário
   * @param {Object} userData - Dados atualizados do usuário
   * @returns {Promise<Object>} Usuário atualizado
   */
  async updateUser(userId, userData) {
    try {
      const response = await api.put(`/users/${userId}`, userData);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar usuário ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Exclui um usuário
   * @param {string} userId - ID do usuário
   * @returns {Promise<void>}
   */
  async deleteUser(userId) {
    try {
      await api.delete(`/users/${userId}`);
    } catch (error) {
      console.error(`Erro ao excluir usuário ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Alterna o status de um usuário (ativo/inativo)
   * @param {string} userId - ID do usuário
   * @returns {Promise<Object>} Usuário com status atualizado
   */
  async toggleUserStatus(userId) {
    try {
      const response = await api.patch(`/users/${userId}/status`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao alterar status do usuário ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Atualiza os módulos de um usuário
   * @param {string} userId - ID do usuário
   * @param {Array<string>} modules - Lista de módulos
   * @returns {Promise<Object>} Usuário com módulos atualizados
   */
  async updateUserModules(userId, modules) {
    try {
      const response = await api.patch(`/users/${userId}/modules`, { modules });
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar módulos do usuário ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Atualiza a função (role) de um usuário
   * @param {string} userId - ID do usuário
   * @param {string} role - Nova função (SYSTEM_ADMIN, COMPANY_ADMIN, EMPLOYEE)
   * @returns {Promise<Object>} Usuário com função atualizada
   */
  async updateUserRole(userId, role) {
    try {
      const response = await api.patch(`/users/${userId}/role`, { role });
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar função do usuário ${userId}:`, error);
      throw error;
    }
  },

  /**
   * Atualiza as permissões de um usuário
   * @param {string} userId - ID do usuário
   * @param {Array<Object>} permissions - Lista de permissões
   * @returns {Promise<Object>} Usuário com permissões atualizadas
   */
  async updateUserPermissions(userId, permissions) {
    try {
      const response = await api.patch(`/users/${userId}/permissions`, { permissions });
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar permissões do usuário ${userId}:`, error);
      throw error;
    }
  }
};