const prisma = require('./prisma');
const timezoneHelper = require('./timezoneHelper');
const holidayHelper = require('./holidayHelper');

class AvailabilityHelper {
  /**
   * Verifica se um paciente já tem agendamento no mesmo horário
   * @param {string} personId - ID do paciente
   * @param {Date|string} startDate - Data e hora de início
   * @param {Date|string} endDate - Data e hora de término
   * @param {string} excludeId - ID do agendamento a ser excluído da verificação (para edição)
   * @returns {Promise<Object>} - Resultado da verificação
   */
  static async checkPatientAvailability(personId, startDate, endDate, excludeId = null) {
    // Converte para Date se necessário
    startDate = new Date(startDate);
    endDate = new Date(endDate);

    // Log para debug inicial
    console.log('Verificando disponibilidade do paciente:', {
      personId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      excludeId: excludeId || 'nenhum' // Log do ID a ser excluído
    });

    // Verifica se já existe agendamento no horário para este paciente
    const whereCondition = {
      Person: {
        some: {
          id: personId
        }
      },
      status: {
        not: 'CANCELLED'
      },
      OR: [
        {
          AND: [
            { startDate: { lte: startDate } },
            { endDate: { gt: startDate } }
          ]
        },
        {
          AND: [
            { startDate: { lt: endDate } },
            { endDate: { gte: endDate } }
          ]
        }
      ]
    };

    // Se houver um ID para excluir (caso de edição), adicionar à condição
    if (excludeId) {
      whereCondition.id = { not: excludeId };
      console.log(`Excluindo agendamento ${excludeId} da verificação de disponibilidade do paciente`);
    }

    const existingScheduling = await prisma.scheduling.findFirst({
      where: whereCondition,
      include: {
        provider: {
          select: {
            fullName: true
          }
        }
      }
    });

    if (existingScheduling) {
      console.log(`Conflito encontrado: Paciente já tem agendamento no horário (ID: ${existingScheduling.id})`);
      return {
        available: false,
        reason: 'PATIENT_CONFLICT',
        conflictData: existingScheduling
      };
    }

    return {
      available: true
    };
  }
  static async checkProviderAvailability(userId, startDate, endDate, excludeId = null, companyId = null) {
    // Converte para Date se necessário
    startDate = new Date(startDate);
    endDate = new Date(endDate);

    // Log para debug inicial
    console.log('Verificando disponibilidade (UTC):', {
      userId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      excludeId: excludeId || 'nenhum' // Log do ID a ser excluído
    });

    // Buscar o usuário para obter o companyId se não foi fornecido
    if (!companyId) {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true }
      });
      companyId = user?.companyId;
    }

    // Obter o timezone da empresa
    const timezone = await timezoneHelper.getCompanyTimezone(companyId, prisma);
    console.log(`Usando timezone: ${timezone}`);

    // Converter datas para o timezone da empresa
    const startDateLocal = timezoneHelper.convertToTimezone(startDate, timezone);
    const endDateLocal = timezoneHelper.convertToTimezone(endDate, timezone);

    // Log para debug após conversão
    console.log('Após conversão para timezone da empresa:', {
      startDateLocal: startDateLocal.toLocaleString('pt-BR'),
      endDateLocal: endDateLocal.toLocaleString('pt-BR'),
      dayOfWeek: startDateLocal.getDay(),
      timezone
    });

    // Verifica se já existe agendamento no horário
    const whereCondition = {
      userId,
      status: {
        not: 'CANCELLED'
      },
      OR: [
        {
          AND: [
            { startDate: { lte: startDate } },
            { endDate: { gt: startDate } }
          ]
        },
        {
          AND: [
            { startDate: { lt: endDate } },
            { endDate: { gte: endDate } }
          ]
        }
      ]
    };

    // Se houver um ID para excluir (caso de edição), adicionar à condição
    if (excludeId) {
      whereCondition.id = { not: excludeId };
      console.log(`Excluindo agendamento ${excludeId} da verificação de disponibilidade do profissional`);
    }

    const existingScheduling = await prisma.scheduling.findFirst({
      where: whereCondition
    });

    if (existingScheduling) {
      return {
        available: false,
        reason: 'CONFLICT',
        conflictData: existingScheduling
      };
    }

    // Verificar se é feriado
    const isHoliday = await holidayHelper.isHoliday(startDate, companyId);
    if (isHoliday) {
      return {
        available: false,
        reason: 'HOLIDAY'
      };
    }

    // Verifica horário de trabalho usando a data no timezone da empresa
    const dayOfWeek = timezoneHelper.getDayOfWeekInTimezone(startDate, timezone);

    // Converter horários para minutos desde a meia-noite (usando timezone da empresa)
    const startTimeMinutes = timezoneHelper.getTimeMinutesInTimezone(startDate, timezone);
    const endTimeMinutes = timezoneHelper.getTimeMinutesInTimezone(endDate, timezone);

    console.log('Buscando horário de trabalho com:', {
      userId,
      dayOfWeek,
      startTimeMinutes,
      endTimeMinutes
    });

    // Buscar todos os horários de trabalho do usuário para este dia para debug
    const allWorkingHours = await prisma.workingHours.findMany({
      where: {
        userId,
        dayOfWeek,
        isActive: true,
      }
    });

    console.log('Horários de trabalho encontrados:', JSON.stringify(allWorkingHours));

    // Buscar todos os horários de trabalho ativos para este dia
    const workingHoursList = await prisma.workingHours.findMany({
      where: {
        userId,
        dayOfWeek,
        isActive: true,
      }
    });

    console.log(`Encontrados ${workingHoursList.length} horários de trabalho para o dia ${dayOfWeek}`);

    // Verificar se o horário solicitado está dentro de algum dos horários de trabalho
    let isWithinWorkingHours = false;
    let matchedWorkingHours = null;

    for (const hours of workingHoursList) {
      if (startTimeMinutes >= hours.startTimeMinutes && endTimeMinutes <= hours.endTimeMinutes) {
        isWithinWorkingHours = true;
        matchedWorkingHours = hours;
        break;
      }
    }

    console.log('Resultado da busca por horário válido:', isWithinWorkingHours ? 'Encontrado' : 'Não encontrado');

    if (!isWithinWorkingHours) {
      return {
        available: false,
        reason: 'OUTSIDE_WORKING_HOURS'
      };
    }

    // Verifica se está no horário de intervalo
    if (matchedWorkingHours.breakStartMinutes && matchedWorkingHours.breakEndMinutes) {
      const isInBreak = (startTimeMinutes >= matchedWorkingHours.breakStartMinutes && startTimeMinutes < matchedWorkingHours.breakEndMinutes) ||
                       (endTimeMinutes > matchedWorkingHours.breakStartMinutes && endTimeMinutes <= matchedWorkingHours.breakEndMinutes) ||
                       (startTimeMinutes <= matchedWorkingHours.breakStartMinutes && endTimeMinutes >= matchedWorkingHours.breakEndMinutes);

      if (isInBreak) {
        return {
          available: false,
          reason: 'BREAK_TIME'
        };
      }
    }

    return {
      available: true
    };
  }
}

module.exports = AvailabilityHelper;