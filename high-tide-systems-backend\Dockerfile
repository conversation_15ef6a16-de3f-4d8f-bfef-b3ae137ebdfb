FROM node:18.20.0

# Instalar dependências do sistema necessárias
RUN apt-get update && apt-get install -y python3 make g++ git

WORKDIR /usr/src/app

# Copiar arquivos de configuração primeiro
COPY package*.json ./
COPY prisma ./prisma/

# Instalar dependências incluindo devDependencies
RUN npm config set fetch-retry-maxtimeout 600000 \
    && npm config set fetch-retry-mintimeout 10000 \
    && npm config set fetch-retries 5 \
    && npm install --no-fund --no-audit --ignore-scripts \
    && npm install redis@4.7.0 \
    && npx prisma generate

# Copiar o resto do código do backend
COPY . .

# Instalar nodemon globalmente
RUN npm install -g nodemon

EXPOSE 5000

# Não há CMD por enquanto