'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';

export function RouteGuard({ 
  children, 
  requiredPermission = null,
  requiredModule = null,
  requireAll = false,
  fallbackPath = '/dashboard'
}) {
  const router = useRouter();
  const { user, loading } = useAuth();
  const { can, canAll, canAny, hasModule, isAdmin } = usePermissions();
  
  useEffect(() => {
    if (!loading && user) {
      let hasAccess = true;
      
      // Verificar módulo requerido
      if (requiredModule && !isAdmin()) {
        if (Array.isArray(requiredModule)) {
          hasAccess = requireAll
            ? requiredModule.every(m => hasModule(m))
            : requiredModule.some(m => hasModule(m));
        } else {
          hasAccess = hasModule(requiredModule);
        }
      }
      
      // Verificar permissão requerida
      if (hasAccess && requiredPermission) {
        if (Array.isArray(requiredPermission)) {
          hasAccess = requireAll
            ? canAll(requiredPermission)
            : canAny(requiredPermission);
        } else {
          hasAccess = can(requiredPermission);
        }
      }
      
      // Redirecionar se não tiver acesso
      if (!hasAccess) {
        router.push(fallbackPath);
      }
    }
  }, [user, loading, requiredPermission, requiredModule, requireAll, router, fallbackPath, can, canAll, canAny, hasModule, isAdmin]);
  
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }
  
  return children;
}