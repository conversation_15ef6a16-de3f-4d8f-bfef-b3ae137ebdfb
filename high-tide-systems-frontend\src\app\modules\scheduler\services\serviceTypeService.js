import { api } from "@/utils/api";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";
import { exportService } from "@/app/services/exportService";


export const serviceTypeService = {
  // Listar tipos de serviço com suporte a filtros
  getServiceTypes: async ({ search, companyId, serviceTypeIds } = {}) => {
    try {
      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (companyId) params.append('companyId', companyId);

      // Adicionar suporte para múltiplos IDs de tipos de serviço
      if (serviceTypeIds && Array.isArray(serviceTypeIds) && serviceTypeIds.length > 0) {
        serviceTypeIds.forEach(id => params.append('serviceTypeIds', id));
      }

      const response = await api.get(`/service-types?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar tipos de serviço:", error);
      throw error;
    }
  },

  // Obter um tipo de serviço específico
  getServiceType: async (id) => {
    try {
      const response = await api.get(`/service-types/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar tipo de serviço ${id}:`, error);
      throw error;
    }
  },

  // Criar um novo tipo de serviço
  createServiceType: async (data) => {
    try {
      const response = await api.post('/service-types', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar tipo de serviço:", error);
      throw error;
    }
  },

  // Atualizar um tipo de serviço existente
  updateServiceType: async (id, data) => {
    try {
      const response = await api.put(`/service-types/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar tipo de serviço ${id}:`, error);
      throw error;
    }
  },

  // Excluir um tipo de serviço
  deleteServiceType: async (id) => {
    try {
      await api.delete(`/service-types/${id}`);
      return true;
    } catch (error) {
      console.error(`Erro ao excluir tipo de serviço ${id}:`, error);
      throw error;
    }
  },
  /**
 * Exporta a lista de tipos de serviço com os filtros aplicados
 * @param {Object} filters - Filtros atuais (busca, companyId, etc)
 * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
 * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
 */
  exportServiceTypes: async (filters, exportFormat = "xlsx") => {
    try {
      // Obter os dados filtrados da API
      const response = await serviceTypeService.getServiceTypes(filters);

      // Extrair os dados dos tipos de serviço
      const data = response?.serviceTypes || [];

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "name", header: "Nome do Serviço" },
        {
          key: "value",
          header: "Valor",
          format: (value) => new Intl.NumberFormat("pt-BR", {
            style: "currency",
            currency: "BRL",
          }).format(value),
          align: "right",
          width: 25
        },
        {
          key: "companyName",
          header: "Empresa",
          format: (value, item) => item.company ? item.company.name : "N/A"
        },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(serviceType => {
        return {
          name: serviceType.name || "",
          value: serviceType.value || 0,
          companyName: serviceType.company ? serviceType.company.name : "N/A",
          createdAt: serviceType.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.companyId) {
        const companyName = data.length > 0 && data[0].company ? data[0].company.name : "Selecionada";
        subtitleParts.push(`Empresa: ${companyName}`);
      }
      if (filters.serviceTypeIds && Array.isArray(filters.serviceTypeIds) && filters.serviceTypeIds.length > 0) {
        subtitleParts.push(`Tipos de Serviço: ${filters.serviceTypeIds.length} selecionados`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "tipos-de-servico",
        columns,
        title: "Lista de Tipos de Serviço",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar tipos de serviço:", error);
      return false;
    }
  }
};

export default serviceTypeService;