// test-redis-container.js
require('dotenv').config();
const { createClient } = require('redis');

async function testRedisDirectly() {
  console.log('=== TESTE DIRETO DO REDIS (DENTRO DO CONTAINER) ===\n');
  
  try {
    // Conectar ao Redis
    console.log('Conectando ao Redis...');
    const redisUrl = process.env.REDIS_URL || 'redis://redis:6379';
    const client = createClient({ url: redisUrl });
    
    // Configurar handlers de eventos
    client.on('error', (err) => {
      console.error('Erro no Redis:', err);
    });
    
    client.on('connect', () => {
      console.log('Conectado ao Redis');
    });
    
    // Conectar
    await client.connect();
    
    // Testar operações básicas
    console.log('\nTestando operações básicas...');
    
    // SET
    const key = 'test:container';
    const value = { message: 'Teste dentro do container', timestamp: new Date().toISOString() };
    console.log(`Armazenando valor com chave "${key}"...`);
    await client.set(key, JSON.stringify(value), { EX: 60 });
    console.log('✅ Valor armazenado com sucesso');
    
    // GET
    console.log(`\nRecuperando valor com chave "${key}"...`);
    const storedValue = await client.get(key);
    console.log('Valor recuperado:', JSON.parse(storedValue));
    console.log('✅ Valor recuperado com sucesso');
    
    // DEL
    console.log(`\nExcluindo chave "${key}"...`);
    await client.del(key);
    console.log('✅ Chave excluída com sucesso');
    
    // Verificar se a chave foi excluída
    const deletedValue = await client.get(key);
    if (deletedValue === null) {
      console.log('✅ Chave não existe mais no Redis');
    } else {
      console.log('❌ Chave ainda existe no Redis');
    }
    
    // Testar padrões de chave
    console.log('\nTestando padrões de chave...');
    
    // Criar várias chaves
    for (let i = 1; i <= 5; i++) {
      await client.set(`test:pattern:${i}`, JSON.stringify({ index: i }), { EX: 60 });
    }
    console.log('✅ Criadas 5 chaves com o padrão "test:pattern:*"');
    
    // Listar chaves com o padrão
    console.log('\nListando chaves com o padrão "test:pattern:*"...');
    let cursor = 0;
    let keys = [];
    
    do {
      const result = await client.scan(cursor, { MATCH: 'test:pattern:*', COUNT: 10 });
      cursor = result.cursor;
      keys = keys.concat(result.keys);
    } while (cursor !== 0);
    
    console.log('Chaves encontradas:', keys);
    console.log(`✅ Encontradas ${keys.length} chaves`);
    
    // Excluir todas as chaves com o padrão
    console.log('\nExcluindo todas as chaves com o padrão...');
    if (keys.length > 0) {
      await client.del(keys);
      console.log('✅ Todas as chaves excluídas com sucesso');
    }
    
    // Fechar conexão
    console.log('\nFechando conexão com Redis...');
    await client.quit();
    console.log('✅ Conexão fechada com sucesso');
    
    console.log('\n=== TESTE CONCLUÍDO COM SUCESSO ===');
  } catch (error) {
    console.error('Erro durante o teste:', error);
    process.exit(1);
  }
}

// Executar o teste
testRedisDirectly().catch(error => {
  console.error('Erro fatal durante o teste:', error);
  process.exit(1);
});
