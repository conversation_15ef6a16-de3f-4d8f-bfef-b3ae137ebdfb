"use client";

import { useParams } from "next/navigation";
import { StrictMode } from "react";
import CurriculumFolderDetails from "./CurriculumFolderDetails";
import { Protected } from "@/components/permissions/Protected";

export default function CurriculumFolderDetailsPage() {
  const params = useParams();
  const folderId = params.id;

  return (
    <StrictMode>
      <Protected permission="abaplus.curriculum-folders.view">
        <CurriculumFolderDetails folderId={folderId} />
      </Protected>
    </StrictMode>
  );
}
