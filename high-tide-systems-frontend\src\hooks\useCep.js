// src/hooks/useCep.js
import { useState } from 'react';
import { cepService } from '@/app/modules/common/services/cepService';

/**
 * Hook para busca de CEP e preenchimento automático de endereço
 */
export function useCep() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  /**
   * Busca endereço a partir do CEP e atualiza o formulário
   * @param {string} cep - CEP a ser consultado
   * @param {Function} setFormData - Função para atualizar o estado do formulário
   * @param {Object} fieldMapping - Mapeamento dos campos do endereço para os campos do formulário
   * @returns {Promise<Object|null>} - Dados do endereço ou null em caso de erro
   */
  const searchAddressByCep = async (cep, setFormData, fieldMapping = {}) => {
    // Limpa erro anterior
    setError(null);

    // Verifica se o CEP tem pelo menos 8 dígitos (considerando possível máscara)
    const cleanCep = cep.replace(/\D/g, '');
    if (cleanCep.length !== 8) {
      return null;
    }

    setIsLoading(true);

    try {
      const addressData = await cepService.searchByCep(cep);
      console.log('Dados do endereço recebidos:', addressData);

      // Atualiza o formulário com os dados do endereço
      setFormData(prevData => {
        console.log('Estado atual do formulário antes da atualização:', prevData);

        // Cria um objeto com os campos mapeados
        const updatedFields = {};

        // Mapeamento padrão se não for fornecido
        const defaultMapping = {
          logradouro: 'address',
          bairro: 'neighborhood',
          localidade: 'city',
          uf: 'state',
          cep: 'postalCode'
        };

        // Usa o mapeamento fornecido ou o padrão
        const mapping = { ...defaultMapping, ...fieldMapping };
        console.log('Mapeamento de campos:', mapping);

        // Para cada campo retornado pela API, mapeia para o campo correspondente no formulário
        Object.keys(addressData).forEach(apiField => {
          const formField = mapping[apiField];
          console.log(`Mapeando ${apiField} para ${formField}`);

          if (formField && addressData[apiField]) {
            // Suporte para campos aninhados (ex: "person.address")
            if (formField.includes('.')) {
              const [parent, child] = formField.split('.');
              console.log(`Campo aninhado: ${parent}.${child} = ${addressData[apiField]}`);
              // Garantir que todos os campos aninhados existentes sejam preservados
              if (!updatedFields[parent]) {
                updatedFields[parent] = { ...prevData[parent] };
                console.log(`Criando objeto para ${parent} com valores existentes:`, updatedFields[parent]);
              }

              // Atualizar o campo específico
              updatedFields[parent][child] = addressData[apiField];
              console.log(`Atualizado ${parent}.${child} para ${addressData[apiField]}. Objeto atual:`, updatedFields[parent]);
            } else {
              console.log(`Campo simples: ${formField} = ${addressData[apiField]}`);

              // Verificar se o valor é diferente do atual antes de atualizar
              if (prevData[formField] !== addressData[apiField]) {
                console.log(`Valor diferente do atual: ${prevData[formField]} -> ${addressData[apiField]}`);
                updatedFields[formField] = addressData[apiField];
              } else {
                console.log(`Valor igual ao atual, não será atualizado: ${prevData[formField]}`);
              }
            }
          }
        });

        console.log('Campos atualizados:', updatedFields);

        // Cria o novo estado do formulário
        const newState = { ...prevData, ...updatedFields };
        console.log('Novo estado do formulário:', newState);

        // Log específico para os campos de endereço
        console.log('Valores dos campos de endereço atualizados:');
        console.log('address:', newState.address);
        console.log('neighborhood:', newState.neighborhood);
        console.log('city:', newState.city);
        console.log('state:', newState.state);
        console.log('postalCode:', newState.postalCode);

        return newState;
      });

      return addressData;
    } catch (err) {
      setError(err.message);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    searchAddressByCep,
    isLoading,
    error
  };
}
