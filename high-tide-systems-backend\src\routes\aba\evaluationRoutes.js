// src/routes/aba/evaluationRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { EvaluationController, createEvaluationValidation, updateEvaluationValidation } = require('../../controllers/aba/evaluationController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para avaliações
router.post('/', createEvaluationValidation, EvaluationController.create);
router.get('/', EvaluationController.list);
router.get('/:id', EvaluationController.get);
router.put('/:id', updateEvaluationValidation, EvaluationController.update);
router.patch('/:id/status', EvaluationController.toggleStatus);
router.delete('/:id', EvaluationController.delete);

module.exports = router;
