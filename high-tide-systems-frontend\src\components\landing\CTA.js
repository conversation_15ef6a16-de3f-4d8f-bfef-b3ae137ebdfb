'use client';

import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import {
  ArrowRight,
  Check,
  Calendar,
  Mail,
  Phone,
  Building,
  User,
  MessageSquare,
  Clock,
  AlertCircle,
  CheckCircle,
  ChevronDown,
  Loader2
} from 'lucide-react';
import { useState, useEffect } from 'react';

// Opções de interesse
const interestOptions = [
  { id: 'demo', label: 'Demonstração do sistema', icon: <Calendar className="h-4 w-4" /> },
  { id: 'pricing', label: 'Informações sobre preços', icon: <Building className="h-4 w-4" /> },
  { id: 'support', label: 'Suporte técnico', icon: <MessageSquare className="h-4 w-4" /> },
  { id: 'partnership', label: 'Parceria comercial', icon: <User className="h-4 w-4" /> },
  { id: 'other', label: 'Outro assunto', icon: <Mail className="h-4 w-4" /> }
];

const CTA = () => {
  const [formState, setFormState] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    interest: '',
    message: '',
    submitted: false,
    submitting: false,
    errors: {}
  });

  const [interestDropdownOpen, setInterestDropdownOpen] = useState(false);
  const [formTouched, setFormTouched] = useState({});

  // Validação em tempo real
  useEffect(() => {
    if (Object.keys(formTouched).length > 0) {
      validateForm();
    }
  }, [formState.name, formState.email, formState.phone, formState.company, formState.interest]);

  const validateForm = () => {
    const errors = {};

    if (formTouched.name && !formState.name.trim()) {
      errors.name = 'Nome é obrigatório';
    }

    if (formTouched.email) {
      if (!formState.email.trim()) {
        errors.email = 'E-mail é obrigatório';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formState.email)) {
        errors.email = 'E-mail inválido';
      }
    }

    if (formTouched.phone) {
      if (!formState.phone.trim()) {
        errors.phone = 'Telefone é obrigatório';
      } else if (!/^(\+\d{1,3})?\s?\(?\d{2}\)?[\s.-]?\d{4,5}[\s.-]?\d{4}$/.test(formState.phone)) {
        errors.phone = 'Telefone inválido';
      }
    }

    if (formTouched.company && !formState.company.trim()) {
      errors.company = 'Nome da clínica é obrigatório';
    }

    if (formTouched.interest && !formState.interest) {
      errors.interest = 'Selecione um assunto';
    }

    setFormState(prev => ({ ...prev, errors }));
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormState(prev => ({ ...prev, [name]: value }));
    setFormTouched(prev => ({ ...prev, [name]: true }));
  };

  const handleInterestSelect = (interest) => {
    setFormState(prev => ({ ...prev, interest }));
    setFormTouched(prev => ({ ...prev, interest: true }));
    setInterestDropdownOpen(false);
  };

  const handleBlur = (e) => {
    const { name } = e.target;
    setFormTouched(prev => ({ ...prev, [name]: true }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormTouched({
      name: true,
      email: true,
      phone: true,
      company: true,
      interest: true
    });

    if (!validateForm()) {
      return;
    }

    setFormState(prev => ({ ...prev, submitting: true }));

    // Simulação de envio com delay para mostrar o estado de loading
    setTimeout(() => {
      // Aqui você adicionaria a lógica para enviar o formulário
      setFormState(prev => ({
        ...prev,
        submitted: true,
        submitting: false
      }));
    }, 1500);
  };

  return (
    <section id="contact" className="py-20 bg-white dark:bg-gray-900 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-300 via-primary-500 to-primary-300" />
      <div className="absolute -top-24 -right-24 w-64 h-64 bg-primary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob" />
      <div className="absolute -bottom-24 -left-24 w-64 h-64 bg-secondary-300 rounded-full mix-blend-multiply filter blur-3xl opacity-20 animate-blob animation-delay-2000" />

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Pronto para transformar a gestão da sua clínica?
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300">
            Agende uma demonstração gratuita e descubra como o High Tide Systems pode otimizar seus processos.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Left Column - Benefits */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 h-full">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Por que escolher o High Tide Systems?
              </h3>

              <ul className="space-y-4">
                {[
                  "Implementação rápida e sem complicações",
                  "Suporte técnico especializado e ágil",
                  "Atualizações constantes com novos recursos",
                  "Segurança e conformidade com a LGPD",
                  "Interface intuitiva e fácil de usar",
                  "Acesso de qualquer dispositivo, a qualquer hora"
                ].map((benefit, index) => (
                  <li key={index} className="flex items-start">
                    <div className="mt-1 mr-3 flex-shrink-0">
                      <Check className="h-5 w-5 text-primary-500" />
                    </div>
                    <span className="text-gray-600 dark:text-gray-300">{benefit}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-700">
                <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                  Outras formas de contato
                </h4>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <Mail className="h-5 w-5 text-primary-500 mr-3" />
                    <a href="mailto:<EMAIL>" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                      <EMAIL>
                    </a>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-5 w-5 text-primary-500 mr-3" />
                    <a href="tel:+5511999999999" className="text-gray-600 dark:text-gray-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors">
                      +55 (11) 99999-9999
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Right Column - Form or CTA */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            {formState.submitted ? (
              <motion.div
                className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 h-full flex flex-col items-center justify-center text-center"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-6">
                  <Check className="h-10 w-10 text-green-600 dark:text-green-400" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                  Obrigado pelo contato!
                </h3>
                <p className="text-gray-600 dark:text-gray-300 mb-6">
                  Recebemos sua solicitação e entraremos em contato em breve para agendar sua demonstração.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 w-full max-w-md">
                  <Link
                    href="/login"
                    className="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors flex-1"
                  >
                    Começar agora
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                  <button
                    onClick={() => setFormState(prev => ({ ...prev, submitted: false, name: '', email: '', phone: '', company: '', interest: '', message: '' }))}
                    className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors flex-1"
                  >
                    Novo contato
                  </button>
                </div>
              </motion.div>
            ) : (
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 relative overflow-hidden">
                {/* Decorative elements */}
                <div className="absolute -top-20 -right-20 w-40 h-40 bg-primary-100 dark:bg-primary-900/20 rounded-full opacity-50"></div>
                <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-primary-100 dark:bg-primary-900/20 rounded-full opacity-50"></div>

                <div className="relative">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                    Agende uma demonstração
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-6">
                    Preencha o formulário abaixo e nossa equipe entrará em contato
                  </p>

                  <form onSubmit={handleSubmit} className="space-y-5">
                    {/* Nome completo */}
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        Nome completo
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formState.name}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          className={`w-full px-4 py-3 border ${
                            formState.errors.name
                              ? 'border-red-500 dark:border-red-500'
                              : formTouched.name && !formState.errors.name
                                ? 'border-green-500 dark:border-green-500'
                                : 'border-gray-300 dark:border-gray-600'
                          } rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors`}
                          placeholder="Seu nome completo"
                          required
                        />
                        {formState.errors.name && (
                          <p className="mt-1 text-sm text-red-500 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {formState.errors.name}
                          </p>
                        )}
                        {formTouched.name && !formState.errors.name && formState.name && (
                          <div className="absolute right-3 top-3 text-green-500">
                            <CheckCircle className="h-5 w-5" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* E-mail */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                        <Mail className="h-4 w-4 mr-1" />
                        E-mail
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formState.email}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          className={`w-full px-4 py-3 border ${
                            formState.errors.email
                              ? 'border-red-500 dark:border-red-500'
                              : formTouched.email && !formState.errors.email && formState.email
                                ? 'border-green-500 dark:border-green-500'
                                : 'border-gray-300 dark:border-gray-600'
                          } rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors`}
                          placeholder="<EMAIL>"
                          required
                        />
                        {formState.errors.email && (
                          <p className="mt-1 text-sm text-red-500 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {formState.errors.email}
                          </p>
                        )}
                        {formTouched.email && !formState.errors.email && formState.email && (
                          <div className="absolute right-3 top-3 text-green-500">
                            <CheckCircle className="h-5 w-5" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Telefone */}
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                        <Phone className="h-4 w-4 mr-1" />
                        Telefone
                      </label>
                      <div className="relative">
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formState.phone}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          className={`w-full px-4 py-3 border ${
                            formState.errors.phone
                              ? 'border-red-500 dark:border-red-500'
                              : formTouched.phone && !formState.errors.phone && formState.phone
                                ? 'border-green-500 dark:border-green-500'
                                : 'border-gray-300 dark:border-gray-600'
                          } rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors`}
                          placeholder="(11) 99999-9999"
                          required
                        />
                        {formState.errors.phone && (
                          <p className="mt-1 text-sm text-red-500 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {formState.errors.phone}
                          </p>
                        )}
                        {formTouched.phone && !formState.errors.phone && formState.phone && (
                          <div className="absolute right-3 top-3 text-green-500">
                            <CheckCircle className="h-5 w-5" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Nome da clínica */}
                    <div>
                      <label htmlFor="company" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                        <Building className="h-4 w-4 mr-1" />
                        Nome da clínica/consultório
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          id="company"
                          name="company"
                          value={formState.company}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          className={`w-full px-4 py-3 border ${
                            formState.errors.company
                              ? 'border-red-500 dark:border-red-500'
                              : formTouched.company && !formState.errors.company && formState.company
                                ? 'border-green-500 dark:border-green-500'
                                : 'border-gray-300 dark:border-gray-600'
                          } rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors`}
                          placeholder="Nome da sua clínica"
                          required
                        />
                        {formState.errors.company && (
                          <p className="mt-1 text-sm text-red-500 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {formState.errors.company}
                          </p>
                        )}
                        {formTouched.company && !formState.errors.company && formState.company && (
                          <div className="absolute right-3 top-3 text-green-500">
                            <CheckCircle className="h-5 w-5" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Assunto de interesse */}
                    <div>
                      <label htmlFor="interest" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        Assunto de interesse
                      </label>
                      <div className="relative">
                        <div className="relative">
                          <button
                            type="button"
                            className={`w-full px-4 py-3 border ${
                              formState.errors.interest
                                ? 'border-red-500 dark:border-red-500'
                                : formTouched.interest && !formState.errors.interest && formState.interest
                                  ? 'border-green-500 dark:border-green-500'
                                  : 'border-gray-300 dark:border-gray-600'
                            } rounded-md shadow-sm bg-white dark:bg-gray-700 text-left flex items-center justify-between focus:outline-none focus:ring-2 focus:ring-primary-500 transition-colors`}
                            onClick={() => setInterestDropdownOpen(!interestDropdownOpen)}
                          >
                            <span className={formState.interest ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
                              {formState.interest
                                ? interestOptions.find(option => option.id === formState.interest)?.label
                                : 'Selecione um assunto'}
                            </span>
                            <ChevronDown className="h-5 w-5 text-gray-400" />
                          </button>

                          {interestDropdownOpen && (
                            <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-700 shadow-lg rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto max-h-60">
                              {interestOptions.map((option) => (
                                <div
                                  key={option.id}
                                  className={`${
                                    formState.interest === option.id
                                      ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                                      : 'text-gray-900 dark:text-white'
                                  } cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors`}
                                  onClick={() => handleInterestSelect(option.id)}
                                >
                                  <div className="flex items-center">
                                    <span className="mr-2">{option.icon}</span>
                                    <span className={`block truncate ${formState.interest === option.id ? 'font-medium' : 'font-normal'}`}>
                                      {option.label}
                                    </span>
                                    {formState.interest === option.id && (
                                      <span className="absolute inset-y-0 right-0 flex items-center pr-4">
                                        <Check className="h-5 w-5 text-primary-500" />
                                      </span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                        {formState.errors.interest && (
                          <p className="mt-1 text-sm text-red-500 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {formState.errors.interest}
                          </p>
                        )}
                        {formTouched.interest && !formState.errors.interest && formState.interest && (
                          <div className="absolute right-10 top-3 text-green-500">
                            <CheckCircle className="h-5 w-5" />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Mensagem */}
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center">
                        <MessageSquare className="h-4 w-4 mr-1" />
                        Mensagem (opcional)
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        value={formState.message}
                        onChange={handleChange}
                        rows="3"
                        className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white transition-colors"
                        placeholder="Descreva sua necessidade ou dúvida..."
                      ></textarea>
                    </div>

                    {/* Botões de ação */}
                    <div className="flex flex-col sm:flex-row gap-4 pt-4">
                      <button
                        type="submit"
                        disabled={formState.submitting}
                        className={`inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary-500 hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors flex-1 ${
                          formState.submitting ? 'opacity-80 cursor-not-allowed' : ''
                        }`}
                      >
                        {formState.submitting ? (
                          <>
                            <Loader2 className="animate-spin mr-2 h-5 w-5" />
                            Enviando...
                          </>
                        ) : (
                          <>
                            <Calendar className="mr-2 h-5 w-5" />
                            Agendar demonstração
                          </>
                        )}
                      </button>
                      <Link
                        href="/login"
                        className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-base font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
                      >
                        Começar agora
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Link>
                    </div>
                  </form>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
