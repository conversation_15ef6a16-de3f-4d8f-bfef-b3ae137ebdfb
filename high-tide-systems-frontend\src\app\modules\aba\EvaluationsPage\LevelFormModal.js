"use client";

import React, { useState, useEffect } from "react";
import { Layers, Save } from "lucide-react";
import { ModuleModal, ModuleInput, ModuleFormGroup } from "@/components/ui";

const LevelFormModal = ({ isOpen, onClose, onSave, level }) => {
  const [formData, setFormData] = useState({
    order: "",
    description: "",
    ageRange: ""
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Carregar dados do nível para edição
  useEffect(() => {
    if (level) {
      setFormData({
        order: level.order || "",
        description: level.description || "",
        ageRange: level.ageRange || ""
      });
    }
  }, [level]);

  // Manipuladores de eventos
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Limpar erro do campo quando ele for alterado
    if (errors[name]) {
      setErrors({ ...errors, [name]: null });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.order) {
      newErrors.order = "A ordem é obrigatória";
    } else if (isNaN(Number(formData.order))) {
      newErrors.order = "A ordem deve ser um número";
    }

    if (!formData.description || formData.description.trim() === "") {
      newErrors.description = "A descrição é obrigatória";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    try {
      setIsLoading(true);

      // Preparar dados para salvar
      const levelData = {
        order: Number(formData.order),
        description: formData.description,
        ageRange: formData.ageRange
      };

      console.log("LevelFormModal - Dados do nível a serem salvos:", levelData);
      onSave(levelData);
    } catch (error) {
      console.error("Erro ao salvar nível:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={level ? "Editar Nível" : "Novo Nível"}
      size="md"
      moduleColor="abaplus"
      icon={<Layers size={20} />}
    >
      <div className="p-6 space-y-6">
        <ModuleFormGroup label="Ordem *" error={errors.order}>
          <ModuleInput
            name="order"
            type="number"
            value={formData.order}
            onChange={handleChange}
            placeholder="Ordem do nível"
            moduleColor="abaplus"
            error={!!errors.order}
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Descrição *" error={errors.description}>
          <ModuleInput
            name="description"
            value={formData.description}
            onChange={handleChange}
            placeholder="Descrição do nível"
            moduleColor="abaplus"
            error={!!errors.description}
          />
        </ModuleFormGroup>

        <ModuleFormGroup label="Faixa Etária">
          <ModuleInput
            name="ageRange"
            value={formData.ageRange}
            onChange={handleChange}
            placeholder="Ex: 0-18 meses, 3-5 anos, Todas as idades"
            moduleColor="abaplus"
          />
        </ModuleFormGroup>

        <div className="flex justify-end gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            disabled={isLoading}
          >
            Cancelar
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <span className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
                <span>Salvando...</span>
              </>
            ) : (
              <>
                <Save size={18} />
                <span>Salvar</span>
              </>
            )}
          </button>
        </div>
      </div>
    </ModuleModal>
  );
};

export default LevelFormModal;
