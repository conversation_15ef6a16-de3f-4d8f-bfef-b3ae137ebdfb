'use client';

import React, { createContext, useContext, useReducer, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';

// Tipos de Toast
export const TOAST_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
};

// Estado inicial
const initialState = {
  toasts: [],
};

// Ações
const ACTIONS = {
  ADD_TOAST: 'ADD_TOAST',
  REMOVE_TOAST: 'REMOVE_TOAST',
  REMOVE_ALL_TOASTS: 'REMOVE_ALL_TOASTS',
};

// Reducer para manipular os toasts
const toastReducer = (state, action) => {
  switch (action.type) {
    case ACTIONS.ADD_TOAST:
      return {
        ...state,
        toasts: [...state.toasts, action.payload],
      };
    case ACTIONS.REMOVE_TOAST:
      return {
        ...state,
        toasts: state.toasts.filter((toast) => toast.id !== action.payload),
      };
    case ACTIONS.REMOVE_ALL_TOASTS:
      return {
        ...state,
        toasts: [],
      };
    default:
      return state;
  }
};

// Criar o contexto
const ToastContext = createContext(null);

export const ToastProvider = ({ children }) => {
  const [state, dispatch] = useReducer(toastReducer, initialState);

  // Função para adicionar um toast
  const addToast = useCallback(
    (messageOrOptions, type = TOAST_TYPES.INFO, duration = 5000) => {
      const id = uuidv4();
      
      // Definir o toast baseado no tipo de entrada
      let toast;
      
      if (typeof messageOrOptions === 'object' && messageOrOptions !== null) {
        // É um objeto de opções
        toast = {
          id,
          title: messageOrOptions.title || null,
          message: messageOrOptions.message || '',
          type: messageOrOptions.type || type,
          duration: typeof messageOrOptions.duration === 'number' ? messageOrOptions.duration : duration,
          createdAt: new Date(),
        };
      } else {
        // É uma string simples
        toast = {
          id,
          title: null,
          message: String(messageOrOptions), // Garantir que seja string
          type,
          duration,
          createdAt: new Date(),
        };
      }
      
      // Adicionar o toast
      dispatch({ type: ACTIONS.ADD_TOAST, payload: toast });
      
      // Configurar remoção automática se duration > 0
      if (toast.duration > 0) {
        setTimeout(() => {
          removeToast(id);
        }, toast.duration);
      }
      
      return id;
    },
    []
  );

  // Função para remover um toast específico
  const removeToast = useCallback((id) => {
    dispatch({ type: ACTIONS.REMOVE_TOAST, payload: id });
  }, []);

  // Função para remover todos os toasts
  const removeAllToasts = useCallback(() => {
    dispatch({ type: ACTIONS.REMOVE_ALL_TOASTS });
  }, []);

  // Funções de conveniência para cada tipo de toast
  const toast_success = useCallback(
    (messageOrOptions, duration) => {
      return addToast(messageOrOptions, TOAST_TYPES.SUCCESS, duration);
    },
    [addToast]
  );

  const toast_error = useCallback(
    (messageOrOptions, duration) => {
      return addToast(messageOrOptions, TOAST_TYPES.ERROR, duration);
    },
    [addToast]
  );

  const toast_warning = useCallback(
    (messageOrOptions, duration) => {
      return addToast(messageOrOptions, TOAST_TYPES.WARNING, duration);
    },
    [addToast]
  );

  const toast_info = useCallback(
    (messageOrOptions, duration) => {
      return addToast(messageOrOptions, TOAST_TYPES.INFO, duration);
    },
    [addToast]
  );

  // Valores que serão expostos pelo contexto
  const contextValue = {
    toasts: state.toasts,
    addToast,
    removeToast,
    removeAllToasts,
    toast_success,
    toast_error,
    toast_warning,
    toast_info,
  };

  return (
    <ToastContext.Provider value={contextValue}>
      {children}
    </ToastContext.Provider>
  );
};

export const useToast = () => {
  const context = useContext(ToastContext);
  
  if (!context) {
    throw new Error('useToast deve ser usado dentro de um ToastProvider');
  }
  
  return context;
};