// prisma/seed.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Iniciando seed...');

  // Criar empresa de teste
  const company = await prisma.company.upsert({
    where: { id: '00000000-0000-0000-0000-000000000001' },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001',
      name: 'Empresa de Teste',
      cnpj: '12345678901234',
      active: true
    }
  });

  console.log('Empresa criada:', company);

  // Criar usuário de teste
  const user = await prisma.user.upsert({
    where: { id: '00000000-0000-0000-0000-000000000001' },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001',
      email: '<EMAIL>',
      login: 'admin',
      password: '$2b$10$EpRnTzVlqHNP0.fUbXUwSOyuiXe/QLSUG6xNekdHgTGmrpHEfIoxm', // senha: 123456
      fullName: 'Administrador',
      role: 'SYSTEM_ADMIN',
      active: true,
      modules: ['ADMIN', 'RH', 'FINANCIAL', 'SCHEDULING', 'BASIC'],
      companyId: '00000000-0000-0000-0000-000000000001'
    }
  });

  console.log('Usuário criado:', user);

  // Criar 5 empresas de exemplo
  const companiesData = [
    {
      name: 'TechSolutions Brasil',
      tradingName: 'TechSolutions',
      legalName: 'TechSolutions Brasil Ltda',
      industry: 'Tecnologia',
      contactEmail: '<EMAIL>',
      cnpj: '12345678000190',
      phone: '(11) 3456-7890',
      phone2: '(11) 98765-4321',
      address: 'Av. Paulista, 1000',
      city: 'São Paulo',
      state: 'SP',
      postalCode: '01310-100',
      website: 'www.techsolutions.com.br',
      primaryColor: '#4285F4',
      secondaryColor: '#34A853',
      description: 'Empresa especializada em soluções tecnológicas para o mercado corporativo',
      socialMedia: {
        facebook: 'techsolutionsbr',
        instagram: '@techsolutions_br',
        linkedin: 'techsolutions-brasil'
      },
      businessHours: {
        monday: { start: '09:00', end: '18:00' },
        tuesday: { start: '09:00', end: '18:00' },
        wednesday: { start: '09:00', end: '18:00' },
        thursday: { start: '09:00', end: '18:00' },
        friday: { start: '09:00', end: '18:00' }
      }
    },
    {
      name: 'MediCare Saúde',
      tradingName: 'MediCare',
      legalName: 'MediCare Serviços de Saúde S.A.',
      industry: 'Saúde',
      contactEmail: '<EMAIL>',
      cnpj: '23456789000123',
      phone: '(21) 2345-6789',
      address: 'Rua Voluntários da Pátria, 450',
      city: 'Rio de Janeiro',
      state: 'RJ',
      postalCode: '22270-010',
      website: 'www.medicare.com.br',
      primaryColor: '#00A8E8',
      secondaryColor: '#007EA7',
      description: 'Rede de clínicas médicas com atendimento especializado',
      socialMedia: {
        facebook: 'medicaresaude',
        instagram: '@medicare_saude',
        twitter: '@MediCareBR'
      },
      businessHours: {
        monday: { start: '08:00', end: '20:00' },
        tuesday: { start: '08:00', end: '20:00' },
        wednesday: { start: '08:00', end: '20:00' },
        thursday: { start: '08:00', end: '20:00' },
        friday: { start: '08:00', end: '20:00' },
        saturday: { start: '08:00', end: '12:00' }
      }
    },
    {
      name: 'EcoVerde Ambiental',
      tradingName: 'EcoVerde',
      legalName: 'EcoVerde Soluções Ambientais Ltda',
      industry: 'Meio Ambiente',
      contactEmail: '<EMAIL>',
      cnpj: '34567890000145',
      phone: '(41) 3567-8901',
      address: 'Rua das Araucárias, 789',
      city: 'Curitiba',
      state: 'PR',
      postalCode: '80210-060',
      website: 'www.ecoverde.com.br',
      primaryColor: '#4CAF50',
      secondaryColor: '#8BC34A',
      description: 'Empresa especializada em consultoria e soluções ambientais sustentáveis',
      socialMedia: {
        instagram: '@ecoverde_ambiental',
        linkedin: 'ecoverde-ambiental'
      },
      businessHours: {
        monday: { start: '08:30', end: '17:30' },
        tuesday: { start: '08:30', end: '17:30' },
        wednesday: { start: '08:30', end: '17:30' },
        thursday: { start: '08:30', end: '17:30' },
        friday: { start: '08:30', end: '17:30' }
      }
    },
    {
      name: 'Constrular Engenharia',
      tradingName: 'Constrular',
      legalName: 'Constrular Engenharia e Construções S.A.',
      industry: 'Construção Civil',
      contactEmail: '<EMAIL>',
      cnpj: '45678901000167',
      phone: '(31) 3678-9012',
      address: 'Av. Afonso Pena, 3500',
      city: 'Belo Horizonte',
      state: 'MG',
      postalCode: '30130-009',
      website: 'www.constrular.com.br',
      primaryColor: '#FF5722',
      secondaryColor: '#E64A19',
      description: 'Construtora com foco em empreendimentos residenciais e comerciais de alto padrão',
      socialMedia: {
        facebook: 'constrularengenharia',
        instagram: '@constrular_oficial',
        linkedin: 'constrular-engenharia'
      },
      businessHours: {
        monday: { start: '08:00', end: '18:00' },
        tuesday: { start: '08:00', end: '18:00' },
        wednesday: { start: '08:00', end: '18:00' },
        thursday: { start: '08:00', end: '18:00' },
        friday: { start: '08:00', end: '18:00' }
      }
    },
    {
      name: 'EduFuturo Ensino',
      tradingName: 'EduFuturo',
      legalName: 'Instituto EduFuturo de Ensino Ltda',
      industry: 'Educação',
      contactEmail: '<EMAIL>',
      cnpj: '56789012000189',
      phone: '(51) 3789-0123',
      address: 'Rua dos Andradas, 1234',
      city: 'Porto Alegre',
      state: 'RS',
      postalCode: '90020-008',
      website: 'www.edufuturo.edu.br',
      primaryColor: '#673AB7',
      secondaryColor: '#9C27B0',
      description: 'Rede de escolas com metodologia inovadora e ensino de qualidade',
      socialMedia: {
        facebook: 'edufuturoensino',
        instagram: '@edufuturo',
        youtube: 'EduFuturoCanal'
      },
      businessHours: {
        monday: { start: '07:30', end: '19:00' },
        tuesday: { start: '07:30', end: '19:00' },
        wednesday: { start: '07:30', end: '19:00' },
        thursday: { start: '07:30', end: '19:00' },
        friday: { start: '07:30', end: '19:00' }
      }
    }
  ];

  // Inserir as empresas e armazenar os IDs
  const createdCompanies = [];

  for (const companyData of companiesData) {
    // Verificar se a empresa já existe pelo CNPJ
    const existingCompany = await prisma.company.findUnique({
      where: { cnpj: companyData.cnpj }
    });

    if (existingCompany) {
      console.log(`Empresa com CNPJ ${companyData.cnpj} já existe. Pulando...`);
      createdCompanies.push(existingCompany);
    } else {
      const newCompany = await prisma.company.create({
        data: companyData
      });
      console.log(`Empresa criada: ${newCompany.name} (ID: ${newCompany.id})`);
      createdCompanies.push(newCompany);
    }
  }

  // Para cada empresa, criar 2 unidades
  for (const company of createdCompanies) {
    // Definir unidades para cada empresa
    const branches = [
      {
        name: `${company.name} - Matriz`,
        code: 'MATRIZ',
        description: 'Unidade matriz da empresa',
        address: company.address || 'Endereço principal, 1000',
        neighborhood: 'Centro',
        city: company.city || 'São Paulo',
        state: company.state || 'SP',
        postalCode: company.postalCode || '01000-000',
        phone: company.phone || '(11) 1234-5678',
        email: `matriz@${company.website?.replace('www.', '') || 'empresa.com.br'}`,
        isHeadquarters: true,
        companyId: company.id
      },
      {
        name: `${company.name} - Filial`,
        code: 'FILIAL01',
        description: 'Primeira filial da empresa',
        address: 'Av. Secundária, 500',
        neighborhood: 'Jardim América',
        city: company.city || 'São Paulo',
        state: company.state || 'SP',
        postalCode: '01100-000',
        phone: '(11) 9876-5432',
        email: `filial@${company.website?.replace('www.', '') || 'empresa.com.br'}`,
        isHeadquarters: false,
        companyId: company.id
      }
    ];

    // Inserir as unidades
    for (const branchData of branches) {
      // Verificar se a unidade já existe pelo código e empresa
      const existingBranch = await prisma.branch.findFirst({
        where: {
          companyId: company.id,
          code: branchData.code
        }
      });

      if (existingBranch) {
        console.log(`Unidade ${branchData.name} já existe para a empresa ${company.name}. Pulando...`);
      } else {
        const branch = await prisma.branch.create({
          data: branchData
        });
        console.log(`Unidade criada: ${branch.name} (ID: ${branch.id}) para empresa ${company.name}`);
      }
    }
  }

  console.log('Seed concluído com sucesso!');
}

main()
  .catch((e) => {
    console.error('Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
