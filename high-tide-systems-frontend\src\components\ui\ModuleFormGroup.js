'use client';

import React from 'react';
import ModuleLabel from './ModuleLabel';

/**
 * Componente de grupo de formulário que combina label e input/select/textarea
 * 
 * @param {Object} props - Propriedades do componente
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {string} props.label - Texto do label
 * @param {string} props.htmlFor - ID do elemento associado
 * @param {React.ReactNode} props.icon - Ícone opcional para o label
 * @param {boolean} props.required - Se o campo é obrigatório
 * @param {React.ReactNode} props.children - Componente de input/select/textarea
 * @param {string} props.helpText - Texto de ajuda opcional
 * @param {string} props.className - Classes adicionais
 */
const ModuleFormGroup = ({
  moduleColor = 'default',
  label,
  htmlFor,
  icon,
  required = false,
  children,
  helpText,
  className = '',
}) => {
  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      helpTextColor: 'text-neutral-500 dark:text-neutral-400',
    },
    people: {
      helpTextColor: 'text-module-people-text dark:text-module-people-text-dark',
    },
    scheduler: {
      helpTextColor: 'text-module-scheduler-text dark:text-module-scheduler-text-dark',
    },
    admin: {
      helpTextColor: 'text-module-admin-text dark:text-module-admin-text-dark',
    },
    financial: {
      helpTextColor: 'text-module-financial-text dark:text-module-financial-text-dark',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;

  return (
    <div className={`space-y-1 ${className}`}>
      {label && (
        <ModuleLabel
          moduleColor={moduleColor}
          htmlFor={htmlFor}
          icon={icon}
          required={required}
        >
          {label}
        </ModuleLabel>
      )}
      
      {children}
      
      {helpText && (
        <p className={`text-xs ${colors.helpTextColor}`}>
          {helpText}
        </p>
      )}
    </div>
  );
};

export default ModuleFormGroup;
