import React, { useState, useRef, useEffect } from 'react';
import { Upload, X, User } from 'lucide-react';
import { useToast } from '@/contexts/ToastContext';
import { personsService } from '@/app/modules/people/services/personsService';
import api from '@/utils/api';

const ProfileImageUpload = ({
  personId,
  onImageUploaded,
  initialImageUrl = null,
  deferUpload = false,
  uploadRef = null,
  showPreviewOnly = false,
  previewFile = null
}) => {
  const [imageUrl, setImageUrl] = useState(initialImageUrl);
  const [isUploading, setIsUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const fileInputRef = useRef(null);
  const { toast_success, toast_error } = useToast();

  // Atualizar a URL da imagem quando o initialImageUrl mudar
  useEffect(() => {
    console.log('initialImageUrl mudou:', initialImageUrl);
    setImageUrl(initialImageUrl);
  }, [initialImageUrl]);

  // Atualizar a pré-visualização quando o arquivo de pré-visualização mudar
  useEffect(() => {
    if (previewFile) {
      console.log('Arquivo de pré-visualização recebido:', previewFile.name);
      // Criar URL de pré-visualização para o arquivo
      const url = URL.createObjectURL(previewFile);
      setPreviewUrl(url);
      setSelectedFile(previewFile);
    }
  }, [previewFile]);

  // Limpar a URL de pré-visualização quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
      }
    };
  }, [previewUrl]);

  // Expor o método de upload para o componente pai
  useEffect(() => {
    if (uploadRef) {
      uploadRef.current = {
        uploadSelectedImage: async () => {
          console.log('Método uploadSelectedImage chamado');
          console.log('Arquivo selecionado:', selectedFile ? selectedFile.name : 'Nenhum');
          console.log('ID da pessoa:', personId);

          if (selectedFile && personId) {
            console.log('Iniciando upload do arquivo selecionado');
            const result = await uploadImage(selectedFile);
            console.log('Resultado do upload:', result);
            return result;
          }
          console.log('Nenhum arquivo para upload ou ID da pessoa ausente');
          return null;
        },
        hasSelectedFile: () => {
          const hasFile = !!selectedFile;
          console.log('Verificando se há arquivo selecionado:', hasFile);
          return hasFile;
        }
      };
    }
  }, [selectedFile, personId, uploadRef]);

  const handleUploadClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    console.log('Arquivo selecionado');
    const file = e.target.files[0];
    if (!file) {
      console.log('Nenhum arquivo selecionado');
      return;
    }
    console.log('Arquivo:', file.name, file.type, file.size);

    // Validar tipo de arquivo
    if (!file.type.startsWith('image/')) {
      toast_error({
        title: 'Erro',
        message: 'Por favor, selecione uma imagem válida'
      });
      return;
    }

    // Validar tamanho do arquivo (2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast_error({
        title: 'Erro',
        message: 'A imagem deve ter no máximo 2MB'
      });
      return;
    }

    // Armazenar o arquivo selecionado
    setSelectedFile(file);

    // Criar URL de pré-visualização
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
    }
    const newPreviewUrl = URL.createObjectURL(file);
    setPreviewUrl(newPreviewUrl);

    // Se não estiver adiando o upload, fazer o upload imediatamente
    if (!deferUpload && personId) {
      uploadImage(file);
    } else {
      // Notificar o componente pai sobre a mudança de arquivo
      if (onImageUploaded) {
        onImageUploaded(null, file);
      }
    }
  };

  // Método para fazer o upload da imagem
  const uploadImage = async (file) => {
    if (!file || !personId) {
      console.error('Upload cancelado: arquivo ou personId ausente', { file: !!file, personId });
      return null;
    }

    setIsUploading(true);
    console.log('Iniciando upload de imagem para pessoa ID:', personId);
    console.log('Arquivo a ser enviado:', file.name, file.type, file.size);

    try {
      console.log('Chamando serviço de upload de imagem');
      const response = await personsService.uploadProfileImage(personId, file);
      console.log('Upload de imagem concluído com sucesso');
      console.log('Resposta completa:', JSON.stringify(response));

      // Atualizar URL da imagem com timestamp para evitar cache
      const timestamp = new Date().getTime();

      // Usar a URL completa retornada pelo servidor
      const newImageUrl = response.fullImageUrl ? `${response.fullImageUrl}?t=${timestamp}` : null;
      console.log('Nova URL da imagem:', newImageUrl);

      setImageUrl(newImageUrl);
      setSelectedFile(null); // Limpar o arquivo selecionado após o upload

      if (onImageUploaded) {
        onImageUploaded(newImageUrl);
      }

      toast_success({
        title: 'Sucesso',
        message: 'Imagem de perfil atualizada com sucesso'
      });

      return newImageUrl;
    } catch (error) {
      console.error('Erro ao fazer upload da imagem:', error);
      toast_error({
        title: 'Erro',
        message: 'Erro ao fazer upload da imagem. Tente novamente.'
      });
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = async () => {
    if (!imageUrl && !previewUrl && !selectedFile) return;

    if (!confirm('Tem certeza que deseja remover a imagem de perfil?')) {
      return;
    }

    // Se estiver apenas em pré-visualização (ainda não foi feito upload)
    if (previewUrl && !imageUrl) {
      // Limpar a pré-visualização
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
      setSelectedFile(null);

      if (onImageUploaded) {
        onImageUploaded(null, null);
      }

      return;
    }

    setIsUploading(true);

    try {
      // Enviar uma imagem vazia para remover a atual
      // Criar um arquivo vazio (1x1 pixel transparente)
      const emptyBlob = new Blob([''], { type: 'image/png' });
      await personsService.uploadProfileImage(personId, new File([emptyBlob], 'empty.png', { type: 'image/png' }));

      setImageUrl(null);
      setPreviewUrl(null);
      setSelectedFile(null);

      if (onImageUploaded) {
        onImageUploaded(null, null);
      }

      toast_success({
        title: 'Sucesso',
        message: 'Imagem de perfil removida com sucesso'
      });
    } catch (error) {
      console.error('Erro ao remover a imagem:', error);
      toast_error({
        title: 'Erro',
        message: 'Erro ao remover a imagem. Tente novamente.'
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="flex flex-col items-center w-full">
      <div className="relative mb-4">
        <div
          className={`w-32 h-32 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-700 flex items-center justify-center ${!showPreviewOnly ? 'cursor-pointer' : ''}`}
          onClick={!showPreviewOnly ? handleUploadClick : undefined}
        >
          {isUploading ? (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></div>
            </div>
          ) : null}

          {imageUrl ? (
            <>
              <img
                src={imageUrl}
                alt="Foto de perfil"
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error('Erro ao carregar imagem:', imageUrl);
                  e.target.onerror = null;
                  e.target.src = '';
                  e.target.classList.add('hidden');
                  setImageUrl(null);
                }}
              />
              {/* Exibir URL da imagem para depuração */}
              <div className="hidden">{imageUrl}</div>
            </>
          ) : previewUrl ? (
            <img
              src={previewUrl}
              alt="Pré-visualização"
              className="w-full h-full object-cover"
            />
          ) : (
            <User className="w-16 h-16 text-gray-400 dark:text-gray-500" />
          )}
        </div>

        {/* Botão de remoção absoluto - apenas se não estiver no modo de pré-visualização */}
        {(imageUrl || previewUrl) && !showPreviewOnly && (
          <button
            type="button"
            onClick={handleRemoveImage}
            className="absolute top-0 right-0 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 transition-colors"
            disabled={isUploading}
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />

      {/* Botões de ação - apenas se não estiver no modo de pré-visualização */}
      {!showPreviewOnly && (
        <button
          type="button"
          onClick={handleUploadClick}
          className="flex items-center gap-2 px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors disabled:opacity-50"
          disabled={isUploading}
        >
          <Upload className="w-4 h-4" />
          {imageUrl || previewUrl ? 'Alterar foto' : 'Adicionar foto'}
        </button>
      )}

      {/* Mensagens de status - apenas se não estiver no modo de pré-visualização */}
      {!showPreviewOnly && (
        <div className="h-12 flex items-center justify-center">
          {isUploading && (
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Enviando imagem...
            </p>
          )}

          {deferUpload && selectedFile && !isUploading && (
            <p className="text-sm text-green-500 dark:text-green-400 text-center">
              Imagem selecionada
          </p>
        )}
      </div>
      )}

      <p className="text-xs text-gray-500 dark:text-gray-400">
        Tamanho máximo: 2MB
      </p>
    </div>
  );
};

export default ProfileImageUpload;
