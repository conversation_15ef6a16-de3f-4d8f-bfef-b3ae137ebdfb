'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import '@/styles/scrollbar.css';

/**
 * Componente que aplica estilos de scrollbar personalizados a um elemento específico
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conte<PERSON>do a ser renderizado dentro da área de rolagem
 * @param {string} props.className - Classes adicionais para o elemento
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {boolean} props.isModal - Se o componente está sendo usado em um modal
 * @param {Object} props.style - Estilos adicionais para o elemento
 */
const CustomScrollArea = ({
  children,
  className = '',
  moduleColor,
  isModal = false,
  style = {},
  ...props
}) => {
  const pathname = usePathname();

  // Determinar o módulo atual com base no pathname se não for fornecido
  let currentModule = moduleColor;
  
  if (!currentModule) {
    if (pathname.includes('/modules/people') || pathname.includes('/pessoas')) {
      currentModule = 'people';
    } else if (pathname.includes('/modules/scheduler') || pathname.includes('/agendamento')) {
      currentModule = 'scheduler';
    } else if (pathname.includes('/modules/admin') || pathname.includes('/admin')) {
      currentModule = 'admin';
    } else if (pathname.includes('/modules/financial') || pathname.includes('/financeiro')) {
      currentModule = 'financial';
    }
  }

  // Construir as classes CSS
  const scrollClasses = [
    'custom-scrollbar',
    currentModule ? `module-${currentModule}` : '',
    isModal ? 'modal-scrollbar' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={scrollClasses} style={{ overflow: 'auto', ...style }} {...props}>
      {children}
    </div>
  );
};

export default CustomScrollArea;
