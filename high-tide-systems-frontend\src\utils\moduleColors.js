/**
 * Utility function to get color classes for different modules
 * @param {string} moduleId - The ID of the module (e.g., 'scheduler', 'abaplus', 'admin', etc.)
 * @returns {Object} Object containing color classes for the module
 */
export const getModuleColors = (moduleId = 'primary') => {
  const colorMap = {
    // Primary (default)
    primary: {
      textColor: 'text-blue-600 dark:text-blue-400',
      bgColor: 'bg-blue-600 dark:bg-blue-500',
      hoverBgColor: 'hover:bg-blue-700 dark:hover:bg-blue-600',
      borderColor: 'border-blue-600 dark:border-blue-500',
      bgLight: 'bg-blue-50 dark:bg-blue-900/30',
      bgHoverLight: 'hover:bg-blue-50 dark:hover:bg-blue-900/30',
      focusRing: 'focus:ring-blue-500 dark:focus:ring-blue-400',
      focusBorder: 'focus:border-blue-500 dark:focus:border-blue-400',
    },
    
    // Scheduler module
    scheduler: {
      textColor: 'text-indigo-600 dark:text-indigo-400',
      bgColor: 'bg-indigo-600 dark:bg-indigo-500',
      hoverBgColor: 'hover:bg-indigo-700 dark:hover:bg-indigo-600',
      borderColor: 'border-indigo-600 dark:border-indigo-500',
      bgLight: 'bg-indigo-50 dark:bg-indigo-900/30',
      bgHoverLight: 'hover:bg-indigo-50 dark:hover:bg-indigo-900/30',
      focusRing: 'focus:ring-indigo-500 dark:focus:ring-indigo-400',
      focusBorder: 'focus:border-indigo-500 dark:focus:border-indigo-400',
    },
    
    // Admin module
    admin: {
      textColor: 'text-gray-700 dark:text-gray-300',
      bgColor: 'bg-gray-700 dark:bg-gray-600',
      hoverBgColor: 'hover:bg-gray-800 dark:hover:bg-gray-700',
      borderColor: 'border-gray-700 dark:border-gray-600',
      bgLight: 'bg-gray-100 dark:bg-gray-800/50',
      bgHoverLight: 'hover:bg-gray-100 dark:hover:bg-gray-800/50',
      focusRing: 'focus:ring-gray-500 dark:focus:ring-gray-400',
      focusBorder: 'focus:border-gray-500 dark:focus:border-gray-400',
    },
    
    // Financial module
    financial: {
      textColor: 'text-emerald-600 dark:text-emerald-400',
      bgColor: 'bg-emerald-600 dark:bg-emerald-500',
      hoverBgColor: 'hover:bg-emerald-700 dark:hover:bg-emerald-600',
      borderColor: 'border-emerald-600 dark:border-emerald-500',
      bgLight: 'bg-emerald-50 dark:bg-emerald-900/30',
      bgHoverLight: 'hover:bg-emerald-50 dark:hover:bg-emerald-900/30',
      focusRing: 'focus:ring-emerald-500 dark:focus:ring-emerald-400',
      focusBorder: 'focus:border-emerald-500 dark:focus:border-emerald-400',
    },
    
    // HR module
    hr: {
      textColor: 'text-amber-600 dark:text-amber-400',
      bgColor: 'bg-amber-600 dark:bg-amber-500',
      hoverBgColor: 'hover:bg-amber-700 dark:hover:bg-amber-600',
      borderColor: 'border-amber-600 dark:border-amber-500',
      bgLight: 'bg-amber-50 dark:bg-amber-900/30',
      bgHoverLight: 'hover:bg-amber-50 dark:hover:bg-amber-900/30',
      focusRing: 'focus:ring-amber-500 dark:focus:ring-amber-400',
      focusBorder: 'focus:border-amber-500 dark:focus:border-amber-400',
    },
    
    // People module
    people: {
      textColor: 'text-violet-600 dark:text-violet-400',
      bgColor: 'bg-violet-600 dark:bg-violet-500',
      hoverBgColor: 'hover:bg-violet-700 dark:hover:bg-violet-600',
      borderColor: 'border-violet-600 dark:border-violet-500',
      bgLight: 'bg-violet-50 dark:bg-violet-900/30',
      bgHoverLight: 'hover:bg-violet-50 dark:hover:bg-violet-900/30',
      focusRing: 'focus:ring-violet-500 dark:focus:ring-violet-400',
      focusBorder: 'focus:border-violet-500 dark:focus:border-violet-400',
    },
    
    // ABA+ module
    abaplus: {
      textColor: 'text-teal-600 dark:text-teal-400',
      bgColor: 'bg-teal-600 dark:bg-teal-500',
      hoverBgColor: 'hover:bg-teal-700 dark:hover:bg-teal-600',
      borderColor: 'border-teal-600 dark:border-teal-500',
      bgLight: 'bg-teal-50 dark:bg-teal-900/30',
      bgHoverLight: 'hover:bg-teal-50 dark:hover:bg-teal-900/30',
      focusRing: 'focus:ring-teal-500 dark:focus:ring-teal-400',
      focusBorder: 'focus:border-teal-500 dark:focus:border-teal-400',
    },
    
    // Error state
    error: {
      textColor: 'text-red-600 dark:text-red-400',
      bgColor: 'bg-red-600 dark:bg-red-500',
      hoverBgColor: 'hover:bg-red-700 dark:hover:bg-red-600',
      borderColor: 'border-red-600 dark:border-red-500',
      bgLight: 'bg-red-50 dark:bg-red-900/30',
      bgHoverLight: 'hover:bg-red-50 dark:hover:bg-red-900/30',
      focusRing: 'focus:ring-red-500 dark:focus:ring-red-400',
      focusBorder: 'focus:border-red-500 dark:focus:border-red-400',
    },
    
    // Success state
    success: {
      textColor: 'text-green-600 dark:text-green-400',
      bgColor: 'bg-green-600 dark:bg-green-500',
      hoverBgColor: 'hover:bg-green-700 dark:hover:bg-green-600',
      borderColor: 'border-green-600 dark:border-green-500',
      bgLight: 'bg-green-50 dark:bg-green-900/30',
      bgHoverLight: 'hover:bg-green-50 dark:hover:bg-green-900/30',
      focusRing: 'focus:ring-green-500 dark:focus:ring-green-400',
      focusBorder: 'focus:border-green-500 dark:focus:border-green-400',
    },
    
    // Warning state
    warning: {
      textColor: 'text-yellow-600 dark:text-yellow-400',
      bgColor: 'bg-yellow-600 dark:bg-yellow-500',
      hoverBgColor: 'hover:bg-yellow-700 dark:hover:bg-yellow-600',
      borderColor: 'border-yellow-600 dark:border-yellow-500',
      bgLight: 'bg-yellow-50 dark:bg-yellow-900/30',
      bgHoverLight: 'hover:bg-yellow-50 dark:hover:bg-yellow-900/30',
      focusRing: 'focus:ring-yellow-500 dark:focus:ring-yellow-400',
      focusBorder: 'focus:border-yellow-500 dark:focus:border-yellow-400',
    },
  };

  // Return the color map for the specified module, or the primary colors if the module doesn't exist
  return colorMap[moduleId] || colorMap.primary;
};

export default getModuleColors;
