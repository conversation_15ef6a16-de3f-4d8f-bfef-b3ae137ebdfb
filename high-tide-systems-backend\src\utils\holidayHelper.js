/**
 * Utility for checking holidays
 */

const prisma = require('./prisma');

class HolidayHelper {
  /**
   * Verifica se uma data é um feriado
   * @param {Date|string} date - A data a ser verificada
   * @param {string} companyId - ID da empresa para verificar feriados específicos
   * @returns {Promise<boolean>} - Verdadeiro se for feriado
   */
  static async isHoliday(date, companyId = null) {
    const checkDate = new Date(date);

    // Normalizar a data para meia-noite para comparação apenas por dia
    const normalizedDate = new Date(
      checkDate.getFullYear(),
      checkDate.getMonth(),
      checkDate.getDate()
    );

    try {
      // Verificar se o modelo Holiday existe no schema
      if (!prisma.holiday) {
        console.log('Modelo Holiday não encontrado no schema do Prisma. Implementação pendente.');
        return false;
      }

      // Verificar se existe um feriado cadastrado para esta data
      const holiday = await prisma.holiday.findFirst({
        where: {
          date: {
            // Buscar por qualquer horário neste dia
            gte: normalizedDate,
            lt: new Date(normalizedDate.getTime() + 24 * 60 * 60 * 1000)
          },
          // Se companyId for fornecido, verificar feriados específicos da empresa
          // ou feriados nacionais (companyId = null)
          OR: [
            { companyId: companyId },
            { companyId: null } // Feriados nacionais
          ]
        }
      });

      return !!holiday;
    } catch (error) {
      console.error('Erro ao verificar feriado:', error);
      // Em caso de erro, assumir que não é feriado para não bloquear agendamentos
      return false;
    }
  }
}

module.exports = HolidayHelper;
