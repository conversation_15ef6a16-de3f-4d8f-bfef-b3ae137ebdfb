'use client';

import React, { useState, useEffect } from 'react';
import { Megaphone, Edit2, Save, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { ModuleTextarea } from '@/components/ui';
import systemUpdateNoteService from '@/services/systemUpdateNoteService';

export const UpdateNotesCard = () => {
  const [notes, setNotes] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [editedNotes, setEditedNotes] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { isSystemAdmin } = useAuth();

  // Fetch update notes from API
  useEffect(() => {
    const fetchUpdateNotes = async () => {
      try {
        setIsLoading(true);

        // Since the backend API isn't implemented yet, we'll use localStorage or default content
        // This code should be replaced with the API call once the backend is ready
        try {
          const savedNotes = localStorage.getItem('systemUpdateNotes');
          if (savedNotes) {
            setNotes(savedNotes);
          } else {
            setNotes('Bem-vindo ao High Tide Systems! Aqui você encontrará notas sobre atualizações do sistema.');
          }
        } catch (storageError) {
          // If localStorage fails, use default content
          setNotes('Bem-vindo ao High Tide Systems! Aqui você encontrará notas sobre atualizações do sistema.');
        }

        /*
        // Uncomment this code once the backend API is implemented
        try {
          const data = await systemUpdateNoteService.getLatest();
          setNotes(data.content || '');
        } catch (apiError) {
          // Silent fail - don't log to console since we expect this endpoint to be missing
          setNotes('Bem-vindo ao High Tide Systems! Aqui você encontrará notas sobre atualizações do sistema.');
        }
        */
      } catch (err) {
        // Don't log to console in production
        if (process.env.NODE_ENV === 'development') {
          console.error('Error in update notes component:', err);
        }
        setError('Não foi possível carregar as notas de atualização.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchUpdateNotes();
  }, []);

  const handleEditClick = () => {
    setEditedNotes(notes);
    setIsEditing(true);
  };

  const handleSaveClick = async () => {
    try {
      setIsLoading(true);

      // Since the backend API isn't implemented yet, we'll just update the local state
      // This code should be replaced with the API call once the backend is ready

      /*
      // Uncomment this code once the backend API is implemented
      try {
        await systemUpdateNoteService.create({ content: editedNotes });
      } catch (apiError) {
        // Silent fail - we expect this endpoint to be missing for now
      }
      */

      // Update the UI regardless of API success
      setNotes(editedNotes);
      setIsEditing(false);

      // Store in localStorage as a temporary solution until backend is ready
      try {
        localStorage.setItem('systemUpdateNotes', editedNotes);
      } catch (storageError) {
        // Ignore storage errors
      }
    } catch (err) {
      // Don't log to console in production
      if (process.env.NODE_ENV === 'development') {
        console.error('Error saving update notes:', err);
      }
      setError('Não foi possível salvar as notas de atualização.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelClick = () => {
    setIsEditing(false);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-xl dark:shadow-lg dark:shadow-black/30 overflow-hidden mb-6"
      style={{ boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 0 1px rgba(0, 0, 0, 0.05)' }}>
      <div className="bg-gradient-to-r from-amber-500 to-amber-600 dark:from-amber-700 dark:to-amber-800 px-6 py-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold text-white flex items-center">
            <Megaphone className="mr-3" size={22} aria-hidden="true" />
            Notas de Atualização
          </h3>
          {isSystemAdmin() && !isEditing && (
            <button
              onClick={handleEditClick}
              className="p-1.5 bg-white/20 hover:bg-white/30 rounded-full text-white transition-colors"
              aria-label="Editar notas de atualização"
            >
              <Edit2 size={16} />
            </button>
          )}
        </div>
      </div>

      <div className="p-6">
        {isLoading ? (
          <div className="animate-pulse h-24 bg-gray-200 dark:bg-gray-700 rounded-md"></div>
        ) : error ? (
          <div className="text-red-500 dark:text-red-400 text-sm min-h-[120px] flex items-center justify-center">{error}</div>
        ) : isEditing ? (
          <div className="space-y-4">
            <ModuleTextarea
              moduleColor="default"
              value={editedNotes}
              onChange={(e) => setEditedNotes(e.target.value)}
              placeholder="Digite as notas de atualização aqui..."
              rows={4}
              className="w-full"
            />
            <div className="flex justify-end space-x-2">
              <button
                onClick={handleCancelClick}
                className="px-3 py-1.5 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors flex items-center"
              >
                <X size={16} className="mr-1" />
                Cancelar
              </button>
              <button
                onClick={handleSaveClick}
                className="px-3 py-1.5 bg-amber-500 text-white rounded-md hover:bg-amber-600 transition-colors flex items-center"
              >
                <Save size={16} className="mr-1" />
                Salvar
              </button>
            </div>
          </div>
        ) : (
          <div className="prose prose-sm dark:prose-invert max-w-none min-h-[120px] max-h-[180px] overflow-y-auto">
            {notes ? (
              <div className="whitespace-pre-wrap">{notes}</div>
            ) : (
              <p className="text-gray-500 dark:text-gray-400 italic">
                Nenhuma nota de atualização disponível.
              </p>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default UpdateNotesCard;
