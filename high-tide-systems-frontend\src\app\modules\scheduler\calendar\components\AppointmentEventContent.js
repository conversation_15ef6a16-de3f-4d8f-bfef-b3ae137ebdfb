"use client";

import React from 'react';
import { APPOINTMENT_STATUS } from '../utils/appointmentConstants';

const AppointmentEventContent = ({ eventInfo, isDarkMode }) => {
  const event = eventInfo.event;
  const status = event.extendedProps.status || "PENDING";
  const statusConfig = APPOINTMENT_STATUS[status];

  // Determinar se o evento deve ser renderizado em modo compacto
  // com base no tipo de visualização (semana/dia) e na largura do evento
  const isTimeGridView = eventInfo.view.type === "timeGridWeek" || eventInfo.view.type === "timeGridDay";

  // Verificar se há eventos sobrepostos
  const hasOverlappingEvents = isTimeGridView && eventInfo.event.display !== 'block';

  // Verificar a duração do evento em minutos
  const durationMinutes = eventInfo.event.end && eventInfo.event.start ?
    Math.round((eventInfo.event.end - eventInfo.event.start) / (60 * 1000)) : 60;

  // Classificar eventos por duração para melhor adaptação do layout
  const isVeryShortEvent = durationMinutes < 15; // Menos de 15 minutos
  const isShortEvent = durationMinutes < 30;     // Menos de 30 minutos
  const isMediumEvent = durationMinutes < 60;    // Menos de 1 hora
  const isLongEvent = durationMinutes >= 60;     // 1 hora ou mais

  // Definimos prioridades fixas para exibição
  const priorities = {
    PATIENT: 1,
    PROFESSIONAL: 2,
    TIME: 3,
    STATUS: 4,
    SERVICE: 5,
    TITLE: 6
  };

  // Verificar se há eventos sobrepostos e quantos são
  const getOverlappingEventsCount = () => {
    if (!isTimeGridView) return 0;

    // Verificar quantos eventos estão no mesmo horário
    const date = event.start;
    const dateStr = date.toISOString().split('T')[0];
    const hour = date.getHours();
    const minute = date.getMinutes();

    // Encontrar eventos no mesmo horário
    // Usamos uma abordagem mais direta para garantir que contamos corretamente
    const allEvents = eventInfo.view.calendar.getEvents();
    const sameTimeEvents = allEvents.filter(evt => {
      if (evt.id === event.id) return false;

      const evtDate = evt.start;
      if (!evtDate) return false;

      return (
        evtDate.toISOString().split('T')[0] === dateStr &&
        evtDate.getHours() === hour &&
        evtDate.getMinutes() === minute
      );
    });

    // Sem log de debug para produção

    return sameTimeEvents.length + 1; // +1 para incluir o evento atual
  };

  const overlappingCount = getOverlappingEventsCount();

  // Função para determinar se deve mostrar um elemento com base na prioridade, duração e sobreposição
  const shouldShowElement = (priority) => {
    // SEMPRE mostrar o nome do paciente, independentemente de qualquer condição
    if (priority === priorities.PATIENT) return true;

    // Sempre mostrar o nome do profissional
    if (priority === priorities.PROFESSIONAL) return true;

    // Regras para horário (prioridade 3)
    if (priority === priorities.TIME) {
      // Se a consulta tem até 59m de duração, não exibe horário
      if (durationMinutes <= 59) return false;

      // Se tem mais de 59m de duração e até 2 consultas no mesmo horário, exibe horário
      if (durationMinutes > 59 && overlappingCount <= 2) return true;

      // Se tem mais de 59m de duração e mais de 2 consultas, não exibe horário
      return false;
    }

    // Regras para status (prioridade 4)
    if (priority === priorities.STATUS) {
      // Se a consulta tem até 61m de duração, não exibe status
      if (durationMinutes <= 61) return false;

      // Se tem mais de 61m de duração, exibe status (independente da sobreposição)
      return true;
    }

    // Para o restante, não exibimos
    return false;
  };

  // Se for visualização de mês, renderiza versão compacta
  if (eventInfo.view.type === "dayGridMonth") {
    return (
      <div
        className="flex flex-col w-full h-full"
        style={{
          backgroundColor: isDarkMode ? statusConfig.darkColor : statusConfig.color,
          border: "none",
          color: "#ffffff",
        }}
      >
        <div className="flex flex-col px-1 pt-1">
          {/* Prioridade 1: Paciente */}
          <span className="text-xs font-medium">{event.extendedProps.personfullName}</span>
          <div className="h-2"></div> {/* Espaço entre o paciente e o profissional */}
          {/* Prioridade 2: Profissional */}
          <span className="text-[10px] font-medium opacity-90">{event.extendedProps.providerfullName}</span>
        </div>
        {/* Prioridade 3: Horário */}
        <div className="flex items-baseline gap-1 px-1 mt-0.5">
          <span className="text-xs font-medium">{eventInfo.timeText.replace(/^(\d{2})/, '$1h')}</span>
        </div>
        {/* Prioridade 4: Status */}
        <div className="flex gap-1 px-1 pb-1 mt-0.5">
          <span className="text-xs px-1 rounded bg-white bg-opacity-20">
            {statusConfig.label}
          </span>
        </div>
      </div>
    );
  }

  // Visualização semanal ou diária - adaptar com base no espaço disponível
  if (isTimeGridView) {
    // Determinar o nível de detalhe com base na sobreposição e duração
    if (hasOverlappingEvents || isShortEvent) {
      // Versão ultra compacta para eventos muito curtos (menos de 15 minutos)
      if (isVeryShortEvent) {
        return (
          <div className="h-full flex flex-col justify-center p-1 min-h-[20px]">
            {/* Prioridade 1: Nome do paciente (sempre mostrar) */}
            <div className="text-xs font-medium leading-tight">{event.extendedProps.personfullName}</div>
            {/* Prioridade 2: Nome do profissional */}
            {shouldShowElement(priorities.PROFESSIONAL) && (
              <div className="text-[10px] font-medium opacity-80 leading-tight -mt-0.5">{event.extendedProps.providerfullName}</div>
            )}
          </div>
        );
      }

      // Versão super compacta para eventos curtos (entre 15 e 30 minutos)
      if (isShortEvent) {
        return (
          <div className="h-full flex flex-col p-1 min-h-[40px]">
            {/* Prioridade 1: Nome do paciente (sempre mostrar) */}
            <div className="text-xs font-medium leading-tight">{event.extendedProps.personfullName}</div>

            {/* Prioridade 2: Nome do profissional */}
            {shouldShowElement(priorities.PROFESSIONAL) && (
              <div className="text-[10px] font-medium opacity-90 leading-tight -mt-0.5">{event.extendedProps.providerfullName}</div>
            )}

            {/* Prioridade 3: Horário (mostrar se tiver até 2 eventos sobrepostos) */}
            {shouldShowElement(priorities.TIME) && (
              <div className="text-[9px] font-medium opacity-80 leading-tight -mt-1.5">{eventInfo.timeText}</div>
            )}

            {/* Prioridade 4: Status (mostrar para consultas com mais de 61m) */}
            {shouldShowElement(priorities.STATUS) && (
              <div className="mt-1">
                <span className="text-[9px] px-1 rounded bg-white bg-opacity-20 inline-block">
                  {statusConfig.label}
                </span>
              </div>
            )}
          </div>
        );
      }

      // Versão compacta para eventos sobrepostos ou médios (entre 30 min e 1 hora)
      return (
        <div className="h-full flex flex-col p-1 min-h-[60px]">
          {/* Prioridade 1: Nome do paciente (sempre mostrar) */}
          <div className="text-xs font-medium">{event.extendedProps.personfullName}</div>

          {/* Prioridade 2: Nome do profissional */}
          {shouldShowElement(priorities.PROFESSIONAL) && (
            <div className="text-[10px] font-medium opacity-90">{event.extendedProps.providerfullName}</div>
          )}

          {/* Prioridade 3: Horário (mostrar se tiver até 2 eventos sobrepostos) */}
          {shouldShowElement(priorities.TIME) && (
            <div className="text-[9px] font-medium opacity-80 -mt-1">
              {eventInfo.timeText}
            </div>
          )}

          {/* Prioridade 4: Status (mostrar para consultas com mais de 61m) */}
          {shouldShowElement(priorities.STATUS) && (
            <div className="mt-2">
              <span className="text-[9px] px-1 rounded bg-white bg-opacity-20 inline-block">
                {statusConfig.label}
              </span>
            </div>
          )}
        </div>
      );
    }

    // Versão normal para eventos sem sobreposição
    // Formatar a duração para exibição (ex: "60 min" ou "2h")
    const formattedDuration = durationMinutes >= 60
      ? `${Math.floor(durationMinutes / 60)}h${durationMinutes % 60 > 0 ? ` ${durationMinutes % 60}min` : ''}`
      : `${durationMinutes}min`;

    // Layout para eventos longos (1 hora ou mais)
    if (isLongEvent) {
      return (
        <div className="h-full flex flex-col p-1.5 min-h-[120px]">
          {/* Prioridade 1: Nome do paciente */}
          <div className="text-sm font-medium leading-tight">{event.extendedProps.personfullName}</div>

          {/* Prioridade 2: Nome do profissional */}
          {shouldShowElement(priorities.PROFESSIONAL) && (
            <div className="text-xs font-medium opacity-90 leading-tight mt-1">{event.extendedProps.providerfullName}</div>
          )}

          {/* Prioridade 3: Horário e duração (mostrar se tiver até 2 eventos sobrepostos) */}
          {shouldShowElement(priorities.TIME) && (
            <div className="text-[9px] font-medium opacity-90 flex items-center justify-between leading-tight mt-1">
              <span>{eventInfo.timeText}</span>
              <span className="text-[8px] opacity-75">{formattedDuration}</span>
            </div>
          )}

          {/* Prioridade 4: Status (mostrar para consultas com mais de 61m) */}
          {shouldShowElement(priorities.STATUS) && (
            <div className="mt-2">
              <span className="text-[9px] px-1 py-0.5 rounded bg-opacity-20 bg-white font-medium">
                {statusConfig.label}
              </span>
            </div>
          )}

          {/* Prioridade 5: Serviço (se disponível) */}
          {event.extendedProps.serviceTypefullName && (
            <div className="text-[9px] font-medium opacity-80 mt-1">
              {event.extendedProps.serviceTypefullName}
            </div>
          )}
        </div>
      );
    }

    // Layout para eventos médios (entre 30 min e 1 hora) ou longos com sobreposição
    return (
      <div className="h-full flex flex-col p-1.5 min-h-[80px]">
        {/* Prioridade 1: Nome do paciente */}
        <div className="text-xs font-medium leading-tight">{event.extendedProps.personfullName}</div>

        {/* Prioridade 2: Nome do profissional */}
        {shouldShowElement(priorities.PROFESSIONAL) && (
          <div className="text-[10px] font-medium opacity-90 leading-tight mt-1">{event.extendedProps.providerfullName}</div>
        )}

        {/* Prioridade 3: Horário (mostrar se tiver duração > 59m e até 2 eventos sobrepostos) */}
        {shouldShowElement(priorities.TIME) && (
          <div className="text-[9px] font-medium opacity-80 leading-tight mt-1">{eventInfo.timeText}</div>
        )}

        {/* Prioridade 4: Status (mostrar para consultas com mais de 61m) */}
        {shouldShowElement(priorities.STATUS) && (
          <div className="mt-1">
            <span className="text-[9px] px-1 rounded bg-white bg-opacity-20 inline-block">
              {statusConfig.label}
            </span>
          </div>
        )}

        {/* Prioridade 5: Serviço (se disponível) */}
        {event.extendedProps.serviceTypefullName && (
          <div className="text-[9px] font-medium opacity-80 mt-1">
            {event.extendedProps.serviceTypefullName}
          </div>
        )}
      </div>
    );
  }

  // Fallback para outros tipos de visualização
  return (
    <div className="h-full flex flex-col p-1.5 min-h-[60px]">
      {/* Prioridade 1: Nome do paciente */}
      <div className="text-xs font-medium leading-tight">{event.extendedProps.personfullName}</div>

      {/* Prioridade 2: Nome do profissional */}
      {shouldShowElement(priorities.PROFESSIONAL) && (
        <div className="text-[10px] font-medium opacity-90 leading-tight mt-1">{event.extendedProps.providerfullName}</div>
      )}

      {/* Prioridade 3: Horário (mostrar se tiver até 2 eventos sobrepostos) */}
      {shouldShowElement(priorities.TIME) && (
        <div className="text-[9px] font-medium opacity-80 leading-tight mt-1">{eventInfo.timeText}</div>
      )}

      {/* Prioridade 4: Status (mostrar para consultas com mais de 61m) */}
      {shouldShowElement(priorities.STATUS) && (
        <div className="mt-1">
          <span className="text-[9px] px-1 rounded bg-white bg-opacity-20 inline-block">
            {statusConfig.label}
          </span>
        </div>
      )}

      {/* Prioridade 5: Serviço (se disponível) */}
      {event.extendedProps.serviceTypefullName && (
        <div className="text-[9px] font-medium opacity-80 mt-1">
          {event.extendedProps.serviceTypefullName}
        </div>
      )}
    </div>
  );
};

export default AppointmentEventContent;