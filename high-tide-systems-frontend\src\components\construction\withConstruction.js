"use client";

import React from 'react';
import { useConstructionMessage } from '@/hooks/useConstructionMessage';

/**
 * HOC (Higher-Order Component) que adiciona a funcionalidade "Em Construção" a um componente
 * 
 * @param {React.ComponentType} Component - Componente a ser envolvido
 * @param {Object} options - Opções para a mensagem "Em Construção"
 * @returns {React.ComponentType} Componente envolvido com a funcionalidade "Em Construção"
 */
export const withConstruction = (Component, options = {}) => {
  // Definir opções padrão
  const defaultOptions = {
    title: 'Em Construção',
    content: 'Esta funcionalidade está em desenvolvimento e estará disponível em breve.',
    position: 'auto',
    icon: 'Construction'
  };

  // Mesclar opções padrão com as opções fornecidas
  const mergedOptions = { ...defaultOptions, ...options };

  // Retornar o componente envolvido
  return function ConstructionWrapper(props) {
    const { constructionProps } = useConstructionMessage();

    // Adicionar a funcionalidade "Em Construção" ao componente
    return <Component {...props} {...constructionProps(mergedOptions)} />;
  };
};

export default withConstruction;
