const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { InsuranceServiceLimitController, createLimitValidation } = require('../../controllers/insuranceServiceLimitController');

// Todas as rotas são protegidas
router.use(authenticate);

// Rotas para gerenciar limites de serviço
router.get('/', InsuranceServiceLimitController.list); // Nova rota para listar todos os limites
router.post('/', createLimitValidation, InsuranceServiceLimitController.create);
router.put('/:id', InsuranceServiceLimitController.update);
router.get('/person/:personId', InsuranceServiceLimitController.listByPerson);
router.get('/person/:personId/insurance/:insuranceId', InsuranceServiceLimitController.listByPersonInsurance);
router.get('/insurance/:insuranceId', InsuranceServiceLimitController.listByInsurance); // Nova rota para listar limites por convênio
router.delete('/:id', InsuranceServiceLimitController.delete);
router.post('/check', InsuranceServiceLimitController.checkLimit);

module.exports = router;