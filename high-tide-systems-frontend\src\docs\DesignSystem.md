# Sistema de Design High Tide Systems

Este documento descreve o sistema de design usado no High Tide Systems, incluindo espaçamento, tipografia, cores e componentes.

## Espaçamento

O sistema de espaçamento é baseado em múltiplos de 4px, seguindo as classes do Tailwind CSS:

| Classe | Valor | Uso recomendado |
|--------|-------|-----------------|
| `p-1`, `m-1` | 0.25rem (4px) | Espaçamento mínimo entre elementos relacionados |
| `p-2`, `m-2` | 0.5rem (8px) | Espaçamento padrão entre elementos relacionados |
| `p-3`, `m-3` | 0.75rem (12px) | Espaçamento médio |
| `p-4`, `m-4` | 1rem (16px) | Espaçamento padrão entre seções |
| `p-5`, `m-5` | 1.25rem (20px) | Espaçamento grande |
| `p-6`, `m-6` | 1.5rem (24px) | Espaçamento entre seções principais |
| `p-8`, `m-8` | 2rem (32px) | Espaçamento entre blocos de conteúdo |
| `p-10`, `m-10` | 2.5rem (40px) | Espaçamento grande entre blocos de conteúdo |
| `p-12`, `m-12` | 3rem (48px) | Espaçamento muito grande |

### Uso Consistente

- Use `p-6` para o padding interno de cards e containers principais
- Use `gap-4` para espaçamento entre itens em grids e flexbox
- Use `my-4` para separar seções de conteúdo
- Use `mb-2` para separar títulos de seu conteúdo

## Tipografia

### Hierarquia de Texto

| Elemento | Classe | Uso |
|----------|--------|-----|
| Título principal | `text-3xl md:text-4xl font-bold` | Títulos de página |
| Título secundário | `text-2xl md:text-3xl font-semibold` | Seções principais |
| Título terciário | `text-xl md:text-2xl font-semibold` | Subseções |
| Título quaternário | `text-lg md:text-xl font-medium` | Grupos de conteúdo |
| Subtítulo | `text-base md:text-lg font-medium` | Cabeçalhos de cards |
| Texto de corpo | `text-base` | Texto principal |
| Texto pequeno | `text-sm` | Informações secundárias |
| Legenda | `text-xs` | Metadados, notas de rodapé |

### Cores de Texto

- Texto principal: `text-neutral-900 dark:text-white`
- Texto secundário: `text-neutral-700 dark:text-neutral-300`
- Texto terciário: `text-neutral-500 dark:text-neutral-400`
- Texto desabilitado: `text-neutral-400 dark:text-neutral-500`

## Sistema de Cores

### Cores Neutras

Usamos a escala de cinza do Tailwind para cores neutras:

- `neutral-50` a `neutral-950`: Escala de cinza para fundos, bordas e texto

### Cores de Módulos

Cada módulo tem seu próprio esquema de cores:

#### Pessoas (People)
- **Claro**: Tons de laranja/âmbar
  - Fundo: `bg-module-people-bg` (`#fff7ed`)
  - Borda: `border-module-people-border` (`#fdba74`)
  - Ícone: `text-module-people-icon` (`#f97316`)
  - Texto: `text-module-people-text` (`#c2410c`)
  - Hover: `hover:bg-module-people-hover` (`#ffedd5`)

- **Escuro**: 
  - Fundo: `bg-module-people-bg-dark` (`#7c2d12`)
  - Borda: `border-module-people-border-dark` (`#c2410c`)
  - Ícone: `text-module-people-icon-dark` (`#fb923c`)
  - Texto: `text-module-people-text-dark` (`#fdba74`)
  - Hover: `hover:bg-module-people-hover-dark` (`#9a3412`)

#### Agendamento (Scheduler)
- **Claro**: Tons de roxo/violeta
  - Fundo: `bg-module-scheduler-bg` (`#f5f3ff`)
  - Borda: `border-module-scheduler-border` (`#c4b5fd`)
  - Ícone: `text-module-scheduler-icon` (`#8b5cf6`)
  - Texto: `text-module-scheduler-text` (`#6d28d9`)
  - Hover: `hover:bg-module-scheduler-hover` (`#ede9fe`)

- **Escuro**:
  - Fundo: `bg-module-scheduler-bg-dark` (`#5b21b6`)
  - Borda: `border-module-scheduler-border-dark` (`#7c3aed`)
  - Ícone: `text-module-scheduler-icon-dark` (`#a78bfa`)
  - Texto: `text-module-scheduler-text-dark` (`#c4b5fd`)
  - Hover: `hover:bg-module-scheduler-hover-dark` (`#4c1d95`)

#### Administração (Admin)
- **Claro**: Tons de vermelho/rosa
  - Fundo: `bg-module-admin-bg` (`#fdf2f8`)
  - Borda: `border-module-admin-border` (`#f9a8d4`)
  - Ícone: `text-module-admin-icon` (`#ec4899`)
  - Texto: `text-module-admin-text` (`#be185d`)
  - Hover: `hover:bg-module-admin-hover` (`#fce7f3`)

- **Escuro**:
  - Fundo: `bg-module-admin-bg-dark` (`#831843`)
  - Borda: `border-module-admin-border-dark` (`#be185d`)
  - Ícone: `text-module-admin-icon-dark` (`#f472b6`)
  - Texto: `text-module-admin-text-dark` (`#f9a8d4`)
  - Hover: `hover:bg-module-admin-hover-dark` (`#9d174d`)

#### Financeiro (Financial)
- **Claro**: Tons de verde/esmeralda
  - Fundo: `bg-module-financial-bg` (`#ecfdf5`)
  - Borda: `border-module-financial-border` (`#6ee7b7`)
  - Ícone: `text-module-financial-icon` (`#10b981`)
  - Texto: `text-module-financial-text` (`#047857`)
  - Hover: `hover:bg-module-financial-hover` (`#d1fae5`)

- **Escuro**:
  - Fundo: `bg-module-financial-bg-dark` (`#065f46`)
  - Borda: `border-module-financial-border-dark` (`#059669`)
  - Ícone: `text-module-financial-icon-dark` (`#34d399`)
  - Texto: `text-module-financial-text-dark` (`#6ee7b7`)
  - Hover: `hover:bg-module-financial-hover-dark` (`#047857`)

### Cores de Estado

- **Sucesso**: `text-success-500`, `bg-success-50`
- **Erro**: `text-error-500`, `bg-error-50`
- **Aviso**: `text-warning-500`, `bg-warning-50`
- **Informação**: `text-info-500`, `bg-info-50`

## Componentes

### Botões

Usamos o componente `ModalButton` para botões em modais e `Button` para botões gerais.

#### Variantes de Botão

- **Primary**: Botão principal com gradiente de cores do módulo
- **Secondary**: Botão secundário com borda e texto na cor do módulo
- **Outline**: Botão com borda e texto na cor do módulo, sem fundo
- **Ghost**: Botão sem borda nem fundo, apenas texto na cor do módulo
- **Link**: Botão que parece um link
- **Danger**: Botão vermelho para ações destrutivas
- **Success**: Botão verde para ações de sucesso
- **Warning**: Botão âmbar para ações de alerta

#### Tamanhos de Botão

- **sm**: Pequeno (`px-3 py-1.5 text-sm`)
- **md**: Médio (`px-4 py-2 text-base`)
- **lg**: Grande (`px-6 py-3 text-lg`)

### Cards

Os cards devem seguir um padrão consistente:

- Borda arredondada: `rounded-lg`
- Sombra leve: `shadow-sm dark:shadow-gray-900/30`
- Fundo claro/escuro: `bg-white dark:bg-gray-800`
- Padding interno: `p-6`
- Espaçamento entre elementos: `space-y-4`

### Modais

Usamos o componente `ModuleModal` para modais, que suporta diferentes temas de cores para cada módulo.

#### Tamanhos de Modal

- **sm**: Pequeno (`max-w-md`)
- **md**: Médio (`max-w-xl`)
- **lg**: Grande (`max-w-3xl`)
- **xl**: Extra grande (`max-w-5xl`)
- **full**: Largura total (`max-w-full mx-4`)

### Cabeçalhos de Módulo

Usamos o componente `ModuleHeader` para cabeçalhos de módulos, que suporta diferentes temas de cores para cada módulo.

## Padrões de Layout

### Container Principal

```jsx
<div className="max-w-7xl mx-auto">
  {/* Conteúdo */}
</div>
```

### Grid Responsivo

```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Itens do grid */}
</div>
```

### Seção com Título

```jsx
<div className="space-y-4">
  <h2 className="text-2xl font-semibold text-neutral-800 dark:text-white">
    Título da Seção
  </h2>
  <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
    {/* Conteúdo */}
  </div>
</div>
```

## Animações e Transições

- Transições suaves: `transition-all duration-200`
- Hover em cards: `hover:shadow-md dark:hover:shadow-gray-900/50`
- Hover em botões: `hover:bg-primary-600`
- Animação de loading: `animate-spin`

## Acessibilidade

- Use sempre `aria-label` para elementos sem texto visível
- Mantenha contraste adequado entre texto e fundo
- Use `focus:ring-2 focus:ring-offset-2 focus:outline-none` para estados de foco
- Adicione `sr-only` para textos acessíveis apenas para leitores de tela

## Responsividade

- Mobile-first: comece com layout mobile e adicione breakpoints para telas maiores
- Breakpoints padrão:
  - `sm`: 640px
  - `md`: 768px
  - `lg`: 1024px
  - `xl`: 1280px
  - `2xl`: 1536px

## Boas Práticas

1. **Consistência**: Use os mesmos padrões de espaçamento, tipografia e cores em todo o sistema
2. **Hierarquia**: Mantenha uma hierarquia visual clara com tamanhos de texto e espaçamento consistentes
3. **Feedback**: Forneça feedback visual para interações do usuário (hover, focus, active)
4. **Simplicidade**: Mantenha o design simples e focado no conteúdo
5. **Acessibilidade**: Garanta que o design seja acessível para todos os usuários
