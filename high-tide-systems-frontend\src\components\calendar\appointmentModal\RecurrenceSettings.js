import React, { useState, useEffect } from 'react';
import { format, addDays, isAfter } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { RotateCw, AlertCircle } from 'lucide-react';
import { ModuleSelect } from '@/components/ui';

const RecurrenceSettings = ({ formData, setFormData }) => {
  const [validationErrors, setValidationErrors] = useState({
    endDate: '',
    patterns: ''
  });

  const inputBaseClasses =
    "w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-base transition-colors duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-gray-200";

  const formatTime = (date) => {
    // Converter para objeto Date se for string ISO
    const dateObj = typeof date === 'string' ? new Date(date) : date;

    // Formatar diretamente no horário local
    return format(dateObj, "HH:mm", { locale: ptBR });
  };

  // Validar a recorrência sempre que os dados do formulário mudarem
  useEffect(() => {
    if (!formData.recurrence.enabled) {
      setValidationErrors({ endDate: '', patterns: '' });
      return;
    }

    const errors = {};

    // Validar data final
    if (formData.recurrence.type === 'END_DATE') {
      if (!formData.recurrence.endDate) {
        errors.endDate = 'A data final é obrigatória';
      } else {
        const endDate = new Date(formData.recurrence.endDate);
        const tomorrow = addDays(new Date(), 1);

        if (!isAfter(endDate, tomorrow)) {
          errors.endDate = 'A data final deve ser pelo menos 1 dia após a data atual';
        }
      }
    }

    // Validar padrões
    if (!formData.recurrence.patterns || formData.recurrence.patterns.length === 0) {
      errors.patterns = 'Selecione pelo menos um dia da semana';
    }

    setValidationErrors(errors);
  }, [formData.recurrence]);

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <input
          type="checkbox"
          id="recurrence-enabled"
          checked={formData.recurrence.enabled}
          onChange={(e) =>
            setFormData({
              ...formData,
              recurrence: {
                ...formData.recurrence,
                enabled: e.target.checked,
              },
            })
          }
          className="rounded border-neutral-300 dark:border-neutral-600 text-primary-600 focus:ring-primary-500 w-4 h-4"
        />
        <label
          htmlFor="recurrence-enabled"
          className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2"
        >
          <RotateCw className="w-4 h-4" />
          Agendar recorrência
        </label>
      </div>

      {formData.recurrence.enabled && (
        <div className="pl-4 space-y-3 border-l-2 border-primary-100 dark:border-primary-800">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ModuleSelect
            moduleColor="scheduler"
            value={formData.recurrence.type}
            onChange={(e) =>
              setFormData({
                ...formData,
                recurrence: {
                  ...formData.recurrence,
                  type: e.target.value,
                },
              })
            }
            placeholder="Selecione o tipo de recorrência"
            className="text-base py-2"
          >
            <option value="OCCURRENCES">
              Por número de ocorrências
            </option>
            <option value="END_DATE">Por data final</option>
          </ModuleSelect>

          {formData.recurrence.type === "OCCURRENCES" ? (
            <div>
              <label className="text-sm text-neutral-600 dark:text-neutral-400">
                Número de ocorrências
              </label>
              <input
                type="number"
                min="1"
                value={formData.recurrence.numberOfOccurrences}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    recurrence: {
                      ...formData.recurrence,
                      numberOfOccurrences: parseInt(e.target.value),
                    },
                  })
                }
                className={inputBaseClasses}
              />
            </div>
          ) : (
            <div>
              <label className="text-sm text-neutral-600 dark:text-neutral-400">
                Data final
              </label>
              <input
                type="date"
                value={formData.recurrence.endDate ? format(new Date(formData.recurrence.endDate), "yyyy-MM-dd") : ""}
                min={format(new Date(formData.startDate), "yyyy-MM-dd")}
                onChange={(e) => {
                  // Criar uma data a partir do valor do input (formato YYYY-MM-DD)
                  const inputDate = e.target.value;

                  // Criar uma string ISO completa (YYYY-MM-DDT00:00:00.000Z)
                  const isoDate = `${inputDate}T00:00:00.000Z`;

                  console.log(`[RECURRENCE] Data final selecionada: ${inputDate}`);
                  console.log(`[RECURRENCE] Data final ISO: ${isoDate}`);

                  setFormData({
                    ...formData,
                    recurrence: {
                      ...formData.recurrence,
                      endDate: isoDate,
                    },
                  });
                }}
                className={`${inputBaseClasses} ${validationErrors.endDate ? 'border-red-500 dark:border-red-500' : ''}`}
              />
              {validationErrors.endDate && (
                <div className="mt-1 text-red-500 text-xs flex items-center gap-1">
                  <AlertCircle className="w-3 h-3" />
                  {validationErrors.endDate}
                </div>
              )}
            </div>
          )}

          <div className="col-span-full">
            <label className="text-sm text-neutral-600 dark:text-neutral-400 mb-1 block">
              Dias da semana
            </label>
            <div className={`grid grid-cols-7 gap-1 ${validationErrors.patterns ? 'border border-red-500 dark:border-red-500 p-1 rounded-md' : ''}`}>
              {["D", "S", "T", "Q", "Q", "S", "S"].map(
                (day, index) => (
                  <button
                    key={index}
                    type="button"
                    className={`p-2 h-12 w-12 text-base rounded-md transition-colors ${formData.recurrence.patterns?.some(
                      (p) => p.dayOfWeek === index
                    )
                      ? "bg-primary-500 text-white hover:bg-primary-600 dark:bg-primary-600 dark:hover:bg-primary-700"
                      : "bg-white dark:bg-gray-700 border border-neutral-300 dark:border-neutral-600 hover:bg-neutral-50 dark:hover:bg-gray-600 dark:text-gray-200"
                      }`}
                    onClick={() => {
                      const patterns =
                        formData.recurrence.patterns || [];
                      const dayExists = patterns.some(
                        (p) => p.dayOfWeek === index
                      );

                      let newPatterns;
                      if (dayExists) {
                        newPatterns = patterns.filter(
                          (p) => p.dayOfWeek !== index
                        );
                      } else {
                        newPatterns = [
                          ...patterns,
                          {
                            dayOfWeek: index,
                            startTime: formatTime(formData.startDate),
                            endTime: formatTime(formData.endDate),
                          },
                        ];
                      }

                      setFormData({
                        ...formData,
                        recurrence: {
                          ...formData.recurrence,
                          patterns: newPatterns,
                        },
                      });
                    }}
                  >
                    {day}
                  </button>
                )
              )}
            </div>
            {validationErrors.patterns && (
              <div className="mt-1 text-red-500 text-xs flex items-center gap-1">
                <AlertCircle className="w-3 h-3" />
                {validationErrors.patterns}
              </div>
            )}
          </div>

          </div>

          <div className="col-span-full text-xs text-neutral-500 dark:text-neutral-400 bg-neutral-50 dark:bg-gray-700 p-2 rounded-md">
            <div className="flex flex-wrap gap-x-4">
              <span>• O agendamento será repetido nos dias selecionados</span>
              <span>• O horário será o mesmo para todas as ocorrências</span>
              {formData.sequentialAppointments > 1 && (
                <span className="text-primary-600 dark:text-primary-400 font-medium">
                  • Os {formData.sequentialAppointments} agendamentos sequenciais serão criados para cada ocorrência
                </span>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RecurrenceSettings;