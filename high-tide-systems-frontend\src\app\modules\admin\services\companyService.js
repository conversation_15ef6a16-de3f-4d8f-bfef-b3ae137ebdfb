import { api } from "@/utils/api";

export const companyService = {
  // Obter a empresa atual do usuário autenticado
  getCurrentCompany: async () => {
    try {
      const response = await api.get('/companies/current');
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar empresa atual:", error);
      throw error;
    }
  },

  // Listar empresas com suporte a paginação e filtros
  getCompanies: async ({ page = 1, limit = 10, search, active } = {}) => {
    try {
      const params = new URLSearchParams();
      if (page) params.append('page', page);
      if (limit) params.append('limit', limit);
      if (search) params.append('search', search);
      if (active !== undefined) params.append('active', active);

      const response = await api.get(`/companies?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar empresas:", error);
      throw error;
    }
  },

  // Obter uma empresa específica
  getCompany: async (id) => {
    try {
      const response = await api.get(`/companies/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar empresa ${id}:`, error);
      throw error;
    }
  },

  // Obter lista de empresas para formulários de seleção
  getCompaniesForSelect: async () => {
    try {
      const response = await api.get("/companies/select");
      return response.data.companies || [];
    } catch (error) {
      console.error("Erro ao buscar empresas para seleção:", error);
      // Retorna array vazio em caso de erro para facilitar o manuseio no frontend
      return [];
    }
  },

  // Criar uma nova empresa
  createCompany: async (formData) => {
    try {
      const response = await api.post('/companies', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error("Erro ao criar empresa:", error);
      throw error;
    }
  },

  // Atualizar uma empresa existente
  updateCompany: async (id, formData) => {
    try {
      const response = await api.put(`/companies/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar empresa ${id}:`, error);
      throw error;
    }
  },

  // Alternar o status de uma empresa (ativo/inativo)
  toggleCompanyStatus: async (id) => {
    try {
      const response = await api.patch(`/companies/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao alterar status da empresa ${id}:`, error);
      throw error;
    }
  },

  // Excluir uma empresa
  deleteCompany: async (id) => {
    try {
      await api.delete(`/companies/${id}`);
      return true;
    } catch (error) {
      console.error(`Erro ao excluir empresa ${id}:`, error);
      throw error;
    }
  },

  // Obter o logo de uma empresa
  getCompanyLogo: async (id) => {
    try {
      const response = await api.get(`/companies/${id}/logo`, {
        responseType: 'blob'
      });
      return URL.createObjectURL(response.data);
    } catch (error) {
      console.error(`Erro ao obter logo da empresa ${id}:`, error);
      throw error;
    }
  }
};

export default companyService;