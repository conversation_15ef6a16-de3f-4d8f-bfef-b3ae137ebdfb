import React from 'react';
import { Users, MapPin, Briefcase, CreditCard, RotateCw } from 'lucide-react';
import { ModuleSelect, ModuleFormGroup } from '@/components/ui';

const AppointmentSelectors = ({
  formData,
  setFormData,
  filteredProviders,
  persons,
  locations,
  serviceTypes,
  personInsurances,
  isLoadingInsurances,
  isLoading
}) => {
  // Removido inputBaseClasses pois agora usamos o ModuleSelect

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {/* Profissional */}
      <ModuleFormGroup
        moduleColor="scheduler"
        label="Profissional"
        htmlFor="providerId"
        icon={<Users size={16} />}
      >
        <ModuleSelect
          moduleColor="scheduler"
          id="providerId"
          name="providerId"
          value={formData.providerId}
          onChange={(e) =>
            setFormData({ ...formData, providerId: e.target.value })
          }
          required
          className="text-base py-2"
        >
          <option value="">Selecione o profissional</option>
          {filteredProviders.map((provider) => (
            provider && provider.id ? (
              <option key={provider.id} value={provider.id}>
                {provider.fullName}
                {provider.profession && ` (${provider.profession})`}
                {provider.professionObj?.name && ` (${provider.professionObj.name})`}
              </option>
            ) : null
          ))}
        </ModuleSelect>
      </ModuleFormGroup>

      {/* Paciente */}
      <ModuleFormGroup
        moduleColor="scheduler"
        label="Paciente"
        htmlFor="personId"
        icon={<Users size={16} />}
      >
        <ModuleSelect
          moduleColor="scheduler"
          id="personId"
          name="personId"
          value={formData.personId}
          onChange={(e) =>
            setFormData({ ...formData, personId: e.target.value })
          }
          required
          className="text-base py-2"
        >
          <option value="">Selecione o paciente</option>
          {persons.map((person) => (
            person && person.id ? (
              <option key={person.id} value={person.id}>
                {person.fullName}
              </option>
            ) : null
          ))}
        </ModuleSelect>
      </ModuleFormGroup>

      {/* Local */}
      <ModuleFormGroup
        moduleColor="scheduler"
        label="Local"
        htmlFor="locationId"
        icon={<MapPin size={16} />}
      >
        <ModuleSelect
          moduleColor="scheduler"
          id="locationId"
          name="locationId"
          value={formData.locationId}
          onChange={(e) =>
            setFormData({ ...formData, locationId: e.target.value })
          }
          required
          className="text-base py-2"
        >
          <option value="">Selecione o local</option>
          {locations.map((location) => (
            location && location.id ? (
              <option key={location.id} value={location.id}>
                {location.name}
              </option>
            ) : null
          ))}
        </ModuleSelect>
      </ModuleFormGroup>

      {/* Tipo de Serviço */}
      <ModuleFormGroup
        moduleColor="scheduler"
        label="Tipo de Serviço"
        htmlFor="serviceTypeId"
        icon={<Briefcase size={16} />}
      >
        <ModuleSelect
          moduleColor="scheduler"
          id="serviceTypeId"
          name="serviceTypeId"
          value={formData.serviceTypeId}
          onChange={(e) =>
            setFormData({ ...formData, serviceTypeId: e.target.value })
          }
          disabled={!formData.insuranceId || !formData.personId || serviceTypes.length === 0}
          required
          className="text-base py-2"
        >
          <option value="">Selecione o tipo de serviço</option>
          {serviceTypes.map((type) => {
            if (!type || !type.id || !type.name) {
              console.warn("Invalid service type:", type);
              return null;
            }

            return (
              <option key={type.id} value={type.id}>
                {type.name}
              </option>
            );
          })}
        </ModuleSelect>
        {/* Mensagens de status */}
        {!formData.personId ? (
          <div className="text-xs text-blue-500 dark:text-blue-400 mt-1">
            Selecione um paciente primeiro
          </div>
        ) : !formData.insuranceId ? (
          <div className="text-xs text-blue-500 dark:text-blue-400 mt-1">
            Selecione um convênio primeiro
          </div>
        ) : isLoading ? (
          <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">Carregando tipos de serviço...</div>
        ) : serviceTypes.length === 0 ? (
          <div className="text-xs text-error-500 dark:text-error-400 mt-1">
            Nenhum tipo de serviço configurado para este paciente e convênio
          </div>
        ) : (
          <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">{serviceTypes.length} tipo(s) disponível(is)</div>
        )}
      </ModuleFormGroup>

      {/* Convênio */}
      <ModuleFormGroup
        moduleColor="scheduler"
        label="Convênio"
        htmlFor="insuranceId"
        icon={<CreditCard size={16} />}
      >
        <ModuleSelect
          moduleColor="scheduler"
          id="insuranceId"
          name="insuranceId"
          value={formData.insuranceId || ""}
          onChange={(e) =>
            setFormData({ ...formData, insuranceId: e.target.value || null })
          }
          disabled={!formData.personId || isLoadingInsurances}
          className="text-base py-2"
        >
          <option value="">Sem convênio</option>
          {isLoadingInsurances ? (
            <option value="" disabled>Carregando convênios...</option>
          ) : personInsurances.length > 0 ? (
            personInsurances.map((insurance) => {
              // Usar insuranceId em vez de id
              const insuranceId = insurance.insuranceId || insurance.id;
              const insuranceName = insurance.name || insurance.insurance.name || "Convênio";

              if (!insuranceId) {
                return null;
              }

              return (
                <option key={insuranceId} value={insuranceId}>
                  {insuranceName}
                  {insurance.policyNumber ? ` (${insurance.policyNumber})` : ''}
                </option>
              );
            })
          ) : formData.personId ? (
            <option value="" disabled>Sem convênios disponíveis</option>
          ) : (
            <option value="" disabled>Selecione um paciente primeiro</option>
          )}
        </ModuleSelect>

        {formData.personId && !isLoadingInsurances && personInsurances.length === 0 && (
          <div className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
            Este paciente não possui convênios cadastrados
          </div>
        )}
        {!formData.personId && (
          <div className="text-xs text-blue-500 dark:text-blue-400 mt-1">
            Selecione um paciente para ver os convênios disponíveis
          </div>
        )}
        {isLoadingInsurances && (
          <div className="text-xs text-primary-500 dark:text-primary-400 mt-1 flex items-center">
            <RotateCw className="w-3 h-3 mr-1 animate-spin" />
            Carregando convênios...
          </div>
        )}
      </ModuleFormGroup>
    </div>
  );
};

export default AppointmentSelectors;