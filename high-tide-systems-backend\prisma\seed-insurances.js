const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Iniciando seed de convênios de saúde...');

  // Lista de convênios de saúde reais no Brasil
  const healthInsuranceProviders = [
    { name: 'Amil', description: 'Uma das maiores operadoras de planos de saúde do Brasil' },
    { name: 'Bradesco Saúde', description: 'Planos de saúde do grupo Bradesco Seguros' },
    { name: 'SulAmérica', description: 'Empresa com mais de 120 anos no mercado de seguros e saúde' },
    { name: 'Unimed', description: 'Maior rede de assistência médica do Brasil, sistema cooperativo' },
    { name: 'Porto Seguro Saúde', description: 'Planos de saúde da Porto Seguro' },
    { name: 'NotreDame Intermédica', description: 'Uma das maiores operadoras de saúde do Brasil' },
    { name: 'Hap<PERSON><PERSON>', description: 'Operadora com rede própria de hospitais e clínicas' },
    { name: '<PERSON> Cross', description: 'Uma das pioneiras no setor de saúde suplementar no Brasil' },
    { name: 'Prevent Senior', description: 'Especializada em planos para a terceira idade' },
    { name: 'São Francisco Saúde', description: 'Operadora com forte presença no interior de São Paulo' },
    { name: 'Mediservice', description: 'Administradora de benefícios de saúde do grupo Bradesco' },
    { name: 'Omint', description: 'Operadora com foco em planos de alto padrão' },
    { name: 'Care Plus', description: 'Especializada em planos corporativos premium' },
    { name: 'Allianz Saúde', description: 'Planos de saúde da seguradora multinacional Allianz' },
    { name: 'Amil One', description: 'Linha premium da Amil com rede credenciada exclusiva' },
    { name: 'Assim Saúde', description: 'Operadora com forte atuação no Rio de Janeiro' },
    { name: 'Biovida Saúde', description: 'Operadora com foco em medicina preventiva' },
    { name: 'Central Nacional Unimed', description: 'Operadora nacional do Sistema Unimed' },
    { name: 'Economus', description: 'Operadora de autogestão ligada ao Banco do Brasil' },
    { name: 'Gama Saúde', description: 'Administradora de benefícios com planos personalizados' }
  ];

  // Buscar todas as empresas exceto a empresa de teste
  const companies = await prisma.company.findMany({
    where: {
      NOT: {
        id: '00000000-0000-0000-0000-000000000001' // Excluir a empresa de teste
      },
      active: true
    }
  });

  console.log(`Encontradas ${companies.length} empresas para adicionar convênios`);

  // Para cada empresa, criar entre 4 e 10 convênios aleatórios
  for (const company of companies) {
    console.log(`\nProcessando empresa: ${company.name} (ID: ${company.id})`);

    // Determinar quantos convênios criar (entre 4 e 10)
    const numInsurances = Math.floor(Math.random() * 7) + 4;
    console.log(`Criando ${numInsurances} convênios para a empresa ${company.name}`);

    // Embaralhar a lista de convênios para selecionar aleatoriamente
    const shuffledProviders = [...healthInsuranceProviders]
      .sort(() => 0.5 - Math.random())
      .slice(0, numInsurances);

    // Criar os convênios para esta empresa
    for (const provider of shuffledProviders) {
      // Verificar se o convênio já existe para esta empresa
      const existingInsurance = await prisma.insurance.findFirst({
        where: {
          name: provider.name,
          companyId: company.id
        }
      });

      if (existingInsurance) {
        console.log(`Convênio ${provider.name} já existe para a empresa ${company.name}. Pulando...`);
        continue;
      }

      // Criar o convênio
      const insurance = await prisma.insurance.create({
        data: {
          name: provider.name,
          company: {
            connect: { id: company.id }
          }
        }
      });

      console.log(`Convênio criado: ${insurance.name} (ID: ${insurance.id})`);
    }
  }

  console.log('\nSeed de convênios concluído com sucesso!');
}

main()
  .catch((e) => {
    console.error('Erro durante o seed de convênios:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
