"use client";

import { useState, useEffect, useCallback } from 'react';
import { usePathname } from 'next/navigation';
import { PrivateRoute } from '@/components/PrivateRoute';
import { Header, modules } from './components';
import ClientHeader from './ClientHeader';
import { useRouter } from 'next/navigation';
import Sidebar from '@/components/dashboard/Sidebar';
import ClientSidebar from '@/components/dashboard/Sidebar/ClientSidebar';
import { usePermissions } from '@/hooks/usePermissions';
import { useAuth } from '@/contexts/AuthContext';

// Componentes de tutorial
import TutorialManager from '@/components/tutorial/TutorialManager';
import ContextualHelpButton from '@/components/tutorial/ContextualHelpButton';

export default function DashboardLayout({ children }) {
  const router = useRouter();
  const pathname = usePathname();
  const { isClient } = usePermissions();
  const { user } = useAuth();

  // Verificar se o usuário é um cliente
  const isClientUser = () => {
    return user?.isClient || user?.role === 'CLIENT';
  };
  const [activeModule, setActiveModule] = useState(null);
  const [activeSubmenu, setActiveSubmenu] = useState(null);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  // Determina o módulo ativo e submenu baseado na URL
  useEffect(() => {
    if (pathname) {
      // Exemplo: /dashboard/people/persons/ID => people, persons
      const path = pathname.split('/');
      if (path.length >= 3) {
        const moduleFromUrl = path[2]; // O módulo é o terceiro segmento da URL
        setActiveModule(moduleFromUrl);

        if (path.length >= 4) {
          const submenuFromUrl = path[3]; // O submenu é o quarto segmento da URL
          setActiveSubmenu(submenuFromUrl);
        }
      } else {
        setActiveModule(null);
        setActiveSubmenu(null);
      }
    }
  }, [pathname]);

  // Memoize handlers para evitar recriações em cada render
  const handleBackToModules = useCallback(() => {
    router.push('/dashboard');
  }, [router]);

  const handleModuleSubmenuClick = useCallback((moduleId, submenuId) => {
    router.push(`/dashboard/${moduleId}/${submenuId}`);
  }, [router]);

  // Verifica se um submenu está ativo (mesmo quando estamos em uma página de detalhes)
  const isSubmenuActive = useCallback((moduleId, submenuId) => {
    return pathname.startsWith(`/dashboard/${moduleId}/${submenuId}`);
  }, [pathname]);

  const toggleSidebar = useCallback(() => {
    setIsSidebarOpen(prev => !prev);
  }, []);

  // Obter o título do módulo ativo
  const activeModuleTitle = modules.find(m => m.id === activeModule)?.title || '';

  return (
    <div className="min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 transition-colors">
      <PrivateRoute>
        <div className="flex">
          {/* Sidebar - Visível em todas as páginas exceto a página principal do dashboard e a página de perfil */}
          {pathname !== '/dashboard' && pathname !== '/dashboard/profile' && (
            isClientUser() ? (
              <ClientSidebar
                activeModule={activeModule}
                activeModuleTitle={activeModuleTitle}
                isSubmenuActive={isSubmenuActive}
                handleModuleSubmenuClick={handleModuleSubmenuClick}
                handleBackToModules={handleBackToModules}
                isSidebarOpen={isSidebarOpen}
              />
            ) : (
              <Sidebar
                activeModule={activeModule}
                activeModuleTitle={activeModuleTitle}
                isSubmenuActive={isSubmenuActive}
                handleModuleSubmenuClick={handleModuleSubmenuClick}
                handleBackToModules={handleBackToModules}
                isSidebarOpen={isSidebarOpen}
              />
            )
          )}

          {/* Main Content */}
          <div className="flex-1">
            {isClientUser() ? (
              <ClientHeader
                toggleSidebar={toggleSidebar}
                isSidebarOpen={isSidebarOpen}
              />
            ) : (
              <Header
                toggleSidebar={toggleSidebar}
                isSidebarOpen={isSidebarOpen}
              />
            )}

            <main
              className="p-6"
              id="main-content"
            >
              <div className="max-w-7xl mx-auto">
                {children}
              </div>
            </main>
          </div>
        </div>

        {/* Componentes de tutorial */}
        <TutorialManager />
        <ContextualHelpButton />
      </PrivateRoute>
    </div>
  );
}