// tests/controllers/service-type-controller-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Dados para testes
const testServiceType = {
  name: `Test Service Type ${Date.now()}`,
  value: 100.00
};

// Variáveis para armazenar IDs criados durante os testes
let serviceTypeId;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do controlador de tipos de serviço...');
  
  try {
    // Teste 1: Listar tipos de serviço
    console.log('\n1. Listando tipos de serviço...');
    const listResponse = await api.get('/service-types');
    
    if (listResponse.status === 200) {
      console.log(`✅ ${listResponse.data.length} tipos de serviço encontrados`);
    } else {
      console.log('❌ Falha ao listar tipos de serviço');
    }
    
    // Teste 2: Criar um novo tipo de serviço
    console.log('\n2. Criando tipo de serviço...');
    const createResponse = await api.post('/service-types', testServiceType);
    
    if (createResponse.status === 201) {
      console.log('✅ Tipo de serviço criado com sucesso!');
      serviceTypeId = createResponse.data.id;
      console.log(`ID do tipo de serviço: ${serviceTypeId}`);
    } else {
      console.log('❌ Falha ao criar tipo de serviço');
      return;
    }
    
    // Teste 3: Obter tipo de serviço por ID
    console.log('\n3. Obtendo tipo de serviço por ID...');
    const getResponse = await api.get(`/service-types/${serviceTypeId}`);
    
    if (getResponse.status === 200) {
      console.log('✅ Tipo de serviço obtido com sucesso!');
      console.log(`Nome: ${getResponse.data.name}`);
      console.log(`Valor: ${getResponse.data.value}`);
    } else {
      console.log('❌ Falha ao obter tipo de serviço');
    }
    
    // Teste 4: Atualizar tipo de serviço
    console.log('\n4. Atualizando tipo de serviço...');
    const updateResponse = await api.put(`/service-types/${serviceTypeId}`, {
      name: `${testServiceType.name} Updated`,
      value: 150.00
    });
    
    if (updateResponse.status === 200) {
      console.log('✅ Tipo de serviço atualizado com sucesso!');
      console.log(`Novo nome: ${updateResponse.data.name}`);
      console.log(`Novo valor: ${updateResponse.data.value}`);
    } else {
      console.log('❌ Falha ao atualizar tipo de serviço');
    }
    
    // Teste 5: Verificar cache
    console.log('\n5. Verificando cache...');
    
    // Primeira requisição (com cache)
    console.log('Fazendo primeira requisição (com cache)...');
    const startTime1 = Date.now();
    await api.get('/service-types');
    const endTime1 = Date.now();
    const time1 = endTime1 - startTime1;
    
    console.log(`✅ Primeira requisição concluída em ${time1}ms`);
    
    // Segunda requisição (com cache)
    console.log('Fazendo segunda requisição (com cache)...');
    const startTime2 = Date.now();
    await api.get('/service-types');
    const endTime2 = Date.now();
    const time2 = endTime2 - startTime2;
    
    console.log(`✅ Segunda requisição concluída em ${time2}ms`);
    
    // Verificar se a segunda requisição foi mais rápida (indicando uso de cache)
    if (time2 < time1) {
      console.log('✅ Cache está funcionando! Segunda requisição foi mais rápida.');
    } else {
      console.log('⚠️ Cache pode não estar funcionando corretamente. Segunda requisição não foi mais rápida.');
    }
    
    // Teste 6: Excluir tipo de serviço
    console.log('\n6. Excluindo tipo de serviço...');
    const deleteResponse = await api.delete(`/service-types/${serviceTypeId}`);
    
    if (deleteResponse.status === 200) {
      console.log('✅ Tipo de serviço excluído com sucesso!');
    } else {
      console.log('❌ Falha ao excluir tipo de serviço');
    }
    
    // Teste 7: Verificar limpeza de cache após exclusão
    console.log('\n7. Verificando limpeza de cache após exclusão...');
    
    try {
      const getAfterDeleteResponse = await api.get(`/service-types/${serviceTypeId}`);
      console.log('❌ Tipo de serviço ainda existe após exclusão (ou cache não foi limpo)');
      console.log(`Status: ${getAfterDeleteResponse.status}`);
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('✅ Tipo de serviço não encontrado após exclusão (cache foi limpo corretamente)');
      } else {
        throw error;
      }
    }
    
    console.log('\n✅ Todos os testes do controlador de tipos de serviço concluídos com sucesso!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
