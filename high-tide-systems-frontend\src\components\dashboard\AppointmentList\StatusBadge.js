'use client';

import React from 'react';
import { getStatusInfo } from '@/utils/statusHelpers';

export const StatusBadge = ({ status }) => {
  const statusInfo = getStatusInfo(status);
  const StatusIcon = statusInfo.icon;
  return (
    <div className={`px-3 py-1.5 rounded-lg text-xs font-medium flex items-center 
                   border border-appointmentStatus-${statusInfo.status}-border 
                   dark:border-appointmentStatus-${statusInfo.status}-border-dark
                   bg-appointmentStatus-${statusInfo.status}-bg/10 
                   dark:bg-appointmentStatus-${statusInfo.status}-bg-dark/20`}>
      <StatusIcon 
        size={14} 
        className={`text-appointmentStatus-${statusInfo.status}-icon 
                   dark:text-appointmentStatus-${statusInfo.status}-icon-dark 
                   mr-1.5 ${statusInfo.iconColor}`} 
        aria-hidden="true" />
      <span className={`text-appointmentStatus-${statusInfo.status}-text 
                      dark:text-appointmentStatus-${statusInfo.status}-text-dark`}>
        {statusInfo.text}
      </span>
    </div>
  );
};

export default StatusBadge;