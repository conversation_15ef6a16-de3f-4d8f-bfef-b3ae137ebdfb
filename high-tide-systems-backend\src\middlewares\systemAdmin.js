/**
 * Middleware que verifica se o usuário é um administrador do sistema
 * Este middleware deve ser usado após o middleware de autenticação
 */
const systemAdminMiddleware = (req, res, next) => {
    // Verifica se o usuário está autenticado
    if (!req.user) {
      return res.status(401).json({ message: 'Autenticação necessária' });
    }
  
    // Verifica se o usuário é um administrador do sistema
    if (req.user.role !== 'SYSTEM_ADMIN') {
      return res.status(403).json({ 
        message: 'Acesso restrito a administradores do sistema' 
      });
    }
  
    next();
  };
  
  module.exports = systemAdminMiddleware;