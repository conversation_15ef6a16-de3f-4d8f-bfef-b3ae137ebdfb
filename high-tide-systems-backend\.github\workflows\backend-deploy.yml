name: Deploy Backend

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test
        continue-on-error: true

      - name: Deploy to VPS
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.VPS_HOST }}
          username: ${{ secrets.VPS_USERNAME }}
          key: ${{ secrets.VPS_SSH_KEY }}
          script: |
            echo "===== INICIANDO DEPLOY DO BACKEND ====="
            echo "[1/7] Navegando para o diretório do projeto..."
            cd ${{ secrets.VPS_PROJECT_PATH }}
            
            # Verificar se o diretório existe, se não, cloná-lo
            if [ ! -d "inside-the-houses-backend" ]; then
              echo "[2/7] Repositório não encontrado. Clonando repositório..."
              git clone https://github.com/gabrielbr619/inside-the-houses-backend.git
              cd inside-the-houses-backend
            else
              echo "[2/7] Repositório encontrado. Atualizando código..."
              cd inside-the-houses-backend
              git fetch --all
              git reset --hard origin/main
            fi
            
            # Pull das alterações mais recentes
            echo "[3/7] Baixando as alterações mais recentes..."
            git pull origin main
            
            # Voltar para o diretório raiz
            echo "[4/7] Voltando para o diretório raiz..."
            cd ${{ secrets.VPS_PROJECT_PATH }}
            
            # Reconstruir e reiniciar contêiner do backend
            echo "[5/7] Reconstruindo o contêiner da API..."
            docker compose build api
            
            echo "[6/7] Reiniciando o contêiner da API..."
            docker compose up -d api
            
            # Limpar imagens Docker não utilizadas para economizar espaço
            echo "[7/7] Limpando imagens Docker não utilizadas..."
            docker image prune -f
            
            echo "===== DEPLOY DO BACKEND CONCLUÍDO COM SUCESSO ====="
