"use client";

import React from "react";

/**
 * Componente para o conteúdo de uma aba do ModuleTabs
 * 
 * @param {Object} props
 * @param {string} props.id - ID da aba
 * @param {string} props.label - <PERSON><PERSON><PERSON><PERSON> da aba
 * @param {React.ReactNode} props.children - <PERSON><PERSON><PERSON><PERSON> da aba
 * @param {boolean} props.active - Se a aba está ativa
 */
const ModuleTab = ({ id, label, children, active }) => {
  if (!active) return null;
  
  return (
    <div id={`tab-content-${id}`} role="tabpanel" aria-labelledby={`tab-${id}`}>
      {children}
    </div>
  );
};

export default ModuleTab;
