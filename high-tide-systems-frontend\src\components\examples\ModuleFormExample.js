'use client';

import React, { useState } from 'react';
import { User, Mail, Phone, MapPin, Calendar, FileText } from 'lucide-react';
import ModuleModal from '../ui/ModuleModal';
import ModalButton from '../ui/ModalButton';
import ModuleInput from '../ui/ModuleInput';
import ModuleSelect from '../ui/ModuleSelect';
import ModuleTextarea from '../ui/ModuleTextarea';
import ModuleFormGroup from '../ui/ModuleFormGroup';

/**
 * Componente de exemplo para demonstrar o uso dos componentes de formulário adaptados à cor do módulo
 */
const ModuleFormExample = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeModule, setActiveModule] = useState('people');
  
  const modules = [
    { id: 'people', name: '<PERSON><PERSON><PERSON><PERSON>', color: 'people' },
    { id: 'scheduler', name: 'Agendamento', color: 'scheduler' },
    { id: 'admin', name: 'Administração', color: 'admin' },
    { id: 'financial', name: 'Financeiro', color: 'financial' },
  ];
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    birthdate: '',
    gender: '',
    notes: '',
  });
  
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };
  
  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form data:', formData);
    setIsOpen(false);
  };
  
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor={activeModule}
        onClick={() => setIsOpen(false)}
      >
        Cancelar
      </ModalButton>
      
      <ModalButton
        variant="primary"
        moduleColor={activeModule}
        type="submit"
        form="module-form-example"
      >
        Salvar
      </ModalButton>
    </div>
  );
  
  return (
    <div className="p-6 space-y-6">
      <h2 className="text-2xl font-semibold text-neutral-800 dark:text-neutral-100">
        Exemplo de Formulário com Cores de Módulo
      </h2>
      
      <div className="flex flex-wrap gap-3">
        {modules.map((module) => (
          <button
            key={module.id}
            onClick={() => {
              setActiveModule(module.id);
              setIsOpen(true);
            }}
            className={`px-4 py-2 rounded-lg font-medium transition-colors
              ${module.id === 'people' ? 'bg-orange-500 hover:bg-orange-600 text-white' : ''}
              ${module.id === 'scheduler' ? 'bg-purple-500 hover:bg-purple-600 text-white' : ''}
              ${module.id === 'admin' ? 'bg-pink-500 hover:bg-pink-600 text-white' : ''}
              ${module.id === 'financial' ? 'bg-green-500 hover:bg-green-600 text-white' : ''}
            `}
          >
            Abrir Modal de {module.name}
          </button>
        ))}
      </div>
      
      <ModuleModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="Formulário de Exemplo"
        icon={<User size={22} />}
        moduleColor={activeModule}
        size="lg"
        footer={modalFooter}
      >
        <form id="module-form-example" onSubmit={handleSubmit} className="p-6 space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ModuleFormGroup
              moduleColor={activeModule}
              label="Nome Completo"
              htmlFor="name"
              icon={<User />}
              required
            >
              <ModuleInput
                moduleColor={activeModule}
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="Digite o nome completo"
                required
                leftIcon={<User size={16} />}
              />
            </ModuleFormGroup>
            
            <ModuleFormGroup
              moduleColor={activeModule}
              label="E-mail"
              htmlFor="email"
              icon={<Mail />}
            >
              <ModuleInput
                moduleColor={activeModule}
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Digite o e-mail"
                leftIcon={<Mail size={16} />}
              />
            </ModuleFormGroup>
            
            <ModuleFormGroup
              moduleColor={activeModule}
              label="Telefone"
              htmlFor="phone"
              icon={<Phone />}
            >
              <ModuleInput
                moduleColor={activeModule}
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="Digite o telefone"
                leftIcon={<Phone size={16} />}
              />
            </ModuleFormGroup>
            
            <ModuleFormGroup
              moduleColor={activeModule}
              label="Endereço"
              htmlFor="address"
              icon={<MapPin />}
            >
              <ModuleInput
                moduleColor={activeModule}
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder="Digite o endereço"
                leftIcon={<MapPin size={16} />}
              />
            </ModuleFormGroup>
            
            <ModuleFormGroup
              moduleColor={activeModule}
              label="Data de Nascimento"
              htmlFor="birthdate"
              icon={<Calendar />}
            >
              <ModuleInput
                moduleColor={activeModule}
                id="birthdate"
                name="birthdate"
                type="date"
                value={formData.birthdate}
                onChange={handleChange}
              />
            </ModuleFormGroup>
            
            <ModuleFormGroup
              moduleColor={activeModule}
              label="Gênero"
              htmlFor="gender"
              icon={<User />}
            >
              <ModuleSelect
                moduleColor={activeModule}
                id="gender"
                name="gender"
                value={formData.gender}
                onChange={handleChange}
                placeholder="Selecione o gênero"
              >
                <option value="male">Masculino</option>
                <option value="female">Feminino</option>
                <option value="other">Outro</option>
                <option value="not_informed">Prefiro não informar</option>
              </ModuleSelect>
            </ModuleFormGroup>
          </div>
          
          <ModuleFormGroup
            moduleColor={activeModule}
            label="Observações"
            htmlFor="notes"
            icon={<FileText />}
            helpText="Informações adicionais sobre o cadastro"
          >
            <ModuleTextarea
              moduleColor={activeModule}
              id="notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              placeholder="Digite observações adicionais"
              rows={4}
            />
          </ModuleFormGroup>
        </form>
      </ModuleModal>
    </div>
  );
};

export default ModuleFormExample;
