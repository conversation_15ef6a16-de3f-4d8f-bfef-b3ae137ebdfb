"use client";

import React, { useState, useEffect } from "react";
import { Plus, Search, Filter, RefreshCw, FileText } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import ExportMenu from "@/components/ui/ExportMenu";
import { isUnderConstruction } from "@/utils/constructionUtils";

const InvoicesPage = () => {
  const { user: currentUser } = useAuth();
  const [isExporting, setIsExporting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [invoices, setInvoices] = useState([]);

  // Verificar se o módulo está em construção
  const moduleUnderConstruction = isUnderConstruction('financial', 'invoices');

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Implementação futura
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error("Erro ao exportar faturas:", error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Faturas</h1>
        <div className="flex gap-2">
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || invoices.length === 0}
            underConstruction={moduleUnderConstruction}
          />
          <button
            className="flex items-center gap-2 px-4 py-2 bg-primary-500 dark:bg-primary-600 text-white rounded-lg hover:bg-primary-600 dark:hover:bg-primary-700 transition-colors"
          >
            <Plus size={16} />
            <span>Nova Fatura</span>
          </button>
        </div>
      </div>

      {/* Conteúdo em construção */}
      <div className="flex flex-col items-center justify-center p-10 bg-white dark:bg-gray-800 rounded-lg shadow">
        <FileText size={64} className="text-amber-500 dark:text-amber-400 mb-4" />
        <h2 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-2">Módulo em Construção</h2>
        <p className="text-neutral-600 dark:text-neutral-300 text-center max-w-md">
          O módulo de faturas está em desenvolvimento e estará disponível em breve. 
          Você poderá gerenciar todas as faturas da empresa nesta página.
        </p>
      </div>
    </div>
  );
};

export default InvoicesPage;
