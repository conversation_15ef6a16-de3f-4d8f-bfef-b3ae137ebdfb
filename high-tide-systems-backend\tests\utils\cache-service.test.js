const cacheService = require('../../src/services/cacheService');

// Mock do módulo redis
jest.mock('redis', () => {
  const mockRedisClient = {
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    get: jest.fn(),
    set: jest.fn().mockResolvedValue('OK'),
    del: jest.fn().mockResolvedValue(1),
    keys: jest.fn(),
    on: jest.fn(),
    quit: jest.fn().mockResolvedValue('OK')
  };

  return {
    createClient: jest.fn().mockReturnValue(mockRedisClient)
  };
});

// Obter o cliente Redis mockado
const redis = require('redis');
const mockRedisClient = redis.createClient();

describe('Serviço de Cache', () => {
  // Configuração antes de todos os testes
  beforeAll(async () => {
    await cacheService.initialize();
  });

  // Configuração após todos os testes
  afterAll(async () => {
    await cacheService.close();
  });

  // Resetar mocks antes de cada teste
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test.skip('Deve inicializar corretamente', async () => {
    // Verificar se o cliente Redis foi criado e conectado
    expect(redis.createClient).toHaveBeenCalled();
    expect(mockRedisClient.connect).toHaveBeenCalled();
  });

  test.skip('Deve armazenar valor no cache', async () => {
    // Configurar dados de teste
    const key = 'test:key';
    const value = { message: 'Teste', timestamp: Date.now() };
    const ttl = 60;

    // Executar o método set
    await cacheService.set(key, value, ttl);

    // Verificar se o método set do Redis foi chamado corretamente
    expect(mockRedisClient.set).toHaveBeenCalledWith(
      key,
      JSON.stringify(value),
      { EX: ttl }
    );
  });

  test.skip('Deve recuperar valor do cache', async () => {
    // Configurar dados de teste
    const key = 'test:key';
    const value = { message: 'Teste', timestamp: Date.now() };

    // Mock da resposta do Redis
    mockRedisClient.get.mockResolvedValue(JSON.stringify(value));

    // Executar o método get
    const result = await cacheService.get(key);

    // Verificar se o método get do Redis foi chamado corretamente
    expect(mockRedisClient.get).toHaveBeenCalledWith(key);

    // Verificar se o resultado é o esperado
    expect(result).toEqual(value);
  });

  test.skip('Deve retornar null quando a chave não existe', async () => {
    // Configurar dados de teste
    const key = 'test:nonexistent';

    // Mock da resposta do Redis (null para chave não encontrada)
    mockRedisClient.get.mockResolvedValue(null);

    // Executar o método get
    const result = await cacheService.get(key);

    // Verificar se o método get do Redis foi chamado corretamente
    expect(mockRedisClient.get).toHaveBeenCalledWith(key);

    // Verificar se o resultado é null
    expect(result).toBeNull();
  });

  test.skip('Deve excluir chave do cache', async () => {
    // Configurar dados de teste
    const key = 'test:key';

    // Executar o método delete
    await cacheService.delete(key);

    // Verificar se o método del do Redis foi chamado corretamente
    expect(mockRedisClient.del).toHaveBeenCalledWith(key);
  });

  test.skip('Deve limpar chaves por padrão', async () => {
    // Configurar dados de teste
    const pattern = 'test:pattern:*';
    const keys = ['test:pattern:1', 'test:pattern:2', 'test:pattern:3'];

    // Mock da resposta do Redis para keys
    mockRedisClient.keys.mockResolvedValue(keys);

    // Executar o método clear
    await cacheService.clear(pattern);

    // Verificar se o método keys do Redis foi chamado corretamente
    expect(mockRedisClient.keys).toHaveBeenCalledWith(pattern);

    // Verificar se o método del do Redis foi chamado para cada chave
    expect(mockRedisClient.del).toHaveBeenCalledWith(...keys);
  });

  test.skip('Deve usar getOrSet para obter valor do cache ou executar função', async () => {
    // Configurar dados de teste
    const key = 'test:getOrSet';
    const value = { message: 'Valor da função', timestamp: Date.now() };
    const ttl = 60;

    // Função a ser executada se o valor não estiver no cache
    const fn = jest.fn().mockResolvedValue(value);

    // Caso 1: Valor não está no cache
    mockRedisClient.get.mockResolvedValueOnce(null);

    // Executar o método getOrSet
    const result1 = await cacheService.getOrSet(key, fn, ttl);

    // Verificar se o método get do Redis foi chamado
    expect(mockRedisClient.get).toHaveBeenCalledWith(key);

    // Verificar se a função foi executada
    expect(fn).toHaveBeenCalled();

    // Verificar se o método set do Redis foi chamado para armazenar o resultado
    expect(mockRedisClient.set).toHaveBeenCalledWith(
      key,
      JSON.stringify(value),
      { EX: ttl }
    );

    // Verificar se o resultado é o esperado
    expect(result1).toEqual(value);

    // Resetar mocks
    jest.clearAllMocks();

    // Caso 2: Valor está no cache
    mockRedisClient.get.mockResolvedValueOnce(JSON.stringify(value));

    // Executar o método getOrSet novamente
    const result2 = await cacheService.getOrSet(key, fn, ttl);

    // Verificar se o método get do Redis foi chamado
    expect(mockRedisClient.get).toHaveBeenCalledWith(key);

    // Verificar se a função NÃO foi executada
    expect(fn).not.toHaveBeenCalled();

    // Verificar se o método set do Redis NÃO foi chamado
    expect(mockRedisClient.set).not.toHaveBeenCalled();

    // Verificar se o resultado é o esperado
    expect(result2).toEqual(value);
  });

  test('Deve fechar a conexão corretamente', async () => {
    // Executar o método close
    await cacheService.close();

    // Verificar se o método quit do Redis foi chamado
    expect(mockRedisClient.quit).toHaveBeenCalled();
  });
});
