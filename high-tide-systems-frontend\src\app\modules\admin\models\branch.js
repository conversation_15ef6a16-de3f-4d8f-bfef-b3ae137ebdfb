export class Branch {
  constructor({
    id,
    name,
    code,
    description,
    address,
    neighborhood,
    city,
    state,
    postalCode,
    phone,
    email,
    active,
    isHeadquarters,
    companyId,
    company,
    defaultWorkingHours,
    createdAt,
    updatedAt
  }) {
    this.id = id;
    this.name = name;
    this.code = code;
    this.description = description;
    this.address = address;
    this.neighborhood = neighborhood;
    this.city = city;
    this.state = state;
    this.postalCode = postalCode;
    this.phone = phone;
    this.email = email;
    this.active = active;
    this.isHeadquarters = isHeadquarters;
    this.companyId = companyId;
    this.company = company;
    this.defaultWorkingHours = defaultWorkingHours || null;
    this.createdAt = createdAt ? new Date(createdAt) : null;
    this.updatedAt = updatedAt ? new Date(updatedAt) : null;
  }

  // Format phone number for display
  get formattedPhone() {
    if (!this.phone) return '';
    
    const phone = this.phone.replace(/\D/g, '');
    if (phone.length === 11) {
      return `(${phone.substring(0, 2)}) ${phone.substring(2, 7)}-${phone.substring(7)}`;
    } else if (phone.length === 10) {
      return `(${phone.substring(0, 2)}) ${phone.substring(2, 6)}-${phone.substring(6)}`;
    }
    return this.phone;
  }

  // Get full address
  get fullAddress() {
    const parts = [this.address];
    if (this.neighborhood) parts.push(this.neighborhood);
    if (this.city) parts.push(this.city);
    if (this.state) parts.push(this.state);
    if (this.postalCode) parts.push(this.postalCode);
    
    return parts.join(', ');
  }

  // Check if branch has default working hours
  get hasDefaultWorkingHours() {
    return this.defaultWorkingHours !== null && 
           Object.keys(this.defaultWorkingHours || {}).length > 0;
  }
}

export default Branch;
