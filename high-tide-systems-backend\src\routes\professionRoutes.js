// src/routes/professionRoutes.js
const express = require("express");
const router = express.Router();
const {
  ProfessionController,
  createProfessionValidation,
  updateProfessionValidation
} = require("../controllers/professionController");
const {
  ProfessionGroupController,
  createProfessionGroupValidation,
  updateProfessionGroupValidation
} = require("../controllers/professionGroupController");
const { authenticate } = require("../middlewares/auth");
const checkPermission = require("../middlewares/permissionCheck");

// Rotas para grupos de profissões (devem vir antes das rotas com parâmetros dinâmicos)
router.get("/groups", authenticate, checkPermission('admin.profession-groups.view'), ProfessionGroupController.list);
router.get("/groups/:id", authenticate, checkPermission('admin.profession-groups.view'), ProfessionGroupController.getById);
router.post("/groups", authenticate, checkPermission('admin.profession-groups.create'), createProfessionGroupValidation, ProfessionGroupController.create);
router.put("/groups/:id", authenticate, checkPermission('admin.profession-groups.edit'), updateProfessionGroupValidation, ProfessionGroupController.update);
router.delete("/groups/:id", authenticate, checkPermission('admin.profession-groups.delete'), ProfessionGroupController.delete);

// Rotas para profissões
router.get("/", authenticate, checkPermission('admin.professions.view'), ProfessionController.list);
router.get("/:id/users", authenticate, checkPermission('admin.professions.view'), ProfessionController.listUsers); // Nova rota para listar usuários por profissão
router.get("/:id", authenticate, checkPermission('admin.professions.view'), ProfessionController.getById);
router.post("/", authenticate, checkPermission('admin.professions.create'), createProfessionValidation, ProfessionController.create);
router.put("/:id", authenticate, checkPermission('admin.professions.edit'), updateProfessionValidation, ProfessionController.update);
router.delete("/:id", authenticate, checkPermission('admin.professions.delete'), ProfessionController.delete);

module.exports = router;
