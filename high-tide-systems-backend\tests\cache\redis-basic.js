// test-redis.js
require('dotenv').config();
const cacheService = require('../../src/services/cacheService');

async function testRedisConnection() {
  console.log('Testando conexão com Redis...');

  try {
    // Inicializar o serviço de cache
    const result = await cacheService.initialize();

    if (result.success) {
      console.log('✅ Conexão com Redis estabelecida com sucesso!');
      console.log(`URL do Redis: ${process.env.REDIS_URL || 'redis://localhost:6379'}`);
      return true;
    } else {
      console.error('❌ Falha ao conectar com Redis:', result.error);
      return false;
    }
  } catch (error) {
    console.error('❌ Erro ao testar conexão com Redis:', error);
    return false;
  }
}

async function testCacheOperations() {
  console.log('\nTestando operações básicas de cache...');

  try {
    // Definir um valor no cache
    const testKey = 'test:key';
    const testValue = { message: 'Teste de cache', timestamp: new Date().toISOString() };

    console.log(`Armazenando valor no cache com chave "${testKey}"...`);
    const setResult = await cacheService.set(testKey, testValue, 60); // TTL de 60 segundos

    if (setResult) {
      console.log('✅ Valor armazenado com sucesso!');
    } else {
      console.error('❌ Falha ao armazenar valor no cache');
      return false;
    }

    // Recuperar o valor do cache
    console.log(`Recuperando valor do cache com chave "${testKey}"...`);
    const cachedValue = await cacheService.get(testKey);

    if (cachedValue) {
      console.log('✅ Valor recuperado com sucesso!');
      console.log('Valor armazenado:', cachedValue);

      // Verificar se o valor recuperado é igual ao valor armazenado
      if (JSON.stringify(cachedValue) === JSON.stringify(testValue)) {
        console.log('✅ Valores são idênticos!');
      } else {
        console.error('❌ Valores são diferentes!');
        console.log('Valor original:', testValue);
        console.log('Valor recuperado:', cachedValue);
        return false;
      }
    } else {
      console.error('❌ Falha ao recuperar valor do cache');
      return false;
    }

    // Testar a função getOrSet
    console.log('\nTestando função getOrSet...');
    const getOrSetKey = 'test:getOrSet';

    // Primeira chamada - deve executar a função
    console.log('Primeira chamada - deve executar a função...');
    const firstCallValue = await cacheService.getOrSet(
      getOrSetKey,
      async () => {
        console.log('✅ Função executada como esperado!');
        return { generated: true, value: 'Valor gerado pela função', timestamp: Date.now() };
      },
      60
    );

    console.log('Valor retornado:', firstCallValue);

    // Segunda chamada - deve retornar do cache
    console.log('\nSegunda chamada - deve retornar do cache...');
    const secondCallValue = await cacheService.getOrSet(
      getOrSetKey,
      async () => {
        console.log('❌ Função executada novamente (não deveria)!');
        return { generated: true, value: 'Novo valor gerado', timestamp: Date.now() };
      },
      60
    );

    console.log('Valor retornado:', secondCallValue);

    if (JSON.stringify(firstCallValue) === JSON.stringify(secondCallValue)) {
      console.log('✅ Segunda chamada retornou o valor em cache como esperado!');
    } else {
      console.error('❌ Segunda chamada retornou um valor diferente!');
      return false;
    }

    // Testar a exclusão de chave
    console.log('\nTestando exclusão de chave...');
    console.log(`Excluindo chave "${testKey}"...`);
    const deleteResult = await cacheService.delete(testKey);

    if (deleteResult) {
      console.log('✅ Chave excluída com sucesso!');

      // Verificar se a chave foi realmente excluída
      const deletedValue = await cacheService.get(testKey);

      if (deletedValue === null) {
        console.log('✅ Chave não existe mais no cache!');
      } else {
        console.error('❌ Chave ainda existe no cache!');
        return false;
      }
    } else {
      console.error('❌ Falha ao excluir chave do cache');
      return false;
    }

    // Testar limpeza por padrão
    console.log('\nTestando limpeza por padrão...');

    // Criar várias chaves com o mesmo prefixo
    const prefix = 'test:pattern';
    for (let i = 1; i <= 5; i++) {
      await cacheService.set(`${prefix}:${i}`, { index: i, value: `Valor ${i}` }, 60);
    }

    console.log('✅ Criadas 5 chaves com o padrão "test:pattern:*"');

    // Limpar todas as chaves com o padrão
    console.log('Limpando todas as chaves com o padrão "test:pattern:*"...');
    const clearResult = await cacheService.clear(`${prefix}:*`);

    if (clearResult) {
      console.log('✅ Chaves limpas com sucesso!');

      // Verificar se as chaves foram realmente excluídas
      let allDeleted = true;
      for (let i = 1; i <= 5; i++) {
        const value = await cacheService.get(`${prefix}:${i}`);
        if (value !== null) {
          console.error(`❌ Chave "${prefix}:${i}" ainda existe no cache!`);
          allDeleted = false;
        }
      }

      if (allDeleted) {
        console.log('✅ Todas as chaves foram excluídas!');
      } else {
        return false;
      }
    } else {
      console.error('❌ Falha ao limpar chaves do cache');
      return false;
    }

    return true;
  } catch (error) {
    console.error('❌ Erro ao testar operações de cache:', error);
    return false;
  }
}

async function runTests() {
  console.log('=== TESTE DO REDIS CACHE ===\n');

  // Testar conexão
  const connectionSuccess = await testRedisConnection();

  if (!connectionSuccess) {
    console.error('\n❌ Teste de conexão falhou. Abortando testes restantes.');
    process.exit(1);
  }

  // Testar operações de cache
  const operationsSuccess = await testCacheOperations();

  if (operationsSuccess) {
    console.log('\n✅ Todos os testes de cache foram bem-sucedidos!');
  } else {
    console.error('\n❌ Alguns testes de cache falharam.');
    process.exit(1);
  }

  // Fechar a conexão com o Redis
  await cacheService.close();
  console.log('\nConexão com Redis fechada.');

  process.exit(0);
}

// Executar os testes
runTests().catch(error => {
  console.error('Erro fatal durante os testes:', error);
  process.exit(1);
});
