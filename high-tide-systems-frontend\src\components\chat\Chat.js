'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import ChatContainer from './ChatContainer';
import ChatButton from './ChatButton';

const Chat = () => {
  const { user } = useAuth();

  // Se o usuário não estiver logado ou for um cliente, não mostrar o chat
  if (!user || user.isClient) return null;

  return (
    <>
      <ChatContainer />
      <ChatButton />
    </>
  );
};

export default Chat;
