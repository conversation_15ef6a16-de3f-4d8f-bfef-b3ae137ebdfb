// src/app/modules/aba/services/scoreService.js
import { api } from "@/utils/api";

export const scoreService = {
  // Get scores with optional filters
  getScores: async (filters = {}) => {
    const { page = 1, limit = 10, search = "", evaluationId } = filters;

    try {
      console.log("scoreService.getScores - Enviando requisição com parâmetros:", {
        page,
        limit,
        search: search || undefined,
        evaluationId: evaluationId || undefined
      });

      const response = await api.get("/aba/scores", {
        params: {
          page,
          limit,
          search: search || undefined,
          evaluationId: evaluationId || undefined
        }
      });

      console.log("scoreService.getScores - Resposta da API:", response.data);

      // Verificar a estrutura da resposta
      const items = response.data.scores || response.data.items || [];
      const total = response.data.total || 0;

      console.log("scoreService.getScores - Itens extraídos:", items);

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching scores:", error);
      throw error;
    }
  },

  // Get a single score by ID
  getScore: async (id) => {
    try {
      const response = await api.get(`/aba/scores/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching score ${id}:`, error);
      throw error;
    }
  },

  // Create a new score
  createScore: async (scoreData) => {
    try {
      const response = await api.post("/aba/scores", scoreData);
      return response.data;
    } catch (error) {
      console.error("Error creating score:", error);
      throw error;
    }
  },

  // Update an existing score
  updateScore: async (id, scoreData) => {
    try {
      const response = await api.put(`/aba/scores/${id}`, scoreData);
      return response.data;
    } catch (error) {
      console.error(`Error updating score ${id}:`, error);
      throw error;
    }
  },

  // Delete a score
  deleteScore: async (id) => {
    try {
      const response = await api.delete(`/aba/scores/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting score ${id}:`, error);
      throw error;
    }
  }
};

export default scoreService;
