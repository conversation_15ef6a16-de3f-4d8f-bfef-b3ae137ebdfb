'use client';

import React from 'react';
import { MessageCircle } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';
import { useAuth } from '@/contexts/AuthContext';
import { motion } from 'framer-motion';

const ChatButton = () => {
  const { toggleChatPanel, unreadCount } = useChat();
  const { user } = useAuth();

  // Se o usuário não estiver logado ou for um cliente, não mostrar o botão
  if (!user || user.isClient) return null;

  return (
    <button
      onClick={toggleChatPanel}
      className="p-2 text-orange-600 dark:text-orange-300 hover:bg-orange-100 dark:hover:bg-orange-900/30 rounded-full relative transition-colors"
      aria-label="Mensagens"
    >
      <MessageCircle size={20} aria-hidden="true" />
      {unreadCount > 0 && (
        <motion.span
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute top-0 right-0 h-5 w-5 flex items-center justify-center rounded-full bg-orange-500 text-white text-xs font-bold"
        >
          {unreadCount > 9 ? '9+' : unreadCount}
        </motion.span>
      )}
    </button>
  );
};

export default ChatButton;
