// src/controllers/locationController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");

// Validations
const createLocationValidation = [
  body("name").notEmpty().withMessage("Nome é obrigatório"),
  body("address").notEmpty().withMessage("Endereço é obrigatório"),
  body("phone").optional().matches(/^\d{10,11}$/).withMessage("Telefone inválido"),
  body("branchId").optional().isUUID().withMessage("ID da unidade inválido"),
];

class LocationController {
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, address, phone, branchId, companyId } = req.body;

      // Either branchId or companyId must be provided
      if (!branchId && !companyId) {
        return res.status(400).json({
          message: 'Você deve fornecer o ID da unidade ou o ID da empresa'
        });
      }

      let data = {
        name,
        address,
        phone
      };

      // If branchId is provided, check if it exists
      if (branchId) {
        const branch = await prisma.branch.findUnique({
          where: { id: branchId }
        });

        if (!branch) {
          return res.status(404).json({ message: 'Unidade não encontrada' });
        }

        // Check if user has access to this branch
        if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && branch.companyId !== req.user.companyId) {
          return res.status(403).json({ message: 'Acesso negado a esta unidade' });
        }

        data.branchId = branchId;
        data.companyId = branch.companyId; // Set the company ID from the branch
      } else {
        // Only set companyId if branchId is not provided
        data.companyId = companyId;
      }

      const location = await prisma.location.create({
        data
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'CREATE',
          entityType: 'Location',
          entityId: location.id,
          details: { requestData: req.body },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: data.companyId
        }
      });

      res.status(201).json(location);
    } catch (error) {
      console.error("Erro ao criar localização:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async update(req, res) {
    try {
      const { id } = req.params;
      const { name, address, phone, branchId } = req.body;

      // Check if location exists
      const existingLocation = await prisma.location.findUnique({
        where: { id }
      });

      if (!existingLocation) {
        return res.status(404).json({ message: "Localização não encontrada" });
      }

      // Check if user has access to this location
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && existingLocation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta localização' });
      }

      let data = {
        name,
        address,
        phone
      };

      // If branchId is provided, check if it exists
      if (branchId) {
        const branch = await prisma.branch.findUnique({
          where: { id: branchId }
        });

        if (!branch) {
          return res.status(404).json({ message: "Unidade não encontrada" });
        }

        // Make sure the branch is from the same company as the location
        if (existingLocation.companyId && branch.companyId !== existingLocation.companyId) {
          return res.status(400).json({
            message: 'A unidade deve pertencer à mesma empresa da localização'
          });
        }

        data.branchId = branchId;
      }

      const location = await prisma.location.update({
        where: { id },
        data
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'UPDATE',
          entityType: 'Location',
          entityId: location.id,
          details: { requestData: req.body },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: location.companyId
        }
      });

      res.json(location);
    } catch (error) {
      console.error("Erro ao atualizar localização:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async list(req, res) {
    try {
      const { page = 1, limit = 10, search, active, branchId, companyId, locationIds } = req.query;

      // Construir filtros AND
      const andFilters = [
        search
          ? {
              OR: [
                { name: { contains: search, mode: 'insensitive' } },
                { address: { contains: search, mode: 'insensitive' } },
              ],
            }
          : {},
        active !== undefined ? { active: active === "true" } : {},
        branchId ? { branchId } : {},
        companyId ? { companyId } : {},
        // If user is not system admin, limit to their company
        req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId
          ? { companyId: req.user.companyId }
          : {},
      ];

      // Filtrar por IDs específicos de localizações
      if (locationIds) {
        // Converter para array se não for
        const idsArray = Array.isArray(locationIds) ? locationIds : [locationIds];
        if (idsArray.length > 0) {
          console.log("Filtrando por IDs de localizações:", idsArray);
          andFilters.push({ id: { in: idsArray } });
        }
      }

      const where = {
        AND: andFilters,
      };

      const [locations, total] = await Promise.all([
        prisma.location.findMany({
          where,
          skip: (Number(page) - 1) * Number(limit),
          take: Number(limit),
          orderBy: { createdAt: 'desc' },
          include: {
            branch: {
              select: {
                id: true,
                name: true,
                code: true
              }
            },
            company: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }),
        prisma.location.count({ where }),
      ]);

      res.json({
        locations,
        total,
        pages: Math.ceil(total / Number(limit)),
      });
    } catch (error) {
      console.error("Erro ao listar localizações:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async get(req, res) {
    try {
      const { id } = req.params;

      const location = await prisma.location.findUnique({
        where: { id },
        include: {
          branch: {
            select: {
              id: true,
              name: true,
              code: true
            }
          },
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      if (!location) {
        return res.status(404).json({ message: "Localização não encontrada" });
      }

      // Check if user has access to this location
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && location.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta localização' });
      }

      res.json(location);
    } catch (error) {
      console.error("Erro ao buscar localização:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      const location = await prisma.location.findUnique({ where: { id } });
      if (!location) {
        return res.status(404).json({ message: "Localização não encontrada" });
      }

      // Check if user has access to this location
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && location.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta localização' });
      }

      const updatedLocation = await prisma.location.update({
        where: { id },
        data: { active: !location.active },
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'TOGGLE_STATUS',
          entityType: 'Location',
          entityId: location.id,
          details: { newStatus: updatedLocation.active },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: location.companyId
        }
      });

      res.json(updatedLocation);
    } catch (error) {
      console.error("Erro ao alterar status da localização:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async delete(req, res) {
    try {
      const { id } = req.params;

      const location = await prisma.location.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              schedulings: {
                where: {
                  status: {
                    notIn: ['CANCELLED', 'COMPLETED']
                  }
                }
              }
            }
          }
        }
      });

      if (!location) {
        return res.status(404).json({ message: "Localização não encontrada" });
      }

      // Check if user has access to this location
      if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId && location.companyId !== req.user.companyId) {
        return res.status(403).json({ message: 'Acesso negado a esta localização' });
      }

      // Check if location has active schedulings
      if (location._count.schedulings > 0) {
        return res.status(400).json({
          message: "Não é possível excluir uma localização com agendamentos ativos."
        });
      }

      await prisma.location.update({
        where: { id },
        data: {
          active: false,
          deletedAt: new Date()
        }
      });

      // Log this action
      await prisma.auditLog.create({
        data: {
          userId: req.user.id,
          action: 'DELETE',
          entityType: 'Location',
          entityId: location.id,
          details: { softDelete: true },
          ipAddress: req.ip || req.connection.remoteAddress,
          userAgent: req.headers['user-agent'],
          companyId: location.companyId
        }
      });

      res.status(204).send();
    } catch (error) {
      console.error("Erro ao deletar localização:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  LocationController,
  createLocationValidation,
};