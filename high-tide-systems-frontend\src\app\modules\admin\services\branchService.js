import { api } from "@/utils/api";
import { Branch } from "../models/branch";

export const branchService = {
  // Listar unidades com suporte a paginação e filtros
  getBranches: async ({ page = 1, limit = 10, search, active, companyId } = {}) => {
    try {
      const params = new URLSearchParams();
      if (page) params.append('page', page);
      if (limit) params.append('limit', limit);
      if (search) params.append('search', search);
      if (active !== undefined) params.append('active', active);
      if (companyId) params.append('companyId', companyId);

      const response = await api.get(`/branches?${params.toString()}`);

      // Transform branches to Branch objects
      if (response.data.branches) {
        response.data.branches = response.data.branches.map(branch => new Branch(branch));
      }

      return response.data;
    } catch (error) {
      console.error("Erro ao buscar unidades:", error);
      throw error;
    }
  },

  // Obter uma unidade específica
  getBranch: async (id) => {
    try {
      const response = await api.get(`/branches/${id}`);
      return new Branch(response.data);
    } catch (error) {
      console.error(`Erro ao buscar unidade ${id}:`, error);
      throw error;
    }
  },

  // Criar uma nova unidade
  createBranch: async (data) => {
    try {
      const response = await api.post('/branches', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar unidade:", error);
      throw error;
    }
  },

  // Atualizar uma unidade existente
  updateBranch: async (id, data) => {
    try {
      const response = await api.put(`/branches/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar unidade ${id}:`, error);
      throw error;
    }
  },

  // Alternar o status de uma unidade (ativo/inativo)
  toggleBranchStatus: async (id) => {
    try {
      const response = await api.patch(`/branches/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao alterar status da unidade ${id}:`, error);
      throw error;
    }
  },

  // Excluir uma unidade
  deleteBranch: async (id) => {
    try {
      await api.delete(`/branches/${id}`);
      return true;
    } catch (error) {
      console.error(`Erro ao excluir unidade ${id}:`, error);
      throw error;
    }
  },

  // Definir uma unidade como matriz/sede principal
  setHeadquarters: async (id) => {
    try {
      const branch = await branchService.getBranch(id);
      const response = await api.put(`/branches/${id}`, {
        ...branch,
        isHeadquarters: true
      });
      return response.data;
    } catch (error) {
      console.error(`Erro ao definir unidade ${id} como matriz:`, error);
      throw error;
    }
  },

  // Obter horários de trabalho padrão de uma unidade
  getDefaultWorkingHours: async (id) => {
    try {
      const response = await api.get(`/branches/${id}/default-working-hours`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar horários de trabalho padrão da unidade ${id}:`, error);
      throw error;
    }
  },

  // Aplicar horários de trabalho padrão a todos os usuários da unidade
  applyWorkingHoursToUsers: async (id) => {
    try {
      const response = await api.post(`/branches/${id}/apply-working-hours`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao aplicar horários de trabalho aos usuários da unidade ${id}:`, error);
      throw error;
    }
  }
};

export default branchService;