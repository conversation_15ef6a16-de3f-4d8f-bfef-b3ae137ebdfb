'use client';

import React, { useState } from 'react';
import DesignSystemExample from '@/components/examples/DesignSystemExample';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Book, Palette } from 'lucide-react';

/**
 * Página de documentação do sistema de design
 */
export default function DesignSystemPage() {
  const [activeTab, setActiveTab] = useState('example');
  
  // Carregar o conteúdo do arquivo markdown
  const [markdownContent, setMarkdownContent] = useState('');
  
  React.useEffect(() => {
    // Carregar o conteúdo do arquivo markdown
    fetch('/docs/DesignSystem.md')
      .then(response => response.text())
      .then(text => {
        setMarkdownContent(text);
      })
      .catch(error => {
        console.error('Erro ao carregar a documentação:', error);
        setMarkdownContent('# Erro ao carregar a documentação\n\nNão foi possível carregar o conteúdo da documentação do sistema de design.');
      });
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="max-w-7xl mx-auto px-4">
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white">
            Sistema de Design High Tide
          </h1>
          <p className="mt-2 text-lg text-gray-600 dark:text-gray-300">
            Documentação e exemplos dos componentes e padrões de design
          </p>
        </div>

        <Tabs defaultValue="example" className="w-full">
          <TabsList className="mb-8">
            <TabsTrigger value="example" className="flex items-center gap-2">
              <Palette size={18} />
              <span>Exemplos Visuais</span>
            </TabsTrigger>
            <TabsTrigger value="docs" className="flex items-center gap-2">
              <Book size={18} />
              <span>Documentação</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="example" className="mt-0">
            <DesignSystemExample />
          </TabsContent>
          
          <TabsContent value="docs" className="mt-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-8">
              <div className="prose dark:prose-invert max-w-none">
                {/* Renderizar o conteúdo markdown */}
                <div dangerouslySetInnerHTML={{ __html: markdownToHtml(markdownContent) }} />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}

/**
 * Função simples para converter markdown em HTML
 * Em um ambiente real, você usaria uma biblioteca como marked ou remark
 */
function markdownToHtml(markdown) {
  if (!markdown) return '';
  
  // Esta é uma implementação muito básica apenas para demonstração
  // Em produção, use uma biblioteca de markdown adequada
  
  let html = markdown
    // Headers
    .replace(/^# (.*$)/gm, '<h1 class="text-3xl font-bold mt-8 mb-4">$1</h1>')
    .replace(/^## (.*$)/gm, '<h2 class="text-2xl font-semibold mt-6 mb-3">$1</h2>')
    .replace(/^### (.*$)/gm, '<h3 class="text-xl font-semibold mt-5 mb-2">$1</h3>')
    .replace(/^#### (.*$)/gm, '<h4 class="text-lg font-medium mt-4 mb-2">$1</h4>')
    
    // Bold
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    
    // Italic
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    
    // Lists
    .replace(/^\d+\. (.*$)/gm, '<li class="ml-6 list-decimal">$1</li>')
    .replace(/^- (.*$)/gm, '<li class="ml-6 list-disc">$1</li>')
    
    // Links
    .replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" class="text-blue-600 dark:text-blue-400 hover:underline">$1</a>')
    
    // Code
    .replace(/`(.*?)`/g, '<code class="bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono">$1</code>')
    
    // Paragraphs
    .replace(/^(?!<[hl]|<li|<code|<table|<pre)(.+$)/gm, '<p class="mb-4">$1</p>')
    
    // Tables
    .replace(/^\|(.*)\|$/gm, function(match, content) {
      const cells = content.split('|').map(cell => cell.trim());
      const isHeader = cells.some(cell => cell.startsWith('---'));
      
      if (isHeader) {
        return ''; // Skip separator row
      }
      
      const cellTag = match.startsWith('|--') ? 'th' : 'td';
      const cellsHtml = cells.map(cell => `<${cellTag} class="border px-4 py-2">${cell}</${cellTag}>`).join('');
      
      return `<tr>${cellsHtml}</tr>`;
    })
    .replace(/(<tr>.*?<\/tr>)+/gs, '<table class="min-w-full border-collapse border border-gray-300 dark:border-gray-700 my-6">$&</table>');
  
  // Fix lists
  html = html.replace(/(<li[^>]*>.*<\/li>\n)+/g, '<ul class="mb-4">$&</ul>');
  
  return html;
}
