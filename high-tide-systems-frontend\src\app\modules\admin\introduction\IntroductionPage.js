"use client";

import React, { useState } from "react";
import {
  Users,
  Briefcase,
  Settings,
  FileText,
  Database,
  LayoutDashboard,
  Info,
  ShieldIcon,
  Building,
  Clock,
  Play,
  Pause,
  ChevronRight,
  ArrowRight,
  BarChart4,
  Pie<PERSON>hart,
  LineChart,
  Activity,
  Plus,
  Lock,
  Mail
} from "lucide-react";
import { ModuleHeader } from "@/components/ui";

const IntroductionPage = () => {
  const [isVideoPlaying, setIsVideoPlaying] = useState(false);

  return (
    <div className="space-y-6">
      {/* Título */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Info size={24} className="mr-2 text-slate-600 dark:text-slate-400" />
          Introdução
        </h1>
      </div>

      {/* Main content */}
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-module-admin-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden">
        {/* Header with gradient */}
        <div className="bg-gradient-to-r from-slate-600 to-slate-400 dark:from-slate-700 dark:to-slate-600 px-6 py-4">
          <div className="flex items-center">
            <ShieldIcon className="mr-3 text-white" size={24} aria-hidden="true" />
            <h2 className="text-xl font-bold text-white">Módulo de Administração</h2>
          </div>
        </div>

        {/* Introduction text and video */}
        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <div>
              <p className="text-gray-700 dark:text-gray-300 mb-6">
                Bem-vindo ao Módulo de Administração do High Tide Systems. Este módulo é o centro de controle do sistema,
                permitindo gerenciar usuários, configurações, profissões, logs e realizar backups.
                Aqui você encontrará todas as ferramentas necessárias para administrar o sistema de forma eficiente.
              </p>
            </div>

            {/* AI-generated video tutorial section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-slate-800 dark:text-white flex items-center">
                    <Play className="mr-2 text-slate-600 dark:text-slate-300" size={18} />
                    Demonstração Interativa
                  </h3>
                  <button
                    onClick={() => setIsVideoPlaying(!isVideoPlaying)}
                    className="text-slate-600 dark:text-slate-300 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                    aria-label={isVideoPlaying ? "Pausar demonstração" : "Iniciar demonstração"}
                  >
                    {isVideoPlaying ? <Pause size={18} /> : <Play size={18} />}
                  </button>
                </div>
              </div>
              <div className="relative aspect-video bg-gradient-to-br from-slate-900 to-slate-800 flex items-center justify-center overflow-hidden">
                {isVideoPlaying ? (
                  <div className="w-full h-full relative">
                    {/* AI-generated video simulation */}
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="absolute top-2 left-2 bg-blue-500/20 px-2 py-1 rounded text-xs text-blue-200 flex items-center">
                        <div className="w-2 h-2 rounded-full bg-red-500 mr-2 animate-pulse"></div>
                        Demonstração IA
                      </div>

                      {/* Animation container */}
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="ai-video-content w-4/5 h-4/5 relative">
                          {/* Admin module overview animation */}
                          <div className="absolute inset-0 flex flex-col items-center justify-center ai-slide" data-slide="1">
                            <div className="text-white text-xl font-bold mb-6">Módulo de Administração</div>
                            <div className="flex space-x-8 mb-8">
                              <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                                <div className="w-16 h-16 rounded-lg bg-blue-500/30 flex items-center justify-center mb-2">
                                  <Users size={32} className="text-blue-300" />
                                </div>
                                <span className="text-blue-200 text-sm">Usuários</span>
                              </div>
                              <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                                <div className="w-16 h-16 rounded-lg bg-green-500/30 flex items-center justify-center mb-2">
                                  <Settings size={32} className="text-green-300" />
                                </div>
                                <span className="text-green-200 text-sm">Configurações</span>
                              </div>
                              <div className="flex flex-col items-center transition-all duration-500 hover:scale-110">
                                <div className="w-16 h-16 rounded-lg bg-purple-500/30 flex items-center justify-center mb-2">
                                  <Database size={32} className="text-purple-300" />
                                </div>
                                <span className="text-purple-200 text-sm">Backup</span>
                              </div>
                            </div>
                          </div>

                          {/* Users management animation */}
                          <div className="absolute inset-0 flex flex-col items-center justify-center ai-slide" data-slide="2">
                            <div className="text-white text-xl font-bold mb-6">Gerenciamento de Usuários</div>
                            <div className="bg-slate-800/80 p-4 rounded-lg w-4/5 mb-4">
                              <div className="flex items-center mb-3 border-b border-slate-700 pb-2">
                                <div className="w-8 h-8 rounded-full bg-blue-500/30 flex items-center justify-center mr-3">
                                  <Users size={16} className="text-blue-300" />
                                </div>
                                <div className="text-blue-200">Crie e gerencie usuários do sistema</div>
                              </div>
                              <div className="grid grid-cols-3 gap-2">
                                <div className="bg-slate-700/50 p-2 rounded flex items-center">
                                  <div className="w-6 h-6 rounded-full bg-slate-600 mr-2"></div>
                                  <div className="h-2 bg-slate-600 rounded w-16"></div>
                                </div>
                                <div className="bg-slate-700/50 p-2 rounded flex items-center">
                                  <div className="w-6 h-6 rounded-full bg-slate-600 mr-2"></div>
                                  <div className="h-2 bg-slate-600 rounded w-12"></div>
                                </div>
                                <div className="bg-slate-700/50 p-2 rounded flex items-center">
                                  <div className="w-6 h-6 rounded-full bg-slate-600 mr-2"></div>
                                  <div className="h-2 bg-slate-600 rounded w-14"></div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {/* Settings animation */}
                          <div className="absolute inset-0 flex flex-col items-center justify-center ai-slide" data-slide="3">
                            <div className="text-white text-xl font-bold mb-6">Configurações do Sistema</div>
                            <div className="flex space-x-4 mb-4">
                              <div className="bg-slate-800/80 p-3 rounded-lg flex flex-col items-center">
                                <Settings size={24} className="text-green-300 mb-2 animate-spin-slow" />
                                <div className="h-2 bg-slate-700 rounded w-20 mb-2"></div>
                                <div className="h-2 bg-slate-700 rounded w-16"></div>
                              </div>
                              <div className="bg-slate-800/80 p-3 rounded-lg flex flex-col items-center">
                                <Lock size={24} className="text-amber-300 mb-2" />
                                <div className="h-2 bg-slate-700 rounded w-20 mb-2"></div>
                                <div className="h-2 bg-slate-700 rounded w-16"></div>
                              </div>
                              <div className="bg-slate-800/80 p-3 rounded-lg flex flex-col items-center">
                                <Mail size={24} className="text-blue-300 mb-2" />
                                <div className="h-2 bg-slate-700 rounded w-20 mb-2"></div>
                                <div className="h-2 bg-slate-700 rounded w-16"></div>
                              </div>
                            </div>
                          </div>

                          {/* Dashboard animation */}
                          <div className="absolute inset-0 flex flex-col items-center justify-center ai-slide" data-slide="4">
                            <div className="text-white text-xl font-bold mb-6">Dashboard Administrativo</div>
                            <div className="bg-slate-800/80 p-4 rounded-lg w-4/5">
                              <div className="grid grid-cols-2 gap-3 mb-3">
                                <div className="bg-slate-700/50 p-2 rounded flex items-center justify-between">
                                  <div className="h-2 bg-slate-600 rounded w-16"></div>
                                  <div className="text-blue-300 text-lg font-bold">24</div>
                                </div>
                                <div className="bg-slate-700/50 p-2 rounded flex items-center justify-between">
                                  <div className="h-2 bg-slate-600 rounded w-16"></div>
                                  <div className="text-green-300 text-lg font-bold">85%</div>
                                </div>
                              </div>
                              <div className="h-24 bg-slate-700/50 rounded mb-3 p-2 flex items-end">
                                <div className="h-1/3 w-1/6 bg-blue-500/50 rounded-sm mx-1"></div>
                                <div className="h-1/2 w-1/6 bg-blue-500/50 rounded-sm mx-1"></div>
                                <div className="h-2/3 w-1/6 bg-blue-500/50 rounded-sm mx-1"></div>
                                <div className="h-3/4 w-1/6 bg-blue-500/50 rounded-sm mx-1"></div>
                                <div className="h-1/2 w-1/6 bg-blue-500/50 rounded-sm mx-1"></div>
                                <div className="h-2/3 w-1/6 bg-blue-500/50 rounded-sm mx-1"></div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>



                    {/* CSS for animations */}
                    <style jsx>{`
                      @keyframes spin-slow {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                      }
                      .animate-spin-slow {
                        animation: spin-slow 10s linear infinite;
                      }

                      .ai-slide {
                        opacity: 0;
                        transform: translateY(20px);
                        transition: opacity 0.5s ease-out, transform 0.5s ease-out;
                      }

                      .ai-slide[data-slide="1"] {
                        opacity: 1;
                        transform: translateY(0);
                        animation: slideChange 20s infinite;
                      }

                      .ai-slide[data-slide="2"] {
                        animation: slideChange2 20s infinite;
                      }

                      .ai-slide[data-slide="3"] {
                        animation: slideChange3 20s infinite;
                      }

                      .ai-slide[data-slide="4"] {
                        animation: slideChange4 20s infinite;
                      }

                      @keyframes slideChange {
                        0%, 20% { opacity: 1; transform: translateY(0); }
                        25%, 95% { opacity: 0; transform: translateY(-20px); }
                        100% { opacity: 1; transform: translateY(0); }
                      }

                      @keyframes slideChange2 {
                        0%, 20% { opacity: 0; transform: translateY(20px); }
                        25%, 45% { opacity: 1; transform: translateY(0); }
                        50%, 100% { opacity: 0; transform: translateY(-20px); }
                      }

                      @keyframes slideChange3 {
                        0%, 45% { opacity: 0; transform: translateY(20px); }
                        50%, 70% { opacity: 1; transform: translateY(0); }
                        75%, 100% { opacity: 0; transform: translateY(-20px); }
                      }

                      @keyframes slideChange4 {
                        0%, 70% { opacity: 0; transform: translateY(20px); }
                        75%, 95% { opacity: 1; transform: translateY(0); }
                        100% { opacity: 0; transform: translateY(-20px); }
                      }
                    `}</style>
                  </div>
                ) : (
                  <div className="text-center p-6">
                    <button
                      onClick={() => setIsVideoPlaying(true)}
                      className="w-20 h-20 rounded-full bg-primary-500/20 flex items-center justify-center mx-auto mb-4 hover:bg-primary-500/30 transition-colors cursor-pointer"
                      aria-label="Iniciar demonstração"
                    >
                      <Play size={36} className="text-primary-500 ml-1" />
                    </button>
                    <p className="text-white text-sm mb-2">Clique para iniciar a demonstração interativa</p>
                    <p className="text-slate-400 text-xs">Visualize as principais funcionalidades do módulo</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Section cards */}
          <h3 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center">
            <Info className="mr-2 text-primary-500" size={20} />
            Seções do Módulo
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
            {/* Users section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Users className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Usuários</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie todos os usuários do sistema, incluindo administradores, funcionários e suas permissões.
                      Você pode criar novos usuários, editar perfis existentes, definir funções e controlar o acesso aos módulos.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Criar, editar e desativar usuários;
                        Gerenciar permissões; Atribuir módulos; Definir funções.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-full">
                      <div className="relative">
                        <div className="w-16 h-16 rounded-full bg-blue-100 dark:bg-blue-800/50 flex items-center justify-center">
                          <Users size={32} className="text-blue-500 dark:text-blue-400" />
                        </div>
                        <div className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full bg-green-100 dark:bg-green-800/50 flex items-center justify-center border-2 border-white dark:border-gray-700">
                          <Plus size={16} className="text-green-500 dark:text-green-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Professions section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Briefcase className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Profissões</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Configure as profissões e grupos profissionais disponíveis no sistema.
                      Estas profissões são utilizadas para categorizar os usuários e definir suas funções dentro da organização.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Criar e gerenciar profissões;
                        Organizar profissões em grupos; Associar profissões a empresas.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="relative">
                      <div className="w-24 h-16 bg-green-100 dark:bg-green-800/30 rounded-lg flex items-center justify-center">
                        <Briefcase size={24} className="text-green-600 dark:text-green-400" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-10 h-10 bg-blue-100 dark:bg-blue-800/30 rounded-lg flex items-center justify-center transform rotate-12">
                        <Building size={18} className="text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="absolute -bottom-2 -left-2 w-10 h-10 bg-amber-100 dark:bg-amber-800/30 rounded-lg flex items-center justify-center transform -rotate-12">
                        <Users size={18} className="text-amber-600 dark:text-amber-400" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Settings section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Settings className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Configurações</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Ajuste as configurações gerais do sistema, incluindo parâmetros de segurança,
                      configurações de e-mail e outras opções que afetam o funcionamento global da aplicação.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Configurações gerais;
                        Configurações de segurança; Configurações de e-mail; Personalização do sistema.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="relative">
                      <div className="w-20 h-20 rounded-full border-4 border-dashed border-purple-300 dark:border-purple-700 flex items-center justify-center animate-spin-slow">
                        <div className="w-14 h-14 rounded-full bg-purple-100 dark:bg-purple-800/40 flex items-center justify-center">
                          <Settings size={24} className="text-purple-600 dark:text-purple-400" />
                        </div>
                      </div>
                      <style jsx>{`
                        @keyframes spin-slow {
                          from { transform: rotate(0deg); }
                          to { transform: rotate(360deg); }
                        }
                        .animate-spin-slow {
                          animation: spin-slow 20s linear infinite;
                        }
                      `}</style>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Logs section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <FileText className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Logs</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Monitore todas as atividades realizadas no sistema. Os logs registram ações dos usuários,
                      alterações em registros e eventos do sistema, permitindo auditoria completa das operações.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Visualizar histórico de atividades;
                        Filtrar logs por tipo, usuário ou data; Exportar logs para análise.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
                      <div className="space-y-2 w-32">
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-full"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-3/4"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-5/6"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-2/3"></div>
                        <div className="h-2 bg-amber-200 dark:bg-amber-700 rounded-full w-full"></div>
                        <div className="absolute right-0 bottom-0">
                          <FileText size={20} className="text-amber-500 dark:text-amber-400" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Backup section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <Database className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Backup</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Gerencie backups do banco de dados e arquivos do sistema. Esta seção permite
                      criar, restaurar e programar backups automáticos para garantir a segurança dos dados.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Criar backups manuais;
                        Configurar backups automáticos; Restaurar dados a partir de backups; Gerenciar histórico de backups.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="relative">
                      <div className="w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
                        <Database size={24} className="text-red-600 dark:text-red-400" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-red-200 dark:bg-red-800/40 rounded-full flex items-center justify-center">
                        <div className="w-6 h-6 bg-red-300 dark:bg-red-700/50 rounded-full flex items-center justify-center">
                          <div className="w-4 h-4 bg-red-400 dark:bg-red-600/60 rounded-full flex items-center justify-center">
                            <div className="w-2 h-2 bg-red-500 dark:bg-red-500/70 rounded-full"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Dashboard section */}
            <div className="bg-slate-50 dark:bg-gray-700 rounded-lg border border-slate-200 dark:border-gray-600 overflow-hidden shadow-md">
              <div className="bg-slate-100 dark:bg-gray-600 px-4 py-3 border-b border-slate-200 dark:border-gray-500">
                <div className="flex items-center">
                  <LayoutDashboard className="mr-2 text-slate-600 dark:text-slate-300" size={20} />
                  <h3 className="font-semibold text-slate-800 dark:text-white">Dashboard</h3>
                </div>
              </div>
              <div className="p-4">
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <p className="text-gray-600 dark:text-gray-300 text-sm">
                      Visualize estatísticas e informações gerais sobre o sistema. O dashboard apresenta
                      gráficos, indicadores e dados relevantes para monitorar o desempenho e uso do sistema.
                    </p>
                    <div className="mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-slate-200 dark:border-gray-600">
                      <p className="text-xs text-slate-500 dark:text-slate-400">
                        <span className="font-semibold">Funcionalidades:</span> Estatísticas de usuários;
                        Atividades recentes; Distribuição de módulos; Informações do sistema.
                      </p>
                    </div>
                  </div>
                  <div className="md:w-1/3 flex items-center justify-center">
                    <div className="grid grid-cols-2 gap-2">
                      <div className="bg-blue-100 dark:bg-blue-900/30 p-2 rounded flex items-center justify-center">
                        <BarChart4 size={20} className="text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="bg-green-100 dark:bg-green-900/30 p-2 rounded flex items-center justify-center">
                        <PieChart size={20} className="text-green-600 dark:text-green-400" />
                      </div>
                      <div className="bg-purple-100 dark:bg-purple-900/30 p-2 rounded flex items-center justify-center">
                        <LineChart size={20} className="text-purple-600 dark:text-purple-400" />
                      </div>
                      <div className="bg-amber-100 dark:bg-amber-900/30 p-2 rounded flex items-center justify-center">
                        <Activity size={20} className="text-amber-600 dark:text-amber-400" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tips section */}
          <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800 overflow-hidden">
            <div className="bg-blue-100 dark:bg-blue-800/30 px-5 py-3 border-b border-blue-200 dark:border-blue-700">
              <div className="flex items-center">
                <Info className="mr-2 text-blue-600 dark:text-blue-400" size={20} />
                <h3 className="font-semibold text-blue-700 dark:text-blue-300">Dicas de Uso</h3>
              </div>
            </div>
            <div className="p-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <ul className="space-y-3 text-sm text-blue-700 dark:text-blue-300">
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Comece configurando as profissões e grupos antes de criar usuários</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Verifique regularmente os logs para monitorar atividades suspeitas</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Configure backups automáticos para garantir a segurança dos dados</span>
                    </li>
                  </ul>
                </div>
                <div>
                  <ul className="space-y-3 text-sm text-blue-700 dark:text-blue-300">
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Utilize o dashboard para obter uma visão geral do sistema</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Atribua permissões com cuidado, seguindo o princípio do menor privilégio</span>
                    </li>
                    <li className="flex items-start">
                      <ChevronRight size={16} className="mt-0.5 mr-2 text-blue-500 dark:text-blue-400 flex-shrink-0" />
                      <span>Mantenha-se atualizado sobre as novas funcionalidades do sistema</span>
                    </li>
                  </ul>
                </div>
              </div>
              <div className="mt-4 bg-white dark:bg-blue-900/40 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                <p className="text-xs text-blue-600 dark:text-blue-300 italic">
                  <span className="font-semibold">Dica Pro:</span> Assista ao vídeo tutorial acima para uma explicação detalhada de como utilizar cada recurso do módulo de administração de forma eficiente.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IntroductionPage;
