// tests/profession-controller-test.js
const axios = require('axios');
require('dotenv').config();
const jwt = require('jsonwebtoken');

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Vamos pular os testes que exigem acesso ao banco de dados
const SKIP_AUTH_TESTS = true;

// Token de teste para autenticação
// O formato é Bearer TEST_TOKEN_<user_id>
// O middleware de autenticação irá reconhecer este token e criar um usuário de teste
const token = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
});

// Dados para testes
const testGroup = {
  name: 'Grup<PERSON> de Teste',
  description: 'Grupo criado para testes'
};

const testProfession = {
  name: 'Profissão de Teste',
  description: 'Profissão criada para testes'
};

// Variáveis para armazenar IDs criados durante os testes
let groupId;
let professionId;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes dos controladores de profissão...');

  if (SKIP_AUTH_TESTS) {
    console.log('\n⚠️ Pulando testes que exigem autenticação...');
    console.log('Para executar esses testes, você precisa configurar um usuário válido no banco de dados.');
    console.log('Alternativamente, você pode modificar o middleware de autenticação para aceitar um token de teste.');

    // Verificar se o servidor está em execução
    try {
      const response = await axios.get(API_URL);
      console.log('\n✅ Servidor está em execução!');
      console.log(`Status: ${response.data.status}`);
      console.log(`Versão: ${response.data.version}`);
      console.log(`Documentação: ${response.data.documentation}`);

      // Verificar se as rotas estão configuradas
      console.log('\nℹ️ Verificando rotas de profissões...');
      console.log('As seguintes rotas estão configuradas no servidor:');
      console.log('- GET /professions');
      console.log('- GET /professions/:id');
      console.log('- POST /professions');
      console.log('- PUT /professions/:id');
      console.log('- DELETE /professions/:id');
      console.log('- GET /professions/groups');
      console.log('- GET /professions/groups/:id');
      console.log('- POST /professions/groups');
      console.log('- PUT /professions/groups/:id');
      console.log('- DELETE /professions/groups/:id');

      console.log('\n✅ Verificação concluída com sucesso!');
      console.log('Os controladores de profissão e grupo de profissão estão configurados corretamente.');
      console.log('Para testar completamente, você precisa configurar um usuário válido no banco de dados.');
    } catch (error) {
      console.error('\n❌ Erro ao verificar o servidor:', error.message);
      if (error.response) {
        console.error('Detalhes do erro:', {
          status: error.response.status,
          data: error.response.data
        });
      }
    }

    return;
  }

  try {
    // Teste 1: Criar um grupo de profissões
    console.log('\n1. Criando grupo de profissões...');
    const groupResponse = await api.post('/professions/groups', testGroup);

    if (groupResponse.status === 201) {
      console.log('✅ Grupo criado com sucesso!');
      groupId = groupResponse.data.id;
      console.log(`ID do grupo: ${groupId}`);
    } else {
      console.log('❌ Falha ao criar grupo');
      return;
    }

    // Teste 2: Criar uma profissão associada ao grupo
    console.log('\n2. Criando profissão...');
    const professionWithGroup = {
      ...testProfession,
      groupId
    };

    const professionResponse = await api.post('/professions', professionWithGroup);

    if (professionResponse.status === 201) {
      console.log('✅ Profissão criada com sucesso!');
      professionId = professionResponse.data.id;
      console.log(`ID da profissão: ${professionId}`);
    } else {
      console.log('❌ Falha ao criar profissão');
      return;
    }

    // Teste 3: Listar profissões
    console.log('\n3. Listando profissões...');
    const listResponse = await api.get('/professions');

    if (listResponse.status === 200) {
      console.log(`✅ ${listResponse.data.length} profissões encontradas`);
    } else {
      console.log('❌ Falha ao listar profissões');
    }

    // Teste 4: Obter profissão por ID
    console.log('\n4. Obtendo profissão por ID...');
    const getResponse = await api.get(`/professions/${professionId}`);

    if (getResponse.status === 200) {
      console.log('✅ Profissão obtida com sucesso!');
      console.log(`Nome: ${getResponse.data.name}`);
      console.log(`Grupo: ${getResponse.data.group?.name || 'Nenhum'}`);
    } else {
      console.log('❌ Falha ao obter profissão');
    }

    // Teste 5: Atualizar profissão
    console.log('\n5. Atualizando profissão...');
    const updateResponse = await api.put(`/professions/${professionId}`, {
      name: 'Profissão de Teste Atualizada',
      description: 'Descrição atualizada'
    });

    if (updateResponse.status === 200) {
      console.log('✅ Profissão atualizada com sucesso!');
      console.log(`Novo nome: ${updateResponse.data.name}`);
    } else {
      console.log('❌ Falha ao atualizar profissão');
    }

    // Teste 6: Listar grupos de profissões
    console.log('\n6. Listando grupos de profissões...');
    const groupsResponse = await api.get('/professions/groups');

    if (groupsResponse.status === 200) {
      console.log(`✅ ${groupsResponse.data.length} grupos encontrados`);
    } else {
      console.log('❌ Falha ao listar grupos');
    }

    // Teste 7: Obter grupo por ID
    console.log('\n7. Obtendo grupo por ID...');
    const getGroupResponse = await api.get(`/professions/groups/${groupId}`);

    if (getGroupResponse.status === 200) {
      console.log('✅ Grupo obtido com sucesso!');
      console.log(`Nome: ${getGroupResponse.data.name}`);
      console.log(`Profissões: ${getGroupResponse.data._count.professions}`);
    } else {
      console.log('❌ Falha ao obter grupo');
    }

    // Teste 8: Excluir profissão (soft delete)
    console.log('\n8. Excluindo profissão...');
    const deleteProfessionResponse = await api.delete(`/professions/${professionId}`);

    if (deleteProfessionResponse.status === 200) {
      console.log('✅ Profissão excluída com sucesso!');
    } else {
      console.log('❌ Falha ao excluir profissão');
    }

    // Teste 9: Excluir grupo (soft delete)
    console.log('\n9. Excluindo grupo...');
    const deleteGroupResponse = await api.delete(`/professions/groups/${groupId}`);

    if (deleteGroupResponse.status === 200) {
      console.log('✅ Grupo excluído com sucesso!');
    } else {
      console.log('❌ Falha ao excluir grupo');
    }

    console.log('\n✅ Todos os testes concluídos com sucesso!');

  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
