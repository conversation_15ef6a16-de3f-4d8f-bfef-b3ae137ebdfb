// src/routes/documentRoutes.js
const express = require("express");
const router = express.Router();
const { DocumentController, uploadDocumentValidation } = require("../../controllers/documentController");
const upload = require("../../middlewares/upload");
const { authenticate } = require('../../middlewares/auth');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rota para upload de múltiplos documentos
router.post(
  "/upload",
  upload.array("documents", 10), // 'documents' é o nome do campo, 10 é o máximo de arquivos
  uploadDocumentValidation,
  DocumentController.upload
);

// Demais rotas permanecem iguais
router.get("/", DocumentController.list);
router.get("/:id", DocumentController.getDocument);
router.delete("/:id", DocumentController.delete);

module.exports = router;