// src/components/dashboard/ClientDashboard.js
'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Calendar, Users, User, Phone, Mail } from 'lucide-react';
import ModuleCard from '@/components/dashboard/ModuleCard';
import { ModuleTable } from '@/components/ui';
import { appointmentService } from '@/app/modules/scheduler/services/appointmentService';
import { personsService } from '@/app/modules/people/services/personsService';
import { formatDate } from '@/utils/dateUtils';
import { formatTime } from '@/utils/dateFormatters';
import WelcomeCard from '@/components/dashboard/WelcomeCard';

const ClientDashboard = () => {
  const { user } = useAuth();
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [persons, setPersons] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadClientData = async () => {
      setIsLoading(true);
      try {
        // Load client's persons
        const personsResponse = await personsService.getPersons({
          clientId: user.id,
          limit: 100 // Get all persons for this client
        });

        // Processar as pessoas para adicionar URLs completas de imagens
        const personsData = personsResponse.persons || personsResponse.people || [];
        const processedPersons = personsData.map(person => {
          // Se a pessoa tem uma imagem de perfil, adicionar a URL completa
          if (person.profileImageUrl && !person.profileImageFullUrl) {
            person.profileImageFullUrl = personsService.getProfileImageUrl(
              person.id,
              person.profileImageUrl
            );
            console.log(`URL completa gerada para ${person.fullName}:`, person.profileImageFullUrl);
          }
          return person;
        });

        setPersons(processedPersons);

        // Load client's appointments
        const appointmentsResponse = await appointmentService.getAppointments({
          clientId: user.id,
          limit: 10,
          status: ['PENDING', 'CONFIRMED'] // Only upcoming appointments
        });
        setAppointments(appointmentsResponse.appointments || []);
      } catch (error) {
        console.error('Error loading client data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    if (user?.id) {
      loadClientData();
    }
  }, [user]);

  const handleModuleClick = (moduleId) => {
    if (moduleId === 'people') {
      router.push('/dashboard/people/persons');
    } else if (moduleId === 'scheduler') {
      router.push('/dashboard/scheduler/calendar');
    }
  };

  // Garantir que o usuário tenha o papel de cliente e o nome correto
  const userWithRole = useMemo(() => {
    // Verificar se o usuário tem pessoas relacionadas e usar o nome da primeira pessoa
    const mainPerson = user?.persons && user.persons.length > 0 ? user.persons[0] : null;

    return {
      ...user,
      role: 'CLIENT',
      // Prioridade: nome da pessoa principal > nome do usuário > login > 'Cliente'
      fullName: mainPerson?.fullName || user?.fullName || user?.login || 'Cliente'
    };
  }, [user]);

  // Log para debug
  console.log('Cliente autenticado:', userWithRole);

  return (
    <div className="space-y-8">
      {/* Cartão de boas-vindas adaptado para clientes */}
      <WelcomeCard user={userWithRole} />

      {/* Client's Modules */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <ModuleCard
          title="Pessoas"
          icon={Users}
          description="Gerencie seus dados pessoais e informações de pessoas relacionadas. Atualize perfis, contatos e documentos importantes."
          onClick={() => handleModuleClick('people')}
          isAccessible={true}
          moduleId="people"
        />
        <ModuleCard
          title="Agendamentos"
          icon={Calendar}
          description="Visualize seus agendamentos, consultas marcadas e histórico de atendimentos. Acompanhe datas, horários e profissionais."
          onClick={() => handleModuleClick('scheduler')}
          isAccessible={true}
          moduleId="scheduler"
        />
      </div>

      {/* Client's Persons */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Users className="mr-2 text-people-500" size={20} />
          Pessoas Relacionadas
        </h2>
        <ModuleTable
          moduleColor="people"
          columns={[
            { header: 'Nome', field: 'fullName', width: '40%' },
            { header: 'Contato', field: 'contact', width: '30%' },
            { header: 'Ações', field: 'actions', width: '30%', sortable: false, className: 'text-right' }
          ]}
          data={persons}
          isLoading={isLoading}
          emptyMessage="Nenhuma pessoa encontrada"
          emptyIcon={<User size={24} />}
          tableId="client-persons-table"
          defaultSortField="fullName"
          defaultSortDirection="asc"
          renderRow={(person, _index, moduleColors, visibleColumns) => (
            <tr key={person.id} className={moduleColors.hoverBg}>
              {visibleColumns.includes('fullName') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                      {person.profileImageUrl ? (
                        <img
                          src={person.profileImageFullUrl || personsService.getProfileImageUrl(person.id, person.profileImageUrl)}
                          alt={person.fullName}
                          className="h-10 w-10 rounded-full object-cover"
                          onError={(e) => {
                            console.error("Erro ao carregar imagem:", e.target.src);
                            e.target.onerror = null;
                            e.target.style.display = 'none';
                            e.target.parentNode.innerHTML = '<div class="flex items-center justify-center w-full h-full"><User className="h-5 w-5 text-gray-500 dark:text-gray-400" /></div>';
                          }}
                        />
                      ) : (
                        <User className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                      )}
                    </div>
                    <div className="ml-4">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {person.fullName}
                      </div>
                      {person.relationship && (
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {person.relationship}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
              )}
              {visibleColumns.includes('contact') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white flex items-center">
                    {person.email && (
                      <div className="flex items-center mr-4">
                        <Mail size={14} className="mr-1 text-gray-500" />
                        <span>{person.email}</span>
                      </div>
                    )}
                    {person.phone && (
                      <div className="flex items-center">
                        <Phone size={14} className="mr-1 text-gray-500" />
                        <span>{person.phone}</span>
                      </div>
                    )}
                  </div>
                </td>
              )}
              {visibleColumns.includes('actions') && (
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex justify-end w-full">
                    <button
                      onClick={() => router.push(`/dashboard/people/persons/${person.id}`)}
                      className={`text-people-600 hover:text-people-900 dark:text-people-400 dark:hover:text-people-300`}
                    >
                      Ver Detalhes
                    </button>
                  </div>
                </td>
              )}
            </tr>
          )}
        />
      </div>

      {/* Client's Appointments */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4 flex items-center">
          <Calendar className="mr-2 text-scheduler-500" size={20} />
          Próximos Agendamentos
        </h2>
        <ModuleTable
          moduleColor="scheduler"
          columns={[
            { header: 'Data', field: 'date', width: '20%' },
            { header: 'Horário', field: 'time', width: '15%' },
            { header: 'Serviço', field: 'service', width: '25%' },
            { header: 'Profissional', field: 'professional', width: '25%' },
            { header: 'Status', field: 'status', width: '15%' }
          ]}
          data={appointments}
          isLoading={isLoading}
          emptyMessage="Nenhum agendamento encontrado"
          emptyIcon={<Calendar size={24} />}
          tableId="client-appointments-table"
          defaultSortField="date"
          defaultSortDirection="asc"
          renderRow={(appointment, _index, moduleColors, visibleColumns) => (
            <tr key={appointment.id} className={moduleColors.hoverBg}>
              {visibleColumns.includes('date') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {formatDate(appointment.startDate)}
                  </div>
                </td>
              )}
              {visibleColumns.includes('time') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {formatTime(appointment.startDate)} - {formatTime(appointment.endDate)}
                  </div>
                </td>
              )}
              {visibleColumns.includes('service') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {appointment.serviceType?.name || appointment.title}
                  </div>
                </td>
              )}
              {visibleColumns.includes('professional') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-white">
                    {appointment.provider?.fullName || 'Não especificado'}
                  </div>
                </td>
              )}
              {visibleColumns.includes('status') && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    ${appointment.status === 'CONFIRMED'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : appointment.status === 'PENDING'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'}`}>
                    {appointment.status === 'CONFIRMED' ? 'Confirmado' :
                     appointment.status === 'PENDING' ? 'Pendente' :
                     appointment.status === 'CANCELLED' ? 'Cancelado' :
                     appointment.status === 'COMPLETED' ? 'Concluído' :
                     appointment.status === 'NO_SHOW' ? 'Não Compareceu' :
                     appointment.status}
                  </span>
                </td>
              )}
            </tr>
          )}
        />
      </div>
    </div>
  );
};

export default ClientDashboard;
