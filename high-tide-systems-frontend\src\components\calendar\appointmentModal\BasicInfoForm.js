import React from 'react';
import { ModuleInput, ModuleTextarea, ModuleFormGroup } from '@/components/ui';
import { FileText, Type } from 'lucide-react';

const BasicInfoForm = ({ formData, setFormData }) => {
  return (
    <div className="space-y-4">
      <ModuleFormGroup
        moduleColor="scheduler"
        label="Título"
        htmlFor="title"
        icon={<Type size={16} />}
      >
        <ModuleInput
          moduleColor="scheduler"
          type="text"
          id="title"
          name="title"
          value={formData.title}
          onChange={(e) =>
            setFormData({ ...formData, title: e.target.value })
          }
          placeholder="Digite o título do agendamento"
          required
          className="text-base py-2"
        />
      </ModuleFormGroup>

      <ModuleFormGroup
        moduleColor="scheduler"
        label="Descrição"
        htmlFor="description"
        icon={<FileText size={16} />}
      >
        <ModuleTextarea
          moduleColor="scheduler"
          id="description"
          name="description"
          value={formData.description}
          onChange={(e) =>
            setFormData({ ...formData, description: e.target.value })
          }
          placeholder="Adicione detalhes sobre o agendamento"
          rows={2}
          className="text-base"
        />
      </ModuleFormGroup>
    </div>
  );
};

export default BasicInfoForm;