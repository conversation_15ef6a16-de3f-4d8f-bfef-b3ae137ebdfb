// components/dashboard/Background/index.js
'use client';

import React from 'react';

export const BackgroundDecoration = () => (
  <div className="absolute top-0 right-0 w-full h-64 overflow-hidden -z-10 opacity-60">
    <div className="absolute top-0 right-0 w-96 h-96 bg-primary-200 rounded-full filter blur-3xl transform translate-x-1/2 -translate-y-1/2 animate-pulse-slow"></div>
    <div className="absolute top-40 right-20 w-64 h-64 bg-purple-200 rounded-full filter blur-3xl"></div>
    <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full filter blur-3xl transform -translate-x-1/2"></div>
    <div className="absolute bottom-0 left-1/2 w-full h-24 bg-gradient-to-t from-gray-100 to-transparent"></div>
  </div>
);

export default BackgroundDecoration;