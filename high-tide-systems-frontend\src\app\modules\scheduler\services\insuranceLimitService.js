// app/modules/scheduler/services/insuranceLimitService.js
import { api } from "@/utils/api";

export const insuranceLimitService = {
  /**
   * Obtém os limites de convênio para uma pessoa
   * @param {string} personId - ID da pessoa
   * @returns {Promise<Array>} - Lista de limites de serviço
   */
  getLimits: async (personId) => {
    try {
      const response = await api.get(`/insurance-service-limits/person/${personId}`);
      return response.data || [];
    } catch (error) {
      console.error("Erro ao buscar limites de convênio:", error);
      return [];
    }
  },

  /**
   * Obtém o limite específico para uma combinação de pessoa, convênio e tipo de serviço
   * @param {Object} params - Parâmetros
   * @param {string} params.personId - ID da pessoa
   * @param {string} params.insuranceId - ID do convênio
   * @param {string} params.serviceTypeId - ID do tipo de serviço
   * @returns {Promise<Object>} - Informações do limite
   */
  getLimitByService: async ({ personId, insuranceId, serviceTypeId }) => {
    try {
      // Obter todos os limites da pessoa primeiro
      const allLimits = await api.get(`/insurance-service-limits/person/${personId}`);

      // Filtrar pelo insuranceId e serviceTypeId
      const specificLimit = allLimits.data.find(
        limit => limit.insuranceId === insuranceId && limit.serviceTypeId === serviceTypeId
      );

      return specificLimit || null;
    } catch (error) {
      console.error("Erro ao buscar limite específico:", error);
      return null;
    }
  },

  /**
   * Verifica a disponibilidade de um agendamento com relação aos limites de convênio
   * @param {Object} params - Parâmetros
   * @param {string} params.personId - ID da pessoa
   * @param {string} params.insuranceId - ID do convênio
   * @param {string} params.serviceTypeId - ID do tipo de serviço
   * @param {Date} [params.appointmentDate] - Data do agendamento para verificar o limite
   * @returns {Promise<Object>} - Resultado da verificação com informações de uso
   */
  checkLimitAvailability: async ({ personId, insuranceId, serviceTypeId, appointmentDate }) => {
    try {
      if (!personId || !insuranceId || !serviceTypeId) {
        return { available: true, message: "Dados insuficientes para verificar limites" };
      }

      // Usar a data do agendamento para verificação, se disponível
      const dateToCheck = appointmentDate || new Date();

      const response = await api.post(`/insurance-service-limits/check`, {
        personId,
        insuranceId,
        serviceTypeId,
        date: dateToCheck.toISOString() // Data para verificação
      });

      // Adaptar o formato da resposta para o formato esperado pelo frontend
      const data = response.data;

      return {
        available: data.allowed,
        message: data.message,
        usage: {
          monthly: {
            used: data.monthlyUsed,
            limit: data.monthlyLimit,
            remaining: data.monthlyRemaining,
            unlimited: data.monthlyLimit === 0
          }
        }
      };
    } catch (error) {
      console.error("Erro ao verificar disponibilidade de limites:", error);
      return { available: true, message: "Erro ao verificar limites do convênio" };
    }
  }
};

export default insuranceLimitService;