/**
 * Script para copiar a documentação do sistema de design para a pasta pública
 */
const fs = require('fs');
const path = require('path');

// Caminhos
const docsDir = path.join(__dirname, '..', 'src', 'docs');
const publicDocsDir = path.join(__dirname, '..', 'public', 'docs');

// Criar pasta de destino se não existir
if (!fs.existsSync(publicDocsDir)) {
  fs.mkdirSync(publicDocsDir, { recursive: true });
  console.log(`Pasta criada: ${publicDocsDir}`);
}

// Copiar arquivos
try {
  const files = fs.readdirSync(docsDir);
  
  files.forEach(file => {
    const sourcePath = path.join(docsDir, file);
    const destPath = path.join(publicDocsDir, file);
    
    // Verificar se é um arquivo
    if (fs.statSync(sourcePath).isFile()) {
      fs.copyFileSync(sourcePath, destPath);
      console.log(`Arquivo copiado: ${file}`);
    }
  });
  
  console.log('Documentação copiada com sucesso!');
} catch (error) {
  console.error('Erro ao copiar documentação:', error);
}
