-- CreateEnum
CREATE TYPE "ProgramType" AS ENUM ('PROGRAM_CATALOG', 'LEARNING_PROGRAM');

-- CreateTable
CREATE TABLE "Program" (
    "id" TEXT NOT NULL,
    "type" "ProgramType" NOT NULL,
    "name" TEXT NOT NULL,
    "protocol" TEXT,
    "skill" TEXT,
    "milestone" TEXT,
    "teachingType" TEXT,
    "targetsPerSession" INTEGER DEFAULT 1,
    "attemptsPerTarget" INTEGER DEFAULT 1,
    "teachingProcedure" TEXT DEFAULT '',
    "instruction" TEXT DEFAULT '',
    "objective" TEXT DEFAULT '',
    "correctionProcedure" TEXT DEFAULT '',
    "learningCriteria" TEXT DEFAULT '',
    "materials" TEXT DEFAULT '',
    "notes" TEXT DEFAULT '',
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "deletedAt" TIMESTAMP(3),
    "companyId" TEXT NOT NULL,
    "createdById" TEXT NOT NULL,

    CONSTRAINT "Program_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ProgramTarget" (
    "id" TEXT NOT NULL,
    "target" TEXT NOT NULL,
    "order" INTEGER NOT NULL,
    "group" TEXT,
    "situation" TEXT DEFAULT 'ACTIVE',
    "startDate" TIMESTAMP(3),
    "acquisitionDate" TIMESTAMP(3),
    "maintenanceCount" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "programId" TEXT NOT NULL,

    CONSTRAINT "ProgramTarget_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "Program_companyId_idx" ON "Program"("companyId");

-- CreateIndex
CREATE INDEX "Program_active_idx" ON "Program"("active");

-- CreateIndex
CREATE INDEX "Program_type_idx" ON "Program"("type");

-- CreateIndex
CREATE INDEX "Program_createdById_idx" ON "Program"("createdById");

-- CreateIndex
CREATE INDEX "ProgramTarget_programId_idx" ON "ProgramTarget"("programId");

-- CreateIndex
CREATE INDEX "ProgramTarget_order_idx" ON "ProgramTarget"("order");

-- AddForeignKey
ALTER TABLE "Program" ADD CONSTRAINT "Program_companyId_fkey" FOREIGN KEY ("companyId") REFERENCES "Company"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Program" ADD CONSTRAINT "Program_createdById_fkey" FOREIGN KEY ("createdById") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ProgramTarget" ADD CONSTRAINT "ProgramTarget_programId_fkey" FOREIGN KEY ("programId") REFERENCES "Program"("id") ON DELETE CASCADE ON UPDATE CASCADE;
