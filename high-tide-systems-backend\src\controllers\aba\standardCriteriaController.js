// src/controllers/aba/standardCriteriaController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createStandardCriteriaValidation = [
  body("teachingType")
    .isIn(["DISCRETE_TRIAL_STRUCTURED", "TASK_ANALYSIS", "NATURALISTIC_TEACHING", "DISCRETE_TRIAL_INTERSPERSED"])
    .withMessage("Tipo de ensino inválido"),
  body("acronym")
    .notEmpty()
    .withMessage("Sigla é obrigatória"),
  body("degree")
    .isIn(["OMISSION", "ERROR", "MORE_INTRUSIVE", "PARTIALLY_INTRUSIVE", "LESS_INTRUSIVE", "INDEPENDENT"])
    .withMessage("Grau inválido"),
];

const updateStandardCriteriaValidation = [
  body("teachingType")
    .optional()
    .isIn(["DISCRETE_TRIAL_STRUCTURED", "TASK_ANALYSIS", "NATURALISTIC_TEACHING", "DISCRETE_TRIAL_INTERSPERSED"])
    .withMessage("Tipo de ensino inválido"),
  body("acronym")
    .optional()
    .notEmpty()
    .withMessage("Sigla é obrigatória"),
  body("degree")
    .optional()
    .isIn(["OMISSION", "ERROR", "MORE_INTRUSIVE", "PARTIALLY_INTRUSIVE", "LESS_INTRUSIVE", "INDEPENDENT"])
    .withMessage("Grau inválido"),
];

class StandardCriteriaController {
  /**
   * Cria um novo critério padrão
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { teachingType, acronym, degree } = req.body;

      // Criar critério padrão
      const standardCriteria = await prisma.standardCriteria.create({
        data: {
          teachingType,
          acronym,
          degree,
          companyId: req.user.companyId,
        },
      });

      res.status(201).json(standardCriteria);
    } catch (error) {
      console.error("Erro ao criar critério padrão:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todos os critérios padrão com paginação e filtros
   */
  static async list(req, res) {
    try {
      const { page = 1, limit = 10, search = "", active } = req.query;

      // Construir filtro de busca
      const where = {
        companyId: req.user.companyId,
      };

      // Filtro de status ativo/inativo
      if (active !== undefined) {
        where.active = active === 'true';
      }

      // Filtro de busca
      if (search) {
        where = {
          ...where,
          acronym: { contains: search, mode: "insensitive" }
        };
      }

      // Contar total de registros
      const total = await prisma.standardCriteria.count({ where });

      // Buscar critérios padrão com paginação
      const standardCriteria = await prisma.standardCriteria.findMany({
        where,
        orderBy: {
          createdAt: "desc",
        },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar resposta
      const response = formatSuccessResponse(
        standardCriteria,
        "standardCriteria",
        total,
        Math.ceil(total / Number(limit))
      );

      res.json(response);
    } catch (error) {
      console.error("Erro ao listar critérios padrão:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém um critério padrão específico pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const standardCriteria = await prisma.standardCriteria.findUnique({
        where: { id },
      });

      if (!standardCriteria) {
        return res.status(404).json({ message: "Critério padrão não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && standardCriteria.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para visualizar este critério padrão" });
      }

      res.json(standardCriteria);
    } catch (error) {
      console.error("Erro ao buscar critério padrão:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza um critério padrão existente
   */
  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { teachingType, acronym, degree } = req.body;

      // Verificar se o critério padrão existe
      const existingCriteria = await prisma.standardCriteria.findUnique({
        where: { id },
      });

      if (!existingCriteria) {
        return res.status(404).json({ message: "Critério padrão não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingCriteria.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para editar este critério padrão" });
      }

      // Atualizar critério padrão
      const updatedCriteria = await prisma.standardCriteria.update({
        where: { id },
        data: {
          teachingType: teachingType || existingCriteria.teachingType,
          acronym: acronym || existingCriteria.acronym,
          degree: degree || existingCriteria.degree,
        },
      });

      res.json(updatedCriteria);
    } catch (error) {
      console.error("Erro ao atualizar critério padrão:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status ativo/inativo de um critério padrão
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o critério padrão existe
      const existingCriteria = await prisma.standardCriteria.findUnique({
        where: { id },
      });

      if (!existingCriteria) {
        return res.status(404).json({ message: "Critério padrão não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingCriteria.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para alterar este critério padrão" });
      }

      // Alternar status
      const updatedCriteria = await prisma.standardCriteria.update({
        where: { id },
        data: {
          active: !existingCriteria.active,
        },
      });

      res.json(updatedCriteria);
    } catch (error) {
      console.error("Erro ao alternar status do critério padrão:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Exclui um critério padrão
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o critério padrão existe
      const existingCriteria = await prisma.standardCriteria.findUnique({
        where: { id },
      });

      if (!existingCriteria) {
        return res.status(404).json({ message: "Critério padrão não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingCriteria.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para excluir este critério padrão" });
      }

      // Exclusão lógica (soft delete)
      await prisma.standardCriteria.update({
        where: { id },
        data: {
          active: false,
          deletedAt: new Date(),
        },
      });

      res.json({ message: "Critério padrão excluído com sucesso" });
    } catch (error) {
      console.error("Erro ao excluir critério padrão:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  StandardCriteriaController,
  createStandardCriteriaValidation,
  updateStandardCriteriaValidation,
};
