'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useQuickNav } from '@/contexts/QuickNavContext';
import {
  Search,
  X,
  Clock,
  Calendar,
  Users,
  Settings,
  FileText,
  Home,
  ChevronRight,
  Star,
  Zap,
  Bookmark,
  Palette,
  Info,
  BarChart,
  LayoutDashboard,
  UserPlus,
  CreditCard,
  Shield
} from 'lucide-react';
import { createPortal } from 'react-dom';
import { usePermissions } from '@/hooks/usePermissions';

// Mapeamento de módulos e suas rotas
const navigationItems = [
  {
    category: 'Módulos',
    items: [
      {
        title: 'Dashboard',
        description: 'Visão geral do sistema',
        path: '/dashboard',
        icon: <Home size={18} />,
        permission: null // Todos têm acesso
      },
      {
        title: 'Agendamento',
        description: 'Gerenciar calendário e agendamentos',
        path: '/dashboard/scheduler/calendar',
        icon: <Calendar size={18} />,
        permission: 'scheduling.calendar.view'
      },
      {
        title: 'Pessoas',
        description: 'Gerenciar pacientes e contatos',
        path: '/dashboard/people/persons',
        icon: <Users size={18} />,
        permission: 'people.persons.view'
      },
      {
        title: 'Administração',
        description: 'Configurações do sistema',
        path: '/dashboard/admin/introduction',
        icon: <Settings size={18} />,
        permission: 'admin.dashboard.view'
      }
    ]
  },
  {
    category: 'Agendamento',
    items: [
      {
        title: 'Introdução',
        description: 'Visão geral do módulo de agendamento',
        path: '/dashboard/scheduler/introduction',
        icon: <Info size={18} />,
        permission: 'scheduler.dashboard.view'
      },
      {
        title: 'Calendário',
        description: 'Visualizar e gerenciar agendamentos',
        path: '/dashboard/scheduler/calendar',
        icon: <Calendar size={18} />,
        permission: 'scheduling.calendar.view'
      },
      {
        title: 'Relatório',
        description: 'Relatórios e estatísticas de agendamentos',
        path: '/dashboard/scheduler/appointments-report',
        icon: <FileText size={18} />,
        permission: 'scheduling.appointments-report.view'
      },
      {
        title: 'Horários de Trabalho',
        description: 'Configurar horários de atendimento',
        path: '/dashboard/scheduler/working-hours',
        icon: <Clock size={18} />,
        permission: 'scheduling.working-hours.view'
      },
      {
        title: 'Tipos de Serviço',
        description: 'Gerenciar tipos de serviço disponíveis',
        path: '/dashboard/scheduler/service-types',
        icon: <FileText size={18} />,
        permission: 'scheduling.service-types.view'
      },
      {
        title: 'Locais',
        description: 'Gerenciar locais de atendimento',
        path: '/dashboard/scheduler/locations',
        icon: <FileText size={18} />,
        permission: 'scheduling.locations.view'
      },
      {
        title: 'Dashboard',
        description: 'Análise de agendamentos e estatísticas',
        path: '/dashboard/scheduler/appointments-dashboard',
        icon: <LayoutDashboard size={18} />,
        permission: 'scheduling.appointments-dashboard.view'
      }
    ]
  },
  {
    category: 'Pessoas',
    items: [
      {
        title: 'Introdução',
        description: 'Visão geral do módulo de pessoas',
        path: '/dashboard/people/introduction',
        icon: <Info size={18} />,
        permission: 'people.dashboard.view'
      },
      {
        title: 'Clientes',
        description: 'Gerenciar clientes e contas',
        path: '/dashboard/people/clients',
        icon: <UserPlus size={18} />,
        permission: 'people.clients.view'
      },
      {
        title: 'Pacientes',
        description: 'Gerenciar cadastro de pacientes',
        path: '/dashboard/people/persons',
        icon: <Users size={18} />,
        permission: 'people.persons.view'
      },
      {
        title: 'Convênios',
        description: 'Gerenciar convênios e planos',
        path: '/dashboard/people/insurances',
        icon: <CreditCard size={18} />,
        permission: 'people.insurances.view'
      },
      {
        title: 'Limites de Convênio',
        description: 'Configurar limites de atendimento por convênio',
        path: '/dashboard/people/insurance-limits',
        icon: <Shield size={18} />,
        permission: 'people.insurance-limits.view'
      },
      {
        title: 'Dashboard',
        description: 'Análise e estatísticas de pessoas',
        path: '/dashboard/people/dashboard',
        icon: <LayoutDashboard size={18} />,
        permission: 'people.dashboard.view'
      }
    ]
  },
  {
    category: 'Administração',
    items: [
      {
        title: 'Usuários',
        description: 'Gerenciar usuários do sistema',
        path: '/dashboard/admin/users',
        icon: <Users size={18} />,
        permission: 'admin.users.view'
      },
      {
        title: 'Profissões',
        description: 'Gerenciar profissões e grupos',
        path: '/dashboard/admin/professions',
        icon: <FileText size={18} />,
        permission: 'admin.professions.view'
      },
      {
        title: 'Configurações',
        description: 'Configurações gerais do sistema',
        path: '/dashboard/admin/settings',
        icon: <Settings size={18} />,
        permission: 'admin.config.edit'
      }
    ]
  },
  {
    category: 'Ações Rápidas',
    items: [
      {
        title: 'Novo Agendamento',
        description: 'Criar um novo agendamento',
        path: '/dashboard/scheduler/calendar?action=new',
        icon: <Zap size={18} />,
        permission: 'scheduling.calendar.edit'
      },
      {
        title: 'Novo Paciente',
        description: 'Cadastrar um novo paciente',
        path: '/dashboard/people/persons?action=new',
        icon: <Zap size={18} />,
        permission: 'people.persons.edit'
      }
    ]
  },
  {
    category: 'Desenvolvimento',
    items: [
      {
        title: 'Sistema de Design',
        description: 'Documentação e exemplos de componentes UI',
        path: '/design-system',
        icon: <Palette size={18} />,
        permission: null // Todos têm acesso
      }
    ]
  }
];

const QuickNav = () => {
  const { isOpen, closeQuickNav, searchTerm, setSearchTerm, navigationHistory, addToHistory } = useQuickNav();
  const [filteredItems, setFilteredItems] = useState([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const searchInputRef = useRef(null);
  const router = useRouter();
  const { can } = usePermissions();

  // Filtrar itens com base no termo de pesquisa
  useEffect(() => {
    if (!isOpen) return;

    // Filtrar itens com base no termo de pesquisa e permissões
    const filtered = [];

    // Adicionar histórico de navegação se houver termo de pesquisa
    if (navigationHistory.length > 0) {
      const historyItems = navigationHistory.filter(item =>
        item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );

      if (historyItems.length > 0) {
        filtered.push({
          category: 'Recentes',
          items: historyItems
        });
      }
    }

    // Filtrar itens de navegação
    navigationItems.forEach(category => {
      const items = category.items.filter(item =>
        // Verificar permissão
        (item.permission === null || can(item.permission)) &&
        // Verificar termo de pesquisa
        (searchTerm === '' ||
          item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase())))
      );

      if (items.length > 0) {
        filtered.push({
          category: category.category,
          items: items
        });
      }
    });

    setFilteredItems(filtered);
    setSelectedIndex(0);
  }, [isOpen, searchTerm, navigationHistory, can]);

  // Focar no input de pesquisa quando o QuickNav é aberto
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  }, [isOpen]);

  // Lidar com navegação por teclado
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e) => {
      // Calcular o número total de itens
      const totalItems = filteredItems.reduce((acc, category) => acc + category.items.length, 0);

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => (prev + 1) % totalItems);
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => (prev - 1 + totalItems) % totalItems);
          break;
        case 'Enter':
          e.preventDefault();
          handleSelectItem();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen, filteredItems, selectedIndex]);

  // Função para selecionar um item
  const handleSelectItem = () => {
    // Encontrar o item selecionado
    let currentIndex = 0;
    let selectedItem = null;

    for (const category of filteredItems) {
      for (const item of category.items) {
        if (currentIndex === selectedIndex) {
          selectedItem = item;
          break;
        }
        currentIndex++;
      }
      if (selectedItem) break;
    }

    if (selectedItem) {
      // Adicionar ao histórico
      addToHistory(selectedItem);

      // Navegar para a rota
      router.push(selectedItem.path);

      // Fechar o QuickNav
      closeQuickNav();
    }
  };

  // Função para lidar com clique em um item
  const handleItemClick = (item) => {
    // Adicionar ao histórico
    addToHistory(item);

    // Navegar para a rota
    router.push(item.path);

    // Fechar o QuickNav
    closeQuickNav();
  };

  // Não renderizar nada se o QuickNav não estiver aberto
  if (!isOpen) return null;

  // Renderizar o QuickNav usando um portal
  return createPortal(
    <div className="fixed inset-0 z-[9999] flex items-start justify-center pt-[15vh] bg-black/50 backdrop-blur-sm">
      <div
        className="w-full max-w-2xl bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Cabeçalho com input de pesquisa */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center gap-3">
          <div className="flex items-center gap-2 text-gray-400 dark:text-gray-500 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs">
            <span>Ctrl + K</span>
          </div>

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" size={18} />
            <input
              ref={searchInputRef}
              type="text"
              placeholder="Pesquisar..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-transparent border-none focus:outline-none text-gray-800 dark:text-gray-200"
            />
          </div>

          <button
            onClick={closeQuickNav}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <X size={20} />
          </button>
        </div>

        {/* Conteúdo */}
        <div className="max-h-[60vh] overflow-y-auto">
          {filteredItems.length === 0 ? (
            <div className="p-6 text-center text-gray-500 dark:text-gray-400">
              <p>Nenhum resultado encontrado</p>
            </div>
          ) : (
            <div className="p-2">
              {filteredItems.map((category, categoryIndex) => (
                <div key={categoryIndex} className="mb-4">
                  <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 px-3 py-2">
                    {category.category}
                  </h3>

                  <div className="space-y-1">
                    {category.items.map((item, itemIndex) => {
                      // Calcular o índice global do item
                      let globalIndex = 0;
                      for (let i = 0; i < categoryIndex; i++) {
                        globalIndex += filteredItems[i].items.length;
                      }
                      globalIndex += itemIndex;

                      // Verificar se este é o item selecionado
                      const isSelected = globalIndex === selectedIndex;

                      // Determinar a cor do ícone com base na categoria
                      let iconColorClass = 'text-gray-400';
                      if (category.category === 'Agendamento') {
                        iconColorClass = 'text-purple-500 dark:text-purple-400';
                      } else if (category.category === 'Pessoas') {
                        iconColorClass = 'text-orange-500 dark:text-orange-400';
                      } else if (category.category === 'Administração') {
                        iconColorClass = 'text-red-500 dark:text-red-400';
                      } else if (category.category === 'Ações Rápidas') {
                        iconColorClass = 'text-blue-500 dark:text-blue-400';
                      } else if (category.category === 'Recentes') {
                        iconColorClass = 'text-gray-400 dark:text-gray-500';
                      }

                      return (
                        <div
                          key={`${item.path}-${itemIndex}`}
                          className={`px-3 py-2 rounded-lg flex items-center gap-3 cursor-pointer transition-colors ${
                            isSelected
                              ? 'bg-gray-100 dark:bg-gray-700'
                              : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
                          }`}
                          onClick={() => handleItemClick(item)}
                        >
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${iconColorClass} bg-gray-100 dark:bg-gray-700`}>
                            {category.category === 'Recentes' ? (
                              <Clock size={16} />
                            ) : (
                              item.icon || <ChevronRight size={16} />
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="font-medium text-gray-800 dark:text-gray-200 truncate">
                              {item.title}
                            </div>
                            {item.description && (
                              <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
                                {item.description}
                              </div>
                            )}
                          </div>

                          <div className="text-xs text-gray-400 dark:text-gray-500">
                            <ChevronRight size={16} />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Rodapé com dicas */}
        <div className="p-3 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 text-xs text-gray-500 dark:text-gray-400 flex justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <span className="px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600">↑</span>
              <span className="px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600">↓</span>
              <span className="ml-1">navegar</span>
            </div>

            <div className="flex items-center gap-1">
              <span className="px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600">Enter</span>
              <span className="ml-1">selecionar</span>
            </div>

            <div className="flex items-center gap-1">
              <span className="px-1.5 py-0.5 rounded border border-gray-300 dark:border-gray-600">Esc</span>
              <span className="ml-1">fechar</span>
            </div>
          </div>

          <div>
            Pressione <span className="font-medium">Ctrl+K</span> ou <span className="font-medium">/</span> para abrir
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default QuickNav;
