// src/routes/aba/levelRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { LevelController, createLevelValidation, updateLevelValidation } = require('../../controllers/aba/levelController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para níveis
router.post('/', createLevelValidation, LevelController.create);
router.get('/', LevelController.list);
router.get('/:id', LevelController.get);
router.put('/:id', updateLevelValidation, LevelController.update);
router.delete('/:id', LevelController.delete);

module.exports = router;
