"use client";

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { getModuleRedirectionPath } from '@/utils/moduleRedirection';

export default function FinancialModuleRoute() {
  const router = useRouter();

  useEffect(() => {
    // Obtém o caminho de redirecionamento com base nas preferências do usuário
    const redirectPath = getModuleRedirectionPath(
      'financial',
      '/dashboard/financial/invoices', // Não há página de introdução ainda, então usamos a página padrão
      '/dashboard/financial/invoices'
    );

    // Redireciona para o caminho determinado
    router.push(redirectPath);
  }, [router]);

  return <div>Carregando...</div>;
}