{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/utils/moduleRedirection.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { api } from './api';\r\n\r\n/**\r\n * Utilitário para gerenciar redirecionamentos de módulos com base nas preferências do usuário\r\n * e se é a primeira visita ao módulo.\r\n */\r\n\r\n/**\r\n * Verifica se é a primeira visita do usuário ao módulo\r\n * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')\r\n * @returns {boolean} - Retorna true se for a primeira visita, false caso contrário\r\n */\r\nexport const isFirstVisit = (moduleId) => {\r\n  if (typeof window === 'undefined') return true;\r\n\r\n  try {\r\n    const visitedModules = JSON.parse(localStorage.getItem('visitedModules') || '{}');\r\n    return !visitedModules[moduleId];\r\n  } catch (error) {\r\n    console.error('Erro ao verificar primeira visita:', error);\r\n    return true;\r\n  }\r\n};\r\n\r\n/**\r\n * Marca um módulo como visitado\r\n * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')\r\n */\r\nexport const markModuleAsVisited = (moduleId) => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  try {\r\n    const visitedModules = JSON.parse(localStorage.getItem('visitedModules') || '{}');\r\n    visitedModules[moduleId] = true;\r\n    localStorage.setItem('visitedModules', JSON.stringify(visitedModules));\r\n  } catch (error) {\r\n    console.error('Erro ao marcar módulo como visitado:', error);\r\n  }\r\n};\r\n\r\n/**\r\n * Obtém a página inicial preferida do usuário para um módulo específico\r\n * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')\r\n * @returns {string} - Caminho da página inicial preferida\r\n */\r\nexport const getPreferredLandingPage = (moduleId) => {\r\n  if (typeof window === 'undefined') return null;\r\n\r\n  try {\r\n    // Primeiro tenta obter do localStorage para evitar chamadas API desnecessárias\r\n    const localPreferences = JSON.parse(localStorage.getItem('modulePreferences') || '{}');\r\n    return localPreferences[moduleId] || null;\r\n  } catch (error) {\r\n    console.error('Erro ao obter página inicial preferida do localStorage:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Define a página inicial preferida do usuário para um módulo específico\r\n * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')\r\n * @param {string} pagePath - Caminho da página inicial preferida\r\n * @returns {Promise} - Promise que resolve quando a operação é concluída\r\n */\r\nexport const setPreferredLandingPage = async (moduleId, pagePath) => {\r\n  if (typeof window === 'undefined') return;\r\n\r\n  try {\r\n    // Primeiro, atualiza o localStorage para feedback imediato\r\n    const localPreferences = JSON.parse(localStorage.getItem('modulePreferences') || '{}');\r\n    localPreferences[moduleId] = pagePath;\r\n    localStorage.setItem('modulePreferences', JSON.stringify(localPreferences));\r\n\r\n    // Depois, atualiza no backend\r\n    try {\r\n      // Obter as preferências atuais do backend\r\n      const response = await api.get('/module-preferences');\r\n\r\n      // Verificar o formato da resposta\r\n      let serverPreferences;\r\n      if (response.data && response.data.success && response.data.data) {\r\n        // Formato com wrapper de sucesso\r\n        serverPreferences = response.data.data;\r\n      } else {\r\n        // Formato direto\r\n        serverPreferences = response.data || {};\r\n      }\r\n\r\n      console.log('Preferências extraídas da resposta (moduleRedirection):', serverPreferences);\r\n\r\n      // Atualizar a preferência do módulo\r\n      serverPreferences[moduleId] = pagePath;\r\n\r\n      // Enviar as preferências atualizadas para o backend\r\n      await api.put('/module-preferences', {\r\n        modulePreferences: serverPreferences\r\n      });\r\n\r\n      console.log(`Preferência de módulo ${moduleId} atualizada com sucesso no backend`);\r\n    } catch (apiError) {\r\n      console.error('Erro ao atualizar preferências no backend:', apiError);\r\n      // Não falhar a operação se o backend não estiver disponível\r\n    }\r\n  } catch (error) {\r\n    console.error('Erro ao definir página inicial preferida:', error);\r\n  }\r\n};\r\n\r\n/**\r\n * Obtém o caminho de redirecionamento para um módulo com base nas preferências do usuário\r\n * e se é a primeira visita\r\n * @param {string} moduleId - ID do módulo (ex: 'admin', 'scheduler')\r\n * @param {string} introductionPath - Caminho da página de introdução\r\n * @param {string} defaultPath - Caminho padrão para redirecionamento\r\n * @returns {string} - Caminho para redirecionamento\r\n */\r\nexport const getModuleRedirectionPath = (moduleId, introductionPath, defaultPath) => {\r\n  // Se for a primeira visita, redireciona para a introdução\r\n  if (isFirstVisit(moduleId)) {\r\n    markModuleAsVisited(moduleId);\r\n    return introductionPath;\r\n  }\r\n\r\n  // Verifica se há uma página inicial preferida\r\n  const preferredPage = getPreferredLandingPage(moduleId);\r\n  if (preferredPage) {\r\n    return preferredPage;\r\n  }\r\n\r\n  // Caso contrário, usa o caminho padrão\r\n  return defaultPath;\r\n};\r\n\r\n/**\r\n * Obtém as opções de páginas disponíveis para um módulo\r\n * @param {string} moduleId - ID do módulo\r\n * @returns {Array} - Array de objetos com as opções de páginas\r\n */\r\nexport const getModulePageOptions = (moduleId) => {\r\n  switch (moduleId) {\r\n    case 'admin':\r\n      return [\r\n        { value: '/dashboard/admin/introduction', label: 'Introdução' },\r\n        { value: '/dashboard/admin/dashboard', label: 'Dashboard' },\r\n        { value: '/dashboard/admin/users', label: 'Usuários' },\r\n        { value: '/dashboard/admin/professions', label: 'Profissões' },\r\n        { value: '/dashboard/admin/logs', label: 'Logs' },\r\n        { value: '/dashboard/admin/settings', label: 'Configurações' }\r\n      ];\r\n    case 'scheduler':\r\n      return [\r\n        { value: '/dashboard/scheduler/introduction', label: 'Introdução' },\r\n        { value: '/dashboard/scheduler/calendar', label: 'Calendário' },\r\n        { value: '/dashboard/scheduler/working-hours', label: 'Horários de Trabalho' },\r\n        { value: '/dashboard/scheduler/service-types', label: 'Tipos de Serviço' },\r\n        { value: '/dashboard/scheduler/locations', label: 'Locais' },\r\n        { value: '/dashboard/scheduler/appointments-report', label: 'Relatório' }\r\n      ];\r\n    case 'people':\r\n      return [\r\n        { value: '/dashboard/people/clients', label: 'Clientes' },\r\n        { value: '/dashboard/people/persons', label: 'Pessoas' }\r\n      ];\r\n    case 'financial':\r\n      return [\r\n        { value: '/dashboard/financial/invoices', label: 'Faturas' },\r\n        { value: '/dashboard/financial/payments', label: 'Pagamentos' }\r\n      ];\r\n    default:\r\n      return [];\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAFA;;AAcO,MAAM,eAAe,CAAC;IAC3B,wCAAmC,OAAO;;AAS5C;AAMO,MAAM,sBAAsB,CAAC;IAClC,wCAAmC;;AASrC;AAOO,MAAM,0BAA0B,CAAC;IACtC,wCAAmC,OAAO;;AAU5C;AAQO,MAAM,0BAA0B,OAAO,UAAU;IACtD,wCAAmC;;AAyCrC;AAUO,MAAM,2BAA2B,CAAC,UAAU,kBAAkB;IACnE,0DAA0D;IAC1D,IAAI,aAAa,WAAW;QAC1B,oBAAoB;QACpB,OAAO;IACT;IAEA,8CAA8C;IAC9C,MAAM,gBAAgB,wBAAwB;IAC9C,IAAI,eAAe;QACjB,OAAO;IACT;IAEA,uCAAuC;IACvC,OAAO;AACT;AAOO,MAAM,uBAAuB,CAAC;IACnC,OAAQ;QACN,KAAK;YACH,OAAO;gBACL;oBAAE,OAAO;oBAAiC,OAAO;gBAAa;gBAC9D;oBAAE,OAAO;oBAA8B,OAAO;gBAAY;gBAC1D;oBAAE,OAAO;oBAA0B,OAAO;gBAAW;gBACrD;oBAAE,OAAO;oBAAgC,OAAO;gBAAa;gBAC7D;oBAAE,OAAO;oBAAyB,OAAO;gBAAO;gBAChD;oBAAE,OAAO;oBAA6B,OAAO;gBAAgB;aAC9D;QACH,KAAK;YACH,OAAO;gBACL;oBAAE,OAAO;oBAAqC,OAAO;gBAAa;gBAClE;oBAAE,OAAO;oBAAiC,OAAO;gBAAa;gBAC9D;oBAAE,OAAO;oBAAsC,OAAO;gBAAuB;gBAC7E;oBAAE,OAAO;oBAAsC,OAAO;gBAAmB;gBACzE;oBAAE,OAAO;oBAAkC,OAAO;gBAAS;gBAC3D;oBAAE,OAAO;oBAA4C,OAAO;gBAAY;aACzE;QACH,KAAK;YACH,OAAO;gBACL;oBAAE,OAAO;oBAA6B,OAAO;gBAAW;gBACxD;oBAAE,OAAO;oBAA6B,OAAO;gBAAU;aACxD;QACH,KAAK;YACH,OAAO;gBACL;oBAAE,OAAO;oBAAiC,OAAO;gBAAU;gBAC3D;oBAAE,OAAO;oBAAiC,OAAO;gBAAa;aAC/D;QACH;YACE,OAAO,EAAE;IACb;AACF"}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/dashboard/people/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useRouter } from 'next/navigation';\r\nimport { useEffect } from 'react';\r\nimport { getModuleRedirectionPath } from '@/utils/moduleRedirection';\r\n\r\nexport default function PeopleModuleRoute() {\r\n  const router = useRouter();\r\n\r\n  useEffect(() => {\r\n    // Obtém o caminho de redirecionamento com base nas preferências do usuário\r\n    const redirectPath = getModuleRedirectionPath(\r\n      'people',\r\n      '/dashboard/people/introduction', // Agora temos uma página de introdução\r\n      '/dashboard/people/clients'\r\n    );\r\n\r\n    // Redireciona para o caminho determinado\r\n    router.push(redirectPath);\r\n  }, [router]);\r\n\r\n  return <div>Carregando...</div>;\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,2EAA2E;QAC3E,MAAM,eAAe,CAAA,GAAA,iIAAA,CAAA,2BAAwB,AAAD,EAC1C,UACA,kCACA;QAGF,yCAAyC;QACzC,OAAO,IAAI,CAAC;IACd,GAAG;QAAC;KAAO;IAEX,qBAAO,8OAAC;kBAAI;;;;;;AACd"}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}