-- Add missing neighborhood column to User table
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "neighborhood" TEXT;

-- Make sure branchId column exists (in case the previous migration didn't run)
ALTER TABLE "User" ADD COLUMN IF NOT EXISTS "branchId" TEXT;

-- Add foreign key constraint if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_constraint WHERE conname = 'User_branchId_fkey'
    ) THEN
        ALTER TABLE "User" ADD CONSTRAINT "User_branchId_fkey" 
        FOREIGN KEY ("branchId") REFERENCES "Branch"("id") 
        ON DELETE SET NULL ON UPDATE CASCADE;
    END IF;
END
$$;
