"use client";

import React, { useState, useEffect } from "react";
import { AlertCircle, CreditCard, Building } from "lucide-react";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import { ModuleModal, ModalButton, ModuleSelect, ModuleInput, ModuleFormGroup } from "@/components/ui";


const InsuranceFormModal = ({ isOpen, onClose, insurance = null, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: "",
    companyId: "",
  });
  const [companies, setCompanies] = useState([]);
  const [error, setError] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);

  const { user, isSystemAdmin } = useAuth();

  // Se não for system admin, deve usar a empresa do usuário
  useEffect(() => {
    if (!isSystemAdmin && user?.companyId) {
      setFormData(prev => ({
        ...prev,
        companyId: user.companyId
      }));
    }
  }, [isSystemAdmin, user?.companyId]);


  // Carregar empresas (se você tiver esse serviço)
  useEffect(() => {
    const loadCompanies = async () => {
      // Se não for system admin, não precisa carregar empresas
      if (!isSystemAdmin) return;

      setIsLoadingCompanies(true);
      try {
        const response = await companyService.getCompaniesForSelect();
        setCompanies(response || []);
      } catch (error) {
        console.error("Erro ao carregar empresas:", error);
        setError("Não foi possível carregar a lista de empresas.");
      } finally {
        setIsLoadingCompanies(false);
      }
    };

    if (isOpen) {
      loadCompanies();
    }
  }, [isOpen, isSystemAdmin]);

  // Preencher o formulário se estivermos editando
  useEffect(() => {
    if (insurance) {
      // Ao editar, carregar os dados do convênio
      setFormData({
        name: insurance.name || "",
        // Se não for system admin, forçar a empresa do usuário
        companyId: isSystemAdmin ? (insurance.companyId || "") : user?.companyId || "",
      });
    } else {
      // Reset do formulário para novo convênio
      setFormData({
        name: "",
        // Se não for system admin, definir a empresa do usuário
        companyId: isSystemAdmin ? "" : user?.companyId || "",
      });
    }
  }, [insurance, isSystemAdmin, user?.companyId]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Garantir que companyId esteja definido para não-admin
      const dataToSubmit = {
        ...formData,
        // Se não for system admin, garantir que use a empresa do usuário
        companyId: isSystemAdmin ? formData.companyId : user?.companyId
      };

      if (insurance) {
        // Editar convênio existente
        await insurancesService.updateInsurance(insurance.id, dataToSubmit);
      } else {
        // Criar novo convênio
        await insurancesService.createInsurance(dataToSubmit);
      }

      onSuccess();
      onClose();
    } catch (err) {
      console.error("Erro ao salvar convênio:", err);
      setError(err.response?.data?.message || "Ocorreu um erro ao salvar o convênio.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="people"
        onClick={onClose}
        disabled={isSubmitting}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="people"
        type="submit"
        form="insurance-form"
        isLoading={isSubmitting}
      >
        {insurance ? "Atualizar" : "Salvar"}
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={insurance ? "Editar Convênio" : "Novo Convênio"}
      icon={<CreditCard size={22} />}
      moduleColor="people"
      size="md"
      animateExit={true}
      footer={modalFooter}
    >
      <form id="insurance-form" onSubmit={handleSubmit} className="overflow-y-auto dark:bg-gray-800 flex flex-col justify-between p-6 space-y-6">
          {error && (
            <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg flex items-center gap-2">
              <AlertCircle size={16} />
              <span>{error}</span>
            </div>
          )}

          <ModuleFormGroup
            moduleColor="people"
            label="Nome do Convênio"
            htmlFor="name"
            icon={<CreditCard size={16} />}
            required
          >
            <ModuleInput
              moduleColor="people"
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              placeholder="Digite o nome do convênio"
            />
          </ModuleFormGroup>

          {/* Campo de empresa só visível para admin */}
          {isSystemAdmin && (
            <ModuleFormGroup
              moduleColor="people"
              label="Empresa (opcional)"
              htmlFor="companyId"
              icon={<Building size={16} />}
              helpText="Se não selecionado, o convênio estará disponível para toda a plataforma."
            >
              <ModuleSelect
                moduleColor="people"
                id="companyId"
                name="companyId"
                value={formData.companyId}
                onChange={handleChange}
                placeholder="Selecione uma empresa (Opcional)"
              >
                {isLoadingCompanies ? (
                  <option disabled>Carregando empresas...</option>
                ) : (
                  companies.map(company => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))
                )}
              </ModuleSelect>
            </ModuleFormGroup>
          )}

          {/* Para não-admin, mostrar a empresa que será usada */}
          {!isSystemAdmin && user?.companyId && (
            <ModuleFormGroup
              moduleColor="people"
              label="Empresa"
              icon={<Building size={16} />}
              helpText="O convênio será associado automaticamente à sua empresa."
            >
              <div className="px-3 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg bg-neutral-50 dark:bg-gray-700 text-neutral-700 dark:text-gray-300">
                {user.companyName || "Sua empresa"}
              </div>
            </ModuleFormGroup>
          )}

        </form>
    </ModuleModal>
  );
};

export default InsuranceFormModal;