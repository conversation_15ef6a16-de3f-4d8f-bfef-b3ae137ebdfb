# Script de Seed Base

Este script cria os dados básicos iniciais do sistema, incluindo a empresa de teste, o usuário administrador e empresas de exemplo.

## Características

- Cria uma empresa de teste com ID fixo
- Cria um usuário administrador do sistema com ID fixo
- Cria 5 empresas de exemplo com dados realistas
- Cria 2 unidades (matriz e filial) para cada empresa de exemplo
- Verifica duplicidade para não criar empresas ou unidades repetidas

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-base-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed.js
```

### Opção 3: Executando dentro do container Docker

```bash
docker exec -it high-tide-systems-api node prisma/seed.js
```

## Detalhes do Script

O script realiza as seguintes operações:

1. Cria uma empresa de teste com ID fixo '00000000-0000-0000-0000-000000000001'
2. Cria um usuário administrador do sistema com ID fixo '00000000-0000-0000-0000-000000000001'
3. Cria 5 empresas de exemplo com dados realistas:
   - TechSolutions Brasil (Tecnologia)
   - MediCare Saúde (Saúde)
   - EcoVerde Ambiental (Meio Ambiente)
   - Constrular Engenharia (Construção Civil)
   - EduFuturo Ensino (Educação)
4. Para cada empresa de exemplo, cria 2 unidades:
   - Matriz (com flag isHeadquarters = true)
   - Filial (com flag isHeadquarters = false)

## Dados do Usuário Administrador

O script cria um usuário administrador do sistema com os seguintes dados:

- **ID**: 00000000-0000-0000-0000-000000000001
- **Email**: <EMAIL>
- **Login**: admin
- **Senha**: 123456 (hash pré-gerado)
- **Nome**: Administrador
- **Função**: SYSTEM_ADMIN
- **Módulos**: ADMIN, RH, FINANCIAL, SCHEDULING, BASIC
- **Empresa**: Empresa de Teste (ID: 00000000-0000-0000-0000-000000000001)

## Notas Importantes

- Este seed deve ser executado antes de todos os outros seeds, pois cria as empresas e o usuário administrador que são utilizados pelos outros seeds
- Os IDs fixos são utilizados para garantir que os dados possam ser referenciados de forma consistente em ambientes de desenvolvimento e teste
