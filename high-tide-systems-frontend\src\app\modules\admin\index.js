// Componentes do módulo Admin
import AdminDashboard from './dashboard/AdminDashboard';
import UsersPage from './users/UsersPage';
import LogsPage from './logs/LogsPage';
import SettingsPage from './settings/SettingsPage';
import IntroductionPage from './introduction/IntroductionPage';

// Componentes de logs
import { auditLogService } from './services/auditLogService';
import AuditLogsDashboard from '@/components/logs/AuditLogsDashboard';
import LogDetailViewer from '@/components/logs/LogDetailViewer';

import { adminDashboardService } from './services/adminDashboardService';

// Exportamos os componentes para serem facilmente importados no arquivo principal
export {
  // Páginas principais
  IntroductionPage,
  AdminDashboard,
  UsersPage,
  LogsPage,
  SettingsPage,

  // Componentes de logs
  auditLogService,
  AuditLogsDashboard,
  LogDetailViewer,

  adminDashboardService
};
