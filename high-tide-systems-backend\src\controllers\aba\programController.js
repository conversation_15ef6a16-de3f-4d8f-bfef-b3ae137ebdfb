// src/controllers/aba/programController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createProgramValidation = [
  body("type").isIn(["PROGRAM_CATALOG", "LEARNING_PROGRAM"]).withMessage("Tipo de programa inválido"),
  body("name").notEmpty().withMessage("Nome do programa é obrigatório"),
  body("protocol").optional(),
  body("skill").optional(),
  body("milestone").optional(),
  body("teachingType").optional(),
  body("targetsPerSession").optional().isInt({ min: 1 }).withMessage("Quantidade de alvos por sessão deve ser um número inteiro positivo"),
  body("attemptsPerTarget").optional().isInt({ min: 1 }).withMessage("Quantidade de tentativas por alvo deve ser um número inteiro positivo"),
  body("teachingProcedure").optional(),
  body("instruction").optional(),
  body("objective").optional(),
  body("promptStep").optional(),
  body("correctionProcedure").optional(),
  body("learningCriteria").optional(),
  body("materials").optional(),
  body("notes").optional(),
];

const updateProgramValidation = [
  body("type").optional().isIn(["PROGRAM_CATALOG", "LEARNING_PROGRAM"]).withMessage("Tipo de programa inválido"),
  body("name").optional().notEmpty().withMessage("Nome do programa é obrigatório"),
  body("protocol").optional(),
  body("skill").optional(),
  body("milestone").optional(),
  body("teachingType").optional(),
  body("targetsPerSession").optional().isInt({ min: 1 }).withMessage("Quantidade de alvos por sessão deve ser um número inteiro positivo"),
  body("attemptsPerTarget").optional().isInt({ min: 1 }).withMessage("Quantidade de tentativas por alvo deve ser um número inteiro positivo"),
  body("teachingProcedure").optional(),
  body("instruction").optional(),
  body("objective").optional(),
  body("promptStep").optional(),
  body("correctionProcedure").optional(),
  body("learningCriteria").optional(),
  body("materials").optional(),
  body("notes").optional(),
];

const createProgramTargetValidation = [
  body("target").notEmpty().withMessage("Nome do alvo é obrigatório"),
  body("order").isInt({ min: 0 }).withMessage("Ordem deve ser um número inteiro não negativo"),
  body("group").optional(),
  body("situation").optional(),
  body("startDate").optional().isISO8601().withMessage("Data de início inválida"),
  body("acquisitionDate").optional().isISO8601().withMessage("Data de aquisição inválida"),
  body("maintenanceCount").optional().isInt({ min: 0 }).withMessage("Contagem de manutenção deve ser um número inteiro não negativo"),
  body("programId").notEmpty().withMessage("ID do programa é obrigatório"),
];

const updateProgramTargetValidation = [
  body("target").optional().notEmpty().withMessage("Nome do alvo é obrigatório"),
  body("order").optional().isInt({ min: 0 }).withMessage("Ordem deve ser um número inteiro não negativo"),
  body("group").optional(),
  body("situation").optional(),
  body("startDate").optional().isISO8601().withMessage("Data de início inválida"),
  body("acquisitionDate").optional().isISO8601().withMessage("Data de aquisição inválida"),
  body("maintenanceCount").optional().isInt({ min: 0 }).withMessage("Contagem de manutenção deve ser um número inteiro não negativo"),
];

// Exportar o controlador e as validações
module.exports = {
  ProgramController: class ProgramController {
  /**
   * Cria um novo programa
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        type,
        name,
        protocol,
        skill,
        milestone,
        teachingType,
        targetsPerSession,
        attemptsPerTarget,
        teachingProcedure,
        instruction,
        objective,
        promptStep,
        correctionProcedure,
        learningCriteria,
        materials,
        notes,
      } = req.body;

      // Criar programa
      const program = await prisma.program.create({
        data: {
          type,
          name,
          protocol,
          skill,
          milestone,
          teachingType,
          targetsPerSession: targetsPerSession || 1,
          attemptsPerTarget: attemptsPerTarget || 1,
          teachingProcedure: teachingProcedure || "",
          instruction: instruction || "",
          objective: objective || "",
          promptStep: promptStep || "",
          correctionProcedure: correctionProcedure || "",
          learningCriteria: learningCriteria || "",
          materials: materials || "",
          notes: notes || "",
          companyId: req.user.companyId,
          createdById: req.user.id,
        },
      });

      res.status(201).json(program);
    } catch (error) {
      console.error("Erro ao criar programa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todos os programas com filtros e paginação
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search = "",
        type,
        active = "true",
      } = req.query;

      // Construir filtros
      const where = {
        companyId: req.user.companyId,
        deletedAt: null,
      };

      // Filtro de busca
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { protocol: { contains: search, mode: "insensitive" } },
          { skill: { contains: search, mode: "insensitive" } },
        ];
      }

      // Filtro de tipo
      if (type) {
        where.type = type;
      }

      // Filtro de status
      if (active !== "all") {
        where.active = active === "true";
      }

      // Contar total de registros
      const total = await prisma.program.count({ where });

      // Buscar programas com paginação
      const programs = await prisma.program.findMany({
        where,
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          _count: {
            select: {
              targets: true,
            },
          },
        },
        orderBy: {
          createdAt: "desc",
        },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar resposta
      const response = formatSuccessResponse(
        programs,
        "programs",
        total,
        Math.ceil(total / Number(limit)),
        Number(page),
        Number(limit)
      );

      res.status(200).json(response);
    } catch (error) {
      console.error("Erro ao listar programas:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém um programa específico pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      // Buscar programa
      const program = await prisma.program.findUnique({
        where: { id },
        include: {
          createdBy: {
            select: {
              id: true,
              fullName: true,
            },
          },
          targets: {
            orderBy: {
              order: "asc",
            },
          },
        },
      });

      if (!program) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && program.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para acessar este programa" });
      }

      res.status(200).json(program);
    } catch (error) {
      console.error("Erro ao buscar programa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza um programa existente
   */
  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const {
        type,
        name,
        protocol,
        skill,
        milestone,
        teachingType,
        targetsPerSession,
        attemptsPerTarget,
        teachingProcedure,
        instruction,
        objective,
        promptStep,
        correctionProcedure,
        learningCriteria,
        materials,
        notes,
      } = req.body;

      // Verificar se o programa existe
      const existingProgram = await prisma.program.findUnique({
        where: { id },
      });

      if (!existingProgram) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingProgram.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para editar este programa" });
      }

      // Atualizar programa
      const updatedProgram = await prisma.program.update({
        where: { id },
        data: {
          type: type !== undefined ? type : undefined,
          name: name !== undefined ? name : undefined,
          protocol: protocol !== undefined ? protocol : undefined,
          skill: skill !== undefined ? skill : undefined,
          milestone: milestone !== undefined ? milestone : undefined,
          teachingType: teachingType !== undefined ? teachingType : undefined,
          targetsPerSession: targetsPerSession !== undefined ? targetsPerSession : undefined,
          attemptsPerTarget: attemptsPerTarget !== undefined ? attemptsPerTarget : undefined,
          teachingProcedure: teachingProcedure !== undefined ? teachingProcedure : undefined,
          instruction: instruction !== undefined ? instruction : undefined,
          objective: objective !== undefined ? objective : undefined,
          promptStep: promptStep !== undefined ? promptStep : undefined,
          correctionProcedure: correctionProcedure !== undefined ? correctionProcedure : undefined,
          learningCriteria: learningCriteria !== undefined ? learningCriteria : undefined,
          materials: materials !== undefined ? materials : undefined,
          notes: notes !== undefined ? notes : undefined,
        },
      });

      res.status(200).json(updatedProgram);
    } catch (error) {
      console.error("Erro ao atualizar programa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Alterna o status ativo/inativo de um programa
   */
  static async toggleStatus(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o programa existe
      const existingProgram = await prisma.program.findUnique({
        where: { id },
      });

      if (!existingProgram) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingProgram.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para alterar este programa" });
      }

      // Alternar status
      const updatedProgram = await prisma.program.update({
        where: { id },
        data: {
          active: !existingProgram.active,
        },
      });

      res.status(200).json(updatedProgram);
    } catch (error) {
      console.error("Erro ao alternar status do programa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Exclui um programa
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o programa existe
      const existingProgram = await prisma.program.findUnique({
        where: { id },
      });

      if (!existingProgram) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingProgram.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para excluir este programa" });
      }

      // Exclusão lógica (soft delete)
      const deletedProgram = await prisma.program.update({
        where: { id },
        data: {
          deletedAt: new Date(),
          active: false,
        },
      });

      res.status(200).json({ message: "Programa excluído com sucesso" });
    } catch (error) {
      console.error("Erro ao excluir programa:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Cria um novo alvo para um programa
   */
  static async createTarget(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const {
        target,
        order,
        group,
        situation,
        startDate,
        acquisitionDate,
        maintenanceCount,
        programId,
      } = req.body;

      // Verificar se o programa existe e pertence à empresa do usuário
      const program = await prisma.program.findUnique({
        where: { id: programId },
      });

      if (!program) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      if (req.user.role !== 'SYSTEM_ADMIN' && program.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para adicionar alvos a este programa" });
      }

      // Criar alvo
      const programTarget = await prisma.programTarget.create({
        data: {
          target,
          order,
          group,
          situation: situation || "ACTIVE",
          startDate: startDate ? new Date(startDate) : null,
          acquisitionDate: acquisitionDate ? new Date(acquisitionDate) : null,
          maintenanceCount: maintenanceCount || 0,
          programId,
        },
      });

      res.status(201).json(programTarget);
    } catch (error) {
      console.error("Erro ao criar alvo:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todos os alvos de um programa
   */
  static async listTargets(req, res) {
    try {
      const { programId } = req.params;
      const { page = 1, limit = 10 } = req.query;

      // Verificar se o programa existe e pertence à empresa do usuário
      const program = await prisma.program.findUnique({
        where: { id: programId },
      });

      if (!program) {
        return res.status(404).json({ message: "Programa não encontrado" });
      }

      if (req.user.role !== 'SYSTEM_ADMIN' && program.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para acessar os alvos deste programa" });
      }

      // Contar total de registros
      const total = await prisma.programTarget.count({
        where: { programId },
      });

      // Buscar alvos com paginação
      const targets = await prisma.programTarget.findMany({
        where: { programId },
        orderBy: {
          order: "asc",
        },
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar resposta
      const response = formatSuccessResponse(
        targets,
        "targets",
        total,
        Math.ceil(total / Number(limit)),
        Number(page),
        Number(limit)
      );

      res.status(200).json(response);
    } catch (error) {
      console.error("Erro ao listar alvos:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza um alvo existente
   */
  static async updateTarget(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const {
        target,
        order,
        group,
        situation,
        startDate,
        acquisitionDate,
        maintenanceCount,
      } = req.body;

      // Verificar se o alvo existe
      const existingTarget = await prisma.programTarget.findUnique({
        where: { id },
        include: {
          program: true,
        },
      });

      if (!existingTarget) {
        return res.status(404).json({ message: "Alvo não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingTarget.program.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para editar este alvo" });
      }

      // Atualizar alvo
      const updatedTarget = await prisma.programTarget.update({
        where: { id },
        data: {
          target: target !== undefined ? target : undefined,
          order: order !== undefined ? order : undefined,
          group: group !== undefined ? group : undefined,
          situation: situation !== undefined ? situation : undefined,
          startDate: startDate !== undefined ? (startDate ? new Date(startDate) : null) : undefined,
          acquisitionDate: acquisitionDate !== undefined ? (acquisitionDate ? new Date(acquisitionDate) : null) : undefined,
          maintenanceCount: maintenanceCount !== undefined ? maintenanceCount : undefined,
        },
      });

      res.status(200).json(updatedTarget);
    } catch (error) {
      console.error("Erro ao atualizar alvo:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Exclui um alvo
   */
  static async deleteTarget(req, res) {
    try {
      const { id } = req.params;

      // Verificar se o alvo existe
      const existingTarget = await prisma.programTarget.findUnique({
        where: { id },
        include: {
          program: true,
        },
      });

      if (!existingTarget) {
        return res.status(404).json({ message: "Alvo não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingTarget.program.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para excluir este alvo" });
      }

      // Excluir alvo
      await prisma.programTarget.delete({
        where: { id },
      });

      res.status(200).json({ message: "Alvo excluído com sucesso" });
    } catch (error) {
      console.error("Erro ao excluir alvo:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
  },
  createProgramValidation,
  updateProgramValidation,
  createProgramTargetValidation,
  updateProgramTargetValidation
}
