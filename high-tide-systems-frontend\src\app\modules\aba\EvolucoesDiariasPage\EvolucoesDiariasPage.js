"use client";

import React, { useState, useEffect, useCallback } from "react";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleSelect } from "@/components/ui";
import ModuleTable from "@/components/ui/ModuleTable";
import {
  Plus,
  Search,
  RefreshCw,
  Edit,
  Trash,
  FileText,
  Power,
  CheckCircle,
  XCircle,
  Users,
  ClipboardList,
  BarChart2
} from "lucide-react";
import evolucaoDiariaService from "../services/evolucaoDiariaService";
import { personsService } from "@/app/modules/people/services/personsService";
import { usersService } from "@/app/modules/admin/services/usersService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/contexts/ToastContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import EvolucaoDiariaFormModal from "./EvolucaoDiariaFormModal";
import EvolucoesSummaryModal from "./EvolucoesSummaryModal";
import { formatDate, formatDateTime } from "@/utils/formatters";

const EvolucoesDiariasPage = () => {
  // Estado para controlar a exibição dos filtros
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Estado para os filtros
  const [filters, setFilters] = useState({
    search: "",
    personId: "",
    profissionalId: "",
    status: "",
    faltou: "",
    active: true
  });

  // Estado para a lista de evoluções diárias
  const [evolucoesDiarias, setEvolucoesDiarias] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 1
  });

  // Estado para os modais
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isSummaryModalOpen, setIsSummaryModalOpen] = useState(false);
  const [selectedEvolucaoDiaria, setSelectedEvolucaoDiaria] = useState(null);

  // Estado para o diálogo de confirmação
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: "",
    message: "",
    confirmText: "",
    cancelText: "",
    onConfirm: () => {}
  });

  // Estado para as listas de pacientes e profissionais (para os filtros)
  const [persons, setPersons] = useState([]);
  const [profissionais, setProfissionais] = useState([]);

  // Hooks
  const { toast_success, toast_error } = useToast();

  // Carregar evoluções diárias
  const loadEvolucoesDiarias = useCallback(async () => {
    try {
      setLoading(true);
      const { page, limit } = pagination;

      const result = await evolucaoDiariaService.getEvolucoesDiarias({
        page,
        limit,
        ...filters
      });

      if (result && result.items) {
        setEvolucoesDiarias(result.items);
        setPagination({
          page: result.page || 1,
          limit: result.limit || 10,
          total: result.total || 0,
          pages: result.pages || 1
        });
      } else {
        setEvolucoesDiarias([]);
        setPagination({
          page: 1,
          limit: 10,
          total: 0,
          pages: 1
        });
      }
    } catch (error) {
      console.error("Erro ao carregar evoluções diárias:", error);
      setEvolucoesDiarias([]);
      toast_error({
        title: "Erro",
        message: "Não foi possível carregar as evoluções diárias. Tente novamente mais tarde."
      });
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.page, pagination.limit, toast_error]);

  // Carregar pacientes para o filtro
  const loadPersons = useCallback(async () => {
    try {
      const result = await personsService.getPersons({ limit: 100 });
      if (result && result.persons) {
        setPersons(result.persons);
      } else if (result && result.items) {
        setPersons(result.items);
      }
    } catch (error) {
      console.error("Erro ao carregar pacientes para filtro:", error);
    }
  }, []);

  // Carregar profissionais para o filtro
  const loadProfissionais = useCallback(async () => {
    try {
      const result = await usersService.getUsers({ limit: 100 });
      if (result && result.users) {
        setProfissionais(result.users);
      } else if (result && result.items) {
        setProfissionais(result.items);
      }
    } catch (error) {
      console.error("Erro ao carregar profissionais para filtro:", error);
    }
  }, []);

  // Efeito para carregar dados iniciais
  useEffect(() => {
    loadEvolucoesDiarias();
    loadPersons();
    loadProfissionais();
  }, []);

  // Efeito para recarregar dados quando a paginação mudar
  useEffect(() => {
    if (pagination.page > 0) {
      loadEvolucoesDiarias();
    }
  }, [pagination.page, pagination.limit, loadEvolucoesDiarias]);

  // Manipuladores de eventos
  const handlePageChange = (newPage) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleLimitChange = (newLimit) => {
    setPagination(prev => ({ ...prev, page: 1, limit: newLimit }));
  };

  const handleFilterChange = (e) => {
    const { name, value } = e.target;
    setFilters(prev => ({ ...prev, [name]: value }));
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadEvolucoesDiarias();
  };

  const handleRefresh = () => {
    loadEvolucoesDiarias();
  };

  const handleOpenFormModal = (evolucaoDiaria = null) => {
    setSelectedEvolucaoDiaria(evolucaoDiaria);
    setIsFormModalOpen(true);
  };

  const handleCloseFormModal = () => {
    setSelectedEvolucaoDiaria(null);
    setIsFormModalOpen(false);
  };

  const handleFormSubmit = async () => {
    handleCloseFormModal();
    await loadEvolucoesDiarias();
  };

  // Manipuladores para o modal de resumo
  const handleOpenSummaryModal = () => {
    setIsSummaryModalOpen(true);
  };

  const handleCloseSummaryModal = () => {
    setIsSummaryModalOpen(false);
  };

  const handleToggleStatus = (id, currentStatus) => {
    setConfirmDialog({
      isOpen: true,
      title: currentStatus ? "Desativar Evolução Diária" : "Ativar Evolução Diária",
      message: `Tem certeza que deseja ${currentStatus ? "desativar" : "ativar"} esta evolução diária?`,
      confirmText: "Sim",
      cancelText: "Não",
      onConfirm: async () => {
        try {
          await evolucaoDiariaService.toggleEvolucaoDiariaStatus(id);
          toast_success({
            title: "Sucesso",
            message: `Evolução diária ${currentStatus ? "desativada" : "ativada"} com sucesso!`
          });
          loadEvolucoesDiarias();
        } catch (error) {
          console.error("Erro ao alterar status da evolução diária:", error);
          toast_error({
            title: "Erro",
            message: "Não foi possível alterar o status da evolução diária. Tente novamente mais tarde."
          });
        } finally {
          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        }
      }
    });
  };

  const handleDelete = (id) => {
    setConfirmDialog({
      isOpen: true,
      title: "Excluir Evolução Diária",
      message: "Tem certeza que deseja excluir esta evolução diária? Esta ação não pode ser desfeita.",
      confirmText: "Excluir",
      cancelText: "Cancelar",
      onConfirm: async () => {
        try {
          await evolucaoDiariaService.deleteEvolucaoDiaria(id);
          toast_success({
            title: "Sucesso",
            message: "Evolução diária excluída com sucesso!"
          });
          loadEvolucoesDiarias();
        } catch (error) {
          console.error("Erro ao excluir evolução diária:", error);
          toast_error({
            title: "Erro",
            message: "Não foi possível excluir a evolução diária. Tente novamente mais tarde."
          });
        } finally {
          setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        }
      }
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Evoluções Diárias"
        description="Gerencie as evoluções diárias dos atendimentos no módulo ABA+"
        moduleColor="abaplus"
      >
        <button
          onClick={handleRefresh}
          className="p-2 text-teal-600 hover:bg-teal-50 dark:text-teal-400 dark:hover:bg-teal-900/20 rounded-lg transition-colors"
          aria-label="Atualizar lista"
        >
          <RefreshCw size={20} />
        </button>
        <FilterButton
          isOpen={isFilterOpen}
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          moduleColor="abaplus"
        />
        <button
          onClick={handleOpenSummaryModal}
          className="px-4 py-2 bg-teal-500 hover:bg-teal-600 text-white rounded-lg shadow transition-colors flex items-center gap-2 mr-2"
          aria-label="Ver resumo das evoluções"
        >
          <BarChart2 size={16} />
          Resumo da Evolução
        </button>
        <button
          onClick={() => handleOpenFormModal()}
          className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
        >
          <Plus size={16} />
          Nova Evolução Diária
        </button>
      </ModuleHeader>

      {/* Filtros */}
      {isFilterOpen && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <ModuleInput
                name="search"
                value={filters.search}
                onChange={handleFilterChange}
                placeholder="Buscar por nome, atendimento..."
                moduleColor="abaplus"
                leftIcon={<Search size={16} />}
              />
            </div>
            <div>
              <ModuleSelect
                name="personId"
                value={filters.personId}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="">Todos os aprendizes</option>
                {Array.isArray(persons) && persons.length > 0 ? (
                  persons.map(person => (
                    <option key={person.id} value={person.id}>
                      {person.fullName || `Aprendiz ${person.id}`}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>Carregando aprendizes...</option>
                )}
              </ModuleSelect>
            </div>
            <div>
              <ModuleSelect
                name="profissionalId"
                value={filters.profissionalId}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="">Todos os profissionais</option>
                {Array.isArray(profissionais) && profissionais.length > 0 ? (
                  profissionais.map(profissional => (
                    <option key={profissional.id} value={profissional.id}>
                      {profissional.fullName || `Profissional ${profissional.id}`}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>Carregando profissionais...</option>
                )}
              </ModuleSelect>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <ModuleSelect
                name="status"
                value={filters.status}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="">Todos os status</option>
                <option value="RASCUNHO">Rascunho</option>
                <option value="FINALIZADA">Finalizada</option>
              </ModuleSelect>
            </div>
            <div>
              <ModuleSelect
                name="faltou"
                value={filters.faltou}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="">Todos</option>
                <option value="true">Com falta</option>
                <option value="false">Sem falta</option>
              </ModuleSelect>
            </div>
            <div>
              <ModuleSelect
                name="active"
                value={filters.active}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="true">Ativos</option>
                <option value="false">Inativos</option>
                <option value="">Todos</option>
              </ModuleSelect>
            </div>
          </div>
          <div className="flex justify-end">
            <button
              onClick={handleSearch}
              className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
            >
              <Search size={16} />
              Buscar
            </button>
          </div>
        </div>
      )}

      {/* Tabela de Evoluções Diárias */}
      <ModuleTable
        moduleColor="abaplus"
        columns={[
          { header: 'Aprendiz', field: 'person', width: '20%' },
          { header: 'Data', field: 'dataInicio', width: '15%' },
          { header: 'Profissional', field: 'profissional', width: '20%' },
          { header: 'Situação', field: 'status', width: '10%' },
          { header: 'Falta', field: 'faltou', width: '10%' },
          { header: 'Status', field: 'active', width: '10%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={evolucoesDiarias.map(evolucao => ({
          id: evolucao.id,
          person: evolucao.person,
          personFullName: evolucao.person?.fullName || "",
          personProfileImageUrl: evolucao.person?.profileImageUrl || "",
          dataInicio: evolucao.dataInicio,
          formattedDataInicio: formatDateTime(evolucao.dataInicio),
          dataFim: evolucao.dataFim,
          formattedDataFim: formatDateTime(evolucao.dataFim),
          profissional: evolucao.profissional,
          profissionalFullName: evolucao.profissional?.fullName || "",
          status: evolucao.status,
          faltou: evolucao.faltou,
          active: evolucao.active,
        }))}
        renderRow={(item, _index, moduleColors, visibleColumns) => (
          <tr key={item.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('person') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center gap-2">
                  {item.personProfileImageUrl ? (
                    <img
                      src={item.personProfileImageUrl}
                      alt={item.personFullName}
                      className="w-8 h-8 rounded-full object-cover"
                    />
                  ) : (
                    <div className="w-8 h-8 rounded-full bg-teal-100 dark:bg-teal-900 flex items-center justify-center text-teal-600 dark:text-teal-300 font-medium">
                      {item.personFullName.charAt(0)}
                    </div>
                  )}
                  <span className="font-medium">{item.personFullName}</span>
                </div>
              </td>
            )}

            {visibleColumns.includes('dataInicio') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-gray-100">
                  {item.formattedDataInicio}
                </div>
              </td>
            )}

            {visibleColumns.includes('profissional') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="text-sm text-gray-900 dark:text-gray-100">
                  {item.profissionalFullName}
                </div>
              </td>
            )}

            {visibleColumns.includes('status') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  {item.status === "FINALIZADA" ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      <CheckCircle size={12} className="mr-1" />
                      Finalizada
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                      <ClipboardList size={12} className="mr-1" />
                      Rascunho
                    </span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('faltou') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  {item.faltou ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                      <XCircle size={12} className="mr-1" />
                      Sim
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      <CheckCircle size={12} className="mr-1" />
                      Não
                    </span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('active') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  {item.active ? (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      <CheckCircle size={12} className="mr-1" />
                      Ativo
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                      <XCircle size={12} className="mr-1" />
                      Inativo
                    </span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right">
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => handleOpenFormModal(evolucoesDiarias.find(e => e.id === item.id))}
                    className="p-1 text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20 rounded"
                    aria-label="Editar"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleToggleStatus(item.id, item.active)}
                    className={`p-1 ${
                      item.active
                        ? "text-amber-600 hover:bg-amber-50 dark:text-amber-400 dark:hover:bg-amber-900/20"
                        : "text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20"
                    } rounded`}
                    aria-label={item.active ? "Desativar" : "Ativar"}
                  >
                    <Power size={16} />
                  </button>
                  <button
                    onClick={() => handleDelete(item.id)}
                    className="p-1 text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 rounded"
                    aria-label="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
        currentPage={pagination.page}
        totalPages={pagination.pages}
        totalItems={pagination.total}
        onPageChange={handlePageChange}
        itemsPerPage={pagination.limit}
        isLoading={loading}
        emptyMessage="Nenhuma evolução diária encontrada."
        tableId="aba-evolucoes-diarias-table"
        enableColumnToggle={true}
        defaultSortField="dataInicio"
        defaultSortDirection="desc"
      />

      {/* Modal de Formulário */}
      {isFormModalOpen && (
        <EvolucaoDiariaFormModal
          isOpen={isFormModalOpen}
          onClose={handleCloseFormModal}
          onSubmit={handleFormSubmit}
          evolucaoDiaria={selectedEvolucaoDiaria}
        />
      )}

      {/* Diálogo de Confirmação */}
      <ConfirmationDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        message={confirmDialog.message}
        confirmText={confirmDialog.confirmText}
        cancelText={confirmDialog.cancelText}
        onConfirm={confirmDialog.onConfirm}
        onCancel={() => setConfirmDialog(prev => ({ ...prev, isOpen: false }))}
      />

      {/* Modal de Resumo das Evoluções */}
      {isSummaryModalOpen && (
        <EvolucoesSummaryModal
          isOpen={isSummaryModalOpen}
          onClose={handleCloseSummaryModal}
        />
      )}
    </div>
  );
};

export default EvolucoesDiariasPage;
