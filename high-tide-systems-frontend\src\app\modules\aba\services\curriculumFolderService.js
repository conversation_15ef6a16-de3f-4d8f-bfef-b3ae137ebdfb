// src/app/modules/aba/services/curriculumFolderService.js
import { api } from "@/utils/api";
import { extractData, extractEntity } from "@/utils/apiResponseAdapter";

export const curriculumFolderService = {
  // Criar uma nova pasta curricular
  createCurriculumFolder: async (folderData) => {
    try {
      const response = await api.post("/aba/curriculum-folders", folderData);
      return extractEntity(response.data);
    } catch (error) {
      console.error("Error creating curriculum folder:", error);
      throw error;
    }
  },

  // Listar pastas curriculares com paginação e filtros
  getCurriculumFolders: async (params = {}) => {
    try {
      const response = await api.get("/aba/curriculum-folders", { params });
      return extractData(response.data, 'curriculumFolders');
    } catch (error) {
      console.error("Error fetching curriculum folders:", error);
      throw error;
    }
  },

  // Obter uma pasta curricular específica
  getCurriculumFolder: async (id) => {
    try {
      const response = await api.get(`/aba/curriculum-folders/${id}`);
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error fetching curriculum folder ${id}:`, error);
      throw error;
    }
  },

  // Atualizar uma pasta curricular
  updateCurriculumFolder: async (id, folderData) => {
    try {
      const response = await api.put(`/aba/curriculum-folders/${id}`, folderData);
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error updating curriculum folder ${id}:`, error);
      throw error;
    }
  },

  // Alternar o status de uma pasta curricular
  toggleCurriculumFolderStatus: async (id) => {
    try {
      const response = await api.patch(`/aba/curriculum-folders/${id}/status`);
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error toggling curriculum folder status ${id}:`, error);
      throw error;
    }
  },

  // Excluir uma pasta curricular
  deleteCurriculumFolder: async (id) => {
    try {
      const response = await api.delete(`/aba/curriculum-folders/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting curriculum folder ${id}:`, error);
      throw error;
    }
  }
};

export default curriculumFolderService;
