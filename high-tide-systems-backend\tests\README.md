# Testes do High Tide Systems Backend

Este diretório contém testes para o backend do High Tide Systems.

## Estrutura de Diretórios

- `controllers/`: Testes para os controladores da API
- `models/`: Testes para os modelos de dados
- `routes/`: Testes para as rotas da API
- `utils/`: Testes para utilitários e serviços

## Como Executar os Testes

Você pode executar os testes usando os comandos npm definidos no `package.json`:

### Executar todos os testes

```bash
npm run test:all
```

### Executar testes específicos

```bash
# Testes de controladores
npm run test:controllers
npm run test:auth
npm run test:users
npm run test:companies
npm run test:persons
npm run test:schedulings
npm run test:service-types
npm run test:professions

# Testes de utilitários
npm run test:utils
npm run test:cache
npm run test:email
npm run test:auth-middleware
```

## Autenticação nos Testes

Os testes utilizam um token de teste especial que é reconhecido pelo middleware de autenticação quando o ambiente é de desenvolvimento ou teste. Isso permite que os testes sejam executados sem a necessidade de criar usuários reais no banco de dados.

O formato do token de teste é:

```
Bearer TEST_TOKEN_<user_id>
```

Onde `<user_id>` é um ID de usuário fictício que será usado para identificar o usuário nos testes.

## Notas Importantes

1. Os testes são projetados para serem executados em um ambiente de desenvolvimento ou teste.
2. Alguns testes podem modificar dados no banco de dados, então é recomendado executá-los em um ambiente isolado.
3. Os testes de e-mail não enviam e-mails reais, apenas verificam a configuração do serviço.
4. Os testes de cache verificam se o cache está funcionando corretamente, mas não modificam a configuração do Redis.
