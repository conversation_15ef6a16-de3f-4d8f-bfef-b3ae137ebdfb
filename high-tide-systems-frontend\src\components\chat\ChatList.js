'use client';

import React, { useMemo, useCallback, useState, useEffect } from 'react';
import { useChat } from '@/contexts/ChatContext';
import { formatDistanceToNow, format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { useAuth } from '@/contexts/AuthContext';
import { Users } from 'lucide-react';

const ChatList = ({ searchQuery = '' }) => {
  const { conversations, setActiveConversation, messages, loadConversations, isLoading } = useChat();
  const { user } = useAuth();

  // Estado para forçar re-renderização
  const [updateTrigger, setUpdateTrigger] = useState(0);

  // Carregar conversas quando o componente é montado
  useEffect(() => {
    // Carregar conversas inicialmente
    console.log('ChatList montado, verificando se é necessário carregar conversas...');
    if (user && user.id) {
      console.log('Usuário logado:', user.id);
      // Verificar se já temos conversas carregadas
      if (conversations.length === 0) {
        console.log('Não há conversas carregadas, carregando conversas...');
        loadConversations(false); // Usar cache se disponível
      } else {
        console.log('Já existem conversas carregadas, não é necessário recarregar');
      }
    } else {
      console.log('Usuário não logado, não carregando conversas');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user?.id]); // Executar quando o usuário mudar

  // Adicionar listener para eventos de atualização do WebSocket
  useEffect(() => {
    // Função para lidar com eventos de atualização
    const handleWebSocketUpdate = (event) => {
      const { type, action, conversationId } = event.detail;

      console.log(`ChatList recebeu evento de atualização: ${type}`, event.detail);

      // Forçar re-renderização para qualquer tipo de evento
      setUpdateTrigger(prev => prev + 1);

      // Se for uma atualização de conversas, verificar se precisamos recarregar
      if (type === 'conversations') {
        // Se for uma ação de exclusão, não precisamos recarregar as conversas
        // pois o estado já foi atualizado no ChatContext
        if (action === 'delete') {
          console.log(`ChatList: Conversa ${conversationId} foi apagada, atualizando interface`);
          // Apenas forçar re-renderização
          setUpdateTrigger(prev => prev + 1);
        } else if (conversations.length === 0) {
          // Só recarregar se não tivermos conversas
          console.log('ChatList: Não há conversas carregadas, recarregando após evento de atualização');
          loadConversations(false).then(() => {
            console.log('ChatList: Conversas carregadas com sucesso');
            // Forçar outra re-renderização após o carregamento
            setUpdateTrigger(prev => prev + 1);
          });
        } else {
          console.log('ChatList: Já existem conversas carregadas, apenas atualizando interface');
          // Apenas forçar re-renderização
          setUpdateTrigger(prev => prev + 1);
        }
      }
    };

    // Adicionar listener
    window.addEventListener('chat:websocket:update', handleWebSocketUpdate);

    // Remover listener quando o componente for desmontado
    return () => {
      window.removeEventListener('chat:websocket:update', handleWebSocketUpdate);
    };
  }, [loadConversations]); // Dependência: loadConversations

  // Filtrar conversas com base na pesquisa
  const filteredConversations = useMemo(() => {
    // Log para debug
    console.log(`Recalculando filteredConversations. Trigger: ${updateTrigger}, Conversas: ${conversations.length}`);

    if (!searchQuery) return conversations;

    return conversations.filter(conversation => {
      // Para conversas individuais, pesquisar pelo nome do outro participante
      if (conversation.type === 'INDIVIDUAL') {
        const otherParticipant = conversation.participants?.find(
          p => p.userId !== user?.id
        );
        return otherParticipant?.user?.fullName?.toLowerCase().includes(searchQuery.toLowerCase());
      }

      // Para grupos, pesquisar pelo título
      return conversation.title?.toLowerCase().includes(searchQuery.toLowerCase());
    });
  }, [conversations, searchQuery, user?.id, updateTrigger]); // Adicionado updateTrigger como dependência

  // Obter o nome da conversa - memoizado para evitar recalculos
  const getConversationName = useCallback((conversation) => {
    if (!conversation) return 'Conversa';

    if (conversation.type === 'GROUP') {
      return conversation.title || 'Grupo';
    }

    const otherParticipant = conversation.participants?.find(
      p => p.userId !== user?.id
    );

    return otherParticipant?.user?.fullName || 'Usuário';
  }, [user?.id]);

  // Obter a imagem da conversa - memoizado para evitar recalculos
  const getConversationImage = useCallback((conversation) => {
    if (!conversation) return null;

    if (conversation.type === 'GROUP') {
      return null; // Usar um ícone de grupo
    }

    const otherParticipant = conversation.participants?.find(
      p => p.userId !== user?.id
    );

    return otherParticipant?.user?.profileImageUrl;
  }, [user?.id]);

  // Formatar a data da última mensagem - memoizado para evitar recalculos
  const formatLastMessageTime = useCallback((timestamp) => {
    if (!timestamp) return '';

    try {
      return formatDistanceToNow(new Date(timestamp), {
        addSuffix: true,
        locale: ptBR
      });
    } catch (error) {
      console.error('Erro ao formatar data:', error);
      return '';
    }
  }, []);

  // Obter iniciais para avatar - memoizado para evitar recalculos
  const getInitials = useCallback((name) => {
    if (!name) return 'U';

    try {
      const names = name.split(' ');
      if (names.length === 1) return names[0].charAt(0);

      return `${names[0].charAt(0)}${names[names.length - 1].charAt(0)}`;
    } catch (error) {
      console.error('Erro ao obter iniciais:', error);
      return 'U';
    }
  }, []);

  // Log para depuração - quando o componente é renderizado
  console.log(`ChatList renderizando. Trigger: ${updateTrigger}, Conversas: ${conversations.length}, Filtradas: ${filteredConversations.length}`);

  if (filteredConversations.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-gray-500 dark:text-gray-400 relative">
        {/* Indicador de status da conexão */}
        <div className="absolute top-2 right-2 flex flex-col items-end">
          <div className="flex items-center">
            {isLoading && (
              <span className="text-xs text-orange-500 animate-pulse">Carregando...</span>
            )}
          </div>
          {/* Removido indicador de última atualização e status de conexão */}
        </div>

        <Users size={48} className="mb-2" />
        <p className="text-center text-sm">
          {searchQuery
            ? 'Nenhuma conversa encontrada'
            : 'Nenhuma conversa iniciada'}
        </p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700 relative">
      {/* Indicador de status da conexão */}
      <div className="absolute top-2 right-2 flex flex-col items-end">
        <div className="flex items-center">
          {isLoading && (
            <span className="text-xs text-orange-500 animate-pulse">Carregando...</span>
          )}
        </div>
        {/* Removido indicador de última atualização e status de conexão */}
      </div>

      {filteredConversations.map(conversation => {
        const name = getConversationName(conversation);
        const image = getConversationImage(conversation);
        // Verificar se há mensagens carregadas para esta conversa
        const conversationMessages = messages[conversation.id] || [];
        // Usar a última mensagem da conversa ou a última mensagem carregada
        const lastMessage = conversation.lastMessage || (conversationMessages.length > 0 ? conversationMessages[conversationMessages.length - 1] : null);
        const hasUnread = conversation.unreadCount > 0;

        return (
          <button
            key={conversation.id}
            onClick={() => setActiveConversation(conversation.id)}
            className="w-full flex items-center gap-3 p-3 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors text-left"
          >
            {/* Avatar */}
            <div className="relative flex-shrink-0">
              {image ? (
                <img
                  src={image}
                  alt={name}
                  className="h-10 w-10 rounded-full object-cover"
                  onError={(e) => {
                    e.target.onerror = null;
                    e.target.style.display = 'none';
                  }}
                />
              ) : conversation.type === 'GROUP' ? (
                <div className="h-10 w-10 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center text-purple-600 dark:text-purple-300">
                  <Users size={18} />
                </div>
              ) : (
                <div className="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center text-blue-600 dark:text-blue-300 font-medium">
                  {getInitials(name)}
                </div>
              )}

              {/* Indicador online */}
              {conversation.isOnline && (
                <div className="absolute bottom-0 right-0 h-3 w-3 rounded-full bg-green-500 border-2 border-white dark:border-gray-800"></div>
              )}
            </div>

            {/* Informações */}
            <div className="flex-1 min-w-0">
              <div className="flex justify-between items-center">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 truncate">
                  {name}
                </h4>
                {lastMessage && (
                  <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap">
                    {formatLastMessageTime(lastMessage.createdAt)}
                  </span>
                )}
              </div>

              {lastMessage ? (
                <p className={`text-sm truncate ${hasUnread ? 'font-medium text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'}`}>
                  {lastMessage.content}
                </p>
              ) : (
                <p className="text-sm text-gray-500 dark:text-gray-400 italic">
                  Nenhuma mensagem
                </p>
              )}
            </div>

            {/* Indicador de não lidas */}
            {hasUnread && (
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
            )}
          </button>
        );
      })}
    </div>
  );
};

export default ChatList;
