/**
 * Utilitário para tratamento padronizado de erros na API
 */
const { AppError, NotFoundError, UnauthorizedError, ForbiddenError, ValidationError } = require('./errors');

/**
 * Trata erros de validação do Prisma
 * @param {Error} error - Erro capturado
 * @returns {Object} Objeto com código de status e mensagem de erro formatada
 */
const handlePrismaError = (error) => {
  // Erro de registro único (unique constraint)
  if (error.code === 'P2002') {
    const field = error.meta?.target?.[0] || 'campo';
    return {
      status: 400,
      message: `Já existe um registro com este ${field}`,
      errors: [{ param: field, msg: `Já existe um registro com este valor` }]
    };
  }

  // Erro de registro não encontrado
  if (error.code === 'P2025') {
    return {
      status: 404,
      message: 'Registro não encontrado',
      errors: [{ param: 'id', msg: 'Registro não encontrado' }]
    };
  }

  // Erro de relação (foreign key constraint)
  if (error.code === 'P2003') {
    const field = error.meta?.field_name || 'campo relacionado';
    return {
      status: 400,
      message: `Operação inválida: ${field} não existe ou não está disponível`,
      errors: [{ param: field, msg: 'Referência inválida' }]
    };
  }

  // Erro genérico do Prisma
  return {
    status: 500,
    message: 'Erro no banco de dados',
    errors: [{ param: 'database', msg: error.message }]
  };
};

/**
 * Trata erros de validação
 * @param {Error} error - Erro capturado
 * @returns {Object} Objeto com código de status e mensagem de erro formatada
 */
const handleValidationError = (error) => {
  return {
    status: 400,
    message: 'Erro de validação',
    errors: error.errors || [{ param: 'validation', msg: error.message }]
  };
};

/**
 * Trata erros de autenticação
 * @param {Error} error - Erro capturado
 * @returns {Object} Objeto com código de status e mensagem de erro formatada
 */
const handleAuthError = (error) => {
  return {
    status: 401,
    message: error.message || 'Não autorizado',
    errors: [{ param: 'auth', msg: error.message || 'Não autorizado' }]
  };
};

/**
 * Trata erros de permissão
 * @param {Error} error - Erro capturado
 * @returns {Object} Objeto com código de status e mensagem de erro formatada
 */
const handleForbiddenError = (error) => {
  return {
    status: 403,
    message: error.message || 'Acesso negado',
    errors: [{ param: 'permission', msg: error.message || 'Acesso negado' }]
  };
};

/**
 * Trata erros genéricos
 * @param {Error} error - Erro capturado
 * @returns {Object} Objeto com código de status e mensagem de erro formatada
 */
const handleGenericError = (error) => {
  return {
    status: error.status || 500,
    message: error.message || 'Erro interno do servidor',
    errors: error.errors || [{ param: 'server', msg: error.message || 'Erro interno do servidor' }]
  };
};

/**
 * Função principal para tratamento de erros
 * @param {Error} error - Erro capturado
 * @returns {Object} Objeto com código de status e mensagem de erro formatada
 */
const handleError = (error) => {
  // Log do erro para depuração
  console.error('Erro capturado:', error);

  // Verificar o tipo de erro
  if (error.name === 'PrismaClientKnownRequestError' || error.code?.startsWith('P')) {
    return handlePrismaError(error);
  }

  if (error.name === 'ValidationError' || error.errors) {
    return handleValidationError(error);
  }

  if (error.name === 'UnauthorizedError' || error.status === 401) {
    return handleAuthError(error);
  }

  if (error.name === 'ForbiddenError' || error.status === 403) {
    return handleForbiddenError(error);
  }

  // Erro genérico
  return handleGenericError(error);
};

module.exports = {
  handleError,
  handlePrismaError,
  handleValidationError,
  handleAuthError,
  handleForbiddenError,
  handleGenericError
};
