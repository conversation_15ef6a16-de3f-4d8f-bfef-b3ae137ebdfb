@echo off
chcp 65001 >nul
cls

echo 🚀 Setup rápido da migração ABA+ para projeto existente (Windows)...
echo.

REM Verificar se estamos na raiz do projeto
if not exist "docker-compose.yml" (
    echo ❌ Execute este script na raiz do seu projeto (onde está o docker-compose.yml)
    pause
    exit /b 1
)

echo ✓ Projeto Docker encontrado
echo.

REM Criar pasta para scripts de migração
echo 📁 Criando pasta migration-scripts...
if not exist "migration-scripts" mkdir migration-scripts

REM Verificar se containers estão rodando
echo 🐳 Verificando containers...
docker-compose ps | findstr "postgres.*Up" >nul
if %errorlevel% neq 0 (
    echo ⏳ Iniciando containers...
    docker-compose up -d
    echo ⏳ Aguardando banco ficar pronto...
    timeout /t 10 /nobreak >nul
)

REM Testar conexão com banco
echo 🔍 Testando conexão com banco...
docker-compose exec -T postgres psql -U admin -d insidehouses -c "SELECT 1;" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Erro de conexão com banco
    echo Verifique se os containers estão rodando:
    docker-compose ps
    pause
    exit /b 1
)
echo ✓ Banco de dados acessível

REM Verificar se configurações existem no .env
echo ⚙️  Verificando configurações...
findstr /c:"DEFAULT_COMPANY_NAME" .env >nul 2>&1
if %errorlevel% neq 0 (
    echo. >> .env
    echo # Configurações da Migração ABA+ >> .env
    echo DEFAULT_COMPANY_NAME="ABA+ Migrada" >> .env
    echo DEFAULT_BRANCH_NAME="Unidade Principal" >> .env
    echo DEFAULT_PASSWORD="TrocarSenha123!" >> .env
    echo BEARER_TOKEN="seu_token_bearer_da_api_aqui" >> .env
    echo ✓ Configurações adicionadas ao .env
) else (
    echo ✓ Configurações já existem no .env
)

REM Verificar dependências
echo 📦 Verificando dependências...
call npm list axios >nul 2>&1
set axios_missing=%errorlevel%
call npm list csv-writer >nul 2>&1
set csvwriter_missing=%errorlevel%
call npm list papaparse >nul 2>&1
set papa_missing=%errorlevel%
call npm list uuid >nul 2>&1
set uuid_missing=%errorlevel%

set missing_deps=
if %axios_missing% neq 0 set missing_deps=%missing_deps% axios
if %csvwriter_missing% neq 0 set missing_deps=%missing_deps% csv-writer
if %papa_missing% neq 0 set missing_deps=%missing_deps% papaparse
if %uuid_missing% neq 0 set missing_deps=%missing_deps% uuid

if not "%missing_deps%"=="" (
    echo 📦 Instalando dependências faltantes:%missing_deps%
    call npm install%missing_deps%
    echo ✓ Dependências instaladas
) else (
    echo ✓ Todas as dependências já estão instaladas
)

echo.
echo 🎉 Setup concluído!
echo.
echo 📋 Próximos passos:
echo.
echo 1. 📝 Configure o BEARER_TOKEN no arquivo .env:
echo    BEARER_TOKEN="seu_token_bearer_da_api_aba"
echo.
echo 2. 📥 Coloque os scripts na raiz do projeto:
echo    • index.js (extração da API)
echo    • migrate-existing-docker.js (migração)
echo.
echo 3. 🏃 Execute a migração:
echo    node index.js
echo    node migrate-existing-docker.js
echo.
echo 4. 🔍 Verificar resultados:
echo    • Prisma Studio: http://localhost:5555
echo    • Relatório: relatorio_migracao_banco.json
echo.
echo 🌐 Seu ambiente:
echo    • PostgreSQL: localhost:5432 (admin / sua_senha)
echo    • Database: insidehouses
echo    • Prisma Studio: http://localhost:5555
echo.
echo ⚠️  IMPORTANTE: Altere as senhas dos usuários migrados antes da produção!
echo.
pause