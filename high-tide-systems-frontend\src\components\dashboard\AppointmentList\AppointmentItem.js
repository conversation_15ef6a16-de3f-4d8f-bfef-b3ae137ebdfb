'use client';

import React from 'react';
import { MapPin, Users, UserCheck } from 'lucide-react';
import { formatDateTime, getTimeFrame } from '@/utils/dateFormatters';
import StatusBadge from './StatusBadge';

export const AppointmentItem = ({ appointment }) => {
  const { date, time, weekday } = formatDateTime(appointment.startDate);
  const timeFrame = getTimeFrame(appointment.startDate);

  return (
    <div
      className="flex items-start p-4 rounded-xl border-2 border-orange-200 dark:border-orange-800/50 hover:shadow-xl dark:hover:shadow-lg dark:hover:shadow-black/30 transition-all duration-300 bg-white dark:bg-gray-800 transform hover:-translate-y-1"
      style={{ boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.05)' }}
    >
      <div className="flex flex-col items-center mr-5">
        <div className={`rounded-t-lg ${timeFrame.class} px-3 py-1 text-xs font-bold uppercase w-full text-center dark:bg-opacity-80`}>
          {timeFrame.text}
        </div>
        <div className="w-16 h-16 flex-shrink-0 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800 border border-gray-200 dark:border-gray-700 border-t-0 rounded-b-lg flex flex-col items-center justify-center">
          <span className="text-xs font-bold text-gray-400 dark:text-gray-500 uppercase">{weekday}</span>
          <span className="text-lg font-bold text-gray-800 dark:text-gray-200">{date.split('/')[0]}</span>
          <span className="text-xs font-medium text-gray-500 dark:text-gray-400">{time}</span>
        </div>
      </div>

      <div className="flex-1">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-2">
          <div>
            <h4 className="font-bold text-gray-800 dark:text-gray-100 text-lg">{appointment.title}</h4>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-0.5 flex items-center">
              <UserCheck size={14} className="mr-1 text-primary-500 dark:text-primary-400" aria-hidden="true" />
              {appointment.personName || 'Paciente'}
            </p>
          </div>
          <StatusBadge status={appointment.status} />
        </div>

        <div className="flex flex-col sm:flex-row gap-3 mt-3 text-xs text-gray-500 dark:text-gray-400">
          <span className="flex items-center">
            <MapPin size={14} className="mr-1 text-gray-400 dark:text-gray-500" aria-hidden="true" />
            <strong className="mr-1">Local:</strong> {appointment.locationName || 'Consultório'}
          </span>
          <span className="flex items-center">
            <Users size={14} className="mr-1 text-gray-400 dark:text-gray-500" aria-hidden="true" />
            <strong className="mr-1">Profissional:</strong> {appointment.providerName || 'Médico'}
          </span>
        </div>
      </div>
    </div>
  );
};

export default AppointmentItem;