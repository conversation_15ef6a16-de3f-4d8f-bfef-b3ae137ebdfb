# Sistema de Notas de Atualização

Este documento descreve como implementar o sistema de notas de atualização no backend.

## Passos para Implementação

### 1. Adicionar o modelo ao schema do Prisma

Adicione o seguinte modelo ao arquivo `prisma/schema.prisma`:

```prisma
model SystemUpdateNote {
  id        String   @id @default(uuid())
  content   String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String
  user      User     @relation(fields: [createdBy], references: [id])

  @@index([createdAt])
}
```

Adicione também a relação ao modelo User:

```prisma
model User {
  // ... outros campos e relações
  
  SystemUpdateNotes SystemUpdateNote[]
}
```

### 2. Gerar e aplicar a migração

Execute os seguintes comandos:

```bash
npx prisma migrate dev --name add_system_update_notes
```

### 3. Registrar as rotas no servidor

Adicione a seguinte linha ao arquivo `src/server.js` e `src/app.js`:

```javascript
const systemUpdateNoteRoutes = require('./routes/systemUpdateNoteRoutes');
```

E adicione a seguinte linha na seção de configuração de rotas:

```javascript
app.use('/system-update-notes', systemUpdateNoteRoutes);
```

### 4. Testar a implementação

Após implementar, teste as seguintes rotas:

- `GET /system-update-notes/latest` - Obter a nota mais recente
- `POST /system-update-notes` - Criar uma nova nota (apenas system_admin)
- `PUT /system-update-notes/:id` - Atualizar uma nota existente (apenas system_admin)
- `DELETE /system-update-notes/:id` - Excluir uma nota (apenas system_admin)
- `GET /system-update-notes` - Listar todas as notas

## Observações

- Apenas usuários com a role `SYSTEM_ADMIN` podem criar, atualizar ou excluir notas
- Qualquer usuário pode visualizar as notas
- A rota `/latest` é pública e não requer autenticação para facilitar o carregamento inicial da página
