// src/controllers/aba/scoreController.js
const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../../utils/prisma");
const { formatSuccessResponse, formatErrorResponse } = require('../../utils/responseUtil');

// Validações
const createScoreValidation = [
  body("type").isIn(["ALWAYS", "FREQUENTLY", "SOMETIMES", "RARELY", "NEVER", "NOT_APPLICABLE"]).withMessage("Tipo de pontuação inválido"),
  body("value").optional(),
  body("description").optional(),
  body("evaluationId").notEmpty().withMessage("ID da avaliação é obrigatório"),
];

const updateScoreValidation = [
  body("type").optional().isIn(["ALWAYS", "FREQUENTLY", "SOMETIMES", "RARELY", "NEVER", "NOT_APPLICABLE"]).withMessage("Tipo de pontuação inválido"),
  body("value").optional(),
  body("description").optional(),
];

class ScoreController {
  /**
   * Cria uma nova pontuação
   */
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { type, value, description, evaluationId } = req.body;

      // Verificar se a avaliação existe e pertence à empresa do usuário
      const evaluation = await prisma.evaluation.findUnique({
        where: { id: evaluationId },
      });

      if (!evaluation) {
        return res.status(404).json({ message: "Avaliação não encontrada" });
      }

      if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para adicionar pontuações a esta avaliação" });
      }

      // Criar pontuação
      const score = await prisma.score.create({
        data: {
          type,
          value,
          description,
          evaluationId,
        },
      });

      res.status(201).json(score);
    } catch (error) {
      console.error("Erro ao criar pontuação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Lista todas as pontuações com filtros e paginação
   */
  static async list(req, res) {
    try {
      const {
        page = 1,
        limit = 10,
        search = "",
        evaluationId,
        type,
      } = req.query;

      // Construir filtros
      const where = {};

      // Filtro por avaliação
      if (evaluationId) {
        where.evaluationId = evaluationId;

        // Verificar se a avaliação pertence à empresa do usuário
        const evaluation = await prisma.evaluation.findUnique({
          where: { id: evaluationId },
        });

        if (!evaluation) {
          return res.status(404).json({ message: "Avaliação não encontrada" });
        }

        if (req.user.role !== 'SYSTEM_ADMIN' && evaluation.companyId !== req.user.companyId) {
          return res.status(403).json({ message: "Você não tem permissão para visualizar pontuações desta avaliação" });
        }
      } else {
        // Se não for filtrado por avaliação, filtrar por empresa
        where.evaluation = {
          companyId: req.user.companyId,
          deletedAt: null,
        };
      }

      // Filtro por tipo
      if (type) {
        where.type = type;
      }

      // Filtro de busca
      if (search) {
        where.OR = [
          { description: { contains: search, mode: "insensitive" } },
          { value: { contains: search, mode: "insensitive" } },
        ];
      }

      // Contar total de registros
      const total = await prisma.score.count({ where });

      // Buscar pontuações com paginação
      const scores = await prisma.score.findMany({
        where,
        include: {
          evaluation: {
            select: {
              id: true,
              name: true,
              type: true,
            },
          },
        },
        orderBy: [
          { evaluationId: "asc" },
          { type: "asc" },
        ],
        skip: (Number(page) - 1) * Number(limit),
        take: Number(limit),
      });

      // Formatar resposta
      const response = formatSuccessResponse(
        scores,
        "scores",
        total,
        Math.ceil(total / Number(limit))
      );

      res.json(response);
    } catch (error) {
      console.error("Erro ao listar pontuações:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Obtém uma pontuação específica pelo ID
   */
  static async get(req, res) {
    try {
      const { id } = req.params;

      const score = await prisma.score.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              id: true,
              name: true,
              type: true,
              companyId: true,
            },
          },
        },
      });

      if (!score) {
        return res.status(404).json({ message: "Pontuação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && score.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para visualizar esta pontuação" });
      }

      res.json(score);
    } catch (error) {
      console.error("Erro ao buscar pontuação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Atualiza uma pontuação existente
   */
  static async update(req, res) {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      // Verificar se a pontuação existe
      const existingScore = await prisma.score.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              companyId: true,
            },
          },
        },
      });

      if (!existingScore) {
        return res.status(404).json({ message: "Pontuação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingScore.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para atualizar esta pontuação" });
      }

      const { type, value, description } = req.body;

      // Atualizar pontuação
      const updatedScore = await prisma.score.update({
        where: { id },
        data: {
          type: type !== undefined ? type : undefined,
          value: value !== undefined ? value : undefined,
          description: description !== undefined ? description : undefined,
          updatedAt: new Date(),
        },
      });

      res.json(updatedScore);
    } catch (error) {
      console.error("Erro ao atualizar pontuação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  /**
   * Remove uma pontuação
   */
  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Verificar se a pontuação existe
      const score = await prisma.score.findUnique({
        where: { id },
        include: {
          evaluation: {
            select: {
              companyId: true,
            },
          },
        },
      });

      if (!score) {
        return res.status(404).json({ message: "Pontuação não encontrada" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && score.evaluation.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para remover esta pontuação" });
      }

      // Remover pontuação
      await prisma.score.delete({
        where: { id },
      });

      res.json({ message: "Pontuação removida com sucesso" });
    } catch (error) {
      console.error("Erro ao remover pontuação:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  ScoreController,
  createScoreValidation,
  updateScoreValidation,
};
