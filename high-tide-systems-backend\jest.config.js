module.exports = {
  // Diretório raiz para procurar arquivos de teste
  roots: ['<rootDir>/tests'],
  
  // Padrão de arquivos de teste
  testMatch: [
    '**/__tests__/**/*.js',
    '**/?(*.)+(spec|test).js'
  ],
  
  // Ambiente de teste
  testEnvironment: 'node',
  
  // Cobertura de código
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/swagger-autogen.js',
    '!**/node_modules/**',
    '!**/vendor/**'
  ],
  
  // Diretório para relatórios de cobertura
  coverageDirectory: 'coverage',
  
  // Limiar de cobertura (opcional)
  // coverageThreshold: {
  //   global: {
  //     branches: 80,
  //     functions: 80,
  //     lines: 80,
  //     statements: 80
  //   }
  // },
  
  // Configuração de timeout para testes (10 segundos)
  testTimeout: 10000,
  
  // Configuração para mocks
  clearMocks: true,
  
  // Configuração para resetar mocks entre testes
  resetMocks: true,
  
  // Configuração para restaurar mocks entre testes
  restoreMocks: true,
  
  // Configuração para exibir mensagens de diagnóstico
  verbose: true,
  
  // Configuração para executar setup antes dos testes
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js']
};
