// workingHoursService.js
import { api } from "@/utils/api";
import { exportService } from "@/app/services/exportService";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";

class WorkingHoursService {
  async create(data) {
    try {
      const response = await api.post("/working-hours", data);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar horário<PERSON> de trabalho:", error);
      throw error;
    }
  }

  async list(userId) {
    try {
      const response = await api.get(`/working-hours/${userId}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar horários de trabalho:", error);
      throw error;
    }
  }

  async update(id, data) {
    try {
      const response = await api.put(`/working-hours/${id}`, data);
      return response.data;
    } catch (error) {
      console.error("Erro ao atualizar horário de trabalho:", error);
      throw error;
    }
  }

  async delete(id) {
    try {
      const response = await api.delete(`/working-hours/${id}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao desativar horário de trabalho:", error);
      throw error;
    }
  }

  async setDaySchedule(userId, dayOfWeek, timeSlots) {
    try {
      const response = await api.post(`/working-hours/${userId}/${dayOfWeek}`, {
        timeSlots,
      });
      return response.data;
    } catch (error) {
      console.error("Erro ao definir horários do dia:", error);
      throw error;
    }
  }

  // Método para buscar grade de horários de um usuário para um dia
  async getDayGrid(userId, dayOfWeek) {
    try {
      const response = await api.get(
        `/working-hours/${userId}/${dayOfWeek}/grid`
      );
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar grade de horários:", error);
      throw error;
    }
  }

  // Método para buscar grade de horários de múltiplos usuários
  async getUsersTimeGrid(dayOfWeek, userIds = []) {
    try {
      const response = await api.get(`/working-hours/grid/${dayOfWeek}`, {
        params: { userIds: userIds.join(",") },
      });
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar grade de horários de usuários:", error);
      throw error;
    }
  }

  // Método para buscar grade de horários de toda a semana para múltiplos usuários
  async getWeeklyTimeGrids(userIds = []) {
    try {
      // Dias da semana (1-7, onde 1 é segunda-feira)
      const weekDays = ['1', '2', '3', '4', '5', '6', '7'];
      const weeklyData = {};

      console.log("Buscando dados semanais para os usuários:", userIds);

      // Para cada dia da semana, buscar os dados
      for (const day of weekDays) {
        try {
          console.log(`Buscando dados para o dia ${day}...`);
          const response = await api.get(`/working-hours/grid/${day}`, {
            params: { userIds: userIds.join(",") },
          });

          console.log(`Dados recebidos para o dia ${day}:`, response.data);
          weeklyData[day] = response.data;

          // Verificar se há dados de disponibilidade
          const users = response.data?.users || [];
          if (users.length > 0) {
            for (const user of users) {
              const availableHours = user.timeGrid ? user.timeGrid.filter(Boolean).length : 0;
              console.log(`Usuário ${user.userId} tem ${availableHours} horas disponíveis no dia ${day}`);
            }
          } else {
            console.log(`Nenhum usuário encontrado para o dia ${day}`);
          }
        } catch (err) {
          console.error(`Erro ao buscar grade de horários para o dia ${day}:`, err);
          // Continuar para o próximo dia mesmo se houver erro
          weeklyData[day] = { users: [] };
        }
      }

      return weeklyData;
    } catch (error) {
      console.error("Erro ao buscar grade de horários semanal:", error);
      throw error;
    }
  }

  // Método para atualizar grade horária
  async updateHourlyGrid(userId, dayOfWeek, timeGrid) {
    try {
      const response = await api.put(
        `/working-hours/hourly-grid/${userId}/${dayOfWeek}`,
        { timeGrid }
      );
      return response.data;
    } catch (error) {
      console.error("Erro ao atualizar grade horária:", error);
      throw error;
    }
  }

  // Método para atualizar múltiplos usuários de uma vez
  async updateMultipleUsersGrid(dayOfWeek, usersGrid) {
    try {
      const response = await api.put(
        `/working-hours/multiple-grids/${dayOfWeek}`,
        { usersGrid }
      );
      return response.data;
    } catch (error) {
      console.error("Erro ao atualizar grades horárias:", error);
      throw error;
    }
  }

  /**
   * Exporta os horários de trabalho dos profissionais selecionados para a semana inteira
   * @param {Array} providers - Lista de profissionais selecionados
   * @param {Object} timeGrids - Dados da grade de horários (ignorado, será buscado novamente)
   * @param {string} dayOfWeek - Dia da semana selecionado (não usado, exportará a semana inteira)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  async exportWorkingHours(providers, timeGrids, dayOfWeek, exportFormat = "xlsx") {
    try {
      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Mapeamento de dias da semana
      const daysOfWeek = {
        '1': 'Segunda-feira',
        '2': 'Terça-feira',
        '3': 'Quarta-feira',
        '4': 'Quinta-feira',
        '5': 'Sexta-feira',
        '6': 'Sábado',
        '7': 'Domingo'
      };

      // Buscar dados da semana inteira para todos os profissionais selecionados
      const providerIds = providers.map(p => p.userId);
      console.log("Exportando horários para os profissionais:", providers);
      const weeklyData = await this.getWeeklyTimeGrids(providerIds);
      console.log("Dados semanais obtidos:", weeklyData);

      // Função auxiliar para agrupar horas contínuas em períodos
      const groupContinuousHours = (timeGrid) => {
        if (!timeGrid || !Array.isArray(timeGrid)) {
          console.log("timeGrid inválido:", timeGrid);
          return [];
        }

        console.log("Processando timeGrid:", timeGrid);

        const periods = [];
        let startHour = null;
        let endHour = null;

        // Percorrer todas as horas (0-23)
        for (let hour = 0; hour <= 24; hour++) {
          // Verificar se a hora atual está disponível (ou se chegamos ao final do dia)
          // Convertemos explicitamente para booleano e tratamos qualquer valor truthy como disponível
          const hourValue = timeGrid[hour];
          const isAvailable = hour < 24 ? Boolean(hourValue) : false;

          console.log(`Hora ${hour}: ${isAvailable ? 'Disponível' : 'Indisponível'}`);

          if (isAvailable) {
            // Se estamos iniciando um novo período
            if (startHour === null) {
              startHour = hour;
              endHour = hour;
            }
            // Se estamos continuando um período existente
            else {
              endHour = hour;
            }
          } else {
            // Se tínhamos um período em andamento e agora terminou
            if (startHour !== null) {
              periods.push({
                start: startHour,
                end: endHour + 1 // +1 porque o período vai até o final da hora
              });
              console.log(`Período adicionado: ${startHour}h-${endHour + 1}h`);
              startHour = null;
              endHour = null;
            }
          }
        }

        console.log("Períodos encontrados:", periods);
        return periods;
      };

      // Função para formatar um período como string (ex: "08h-12h")
      const formatPeriod = (period) => {
        const start = String(period.start).padStart(2, '0');
        const end = String(period.end).padStart(2, '0');
        return `${start}h-${end}h`;
      };

      // Função para formatar múltiplos períodos como string (ex: "08h-12h 13h-17h")
      const formatPeriods = (periods) => {
        if (!periods || periods.length === 0) return "Indisponível";
        return periods.map(formatPeriod).join(' ');
      };

      // Preparar os dados para exportação
      const workingHoursData = [];

      // Para cada profissional
      providers.forEach(provider => {
        // Para cada dia da semana
        for (const day of Object.keys(daysOfWeek)) {
          // Buscar os dados do profissional para este dia
          const dayData = weeklyData[day];
          let userData = dayData?.users?.find(u => u.userId === provider.userId);

          // Se não encontrou dados para este usuário, tente buscar pelo ID como string
          if (!userData && provider.userId) {
            const userIdStr = String(provider.userId);
            const userDataAlt = dayData?.users?.find(u => String(u.userId) === userIdStr);
            if (userDataAlt) {
              console.log(`Encontrado usuário usando ID como string: ${userIdStr}`);
              userData = userDataAlt;
            }
          }

          console.log(`Processando ${provider.userName} para ${daysOfWeek[day]}:`, userData);

          // Agrupar horas contínuas em períodos
          const periods = groupContinuousHours(userData?.timeGrid);

          console.log(`Períodos encontrados para ${provider.userName} em ${daysOfWeek[day]}:`, periods);

          // Adicionar linha para este profissional e dia
          const formattedHorarios = formatPeriods(periods);
          console.log(`Horários formatados: ${formattedHorarios}`);

          workingHoursData.push({
            profissional: provider.userName,
            dia: daysOfWeek[day],
            horarios: formattedHorarios
          });
        }
      });

      // Definição das colunas
      const columns = [
        { key: "profissional", header: "Profissional" },
        { key: "dia", header: "Dia da Semana" },
        { key: "horarios", header: "Horários Disponíveis" }
      ];

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      subtitle += ` | Profissionais: ${providers.length} selecionados`;

      // Exportar os dados
      return await exportService.exportData(workingHoursData, {
        format: exportFormat,
        filename: "horarios-trabalho-semanal",
        columns,
        title: "Horários de Trabalho Semanal",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar horários de trabalho:", error);
      return false;
    }
  }
}

const workingHoursService = new WorkingHoursService();
export default workingHoursService;
