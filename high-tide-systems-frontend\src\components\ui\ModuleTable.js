'use client';

import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Loader2, ArrowUp, <PERSON>Down, Setting<PERSON>, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Componente de tabela modular que se adapta à cor do módulo
 *
 * @param {Object} props
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {Array} props.columns - Array de objetos com as configurações das colunas
 * @param {Array} props.data - Array de objetos com os dados a serem exibidos
 * @param {Function} props.renderRow - Função para renderizar cada linha da tabela
 * @param {boolean} props.isLoading - Se a tabela está carregando
 * @param {string} props.emptyMessage - Mensagem a ser exibida quando não há dados
 * @param {React.ReactNode} props.emptyIcon - Ícone a ser exibido quando não há dados
 * @param {number} props.currentPage - Página atual
 * @param {number} props.totalPages - Total de páginas
 * @param {number} props.totalItems - Total de itens
 * @param {Function} props.onPageChange - Função chamada quando a página muda
 * @param {boolean} props.showPagination - Se deve mostrar a paginação
 * @param {string} props.className - Classes adicionais para a tabela
 * @param {React.ReactNode} props.headerContent - Conteúdo adicional para o cabeçalho
 * @param {string} props.title - Título da tabela
 * @param {Function} props.onSort - Função chamada quando uma coluna é ordenada (opcional)
 * @param {string} props.defaultSortField - Campo padrão para ordenação inicial (opcional)
 * @param {string} props.defaultSortDirection - Direção padrão para ordenação inicial ('asc' ou 'desc', opcional)
 * @param {string} props.tableId - ID único para a tabela, usado para salvar preferências (opcional)
 * @param {boolean} props.enableColumnToggle - Habilita a opção de esconder/mostrar colunas (opcional)
 * @param {number} props.itemsPerPage - Número de itens por página para paginação local (padrão: 10)
 */
const ModuleTable = ({
  moduleColor = 'default',
  columns = [],
  data = [],
  renderRow,
  isLoading = false,
  emptyMessage = "Nenhum item encontrado",
  emptyIcon,
  currentPage = 1,
  totalPages = 1,
  totalItems = 0,
  onPageChange,
  showPagination = true,
  className = '',
  headerContent,
  title,
  onSort,
  defaultSortField = '',
  defaultSortDirection = 'asc',
  tableId,
  enableColumnToggle = false,
  itemsPerPage = 10
}) => {
  // Cores baseadas no módulo
  const colors = {
    default: {
      headerBg: 'bg-neutral-50 dark:bg-gray-700',
      borderColor: 'border-neutral-200 dark:border-gray-700',
      hoverBg: 'hover:bg-neutral-50 dark:hover:bg-gray-700',
      textColor: 'text-neutral-800 dark:text-white',
      mutedTextColor: 'text-neutral-500 dark:text-neutral-400'
    },
    people: {
      headerBg: 'bg-module-people-bg/10 dark:bg-module-people-bg-dark/10',
      borderColor: 'border-module-people-border/30 dark:border-module-people-border-dark/30',
      hoverBg: 'hover:bg-module-people-bg/5 dark:hover:bg-module-people-bg-dark/5',
      textColor: 'text-module-people-text dark:text-module-people-text-dark',
      mutedTextColor: 'text-module-people-text/70 dark:text-module-people-text-dark/70'
    },
    scheduler: {
      headerBg: 'bg-module-scheduler-bg/10 dark:bg-module-scheduler-bg-dark/10',
      borderColor: 'border-module-scheduler-border/30 dark:border-module-scheduler-border-dark/30',
      hoverBg: 'hover:bg-module-scheduler-bg/5 dark:hover:bg-module-scheduler-bg-dark/5',
      textColor: 'text-module-scheduler-text dark:text-module-scheduler-text-dark',
      mutedTextColor: 'text-module-scheduler-text/70 dark:text-module-scheduler-text-dark/70'
    },
    admin: {
      headerBg: 'bg-module-admin-bg/10 dark:bg-module-admin-bg-dark/10',
      borderColor: 'border-module-admin-border/30 dark:border-module-admin-border-dark/30',
      hoverBg: 'hover:bg-module-admin-bg/5 dark:hover:bg-module-admin-bg-dark/5',
      textColor: 'text-module-admin-text dark:text-module-admin-text-dark',
      mutedTextColor: 'text-module-admin-text/70 dark:text-module-admin-text-dark/70'
    },
    financial: {
      headerBg: 'bg-module-financial-bg/10 dark:bg-module-financial-bg-dark/10',
      borderColor: 'border-module-financial-border/30 dark:border-module-financial-border-dark/30',
      hoverBg: 'hover:bg-module-financial-bg/5 dark:hover:bg-module-financial-bg-dark/5',
      textColor: 'text-module-financial-text dark:text-module-financial-text-dark',
      mutedTextColor: 'text-module-financial-text/70 dark:text-module-financial-text-dark/70'
    },
    abaplus: {
      headerBg: 'bg-module-abaplus-bg/10 dark:bg-module-abaplus-bg-dark/10',
      borderColor: 'border-module-abaplus-border/30 dark:border-module-abaplus-border-dark/30',
      hoverBg: 'hover:bg-module-abaplus-bg/5 dark:hover:bg-module-abaplus-bg-dark/5',
      textColor: 'text-module-abaplus-text dark:text-module-abaplus-text-dark',
      mutedTextColor: 'text-module-abaplus-text/70 dark:text-module-abaplus-text-dark/70'
    }
  };

  // Estados para ordenação e visibilidade das colunas
  const [sortField, setSortField] = useState(defaultSortField);
  const [sortDirection, setSortDirection] = useState(defaultSortDirection);
  const [visibleColumns, setVisibleColumns] = useState([]);
  const [showColumnToggle, setShowColumnToggle] = useState(false);

  // Referência para o menu de colunas
  const columnMenuRef = React.useRef(null);

  // Efeito para fechar o menu quando o usuário clicar fora dele
  React.useEffect(() => {
    const handleClickOutside = (event) => {
      if (columnMenuRef.current && !columnMenuRef.current.contains(event.target)) {
        setShowColumnToggle(false);
      }
    };

    if (showColumnToggle) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showColumnToggle]);

  // Obter as cores do módulo atual
  const moduleColors = colors[moduleColor] || colors.default;

  // Efeito para inicializar as colunas visíveis e carregar preferências salvas
  useEffect(() => {
    // Inicializa todas as colunas como visíveis por padrão
    // Garante que cada coluna tenha um identificador único
    const initialVisibleColumns = columns.map(col => {
      // Se a coluna não tiver um identificador, usa o header como fallback
      return col.field || col.accessor || col.id || col.header;
    });

    if (tableId && typeof window !== 'undefined') {
      // Tenta carregar as preferências salvas
      try {
        const savedPreferences = localStorage.getItem(`table-prefs-${tableId}`);
        if (savedPreferences) {
          const { visibleCols, sortF, sortDir } = JSON.parse(savedPreferences);
          if (visibleCols && Array.isArray(visibleCols)) {
            setVisibleColumns(visibleCols);
          } else {
            setVisibleColumns(initialVisibleColumns);
          }

          if (sortF) setSortField(sortF);
          if (sortDir) setSortDirection(sortDir);
          return;
        }
      } catch (error) {
        console.error('Erro ao carregar preferências da tabela:', error);
      }
    }

    // Se não houver preferências salvas, usa os valores padrão
    setVisibleColumns(initialVisibleColumns);
  }, [columns, tableId, defaultSortField, defaultSortDirection]);

  // Função para salvar as preferências do usuário
  const savePreferences = (newVisibleColumns, newSortField, newSortDirection) => {
    if (tableId && typeof window !== 'undefined') {
      try {
        localStorage.setItem(`table-prefs-${tableId}`, JSON.stringify({
          visibleCols: newVisibleColumns || visibleColumns,
          sortF: newSortField || sortField,
          sortDir: newSortDirection || sortDirection
        }));
      } catch (error) {
        console.error('Erro ao salvar preferências da tabela:', error);
      }
    }
  };

  // Função para alternar a visibilidade de uma coluna
  const toggleColumnVisibility = (columnId) => {
    const newVisibleColumns = visibleColumns.includes(columnId)
      ? visibleColumns.filter(id => id !== columnId)
      : [...visibleColumns, columnId];

    setVisibleColumns(newVisibleColumns);
    savePreferences(newVisibleColumns);
  };

  // Função para ordenar os dados quando uma coluna é clicada
  const handleSort = (columnId) => {
    // Usar o valor padrão de ordenação das props
    const defaultField = defaultSortField || 'id';
    let newDirection = 'asc';

    if (sortField === columnId) {
      // Ciclo: asc -> desc -> none
      if (sortDirection === 'asc') {
        newDirection = 'desc';
      } else if (sortDirection === 'desc') {
        // Volta ao estado original (sem ordenação)
        // Usar o valor padrão para o campo de ordenação em vez de string vazia
        setSortField(defaultField);
        setSortDirection('asc');
        savePreferences(null, defaultField, 'asc');

        // Se houver uma função de ordenação externa, chama com valores padrão
        if (onSort) {
          onSort(defaultField, 'asc');
        }
        return;
      }
    }

    console.log(`ModuleTable - Alterando ordenação: campo=${columnId}, direção=${newDirection}`);
    setSortField(columnId);
    setSortDirection(newDirection);
    savePreferences(null, columnId, newDirection);

    // Se houver uma função de ordenação externa, chama com os novos valores
    if (onSort) {
      onSort(columnId, newDirection);
    }
  };

  // Filtra as colunas visíveis
  const filteredColumns = columns.filter(col => {
    const columnId = col.field || col.accessor || col.id || col.header;
    return visibleColumns.includes(columnId);
  });

  // Ordena os dados localmente e aplica paginação no frontend
  const { sortedData, paginatedData, calculatedTotalPages } = React.useMemo(() => {
    // Primeiro, ordenamos todos os dados
    let allSortedData = [...data];

    // Se temos um campo de ordenação e não há função de ordenação externa, ordenamos localmente
    if (sortField && !onSort) {
      allSortedData.sort((a, b) => {
        const column = columns.find(col => (col.field || col.accessor || col.id || col.header) === sortField);

        if (!column) return 0;

        // Obtém os valores a serem comparados
        let aValue = a[sortField];
        let bValue = b[sortField];

        // Se houver uma função de acesso personalizada, usa-a
        if (column.accessor && typeof column.accessor === 'function') {
          aValue = column.accessor(a);
          bValue = column.accessor(b);
        }

        // Se o tipo de dados for especificado, converte os valores
        if (column.dataType) {
          switch (column.dataType) {
            case 'number':
              // Converte para número, removendo formatação de moeda se necessário
              aValue = typeof aValue === 'string'
                ? Number(aValue.replace(/[^0-9,-]+/g, '').replace(',', '.'))
                : Number(aValue);
              bValue = typeof bValue === 'string'
                ? Number(bValue.replace(/[^0-9,-]+/g, '').replace(',', '.'))
                : Number(bValue);
              break;
            case 'date':
              // Converte para objeto Date
              aValue = aValue instanceof Date ? aValue : new Date(aValue);
              bValue = bValue instanceof Date ? bValue : new Date(bValue);
              break;
          }
        }

        // Se houver uma função de ordenação personalizada na coluna, usa-a
        if (column.sortFn && typeof column.sortFn === 'function') {
          return column.sortFn(a, b, sortDirection);
        }

        // Ordenação padrão
        if (aValue === bValue) return 0;
        if (aValue === null || aValue === undefined) return 1;
        if (bValue === null || bValue === undefined) return -1;

        // Ordenação de strings
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return sortDirection === 'asc'
            ? aValue.localeCompare(bValue, 'pt-BR', { sensitivity: 'base' })
            : bValue.localeCompare(aValue, 'pt-BR', { sensitivity: 'base' });
        }

        // Ordenação numérica ou de datas
        return sortDirection === 'asc' ? (aValue > bValue ? 1 : -1) : (aValue > bValue ? -1 : 1);
      });
    }

    // IMPORTANTE: Se estamos recebendo dados paginados do backend (totalItems e totalPages fornecidos),
    // não aplicamos paginação local, pois os dados já estão paginados
    let paginatedItems;
    let calculatedTotal;

    if (totalItems && totalPages) {
      // Dados já estão paginados pelo backend, usamos diretamente
      paginatedItems = allSortedData;
      calculatedTotal = totalPages;
    } else {
      // Aplicamos paginação local usando o itemsPerPage fornecido
      calculatedTotal = Math.ceil(allSortedData.length / itemsPerPage) || 1;

      const startIndex = (currentPage - 1) * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      paginatedItems = allSortedData.slice(startIndex, endIndex);
    }

    return {
      sortedData: allSortedData,
      paginatedData: paginatedItems,
      calculatedTotalPages: calculatedTotal
    };
  }, [data, sortField, sortDirection, columns, onSort, currentPage, totalPages, totalItems, itemsPerPage]);

  // Renderiza o estado vazio (loading ou sem dados)
  const renderEmptyState = () => {
    if (isLoading) {
      return (
        <tr>
          <td colSpan={filteredColumns.length} className="py-8 text-center">
            <div className="flex flex-col items-center justify-center">
              <Loader2 className={`h-8 w-8 animate-spin ${moduleColors.textColor}`} />
              <p className={`mt-2 ${moduleColors.mutedTextColor}`}>Carregando...</p>
            </div>
          </td>
        </tr>
      );
    }

    return (
      <tr>
        <td colSpan={filteredColumns.length} className="py-12 text-center">
          <div className="flex flex-col items-center justify-center">
            {emptyIcon && (
              <div className={`h-12 w-12 mb-3 ${moduleColors.mutedTextColor}`}>
                {emptyIcon}
              </div>
            )}
            <p className={`text-lg font-medium ${moduleColors.textColor}`}>{emptyMessage}</p>
          </div>
        </td>
      </tr>
    );
  };

  return (
    <div className={cn(
      "bg-white dark:bg-gray-800 rounded-xl shadow-soft dark:shadow-lg dark:shadow-black/30 border overflow-hidden",
      moduleColors.borderColor,
      className
    )}>
      {/* Cabeçalho opcional */}
      {(title || headerContent || enableColumnToggle) && (
        <div className={cn(
          "px-6 py-4 border-b flex justify-between items-center",
          moduleColors.borderColor
        )}>
          <div className="flex items-center gap-2">
            {title && (
              <h3 className={`text-lg font-medium ${moduleColors.textColor}`}>
                {title}
              </h3>
            )}
          </div>

          <div className="flex items-center gap-2">
            {enableColumnToggle && (
              <div className="relative">
                <button
                  onClick={() => setShowColumnToggle(!showColumnToggle)}
                  className={cn(
                    "p-2 rounded-lg border flex items-center gap-1 text-sm",
                    moduleColors.borderColor,
                    moduleColors.textColor,
                    moduleColors.hoverBg
                  )}
                  title="Configurar colunas"
                >
                  <Settings size={16} />
                  <span className="hidden sm:inline">Colunas</span>
                </button>

                {showColumnToggle && (
                  <div
                    ref={columnMenuRef}
                    className="absolute right-0 mt-2 w-64 bg-white dark:bg-gray-800 border rounded-lg shadow-lg z-50 p-3 space-y-2"
                    style={{ minWidth: '200px' }}
                  >
                    <div className="font-medium text-sm border-b pb-2 mb-2 flex justify-between items-center">
                      <span>Colunas visíveis</span>
                      <button
                        onClick={() => setShowColumnToggle(false)}
                        className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                      >
                        ×
                      </button>
                    </div>
                    {columns.map((column) => {
                      const columnId = column.field || column.accessor || column.id || column.header;
                      const isVisible = visibleColumns.includes(columnId);
                      return (
                        <div key={columnId} className="flex items-center gap-2 py-1">
                          <button
                            onClick={() => toggleColumnVisibility(columnId)}
                            className={cn(
                              "flex items-center justify-center w-5 h-5 rounded border",
                              isVisible ? `bg-module-${moduleColor}-bg dark:bg-module-${moduleColor}-bg-dark border-module-${moduleColor}-border dark:border-module-${moduleColor}-border-dark` : "bg-white dark:bg-gray-700 border-neutral-300 dark:border-gray-600"
                            )}
                          >
                            {isVisible && <Check size={12} className="text-white" />}
                          </button>
                          <span className={moduleColors.textColor}>
                            {column.header}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            )}
            {headerContent}
          </div>
        </div>
      )}

      {/* Tabela */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-neutral-200 dark:divide-gray-700">
          <thead className={moduleColors.headerBg}>
            <tr>
              {filteredColumns.map((column, index) => {
                const columnId = column.field || column.accessor || column.id || column.header;
                const isSortable = column.sortable !== false && (column.field || column.accessor);
                const isSorted = sortField === columnId;
                const sortIcon = isSorted ? (
                  sortDirection === 'asc' ? <ArrowUp size={14} /> : <ArrowDown size={14} />
                ) : null;

                return (
                  <th
                    key={index}
                    className={cn(
                      "px-6 py-3 text-left text-xs font-medium uppercase tracking-wider",
                      moduleColors.mutedTextColor,
                      column.className,
                      isSortable && "cursor-pointer hover:bg-black/5 dark:hover:bg-white/5"
                    )}
                    style={column.width ? { width: column.width } : {}}
                    onClick={isSortable ? () => handleSort(columnId) : undefined}
                  >
                    <div className="flex items-center gap-1">
                      <span>{column.header}</span>
                      {isSortable && (
                        <span className={cn(
                          "transition-opacity",
                          isSorted ? "opacity-100" : "opacity-0 group-hover:opacity-30"
                        )}>
                          {sortIcon || <ArrowUp size={14} className="opacity-0" />}
                        </span>
                      )}
                    </div>
                  </th>
                );
              })}
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-neutral-200 dark:divide-gray-700">
            {isLoading ? (
              renderEmptyState()
            ) : data.length === 0 || paginatedData.length === 0 ? (
              renderEmptyState()
            ) : (
              <>
                {console.log("ModuleTable - Dados para renderizar:", paginatedData)}
                {console.log("ModuleTable - Colunas visíveis:", visibleColumns)}
                {paginatedData.map((item, index) => renderRow(item, index, moduleColors, visibleColumns))}
              </>
            )}
          </tbody>
        </table>
      </div>

      {/* Paginação */}
      {showPagination && (
        <div className={cn(
          "px-6 py-4 flex items-center justify-between border-t",
          moduleColors.borderColor
        )}>
          <div className={`text-sm ${moduleColors.mutedTextColor}`}>
            Mostrando <span className="font-medium">{paginatedData.length}</span> de{" "}
            <span className="font-medium">{totalItems || sortedData.length}</span> itens
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={cn(
                "px-3 py-1 border rounded-md text-sm flex items-center gap-1",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                moduleColors.borderColor,
                moduleColors.textColor
              )}
            >
              <ChevronLeft className="h-4 w-4" />
              Anterior
            </button>

            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages || calculatedTotalPages }, (_, i) => i + 1)
                .filter(
                  (page) =>
                    page === 1 ||
                    page === (totalPages || calculatedTotalPages) ||
                    (page >= currentPage - 1 && page <= currentPage + 1)
                )
                .map((page, index, array) => {
                  // Adicionar indicador de elipse
                  if (index > 0 && array[index - 1] !== page - 1) {
                    return (
                      <React.Fragment key={`ellipsis-${page}`}>
                        <span className={`px-2 py-1 ${moduleColors.mutedTextColor}`}>...</span>
                        <button
                          onClick={() => onPageChange(page)}
                          className={cn(
                            "px-3 py-1 rounded-md text-sm",
                            currentPage === page
                              ? `bg-module-${moduleColor}-bg dark:bg-module-${moduleColor}-bg-dark text-module-${moduleColor}-text dark:text-module-${moduleColor}-text-dark`
                              : `border ${moduleColors.borderColor} ${moduleColors.textColor}`
                          )}
                        >
                          {page}
                        </button>
                      </React.Fragment>
                    );
                  }

                  return (
                    <button
                      key={page}
                      onClick={() => onPageChange(page)}
                      className={cn(
                        "px-3 py-1 rounded-md text-sm",
                        currentPage === page
                          ? `bg-module-${moduleColor}-bg dark:bg-module-${moduleColor}-bg-dark text-module-${moduleColor}-text dark:text-module-${moduleColor}-text-dark`
                          : `border ${moduleColors.borderColor} ${moduleColors.textColor}`
                      )}
                    >
                      {page}
                    </button>
                  );
                })}
            </div>

            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === (totalPages || calculatedTotalPages)}
              className={cn(
                "px-3 py-1 border rounded-md text-sm flex items-center gap-1",
                "disabled:opacity-50 disabled:cursor-not-allowed",
                moduleColors.borderColor,
                moduleColors.textColor
              )}
            >
              Próximo
              <ChevronRight className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// FilterSection component has been moved to ./ModuleTable/FilterSection.js

// Note: FilterSection is now imported from ./ModuleTable/FilterSection.js
// and attached to ModuleTable in ./ModuleTable/index.js

export default ModuleTable;
