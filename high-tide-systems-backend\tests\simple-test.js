// tests/simple-test.js
const axios = require('axios');

// Configuração
const API_URL = 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes simples...');

  try {
    // Teste 1: Listar pessoas
    console.log('\n1. Listando pessoas...');
    const listPersonsResponse = await api.get('/persons');

    if (listPersonsResponse.status === 200) {
      console.log(`✅ ${listPersonsResponse.data.people.length} pessoas encontradas`);
    } else {
      console.log('❌ Falha ao listar pessoas');
    }

    // Teste 2: Criar uma pessoa
    console.log('\n2. Criando pessoa...');
    const testPerson = {
      fullName: `Test Person ${Date.now()}`,
      cpf: `${Date.now()}`.substring(0, 11),
      birthDate: '1990-01-01',
      address: 'Test Address',
      neighborhood: 'Test Neighborhood',
      city: 'Test City',
      state: 'TS',
      postalCode: '12345-678',
      phone: '1234567890',
      email: `test_person_${Date.now()}@example.com`,
      gender: 'M',
      notes: 'Test notes'
    };

    const createPersonResponse = await api.post('/persons', testPerson);

    if (createPersonResponse.status === 201) {
      console.log('✅ Pessoa criada com sucesso!');
      const personId = createPersonResponse.data.id;
      console.log(`ID da pessoa: ${personId}`);

      // Teste 3: Buscar a pessoa criada
      console.log('\n3. Buscando pessoa criada...');
      const getPersonResponse = await api.get(`/persons/${personId}`);

      if (getPersonResponse.status === 200) {
        console.log('✅ Pessoa encontrada com sucesso!');
        console.log(`Nome: ${getPersonResponse.data.fullName}`);
      } else {
        console.log('❌ Falha ao buscar pessoa');
      }

      // Teste 4: Excluir a pessoa criada
      console.log('\n4. Excluindo pessoa criada...');
      const deletePersonResponse = await api.delete(`/persons/${personId}`);

      if (deletePersonResponse.status === 204) {
        console.log('✅ Pessoa excluída com sucesso!');
      } else {
        console.log('❌ Falha ao excluir pessoa');
      }
    } else {
      console.log('❌ Falha ao criar pessoa');
    }

    console.log('\n✅ Testes concluídos com sucesso!');

  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
