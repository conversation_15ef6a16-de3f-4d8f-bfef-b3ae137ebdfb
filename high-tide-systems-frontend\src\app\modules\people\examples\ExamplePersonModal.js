'use client';

import React, { useState } from 'react';
import { User, Mail, Phone, Calendar, CreditCard } from 'lucide-react';
import ModuleModal from '@/components/ui/ModuleModal';
import ModalButton from '@/components/ui/ModalButton';
import { ModuleInput, ModuleSelect, ModuleFormGroup } from '@/components/ui';

const ExamplePersonModal = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    birthdate: '',
    document: '',
    gender: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulação de envio de dados
    await new Promise(resolve => setTimeout(resolve, 1500));

    setIsLoading(false);
    onClose();
  };

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="people"
        onClick={onClose}
        disabled={isLoading}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="people"
        onClick={handleSubmit}
        isLoading={isLoading}
      >
        Salvar
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title="Novo Paciente"
      icon={<User size={22} />}
      moduleColor="people"
      size="md"
      animateExit={true}
      footer={modalFooter}
    >
      <form className="space-y-6">
        <div className="space-y-4">
          <ModuleFormGroup
            moduleColor="people"
            label="Nome Completo"
            htmlFor="name"
            icon={<User size={16} />}
            required
          >
            <ModuleInput
              moduleColor="people"
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              placeholder="Digite o nome completo"
            />
          </ModuleFormGroup>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ModuleFormGroup
              moduleColor="people"
              label="E-mail"
              htmlFor="email"
              icon={<Mail size={16} />}
            >
              <ModuleInput
                moduleColor="people"
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Digite o e-mail"
              />
            </ModuleFormGroup>

            <ModuleFormGroup
              moduleColor="people"
              label="Telefone"
              htmlFor="phone"
              icon={<Phone size={16} />}
            >
              <ModuleInput
                moduleColor="people"
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="(00) 00000-0000"
              />
            </ModuleFormGroup>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <ModuleFormGroup
            moduleColor="people"
            label="Data de Nascimento"
            htmlFor="birthdate"
            icon={<Calendar size={16} />}
          >
            <ModuleInput
              moduleColor="people"
              type="date"
              id="birthdate"
              name="birthdate"
              value={formData.birthdate}
              onChange={handleChange}
            />
          </ModuleFormGroup>

          <ModuleFormGroup
            moduleColor="people"
            label="CPF"
            htmlFor="document"
            icon={<CreditCard size={16} />}
          >
            <ModuleInput
              moduleColor="people"
              type="text"
              id="document"
              name="document"
              value={formData.document}
              onChange={handleChange}
              placeholder="000.000.000-00"
            />
          </ModuleFormGroup>

          <ModuleFormGroup
            moduleColor="people"
            label="Gênero"
            htmlFor="gender"
          >
            <ModuleSelect
              moduleColor="people"
              id="gender"
              name="gender"
              value={formData.gender}
              onChange={handleChange}
              placeholder="Selecione"
            >
              <option value="">Selecione</option>
              <option value="M">Masculino</option>
              <option value="F">Feminino</option>
              <option value="O">Outro</option>
              <option value="N">Prefiro não informar</option>
            </ModuleSelect>
          </ModuleFormGroup>
        </div>
      </form>
    </ModuleModal>
  );
};

export default ExamplePersonModal;
