"use client";

import React, { useState, useEffect } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleSelect, ModuleTable } from "@/components/ui";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Activity,
  Power,
  CheckCircle,
  XCircle,
  Eye
} from "lucide-react";
import { skillsService } from "@/app/modules/aba/services/skillsService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import { useToast } from "@/contexts/ToastContext";

const SkillsPage = () => {
  const { user } = useAuth();
  const { toast_success, toast_error } = useToast();
  const [skills, setSkills] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filters, setFilters] = useState({
    search: "",
    active: true,
    page: 1,
    limit: 10
  });
  const [totalItems, setTotalItems] = useState(0);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedSkill, setSelectedSkill] = useState(null);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);

  // Carregar habilidades
  useEffect(() => {
    const loadSkills = async () => {
      try {
        setIsLoading(true);
        // Quando a API estiver pronta, substituir por chamada real
        // const response = await skillsService.getSkills(filters);
        // setSkills(response.items);
        // setTotalItems(response.total);
        
        // Dados de exemplo para demonstração
        const mockSkills = [
          {
            id: "1",
            code: "COM-001",
            order: 1,
            description: "Comunicação Verbal - Básico",
            active: true,
            createdAt: "2023-06-01T10:00:00Z"
          },
          {
            id: "2",
            code: "COM-002",
            order: 2,
            description: "Comunicação Verbal - Intermediário",
            active: true,
            createdAt: "2023-06-02T10:00:00Z"
          },
          {
            id: "3",
            code: "COM-003",
            order: 3,
            description: "Comunicação Verbal - Avançado",
            active: true,
            createdAt: "2023-06-03T10:00:00Z"
          },
          {
            id: "4",
            code: "SOC-001",
            order: 1,
            description: "Habilidades Sociais - Básico",
            active: true,
            createdAt: "2023-06-04T10:00:00Z"
          },
          {
            id: "5",
            code: "SOC-002",
            order: 2,
            description: "Habilidades Sociais - Intermediário",
            active: false,
            createdAt: "2023-06-05T10:00:00Z"
          }
        ];
        
        setSkills(mockSkills);
        setTotalItems(mockSkills.length);
      } catch (error) {
        console.error("Erro ao carregar habilidades:", error);
        toast_error("Não foi possível carregar as habilidades");
      } finally {
        setIsLoading(false);
      }
    };

    loadSkills();
  }, [filters, toast_error]);

  // Manipuladores de eventos
  const handleSearch = (e) => {
    e.preventDefault();
    setFilters({ ...filters, page: 1 });
  };

  const handleFilterChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFilters({
      ...filters,
      [name]: type === "checkbox" ? checked : value,
      page: 1
    });
  };

  const handlePageChange = (page) => {
    setFilters({ ...filters, page });
  };

  const handleRefresh = () => {
    setFilters({ ...filters });
  };

  const handleOpenFormModal = (skill = null) => {
    setSelectedSkill(skill);
    setIsFormModalOpen(true);
  };

  const handleCloseFormModal = () => {
    setSelectedSkill(null);
    setIsFormModalOpen(false);
  };

  const handleOpenDeleteModal = (skill) => {
    setSelectedSkill(skill);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setSelectedSkill(null);
    setIsDeleteModalOpen(false);
  };

  const handleOpenStatusModal = (skill) => {
    setSelectedSkill(skill);
    setIsStatusModalOpen(true);
  };

  const handleCloseStatusModal = () => {
    setSelectedSkill(null);
    setIsStatusModalOpen(false);
  };

  const handleDeleteSkill = async () => {
    if (!selectedSkill) return;

    try {
      setIsLoading(true);
      // Quando a API estiver pronta, substituir por chamada real
      // await skillsService.deleteSkill(selectedSkill.id);
      toast_success("Habilidade excluída com sucesso");
      setFilters({ ...filters });
    } catch (error) {
      console.error("Erro ao excluir habilidade:", error);
      toast_error("Não foi possível excluir a habilidade");
    } finally {
      setIsLoading(false);
      handleCloseDeleteModal();
    }
  };

  const handleToggleStatus = async () => {
    if (!selectedSkill) return;

    try {
      setIsLoading(true);
      // Quando a API estiver pronta, substituir por chamada real
      // await skillsService.toggleSkillStatus(selectedSkill.id);
      toast_success(`Habilidade ${selectedSkill.active ? "desativada" : "ativada"} com sucesso`);
      setFilters({ ...filters });
    } catch (error) {
      console.error("Erro ao alterar status da habilidade:", error);
      toast_error("Não foi possível alterar o status da habilidade");
    } finally {
      setIsLoading(false);
      handleCloseStatusModal();
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <ModuleHeader
        title="Habilidades"
        description="Gerencie as habilidades e competências do módulo ABA+"
        moduleColor="abaplus"
        tutorialName="abaplus-skills"
      >
        <TutorialTriggerButton tutorialName="abaplus-skills" />
        <button
          onClick={handleRefresh}
          className="p-2 text-teal-600 hover:bg-teal-50 dark:text-teal-400 dark:hover:bg-teal-900/20 rounded-lg transition-colors"
          aria-label="Atualizar lista"
        >
          <RefreshCw size={20} />
        </button>
        <FilterButton
          isOpen={isFilterOpen}
          onClick={() => setIsFilterOpen(!isFilterOpen)}
          moduleColor="abaplus"
        />
        <button
          onClick={() => handleOpenFormModal()}
          className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center gap-2"
        >
          <Plus size={18} />
          Nova Habilidade
        </button>
      </ModuleHeader>

      {/* Tutorial Manager */}
      <TutorialManager
        name="abaplus-skills"
        steps={[
          {
            target: ".tutorial-search",
            content: "Utilize a barra de pesquisa para encontrar habilidades específicas.",
            placement: "bottom"
          },
          {
            target: ".tutorial-filters",
            content: "Aplique filtros para refinar sua busca de habilidades.",
            placement: "bottom"
          },
          {
            target: ".tutorial-table",
            content: "Visualize todas as habilidades cadastradas e gerencie-as através das ações disponíveis.",
            placement: "top"
          }
        ]}
      />

      {/* Barra de pesquisa */}
      <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md tutorial-search">
        <form onSubmit={handleSearch} className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <ModuleInput
              name="search"
              value={filters.search}
              onChange={handleFilterChange}
              placeholder="Pesquisar por código ou descrição..."
              icon={<Search size={18} />}
              moduleColor="abaplus"
              className="w-full"
            />
          </div>
          <button
            type="submit"
            className="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg shadow transition-colors flex items-center justify-center gap-2"
          >
            <Search size={18} />
            Pesquisar
          </button>
        </form>
      </div>

      {/* Filtros */}
      {isFilterOpen && (
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md tutorial-filters">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Status
              </label>
              <ModuleSelect
                name="active"
                value={filters.active === "" ? "" : filters.active ? "true" : "false"}
                onChange={(e) => {
                  const value = e.target.value;
                  setFilters({
                    ...filters,
                    active: value === "" ? "" : value === "true",
                    page: 1
                  });
                }}
                moduleColor="abaplus"
              >
                <option value="">Todos</option>
                <option value="true">Ativos</option>
                <option value="false">Inativos</option>
              </ModuleSelect>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Itens por página
              </label>
              <ModuleSelect
                name="limit"
                value={filters.limit}
                onChange={handleFilterChange}
                moduleColor="abaplus"
              >
                <option value="10">10</option>
                <option value="25">25</option>
                <option value="50">50</option>
                <option value="100">100</option>
              </ModuleSelect>
            </div>
          </div>
        </div>
      )}

      {/* Tabela de Habilidades */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md overflow-hidden tutorial-table">
        <ModuleTable
          columns={[
            { key: "code", header: "Código" },
            { key: "order", header: "Ordem" },
            { key: "description", header: "Descrição" },
            { key: "active", header: "Status" },
            { key: "actions", header: "Ações" }
          ]}
          data={skills}
          isLoading={isLoading}
          pagination={{
            currentPage: filters.page,
            totalItems,
            itemsPerPage: filters.limit,
            onPageChange: handlePageChange
          }}
          emptyMessage="Nenhuma habilidade encontrada"
          emptyIcon={<Activity size={24} />}
          tableId="skills-table"
          defaultSortField="order"
          defaultSortDirection="asc"
          renderRow={(skill, _index, moduleColors, visibleColumns) => (
            <tr key={skill.id} className={moduleColors.hoverBg}>
              {visibleColumns.includes("code") && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">{skill.code}</div>
                </td>
              )}
              {visibleColumns.includes("order") && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{skill.order}</div>
                </td>
              )}
              {visibleColumns.includes("description") && (
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900 dark:text-gray-100">{skill.description}</div>
                </td>
              )}
              {visibleColumns.includes("active") && (
                <td className="px-6 py-4 whitespace-nowrap">
                  {skill.active ? (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      <CheckCircle size={16} className="mr-1" /> Ativo
                    </span>
                  ) : (
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                      <XCircle size={16} className="mr-1" /> Inativo
                    </span>
                  )}
                </td>
              )}
              {visibleColumns.includes("actions") && (
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => handleOpenFormModal(skill)}
                      className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                      aria-label="Editar"
                    >
                      <Edit size={18} />
                    </button>
                    <button
                      onClick={() => handleOpenStatusModal(skill)}
                      className={`${
                        skill.active
                          ? "text-amber-600 hover:text-amber-900 dark:text-amber-400 dark:hover:text-amber-300"
                          : "text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                      }`}
                      aria-label={skill.active ? "Desativar" : "Ativar"}
                    >
                      <Power size={18} />
                    </button>
                    <button
                      onClick={() => handleOpenDeleteModal(skill)}
                      className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                      aria-label="Excluir"
                    >
                      <Trash size={18} />
                    </button>
                  </div>
                </td>
              )}
            </tr>
          )}
          moduleColor="abaplus"
        />
      </div>

      {/* Modal de Confirmação de Exclusão */}
      <ConfirmationDialog
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleDeleteSkill}
        title="Excluir Habilidade"
        message={`Tem certeza que deseja excluir a habilidade "${selectedSkill?.description}"? Esta ação não pode ser desfeita.`}
        confirmText="Excluir"
        cancelText="Cancelar"
        confirmVariant="danger"
      />

      {/* Modal de Confirmação de Alteração de Status */}
      <ConfirmationDialog
        isOpen={isStatusModalOpen}
        onClose={handleCloseStatusModal}
        onConfirm={handleToggleStatus}
        title={selectedSkill?.active ? "Desativar Habilidade" : "Ativar Habilidade"}
        message={`Tem certeza que deseja ${
          selectedSkill?.active ? "desativar" : "ativar"
        } a habilidade "${selectedSkill?.description}"?`}
        confirmText={selectedSkill?.active ? "Desativar" : "Ativar"}
        cancelText="Cancelar"
        confirmVariant={selectedSkill?.active ? "warning" : "success"}
      />

      {/* Aqui seria adicionado o modal de formulário para criar/editar habilidades */}
      {/* <SkillFormModal
        isOpen={isFormModalOpen}
        onClose={handleCloseFormModal}
        skill={selectedSkill}
        onSuccess={() => {
          handleCloseFormModal();
          setFilters({ ...filters });
        }}
      /> */}
    </div>
  );
};

export default SkillsPage;
