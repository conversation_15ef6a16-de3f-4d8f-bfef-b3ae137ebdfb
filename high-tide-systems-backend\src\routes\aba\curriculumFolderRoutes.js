// src/routes/aba/curriculumFolderRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { CurriculumFolderController, createCurriculumFolderValidation, updateCurriculumFolderValidation } = require('../../controllers/aba/curriculumFolderController');
const { CurriculumFolderProgramController, createCurriculumFolderProgramValidation, addExistingProgramValidation } = require('../../controllers/aba/curriculumFolderProgramController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para pastas curriculares
router.post('/', createCurriculumFolderValidation, CurriculumFolderController.create);
router.get('/', CurriculumFolderController.list);
router.get('/:id', CurriculumFolderController.get);
router.put('/:id', updateCurriculumFolderValidation, CurriculumFolderController.update);
router.patch('/:id/status', CurriculumFolderController.toggleStatus);
router.delete('/:id', CurriculumFolderController.delete);

// Rotas para programas de pastas curriculares
router.get('/:folderId/programs', CurriculumFolderProgramController.list);
router.post('/:folderId/programs', createCurriculumFolderProgramValidation, CurriculumFolderProgramController.create);
router.post('/:folderId/programs/add', addExistingProgramValidation, CurriculumFolderProgramController.addExistingProgram);
router.get('/:folderId/programs/:programId', CurriculumFolderProgramController.get);
router.put('/:folderId/programs/:programId', CurriculumFolderProgramController.update);
router.delete('/:folderId/programs/:programId', CurriculumFolderProgramController.delete);

module.exports = router;
