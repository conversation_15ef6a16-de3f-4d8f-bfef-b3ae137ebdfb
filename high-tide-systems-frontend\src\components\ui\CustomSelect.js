'use client';

import React, { useState, useRef, useEffect, forwardRef } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDown, Search } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Componente de select personalizado que suporta estilização completa
 */
const CustomSelect = forwardRef(({
  options = [],
  value,
  onChange,
  placeholder,
  disabled = false,
  required = false,
  moduleColor = 'default',
  error = false,
  className = '',
  name,
  id,
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [search, setSearch] = useState('');
  const containerRef = useRef(null); // Referência para o container do select
  const dropdownRef = useRef(null); // Referência para o dropdown
  const buttonRef = useRef(null);
  const hiddenInputRef = useRef(null);
  const searchInputRef = useRef(null);
  const [mounted, setMounted] = useState(false);

  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      focusRing: 'focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-gray-400 dark:text-gray-500',
      hoverBg: 'hover:bg-primary-500 dark:hover:bg-primary-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-primary-600 dark:bg-primary-700',
      activeText: 'text-white dark:text-white',
    },
    people: {
      focusRing: 'focus:ring-module-people-border focus:border-module-people-border dark:focus:ring-module-people-border-dark dark:focus:border-module-people-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-people-icon dark:text-module-people-icon-dark',
      hoverBg: 'hover:bg-orange-500 dark:hover:bg-orange-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-orange-600 dark:bg-orange-700',
      activeText: 'text-white dark:text-white',
    },
    scheduler: {
      focusRing: 'focus:ring-module-scheduler-border focus:border-module-scheduler-border dark:focus:ring-module-scheduler-border-dark dark:focus:border-module-scheduler-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-scheduler-icon dark:text-module-scheduler-icon-dark',
      hoverBg: 'hover:bg-purple-500 dark:hover:bg-purple-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-purple-600 dark:bg-purple-700',
      activeText: 'text-white dark:text-white',
    },
    admin: {
      focusRing: 'focus:ring-module-admin-border focus:border-module-admin-border dark:focus:ring-module-admin-border-dark dark:focus:border-module-admin-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-admin-icon dark:text-module-admin-icon-dark',
      hoverBg: 'hover:bg-slate-500 dark:hover:bg-slate-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-slate-600 dark:bg-slate-700',
      activeText: 'text-white dark:text-white',
    },
    financial: {
      focusRing: 'focus:ring-module-financial-border focus:border-module-financial-border dark:focus:ring-module-financial-border-dark dark:focus:border-module-financial-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-financial-icon dark:text-module-financial-icon-dark',
      hoverBg: 'hover:bg-green-500 dark:hover:bg-green-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-green-600 dark:bg-green-700',
      activeText: 'text-white dark:text-white',
    },
    abaplus: {
      focusRing: 'focus:ring-module-abaplus-border focus:border-module-abaplus-border dark:focus:ring-module-abaplus-border-dark dark:focus:border-module-abaplus-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-abaplus-icon dark:text-module-abaplus-icon-dark',
      hoverBg: 'hover:bg-teal-500 dark:hover:bg-teal-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-teal-600 dark:bg-teal-700',
      activeText: 'text-white dark:text-white',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;

  // Encontrar a opção selecionada
  useEffect(() => {
    const selected = options.find(option => option.value === value);
    setSelectedOption(selected || null);
  }, [value, options]);

  // Montar o componente apenas no cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Verificar se o dropdown deve ser exibido acima ou abaixo do select
  useEffect(() => {
    if (!isOpen || !dropdownRef.current || !containerRef.current) return;

    const checkPosition = () => {
      const containerRect = containerRef.current.getBoundingClientRect();
      const dropdownHeight = dropdownRef.current.offsetHeight;
      const windowHeight = window.innerHeight;
      const spaceBelow = windowHeight - containerRect.bottom;

      // Se não houver espaço suficiente abaixo e houver mais espaço acima
      if (spaceBelow < dropdownHeight && containerRect.top > dropdownHeight) {
        dropdownRef.current.style.top = `${containerRect.top - dropdownHeight}px`;
        dropdownRef.current.style.marginTop = '0';
        dropdownRef.current.style.marginBottom = '4px';
      } else {
        dropdownRef.current.style.top = `${containerRect.bottom}px`;
        dropdownRef.current.style.marginTop = '4px';
        dropdownRef.current.style.marginBottom = '0';
      }
    };

    // Verificar a posição quando o dropdown é aberto
    checkPosition();

    // Verificar novamente após um pequeno atraso para garantir que o dropdown foi renderizado corretamente
    const timer = setTimeout(checkPosition, 50);

    // Adicionar listener para reposicionar o dropdown ao rolar a página
    const handleScroll = () => {
      if (containerRef.current && dropdownRef.current) {
        checkPosition();
      }
    };

    window.addEventListener('scroll', handleScroll, true);
    window.addEventListener('resize', handleScroll);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleScroll);
    };
  }, [isOpen]);

  // Fechar o dropdown quando clicar fora dele
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
        setSearch(''); // Limpar a busca quando fechar o dropdown
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Focar no campo de busca quando o dropdown abrir
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current.focus();
      }, 100);
    }
  }, [isOpen]);

  // Filtrar opções com base na busca
  const filteredOptions = options.filter(option => {
    if (!option || typeof option !== 'object') return false;

    const optionLabel = String(option.label || '').toLowerCase();
    const searchTerm = search.toLowerCase();

    return optionLabel.includes(searchTerm);
  });

  // Manipular a seleção de uma opção
  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    setIsOpen(false);
    setSearch(''); // Limpar a busca quando selecionar uma opção

    // Criar um evento sintético para simular o comportamento do select nativo
    const syntheticEvent = {
      target: {
        name,
        value: option.value
      }
    };

    onChange(syntheticEvent);

    // Atualizar o valor do input oculto para compatibilidade com formulários
    if (hiddenInputRef.current) {
      hiddenInputRef.current.value = option.value;

      // Disparar um evento de change para que os formulários detectem a mudança
      const event = new Event('change', { bubbles: true });
      hiddenInputRef.current.dispatchEvent(event);
    }
  };

  // Classes para o botão do select
  const buttonClasses = cn(
    'w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm',
    'transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200',
    'appearance-none pr-10 text-left focus:outline-none',
    error ? colors.errorBorder : '',
    error ? colors.errorRing : colors.focusRing,
    disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
    className
  );

  return (
    <div className="relative overflow-visible" ref={containerRef}>
      {/* Input oculto para compatibilidade com formulários */}
      <input
        type="hidden"
        name={name}
        id={id}
        value={value || ''}
        required={required}
        ref={hiddenInputRef}
      />

      {/* Botão que simula o select */}
      <button
        type="button"
        className={buttonClasses}
        onClick={() => {
          if (!disabled) {
            setIsOpen(!isOpen);
          }
        }}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        disabled={disabled}
        ref={buttonRef}
      >
        <span className="block truncate">
          {selectedOption ? selectedOption.label : placeholder || 'Selecione...'}
        </span>
        <span className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <ChevronDown className={`h-4 w-4 ${colors.iconColor} transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`} />
        </span>
      </button>

      {/* Dropdown renderizado diretamente no DOM usando portal */}
      {isOpen && mounted && createPortal(
        <div
          ref={dropdownRef}
          className="absolute z-[12000] bg-white dark:bg-gray-800 rounded-md shadow-lg max-h-60 overflow-auto border border-neutral-200 dark:border-neutral-700 transition-opacity duration-150 opacity-100 w-full mt-1"
          style={{
            top: `${containerRef.current?.getBoundingClientRect().bottom}px`,
            left: `${containerRef.current?.getBoundingClientRect().left}px`,
            width: `${containerRef.current?.offsetWidth}px`,
            position: 'fixed'
          }}
        >
          {/* Campo de busca */}
          <div className="p-2 border-b border-neutral-100 dark:border-neutral-700">
            <div className="relative">
              <input
                ref={searchInputRef}
                type="text"
                className={cn(
                  'w-full pl-8 pr-2 py-1.5 text-sm rounded-md focus:outline-none',
                  'border border-neutral-200 dark:border-neutral-600 bg-neutral-50 dark:bg-gray-700',
                  'placeholder-neutral-400 dark:placeholder-neutral-500 text-gray-900 dark:text-gray-200',
                  `focus:ring-2 ${colors.focusRing}`
                )}
                placeholder="Buscar..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                onClick={(e) => e.stopPropagation()}
              />
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400 dark:text-neutral-500" />
            </div>
          </div>

          <ul className="py-1" role="listbox">
            {filteredOptions.map((option) => (
              <li
                key={option.value}
                className={cn(
                  'px-3 py-2 text-sm cursor-pointer transition-colors duration-150',
                  option.value === value ? `${colors.activeBg} ${colors.activeText}` : 'text-gray-900 dark:text-gray-200',
                  colors.hoverBg,
                  colors.hoverText
                )}
                style={{
                  '--tw-bg-opacity': '1',
                  '--tw-text-opacity': '1'
                }}
                onClick={() => handleOptionSelect(option)}
                role="option"
                aria-selected={option.value === value}
              >
                {option.label}
              </li>
            ))}
            {filteredOptions.length === 0 && (
              <li className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                {search ? "Nenhum resultado encontrado" : "Nenhuma opção disponível"}
              </li>
            )}
          </ul>
        </div>,
        document.body
      )}
    </div>
  );
});

CustomSelect.displayName = 'CustomSelect';

export default CustomSelect;
