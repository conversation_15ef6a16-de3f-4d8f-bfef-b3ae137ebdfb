'use client';

import React, { useState, useRef, useEffect, forwardRef } from 'react';
import { createPortal } from 'react-dom';
import { ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Componente de select personalizado para a página de perfil
 * Corrige o problema de posicionamento dos dropdowns
 */
const ProfileSelect = forwardRef(({
  options = [],
  value,
  onChange,
  placeholder,
  disabled = false,
  required = false,
  moduleColor = 'default',
  error = false,
  className = '',
  name,
  id,
}, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0 });
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);
  const hiddenInputRef = useRef(null);
  const [mounted, setMounted] = useState(false);

  // Mapeamento de cores por módulo
  const moduleColors = {
    default: {
      focusRing: 'focus:ring-primary-500 focus:border-primary-500 dark:focus:ring-primary-400 dark:focus:border-primary-400',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-gray-400 dark:text-gray-500',
      hoverBg: 'hover:bg-primary-500 dark:hover:bg-primary-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-primary-600 dark:bg-primary-700',
      activeText: 'text-white dark:text-white',
    },
    admin: {
      focusRing: 'focus:ring-module-admin-border focus:border-module-admin-border dark:focus:ring-module-admin-border-dark dark:focus:border-module-admin-border-dark',
      errorRing: 'focus:ring-red-500 focus:border-red-500 dark:focus:ring-red-400 dark:focus:border-red-400',
      errorBorder: 'border-red-300 dark:border-red-700',
      iconColor: 'text-module-admin-icon dark:text-module-admin-icon-dark',
      hoverBg: 'hover:bg-slate-500 dark:hover:bg-slate-600',
      hoverText: 'hover:text-white dark:hover:text-white',
      activeBg: 'bg-slate-600 dark:bg-slate-700',
      activeText: 'text-white dark:text-white',
    },
  };

  // Obter as cores do módulo atual
  const colors = moduleColors[moduleColor] || moduleColors.default;

  // Encontrar a opção selecionada
  useEffect(() => {
    const selected = options.find(option => option.value === value);
    setSelectedOption(selected || null);
  }, [value, options]);

  // Montar o componente apenas no cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Não precisamos mais calcular a posição do dropdown, pois estamos usando posicionamento absoluto

  // Fechar o dropdown quando clicar fora dele
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Manipular a seleção de uma opção
  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    setIsOpen(false);

    // Criar um evento sintético para simular o comportamento do select nativo
    const syntheticEvent = {
      target: {
        name,
        value: option.value
      }
    };

    onChange(syntheticEvent);

    // Atualizar o valor do input oculto para compatibilidade com formulários
    if (hiddenInputRef.current) {
      hiddenInputRef.current.value = option.value;

      // Disparar um evento de change para que os formulários detectem a mudança
      const event = new Event('change', { bubbles: true });
      hiddenInputRef.current.dispatchEvent(event);
    }
  };

  // Classes para o botão do select
  const buttonClasses = cn(
    'w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-sm',
    'transition-colors duration-200 bg-white dark:bg-gray-700 dark:text-gray-200',
    'appearance-none pr-10 text-left focus:outline-none',
    error ? colors.errorBorder : '',
    error ? colors.errorRing : colors.focusRing,
    disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer',
    className
  );

  // Extrair as opções dos children (elementos option)
  const extractOptions = () => {
    const optionsList = [];
    if (Array.isArray(options)) {
      return options;
    }

    // Se options não for um array, assumimos que são children do React
    React.Children.forEach(options, child => {
      if (child && child.type === 'option') {
        optionsList.push({
          value: child.props.value,
          label: child.props.children,
          disabled: child.props.disabled
        });
      }
    });
    return optionsList;
  };

  const optionsList = extractOptions();

  return (
    <div className={`relative profile-select-container ${isOpen ? 'dropdown-open' : ''}`} ref={ref}>
      {/* Input oculto para compatibilidade com formulários */}
      <input
        type="hidden"
        name={name}
        id={id}
        value={value || ''}
        required={required}
        ref={hiddenInputRef}
      />

      {/* Botão que simula o select */}
      <button
        type="button"
        className={buttonClasses}
        onClick={() => !disabled && setIsOpen(!isOpen)}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        disabled={disabled}
        ref={buttonRef}
      >
        <span className="block truncate">
          {selectedOption ? selectedOption.label : placeholder || 'Selecione...'}
        </span>
        <span className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <ChevronDown className={`h-4 w-4 ${colors.iconColor} transition-transform duration-200 ${isOpen ? 'transform rotate-180' : ''}`} />
        </span>
      </button>

      {/* Dropdown - renderizado diretamente no DOM para evitar problemas de posicionamento */}
      {isOpen && mounted && (
        <div
          ref={dropdownRef}
          className="profile-select-dropdown"
          style={{
            maxHeight: '200px', // Limitar a altura máxima
            zIndex: 99999, // Garantir que o dropdown fique acima de tudo
          }}
        >
          <ul className="py-1" role="listbox">
            {optionsList.map((option) => (
              <li
                key={option.value}
                className={cn(
                  'px-3 py-2 text-sm cursor-pointer transition-colors duration-150',
                  option.value === value ? `${colors.activeBg} ${colors.activeText}` : 'text-gray-900 dark:text-gray-200',
                  option.disabled ? 'opacity-50 cursor-not-allowed' : `${colors.hoverBg} ${colors.hoverText}`
                )}
                onClick={() => !option.disabled && handleOptionSelect(option)}
                role="option"
                aria-selected={option.value === value}
                aria-disabled={option.disabled}
              >
                {option.label}
              </li>
            ))}
            {optionsList.length === 0 && (
              <li className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                Nenhuma opção disponível
              </li>
            )}
          </ul>
        </div>
      )}
    </div>
  );
});

ProfileSelect.displayName = 'ProfileSelect';

export default ProfileSelect;
