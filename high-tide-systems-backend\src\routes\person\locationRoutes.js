const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { LocationController, createLocationValidation } = require('../../controllers/locationController');

// Rotas protegidas
router.use(authenticate);

router.post('/', createLocationValidation, LocationController.create);
router.get('/', LocationController.list);
router.get('/:id', LocationController.get);
router.put('/:id', createLocationValidation, LocationController.update);
router.patch('/:id/status', LocationController.toggleStatus);
router.delete('/:id', LocationController.delete);

module.exports = router;