// src/swagger/documentRoutes.js

/**
 * @swagger
 * tags:
 *   name: Documentos
 *   description: Upload e gerenciamento de documentos
 */

/**
 * @swagger
 * /documents/upload:
 *   post:
 *     summary: Faz upload de documentos
 *     description: |
 *       Envia um ou mais documentos para o sistema.
 *       Os documentos podem estar associados a um usuário, cliente ou empresa.
 *     tags: [Documentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: targetId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do alvo (usuário, cliente ou empresa) ao qual os documentos serão vinculados
 *       - in: query
 *         name: targetType
 *         schema:
 *           type: string
 *           enum: [user, client, company]
 *         description: Tipo do alvo (usuário, cliente ou empresa)
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - documents
 *               - types
 *             properties:
 *               documents:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: Arquivos a serem enviados (apenas PDF, máx. 5MB por arquivo)
 *               types:
 *                 type: array
 *                 items:
 *                   type: string
 *                   enum: [RG, CPF, CNH, COMP_RESIDENCIA, OUTROS]
 *                 description: |
 *                   Tipos de documentos (deve ter o mesmo número de itens que 'documents').
 *                   Pode ser enviado como string JSON.
 *     responses:
 *       201:
 *         description: Documentos enviados com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Document'
 *       400:
 *         description: Dados inválidos ou erro no upload
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             examples:
 *               invalidFile:
 *                 value:
 *                   message: "Apenas arquivos PDF são permitidos"
 *               noFile:
 *                 value:
 *                   message: "Nenhum arquivo enviado"
 *               typeMismatch:
 *                 value:
 *                   message: "Número de tipos deve corresponder ao número de arquivos"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Sem permissão para adicionar documentos ao alvo
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Apenas profissionais do RH podem adicionar documentos para outros usuários"
 *       404:
 *         description: Alvo não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Usuário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /documents:
 *   get:
 *     summary: Lista documentos
 *     description: |
 *       Retorna uma lista de documentos.
 *       Permite filtrar por tipo e por alvo (usuário, cliente ou empresa).
 *     tags: [Documentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [RG, CPF, CNH, COMP_RESIDENCIA, OUTROS]
 *         description: Filtrar por tipo de documento
 *       - in: query
 *         name: targetId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do alvo (usuário, cliente ou empresa)
 *       - in: query
 *         name: targetType
 *         schema:
 *           type: string
 *           enum: [user, client, company]
 *         description: Tipo do alvo (usuário, cliente ou empresa)
 *     responses:
 *       200:
 *         description: Lista de documentos
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 allOf:
 *                   - type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         format: uuid
 *                       filename:
 *                         type: string
 *                       type:
 *                         type: string
 *                       createdAt:
 *                         type: string
 *                         format: date-time
 *                       user:
 *                         type: object
 *                         nullable: true
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           fullName:
 *                             type: string
 *                       client:
 *                         type: object
 *                         nullable: true
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           fullName:
 *                             type: string
 *                       company:
 *                         type: object
 *                         nullable: true
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           name:
 *                             type: string
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Sem permissão para ver documentos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Apenas profissionais do RH podem ver documentos de outros usuários"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /documents/{id}:
 *   get:
 *     summary: Obtém um documento
 *     description: Retorna o arquivo do documento especificado.
 *     tags: [Documentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do documento
 *     responses:
 *       200:
 *         description: Arquivo do documento
 *         content:
 *           application/pdf:
 *             schema:
 *               type: string
 *               format: binary
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Sem permissão para acessar o documento
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Documento não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Documento não encontrado ou sem permissão"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove um documento
 *     description: Remove um documento do sistema.
 *     tags: [Documentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do documento
 *     responses:
 *       204:
 *         description: Documento removido com sucesso
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: Sem permissão para remover o documento
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Documento não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Documento não encontrado ou sem permissão"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */