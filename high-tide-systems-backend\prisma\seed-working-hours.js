// prisma/seed-working-hours.js
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Função para converter horário em formato HH:MM para minutos desde a meia-noite
function timeToMinutes(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

// Função principal
async function main() {
  console.log('Iniciando seed de horários de trabalho...');
  
  try {
    // Buscar todos os profissionais (usuários com módulo de agendamento)
    const providers = await prisma.user.findMany({
      where: {
        active: true,
        modules: {
          has: 'SCHEDULING'
        }
      }
    });
    
    console.log(`Encontrados ${providers.length} profissionais para configurar horários de trabalho`);
    
    // Dias úteis (1 = Segunda, 5 = Sexta)
    const workDays = [1, 2, 3, 4, 5];
    
    // Horário de trabalho: 8h às 17h com intervalo de 12h às 13h
    const workingHoursConfig = {
      startTime: '08:00',
      endTime: '17:00',
      breakStart: '12:00',
      breakEnd: '13:00'
    };
    
    // Converter para minutos
    const startTimeMinutes = timeToMinutes(workingHoursConfig.startTime);
    const endTimeMinutes = timeToMinutes(workingHoursConfig.endTime);
    const breakStartMinutes = timeToMinutes(workingHoursConfig.breakStart);
    const breakEndMinutes = timeToMinutes(workingHoursConfig.breakEnd);
    
    // Para cada profissional
    for (const provider of providers) {
      console.log(`\nProcessando profissional: ${provider.fullName} (ID: ${provider.id})`);
      
      // Verificar se já tem horários configurados
      const existingHours = await prisma.workingHours.findMany({
        where: {
          userId: provider.id,
          isActive: true
        }
      });
      
      if (existingHours.length > 0) {
        console.log(`Profissional já possui ${existingHours.length} horários configurados. Desativando...`);
        
        // Desativar horários existentes
        await prisma.workingHours.updateMany({
          where: {
            userId: provider.id
          },
          data: {
            isActive: false
          }
        });
      }
      
      // Criar novos horários para cada dia útil
      for (const dayOfWeek of workDays) {
        try {
          const workingHours = await prisma.workingHours.create({
            data: {
              userId: provider.id,
              dayOfWeek,
              startTimeMinutes,
              endTimeMinutes,
              breakStartMinutes,
              breakEndMinutes,
              isActive: true
            }
          });
          
          console.log(`✅ Horário criado para dia ${dayOfWeek}: ${workingHoursConfig.startTime} - ${workingHoursConfig.endTime} (Intervalo: ${workingHoursConfig.breakStart} - ${workingHoursConfig.breakEnd})`);
        } catch (error) {
          console.error(`Erro ao criar horário para o dia ${dayOfWeek}:`, error);
        }
      }
      
      console.log(`Horários configurados com sucesso para ${provider.fullName}`);
    }
    
    console.log('\nSeed de horários de trabalho concluído com sucesso!');
  } catch (error) {
    console.error('Erro durante o seed de horários de trabalho:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Erro durante o seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
