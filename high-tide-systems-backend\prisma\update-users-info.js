const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('Iniciando atualização de informações dos usuários...');

  // Buscar todos os usuários que não têm CPF
  const users = await prisma.user.findMany({
    where: {
      cpf: null,
      role: 'EMPLOYEE'
    }
  });

  console.log(`Encontrados ${users.length} usuários para atualizar.`);

  // Função para gerar CPF válido
  function generateCPF() {
    // Gera os 9 primeiros dígitos aleatórios
    const n1 = Math.floor(Math.random() * 10);
    const n2 = Math.floor(Math.random() * 10);
    const n3 = Math.floor(Math.random() * 10);
    const n4 = Math.floor(Math.random() * 10);
    const n5 = Math.floor(Math.random() * 10);
    const n6 = Math.floor(Math.random() * 10);
    const n7 = Math.floor(Math.random() * 10);
    const n8 = Math.floor(Math.random() * 10);
    const n9 = Math.floor(Math.random() * 10);

    // Calcula o primeiro dígito verificador
    let d1 = n9 * 2 + n8 * 3 + n7 * 4 + n6 * 5 + n5 * 6 + n4 * 7 + n3 * 8 + n2 * 9 + n1 * 10;
    d1 = 11 - (d1 % 11);
    if (d1 >= 10) d1 = 0;

    // Calcula o segundo dígito verificador
    let d2 = d1 * 2 + n9 * 3 + n8 * 4 + n7 * 5 + n6 * 6 + n5 * 7 + n4 * 8 + n3 * 9 + n2 * 10 + n1 * 11;
    d2 = 11 - (d2 % 11);
    if (d2 >= 10) d2 = 0;

    // Formata o CPF
    return `${n1}${n2}${n3}.${n4}${n5}${n6}.${n7}${n8}${n9}-${d1}${d2}`;
  }

  // Função para gerar data de nascimento aleatória (entre 20 e 65 anos)
  function generateBirthDate() {
    const now = new Date();
    const minAge = 20;
    const maxAge = 65;
    const year = now.getFullYear() - minAge - Math.floor(Math.random() * (maxAge - minAge + 1));
    const month = Math.floor(Math.random() * 12);
    const day = Math.floor(Math.random() * 28) + 1; // Evita problemas com meses com menos de 31 dias
    return new Date(year, month, day);
  }

  // Nomes de ruas para gerar endereços aleatórios
  const streetNames = [
    'Rua das Flores', 'Avenida Brasil', 'Rua São Paulo', 'Avenida Paulista',
    'Rua Rio de Janeiro', 'Avenida Atlântica', 'Rua dos Pinheiros', 'Avenida Rebouças',
    'Rua Augusta', 'Avenida Faria Lima', 'Rua Oscar Freire', 'Avenida Brigadeiro Faria Lima',
    'Rua Consolação', 'Avenida Nove de Julho', 'Rua Haddock Lobo', 'Avenida Santo Amaro',
    'Rua Pamplona', 'Avenida Ibirapuera', 'Rua Joaquim Floriano', 'Avenida Engenheiro Luís Carlos Berrini'
  ];

  // Bairros para gerar endereços aleatórios
  const neighborhoods = [
    'Centro', 'Jardins', 'Moema', 'Itaim Bibi', 'Vila Olímpia', 'Pinheiros',
    'Brooklin', 'Vila Mariana', 'Perdizes', 'Higienópolis', 'Morumbi', 'Campo Belo',
    'Tatuapé', 'Santana', 'Ipiranga', 'Saúde', 'Lapa', 'Butantã', 'Liberdade', 'Bela Vista'
  ];

  // Cidades para gerar endereços aleatórios
  const cities = [
    'São Paulo', 'Rio de Janeiro', 'Belo Horizonte', 'Brasília', 'Salvador',
    'Fortaleza', 'Recife', 'Porto Alegre', 'Curitiba', 'Manaus', 'Belém',
    'Goiânia', 'Guarulhos', 'Campinas', 'São Luís', 'São Gonçalo', 'Maceió', 'Duque de Caxias'
  ];

  // Estados para gerar endereços aleatórios
  const states = [
    'SP', 'RJ', 'MG', 'DF', 'BA', 'CE', 'PE', 'RS', 'PR', 'AM', 'PA', 'GO', 'MA', 'AL', 'ES', 'PB'
  ];

  // Função para gerar CEP aleatório
  function generatePostalCode() {
    const n1 = Math.floor(Math.random() * 10);
    const n2 = Math.floor(Math.random() * 10);
    const n3 = Math.floor(Math.random() * 10);
    const n4 = Math.floor(Math.random() * 10);
    const n5 = Math.floor(Math.random() * 10);
    const n6 = Math.floor(Math.random() * 10);
    const n7 = Math.floor(Math.random() * 10);
    const n8 = Math.floor(Math.random() * 10);
    return `${n1}${n2}${n3}${n4}${n5}-${n6}${n7}${n8}`;
  }

  // Função para gerar endereço aleatório
  function generateAddress() {
    const streetName = streetNames[Math.floor(Math.random() * streetNames.length)];
    const number = Math.floor(Math.random() * 2000) + 1;
    const neighborhood = neighborhoods[Math.floor(Math.random() * neighborhoods.length)];
    const city = cities[Math.floor(Math.random() * cities.length)];
    const state = states[Math.floor(Math.random() * states.length)];
    const postalCode = generatePostalCode();

    return {
      address: `${streetName}, ${number}`,
      neighborhood,
      city,
      state,
      postalCode
    };
  }

  // Set para armazenar CPFs já utilizados e evitar duplicatas
  const usedCPFs = new Set();

  // Atualizar cada usuário com informações aleatórias
  for (const user of users) {
    try {
      // Gerar CPF único
      let cpf;
      do {
        cpf = generateCPF();
      } while (usedCPFs.has(cpf));
      usedCPFs.add(cpf);

      // Gerar data de nascimento
      const birthDate = generateBirthDate();

      // Gerar endereço
      const addressInfo = generateAddress();

      // Atualizar usuário
      await prisma.user.update({
        where: { id: user.id },
        data: {
          cpf,
          birthDate,
          address: `${addressInfo.address}, ${addressInfo.neighborhood}, ${addressInfo.city} - ${addressInfo.state}, CEP: ${addressInfo.postalCode}`
        }
      });

      console.log(`Usuário atualizado: ${user.fullName} (CPF: ${cpf})`);
    } catch (error) {
      console.error(`Erro ao atualizar usuário ${user.fullName}:`, error);
    }
  }

  console.log('\nAtualização de informações dos usuários concluída com sucesso!');
}

main()
  .catch((e) => {
    console.error('Erro durante a atualização de usuários:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
