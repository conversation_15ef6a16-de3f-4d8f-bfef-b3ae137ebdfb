-- <PERSON><PERSON><PERSON> para criar a tabela Person

CREATE TABLE IF NOT EXISTS "Person" (
  "id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "email" TEXT,
  "phone" TEXT,
  "address" TEXT,
  "city" TEXT,
  "state" TEXT,
  "zipCode" TEXT,
  "birthDate" TIMESTAMP(3),
  "documentType" TEXT,
  "documentNumber" TEXT,
  "notes" TEXT,
  "active" BOOLEAN NOT NULL DEFAULT true,
  "companyId" TEXT,
  "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updatedAt" TIMESTAMP(3) NOT NULL,
  "deletedAt" TIMESTAMP(3),
  "profileImage" TEXT,

  CONSTRAINT "Person_pkey" PRIMARY KEY ("id")
);
