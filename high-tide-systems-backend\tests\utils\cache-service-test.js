// tests/utils/cache-service-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do serviço de cache...');

  try {
    // Teste 1: Verificar se o cache está funcionando para listagem de usuários
    console.log('\n1. Verificando cache para listagem de usuários...');

    // Primeira requisição (sem cache)
    console.log('Fazendo primeira requisição (sem cache)...');
    const startTime1 = Date.now();
    const response1 = await api.get('/users');
    const endTime1 = Date.now();
    const time1 = endTime1 - startTime1;

    console.log(`✅ Primeira requisição concluída em ${time1}ms`);
    console.log(`Número de usuários: ${response1.data.length}`);

    // Segunda requisição (com cache)
    console.log('Fazendo segunda requisição (com cache)...');
    const startTime2 = Date.now();
    const response2 = await api.get('/users');
    const endTime2 = Date.now();
    const time2 = endTime2 - startTime2;

    console.log(`✅ Segunda requisição concluída em ${time2}ms`);
    console.log(`Número de usuários: ${response2.data.length}`);

    // Verificar se a segunda requisição foi mais rápida (indicando uso de cache)
    if (time2 < time1) {
      console.log('✅ Cache está funcionando! Segunda requisição foi mais rápida.');
    } else {
      console.log('⚠️ Cache pode não estar funcionando corretamente. Segunda requisição não foi mais rápida.');
    }

    // Teste 2: Verificar cache para listagem de usuários novamente
    console.log('\n2. Verificando cache para listagem de usuários novamente...');

    // Primeira requisição (com cache)
    console.log('Fazendo primeira requisição (com cache)...');
    const startTime3 = Date.now();
    await api.get('/users');
    const endTime3 = Date.now();
    const time3 = endTime3 - startTime3;

    console.log(`✅ Primeira requisição concluída em ${time3}ms`);

    // Segunda requisição (com cache)
    console.log('Fazendo segunda requisição (com cache)...');
    const startTime4 = Date.now();
    await api.get('/users');
    const endTime4 = Date.now();
    const time4 = endTime4 - startTime4;

    console.log(`✅ Segunda requisição concluída em ${time4}ms`);

    // Verificar se a segunda requisição foi mais rápida ou similar (indicando uso de cache)
    if (time4 <= time3 * 1.2) { // Permitir uma pequena variação
      console.log('✅ Cache está funcionando! Segunda requisição foi rápida.');
    } else {
      console.log('⚠️ Cache pode não estar funcionando corretamente. Segunda requisição foi significativamente mais lenta.');
    }

    console.log('\n✅ Testes do serviço de cache concluídos!');

  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
