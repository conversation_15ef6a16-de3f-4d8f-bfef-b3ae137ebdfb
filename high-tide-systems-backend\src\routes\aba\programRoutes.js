// src/routes/aba/programRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { 
  ProgramController, 
  createProgramValidation, 
  updateProgramValidation,
  createProgramTargetValidation,
  updateProgramTargetValidation
} = require('../../controllers/aba/programController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para programas
router.post('/', createProgramValidation, ProgramController.create);
router.get('/', ProgramController.list);
router.get('/:id', ProgramController.get);
router.put('/:id', updateProgramValidation, ProgramController.update);
router.patch('/:id/status', ProgramController.toggleStatus);
router.delete('/:id', ProgramController.delete);

// Rotas para alvos de programas
router.post('/targets', createProgramTargetValidation, ProgramController.createTarget);
router.get('/:programId/targets', ProgramController.listTargets);
router.put('/targets/:id', updateProgramTargetValidation, ProgramController.updateTarget);
router.delete('/targets/:id', ProgramController.deleteTarget);

module.exports = router;
