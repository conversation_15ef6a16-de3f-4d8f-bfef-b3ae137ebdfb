/**
 * Middleware para padronizar o formato das respostas da API
 * Este middleware intercepta as respostas e garante que elas sigam um formato consistente
 */

const responseFormatter = () => {
  return (req, res, next) => {
    // Armazenar a função json original
    const originalJson = res.json;

    // Sobrescrever a função json
    res.json = function(data) {
      // Restaurar a função original
      res.json = originalJson;

      // Se a resposta já estiver no formato padronizado, não modificar
      if (data && (data.data !== undefined || data.error !== undefined)) {
        return originalJson.call(this, data);
      }

      // Se for uma resposta de erro (status >= 400), padronizar como erro
      if (res.statusCode >= 400) {
        const errorResponse = {
          success: false,
          error: {
            message: data.message || 'Erro desconhecido',
            details: data.errors || data.details || undefined
          }
        };
        return originalJson.call(this, errorResponse);
      }

      // Padronizar a resposta de sucesso
      let formattedData = { success: true };

      // Preservar a estrutura original dentro do campo 'data'
      // Isso garante compatibilidade com o código existente
      formattedData.data = data;

      // Chamar a função json original com os dados formatados
      return originalJson.call(this, formattedData);
    };

    next();
  };
};

module.exports = responseFormatter;
