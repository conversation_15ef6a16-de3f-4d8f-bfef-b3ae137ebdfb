const { validationResult } = require("express-validator");
const { body } = require("express-validator");
const prisma = require("../utils/prisma");

// Validações
const createServiceTypeValidation = [
  body("name").notEmpty().withMessage("Nome é obrigatório"),
  body("value").isNumeric().withMessage("Valor deve ser numérico"),
  body("companyId").optional().isUUID().withMessage("ID da empresa inválido"),
];

class ServiceTypeController {
  static async create(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { name, value, companyId } = req.body;

      // Verificar se o usuário tem permissão para esta empresa
      if (companyId && req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId !== companyId) {
        return res.status(403).json({ message: "Você não tem permissão para criar serviços para esta empresa" });
      }

      // Usar o companyId do usuário caso não seja fornecido
      const targetCompanyId = companyId || req.user.companyId;

      // Verificar se já existe um serviço com o mesmo nome na mesma empresa
      const existingService = await prisma.serviceType.findFirst({
        where: {
          name,
          companyId: targetCompanyId
        }
      });

      if (existingService) {
        return res.status(400).json({ message: "Já existe um serviço com este nome nesta empresa" });
      }

      const serviceType = await prisma.serviceType.create({
        data: {
          name,
          value,
          companyId: targetCompanyId
        },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      res.status(201).json(serviceType);
    } catch (error) {
      console.error("Erro ao criar tipo de serviço:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async update(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
      }

      const { id } = req.params;
      const { name, value, companyId } = req.body;

      // Buscar o serviço atual
      const existingService = await prisma.serviceType.findUnique({
        where: { id }
      });

      if (!existingService) {
        return res.status(404).json({ message: "Tipo de serviço não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && existingService.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para editar este serviço" });
      }

      // Se o companyId está sendo alterado, verificar permissão
      if (companyId && companyId !== existingService.companyId && req.user.role !== 'SYSTEM_ADMIN') {
        return res.status(403).json({ message: "Você não tem permissão para alterar a empresa deste serviço" });
      }

      // Verificar se já existe outro serviço com o mesmo nome na mesma empresa
      if (name !== existingService.name) {
        const duplicateService = await prisma.serviceType.findFirst({
          where: {
            name,
            companyId: companyId || existingService.companyId,
            id: { not: id }
          }
        });

        if (duplicateService) {
          return res.status(400).json({ message: "Já existe um serviço com este nome nesta empresa" });
        }
      }

      const serviceType = await prisma.serviceType.update({
        where: { id },
        data: {
          name,
          value,
          companyId: companyId || existingService.companyId
        },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      res.json(serviceType);
    } catch (error) {
      console.error("Erro ao atualizar tipo de serviço:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async list(req, res) {
    try {
      const { search, companyId, serviceTypeIds } = req.query;

      // Construir o filtro de busca
      const where = {};

      // Filtrar por termo de busca
      if (search) {
        where.name = {
          contains: search,
          mode: "insensitive",
        };
      }

      // Filtrar por empresa
      if (companyId) {
        where.companyId = companyId;
      } else if (req.user.role !== 'SYSTEM_ADMIN' && req.user.companyId) {
        // Se não for admin do sistema, mostrar apenas serviços da própria empresa
        where.companyId = req.user.companyId;
      }

      // Filtrar por IDs específicos de tipos de serviço
      if (serviceTypeIds) {
        // Converter para array se não for
        const idsArray = Array.isArray(serviceTypeIds) ? serviceTypeIds : [serviceTypeIds];
        if (idsArray.length > 0) {
          console.log("Filtrando por IDs de tipos de serviço:", idsArray);
          where.id = { in: idsArray };
        }
      }

      const [serviceTypes, total] = await Promise.all([
        prisma.serviceType.findMany({
          where,
          include: {
            company: {
              select: {
                id: true,
                name: true
              }
            }
          },
          orderBy: {
            name: 'asc'
          }
        }),
        prisma.serviceType.count({ where }),
      ]);

      res.json({
        serviceTypes,
        total,
      });
    } catch (error) {
      console.error("Erro ao listar tipos de serviço:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async get(req, res) {
    try {
      const { id } = req.params;

      const serviceType = await prisma.serviceType.findUnique({
        where: { id },
        include: {
          company: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      if (!serviceType) {
        return res
          .status(404)
          .json({ message: "Tipo de serviço não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && serviceType.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para visualizar este serviço" });
      }

      res.json(serviceType);
    } catch (error) {
      console.error("Erro ao buscar tipo de serviço:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }

  static async delete(req, res) {
    try {
      const { id } = req.params;

      // Buscar o serviço
      const serviceType = await prisma.serviceType.findUnique({
        where: { id },
        include: {
          _count: {
            select: {
              schedulings: true,
              recurrences: true
            }
          }
        }
      });

      if (!serviceType) {
        return res.status(404).json({ message: "Tipo de serviço não encontrado" });
      }

      // Verificar permissão
      if (req.user.role !== 'SYSTEM_ADMIN' && serviceType.companyId !== req.user.companyId) {
        return res.status(403).json({ message: "Você não tem permissão para excluir este serviço" });
      }

      // Verificar se o serviço está sendo usado
      if (serviceType._count.schedulings > 0 || serviceType._count.recurrences > 0) {
        return res.status(400).json({
          message: "Este tipo de serviço está sendo usado por agendamentos ou recorrências e não pode ser excluído"
        });
      }

      await prisma.serviceType.delete({
        where: { id },
      });

      res.status(204).send();
    } catch (error) {
      console.error("Erro ao deletar tipo de serviço:", error);
      res.status(500).json({ message: "Erro interno do servidor" });
    }
  }
}

module.exports = {
  ServiceTypeController,
  createServiceTypeValidation,
};