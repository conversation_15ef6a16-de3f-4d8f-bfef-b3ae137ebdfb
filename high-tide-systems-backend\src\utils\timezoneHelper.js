/**
 * Utility functions for handling timezone conversions
 */

// Default timezone for the application
const DEFAULT_TIMEZONE = 'America/Sao_Paulo';

/**
 * Converts a date to the specified timezone
 * @param {Date|string} date - The date to convert
 * @param {string} timezone - The timezone to convert to (e.g., 'America/Sao_Paulo')
 * @returns {Date} - The date in the specified timezone
 */
const convertToTimezone = (date, timezone = DEFAULT_TIMEZONE) => {
  const inputDate = new Date(date);
  
  // Get the timezone offset in minutes for the specified timezone
  // For Brazil (America/Sao_Paulo), this is typically UTC-3
  let offsetMinutes;
  
  try {
    // This is a more accurate way to get the timezone offset, but it requires
    // the Intl API which might not be available in all environments
    const formatter = new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: 'numeric',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
      hour12: false
    });
    
    const parts = formatter.formatToParts(inputDate);
    const dateObj = {};
    parts.forEach(part => {
      if (part.type !== 'literal') {
        dateObj[part.type] = parseInt(part.value, 10);
      }
    });
    
    // Create a date in the target timezone
    const targetDate = new Date(
      dateObj.year,
      dateObj.month - 1,
      dateObj.day,
      dateObj.hour,
      dateObj.minute,
      dateObj.second
    );
    
    // Calculate the offset
    offsetMinutes = (targetDate - inputDate) / (60 * 1000);
  } catch (error) {
    console.error('Error calculating timezone offset with Intl API:', error);
    
    // Fallback to a fixed offset for Brazil (UTC-3)
    // This is not ideal as it doesn't account for DST, but it's better than nothing
    offsetMinutes = -180;
    console.warn(`Using fallback timezone offset of ${offsetMinutes} minutes for ${timezone}`);
  }
  
  // Apply the offset to the input date
  return new Date(inputDate.getTime() + offsetMinutes * 60 * 1000);
};

/**
 * Gets the day of week in the specified timezone
 * @param {Date|string} date - The date to get the day of week for
 * @param {string} timezone - The timezone to use
 * @returns {number} - The day of week (0-6, where 0 is Sunday)
 */
const getDayOfWeekInTimezone = (date, timezone = DEFAULT_TIMEZONE) => {
  const dateInTimezone = convertToTimezone(date, timezone);
  return dateInTimezone.getDay();
};

/**
 * Gets the time in minutes since midnight in the specified timezone
 * @param {Date|string} date - The date to get the time for
 * @param {string} timezone - The timezone to use
 * @returns {number} - The time in minutes since midnight
 */
const getTimeMinutesInTimezone = (date, timezone = DEFAULT_TIMEZONE) => {
  const dateInTimezone = convertToTimezone(date, timezone);
  return dateInTimezone.getHours() * 60 + dateInTimezone.getMinutes();
};

/**
 * Gets the company's timezone
 * @param {string} companyId - The company ID
 * @returns {Promise<string>} - The company's timezone
 */
const getCompanyTimezone = async (companyId, prisma) => {
  if (!companyId) {
    return DEFAULT_TIMEZONE;
  }
  
  try {
    const company = await prisma.company.findUnique({
      where: { id: companyId },
      select: { timeZone: true }
    });
    
    return company?.timeZone || DEFAULT_TIMEZONE;
  } catch (error) {
    console.error('Error fetching company timezone:', error);
    return DEFAULT_TIMEZONE;
  }
};

module.exports = {
  convertToTimezone,
  getDayOfWeekInTimezone,
  getTimeMinutesInTimezone,
  getCompanyTimezone,
  DEFAULT_TIMEZONE
};
