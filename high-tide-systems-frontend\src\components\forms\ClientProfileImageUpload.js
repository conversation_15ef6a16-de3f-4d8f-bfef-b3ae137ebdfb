'use client';

import React, { useState, useEffect, useRef, forwardRef } from 'react';
import { Upload, Trash, Loader2 } from 'lucide-react';
import { personsService } from '@/app/modules/people/services/personsService';

const ClientProfileImageUpload = forwardRef(({ onImageSelected }, ref) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [error, setError] = useState(null);
  const fileInputRef = useRef(null);

  // Expor o método de clique para o componente pai
  useEffect(() => {
    if (ref) {
      ref.current = {
        click: () => {
          if (fileInputRef.current) {
            fileInputRef.current.click();
          }
        }
      };
    }
  }, [ref]);

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (!file) return;

    // Verificar tipo de arquivo (apenas imagens)
    if (!file.type.startsWith('image/')) {
      setError('Por favor, selecione uma imagem válida');
      return;
    }

    // Limitar tamanho (2MB)
    if (file.size > 2 * 1024 * 1024) {
      setError('A imagem deve ter no máximo 2MB');
      return;
    }

    setError(null);
    setSelectedFile(file);
    
    // Notificar o componente pai sobre a seleção do arquivo
    if (onImageSelected) {
      onImageSelected(file);
    }
  };

  return (
    <input
      ref={fileInputRef}
      type="file"
      accept="image/*"
      onChange={handleFileChange}
      className="hidden"
      id="profile-photo-upload"
    />
  );
});

ClientProfileImageUpload.displayName = 'ClientProfileImageUpload';

export default ClientProfileImageUpload;
