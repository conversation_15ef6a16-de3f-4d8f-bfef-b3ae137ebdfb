const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { cacheMiddleware, clearCacheMiddleware } = require('../../middlewares/cache');
const { ServiceTypeController, createServiceTypeValidation } = require('../../controllers/serviceTypeController');

// Configurar TTL para cache de tipos de serviço (10 minutos)
const SERVICE_TYPE_CACHE_TTL = 600;

// Rotas protegidas
router.use(authenticate);

// Aplicar middleware para limpar cache quando dados são modificados
router.post('/', createServiceTypeValidation, clearCacheMiddleware('service-types:*'), ServiceTypeController.create);

// Aplicar cache para listagens
router.get('/', cacheMiddleware('service-types:list', SERVICE_TYPE_CACHE_TTL), ServiceTypeController.list);
router.get('/:id', cacheMiddleware('service-types:detail', SERVICE_TYPE_CACHE_TTL), ServiceTypeController.get);

// Limpar cache quando dados são modificados
router.put('/:id', createServiceTypeValidation, clearCacheMiddleware('service-types:*'), ServiceTypeController.update);
router.delete('/:id', clearCacheMiddleware('service-types:*'), ServiceTypeController.delete);

module.exports = router;