// src/swagger/branchRoutes.js

/**
 * @swagger
 * tags:
 *   name: Unidades
 *   description: Gerenciamento de unidades/filiais da empresa
 */

/**
 * @swagger
 * /branches:
 *   post:
 *     summary: Cria uma nova unidade
 *     description: Cria uma nova unidade/filial para uma empresa.
 *     tags: [Unidades]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - address
 *               - companyId
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome da unidade
 *                 example: "Filial Centro"
 *               code:
 *                 type: string
 *                 description: Código da unidade (único por empresa)
 *                 example: "FIL-01"
 *               description:
 *                 type: string
 *                 description: Descrição da unidade
 *                 example: "Unidade principal no centro da cidade"
 *               address:
 *                 type: string
 *                 description: Endereço completo da unidade
 *                 example: "Rua Principal, 100, Centro"
 *               city:
 *                 type: string
 *                 description: Cidade
 *                 example: "São Paulo"
 *               state:
 *                 type: string
 *                 description: Estado
 *                 example: "SP"
 *               postalCode:
 *                 type: string
 *                 description: CEP
 *                 example: "01234-567"
 *               phone:
 *                 type: string
 *                 description: Telefone da unidade (opcional)
 *                 example: "1122223333"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email de contato da unidade
 *                 example: "<EMAIL>"
 *               companyId:
 *                 type: string
 *                 format: uuid
 *                 description: ID da empresa à qual a unidade pertence
 *                 example: "550e8400-e29b-41d4-a716-************"
 *               isHeadquarters:
 *                 type: boolean
 *                 description: Indica se esta é a matriz da empresa
 *                 example: false
 *     responses:
 *       201:
 *         description: Unidade criada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Branch'
 *       400:
 *         description: Dados inválidos ou código já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Empresa não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   get:
 *     summary: Lista unidades
 *     description: Retorna uma lista paginada de unidades. Permite filtrar por busca, status e empresa.
 *     tags: [Unidades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Número da página
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Número de itens por página
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Texto para busca (nome, código, endereço)
 *       - in: query
 *         name: active
 *         schema:
 *           type: boolean
 *         description: Filtrar por status (ativa/inativa)
 *       - in: query
 *         name: companyId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da empresa para filtrar unidades
 *     responses:
 *       200:
 *         description: Lista de unidades
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 branches:
 *                   type: array
 *                   items:
 *                     type: object
 *                     allOf:
 *                       - $ref: '#/components/schemas/Branch'
 *                       - type: object
 *                         properties:
 *                           company:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                                 format: uuid
 *                               name:
 *                                 type: string
 *                           _count:
 *                             type: object
 *                             properties:
 *                               locations:
 *                                 type: integer
 *                 total:
 *                   type: integer
 *                   description: Total de registros
 *                   example: 15
 *                 pages:
 *                   type: integer
 *                   description: Total de páginas
 *                   example: 2
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /branches/{id}:
 *   get:
 *     summary: Obtém detalhes de uma unidade
 *     description: Retorna os detalhes completos de uma unidade específica, incluindo seus locais.
 *     tags: [Unidades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da unidade
 *     responses:
 *       200:
 *         description: Detalhes da unidade
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/Branch'
 *                 - type: object
 *                   properties:
 *                     company:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                     locations:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: string
 *                             format: uuid
 *                           name:
 *                             type: string
 *                           address:
 *                             type: string
 *                           phone:
 *                             type: string
 *                           active:
 *                             type: boolean
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Unidade não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Unidade não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   put:
 *     summary: Atualiza uma unidade
 *     description: Atualiza os dados de uma unidade existente.
 *     tags: [Unidades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da unidade
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: Nome da unidade
 *               code:
 *                 type: string
 *                 description: Código da unidade
 *               description:
 *                 type: string
 *                 description: Descrição da unidade
 *               address:
 *                 type: string
 *                 description: Endereço completo da unidade
 *               city:
 *                 type: string
 *                 description: Cidade
 *               state:
 *                 type: string
 *                 description: Estado
 *               postalCode:
 *                 type: string
 *                 description: CEP
 *               phone:
 *                 type: string
 *                 description: Telefone da unidade
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email de contato da unidade
 *               isHeadquarters:
 *                 type: boolean
 *                 description: Indica se esta é a matriz da empresa
 *     responses:
 *       200:
 *         description: Unidade atualizada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Branch'
 *       400:
 *         description: Dados inválidos ou código já existe
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Unidade não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Unidade não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Remove uma unidade
 *     description: Remove uma unidade do sistema. Não é possível remover unidades com locais ativos.
 *     tags: [Unidades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da unidade
 *     responses:
 *       204:
 *         description: Unidade removida com sucesso
 *       400:
 *         description: Unidade possui locais ativos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Não é possível excluir uma unidade com locais ativos. Desative ou remova os locais primeiro."
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Unidade não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Unidade não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /branches/{id}/status:
 *   patch:
 *     summary: Alterna o status de uma unidade
 *     description: Ativa ou desativa uma unidade no sistema.
 *     tags: [Unidades]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID da unidade
 *     responses:
 *       200:
 *         description: Status alterado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Branch'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Unidade não encontrada
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Unidade não encontrada"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */