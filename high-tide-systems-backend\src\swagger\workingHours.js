// src/swagger/workingHoursRoutes.js

/**
 * @swagger
 * tags:
 *   name: Hor<PERSON><PERSON>s de Trabalho
 *   description: Gerenciamento de disponibilidade dos profissionais
 */

/**
 * @swagger
 * /working-hours:
 *   post:
 *     summary: Define os horários de trabalho
 *     description: |
 *       Define os horários de trabalho para um profissional.
 *       Os horários existentes são desativados e novos são criados.
 *     tags: [Hor<PERSON><PERSON>s de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userId
 *               - schedules
 *             properties:
 *               userId:
 *                 type: string
 *                 format: uuid
 *                 description: ID do profissional
 *               schedules:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - dayOfWeek
 *                     - startTime
 *                     - endTime
 *                   properties:
 *                     dayOfWeek:
 *                       type: integer
 *                       minimum: 0
 *                       maximum: 6
 *                       description: <PERSON><PERSON> da semana (0=Domingo, 6=Sábado)
 *                       example: 1
 *                     startTime:
 *                       type: string
 *                       pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                       description: Hora de início (formato HH:MM)
 *                       example: "08:00"
 *                     endTime:
 *                       type: string
 *                       pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                       description: Hora de término (formato HH:MM)
 *                       example: "18:00"
 *                     breakStart:
 *                       type: string
 *                       pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                       description: Hora de início do intervalo (formato HH:MM)
 *                       example: "12:00"
 *                     breakEnd:
 *                       type: string
 *                       pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                       description: Hora de término do intervalo (formato HH:MM)
 *                       example: "13:00"
 *     responses:
 *       201:
 *         description: Horários definidos com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/WorkingHours'
 *       400:
 *         description: Dados inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /working-hours/{userId}:
 *   get:
 *     summary: Lista horários de trabalho
 *     description: Retorna os horários de trabalho ativos de um profissional.
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do profissional
 *     responses:
 *       200:
 *         description: Lista de horários de trabalho
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/WorkingHours'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /working-hours/{id}:
 *   put:
 *     summary: Atualiza um horário de trabalho
 *     description: |
 *       Atualiza um período específico de horário de trabalho.
 *       Verificará se há agendamentos pendentes que possam conflitar com a alteração.
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do horário de trabalho
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               dayOfWeek:
 *                 type: integer
 *                 minimum: 0
 *                 maximum: 6
 *                 description: Dia da semana (0=Domingo, 6=Sábado)
 *               startTime:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                 description: Hora de início (formato HH:MM)
 *               endTime:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                 description: Hora de término (formato HH:MM)
 *               breakStart:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                 description: Hora de início do intervalo (formato HH:MM)
 *               breakEnd:
 *                 type: string
 *                 pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                 description: Hora de término do intervalo (formato HH:MM)
 *     responses:
 *       200:
 *         description: Horário atualizado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/WorkingHours'
 *       400:
 *         description: Dados inválidos ou conflito com agendamentos
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Não é possível editar horário com agendamentos pendentes"
 *                 schedulings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SchedulingResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Horário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Horário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 *
 *   delete:
 *     summary: Desativa um horário de trabalho
 *     description: |
 *       Desativa um período específico de horário de trabalho.
 *       Verificará se há agendamentos pendentes que possam conflitar com a desativação.
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do horário de trabalho
 *     responses:
 *       200:
 *         description: Horário desativado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Horário desativado com sucesso"
 *       400:
 *         description: Conflito com agendamentos
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Não é possível desativar horário com agendamentos pendentes"
 *                 schedulings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SchedulingResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Horário não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Horário não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /working-hours/{userId}/{dayOfWeek}:
 *   post:
 *     summary: Define horários para um dia específico
 *     description: |
 *       Define os horários de trabalho para um dia específico da semana.
 *       Os horários existentes para este dia são desativados e novos são criados.
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do profissional
 *       - in: path
 *         name: dayOfWeek
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 6
 *         description: Dia da semana (0=Domingo, 6=Sábado)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - timeSlots
 *             properties:
 *               timeSlots:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - start
 *                     - end
 *                   properties:
 *                     start:
 *                       type: string
 *                       pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                       description: Hora de início (formato HH:MM)
 *                       example: "09:00"
 *                     end:
 *                       type: string
 *                       pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$'
 *                       description: Hora de término (formato HH:MM)
 *                       example: "10:00"
 *     responses:
 *       200:
 *         description: Horários definidos com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/WorkingHours'
 *       400:
 *         description: Dados inválidos ou conflito com agendamentos
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Não é possível alterar horários com agendamentos pendentes"
 *                 schedulings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SchedulingResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /working-hours/{userId}/{dayOfWeek}/grid:
 *   get:
 *     summary: Obtém grade de horários para um dia
 *     description: |
 *       Retorna a grade de horários disponíveis para um dia específico,
 *       com granularidade de 1 hora (24 slots).
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do profissional
 *       - in: path
 *         name: dayOfWeek
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 6
 *         description: Dia da semana (0=Domingo, 6=Sábado)
 *     responses:
 *       200:
 *         description: Grade de horários
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 dayOfWeek:
 *                   type: integer
 *                   example: 1
 *                 timeGrid:
 *                   type: array
 *                   items:
 *                     type: boolean
 *                   description: Grade de 24 slots (um para cada hora)
 *                   example: [false, false, false, false, false, false, false, false, true, true, true, true, false, true, true, true, true, true, false, false, false, false, false, false]
 *                 workingHours:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/WorkingHours'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /working-hours/grid/{dayOfWeek}:
 *   get:
 *     summary: Obtém grade de horários de múltiplos profissionais
 *     description: |
 *       Retorna a grade de horários para múltiplos profissionais em um dia específico.
 *       Útil para visualização em quadro de horários.
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: dayOfWeek
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 6
 *         description: Dia da semana (0=Domingo, 6=Sábado)
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: Lista de IDs dos profissionais (opcional, se não for fornecido retorna todos ativos)
 *     responses:
 *       200:
 *         description: Grade de horários por profissional
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 dayOfWeek:
 *                   type: integer
 *                   example: 1
 *                 users:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         format: uuid
 *                       userName:
 *                         type: string
 *                       timeGrid:
 *                         type: array
 *                         items:
 *                           type: boolean
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /working-hours/hourly-grid/{userId}/{dayOfWeek}:
 *   put:
 *     summary: Atualiza grade horária
 *     description: |
 *       Atualiza a grade de disponibilidade horária para um dia específico,
 *       com granularidade de 1 hora (24 slots).
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do profissional
 *       - in: path
 *         name: dayOfWeek
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 6
 *         description: Dia da semana (0=Domingo, 6=Sábado)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - timeGrid
 *             properties:
 *               timeGrid:
 *                 type: array
 *                 minItems: 24
 *                 maxItems: 24
 *                 items:
 *                   type: boolean
 *                 description: Grid de 24 valores booleanos (true = disponível, false = indisponível)
 *                 example: [false, false, false, false, false, false, false, false, true, true, true, true, false, true, true, true, true, true, false, false, false, false, false, false]
 *     responses:
 *       200:
 *         description: Grade atualizada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 intervals:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       start:
 *                         type: integer
 *                         example: 8
 *                       end:
 *                         type: integer
 *                         example: 12
 *                 workingHours:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/WorkingHours'
 *       400:
 *         description: Dados inválidos ou conflito com agendamentos
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Não é possível alterar horários com agendamentos pendentes"
 *                 schedulings:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SchedulingResponse'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /working-hours/multiple-grids/{dayOfWeek}:
 *   put:
 *     summary: Atualiza grade horária de múltiplos profissionais
 *     description: |
 *       Atualiza a grade de disponibilidade horária de múltiplos profissionais para um dia específico.
 *       Útil para configurar rapidamente vários profissionais em uma tela de administração.
 *     tags: [Horários de Trabalho]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: dayOfWeek
 *         required: true
 *         schema:
 *           type: integer
 *           minimum: 0
 *           maximum: 6
 *         description: Dia da semana (0=Domingo, 6=Sábado)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - usersGrid
 *             properties:
 *               usersGrid:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - userId
 *                     - timeGrid
 *                   properties:
 *                     userId:
 *                       type: string
 *                       format: uuid
 *                       description: ID do profissional
 *                     timeGrid:
 *                       type: array
 *                       minItems: 24
 *                       maxItems: 24
 *                       items:
 *                         type: boolean
 *                       description: Grid de 24 valores booleanos
 *     responses:
 *       200:
 *         description: Grades atualizadas com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         format: uuid
 *                       success:
 *                         type: boolean
 *                       intervals:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             start:
 *                               type: integer
 *                             end:
 *                               type: integer
 *                       workingHours:
 *                         type: array
 *                         items:
 *                           $ref: '#/components/schemas/WorkingHours'
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         format: uuid
 *                       message:
 *                         type: string
 *                       error:
 *                         type: string
 *                 overallSuccess:
 *                   type: boolean
 *       400:
 *         description: Dados inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */