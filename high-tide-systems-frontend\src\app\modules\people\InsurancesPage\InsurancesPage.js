"use client";

import React, { useState, useEffect, useCallback } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleInput, ModuleTable } from "@/components/ui";
import MultiSelect from "@/components/ui/multi-select";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  Users,
  Building
} from "lucide-react";
import { insurancesService } from "@/app/modules/people/services/insurancesService";
import { companyService } from "@/app/modules/admin/services/companyService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import InsuranceFormModal from "@/components/people/InsurancesFormModal";
import ExportMenu from "@/components/ui/ExportMenu";
import { ModuleSelect } from "@/components/ui";

// Tutorial steps para a página de convênios
const insurancesTutorialSteps = [
  {
    title: "Convênios",
    content: "Esta tela permite gerenciar os convênios disponíveis no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Convênio",
    content: "Clique aqui para adicionar um novo convênio.",
    selector: "button:has(span:contains('Novo Convênio'))",
    position: "left"
  },
  {
    title: "Filtrar Convênios",
    content: "Use esta barra de pesquisa para encontrar convênios específicos pelo nome.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtrar por Múltiplos Convênios",
    content: "Selecione um ou mais convênios pelo nome para filtrar a lista.",
    selector: "div:has(> label:contains('Filtrar por Convênios'))",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de convênios em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Convênios",
    content: "Edite ou exclua convênios existentes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const InsurancesPage = () => {
  const { user: currentUser } = useAuth();
  const isAdmin = currentUser?.modules?.includes("ADMIN") || currentUser?.modules?.includes("RH");
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  const [insurances, setInsurances] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [search, setSearch] = useState("");
  const [insurancesFilter, setInsurancesFilter] = useState([]);
  const [insuranceOptions, setInsuranceOptions] = useState([]);
  const [isLoadingInsuranceOptions, setIsLoadingInsuranceOptions] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedInsurance, setSelectedInsurance] = useState(null);
  const [insuranceFormOpen, setInsuranceFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  // Estados para paginação
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  // Estados para filtro de empresa (apenas para system_admin)
  const [companies, setCompanies] = useState([]);
  const [companyFilter, setCompanyFilter] = useState("");
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);

  // Constante para itens por página
  const ITEMS_PER_PAGE = 10;

  // Função para carregar opções de convênios para o multi-select
  const loadInsuranceOptions = useCallback(async () => {
    setIsLoadingInsuranceOptions(true);
    try {
      // Carregar todos os convênios para o multi-select (com limite maior)
      const response = await insurancesService.getInsurances({
        limit: 100 // Limite maior para ter mais opções
      });

      const options = response?.insurances?.map(insurance => ({
        value: insurance.id,
        label: insurance.name,
        // Guardar o nome para ordenação
        sortName: insurance.name.toLowerCase()
      })) || [];

      // Ordenar as opções alfabeticamente pelo nome
      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName));

      setInsuranceOptions(sortedOptions);
    } catch (error) {
      console.error("Erro ao carregar opções de convênios:", error);
      setInsuranceOptions([]);
    } finally {
      setIsLoadingInsuranceOptions(false);
    }
  }, []);

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (!isSystemAdmin) return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  const loadInsurances = async (
    page = currentPage,
    searchQuery = search,
    insuranceIds = insurancesFilter,
    company = companyFilter
  ) => {
    setIsLoading(true);
    try {
      const response = await insurancesService.getInsurances({
        search: searchQuery || undefined,
        insuranceIds: insuranceIds.length > 0 ? insuranceIds : undefined,
        companyId: company || undefined,
        page,
        limit: ITEMS_PER_PAGE
      });

      console.log('Dados de convênios recebidos:', response);

      // Verificar se temos os dados no formato esperado
      if (response && typeof response === 'object') {
        // Extrair os convênios, total e páginas
        const insurancesArray = response.insurances || [];
        const total = response.total || insurancesArray.length;
        const pages = response.pages || Math.ceil(total / ITEMS_PER_PAGE);

        console.log('Array de convênios extraído:', insurancesArray);
        console.log('Total de itens:', total);
        console.log('Total de páginas:', pages);

        // Atualizar o estado
        setInsurances(insurancesArray);
        setTotalItems(total);
        setTotalPages(pages);
        setCurrentPage(page);
      } else {
        console.error('Dados de convênios inválidos:', response);
        setInsurances([]);
        setTotalItems(0);
        setTotalPages(1);
      }
    } catch (error) {
      console.error("Erro ao carregar convênios:", error);
      setInsurances([]);
      setTotalItems(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadInsurances();
    loadInsuranceOptions();
    // Carregar empresas se o usuário for system_admin
    if (isSystemAdmin) {
      loadCompanies();
    }
  }, [loadInsuranceOptions, isSystemAdmin]);

  const handleSearch = (e) => {
    e.preventDefault();
    loadInsurances(1, search, insurancesFilter, companyFilter);
  };

  const handleInsurancesFilterChange = (value) => {
    setInsurancesFilter(value);
    loadInsurances(1, search, value, companyFilter);
  };

  const handleCompanyFilterChange = (value) => {
    setCompanyFilter(value);
    loadInsurances(1, search, insurancesFilter, value);
  };

  const handleResetFilters = () => {
    setSearch("");
    setInsurancesFilter([]);
    setCompanyFilter("");
    loadInsurances(1, "", [], "");
  };

  // Função para lidar com a mudança de página
  const handlePageChange = (page) => {
    loadInsurances(page, search, insurancesFilter, companyFilter);
  };

  const handleEditInsurance = (insurance) => {
    setSelectedInsurance(insurance);
    setInsuranceFormOpen(true);
  };

  const handleDeleteInsurance = (insurance) => {
    setSelectedInsurance(insurance);
    setConfirmationDialogOpen(true);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Exportar usando os mesmos filtros da tabela atual
      await insurancesService.exportInsurances({
        search: search || undefined,
        insuranceIds: insurancesFilter.length > 0 ? insurancesFilter : undefined,
        companyId: companyFilter || undefined
      }, format);
    } catch (error) {
      console.error("Erro ao exportar convênios:", error);
      // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast
    } finally {
      setIsExporting(false);
    }
  };

  const confirmDeleteInsurance = async () => {
    try {
      await insurancesService.deleteInsurance(selectedInsurance.id);
      loadInsurances();
    } catch (error) {
      console.error("Erro ao excluir convênio:", error);
    }
    setConfirmationDialogOpen(false);
  };



  return (
    <div className="space-y-6">
      {/* Título e botões de exportar e adicionar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <Building size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Convênios
        </h1>

        <div className="flex items-center gap-2">
          {/* Botão de exportar */}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || insurances.length === 0}
            className="text-orange-700 dark:text-orange-300"
          />

          {/* Botão de adicionar */}
          {isAdmin && (
            <button
              onClick={() => {
                setSelectedInsurance(null);
                setInsuranceFormOpen(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all"
            >
              <Plus size={18} />
              <span className="font-medium">Novo Convênio</span>
            </button>
          )}
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Gerencie os convênios disponíveis no sistema. Utilize o filtro abaixo para encontrar convênios específicos."
        tutorialSteps={insurancesTutorialSteps}
        tutorialName="insurances-overview"
        moduleColor="people"
        filters={
          <form
            onSubmit={handleSearch}
            className="flex flex-col gap-4"
          >
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5 z-10" />
                <ModuleInput
                  moduleColor="people"
                  type="text"
                  placeholder="Buscar por nome do convênio..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full pl-10"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
                {/* Filtro de empresa (apenas para system_admin) */}
                {isSystemAdmin && (
                  <ModuleSelect
                    moduleColor="people"
                    value={companyFilter}
                    onChange={(e) => handleCompanyFilterChange(e.target.value)}
                    placeholder="Empresa"
                    disabled={isLoadingCompanies}
                  >
                    <option value="">Todas as empresas</option>
                    {companies.map((company) => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))}
                  </ModuleSelect>
                )}

                <FilterButton type="submit" moduleColor="people" variant="primary">
                  <Filter size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Filtrar</span>
                </FilterButton>

                <FilterButton
                  type="button"
                  onClick={handleResetFilters}
                  moduleColor="people"
                  variant="secondary"
                >
                  <RefreshCw size={16} className="sm:hidden" />
                  <span className="hidden sm:inline">Limpar</span>
                </FilterButton>
              </div>
            </div>

            {/* Multi-select para filtrar por múltiplos convênios */}
            <div className="w-full">
              <MultiSelect
                label="Filtrar por Convênios"
                value={insurancesFilter}
                onChange={handleInsurancesFilterChange}
                options={insuranceOptions}
                placeholder="Selecione um ou mais convênios pelo nome..."
                loading={isLoadingInsuranceOptions}
                moduleOverride="people"
              />
            </div>
          </form>
      }
      />

      {/* Tabela de Convênios */}
      <ModuleTable
        moduleColor="people"
        columns={[
          { header: 'Nome', field: 'name', width: '40%' },
          { header: 'Empresa', field: 'company', width: '40%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '20%', sortable: false }
        ]}
        data={insurances}
        isLoading={isLoading}
        emptyMessage="Nenhum convênio encontrado"
        emptyIcon={<Building size={24} />}
        tableId="people-insurances-table"
        enableColumnToggle={true}
        defaultSortField="name"
        defaultSortDirection="asc"
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        showPagination={totalPages > 1}
        renderRow={(insurance, index, moduleColors, visibleColumns) => (
          <tr key={insurance.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('name') && (
              <td className="px-4 py-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center">
                    <Building size={20} />
                  </div>
                  <div className="ml-4">
                    <div className="text-sm font-medium text-neutral-900 dark:text-neutral-100">
                      {insurance.name}
                    </div>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('company') && (
              <td className="px-4 py-4">
                {insurance.company ? (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0" />
                    <span className="text-neutral-700 dark:text-neutral-300">{insurance.company.name}</span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">
                    Convênio geral
                  </span>
                )}
              </td>
            )}

            {visibleColumns.includes('actions') && isAdmin && (
              <td className="px-4 py-4 text-right">
                <div className="flex justify-end gap-2">
                  <button
                    onClick={() => handleEditInsurance(insurance)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors"
                    title="Editar"
                  >
                    <Edit size={16} />
                  </button>
                  <button
                    onClick={() => handleDeleteInsurance(insurance)}
                    className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                    title="Excluir"
                  >
                    <Trash size={16} />
                  </button>
                </div>
              </td>
            )}
          </tr>
        )}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmDeleteInsurance}
        title="Excluir Convênio"
        message={`Tem certeza que deseja excluir o convênio "${selectedInsurance?.name}"? Esta ação não pode ser desfeita.`}
        variant="danger"
        confirmText="Excluir"
        cancelText="Cancelar"
      />

      {/* Insurance Form Modal */}
      {insuranceFormOpen && (
        <InsuranceFormModal
          isOpen={insuranceFormOpen}
          onClose={() => setInsuranceFormOpen(false)}
          insurance={selectedInsurance}
          onSuccess={() => {
            setInsuranceFormOpen(false);
            loadInsurances();
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default InsurancesPage;