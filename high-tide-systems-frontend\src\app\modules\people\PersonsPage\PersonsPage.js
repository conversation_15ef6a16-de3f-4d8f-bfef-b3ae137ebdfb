"use client";

import React, { useState, useEffect, useCallback } from "react";
import TutorialManager from "@/components/tutorial/TutorialManager";
import TutorialTriggerButton from "@/components/tutorial/TutorialTriggerButton";
import ModuleHeader, { FilterButton } from "@/components/ui/ModuleHeader";
import { ModuleSelect, ModuleTable } from "@/components/ui";
import MultiSelect from "@/components/ui/multi-select";
import {
  Plus,
  Search,
  Filter,
  RefreshCw,
  Edit,
  Trash,
  User,
  Power,
  CheckCircle,
  XCircle,
  Mail,
  Phone,
  CreditCard,
  Calendar,
  Users,
  FileText,
  Eye,
} from "lucide-react";
import { personsService } from "@/app/modules/people/services/personsService";
import { useAuth } from "@/contexts/AuthContext";
import ConfirmationDialog from "@/components/ui/ConfirmationDialog";
import PersonFormModal from "@/components/people/PersonFormModal.js";
import ExportMenu from "@/components/ui/ExportMenu";

import { formatDate, formatDateTime } from "@/utils/dateUtils";
import Link from "next/link";
import { companyService } from "@/app/modules/admin/services/companyService";

// Tutorial steps para a página de pacientes
const personsTutorialSteps = [
  {
    title: "Pacientes",
    content: "Esta tela permite gerenciar o cadastro de pacientes no sistema.",
    selector: "h1",
    position: "bottom"
  },
  {
    title: "Adicionar Novo Paciente",
    content: "Clique aqui para adicionar um novo paciente.",
    selector: "button:has(span:contains('Novo Paciente'))",
    position: "left"
  },
  {
    title: "Filtrar Pacientes",
    content: "Use esta barra de pesquisa para encontrar pacientes específicos pelo nome, email ou CPF.",
    selector: "input[placeholder*='Buscar']",
    position: "bottom"
  },
  {
    title: "Filtrar por Status",
    content: "Filtre os pacientes por status (ativos ou inativos).",
    selector: "select:first-of-type",
    position: "bottom"
  },
  {
    title: "Filtrar por Tipo",
    content: "Filtre os pacientes por tipo de relacionamento (titular ou dependente).",
    selector: "select:nth-of-type(2)",
    position: "bottom"
  },
  {
    title: "Filtrar por Empresa",
    content: "Filtre os pacientes pela empresa a que pertencem.",
    selector: "select:nth-of-type(3)",
    position: "bottom"
  },
  {
    title: "Filtrar por Múltiplos Pacientes",
    content: "Selecione um ou mais pacientes pelo nome completo para filtrar a lista.",
    selector: "div:has(> label:contains('Filtrar por pacientes específicos'))",
    position: "bottom"
  },
  {
    title: "Exportar Dados",
    content: "Exporte a lista de pacientes em diferentes formatos usando este botão.",
    selector: ".export-button",
    position: "bottom"
  },
  {
    title: "Gerenciar Pacientes",
    content: "Visualize, edite, ative/desative ou exclua pacientes usando os botões de ação na tabela.",
    selector: "table",
    position: "top"
  }
];

const PersonsPage = () => {
  const { user: currentUser } = useAuth();
  const [persons, setPersons] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [totalPersons, setTotalPersons] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [personsFilter, setPersonsFilter] = useState([]);
  const [personOptions, setPersonOptions] = useState([]);
  const [isLoadingPersonOptions, setIsLoadingPersonOptions] = useState(false);
  const [statusFilter, setStatusFilter] = useState("");
  const [relationshipFilter, setRelationshipFilter] = useState("");
  const [companyFilter, setCompanyFilter] = useState("");
  const [companies, setCompanies] = useState([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState(null);
  const [actionToConfirm, setActionToConfirm] = useState(null);
  const [personFormOpen, setPersonFormOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Constants
  const ITEMS_PER_PAGE = 10;

  // Função para carregar empresas (apenas para system_admin)
  const loadCompanies = async () => {
    if (currentUser?.role !== "SYSTEM_ADMIN") return;

    setIsLoadingCompanies(true);
    try {
      const response = await companyService.getCompaniesForSelect();
      setCompanies(response);
    } catch (error) {
      console.error("Erro ao carregar empresas:", error);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  // Função para carregar opções de pacientes para o multi-select
  const loadPersonOptions = useCallback(async () => {
    setIsLoadingPersonOptions(true);
    try {
      // Carregar todos os pacientes para o multi-select (com limite maior)
      const response = await personsService.getPersons({
        limit: 100, // Limite maior para ter mais opções
        active: true // Apenas pacientes ativos por padrão
      });

      const options = response?.persons?.map(person => ({
        value: person.id,
        label: person.fullName,
        // Guardar o nome para ordenação
        sortName: person.fullName.toLowerCase()
      })) || [];

      // Ordenar as opções alfabeticamente pelo nome
      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName));

      setPersonOptions(sortedOptions);
    } catch (error) {
      console.error("Erro ao carregar opções de pacientes:", error);
      setPersonOptions([]);
    } finally {
      setIsLoadingPersonOptions(false);
    }
  }, []);

  const loadPersons = async (
    page = currentPage,
    searchQuery = search,
    personIds = personsFilter,
    status = statusFilter,
    relationship = relationshipFilter,
    company = companyFilter
  ) => {
    setIsLoading(true);
    try {
      const response = await personsService.getPersons({
        page,
        limit: ITEMS_PER_PAGE,
        search: searchQuery || undefined,
        personIds: personIds.length > 0 ? personIds : undefined,
        active: status === "" ? undefined : status === "active",
        relationship: relationship || undefined,
        companyId: company || undefined,
      });

      // Make sure we always have an array even if the API response is different
      // Adicionando "people" que é o que a API realmente retorna
      setPersons(response?.persons || response?.people || response?.data || []);
      setTotalPersons(response?.total || 0);
      setTotalPages(response?.pages || 1);
      setCurrentPage(page);
    } catch (error) {
      console.error("Erro ao carregar pessoas:", error);
      setPersons([]);
      setTotalPersons(0);
      setTotalPages(1);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadPersons();
    loadPersonOptions();
    // Carregar empresas se o usuário for system_admin
    if (currentUser?.role === "SYSTEM_ADMIN") {
      loadCompanies();
    }
  }, [loadPersonOptions]);

  const handleSearch = (e) => {
    e.preventDefault();
    loadPersons(1, search, personsFilter, statusFilter, relationshipFilter, companyFilter);
  };

  const handlePersonsFilterChange = (value) => {
    setPersonsFilter(value);
    loadPersons(1, search, value, statusFilter, relationshipFilter, companyFilter);
  };

  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    loadPersons(1, search, personsFilter, value, relationshipFilter, companyFilter);
  };

  const handleRelationshipFilterChange = (value) => {
    setRelationshipFilter(value);
    loadPersons(1, search, personsFilter, statusFilter, value, companyFilter);
  };

  const handleCompanyFilterChange = (value) => {
    setCompanyFilter(value);
    loadPersons(1, search, personsFilter, statusFilter, relationshipFilter, value);
  };

  const handlePageChange = (page) => {
    loadPersons(page, search, personsFilter, statusFilter, relationshipFilter, companyFilter);
  };

  const handleResetFilters = () => {
    setSearch("");
    setPersonsFilter([]);
    setStatusFilter("");
    setRelationshipFilter("");
    setCompanyFilter("");
    loadPersons(1, "", [], "", "", "");
  };

  const handleEditPerson = async (person) => {
    console.log('Editando pessoa:', person);
    console.log('URL da imagem de perfil na listagem:', person.profileImageFullUrl);

    try {
      // Buscar dados completos da pessoa antes de abrir o modal
      const personData = await personsService.getPerson(person.id);
      console.log('Dados completos da pessoa:', personData);
      console.log('URL da imagem de perfil nos dados completos:', personData.profileImageFullUrl);

      setSelectedPerson(personData);
      setPersonFormOpen(true);
    } catch (error) {
      console.error('Erro ao buscar dados da pessoa:', error);
      // Fallback para os dados da listagem
      setSelectedPerson(person);
      setPersonFormOpen(true);
    }
  };

  const handleToggleStatus = (person) => {
    setSelectedPerson(person);
    setActionToConfirm({
      type: "toggle-status",
      message: `${person.active ? "Desativar" : "Ativar"} a pessoa ${
        person.fullName
      }?`,
    });
    setConfirmationDialogOpen(true);
  };

  const handleDeletePerson = (person) => {
    setSelectedPerson(person);
    setActionToConfirm({
      type: "delete",
      message: `Excluir permanentemente a pessoa ${person.fullName}?`,
    });
    setConfirmationDialogOpen(true);
  };

  const confirmAction = async () => {
    if (actionToConfirm.type === "toggle-status") {
      try {
        await personsService.togglePersonStatus(selectedPerson.id);
        loadPersons();
      } catch (error) {
        console.error("Erro ao alterar status da pessoa:", error);
      }
    } else if (actionToConfirm.type === "delete") {
      try {
        await personsService.deletePerson(selectedPerson.id);
        loadPersons();
      } catch (error) {
        console.error("Erro ao excluir pessoa:", error);
      }
    }
    setConfirmationDialogOpen(false);
  };

  const handleExport = async (format) => {
    setIsExporting(true);
    try {
      // Encontrar o nome da empresa selecionada para o subtítulo da exportação
      let companyName;
      if (companyFilter) {
        const selectedCompany = companies.find(c => c.id === companyFilter);
        companyName = selectedCompany ? selectedCompany.name : undefined;
      }

      // Exportar usando os mesmos filtros da tabela atual
      await personsService.exportPersons({
        search: search || undefined,
        personIds: personsFilter.length > 0 ? personsFilter : undefined,
        active: statusFilter === "" ? undefined : statusFilter === "active",
        relationship: relationshipFilter || undefined,
        companyId: companyFilter || undefined,
        companyName
      }, format);
    } catch (error) {
      console.error("Erro ao exportar pessoas:", error);
      // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast
    } finally {
      setIsExporting(false);
    }
  };

  // Usando o utilitário centralizado de formatação de datas

  const formatCPF = (cpf) => {
    if (!cpf) return "N/A";

    // CPF format: 000.000.000-00
    const cpfNumbers = cpf.replace(/\D/g, "");
    return cpfNumbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
  };

  const formatPhone = (phone) => {
    if (!phone) return "N/A";

    // Phone format: (00) 00000-0000
    const phoneNumbers = phone.replace(/\D/g, "");
    return phoneNumbers.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
  };

  const isAdmin = currentUser?.modules?.includes("ADMIN");
  const isSystemAdmin = currentUser?.role === "SYSTEM_ADMIN";

  return (
    <div className="space-y-6">
      {/* Título e botões de exportar e adicionar */}
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold text-slate-800 dark:text-white flex items-center">
          <User size={24} className="mr-2 text-orange-600 dark:text-orange-400" />
          Pacientes
        </h1>

        <div className="flex items-center gap-2">
          {/* Botão de exportar */}
          <ExportMenu
            onExport={handleExport}
            isExporting={isExporting}
            disabled={isLoading || persons.length === 0}
            className="text-orange-700 dark:text-orange-300"
          />

          {/* Botão de adicionar */}
          <button
            onClick={() => {
              setSelectedPerson(null);
              setPersonFormOpen(true);
            }}
            className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all"
          >
            <Plus size={18} />
            <span className="font-medium">Novo Paciente</span>
          </button>
        </div>
      </div>

      {/* Cabeçalho e filtros da página */}
      <ModuleHeader
        title="Filtros"
        icon={<Filter size={22} className="text-module-people-icon dark:text-module-people-icon-dark" />}
        description="Gerencie o cadastro de pacientes no sistema. Utilize os filtros abaixo para encontrar pacientes específicos."
        tutorialSteps={personsTutorialSteps}
        tutorialName="persons-overview"
        moduleColor="people"
        filters={
          <form
            onSubmit={handleSearch}
            className="flex flex-col gap-4"
          >
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Buscar por nome, email ou CPF..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100"
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-2">
            <div className="w-full sm:w-40">
              <ModuleSelect
                moduleColor="people"
                value={statusFilter}
                onChange={(e) => handleStatusFilterChange(e.target.value)}
                placeholder="Status"
              >
                <option value="">Todos os status</option>
                <option value="active">Ativos</option>
                <option value="inactive">Inativos</option>
              </ModuleSelect>
            </div>

            <div className="w-full sm:w-48">
              <ModuleSelect
                moduleColor="people"
                value={relationshipFilter}
                onChange={(e) => handleRelationshipFilterChange(e.target.value)}
                placeholder="Tipo"
              >
                <option value="">Todos os tipos</option>
                <option value="Titular">Titular</option>
                <option value="Dependente">Dependente</option>
              </ModuleSelect>
            </div>

            {/* Filtro de empresa (apenas para system_admin) */}
            {isSystemAdmin && (
              <div className="w-full sm:w-48">
                <ModuleSelect
                  moduleColor="people"
                  value={companyFilter}
                  onChange={(e) => handleCompanyFilterChange(e.target.value)}
                  placeholder="Empresa"
                  disabled={isLoadingCompanies}
                >
                  <option value="">Todas as empresas</option>
                  {companies.map((company) => (
                    <option key={company.id} value={company.id}>
                      {company.name}
                    </option>
                  ))}
                </ModuleSelect>
              </div>
            )}

            <FilterButton type="submit" moduleColor="people" variant="primary">
              <Filter size={16} className="sm:hidden" />
              <span className="hidden sm:inline">Filtrar</span>
            </FilterButton>

            <FilterButton
              type="button"
              onClick={handleResetFilters}
              moduleColor="people"
              variant="secondary"
            >
              <RefreshCw size={16} className="sm:hidden" />
              <span className="hidden sm:inline">Limpar</span>
            </FilterButton>
          </div>
            </div>

            {/* Multi-select para filtrar por múltiplos pacientes */}
            <div className="w-full">
              <MultiSelect
                label="Filtrar por Pacientes"
                value={personsFilter}
                onChange={handlePersonsFilterChange}
                options={personOptions}
                placeholder="Selecione um ou mais pacientes pelo nome..."
                loading={isLoadingPersonOptions}
                moduleOverride="people"
              />
            </div>
        </form>
      }
      />

      {/* Tabela de Pacientes */}
      <ModuleTable
        moduleColor="people"
        columns={[
          { header: 'Paciente', field: 'fullName', width: '20%' },
          { header: 'Contato', field: 'contact', width: '20%' },
          { header: 'CPF', field: 'cpf', width: '15%' },
          { header: 'Relação', field: 'client', width: '10%' },
          { header: 'Status', field: 'active', width: '10%' },
          { header: 'Cadastro', field: 'createdAt', width: '10%' },
          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }
        ]}
        data={persons}
        isLoading={isLoading}
        emptyMessage="Nenhuma pessoa encontrada"
        emptyIcon={<User size={24} />}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalPersons}
        onPageChange={handlePageChange}
        showPagination={totalPages > 1}
        tableId="people-persons-table"
        enableColumnToggle={true}
        defaultSortField="fullName"
        defaultSortDirection="asc"
        renderRow={(person, index, moduleColors, visibleColumns) => (
          <tr key={person.id} className={moduleColors.hoverBg}>
            {visibleColumns.includes('fullName') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium overflow-hidden">
                    {person.profileImageFullUrl ? (
                      <img
                        src={person.profileImageFullUrl}
                        alt={person.fullName}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.style.display = 'none';
                          e.target.parentNode.innerHTML = person.fullName.charAt(0).toUpperCase();
                        }}
                      />
                    ) : (
                      person.fullName.charAt(0).toUpperCase()
                    )}
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-neutral-800 dark:text-neutral-100">
                      <Link
                        href={`/dashboard/people/persons/${person.id}`}
                        className="hover:text-primary-600 dark:hover:text-primary-400 hover:underline"
                      >
                        {person.fullName}
                      </Link>
                    </p>
                    <div className="flex items-center text-xs text-neutral-500 dark:text-neutral-400">
                      <Calendar className="h-3 w-3 mr-1" />
                      {person.birthDate
                        ? formatDate(person.birthDate)
                        : "Sem data"}
                    </div>
                  </div>
                </div>
              </td>
            )}

            {visibleColumns.includes('contact') && (
              <td className="px-6 py-4">
                <div className="text-sm text-neutral-600 dark:text-neutral-300">
                  {person.email && (
                    <div className="flex items-center gap-1 mb-1">
                      <Mail className="h-3 w-3 text-neutral-400 dark:text-neutral-500" />
                      <span>{person.email}</span>
                    </div>
                  )}
                  {person.phone && (
                    <div className="flex items-center gap-1">
                      <Phone className="h-3 w-3 text-neutral-400 dark:text-neutral-500" />
                      <span>{formatPhone(person.phone)}</span>
                    </div>
                  )}
                  {!person.email && !person.phone && (
                    <span className="text-neutral-400 dark:text-neutral-500">Sem contato</span>
                  )}
                </div>
              </td>
            )}

            {visibleColumns.includes('cpf') && (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-neutral-300">
                <div className="flex items-center gap-1">
                  <CreditCard className="h-3 w-3 text-neutral-400 dark:text-neutral-500" />
                  {person.cpf ? formatCPF(person.cpf) : "Não informado"}
                </div>
              </td>
            )}

            {visibleColumns.includes('client') && (
              <td className="px-6 py-4 whitespace-nowrap">
                {person.clientPersons && person.clientPersons.length > 0 ? (
                  <div className="flex items-center">
                    <Users className="h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1" />
                    <span className="px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400">
                      {person.relationship || "Titular"}
                    </span>
                  </div>
                ) : (
                  <span className="text-neutral-400 dark:text-neutral-500 text-sm">
                    Sem cliente
                  </span>
                )}
              </td>
            )}

            {visibleColumns.includes('active') && (
              <td className="px-6 py-4 whitespace-nowrap">
                <span
                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${
                    person.active
                      ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400"
                      : "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400"
                  }`}
                >
                  {person.active ? (
                    <>
                      <CheckCircle size={12} />
                      <span>Ativo</span>
                    </>
                  ) : (
                    <>
                      <XCircle size={12} />
                      <span>Inativo</span>
                    </>
                  )}
                </span>
              </td>
            )}

            {visibleColumns.includes('createdAt') && (
              <td className="px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-neutral-300">
                {formatDate(person.createdAt)}
              </td>
            )}

            {visibleColumns.includes('actions') && (
              <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div className="flex justify-end gap-2">
                <Link
                  href={`/dashboard/people/persons/${person.id}`}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                  title="Visualizar"
                >
                  <Eye size={18} />
                </Link>
                <button
                  onClick={() => handleEditPerson(person)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors"
                  title="Editar"
                >
                  <Edit size={18} />
                </button>

                <button
                  onClick={() => handleToggleStatus(person)}
                  className={`p-1 transition-colors ${
                    person.active
                      ? "text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400"
                      : "text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400"
                  }`}
                  title={person.active ? "Desativar" : "Ativar"}
                >
                  <Power size={18} />
                </button>

                <button
                  onClick={() => handleDeletePerson(person)}
                  className="p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors"
                  title="Excluir"
                >
                  <Trash size={18} />
                </button>
              </div>
            </td>
            )}
          </tr>
        )}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmationDialogOpen}
        onClose={() => setConfirmationDialogOpen(false)}
        onConfirm={confirmAction}
        title="Confirmar ação"
        message={actionToConfirm?.message || ""}
      />

      {/* Person Form Modal */}
      {personFormOpen && (
        <PersonFormModal
          isOpen={personFormOpen}
          onClose={() => setPersonFormOpen(false)}
          person={selectedPerson}
          onSuccess={() => {
            setPersonFormOpen(false);
            loadPersons();
          }}
        />
      )}

      {/* Gerenciador de tutorial */}
      <TutorialManager />
    </div>
  );
};

export default PersonsPage;