'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  ShieldCheck,
  Calendar,
  Users,
  DollarSign,
  BarChart4,
  Check,
  Info,
  ChevronRight,
  X
} from 'lucide-react';
import { scrollToElement } from '@/utils/scrollUtils';

const Pricing = () => {
  const [selectedModules, setSelectedModules] = useState([]);
  const [isAnnual, setIsAnnual] = useState(true);
  const [activeTab, setActiveTab] = useState('modules'); // 'modules' ou 'comparison'

  // Definição dos módulos e seus preços
  const modules = [
    {
      id: 'scheduler',
      icon: <Calendar size={24} />,
      title: 'Agendamento',
      description: 'Gerencie consultas, compromissos e disponibilidade dos profissionais.',
      features: [
        'Visualização diária, semanal e mensal',
        'Agendamentos recorrentes',
        'Detecção de conflitos',
        'Horários de trabalho personalizáveis',
        'Notificações automáticas'
      ],
      monthlyPrice: 99,
      annualPrice: 990, // Preço anual (equivalente a 10 meses)
      color: 'scheduler',
      colorClass: 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border-purple-200 dark:border-purple-800/50'
    },
    {
      id: 'people',
      icon: <Users size={24} />,
      title: 'Pessoas',
      description: 'Cadastro completo de pacientes, profissionais e colaboradores.',
      features: [
        'Cadastro de pacientes e contatos',
        'Histórico de atendimentos',
        'Gestão de convênios',
        'Documentos digitalizados',
        'Relacionamentos familiares'
      ],
      monthlyPrice: 79,
      annualPrice: 790,
      color: 'people',
      colorClass: 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400 border-amber-200 dark:border-amber-800/50'
    },
    {
      id: 'financial',
      icon: <DollarSign size={24} />,
      title: 'Financeiro',
      description: 'Controle financeiro completo com faturamento, pagamentos e relatórios.',
      features: [
        'Faturamento de convênios',
        'Controle de pagamentos',
        'Relatórios financeiros',
        'Fluxo de caixa',
        'Integração com sistemas contábeis'
      ],
      monthlyPrice: 129,
      annualPrice: 1290,
      color: 'financial',
      colorClass: 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 border-emerald-200 dark:border-emerald-800/50'
    },
    {
      id: 'admin',
      icon: <ShieldCheck size={24} />,
      title: 'Administração',
      description: 'Gerencie usuários, permissões, configurações e controle do sistema.',
      features: [
        'Gestão de usuários e permissões',
        'Configurações do sistema',
        'Logs de atividades',
        'Backup de dados',
        'Segurança e privacidade'
      ],
      monthlyPrice: 69,
      annualPrice: 690,
      color: 'admin',
      colorClass: 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border-red-200 dark:border-red-800/50'
    },
    {
      id: 'reports',
      icon: <BarChart4 size={24} />,
      title: 'Relatórios',
      description: 'Dashboards e relatórios detalhados para análise de desempenho.',
      features: [
        'Dashboard interativo',
        'Relatórios personalizáveis',
        'Análise de ocupação',
        'Indicadores de desempenho',
        'Exportação em diversos formatos'
      ],
      monthlyPrice: 89,
      annualPrice: 890,
      color: 'reports',
      colorClass: 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border-blue-200 dark:border-blue-800/50'
    }
  ];

  // Função para alternar a seleção de um módulo
  const toggleModule = (moduleId) => {
    if (selectedModules.includes(moduleId)) {
      setSelectedModules(selectedModules.filter(id => id !== moduleId));
    } else {
      setSelectedModules([...selectedModules, moduleId]);
    }
  };

  // Calcular o preço total com base nos módulos selecionados
  const calculateTotal = () => {
    let total = 0;
    selectedModules.forEach(moduleId => {
      const module = modules.find(m => m.id === moduleId);
      if (module) {
        total += isAnnual ? module.annualPrice : module.monthlyPrice;
      }
    });

    // Aplicar desconto com base no número de módulos selecionados
    if (selectedModules.length >= 3) {
      total = total * 0.9; // 10% de desconto para 3 ou mais módulos
    } else if (selectedModules.length === 2) {
      total = total * 0.95; // 5% de desconto para 2 módulos
    }

    return total;
  };

  // Calcular o valor mensal (mesmo para plano anual)
  const calculateMonthlyValue = () => {
    const total = calculateTotal();
    return isAnnual ? (total / 12).toFixed(0) : total;
  };

  return (
    <section id="pricing" className="py-16 bg-gray-50 dark:bg-gray-800">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          className="text-center max-w-3xl mx-auto mb-10"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Preços flexíveis baseados em módulos
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
            Pague apenas pelos módulos que você precisa. Escolha os módulos ideais para o seu negócio.
          </p>

          {/* Tabs para alternar entre visualizações */}
          <div className="flex justify-center space-x-4 mb-6">
            <button
              onClick={() => setActiveTab('modules')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'modules'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Módulos
            </button>
            <button
              onClick={() => setActiveTab('comparison')}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === 'comparison'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              Comparativo
            </button>
          </div>

          {/* Toggle Anual/Mensal */}
          <div className="flex items-center justify-center">
            <span className={`mr-3 ${!isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'}`}>
              Mensal
            </span>
            <button
              onClick={() => setIsAnnual(!isAnnual)}
              className={`relative inline-flex h-6 w-14 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                isAnnual ? 'bg-blue-600' : 'bg-gray-300 dark:bg-gray-600'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition ${
                  isAnnual ? 'translate-x-9' : 'translate-x-1'
                }`}
              />
            </button>
            <span className={`ml-3 ${isAnnual ? 'text-gray-900 dark:text-white font-medium' : 'text-gray-600 dark:text-gray-300'}`}>
              Anual <span className="text-green-500 text-sm font-medium">(2 meses grátis)</span>
            </span>
          </div>
        </motion.div>

        {/* Conteúdo baseado na tab ativa */}
        {activeTab === 'modules' ? (
          <ModulesView
            modules={modules}
            selectedModules={selectedModules}
            toggleModule={toggleModule}
            isAnnual={isAnnual}
            calculateMonthlyValue={calculateMonthlyValue}
            calculateTotal={calculateTotal}
          />
        ) : (
          <ComparisonView
            modules={modules}
            isAnnual={isAnnual}
            toggleModule={toggleModule}
            setActiveTab={setActiveTab}
          />
        )}

        {/* Informações adicionais */}
        <div className="mt-12 text-center max-w-5xl mx-auto">
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Todos os planos incluem:
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Suporte técnico</h4>
              <p className="text-gray-500 dark:text-gray-400 text-sm">Suporte por e-mail e chat em horário comercial</p>
            </div>
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Atualizações</h4>
              <p className="text-gray-500 dark:text-gray-400 text-sm">Acesso a todas as atualizações e novos recursos</p>
            </div>
            <div className="bg-white dark:bg-gray-900 p-4 rounded-lg shadow flex-1 min-w-[200px] max-w-[300px]">
              <h4 className="font-medium text-gray-900 dark:text-white mb-2">Segurança</h4>
              <p className="text-gray-500 dark:text-gray-400 text-sm">Backup diário e proteção de dados conforme LGPD</p>
            </div>
          </div>
          <p className="mt-6 text-sm text-gray-500 dark:text-gray-400">
            Preços para até 5 usuários. Para equipes maiores ou necessidades específicas, entre em contato para um orçamento personalizado.
          </p>
        </div>
      </div>
    </section>
  );
};

// Componente para a visualização de módulos
const ModulesView = ({ modules, selectedModules, toggleModule, isAnnual, calculateMonthlyValue, calculateTotal }) => {
  return (
    <>
      {/* Módulos em layout horizontal */}
      <div className="flex flex-wrap justify-center gap-4 mb-8">
        {modules.map((module, index) => (
          <motion.div
            key={module.id}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            className={`bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden border-2 transition-all flex-1 min-w-[250px] max-w-[350px] ${
              selectedModules.includes(module.id)
                ? (module.id === 'scheduler' ? 'border-purple-500 dark:border-purple-400' :
                   module.id === 'people' ? 'border-amber-500 dark:border-amber-400' :
                   module.id === 'financial' ? 'border-emerald-500 dark:border-emerald-400' :
                   module.id === 'admin' ? 'border-red-500 dark:border-red-400' :
                   'border-blue-500 dark:border-blue-400') + ' transform scale-[1.02]'
                : 'border-transparent'
            }`}
          >
            <div className="p-5 flex flex-col h-full justify-between">
              <div className="flex items-center mb-3">
                <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${module.colorClass}`}>
                  {module.icon}
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                    {module.title}
                  </h3>
                  <p className="text-gray-500 dark:text-gray-400 text-sm">
                    R$ {isAnnual ? (module.annualPrice / 12).toFixed(0) : module.monthlyPrice}/mês
                  </p>
                </div>
              </div>

              <p className="text-gray-600 dark:text-gray-300 mb-4 text-sm">
                {module.description}
              </p>

              <div className="mb-4">
                <div className="flex items-center mb-2">
                  <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300 text-sm">{module.features[0]}</span>
                </div>
                <div className="flex items-center mb-2">
                  <Check className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                  <span className="text-gray-600 dark:text-gray-300 text-sm">{module.features[1]}</span>
                </div>
              </div>

              <button
                onClick={() => toggleModule(module.id)}
                className={`w-full py-2 px-3 rounded-lg font-medium transition-colors text-sm ${
                  selectedModules.includes(module.id)
                    ? (module.id === 'scheduler' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-400 border border-purple-300 dark:border-purple-700' :
                       module.id === 'people' ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-400 border border-amber-300 dark:border-amber-700' :
                       module.id === 'financial' ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-400 border border-emerald-300 dark:border-emerald-700' :
                       module.id === 'admin' ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400 border border-red-300 dark:border-red-700' :
                       'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400 border border-blue-300 dark:border-blue-700')
                    : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700'
                }`}
              >
                {selectedModules.includes(module.id) ? 'Selecionado' : 'Selecionar'}
              </button>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Resumo e total */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        className="max-w-5xl mx-auto bg-white dark:bg-gray-900 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700"
      >
        <div className="flex flex-wrap">
          <div className="w-full lg:w-1/2 mb-4 lg:mb-0">
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
              Resumo do seu plano
            </h3>

            {selectedModules.length === 0 ? (
              <div className="text-gray-500 dark:text-gray-400 flex items-center">
                <Info className="h-5 w-5 mr-2 text-gray-400" />
                <p>Selecione pelo menos um módulo para ver o preço total</p>
              </div>
            ) : (
              <div className="flex flex-wrap gap-2">
                {selectedModules.map(moduleId => {
                  const module = modules.find(m => m.id === moduleId);
                  return (
                    <div key={moduleId} className="flex items-center bg-gray-100 dark:bg-gray-800 rounded-full py-1 px-3">
                      <div className={`w-5 h-5 rounded-full flex items-center justify-center mr-1 ${module.colorClass}`}>
                        {module.icon}
                      </div>
                      <span className="text-gray-700 dark:text-gray-300 text-sm">{module.title}</span>
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          <div className="w-full lg:w-1/2 text-center lg:text-right">
            {selectedModules.length > 0 && (
              <>
                <div className="mb-1">
                  <span className="text-gray-500 dark:text-gray-400 text-sm">Valor mensal:</span>
                  <div className="text-3xl font-bold text-gray-900 dark:text-white">
                    R$ {calculateMonthlyValue()}
                    <span className="text-sm text-gray-500 dark:text-gray-400 font-normal">/mês</span>
                  </div>
                </div>

                {isAnnual && (
                  <div className="text-gray-500 dark:text-gray-400 text-sm mb-3">
                    Valor total anual: R$ {calculateTotal().toFixed(0)}
                    <span className="text-green-500 ml-1">(economia de R$ {(calculateTotal() * 0.2).toFixed(0)})</span>
                  </div>
                )}

                {selectedModules.length >= 2 && (
                  <div className="text-green-500 text-sm mb-3">
                    Desconto aplicado: {selectedModules.length >= 3 ? '10%' : '5%'}
                  </div>
                )}

                <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-5 rounded-lg transition-colors">
                  Começar agora
                </button>
              </>
            )}
          </div>
        </div>
      </motion.div>
    </>
  );
};

// Componente para a visualização de comparação
const ComparisonView = ({ modules, isAnnual, toggleModule, setActiveTab }) => {
  return (
    <div className="overflow-x-auto mb-8">
      <table className="w-full bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden">
        <thead>
          <tr className="bg-gray-100 dark:bg-purple-900/20">
            <th className="py-4 px-6 text-left text-gray-700 dark:text-gray-300 font-medium">Recurso</th>
            {modules.map(module => (
              <th key={module.id} className="py-4 px-6 text-center">
                <div className={`inline-flex items-center justify-center w-8 h-8 rounded-lg mb-2 ${module.colorClass}`}>
                  {module.icon}
                </div>
                <div className="text-gray-900 dark:text-white font-bold">{module.title}</div>
                <div className="text-gray-500 dark:text-gray-400 text-sm">
                  R$ {isAnnual ? (module.annualPrice / 12).toFixed(0) : module.monthlyPrice}/mês
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {/* Linhas de recursos */}
          {['Usuários incluídos', 'Armazenamento', 'Suporte', 'API Access', 'Integrações'].map((feature, index) => (
            <tr key={index} className="border-t border-gray-200 dark:border-gray-700">
              <td className="py-3 px-6 text-left text-gray-700 dark:text-gray-300">{feature}</td>
              {modules.map(module => (
                <td key={module.id} className="py-3 px-6 text-center text-gray-600 dark:text-gray-400">
                  {feature === 'Usuários incluídos' && '5 usuários'}
                  {feature === 'Armazenamento' && (module.id === 'admin' ? '10GB' : '5GB')}
                  {feature === 'Suporte' && (
                    module.id === 'financial' || module.id === 'admin'
                      ? 'Prioritário'
                      : 'Padrão'
                  )}
                  {feature === 'API Access' && (
                    module.id === 'admin' || module.id === 'reports'
                      ? <Check className="h-5 w-5 text-green-500 mx-auto" />
                      : <X className="h-5 w-5 text-red-500 mx-auto" />
                  )}
                  {feature === 'Integrações' && (
                    module.id === 'financial' || module.id === 'scheduler' || module.id === 'admin'
                      ? <Check className="h-5 w-5 text-green-500 mx-auto" />
                      : <X className="h-5 w-5 text-red-500 mx-auto" />
                  )}
                </td>
              ))}
            </tr>
          ))}

          {/* Linha de botões */}
          <tr className="border-t border-gray-200 dark:border-gray-700">
            <td className="py-4 px-6"></td>
            {modules.map(module => (
              <td key={module.id} className="py-4 px-6 text-center">
                <button
                  onClick={() => {
                    setActiveTab('modules');
                    setTimeout(() => {
                      scrollToElement('pricing', 80);
                      toggleModule(module.id);
                    }, 100);
                  }}
                  className={`py-2 px-4 rounded-lg font-medium text-sm transition-colors ${
                    module.id === 'scheduler' ? 'bg-purple-600 hover:bg-purple-700 text-white' :
                    module.id === 'people' ? 'bg-amber-600 hover:bg-amber-700 text-white' :
                    module.id === 'financial' ? 'bg-emerald-600 hover:bg-emerald-700 text-white' :
                    module.id === 'admin' ? 'bg-red-600 hover:bg-red-700 text-white' :
                    'bg-blue-600 hover:bg-blue-700 text-white'
                  }`}
                >
                  Selecionar
                </button>
              </td>
            ))}
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default Pricing;
