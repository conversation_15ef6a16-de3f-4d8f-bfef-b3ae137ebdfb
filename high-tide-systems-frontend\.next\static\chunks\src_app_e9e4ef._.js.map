{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/clientsService.js"], "sourcesContent": ["// src/app/modules/people/services/clientsService.js\r\nimport { exportService } from \"@/app/services/exportService\";\r\nimport { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { extractData, extractEntity } from \"@/utils/apiResponseAdapter\";\r\n\r\nexport const clientsService = {\r\n  // Get clients with optional filters\r\n  getClients: async (filters = {}) => {\r\n    // Ensure page is a number and at least 1\r\n    const page = parseInt(filters.page, 10) || 1;\r\n    const limit = parseInt(filters.limit, 10) || 10;\r\n    const {\r\n      search = \"\",\r\n      clientIds,\r\n      active,\r\n      companyId,\r\n      include_persons,\r\n      sortField = 'fullName',  // Default sort by fullName (backend controller will handle this)\r\n      sortDirection = 'asc'    // Default sort direction\r\n    } = filters;\r\n\r\n    try {\r\n      // Construir parâmetros para a API\r\n      const params = {\r\n        page,\r\n        limit,\r\n        search: search || undefined,\r\n        active: active === undefined ? undefined : active,\r\n        companyId: companyId || undefined,\r\n        include_persons: include_persons || undefined,\r\n        sortField,\r\n        sortDirection\r\n      };\r\n\r\n      // Adicionar clientIds como parâmetros separados com notação de array\r\n      if (clientIds && clientIds.length > 0) {\r\n        // Garantir que clientIds seja um array\r\n        const clientIdsArray = Array.isArray(clientIds) ? clientIds : [clientIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        clientIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          params[`clientIds[${index}]`] = id;\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de clientes:\", clientIdsArray);\r\n      }\r\n\r\n      const response = await api.get(\"/clients\", { params });\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      return extractData(response.data, 'clients', ['data']);\r\n    } catch (error) {\r\n      console.error(\"Error fetching clients:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get a single client by ID\r\n  getClient: async (id) => {\r\n    try {\r\n      const response = await api.get(`/clients/${id}`);\r\n      // Usar o adaptador para extrair a entidade\r\n      return extractEntity(response.data);\r\n    } catch (error) {\r\n      console.error(`Error fetching client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create a new client\r\n  createClient: async (clientData) => {\r\n    try {\r\n      // Ajustar payload para incluir dados da pessoa\r\n      const response = await api.post(\"/clients\", clientData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating client:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update an existing client\r\n  updateClient: async (id, clientData) => {\r\n    try {\r\n      const response = await api.put(`/clients/${id}`, clientData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error updating client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Toggle client active status\r\n  toggleClientStatus: async (id) => {\r\n    try {\r\n      const response = await api.patch(`/clients/${id}/status`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error toggling status for client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a client (soft delete)\r\n  deleteClient: async (id) => {\r\n    try {\r\n      const response = await api.delete(`/clients/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error deleting client ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get persons associated with a client\r\n  getClientPersons: async (clientId) => {\r\n    try {\r\n      const response = await api.get(`/clients/${clientId}/persons`);\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      const { persons } = extractData(response.data, 'persons', ['people']);\r\n      return persons;\r\n    } catch (error) {\r\n      console.error(`Error fetching persons for client ${clientId}:`, error);\r\n      return [];\r\n    }\r\n  },\r\n  /**\r\n * Exporta a lista de clientes com os filtros aplicados\r\n * @param {Object} filters - Filtros atuais (busca, status, etc)\r\n * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n */\r\n  exportClients: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await clientsService.getClients({\r\n        ...filters,\r\n        limit: 1000, // Aumentamos o limite para exportar mais dados\r\n      });\r\n\r\n      // Extrair os dados dos clientes usando o adaptador\r\n      const { clients } = extractData(response, 'clients', ['data']);\r\n      const data = clients;\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"fullName\", header: \"Cliente\" },\r\n        { key: \"login\", header: \"Login\" },\r\n        { key: \"email\", header: \"Email\" },\r\n        {\r\n          key: \"personsCount\",\r\n          header: \"Nº de Pessoas\",\r\n          format: (_, item) => item.persons ? item.persons.length : 0\r\n        },\r\n        {\r\n          key: \"active\",\r\n          header: \"Status\",\r\n          format: (value) => value ? \"Ativo\" : \"Inativo\",\r\n          align: \"center\",\r\n          width: 20\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = data.map(client => {\r\n        // Obter o nome completo do titular ou usar o login como fallback\r\n        const fullName = client.persons && client.persons[0] && client.persons[0].fullName\r\n          ? client.persons[0].fullName\r\n          : client.login;\r\n\r\n        return {\r\n          fullName: fullName,\r\n          login: client.login || \"\",\r\n          email: client.email || \"\",\r\n          personsCount: client.persons ? client.persons.length : 0,\r\n          active: client.active,\r\n          createdAt: client.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.clientIds && filters.clientIds.length > 0) {\r\n        subtitleParts.push(`Clientes específicos: ${filters.clientIds.length} selecionados`);\r\n      }\r\n      if (filters.active !== undefined) {\r\n        subtitleParts.push(`Status: ${filters.active ? \"Ativos\" : \"Inativos\"}`);\r\n      }\r\n      if (filters.companyId) {\r\n        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"clientes\",\r\n        columns,\r\n        title: \"Lista de Clientes\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar clientes:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\nexport default clientsService;"], "names": [], "mappings": "AAAA,oDAAoD;;;;;AACpD;AACA;AAGA;AAFA;AACA;;;;;;AAGO,MAAM,iBAAiB;IAC5B,oCAAoC;IACpC,YAAY,OAAO,UAAU,CAAC,CAAC;QAC7B,yCAAyC;QACzC,MAAM,OAAO,SAAS,QAAQ,IAAI,EAAE,OAAO;QAC3C,MAAM,QAAQ,SAAS,QAAQ,KAAK,EAAE,OAAO;QAC7C,MAAM,EACJ,SAAS,EAAE,EACX,SAAS,EACT,MAAM,EACN,SAAS,EACT,eAAe,EACf,YAAY,UAAU,EACtB,gBAAgB,MAAS,yBAAyB;QAA7B,EACtB,GAAG;QAEJ,IAAI;YACF,kCAAkC;YAClC,MAAM,SAAS;gBACb;gBACA;gBACA,QAAQ,UAAU;gBAClB,QAAQ,WAAW,YAAY,YAAY;gBAC3C,WAAW,aAAa;gBACxB,iBAAiB,mBAAmB;gBACpC;gBACA;YACF;YAEA,qEAAqE;YACrE,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,uCAAuC;gBACvC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,YAAY;oBAAC;iBAAU;gBAEzE,+CAA+C;gBAC/C,eAAe,OAAO,CAAC,CAAC,IAAI;oBAC1B,yDAAyD;oBACzD,MAAM,CAAC,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG;gBAClC;gBAEA,QAAQ,GAAG,CAAC,4CAA4C;YAC1D;YAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,YAAY;gBAAE;YAAO;YAEpD,8DAA8D;YAC9D,OAAO,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,WAAW;gBAAC;aAAO;QACvD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,WAAW,OAAO;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;YAC/C,2CAA2C;YAC3C,OAAO,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,cAAc,OAAO;QACnB,IAAI;YACF,+CAA+C;YAC/C,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,YAAY;YAC5C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,cAAc,OAAO,IAAI;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI,EAAE;YACjD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC,EAAE;YACzD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC9C,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,kBAAkB,OAAO;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,QAAQ,CAAC;YAC7D,8DAA8D;YAC9D,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,WAAW;gBAAC;aAAS;YACpE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;YAChE,OAAO,EAAE;QACX;IACF;IACA;;;;;CAKD,GACC,eAAe,OAAO,SAAS,eAAe,MAAM;QAClD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,eAAe,UAAU,CAAC;gBAC/C,GAAG,OAAO;gBACV,OAAO;YACT;YAEA,mDAAmD;YACnD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,UAAU,WAAW;gBAAC;aAAO;YAC7D,MAAM,OAAO;YAEb,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAY,QAAQ;gBAAU;gBACrC;oBAAE,KAAK;oBAAS,QAAQ;gBAAQ;gBAChC;oBAAE,KAAK;oBAAS,QAAQ;gBAAQ;gBAChC;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,GAAG,OAAS,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,MAAM,GAAG;gBAC5D;gBACA;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,QAAQ,UAAU;oBACrC,OAAO;oBACP,OAAO;gBACT;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;gBAC5B,iEAAiE;gBACjE,MAAM,WAAW,OAAO,OAAO,IAAI,OAAO,OAAO,CAAC,EAAE,IAAI,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC9E,OAAO,OAAO,CAAC,EAAE,CAAC,QAAQ,GAC1B,OAAO,KAAK;gBAEhB,OAAO;oBACL,UAAU;oBACV,OAAO,OAAO,KAAK,IAAI;oBACvB,OAAO,OAAO,KAAK,IAAI;oBACvB,cAAc,OAAO,OAAO,GAAG,OAAO,OAAO,CAAC,MAAM,GAAG;oBACvD,QAAQ,OAAO,MAAM;oBACrB,WAAW,OAAO,SAAS,IAAI;gBACjC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,cAAc,IAAI,CAAC,CAAC,sBAAsB,EAAE,QAAQ,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;YACrF;YACA,IAAI,QAAQ,MAAM,KAAK,WAAW;gBAChC,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,GAAG,WAAW,YAAY;YACxE;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,QAAQ,WAAW,IAAI,QAAQ,SAAS,EAAE;YAC3E;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,0IAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;AACF;uCAEe"}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 240, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/common/services/cepService.js"], "sourcesContent": ["// src/app/modules/common/services/cepService.js\r\nimport { api } from \"@/utils/api\";\r\n\r\nexport const cepService = {\r\n  /**\r\n   * Busca informações de endereço a partir de um CEP\r\n   * @param {string} cep - CEP a ser consultado (pode conter máscara)\r\n   * @returns {Promise<Object>} - Dados do endereço\r\n   */\r\n  searchByCep: async (cep) => {\r\n    try {\r\n      // Remove caracteres não numéricos\r\n      const cleanCep = cep.replace(/\\D/g, '');\r\n\r\n      // Valida o CEP (deve ter 8 dígitos)\r\n      if (cleanCep.length !== 8) {\r\n        throw new Error('CEP inválido. O CEP deve conter 8 dígitos.');\r\n      }\r\n\r\n      const response = await api.get(`/cep/${cleanCep}`);\r\n      console.log('Resposta da API de CEP:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Erro ao buscar CEP:', error);\r\n\r\n      // Formata a mensagem de erro\r\n      const errorMessage = error.response?.data?.message ||\r\n                          error.message ||\r\n                          'Erro ao buscar CEP';\r\n\r\n      throw new Error(errorMessage);\r\n    }\r\n  }\r\n};\r\n"], "names": [], "mappings": "AAAA,gDAAgD;;;;AAChD;;AAEO,MAAM,aAAa;IACxB;;;;GAIC,GACD,aAAa,OAAO;QAClB,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,IAAI,OAAO,CAAC,OAAO;YAEpC,oCAAoC;YACpC,IAAI,SAAS,MAAM,KAAK,GAAG;gBACzB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU;YACjD,QAAQ,GAAG,CAAC,2BAA2B,SAAS,IAAI;YACpD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YAErC,6BAA6B;YAC7B,MAAM,eAAe,MAAM,QAAQ,EAAE,MAAM,WACvB,MAAM,OAAO,IACb;YAEpB,MAAM,IAAI,MAAM;QAClB;IACF;AACF"}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/contactsService.js"], "sourcesContent": ["// src/app/modules/people/services/contactsService.js\r\nimport { api } from \"@/utils/api\";\r\n\r\nexport const contactsService = {\r\n  // Get contacts for a person\r\n  getContactsByPerson: async (personId) => {\r\n    try {\r\n      const response = await api.get(\"/contacts\", {\r\n        params: {\r\n          personId\r\n        }\r\n      });\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching contacts for person ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get a single contact by ID\r\n  getContact: async (id) => {\r\n    try {\r\n      const response = await api.get(`/contacts/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching contact ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Create a new contact\r\n  createContact: async (contactData) => {\r\n    try {\r\n      const response = await api.post(\"/contacts\", contactData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Error creating contact:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update an existing contact\r\n  updateContact: async (id, contactData) => {\r\n    try {\r\n      const response = await api.put(`/contacts/${id}`, contactData);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error updating contact ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Delete a contact\r\n  deleteContact: async (id) => {\r\n    try {\r\n      await api.delete(`/contacts/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Error deleting contact ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Get relationship options for dropdowns\r\n  getRelationshipOptions: () => {\r\n    return [\r\n      { value: \"Pai\", label: \"Pai\" },\r\n      { value: \"Mãe\", label: \"Mãe\" },\r\n      { value: \"Filho(a)\", label: \"Filho(a)\" },\r\n      { value: \"Irmão(ã)\", label: \"Irmão(ã)\" },\r\n      { value: \"Cônjuge\", label: \"Cônjuge\" },\r\n      { value: \"Amigo(a)\", label: \"Amigo(a)\" },\r\n      { value: \"Colega\", label: \"Colega\" },\r\n      { value: \"Vizinho(a)\", label: \"Vizinho(a)\" },\r\n      { value: \"Responsável\", label: \"Responsável\" },\r\n      { value: \"Outro\", label: \"Outro\" }\r\n    ];\r\n  }\r\n};\r\n\r\nexport default contactsService;"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AACrD;;AAEO,MAAM,kBAAkB;IAC7B,4BAA4B;IAC5B,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,aAAa;gBAC1C,QAAQ;oBACN;gBACF;YACF;YAEA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC,EAAE;YACjE,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;YAChD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,aAAa;YAC7C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,eAAe,OAAO,IAAI;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI,EAAE;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,IAAI;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,wBAAwB;QACtB,OAAO;YACL;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAO,OAAO;YAAM;YAC7B;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAW,OAAO;YAAU;YACrC;gBAAE,OAAO;gBAAY,OAAO;YAAW;YACvC;gBAAE,OAAO;gBAAU,OAAO;YAAS;YACnC;gBAAE,OAAO;gBAAc,OAAO;YAAa;YAC3C;gBAAE,OAAO;gBAAe,OAAO;YAAc;YAC7C;gBAAE,OAAO;gBAAS,OAAO;YAAQ;SAClC;IACH;AACF;uCAEe"}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/insurancesService.js"], "sourcesContent": ["// app/modules/people/services/insurancesService.js\r\nimport { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { extractData, extractEntity } from \"@/utils/apiResponseAdapter\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\n\r\nexport const insurancesService = {\r\n  // Obter lista de convênios (com suporte a filtragem)\r\n  getInsurances: async ({ search, companyId, insuranceIds, page = 1, limit = 10 } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (search) params.append('search', search);\r\n      if (companyId) params.append('companyId', companyId);\r\n      if (page) params.append('page', page);\r\n      if (limit) params.append('limit', limit);\r\n\r\n      // Adicionar insuranceIds como parâmetros separados com notação de array\r\n      if (insuranceIds && insuranceIds.length > 0) {\r\n        // Garantir que insuranceIds seja um array\r\n        const insuranceIdsArray = Array.isArray(insuranceIds) ? insuranceIds : [insuranceIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        insuranceIdsArray.forEach((id, index) => {\r\n          // Usar a notação de array para compatibilidade com a API\r\n          params.append(`insuranceIds[${index}]`, id);\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de convênios:\", insuranceIdsArray);\r\n      }\r\n\r\n      const response = await api.get(`/insurances?${params.toString()}`);\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      // Primeiro tentamos extrair usando o formato padrão\r\n      const extracted = extractData(response.data, 'insurances', ['data']);\r\n\r\n      // Se não houver insurances no formato padrão, processamos o array diretamente\r\n      if (extracted.insurances && extracted.insurances.length > 0) {\r\n        return extracted;\r\n      } else {\r\n        // Processar o array diretamente se a API retornar apenas um array\r\n        const insurances = Array.isArray(response.data) ? response.data : [];\r\n        return {\r\n          insurances,\r\n          total: insurances.length,\r\n          pages: Math.ceil(insurances.length / limit)\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar convênios:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um convênio específico\r\n  getInsurance: async (id) => {\r\n    try {\r\n      const response = await api.get(`/insurances/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um convênio pelo ID (versão simplificada que não lança erro)\r\n  getInsuranceById: async (id) => {\r\n    try {\r\n      console.log(`Buscando convênio com ID: ${id}`);\r\n      const response = await api.get(`/insurances/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar convênio com ID ${id}:`, error);\r\n      return null;\r\n    }\r\n  },\r\n\r\n  // Criar um novo convênio\r\n  createInsurance: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurances', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar convênio:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar um convênio existente\r\n  updateInsurance: async (id, data) => {\r\n    try {\r\n      const response = await api.put(`/insurances/${id}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir um convênio\r\n  deleteInsurance: async (id) => {\r\n    try {\r\n      await api.delete(`/insurances/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir convênio ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Adicionar um convênio a uma pessoa\r\n  addPersonInsurance: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurances/person', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao adicionar convênio à pessoa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Remover um convênio de uma pessoa\r\n  removePersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      await api.delete(`/insurances/person/${personId}/${insuranceId}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Erro ao remover convênio da pessoa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Listar convênios de uma pessoa - VERSÃO MELHORADA\r\n  listPersonInsurances: async (personId) => {\r\n    try {\r\n      console.log(`Buscando convênios para a pessoa ID: ${personId}`);\r\n      const response = await api.get(`/insurances/person/${personId}`);\r\n\r\n      // Detecta a estrutura retornada e normaliza\r\n      const data = response.data;\r\n\r\n      // Log para debug da estrutura de dados\r\n      console.log(`Resposta original da API (convenios pessoa ${personId}):`, data);\r\n\r\n      // Se for array, retorna diretamente\r\n      if (Array.isArray(data)) {\r\n        console.log(`Encontrados ${data.length} convênios no formato de array`);\r\n        return data;\r\n      }\r\n      // Se for objeto, procura por arrays\r\n      else if (data && typeof data === 'object') {\r\n        // Tenta encontrar um array dentro do objeto\r\n        const possibleArrayProps = ['personInsurances', 'insurances', 'data', 'items', 'results'];\r\n\r\n        // Primeiro procura nas propriedades comuns\r\n        for (const prop of possibleArrayProps) {\r\n          if (Array.isArray(data[prop])) {\r\n            console.log(`Encontrados ${data[prop].length} convênios na propriedade \"${prop}\"`);\r\n            return data[prop];\r\n          }\r\n        }\r\n\r\n        // Se não encontrou nas propriedades comuns, verifica todas as propriedades\r\n        for (const prop of Object.keys(data)) {\r\n          if (Array.isArray(data[prop])) {\r\n            console.log(`Encontrados ${data[prop].length} convênios na propriedade \"${prop}\"`);\r\n            return data[prop];\r\n          }\r\n        }\r\n\r\n        // Tenta extrair informações de convênios mesmo que não estejam em um array direto\r\n        if (data.insurance || data.insuranceId) {\r\n          console.log(`Encontrado um único convênio em formato não-array`);\r\n          const insurance = {\r\n            id: data.insuranceId || data.insurance?.id,\r\n            name: data.insuranceName || data.insurance?.name,\r\n            policyNumber: data.policyNumber,\r\n            validUntil: data.validUntil\r\n          };\r\n\r\n          return [insurance];\r\n        }\r\n      }\r\n\r\n      // Se não conseguir encontrar nenhum array, retorna array vazio\r\n      console.warn(\"Estrutura de resposta inesperada:\", data);\r\n      return [];\r\n    } catch (error) {\r\n      console.error(`Erro ao listar convênios da pessoa ${personId}:`, error);\r\n      // Em caso de erro, retorna array vazio em vez de lançar exceção para melhor UX\r\n      return [];\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de convênios com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  exportInsurances: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await insurancesService.getInsurances({\r\n        ...filters,\r\n        limit: 1000 // Aumentamos o limite para exportar mais dados\r\n      });\r\n\r\n      // Extrair os dados dos convênios\r\n      const insurancesArray = response.insurances || [];\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"name\", header: \"Nome\" },\r\n        {\r\n          key: \"companyName\",\r\n          header: \"Empresa\"\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = insurancesArray.map(insurance => {\r\n        return {\r\n          name: insurance.name || \"\",\r\n          companyName: insurance.company && insurance.company.name ? insurance.company.name : \"\",\r\n          createdAt: insurance.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.insuranceIds && filters.insuranceIds.length > 0) {\r\n        subtitleParts.push(`Convênios específicos: ${filters.insuranceIds.length} selecionados`);\r\n      }\r\n      if (filters.companyId) {\r\n        // Tentar encontrar o nome da empresa nos dados\r\n        const companyName = insurancesArray.find(i => i.company && i.company.id === filters.companyId)?.company?.name;\r\n        subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"convenios\",\r\n        columns,\r\n        title: \"Lista de Convênios\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar convênios:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  updatePersonInsurance: async (personId, insuranceId, data) => {\r\n    try {\r\n      const response = await api.put(`/insurances/person/${personId}/${insuranceId}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar convênio ${insuranceId} da pessoa ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter detalhes de um convênio específico de uma pessoa\r\n  getPersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      const response = await api.get(`/insurances/person/${personId}/${insuranceId}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar detalhes do convênio ${insuranceId} da pessoa ${personId}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default insurancesService;"], "names": [], "mappings": "AAAA,mDAAmD;;;;;AACnD;AAGA;AACA;AAHA;AACA;;;;;;AAIO,MAAM,oBAAoB;IAC/B,qDAAqD;IACrD,eAAe,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC;QAClF,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,MAAM,OAAO,MAAM,CAAC,QAAQ;YAChC,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAElC,wEAAwE;YACxE,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;gBAC3C,0CAA0C;gBAC1C,MAAM,oBAAoB,MAAM,OAAO,CAAC,gBAAgB,eAAe;oBAAC;iBAAa;gBAErF,+CAA+C;gBAC/C,kBAAkB,OAAO,CAAC,CAAC,IAAI;oBAC7B,yDAAyD;oBACzD,OAAO,MAAM,CAAC,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC,EAAE;gBAC1C;gBAEA,QAAQ,GAAG,CAAC,6CAA6C;YAC3D;YAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;YAEjE,8DAA8D;YAC9D,oDAAoD;YACpD,MAAM,YAAY,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,cAAc;gBAAC;aAAO;YAEnE,8EAA8E;YAC9E,IAAI,UAAU,UAAU,IAAI,UAAU,UAAU,CAAC,MAAM,GAAG,GAAG;gBAC3D,OAAO;YACT,OAAO;gBACL,kEAAkE;gBAClE,MAAM,aAAa,MAAM,OAAO,CAAC,SAAS,IAAI,IAAI,SAAS,IAAI,GAAG,EAAE;gBACpE,OAAO;oBACL;oBACA,OAAO,WAAW,MAAM;oBACxB,OAAO,KAAK,IAAI,CAAC,WAAW,MAAM,GAAG;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,cAAc,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;YAChD,MAAM;QACR;IACF;IAEA,qEAAqE;IACrE,kBAAkB,OAAO;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI;YAC7C,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,eAAe;YAC/C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,iBAAiB,OAAO,IAAI;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YACpD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,GAAG,CAAC,CAAC,EAAE;YACnD,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YACpC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAC,EAAE;YACjD,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,oBAAoB,OAAO;QACzB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,sBAAsB;YACtD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,uBAAuB,OAAO,UAAU;QACtC,IAAI;YACF,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa;YAChE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,sBAAsB,OAAO;QAC3B,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,UAAU;YAC9D,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,UAAU;YAE/D,4CAA4C;YAC5C,MAAM,OAAO,SAAS,IAAI;YAE1B,uCAAuC;YACvC,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,SAAS,EAAE,CAAC,EAAE;YAExE,oCAAoC;YACpC,IAAI,MAAM,OAAO,CAAC,OAAO;gBACvB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,MAAM,CAAC,8BAA8B,CAAC;gBACtE,OAAO;YACT,OAEK,IAAI,QAAQ,OAAO,SAAS,UAAU;gBACzC,4CAA4C;gBAC5C,MAAM,qBAAqB;oBAAC;oBAAoB;oBAAc;oBAAQ;oBAAS;iBAAU;gBAEzF,2CAA2C;gBAC3C,KAAK,MAAM,QAAQ,mBAAoB;oBACrC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;wBACjF,OAAO,IAAI,CAAC,KAAK;oBACnB;gBACF;gBAEA,2EAA2E;gBAC3E,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC,MAAO;oBACpC,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;wBACjF,OAAO,IAAI,CAAC,KAAK;oBACnB;gBACF;gBAEA,kFAAkF;gBAClF,IAAI,KAAK,SAAS,IAAI,KAAK,WAAW,EAAE;oBACtC,QAAQ,GAAG,CAAC,CAAC,iDAAiD,CAAC;oBAC/D,MAAM,YAAY;wBAChB,IAAI,KAAK,WAAW,IAAI,KAAK,SAAS,EAAE;wBACxC,MAAM,KAAK,aAAa,IAAI,KAAK,SAAS,EAAE;wBAC5C,cAAc,KAAK,YAAY;wBAC/B,YAAY,KAAK,UAAU;oBAC7B;oBAEA,OAAO;wBAAC;qBAAU;gBACpB;YACF;YAEA,+DAA+D;YAC/D,QAAQ,IAAI,CAAC,qCAAqC;YAClD,OAAO,EAAE;QACX,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC,EAAE;YACjE,+EAA+E;YAC/E,OAAO,EAAE;QACX;IACF;IAEA;;;;;GAKC,GACD,kBAAkB,OAAO,SAAS,eAAe,MAAM;QACrD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,kBAAkB,aAAa,CAAC;gBACrD,GAAG,OAAO;gBACV,OAAO,KAAK,+CAA+C;YAC7D;YAEA,iCAAiC;YACjC,MAAM,kBAAkB,SAAS,UAAU,IAAI,EAAE;YAEjD,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAQ,QAAQ;gBAAO;gBAC9B;oBACE,KAAK;oBACL,QAAQ;gBACV;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,gBAAgB,GAAG,CAAC,CAAA;gBACvC,OAAO;oBACL,MAAM,UAAU,IAAI,IAAI;oBACxB,aAAa,UAAU,OAAO,IAAI,UAAU,OAAO,CAAC,IAAI,GAAG,UAAU,OAAO,CAAC,IAAI,GAAG;oBACpF,WAAW,UAAU,SAAS,IAAI;gBACpC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,GAAG,GAAG;gBAC3D,cAAc,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,YAAY,CAAC,MAAM,CAAC,aAAa,CAAC;YACzF;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,+CAA+C;gBAC/C,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,OAAO,IAAI,EAAE,OAAO,CAAC,EAAE,KAAK,QAAQ,SAAS,GAAG,SAAS;gBACzG,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,QAAQ,SAAS,EAAE;YACnE;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,0IAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,uBAAuB,OAAO,UAAU,aAAa;QACnD,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa,EAAE;YAChF,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,YAAY,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE;YAClF,MAAM;QACR;IACF;IAEA,yDAAyD;IACzD,oBAAoB,OAAO,UAAU;QACnC,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,CAAC,EAAE,aAAa;YAC9E,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,YAAY,WAAW,EAAE,SAAS,CAAC,CAAC,EAAE;YAC3F,MAAM;QACR;IACF;AACF;uCAEe"}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/admin/services/companyService.js"], "sourcesContent": ["import { api } from \"@/utils/api\";\r\n\r\nexport const companyService = {\r\n  // Obter a empresa atual do usuário autenticado\r\n  getCurrentCompany: async () => {\r\n    try {\r\n      const response = await api.get('/companies/current');\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresa atual:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Listar empresas com suporte a paginação e filtros\r\n  getCompanies: async ({ page = 1, limit = 10, search, active } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (page) params.append('page', page);\r\n      if (limit) params.append('limit', limit);\r\n      if (search) params.append('search', search);\r\n      if (active !== undefined) params.append('active', active);\r\n\r\n      const response = await api.get(`/companies?${params.toString()}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresas:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter uma empresa específica\r\n  getCompany: async (id) => {\r\n    try {\r\n      const response = await api.get(`/companies/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter lista de empresas para formulários de seleção\r\n  getCompaniesForSelect: async () => {\r\n    try {\r\n      const response = await api.get(\"/companies/select\");\r\n      return response.data.companies || [];\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar empresas para seleção:\", error);\r\n      // Retorna array vazio em caso de erro para facilitar o manuseio no frontend\r\n      return [];\r\n    }\r\n  },\r\n\r\n  // Criar uma nova empresa\r\n  createCompany: async (formData) => {\r\n    try {\r\n      const response = await api.post('/companies', formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar empresa:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar uma empresa existente\r\n  updateCompany: async (id, formData) => {\r\n    try {\r\n      const response = await api.put(`/companies/${id}`, formData, {\r\n        headers: {\r\n          'Content-Type': 'multipart/form-data'\r\n        }\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Alternar o status de uma empresa (ativo/inativo)\r\n  toggleCompanyStatus: async (id) => {\r\n    try {\r\n      const response = await api.patch(`/companies/${id}/status`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao alterar status da empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir uma empresa\r\n  deleteCompany: async (id) => {\r\n    try {\r\n      await api.delete(`/companies/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter o logo de uma empresa\r\n  getCompanyLogo: async (id) => {\r\n    try {\r\n      const response = await api.get(`/companies/${id}/logo`, {\r\n        responseType: 'blob'\r\n      });\r\n      return URL.createObjectURL(response.data);\r\n    } catch (error) {\r\n      console.error(`Erro ao obter logo da empresa ${id}:`, error);\r\n      throw error;\r\n    }\r\n  }\r\n};\r\n\r\nexport default companyService;"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,iBAAiB;IAC5B,+CAA+C;IAC/C,mBAAmB;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,cAAc,OAAO,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC;QAChE,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,MAAM,OAAO,MAAM,CAAC,QAAQ;YAChC,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAClC,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,WAAW,OAAO,MAAM,CAAC,UAAU;YAElD,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,QAAQ,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI;YACjD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC,EAAE;YAC/C,MAAM;QACR;IACF;IAEA,sDAAsD;IACtD,uBAAuB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,4EAA4E;YAC5E,OAAO,EAAE;QACX;IACF;IAEA,yBAAyB;IACzB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,cAAc,UAAU;gBACtD,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,eAAe,OAAO,IAAI;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE,UAAU;gBAC3D,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC,EAAE;YAClD,MAAM;QACR;IACF;IAEA,mDAAmD;IACnD,qBAAqB,OAAO;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,KAAK,CAAC,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC;YAC1D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,eAAe,OAAO;QACpB,IAAI;YACF,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;YACnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;YAChD,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,EAAE;gBACtD,cAAc;YAChB;YACA,OAAO,IAAI,eAAe,CAAC,SAAS,IAAI;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;AACF;uCAEe"}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 812, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/PersonsPage/PersonsPage.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useCallback } from \"react\";\r\nimport TutorialManager from \"@/components/tutorial/TutorialManager\";\r\nimport TutorialTriggerButton from \"@/components/tutorial/TutorialTriggerButton\";\r\nimport ModuleHeader, { FilterButton } from \"@/components/ui/ModuleHeader\";\r\nimport { ModuleSelect, ModuleTable } from \"@/components/ui\";\r\nimport MultiSelect from \"@/components/ui/multi-select\";\r\nimport {\r\n  Plus,\r\n  Search,\r\n  Filter,\r\n  RefreshCw,\r\n  Edit,\r\n  Trash,\r\n  User,\r\n  Power,\r\n  CheckCircle,\r\n  XCircle,\r\n  Mail,\r\n  Phone,\r\n  CreditCard,\r\n  Calendar,\r\n  Users,\r\n  FileText,\r\n  Eye,\r\n} from \"lucide-react\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport PersonFormModal from \"@/components/people/PersonFormModal.js\";\r\nimport ExportMenu from \"@/components/ui/ExportMenu\";\r\n\r\nimport { formatDate, formatDateTime } from \"@/utils/dateUtils\";\r\nimport Link from \"next/link\";\r\nimport { companyService } from \"@/app/modules/admin/services/companyService\";\r\n\r\n// Tutorial steps para a página de pacientes\r\nconst personsTutorialSteps = [\r\n  {\r\n    title: \"Pacientes\",\r\n    content: \"Esta tela permite gerenciar o cadastro de pacientes no sistema.\",\r\n    selector: \"h1\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Adicionar Novo Paciente\",\r\n    content: \"Clique aqui para adicionar um novo paciente.\",\r\n    selector: \"button:has(span:contains('Novo Paciente'))\",\r\n    position: \"left\"\r\n  },\r\n  {\r\n    title: \"Filtrar Pacientes\",\r\n    content: \"Use esta barra de pesquisa para encontrar pacientes específicos pelo nome, email ou CPF.\",\r\n    selector: \"input[placeholder*='Buscar']\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Status\",\r\n    content: \"Filtre os pacientes por status (ativos ou inativos).\",\r\n    selector: \"select:first-of-type\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Tipo\",\r\n    content: \"Filtre os pacientes por tipo de relacionamento (titular ou dependente).\",\r\n    selector: \"select:nth-of-type(2)\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Empresa\",\r\n    content: \"Filtre os pacientes pela empresa a que pertencem.\",\r\n    selector: \"select:nth-of-type(3)\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Múltiplos Pacientes\",\r\n    content: \"Selecione um ou mais pacientes pelo nome completo para filtrar a lista.\",\r\n    selector: \"div:has(> label:contains('Filtrar por pacientes específicos'))\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Exportar Dados\",\r\n    content: \"Exporte a lista de pacientes em diferentes formatos usando este botão.\",\r\n    selector: \".export-button\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Gerenciar Pacientes\",\r\n    content: \"Visualize, edite, ative/desative ou exclua pacientes usando os botões de ação na tabela.\",\r\n    selector: \"table\",\r\n    position: \"top\"\r\n  }\r\n];\r\n\r\nconst PersonsPage = () => {\r\n  const { user: currentUser } = useAuth();\r\n  const [persons, setPersons] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [totalPersons, setTotalPersons] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [personsFilter, setPersonsFilter] = useState([]);\r\n  const [personOptions, setPersonOptions] = useState([]);\r\n  const [isLoadingPersonOptions, setIsLoadingPersonOptions] = useState(false);\r\n  const [statusFilter, setStatusFilter] = useState(\"\");\r\n  const [relationshipFilter, setRelationshipFilter] = useState(\"\");\r\n  const [companyFilter, setCompanyFilter] = useState(\"\");\r\n  const [companies, setCompanies] = useState([]);\r\n  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);\r\n  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);\r\n  const [selectedPerson, setSelectedPerson] = useState(null);\r\n  const [actionToConfirm, setActionToConfirm] = useState(null);\r\n  const [personFormOpen, setPersonFormOpen] = useState(false);\r\n  const [isExporting, setIsExporting] = useState(false);\r\n\r\n  // Constants\r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  // Função para carregar empresas (apenas para system_admin)\r\n  const loadCompanies = async () => {\r\n    if (currentUser?.role !== \"SYSTEM_ADMIN\") return;\r\n\r\n    setIsLoadingCompanies(true);\r\n    try {\r\n      const response = await companyService.getCompaniesForSelect();\r\n      setCompanies(response);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar empresas:\", error);\r\n    } finally {\r\n      setIsLoadingCompanies(false);\r\n    }\r\n  };\r\n\r\n  // Função para carregar opções de pacientes para o multi-select\r\n  const loadPersonOptions = useCallback(async () => {\r\n    setIsLoadingPersonOptions(true);\r\n    try {\r\n      // Carregar todos os pacientes para o multi-select (com limite maior)\r\n      const response = await personsService.getPersons({\r\n        limit: 100, // Limite maior para ter mais opções\r\n        active: true // Apenas pacientes ativos por padrão\r\n      });\r\n\r\n      const options = response?.persons?.map(person => ({\r\n        value: person.id,\r\n        label: person.fullName,\r\n        // Guardar o nome para ordenação\r\n        sortName: person.fullName.toLowerCase()\r\n      })) || [];\r\n\r\n      // Ordenar as opções alfabeticamente pelo nome\r\n      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName));\r\n\r\n      setPersonOptions(sortedOptions);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de pacientes:\", error);\r\n      setPersonOptions([]);\r\n    } finally {\r\n      setIsLoadingPersonOptions(false);\r\n    }\r\n  }, []);\r\n\r\n  const loadPersons = async (\r\n    page = currentPage,\r\n    searchQuery = search,\r\n    personIds = personsFilter,\r\n    status = statusFilter,\r\n    relationship = relationshipFilter,\r\n    company = companyFilter\r\n  ) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const response = await personsService.getPersons({\r\n        page,\r\n        limit: ITEMS_PER_PAGE,\r\n        search: searchQuery || undefined,\r\n        personIds: personIds.length > 0 ? personIds : undefined,\r\n        active: status === \"\" ? undefined : status === \"active\",\r\n        relationship: relationship || undefined,\r\n        companyId: company || undefined,\r\n      });\r\n\r\n      // Make sure we always have an array even if the API response is different\r\n      // Adicionando \"people\" que é o que a API realmente retorna\r\n      setPersons(response?.persons || response?.people || response?.data || []);\r\n      setTotalPersons(response?.total || 0);\r\n      setTotalPages(response?.pages || 1);\r\n      setCurrentPage(page);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar pessoas:\", error);\r\n      setPersons([]);\r\n      setTotalPersons(0);\r\n      setTotalPages(1);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadPersons();\r\n    loadPersonOptions();\r\n    // Carregar empresas se o usuário for system_admin\r\n    if (currentUser?.role === \"SYSTEM_ADMIN\") {\r\n      loadCompanies();\r\n    }\r\n  }, [loadPersonOptions]);\r\n\r\n  const handleSearch = (e) => {\r\n    e.preventDefault();\r\n    loadPersons(1, search, personsFilter, statusFilter, relationshipFilter, companyFilter);\r\n  };\r\n\r\n  const handlePersonsFilterChange = (value) => {\r\n    setPersonsFilter(value);\r\n    loadPersons(1, search, value, statusFilter, relationshipFilter, companyFilter);\r\n  };\r\n\r\n  const handleStatusFilterChange = (value) => {\r\n    setStatusFilter(value);\r\n    loadPersons(1, search, personsFilter, value, relationshipFilter, companyFilter);\r\n  };\r\n\r\n  const handleRelationshipFilterChange = (value) => {\r\n    setRelationshipFilter(value);\r\n    loadPersons(1, search, personsFilter, statusFilter, value, companyFilter);\r\n  };\r\n\r\n  const handleCompanyFilterChange = (value) => {\r\n    setCompanyFilter(value);\r\n    loadPersons(1, search, personsFilter, statusFilter, relationshipFilter, value);\r\n  };\r\n\r\n  const handlePageChange = (page) => {\r\n    loadPersons(page, search, personsFilter, statusFilter, relationshipFilter, companyFilter);\r\n  };\r\n\r\n  const handleResetFilters = () => {\r\n    setSearch(\"\");\r\n    setPersonsFilter([]);\r\n    setStatusFilter(\"\");\r\n    setRelationshipFilter(\"\");\r\n    setCompanyFilter(\"\");\r\n    loadPersons(1, \"\", [], \"\", \"\", \"\");\r\n  };\r\n\r\n  const handleEditPerson = async (person) => {\r\n    console.log('Editando pessoa:', person);\r\n    console.log('URL da imagem de perfil na listagem:', person.profileImageFullUrl);\r\n\r\n    try {\r\n      // Buscar dados completos da pessoa antes de abrir o modal\r\n      const personData = await personsService.getPerson(person.id);\r\n      console.log('Dados completos da pessoa:', personData);\r\n      console.log('URL da imagem de perfil nos dados completos:', personData.profileImageFullUrl);\r\n\r\n      setSelectedPerson(personData);\r\n      setPersonFormOpen(true);\r\n    } catch (error) {\r\n      console.error('Erro ao buscar dados da pessoa:', error);\r\n      // Fallback para os dados da listagem\r\n      setSelectedPerson(person);\r\n      setPersonFormOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleToggleStatus = (person) => {\r\n    setSelectedPerson(person);\r\n    setActionToConfirm({\r\n      type: \"toggle-status\",\r\n      message: `${person.active ? \"Desativar\" : \"Ativar\"} a pessoa ${\r\n        person.fullName\r\n      }?`,\r\n    });\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  const handleDeletePerson = (person) => {\r\n    setSelectedPerson(person);\r\n    setActionToConfirm({\r\n      type: \"delete\",\r\n      message: `Excluir permanentemente a pessoa ${person.fullName}?`,\r\n    });\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  const confirmAction = async () => {\r\n    if (actionToConfirm.type === \"toggle-status\") {\r\n      try {\r\n        await personsService.togglePersonStatus(selectedPerson.id);\r\n        loadPersons();\r\n      } catch (error) {\r\n        console.error(\"Erro ao alterar status da pessoa:\", error);\r\n      }\r\n    } else if (actionToConfirm.type === \"delete\") {\r\n      try {\r\n        await personsService.deletePerson(selectedPerson.id);\r\n        loadPersons();\r\n      } catch (error) {\r\n        console.error(\"Erro ao excluir pessoa:\", error);\r\n      }\r\n    }\r\n    setConfirmationDialogOpen(false);\r\n  };\r\n\r\n  const handleExport = async (format) => {\r\n    setIsExporting(true);\r\n    try {\r\n      // Encontrar o nome da empresa selecionada para o subtítulo da exportação\r\n      let companyName;\r\n      if (companyFilter) {\r\n        const selectedCompany = companies.find(c => c.id === companyFilter);\r\n        companyName = selectedCompany ? selectedCompany.name : undefined;\r\n      }\r\n\r\n      // Exportar usando os mesmos filtros da tabela atual\r\n      await personsService.exportPersons({\r\n        search: search || undefined,\r\n        personIds: personsFilter.length > 0 ? personsFilter : undefined,\r\n        active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\r\n        relationship: relationshipFilter || undefined,\r\n        companyId: companyFilter || undefined,\r\n        companyName\r\n      }, format);\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar pessoas:\", error);\r\n      // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  // Usando o utilitário centralizado de formatação de datas\r\n\r\n  const formatCPF = (cpf) => {\r\n    if (!cpf) return \"N/A\";\r\n\r\n    // CPF format: 000.000.000-00\r\n    const cpfNumbers = cpf.replace(/\\D/g, \"\");\r\n    return cpfNumbers.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, \"$1.$2.$3-$4\");\r\n  };\r\n\r\n  const formatPhone = (phone) => {\r\n    if (!phone) return \"N/A\";\r\n\r\n    // Phone format: (00) 00000-0000\r\n    const phoneNumbers = phone.replace(/\\D/g, \"\");\r\n    return phoneNumbers.replace(/(\\d{2})(\\d{5})(\\d{4})/, \"($1) $2-$3\");\r\n  };\r\n\r\n  const isAdmin = currentUser?.modules?.includes(\"ADMIN\");\r\n  const isSystemAdmin = currentUser?.role === \"SYSTEM_ADMIN\";\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Título e botões de exportar e adicionar */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h1 className=\"text-2xl font-bold text-slate-800 dark:text-white flex items-center\">\r\n          <User size={24} className=\"mr-2 text-orange-600 dark:text-orange-400\" />\r\n          Pacientes\r\n        </h1>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Botão de exportar */}\r\n          <ExportMenu\r\n            onExport={handleExport}\r\n            isExporting={isExporting}\r\n            disabled={isLoading || persons.length === 0}\r\n            className=\"text-orange-700 dark:text-orange-300\"\r\n          />\r\n\r\n          {/* Botão de adicionar */}\r\n          <button\r\n            onClick={() => {\r\n              setSelectedPerson(null);\r\n              setPersonFormOpen(true);\r\n            }}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all\"\r\n          >\r\n            <Plus size={18} />\r\n            <span className=\"font-medium\">Novo Paciente</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Cabeçalho e filtros da página */}\r\n      <ModuleHeader\r\n        title=\"Filtros\"\r\n        icon={<Filter size={22} className=\"text-module-people-icon dark:text-module-people-icon-dark\" />}\r\n        description=\"Gerencie o cadastro de pacientes no sistema. Utilize os filtros abaixo para encontrar pacientes específicos.\"\r\n        tutorialSteps={personsTutorialSteps}\r\n        tutorialName=\"persons-overview\"\r\n        moduleColor=\"people\"\r\n        filters={\r\n          <form\r\n            onSubmit={handleSearch}\r\n            className=\"flex flex-col gap-4\"\r\n          >\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\" />\r\n                <input\r\n                  type=\"text\"\r\n                  placeholder=\"Buscar por nome, email ou CPF...\"\r\n                  value={search}\r\n                  onChange={(e) => setSearch(e.target.value)}\r\n                  className=\"w-full pl-10 pr-4 py-2 border border-neutral-200 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-300 dark:focus:ring-primary-600 bg-white dark:bg-gray-700 text-neutral-800 dark:text-neutral-100\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex flex-col sm:flex-row gap-2\">\r\n            <div className=\"w-full sm:w-40\">\r\n              <ModuleSelect\r\n                moduleColor=\"people\"\r\n                value={statusFilter}\r\n                onChange={(e) => handleStatusFilterChange(e.target.value)}\r\n                placeholder=\"Status\"\r\n              >\r\n                <option value=\"\">Todos os status</option>\r\n                <option value=\"active\">Ativos</option>\r\n                <option value=\"inactive\">Inativos</option>\r\n              </ModuleSelect>\r\n            </div>\r\n\r\n            <div className=\"w-full sm:w-48\">\r\n              <ModuleSelect\r\n                moduleColor=\"people\"\r\n                value={relationshipFilter}\r\n                onChange={(e) => handleRelationshipFilterChange(e.target.value)}\r\n                placeholder=\"Tipo\"\r\n              >\r\n                <option value=\"\">Todos os tipos</option>\r\n                <option value=\"Titular\">Titular</option>\r\n                <option value=\"Dependente\">Dependente</option>\r\n              </ModuleSelect>\r\n            </div>\r\n\r\n            {/* Filtro de empresa (apenas para system_admin) */}\r\n            {isSystemAdmin && (\r\n              <div className=\"w-full sm:w-48\">\r\n                <ModuleSelect\r\n                  moduleColor=\"people\"\r\n                  value={companyFilter}\r\n                  onChange={(e) => handleCompanyFilterChange(e.target.value)}\r\n                  placeholder=\"Empresa\"\r\n                  disabled={isLoadingCompanies}\r\n                >\r\n                  <option value=\"\">Todas as empresas</option>\r\n                  {companies.map((company) => (\r\n                    <option key={company.id} value={company.id}>\r\n                      {company.name}\r\n                    </option>\r\n                  ))}\r\n                </ModuleSelect>\r\n              </div>\r\n            )}\r\n\r\n            <FilterButton type=\"submit\" moduleColor=\"people\" variant=\"primary\">\r\n              <Filter size={16} className=\"sm:hidden\" />\r\n              <span className=\"hidden sm:inline\">Filtrar</span>\r\n            </FilterButton>\r\n\r\n            <FilterButton\r\n              type=\"button\"\r\n              onClick={handleResetFilters}\r\n              moduleColor=\"people\"\r\n              variant=\"secondary\"\r\n            >\r\n              <RefreshCw size={16} className=\"sm:hidden\" />\r\n              <span className=\"hidden sm:inline\">Limpar</span>\r\n            </FilterButton>\r\n          </div>\r\n            </div>\r\n\r\n            {/* Multi-select para filtrar por múltiplos pacientes */}\r\n            <div className=\"w-full\">\r\n              <MultiSelect\r\n                label=\"Filtrar por Pacientes\"\r\n                value={personsFilter}\r\n                onChange={handlePersonsFilterChange}\r\n                options={personOptions}\r\n                placeholder=\"Selecione um ou mais pacientes pelo nome...\"\r\n                loading={isLoadingPersonOptions}\r\n                moduleOverride=\"people\"\r\n              />\r\n            </div>\r\n        </form>\r\n      }\r\n      />\r\n\r\n      {/* Tabela de Pacientes */}\r\n      <ModuleTable\r\n        moduleColor=\"people\"\r\n        columns={[\r\n          { header: 'Paciente', field: 'fullName', width: '20%' },\r\n          { header: 'Contato', field: 'contact', width: '20%' },\r\n          { header: 'CPF', field: 'cpf', width: '15%' },\r\n          { header: 'Relação', field: 'client', width: '10%' },\r\n          { header: 'Status', field: 'active', width: '10%' },\r\n          { header: 'Cadastro', field: 'createdAt', width: '10%' },\r\n          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }\r\n        ]}\r\n        data={persons}\r\n        isLoading={isLoading}\r\n        emptyMessage=\"Nenhuma pessoa encontrada\"\r\n        emptyIcon={<User size={24} />}\r\n        currentPage={currentPage}\r\n        totalPages={totalPages}\r\n        totalItems={totalPersons}\r\n        onPageChange={handlePageChange}\r\n        showPagination={totalPages > 1}\r\n        tableId=\"people-persons-table\"\r\n        enableColumnToggle={true}\r\n        defaultSortField=\"fullName\"\r\n        defaultSortDirection=\"asc\"\r\n        renderRow={(person, index, moduleColors, visibleColumns) => (\r\n          <tr key={person.id} className={moduleColors.hoverBg}>\r\n            {visibleColumns.includes('fullName') && (\r\n              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium overflow-hidden\">\r\n                    {person.profileImageFullUrl ? (\r\n                      <img\r\n                        src={person.profileImageFullUrl}\r\n                        alt={person.fullName}\r\n                        className=\"w-full h-full object-cover\"\r\n                        onError={(e) => {\r\n                          e.target.onerror = null;\r\n                          e.target.style.display = 'none';\r\n                          e.target.parentNode.innerHTML = person.fullName.charAt(0).toUpperCase();\r\n                        }}\r\n                      />\r\n                    ) : (\r\n                      person.fullName.charAt(0).toUpperCase()\r\n                    )}\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <p className=\"text-sm font-medium text-neutral-800 dark:text-neutral-100\">\r\n                      <Link\r\n                        href={`/dashboard/people/persons/${person.id}`}\r\n                        className=\"hover:text-primary-600 dark:hover:text-primary-400 hover:underline\"\r\n                      >\r\n                        {person.fullName}\r\n                      </Link>\r\n                    </p>\r\n                    <div className=\"flex items-center text-xs text-neutral-500 dark:text-neutral-400\">\r\n                      <Calendar className=\"h-3 w-3 mr-1\" />\r\n                      {person.birthDate\r\n                        ? formatDate(person.birthDate)\r\n                        : \"Sem data\"}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('contact') && (\r\n              <td className=\"px-6 py-4\">\r\n                <div className=\"text-sm text-neutral-600 dark:text-neutral-300\">\r\n                  {person.email && (\r\n                    <div className=\"flex items-center gap-1 mb-1\">\r\n                      <Mail className=\"h-3 w-3 text-neutral-400 dark:text-neutral-500\" />\r\n                      <span>{person.email}</span>\r\n                    </div>\r\n                  )}\r\n                  {person.phone && (\r\n                    <div className=\"flex items-center gap-1\">\r\n                      <Phone className=\"h-3 w-3 text-neutral-400 dark:text-neutral-500\" />\r\n                      <span>{formatPhone(person.phone)}</span>\r\n                    </div>\r\n                  )}\r\n                  {!person.email && !person.phone && (\r\n                    <span className=\"text-neutral-400 dark:text-neutral-500\">Sem contato</span>\r\n                  )}\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('cpf') && (\r\n              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-neutral-300\">\r\n                <div className=\"flex items-center gap-1\">\r\n                  <CreditCard className=\"h-3 w-3 text-neutral-400 dark:text-neutral-500\" />\r\n                  {person.cpf ? formatCPF(person.cpf) : \"Não informado\"}\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('client') && (\r\n              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                {person.clientPersons && person.clientPersons.length > 0 ? (\r\n                  <div className=\"flex items-center\">\r\n                    <Users className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1\" />\r\n                    <span className=\"px-2 py-1 text-xs rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400\">\r\n                      {person.relationship || \"Titular\"}\r\n                    </span>\r\n                  </div>\r\n                ) : (\r\n                  <span className=\"text-neutral-400 dark:text-neutral-500 text-sm\">\r\n                    Sem cliente\r\n                  </span>\r\n                )}\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('active') && (\r\n              <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                <span\r\n                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${\r\n                    person.active\r\n                      ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"\r\n                      : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"\r\n                  }`}\r\n                >\r\n                  {person.active ? (\r\n                    <>\r\n                      <CheckCircle size={12} />\r\n                      <span>Ativo</span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <XCircle size={12} />\r\n                      <span>Inativo</span>\r\n                    </>\r\n                  )}\r\n                </span>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('createdAt') && (\r\n              <td className=\"px-6 py-4 whitespace-nowrap text-sm text-neutral-600 dark:text-neutral-300\">\r\n                {formatDate(person.createdAt)}\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('actions') && (\r\n              <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                <div className=\"flex justify-end gap-2\">\r\n                <Link\r\n                  href={`/dashboard/people/persons/${person.id}`}\r\n                  className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                  title=\"Visualizar\"\r\n                >\r\n                  <Eye size={18} />\r\n                </Link>\r\n                <button\r\n                  onClick={() => handleEditPerson(person)}\r\n                  className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                  title=\"Editar\"\r\n                >\r\n                  <Edit size={18} />\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => handleToggleStatus(person)}\r\n                  className={`p-1 transition-colors ${\r\n                    person.active\r\n                      ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\"\r\n                      : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"\r\n                  }`}\r\n                  title={person.active ? \"Desativar\" : \"Ativar\"}\r\n                >\r\n                  <Power size={18} />\r\n                </button>\r\n\r\n                <button\r\n                  onClick={() => handleDeletePerson(person)}\r\n                  className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                  title=\"Excluir\"\r\n                >\r\n                  <Trash size={18} />\r\n                </button>\r\n              </div>\r\n            </td>\r\n            )}\r\n          </tr>\r\n        )}\r\n      />\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmationDialogOpen}\r\n        onClose={() => setConfirmationDialogOpen(false)}\r\n        onConfirm={confirmAction}\r\n        title=\"Confirmar ação\"\r\n        message={actionToConfirm?.message || \"\"}\r\n      />\r\n\r\n      {/* Person Form Modal */}\r\n      {personFormOpen && (\r\n        <PersonFormModal\r\n          isOpen={personFormOpen}\r\n          onClose={() => setPersonFormOpen(false)}\r\n          person={selectedPerson}\r\n          onSuccess={() => {\r\n            setPersonFormOpen(false);\r\n            loadPersons();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Gerenciador de tutorial */}\r\n      <TutorialManager />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PersonsPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAoBA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AA3BA;AAAA;AAAA;AAAA;AAFA;AAEA;AAFA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;;;;;;;;;AAqCA,4CAA4C;AAC5C,MAAM,uBAAuB;IAC3B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,MAAM,cAAc;;IAClB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,YAAY;IACZ,MAAM,iBAAiB;IAEvB,2DAA2D;IAC3D,MAAM,gBAAgB;QACpB,IAAI,aAAa,SAAS,gBAAgB;QAE1C,sBAAsB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,+JAAA,CAAA,iBAAc,CAAC,qBAAqB;YAC3D,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,+DAA+D;IAC/D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACpC,0BAA0B;YAC1B,IAAI;gBACF,qEAAqE;gBACrE,MAAM,WAAW,MAAM,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAC/C,OAAO;oBACP,QAAQ,KAAK,qCAAqC;gBACpD;gBAEA,MAAM,UAAU,UAAU,SAAS;kEAAI,CAAA,SAAU,CAAC;4BAChD,OAAO,OAAO,EAAE;4BAChB,OAAO,OAAO,QAAQ;4BACtB,gCAAgC;4BAChC,UAAU,OAAO,QAAQ,CAAC,WAAW;wBACvC,CAAC;oEAAM,EAAE;gBAET,8CAA8C;gBAC9C,MAAM,gBAAgB,QAAQ,IAAI;gFAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ;;gBAEhF,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,iBAAiB,EAAE;YACrB,SAAU;gBACR,0BAA0B;YAC5B;QACF;qDAAG,EAAE;IAEL,MAAM,cAAc,OAClB,OAAO,WAAW,EAClB,cAAc,MAAM,EACpB,YAAY,aAAa,EACzB,SAAS,YAAY,EACrB,eAAe,kBAAkB,EACjC,UAAU,aAAa;QAEvB,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;gBAC/C;gBACA,OAAO;gBACP,QAAQ,eAAe;gBACvB,WAAW,UAAU,MAAM,GAAG,IAAI,YAAY;gBAC9C,QAAQ,WAAW,KAAK,YAAY,WAAW;gBAC/C,cAAc,gBAAgB;gBAC9B,WAAW,WAAW;YACxB;YAEA,0EAA0E;YAC1E,2DAA2D;YAC3D,WAAW,UAAU,WAAW,UAAU,UAAU,UAAU,QAAQ,EAAE;YACxE,gBAAgB,UAAU,SAAS;YACnC,cAAc,UAAU,SAAS;YACjC,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,WAAW,EAAE;YACb,gBAAgB;YAChB,cAAc;QAChB,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;YACA;YACA,kDAAkD;YAClD,IAAI,aAAa,SAAS,gBAAgB;gBACxC;YACF;QACF;gCAAG;QAAC;KAAkB;IAEtB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,YAAY,GAAG,QAAQ,eAAe,cAAc,oBAAoB;IAC1E;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;QACjB,YAAY,GAAG,QAAQ,OAAO,cAAc,oBAAoB;IAClE;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB,YAAY,GAAG,QAAQ,eAAe,OAAO,oBAAoB;IACnE;IAEA,MAAM,iCAAiC,CAAC;QACtC,sBAAsB;QACtB,YAAY,GAAG,QAAQ,eAAe,cAAc,OAAO;IAC7D;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;QACjB,YAAY,GAAG,QAAQ,eAAe,cAAc,oBAAoB;IAC1E;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,MAAM,QAAQ,eAAe,cAAc,oBAAoB;IAC7E;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,iBAAiB,EAAE;QACnB,gBAAgB;QAChB,sBAAsB;QACtB,iBAAiB;QACjB,YAAY,GAAG,IAAI,EAAE,EAAE,IAAI,IAAI;IACjC;IAEA,MAAM,mBAAmB,OAAO;QAC9B,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,wCAAwC,OAAO,mBAAmB;QAE9E,IAAI;YACF,0DAA0D;YAC1D,MAAM,aAAa,MAAM,gKAAA,CAAA,iBAAc,CAAC,SAAS,CAAC,OAAO,EAAE;YAC3D,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,QAAQ,GAAG,CAAC,gDAAgD,WAAW,mBAAmB;YAE1F,kBAAkB;YAClB,kBAAkB;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,qCAAqC;YACrC,kBAAkB;YAClB,kBAAkB;QACpB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,mBAAmB;YACjB,MAAM;YACN,SAAS,GAAG,OAAO,MAAM,GAAG,cAAc,SAAS,UAAU,EAC3D,OAAO,QAAQ,CAChB,CAAC,CAAC;QACL;QACA,0BAA0B;IAC5B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,mBAAmB;YACjB,MAAM;YACN,SAAS,CAAC,iCAAiC,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC;QACjE;QACA,0BAA0B;IAC5B;IAEA,MAAM,gBAAgB;QACpB,IAAI,gBAAgB,IAAI,KAAK,iBAAiB;YAC5C,IAAI;gBACF,MAAM,gKAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,eAAe,EAAE;gBACzD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,qCAAqC;YACrD;QACF,OAAO,IAAI,gBAAgB,IAAI,KAAK,UAAU;YAC5C,IAAI;gBACF,MAAM,gKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,eAAe,EAAE;gBACnD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;QACA,0BAA0B;IAC5B;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,yEAAyE;YACzE,IAAI;YACJ,IAAI,eAAe;gBACjB,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACrD,cAAc,kBAAkB,gBAAgB,IAAI,GAAG;YACzD;YAEA,oDAAoD;YACpD,MAAM,gKAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;gBACjC,QAAQ,UAAU;gBAClB,WAAW,cAAc,MAAM,GAAG,IAAI,gBAAgB;gBACtD,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB;gBAC3D,cAAc,sBAAsB;gBACpC,WAAW,iBAAiB;gBAC5B;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,mFAAmF;QACrF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,0DAA0D;IAE1D,MAAM,YAAY,CAAC;QACjB,IAAI,CAAC,KAAK,OAAO;QAEjB,6BAA6B;QAC7B,MAAM,aAAa,IAAI,OAAO,CAAC,OAAO;QACtC,OAAO,WAAW,OAAO,CAAC,gCAAgC;IAC5D;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,OAAO,OAAO;QAEnB,gCAAgC;QAChC,MAAM,eAAe,MAAM,OAAO,CAAC,OAAO;QAC1C,OAAO,aAAa,OAAO,CAAC,yBAAyB;IACvD;IAEA,MAAM,UAAU,aAAa,SAAS,SAAS;IAC/C,MAAM,gBAAgB,aAAa,SAAS;IAE5C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAA8C;;;;;;;kCAI1E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,wIAAA,CAAA,UAAU;gCACT,UAAU;gCACV,aAAa;gCACb,UAAU,aAAa,QAAQ,MAAM,KAAK;gCAC1C,WAAU;;;;;;0CAIZ,6LAAC;gCACC,SAAS;oCACP,kBAAkB;oCAClB,kBAAkB;gCACpB;gCACA,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,0IAAA,CAAA,UAAY;gBACX,OAAM;gBACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;oBAAC,MAAM;oBAAI,WAAU;;;;;;gBAClC,aAAY;gBACZ,eAAe;gBACf,cAAa;gBACb,aAAY;gBACZ,uBACE,6LAAC;oBACC,UAAU;oBACV,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;gDACxD,aAAY;;kEAEZ,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;sDAI7B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,+BAA+B,EAAE,MAAM,CAAC,KAAK;gDAC9D,aAAY;;kEAEZ,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAU;;;;;;kEACxB,6LAAC;wDAAO,OAAM;kEAAa;;;;;;;;;;;;;;;;;wCAK9B,+BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO;gDACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;gDACzD,aAAY;gDACZ,UAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,UAAU,GAAG,CAAC,CAAC,wBACd,6LAAC;4DAAwB,OAAO,QAAQ,EAAE;sEACvC,QAAQ,IAAI;2DADF,QAAQ,EAAE;;;;;;;;;;;;;;;;sDAQ/B,6LAAC,0IAAA,CAAA,eAAY;4CAAC,MAAK;4CAAS,aAAY;4CAAS,SAAQ;;8DACvD,6LAAC,yMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC5B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAGrC,6LAAC,0IAAA,CAAA,eAAY;4CACX,MAAK;4CACL,SAAS;4CACT,aAAY;4CACZ,SAAQ;;8DAER,6LAAC,mNAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC/B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;;;;;;;sCAMrC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6IAAA,CAAA,UAAW;gCACV,OAAM;gCACN,OAAO;gCACP,UAAU;gCACV,SAAS;gCACT,aAAY;gCACZ,SAAS;gCACT,gBAAe;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6LAAC,mLAAA,CAAA,cAAW;gBACV,aAAY;gBACZ,SAAS;oBACP;wBAAE,QAAQ;wBAAY,OAAO;wBAAY,OAAO;oBAAM;oBACtD;wBAAE,QAAQ;wBAAW,OAAO;wBAAW,OAAO;oBAAM;oBACpD;wBAAE,QAAQ;wBAAO,OAAO;wBAAO,OAAO;oBAAM;oBAC5C;wBAAE,QAAQ;wBAAW,OAAO;wBAAU,OAAO;oBAAM;oBACnD;wBAAE,QAAQ;wBAAU,OAAO;wBAAU,OAAO;oBAAM;oBAClD;wBAAE,QAAQ;wBAAY,OAAO;wBAAa,OAAO;oBAAM;oBACvD;wBAAE,QAAQ;wBAAS,OAAO;wBAAW,WAAW;wBAAc,OAAO;wBAAO,UAAU;oBAAM;iBAC7F;gBACD,MAAM;gBACN,WAAW;gBACX,cAAa;gBACb,yBAAW,6LAAC,qMAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;gBACvB,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,gBAAgB,aAAa;gBAC7B,SAAQ;gBACR,oBAAoB;gBACpB,kBAAiB;gBACjB,sBAAqB;gBACrB,WAAW,CAAC,QAAQ,OAAO,cAAc,+BACvC,6LAAC;wBAAmB,WAAW,aAAa,OAAO;;4BAChD,eAAe,QAAQ,CAAC,6BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACZ,OAAO,mBAAmB,iBACzB,6LAAC;gDACC,KAAK,OAAO,mBAAmB;gDAC/B,KAAK,OAAO,QAAQ;gDACpB,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,MAAM,CAAC,OAAO,GAAG;oDACnB,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG;oDACzB,EAAE,MAAM,CAAC,UAAU,CAAC,SAAS,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;gDACvE;;;;;yDAGF,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;sDAGzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACX,cAAA,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,0BAA0B,EAAE,OAAO,EAAE,EAAE;wDAC9C,WAAU;kEAET,OAAO,QAAQ;;;;;;;;;;;8DAGpB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,6MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDACnB,OAAO,SAAS,GACb,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS,IAC3B;;;;;;;;;;;;;;;;;;;;;;;;4BAOb,eAAe,QAAQ,CAAC,4BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,KAAK,kBACX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAM,OAAO,KAAK;;;;;;;;;;;;wCAGtB,OAAO,KAAK,kBACX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAM,YAAY,OAAO,KAAK;;;;;;;;;;;;wCAGlC,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,KAAK,kBAC7B,6LAAC;4CAAK,WAAU;sDAAyC;;;;;;;;;;;;;;;;;4BAMhE,eAAe,QAAQ,CAAC,wBACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCACrB,OAAO,GAAG,GAAG,UAAU,OAAO,GAAG,IAAI;;;;;;;;;;;;4BAK3C,eAAe,QAAQ,CAAC,2BACvB,6LAAC;gCAAG,WAAU;0CACX,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,MAAM,GAAG,kBACrD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDACb,OAAO,YAAY,IAAI;;;;;;;;;;;2DAI5B,6LAAC;oCAAK,WAAU;8CAAiD;;;;;;;;;;;4BAOtE,eAAe,QAAQ,CAAC,2BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCACC,WAAW,CAAC,6DAA6D,EACvE,OAAO,MAAM,GACT,yEACA,gEACJ;8CAED,OAAO,MAAM,iBACZ;;0DACE,6LAAC,8NAAA,CAAA,cAAW;gDAAC,MAAM;;;;;;0DACnB,6LAAC;0DAAK;;;;;;;qEAGR;;0DACE,6LAAC,+MAAA,CAAA,UAAO;gDAAC,MAAM;;;;;;0DACf,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAOf,eAAe,QAAQ,CAAC,8BACvB,6LAAC;gCAAG,WAAU;0CACX,CAAA,GAAA,4HAAA,CAAA,aAAU,AAAD,EAAE,OAAO,SAAS;;;;;;4BAI/B,eAAe,QAAQ,CAAC,4BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACf,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,0BAA0B,EAAE,OAAO,EAAE,EAAE;4CAC9C,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;sDAEb,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAGd,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAW,CAAC,sBAAsB,EAChC,OAAO,MAAM,GACT,0FACA,yFACJ;4CACF,OAAO,OAAO,MAAM,GAAG,cAAc;sDAErC,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;sDAGf,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;uBAzJZ,OAAO,EAAE;;;;;;;;;;0BAmKtB,6LAAC,gJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,WAAW;gBACX,OAAM;gBACN,SAAS,iBAAiB,WAAW;;;;;;YAItC,gCACC,6LAAC,iJAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;gBACR,WAAW;oBACT,kBAAkB;oBAClB;gBACF;;;;;;0BAKJ,6LAAC,mJAAA,CAAA,UAAe;;;;;;;;;;;AAGtB;GAlmBM;;QAC0B,iIAAA,CAAA,UAAO;;;KADjC;uCAomBS"}}, {"offset": {"line": 1970, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1976, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/ClientsPage/ClientsPage.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useCallback } from \"react\";\r\nimport TutorialManager from \"@/components/tutorial/TutorialManager\";\r\nimport ModuleHeader, { FilterButton } from \"@/components/ui/ModuleHeader\";\r\nimport { ModuleInput, ModuleSelect, ModuleTable } from \"@/components/ui\";\r\nimport MultiSelect from \"@/components/ui/multi-select\";\r\nimport ExportMenu from \"@/components/ui/ExportMenu\";\r\nimport {\r\n  Search,\r\n  Filter,\r\n  RefreshCw,\r\n  Edit,\r\n  Trash,\r\n  Power,\r\n  CheckCircle,\r\n  XCircle,\r\n  Mail,\r\n  Users,\r\n  Eye,\r\n  Plus\r\n} from \"lucide-react\";\r\nimport { clientsService } from \"@/app/modules/people/services/clientsService\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport ClientFormModal from \"@/components/people/ClientFormModal\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { useRouter } from \"next/navigation\";\r\n\r\nimport { companyService } from \"@/app/modules/admin/services/companyService\";\r\n\r\n// Tutorial steps para a página de clientes\r\nconst clientsTutorialSteps = [\r\n  {\r\n    title: \"Clientes\",\r\n    content: \"Esta tela permite gerenciar o cadastro de clientes no sistema.\",\r\n    selector: \"h1\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Adicionar Novo Cliente\",\r\n    content: \"Clique aqui para adicionar um novo cliente.\",\r\n    selector: \"button:has(span:contains('Novo Cliente'))\",\r\n    position: \"left\"\r\n  },\r\n  {\r\n    title: \"Filtrar Clientes\",\r\n    content: \"Use esta barra de pesquisa para encontrar clientes específicos pelo login ou email.\",\r\n    selector: \"input[placeholder*='Buscar']\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Status\",\r\n    content: \"Filtre os clientes por status (ativos ou inativos).\",\r\n    selector: \"select:first-of-type\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Empresa\",\r\n    content: \"Filtre os clientes pela empresa a que pertencem.\",\r\n    selector: \"select:nth-of-type(2)\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Múltiplos Clientes\",\r\n    content: \"Selecione um ou mais clientes pelo nome completo para filtrar a lista.\",\r\n    selector: \"div:has(> label:contains('Filtrar por clientes específicos'))\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Exportar Dados\",\r\n    content: \"Exporte a lista de clientes em diferentes formatos usando este botão.\",\r\n    selector: \".export-button\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Gerenciar Clientes\",\r\n    content: \"Visualize, edite, ative/desative ou exclua clientes usando os botões de ação na tabela.\",\r\n    selector: \"table\",\r\n    position: \"top\"\r\n  }\r\n];\r\n\r\nconst ClientsPage = () => {\r\n  const { user: currentUser } = useAuth();\r\n  const router = useRouter();\r\n  const [clients, setClients] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [totalClients, setTotalClients] = useState(0);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [clientsFilter, setClientsFilter] = useState([]);\r\n  const [clientOptions, setClientOptions] = useState([]);\r\n  const [isLoadingClientOptions, setIsLoadingClientOptions] = useState(false);\r\n  const [statusFilter, setStatusFilter] = useState(\"\");\r\n  const [companyFilter, setCompanyFilter] = useState(\"\");\r\n  const [companies, setCompanies] = useState([]);\r\n  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);\r\n  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);\r\n  const [selectedClient, setSelectedClient] = useState(null);\r\n  const [actionToConfirm, setActionToConfirm] = useState(null);\r\n  const [clientFormOpen, setClientFormOpen] = useState(false);\r\n  const [isExporting, setIsExporting] = useState(false);\r\n\r\n  // Constants\r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  // Função para carregar empresas (apenas para system_admin)\r\n  const loadCompanies = async () => {\r\n    if (currentUser?.role !== \"SYSTEM_ADMIN\") return;\r\n\r\n    setIsLoadingCompanies(true);\r\n    try {\r\n      const response = await companyService.getCompaniesForSelect();\r\n      setCompanies(response);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar empresas:\", error);\r\n    } finally {\r\n      setIsLoadingCompanies(false);\r\n    }\r\n  };\r\n\r\n  // Função para carregar opções de clientes para o multi-select\r\n  const loadClientOptions = useCallback(async () => {\r\n    setIsLoadingClientOptions(true);\r\n    try {\r\n      // Carregar todos os clientes para o multi-select (com limite maior)\r\n      const response = await clientsService.getClients({\r\n        limit: 100, // Limite maior para ter mais opções\r\n        active: true, // Apenas clientes ativos por padrão\r\n        include_persons: true // Incluir informações das pessoas associadas\r\n      });\r\n\r\n      const options = response?.clients?.map(client => {\r\n        // Buscar a pessoa titular associada ao cliente\r\n        const titularPerson = client.clientPersons?.find(cp =>\r\n          cp.relationship === 'Titular' || cp.relationship === 'titular'\r\n        )?.person;\r\n\r\n        // Usar o nome completo da pessoa titular, ou o login do cliente se não encontrar\r\n        const label = titularPerson?.fullName || client.login;\r\n\r\n        return {\r\n          value: client.id,\r\n          label: label,\r\n          // Guardar o nome para ordenação\r\n          sortName: label.toLowerCase()\r\n        };\r\n      }) || [];\r\n\r\n      // Ordenar as opções alfabeticamente pelo nome\r\n      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName));\r\n\r\n      setClientOptions(sortedOptions);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de clientes:\", error);\r\n      setClientOptions([]);\r\n    } finally {\r\n      setIsLoadingClientOptions(false);\r\n    }\r\n  }, []);\r\n\r\n  const loadClients = async (\r\n    page = currentPage,\r\n    searchQuery = search,\r\n    clientIds = clientsFilter,\r\n    status = statusFilter,\r\n    company = companyFilter,\r\n    sortField = 'fullName', // Ordenar pelo nome completo do titular por padrão\r\n    sortDirection = 'asc'\r\n  ) => {\r\n    setIsLoading(true);\r\n    try {\r\n      // Ensure page is a number\r\n      const pageNumber = parseInt(page, 10);\r\n\r\n      // Importante: Atualizamos o estado currentPage ANTES de fazer a requisição\r\n      // para garantir que o ModuleTable use o valor correto\r\n      setCurrentPage(pageNumber);\r\n\r\n      const response = await clientsService.getClients({\r\n        page: pageNumber,\r\n        limit: ITEMS_PER_PAGE,\r\n        search: searchQuery || undefined,\r\n        clientIds: clientIds.length > 0 ? clientIds : undefined,\r\n        active: status === \"\" ? undefined : status === \"active\",\r\n        companyId: company || undefined,\r\n        sortField,\r\n        sortDirection,\r\n      });\r\n\r\n      // Make sure we always have an array even if the API response is different\r\n      const clientsData = response?.clients || response?.data || [];\r\n\r\n      if (!Array.isArray(clientsData)) {\r\n        // Garantir que temos um array mesmo se a resposta for inválida\r\n        const safeClientsData = [];\r\n        setClients(safeClientsData);\r\n      } else {\r\n        // Update state with the new data\r\n        setClients(clientsData);\r\n      }\r\n\r\n      setTotalClients(response?.total || 0);\r\n      setTotalPages(response?.pages || 1);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar clientes:\", error);\r\n      setClients([]);\r\n      setTotalClients(0);\r\n      setTotalPages(1);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    // Carregar clientes com ordenação padrão pelo nome completo\r\n    loadClients(1, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');\r\n    loadClientOptions();\r\n    // Carregar empresas se o usuário for system_admin\r\n    if (currentUser?.role === \"SYSTEM_ADMIN\") {\r\n      loadCompanies();\r\n    }\r\n  }, [loadClientOptions]);\r\n\r\n  const handleSearch = (e) => {\r\n    e.preventDefault();\r\n    loadClients(1, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');\r\n  };\r\n\r\n  const handleClientsFilterChange = (value) => {\r\n    setClientsFilter(value);\r\n    loadClients(1, search, value, statusFilter, companyFilter, 'fullName', 'asc');\r\n  };\r\n\r\n  const handleStatusFilterChange = (value) => {\r\n    setStatusFilter(value);\r\n    loadClients(1, search, clientsFilter, value, companyFilter, 'fullName', 'asc');\r\n  };\r\n\r\n  const handleCompanyFilterChange = (value) => {\r\n    setCompanyFilter(value);\r\n    loadClients(1, search, clientsFilter, statusFilter, value, 'fullName', 'asc');\r\n  };\r\n\r\n  const handlePageChange = (page) => {\r\n    // Importante: Não atualizamos o estado currentPage aqui, isso será feito dentro de loadClients\r\n    // para garantir que o ModuleTable use o valor correto\r\n    loadClients(page, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');\r\n  };\r\n\r\n  const handleResetFilters = () => {\r\n    setSearch(\"\");\r\n    setClientsFilter([]);\r\n    setStatusFilter(\"\");\r\n    setCompanyFilter(\"\");\r\n    loadClients(1, \"\", [], \"\", \"\", 'fullName', 'asc');\r\n  };\r\n\r\n  const handleEditClient = (client) => {\r\n    setSelectedClient(client);\r\n    setClientFormOpen(true);\r\n  };\r\n\r\n  const handleToggleStatus = (client) => {\r\n    setSelectedClient(client);\r\n    // Obter o nome completo do titular ou usar o login como fallback\r\n    const clientName = client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName\r\n      ? client.clientPersons[0].person.fullName\r\n      : client.login;\r\n\r\n    setActionToConfirm({\r\n      type: \"toggle-status\",\r\n      message: `${client.active ? \"Desativar\" : \"Ativar\"} o cliente ${clientName}?`,\r\n    });\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  const handleDeleteClient = (client) => {\r\n    setSelectedClient(client);\r\n    // Obter o nome completo do titular ou usar o login como fallback\r\n    const clientName = client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName\r\n      ? client.clientPersons[0].person.fullName\r\n      : client.login;\r\n\r\n    setActionToConfirm({\r\n      type: \"delete\",\r\n      message: `Excluir permanentemente o cliente ${clientName}?`,\r\n    });\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  const handleExport = async (format) => {\r\n    setIsExporting(true);\r\n    try {\r\n      // Encontrar o nome da empresa selecionada para o subtítulo da exportação\r\n      let companyName;\r\n      if (companyFilter) {\r\n        const selectedCompany = companies.find(c => c.id === companyFilter);\r\n        companyName = selectedCompany ? selectedCompany.name : undefined;\r\n      }\r\n\r\n      // Exportar usando os mesmos filtros da tabela atual\r\n      await clientsService.exportClients({\r\n        search: search || undefined,\r\n        clientIds: clientsFilter.length > 0 ? clientsFilter : undefined,\r\n        active: statusFilter === \"\" ? undefined : statusFilter === \"active\",\r\n        companyId: companyFilter || undefined,\r\n        companyName\r\n      }, format);\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar clientes:\", error);\r\n      // Aqui você pode adicionar uma notificação de erro se tiver um componente de toast\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  const confirmAction = async () => {\r\n    if (actionToConfirm.type === \"toggle-status\") {\r\n      try {\r\n        await clientsService.toggleClientStatus(selectedClient.id);\r\n        loadClients(currentPage, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');\r\n      } catch (error) {\r\n        console.error(\"Erro ao alterar status do cliente:\", error);\r\n      }\r\n    } else if (actionToConfirm.type === \"delete\") {\r\n      try {\r\n        await clientsService.deleteClient(selectedClient.id);\r\n        loadClients(currentPage, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');\r\n      } catch (error) {\r\n        console.error(\"Erro ao excluir cliente:\", error);\r\n      }\r\n    }\r\n    setConfirmationDialogOpen(false);\r\n  };\r\n\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n\r\n    try {\r\n      return format(new Date(dateString), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return \"Data inválida\";\r\n    }\r\n  };\r\n\r\n  // Verificar se o usuário é system_admin para mostrar o filtro de empresa\r\n  const isSystemAdmin = currentUser?.role === \"SYSTEM_ADMIN\";\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Título e botões de exportar e adicionar */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h1 className=\"text-2xl font-bold text-slate-800 dark:text-white flex items-center\">\r\n          <Users size={24} className=\"mr-2 text-orange-600 dark:text-orange-400\" />\r\n          Clientes\r\n        </h1>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Botão de exportar */}\r\n          <ExportMenu\r\n            onExport={handleExport}\r\n            isExporting={isExporting}\r\n            disabled={isLoading || clients.length === 0}\r\n            className=\"text-orange-700 dark:text-orange-300\"\r\n          />\r\n\r\n          {/* Botão de adicionar */}\r\n          <button\r\n            onClick={() => {\r\n              setSelectedClient(null);\r\n              setClientFormOpen(true);\r\n            }}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all\"\r\n          >\r\n            <Plus size={18} />\r\n            <span className=\"font-medium\">Novo Cliente</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Cabeçalho e filtros da página */}\r\n      <ModuleHeader\r\n        title=\"Filtros\"\r\n        icon={<Filter size={22} className=\"text-module-people-icon dark:text-module-people-icon-dark\" />}\r\n        description=\"Gerencie o cadastro de clientes no sistema. Utilize os filtros abaixo para encontrar clientes específicos.\"\r\n        tutorialSteps={clientsTutorialSteps}\r\n        tutorialName=\"clients-overview\"\r\n        moduleColor=\"people\"\r\n        filters={\r\n          <form\r\n            onSubmit={handleSearch}\r\n            className=\"flex flex-col gap-4\"\r\n          >\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5 z-10\" />\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  placeholder=\"Buscar por login ou email...\"\r\n                  value={search}\r\n                  onChange={(e) => setSearch(e.target.value)}\r\n                  className=\"w-full pl-10\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex flex-col sm:flex-row gap-2\">\r\n                <ModuleSelect\r\n                  moduleColor=\"people\"\r\n                  value={statusFilter}\r\n                  onChange={(e) => handleStatusFilterChange(e.target.value)}\r\n                >\r\n                  <option value=\"\">Todos os status</option>\r\n                  <option value=\"active\">Ativos</option>\r\n                  <option value=\"inactive\">Inativos</option>\r\n                </ModuleSelect>\r\n\r\n                {/* Filtro de empresa (apenas para system_admin) */}\r\n                {isSystemAdmin && (\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    value={companyFilter}\r\n                    onChange={(e) => handleCompanyFilterChange(e.target.value)}\r\n                    placeholder=\"Empresa\"\r\n                    disabled={isLoadingCompanies}\r\n                  >\r\n                    <option value=\"\">Todas as empresas</option>\r\n                    {companies.map((company) => (\r\n                      <option key={company.id} value={company.id}>\r\n                        {company.name}\r\n                      </option>\r\n                    ))}\r\n                  </ModuleSelect>\r\n                )}\r\n\r\n                <FilterButton type=\"submit\" moduleColor=\"people\" variant=\"primary\">\r\n                  <Filter size={16} className=\"sm:hidden\" />\r\n                  <span className=\"hidden sm:inline\">Filtrar</span>\r\n                </FilterButton>\r\n\r\n                <FilterButton\r\n                  type=\"button\"\r\n                  onClick={handleResetFilters}\r\n                  moduleColor=\"people\"\r\n                  variant=\"secondary\"\r\n                >\r\n                  <RefreshCw size={16} className=\"sm:hidden\" />\r\n                  <span className=\"hidden sm:inline\">Limpar</span>\r\n                </FilterButton>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Multi-select para filtrar por múltiplos clientes */}\r\n            <div className=\"w-full\">\r\n              <MultiSelect\r\n                label=\"Filtrar por Clientes\"\r\n                value={clientsFilter}\r\n                onChange={handleClientsFilterChange}\r\n                options={clientOptions}\r\n                placeholder=\"Selecione um ou mais clientes pelo nome...\"\r\n                loading={isLoadingClientOptions}\r\n                moduleOverride=\"people\"\r\n              />\r\n            </div>\r\n          </form>\r\n        }\r\n      />\r\n\r\n      {/* Tabela de Clientes */}\r\n      <ModuleTable\r\n        moduleColor=\"people\"\r\n        columns={[\r\n          { header: 'Cliente', field: 'login', width: '20%' },\r\n          { header: 'Email', field: 'email', width: '20%' },\r\n          { header: 'Pessoas', field: 'persons', width: '15%' },\r\n          { header: 'Status', field: 'active', width: '15%' },\r\n          { header: 'Cadastro', field: 'createdAt', width: '15%' },\r\n          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }\r\n        ]}\r\n        data={clients}\r\n        isLoading={isLoading}\r\n        emptyMessage=\"Nenhum cliente encontrado\"\r\n        emptyIcon={<Users size={24} />}\r\n        currentPage={currentPage}\r\n        totalPages={totalPages}\r\n        totalItems={totalClients}\r\n        onPageChange={handlePageChange}\r\n        showPagination={totalPages > 1}\r\n        tableId=\"people-clients-table\"\r\n        enableColumnToggle={true}\r\n        defaultSortField=\"login\"\r\n        defaultSortDirection=\"asc\"\r\n        onSort={(field, direction) => {\r\n          // Quando a ordenação mudar, recarregar os clientes com os novos parâmetros de ordenação\r\n          loadClients(\r\n            currentPage,\r\n            search,\r\n            clientsFilter,\r\n            statusFilter,\r\n            companyFilter,\r\n            field,\r\n            direction\r\n          );\r\n        }}\r\n        renderRow={(client, _, moduleColors, visibleColumns) => (\r\n          <tr key={client.id} className={moduleColors.hoverBg}>\r\n            {visibleColumns.includes('login') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"h-10 w-10 rounded-full bg-neutral-200 dark:bg-gray-600 flex items-center justify-center text-neutral-600 dark:text-neutral-300 font-medium flex-shrink-0 overflow-hidden\">\r\n                    {/* Obter a primeira letra do nome do titular ou do login */}\r\n                    {(client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName\r\n                      ? client.clientPersons[0].person.fullName.charAt(0)\r\n                      : client.login.charAt(0)).toUpperCase()}\r\n                  </div>\r\n                  <div className=\"ml-3 min-w-0\">\r\n                    <p className=\"text-sm font-medium text-neutral-800 dark:text-neutral-100 truncate\">\r\n                      {/* Mostrar o nome completo do titular ou o login como fallback */}\r\n                      {client.clientPersons && client.clientPersons[0] && client.clientPersons[0].person?.fullName\r\n                        ? client.clientPersons[0].person.fullName\r\n                        : client.login}\r\n                    </p>\r\n                    <p className=\"text-xs text-neutral-500 dark:text-neutral-400 truncate\">\r\n                      {client.login}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('email') && (\r\n              <td className=\"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\">\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Mail className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0\" />\r\n                  <span className=\"truncate\">{client.email}</span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('persons') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center gap-1\">\r\n                  <Users className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 flex-shrink-0\" />\r\n                  <span className=\"text-neutral-600 dark:text-neutral-300 text-sm\">\r\n                    {client.clientPersons?.length || 0} {(client.clientPersons?.length || 0) === 1 ? \"pessoa\" : \"pessoas\"}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('active') && (\r\n              <td className=\"px-4 py-4\">\r\n                <span\r\n                  className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 w-fit ${client.active\r\n                      ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400\"\r\n                      : \"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-400\"\r\n                    }`}\r\n                >\r\n                  {client.active ? (\r\n                    <>\r\n                      <CheckCircle size={12} className=\"flex-shrink-0\" />\r\n                      <span>Ativo</span>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <XCircle size={12} className=\"flex-shrink-0\" />\r\n                      <span>Inativo</span>\r\n                    </>\r\n                  )}\r\n                </span>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('createdAt') && (\r\n              <td className=\"px-4 py-4 text-sm text-neutral-600 dark:text-neutral-300\">\r\n                {formatDate(client.createdAt)}\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('actions') && (\r\n              <td className=\"px-4 py-4 text-right text-sm font-medium\">\r\n                <div className=\"flex justify-end gap-1\">\r\n                  <button\r\n                    onClick={() => router.push(`/dashboard/people/clients/${client.id}`)}\r\n                    className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-blue-500 dark:hover:text-blue-400 transition-colors\"\r\n                    title=\"Ver detalhes\"\r\n                  >\r\n                    <Eye size={16} />\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => handleEditClient(client)}\r\n                    className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-primary-500 dark:hover:text-primary-400 transition-colors\"\r\n                    title=\"Editar\"\r\n                  >\r\n                    <Edit size={16} />\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => handleToggleStatus(client)}\r\n                    className={`p-1 transition-colors ${client.active\r\n                        ? \"text-neutral-500 dark:text-neutral-400 hover:text-amber-500 dark:hover:text-amber-400\"\r\n                        : \"text-neutral-500 dark:text-neutral-400 hover:text-green-500 dark:hover:text-green-400\"\r\n                      }`}\r\n                    title={client.active ? \"Desativar\" : \"Ativar\"}\r\n                  >\r\n                    <Power size={16} />\r\n                  </button>\r\n\r\n                  <button\r\n                    onClick={() => handleDeleteClient(client)}\r\n                    className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                    title=\"Excluir\"\r\n                  >\r\n                    <Trash size={16} />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            )}\r\n          </tr>\r\n        )}\r\n      />\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmationDialogOpen}\r\n        onClose={() => setConfirmationDialogOpen(false)}\r\n        onConfirm={confirmAction}\r\n        title=\"Confirmar ação\"\r\n        message={actionToConfirm?.message || \"\"}\r\n      />\r\n\r\n      {/* Client Form Modal */}\r\n      {clientFormOpen && (\r\n        <ClientFormModal\r\n          isOpen={clientFormOpen}\r\n          onClose={() => setClientFormOpen(false)}\r\n          client={selectedClient}\r\n          onSuccess={() => {\r\n            setClientFormOpen(false);\r\n            loadClients(1, search, clientsFilter, statusFilter, companyFilter, 'fullName', 'asc');\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Gerenciador de tutorial */}\r\n      <TutorialManager />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ClientsPage;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAeA;AACA;AACA;AACA;AAGA;AAEA;AAJA;AACA;AAnBA;AAAA;AAAA;AAAA;AAHA;AAAA;AAGA;AAHA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;;;;;;;;;AAgCA,2CAA2C;AAC3C,MAAM,uBAAuB;IAC3B;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,MAAM,cAAc;;IAClB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACpC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,YAAY;IACZ,MAAM,iBAAiB;IAEvB,2DAA2D;IAC3D,MAAM,gBAAgB;QACpB,IAAI,aAAa,SAAS,gBAAgB;QAE1C,sBAAsB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,+JAAA,CAAA,iBAAc,CAAC,qBAAqB;YAC3D,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,8DAA8D;IAC9D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YACpC,0BAA0B;YAC1B,IAAI;gBACF,oEAAoE;gBACpE,MAAM,WAAW,MAAM,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAC/C,OAAO;oBACP,QAAQ;oBACR,iBAAiB,KAAK,6CAA6C;gBACrE;gBAEA,MAAM,UAAU,UAAU,SAAS;kEAAI,CAAA;wBACrC,+CAA+C;wBAC/C,MAAM,gBAAgB,OAAO,aAAa,EAAE;0EAAK,CAAA,KAC/C,GAAG,YAAY,KAAK,aAAa,GAAG,YAAY,KAAK;0EACpD;wBAEH,iFAAiF;wBACjF,MAAM,QAAQ,eAAe,YAAY,OAAO,KAAK;wBAErD,OAAO;4BACL,OAAO,OAAO,EAAE;4BAChB,OAAO;4BACP,gCAAgC;4BAChC,UAAU,MAAM,WAAW;wBAC7B;oBACF;oEAAM,EAAE;gBAER,8CAA8C;gBAC9C,MAAM,gBAAgB,QAAQ,IAAI;gFAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ;;gBAEhF,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,iBAAiB,EAAE;YACrB,SAAU;gBACR,0BAA0B;YAC5B;QACF;qDAAG,EAAE;IAEL,MAAM,cAAc,OAClB,OAAO,WAAW,EAClB,cAAc,MAAM,EACpB,YAAY,aAAa,EACzB,SAAS,YAAY,EACrB,UAAU,aAAa,EACvB,YAAY,UAAU,EACtB,gBAAgB,KAAK;QAErB,aAAa;QACb,IAAI;YACF,0BAA0B;YAC1B,MAAM,aAAa,SAAS,MAAM;YAElC,2EAA2E;YAC3E,sDAAsD;YACtD,eAAe;YAEf,MAAM,WAAW,MAAM,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;gBAC/C,MAAM;gBACN,OAAO;gBACP,QAAQ,eAAe;gBACvB,WAAW,UAAU,MAAM,GAAG,IAAI,YAAY;gBAC9C,QAAQ,WAAW,KAAK,YAAY,WAAW;gBAC/C,WAAW,WAAW;gBACtB;gBACA;YACF;YAEA,0EAA0E;YAC1E,MAAM,cAAc,UAAU,WAAW,UAAU,QAAQ,EAAE;YAE7D,IAAI,CAAC,MAAM,OAAO,CAAC,cAAc;gBAC/B,+DAA+D;gBAC/D,MAAM,kBAAkB,EAAE;gBAC1B,WAAW;YACb,OAAO;gBACL,iCAAiC;gBACjC,WAAW;YACb;YAEA,gBAAgB,UAAU,SAAS;YACnC,cAAc,UAAU,SAAS;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,WAAW,EAAE;YACb,gBAAgB;YAChB,cAAc;QAChB,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,4DAA4D;YAC5D,YAAY,GAAG,QAAQ,eAAe,cAAc,eAAe,YAAY;YAC/E;YACA,kDAAkD;YAClD,IAAI,aAAa,SAAS,gBAAgB;gBACxC;YACF;QACF;gCAAG;QAAC;KAAkB;IAEtB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,YAAY,GAAG,QAAQ,eAAe,cAAc,eAAe,YAAY;IACjF;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;QACjB,YAAY,GAAG,QAAQ,OAAO,cAAc,eAAe,YAAY;IACzE;IAEA,MAAM,2BAA2B,CAAC;QAChC,gBAAgB;QAChB,YAAY,GAAG,QAAQ,eAAe,OAAO,eAAe,YAAY;IAC1E;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;QACjB,YAAY,GAAG,QAAQ,eAAe,cAAc,OAAO,YAAY;IACzE;IAEA,MAAM,mBAAmB,CAAC;QACxB,+FAA+F;QAC/F,sDAAsD;QACtD,YAAY,MAAM,QAAQ,eAAe,cAAc,eAAe,YAAY;IACpF;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,iBAAiB,EAAE;QACnB,gBAAgB;QAChB,iBAAiB;QACjB,YAAY,GAAG,IAAI,EAAE,EAAE,IAAI,IAAI,YAAY;IAC7C;IAEA,MAAM,mBAAmB,CAAC;QACxB,kBAAkB;QAClB,kBAAkB;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,iEAAiE;QACjE,MAAM,aAAa,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,EAAE,IAAI,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,WAClG,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,GACvC,OAAO,KAAK;QAEhB,mBAAmB;YACjB,MAAM;YACN,SAAS,GAAG,OAAO,MAAM,GAAG,cAAc,SAAS,WAAW,EAAE,WAAW,CAAC,CAAC;QAC/E;QACA,0BAA0B;IAC5B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,iEAAiE;QACjE,MAAM,aAAa,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,EAAE,IAAI,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,WAClG,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,GACvC,OAAO,KAAK;QAEhB,mBAAmB;YACjB,MAAM;YACN,SAAS,CAAC,kCAAkC,EAAE,WAAW,CAAC,CAAC;QAC7D;QACA,0BAA0B;IAC5B;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,yEAAyE;YACzE,IAAI;YACJ,IAAI,eAAe;gBACjB,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACrD,cAAc,kBAAkB,gBAAgB,IAAI,GAAG;YACzD;YAEA,oDAAoD;YACpD,MAAM,gKAAA,CAAA,iBAAc,CAAC,aAAa,CAAC;gBACjC,QAAQ,UAAU;gBAClB,WAAW,cAAc,MAAM,GAAG,IAAI,gBAAgB;gBACtD,QAAQ,iBAAiB,KAAK,YAAY,iBAAiB;gBAC3D,WAAW,iBAAiB;gBAC5B;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,mFAAmF;QACrF,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,gBAAgB,IAAI,KAAK,iBAAiB;YAC5C,IAAI;gBACF,MAAM,gKAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,eAAe,EAAE;gBACzD,YAAY,aAAa,QAAQ,eAAe,cAAc,eAAe,YAAY;YAC3F,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF,OAAO,IAAI,gBAAgB,IAAI,KAAK,UAAU;YAC5C,IAAI;gBACF,MAAM,gKAAA,CAAA,iBAAc,CAAC,YAAY,CAAC,eAAe,EAAE;gBACnD,YAAY,aAAa,QAAQ,eAAe,cAAc,eAAe,YAAY;YAC3F,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;QACA,0BAA0B;IAC5B;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI;YACF,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,cAAc;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,yEAAyE;IACzE,MAAM,gBAAgB,aAAa,SAAS;IAE5C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,uMAAA,CAAA,QAAK;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAA8C;;;;;;;kCAI3E,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,wIAAA,CAAA,UAAU;gCACT,UAAU;gCACV,aAAa;gCACb,UAAU,aAAa,QAAQ,MAAM,KAAK;gCAC1C,WAAU;;;;;;0CAIZ,6LAAC;gCACC,SAAS;oCACP,kBAAkB;oCAClB,kBAAkB;gCACpB;gCACA,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,0IAAA,CAAA,UAAY;gBACX,OAAM;gBACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;oBAAC,MAAM;oBAAI,WAAU;;;;;;gBAClC,aAAY;gBACZ,eAAe;gBACf,cAAa;gBACb,aAAY;gBACZ,uBACE,6LAAC;oBACC,UAAU;oBACV,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,mLAAA,CAAA,cAAW;4CACV,aAAY;4CACZ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qLAAA,CAAA,eAAY;4CACX,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,KAAK;;8DAExD,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAW;;;;;;;;;;;;wCAI1B,+BACC,6LAAC,qLAAA,CAAA,eAAY;4CACX,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK;4CACzD,aAAY;4CACZ,UAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,UAAU,GAAG,CAAC,CAAC,wBACd,6LAAC;wDAAwB,OAAO,QAAQ,EAAE;kEACvC,QAAQ,IAAI;uDADF,QAAQ,EAAE;;;;;;;;;;;sDAO7B,6LAAC,0IAAA,CAAA,eAAY;4CAAC,MAAK;4CAAS,aAAY;4CAAS,SAAQ;;8DACvD,6LAAC,yMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC5B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAGrC,6LAAC,0IAAA,CAAA,eAAY;4CACX,MAAK;4CACL,SAAS;4CACT,aAAY;4CACZ,SAAQ;;8DAER,6LAAC,mNAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC/B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6IAAA,CAAA,UAAW;gCACV,OAAM;gCACN,OAAO;gCACP,UAAU;gCACV,SAAS;gCACT,aAAY;gCACZ,SAAS;gCACT,gBAAe;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6LAAC,mLAAA,CAAA,cAAW;gBACV,aAAY;gBACZ,SAAS;oBACP;wBAAE,QAAQ;wBAAW,OAAO;wBAAS,OAAO;oBAAM;oBAClD;wBAAE,QAAQ;wBAAS,OAAO;wBAAS,OAAO;oBAAM;oBAChD;wBAAE,QAAQ;wBAAW,OAAO;wBAAW,OAAO;oBAAM;oBACpD;wBAAE,QAAQ;wBAAU,OAAO;wBAAU,OAAO;oBAAM;oBAClD;wBAAE,QAAQ;wBAAY,OAAO;wBAAa,OAAO;oBAAM;oBACvD;wBAAE,QAAQ;wBAAS,OAAO;wBAAW,WAAW;wBAAc,OAAO;wBAAO,UAAU;oBAAM;iBAC7F;gBACD,MAAM;gBACN,WAAW;gBACX,cAAa;gBACb,yBAAW,6LAAC,uMAAA,CAAA,QAAK;oBAAC,MAAM;;;;;;gBACxB,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,gBAAgB,aAAa;gBAC7B,SAAQ;gBACR,oBAAoB;gBACpB,kBAAiB;gBACjB,sBAAqB;gBACrB,QAAQ,CAAC,OAAO;oBACd,wFAAwF;oBACxF,YACE,aACA,QACA,eACA,cACA,eACA,OACA;gBAEJ;gBACA,WAAW,CAAC,QAAQ,GAAG,cAAc,+BACnC,6LAAC;wBAAmB,WAAW,aAAa,OAAO;;4BAChD,eAAe,QAAQ,CAAC,0BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAEZ,CAAC,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,EAAE,IAAI,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,WACjF,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAC/C,OAAO,KAAK,CAAC,MAAM,CAAC,EAAE,EAAE,WAAW;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAEV,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,EAAE,IAAI,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,WAChF,OAAO,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,GACvC,OAAO,KAAK;;;;;;8DAElB,6LAAC;oDAAE,WAAU;8DACV,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;4BAOtB,eAAe,QAAQ,CAAC,0BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAY,OAAO,KAAK;;;;;;;;;;;;;;;;;4BAK7C,eAAe,QAAQ,CAAC,4BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;;gDACb,OAAO,aAAa,EAAE,UAAU;gDAAE;gDAAE,CAAC,OAAO,aAAa,EAAE,UAAU,CAAC,MAAM,IAAI,WAAW;;;;;;;;;;;;;;;;;;4BAMnG,eAAe,QAAQ,CAAC,2BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCACC,WAAW,CAAC,6DAA6D,EAAE,OAAO,MAAM,GAClF,yEACA,gEACF;8CAEH,OAAO,MAAM,iBACZ;;0DACE,6LAAC,8NAAA,CAAA,cAAW;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DACjC,6LAAC;0DAAK;;;;;;;qEAGR;;0DACE,6LAAC,+MAAA,CAAA,UAAO;gDAAC,MAAM;gDAAI,WAAU;;;;;;0DAC7B,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;4BAOf,eAAe,QAAQ,CAAC,8BACvB,6LAAC;gCAAG,WAAU;0CACX,WAAW,OAAO,SAAS;;;;;;4BAI/B,eAAe,QAAQ,CAAC,4BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,0BAA0B,EAAE,OAAO,EAAE,EAAE;4CACnE,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,mMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;;;;;;sDAGb,6LAAC;4CACC,SAAS,IAAM,iBAAiB;4CAChC,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAGd,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAW,CAAC,sBAAsB,EAAE,OAAO,MAAM,GAC3C,0FACA,yFACF;4CACJ,OAAO,OAAO,MAAM,GAAG,cAAc;sDAErC,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;sDAGf,6LAAC;4CACC,SAAS,IAAM,mBAAmB;4CAClC,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;uBA7Gd,OAAO,EAAE;;;;;;;;;;0BAuHtB,6LAAC,gJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,WAAW;gBACX,OAAM;gBACN,SAAS,iBAAiB,WAAW;;;;;;YAItC,gCACC,6LAAC,iJAAA,CAAA,UAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,kBAAkB;gBACjC,QAAQ;gBACR,WAAW;oBACT,kBAAkB;oBAClB,YAAY,GAAG,QAAQ,eAAe,cAAc,eAAe,YAAY;gBACjF;;;;;;0BAKJ,6LAAC,mJAAA,CAAA,UAAe;;;;;;;;;;;AAGtB;GAzjBM;;QAC0B,iIAAA,CAAA,UAAO;QACtB,qIAAA,CAAA,YAAS;;;KAFpB;uCA2jBS"}}, {"offset": {"line": 2980, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2986, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/insuranceServiceLimitService.js"], "sourcesContent": ["import { api } from \"@/utils/api\";\r\nimport { extractData, extractEntity } from \"@/utils/apiResponseAdapter\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\n\r\n// Adaptador para processar os dados de limites de serviço\r\nconst processServiceLimits = (limits) => {\r\n  if (!limits || !Array.isArray(limits)) {\r\n    return [];\r\n  }\r\n\r\n  return limits.map(limit => {\r\n    // Garantir que os campos estejam disponíveis independentemente do formato da resposta\r\n    return {\r\n      ...limit,\r\n      // Usar os campos com letra maiúscula se disponíveis, caso contrário usar os campos com letra minúscula\r\n      insurance: limit.insurance || {},\r\n      serviceType: limit.serviceType || {},\r\n      person: limit.person || {},\r\n      // Adicionar referências normalizadas\r\n      Insurance: limit.Insurance || limit.insurance || {},\r\n      ServiceType: limit.ServiceType || limit.serviceType || {},\r\n      Person: limit.Person || limit.person || {}\r\n    };\r\n  });\r\n};\r\n\r\nexport const insuranceServiceLimitService = {\r\n  // Obter todos os limites com suporte a filtros\r\n  getAllLimits: async (filters = {}) => {\r\n    try {\r\n      const { search, personId, personIds, insuranceId, serviceTypeId, companyId, sortField, sortDirection } = filters;\r\n\r\n      // Construir parâmetros de consulta\r\n      const params = new URLSearchParams();\r\n      if (search) params.append('search', search);\r\n      if (personId) params.append('personId', personId);\r\n      if (insuranceId) params.append('insuranceId', insuranceId);\r\n      if (serviceTypeId) params.append('serviceTypeId', serviceTypeId);\r\n      if (companyId) params.append('companyId', companyId);\r\n      if (sortField) params.append('sortField', sortField);\r\n      if (sortDirection) params.append('sortDirection', sortDirection);\r\n\r\n      // Adicionar personIds como parâmetros separados com notação de array\r\n      if (personIds && personIds.length > 0) {\r\n        // Garantir que personIds seja um array\r\n        const personIdsArray = Array.isArray(personIds) ? personIds : [personIds];\r\n\r\n        // Adicionar cada ID como um parâmetro separado\r\n        personIdsArray.forEach((id) => {\r\n          // Usar o mesmo nome de parâmetro para cada ID (sem índice)\r\n          params.append('personIds', id);\r\n        });\r\n\r\n        console.log(\"Filtrando por múltiplos IDs de pacientes:\", personIdsArray);\r\n      } else {\r\n        console.log(\"Nenhum filtro de paciente aplicado\");\r\n      }\r\n\r\n      // Log para depuração dos parâmetros de ordenação\r\n      if (sortField) {\r\n        // Garantir que a direção seja uma string válida\r\n        const validDirection = sortDirection && ['asc', 'desc'].includes(sortDirection.toLowerCase())\r\n          ? sortDirection.toLowerCase()\r\n          : 'asc';\r\n\r\n        console.log(`Parâmetros de ordenação: campo=${sortField}, direção=${validDirection}`);\r\n\r\n        // Atualizar o parâmetro de direção com o valor normalizado\r\n        params.set('sortDirection', validDirection);\r\n      }\r\n\r\n      // Não enviamos parâmetros de paginação para o backend\r\n      // para garantir que recebemos todos os limites de uma vez\r\n\r\n      console.log(`Enviando requisição para: /insurance-service-limits?${params.toString()}`);\r\n      const response = await api.get(`/insurance-service-limits?${params.toString()}`);\r\n\r\n      console.log(\"Resposta da API:\", response.data);\r\n\r\n      // Usar o adaptador para extrair os dados de forma consistente\r\n      // Primeiro tentamos extrair usando o formato padrão\r\n      const extracted = extractData(response.data, 'limits', ['data']);\r\n\r\n      // Se não houver limites no formato padrão, processamos o array diretamente\r\n      if (extracted.limits && extracted.limits.length > 0) {\r\n        return {\r\n          limits: processServiceLimits(extracted.limits),\r\n          total: extracted.total,\r\n          pages: extracted.pages\r\n        };\r\n      } else {\r\n        // Processar o array diretamente se a API retornar apenas um array\r\n        const processedLimits = processServiceLimits(response.data);\r\n\r\n        return {\r\n          limits: processedLimits,\r\n          total: processedLimits.length,\r\n          pages: Math.ceil(processedLimits.length / 10)\r\n        };\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar limites de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter limites para uma pessoa específica\r\n  getLimitsByPerson: async (personId) => {\r\n    try {\r\n      const response = await api.get(`/insurance-service-limits/person/${personId}`);\r\n      return processServiceLimits(response.data);\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar limites de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter limites para uma combinação pessoa+convênio\r\n  getLimitsByPersonInsurance: async (personId, insuranceId) => {\r\n    try {\r\n      const response = await api.get(`/insurance-service-limits/person/${personId}/insurance/${insuranceId}`);\r\n      return processServiceLimits(response.data);\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar limites de serviço por convênio:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Criar um novo limite\r\n  createLimit: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurance-service-limits', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar limite de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar um limite existente\r\n  updateLimit: async (id, data) => {\r\n    try {\r\n      const response = await api.put(`/insurance-service-limits/${id}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar limite de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir um limite\r\n  deleteLimit: async (id) => {\r\n    try {\r\n      await api.delete(`/insurance-service-limits/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir limite de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Verificar se um agendamento está dentro dos limites\r\n  checkAppointmentLimit: async (data) => {\r\n    try {\r\n      const response = await api.post('/insurance-service-limits/check', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao verificar limite de agendamento:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta a lista de limites de convênio com os filtros aplicados\r\n   * @param {Object} filters - Filtros atuais (busca, personIds, etc)\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx', 'pdf' ou 'image')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  exportInsuranceLimits: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await insuranceServiceLimitService.getAllLimits({\r\n        ...filters,\r\n        // Não precisamos de paginação para exportação\r\n      });\r\n\r\n      // Extrair os dados dos limites\r\n      const limitsArray = response.limits || [];\r\n\r\n      // Log para debug da estrutura dos dados\r\n      console.log(\"Estrutura dos limites para exportação:\", limitsArray);\r\n      if (limitsArray.length > 0) {\r\n        console.log(\"Exemplo do primeiro limite:\", JSON.stringify(limitsArray[0], null, 2));\r\n        console.log(\"Propriedades disponíveis:\", Object.keys(limitsArray[0]));\r\n      }\r\n\r\n      // Se os limites não tiverem as informações completas, vamos tentar enriquecê-los\r\n      // Verificar se temos objetos aninhados ou apenas IDs\r\n      const needsEnrichment = limitsArray.length > 0 && (\r\n        (!limitsArray[0].Person && !limitsArray[0].person && !limitsArray[0].personFullName) ||\r\n        (!limitsArray[0].Insurance && !limitsArray[0].insurance && !limitsArray[0].insuranceName) ||\r\n        (!limitsArray[0].ServiceType && !limitsArray[0].serviceType && !limitsArray[0].serviceTypeName)\r\n      );\r\n\r\n      // Se precisarmos enriquecer os dados, vamos buscar as informações faltantes\r\n      if (needsEnrichment) {\r\n        console.log(\"Dados incompletos detectados, buscando informações adicionais...\");\r\n\r\n        // Coletar todos os IDs únicos\r\n        const personIds = [...new Set(limitsArray.filter(l => l.personId).map(l => l.personId))];\r\n        const insuranceIds = [...new Set(limitsArray.filter(l => l.insuranceId).map(l => l.insuranceId))];\r\n        const serviceTypeIds = [...new Set(limitsArray.filter(l => l.serviceTypeId).map(l => l.serviceTypeId))];\r\n\r\n        console.log(\"IDs para enriquecimento:\", { personIds, insuranceIds, serviceTypeIds });\r\n\r\n        try {\r\n          // Buscar informações de pacientes se necessário\r\n          if (personIds.length > 0) {\r\n            const personsResponse = await api.get('/persons', {\r\n              params: { ids: personIds.join(',') }\r\n            });\r\n            const persons = personsResponse.data || [];\r\n\r\n            // Criar um mapa de ID para objeto completo\r\n            const personsMap = Array.isArray(persons)\r\n              ? persons.reduce((map, person) => {\r\n                  map[person.id] = person;\r\n                  return map;\r\n                }, {})\r\n              : {};\r\n\r\n            // Adicionar informações aos limites\r\n            limitsArray.forEach(limit => {\r\n              if (limit.personId && personsMap[limit.personId]) {\r\n                limit.Person = personsMap[limit.personId];\r\n              }\r\n            });\r\n          }\r\n\r\n          // Buscar informações de convênios se necessário\r\n          if (insuranceIds.length > 0) {\r\n            const insurancesResponse = await api.get('/insurances', {\r\n              params: { ids: insuranceIds.join(',') }\r\n            });\r\n            const insurances = insurancesResponse.data || [];\r\n\r\n            // Criar um mapa de ID para objeto completo\r\n            const insurancesMap = Array.isArray(insurances)\r\n              ? insurances.reduce((map, insurance) => {\r\n                  map[insurance.id] = insurance;\r\n                  return map;\r\n                }, {})\r\n              : {};\r\n\r\n            // Adicionar informações aos limites\r\n            limitsArray.forEach(limit => {\r\n              if (limit.insuranceId && insurancesMap[limit.insuranceId]) {\r\n                limit.Insurance = insurancesMap[limit.insuranceId];\r\n              }\r\n            });\r\n          }\r\n\r\n          // Buscar informações de tipos de serviço se necessário\r\n          if (serviceTypeIds.length > 0) {\r\n            const serviceTypesResponse = await api.get('/service-types', {\r\n              params: { ids: serviceTypeIds.join(',') }\r\n            });\r\n            const serviceTypes = serviceTypesResponse.data || [];\r\n\r\n            // Criar um mapa de ID para objeto completo\r\n            const serviceTypesMap = Array.isArray(serviceTypes)\r\n              ? serviceTypes.reduce((map, serviceType) => {\r\n                  map[serviceType.id] = serviceType;\r\n                  return map;\r\n                }, {})\r\n              : {};\r\n\r\n            // Adicionar informações aos limites\r\n            limitsArray.forEach(limit => {\r\n              if (limit.serviceTypeId && serviceTypesMap[limit.serviceTypeId]) {\r\n                limit.ServiceType = serviceTypesMap[limit.serviceTypeId];\r\n              }\r\n            });\r\n          }\r\n\r\n          console.log(\"Dados enriquecidos com sucesso\");\r\n        } catch (error) {\r\n          console.error(\"Erro ao enriquecer dados em lote:\", error);\r\n\r\n          // Tentar buscar individualmente para cada limite\r\n          console.log(\"Tentando buscar dados individualmente...\");\r\n\r\n          // Criar funções para buscar entidades individuais\r\n          const fetchPerson = async (id) => {\r\n            try {\r\n              const response = await api.get(`/persons/${id}`);\r\n              return response.data;\r\n            } catch (e) {\r\n              console.error(`Erro ao buscar pessoa ${id}:`, e);\r\n              return null;\r\n            }\r\n          };\r\n\r\n          const fetchInsurance = async (id) => {\r\n            try {\r\n              const response = await api.get(`/insurances/${id}`);\r\n              return response.data;\r\n            } catch (e) {\r\n              console.error(`Erro ao buscar convênio ${id}:`, e);\r\n              return null;\r\n            }\r\n          };\r\n\r\n          const fetchServiceType = async (id) => {\r\n            try {\r\n              const response = await api.get(`/service-types/${id}`);\r\n              return response.data;\r\n            } catch (e) {\r\n              console.error(`Erro ao buscar tipo de serviço ${id}:`, e);\r\n              return null;\r\n            }\r\n          };\r\n\r\n          // Buscar dados para cada limite individualmente\r\n          for (const limit of limitsArray) {\r\n            // Buscar pessoa se necessário\r\n            if (limit.personId && !limit.Person && !limit.person && !limit.personFullName) {\r\n              const person = await fetchPerson(limit.personId);\r\n              if (person) {\r\n                limit.Person = person;\r\n                console.log(`Pessoa ${limit.personId} encontrada individualmente`);\r\n              }\r\n            }\r\n\r\n            // Buscar convênio se necessário\r\n            if (limit.insuranceId && !limit.Insurance && !limit.insurance && !limit.insuranceName) {\r\n              const insurance = await fetchInsurance(limit.insuranceId);\r\n              if (insurance) {\r\n                limit.Insurance = insurance;\r\n                console.log(`Convênio ${limit.insuranceId} encontrado individualmente`);\r\n              }\r\n            }\r\n\r\n            // Buscar tipo de serviço se necessário\r\n            if (limit.serviceTypeId && !limit.ServiceType && !limit.serviceType && !limit.serviceTypeName) {\r\n              const serviceType = await fetchServiceType(limit.serviceTypeId);\r\n              if (serviceType) {\r\n                limit.ServiceType = serviceType;\r\n                console.log(`Tipo de serviço ${limit.serviceTypeId} encontrado individualmente`);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"patient\", header: \"Paciente\" },\r\n        { key: \"insurance\", header: \"Convênio\" },\r\n        { key: \"company\", header: \"Empresa\" },\r\n        { key: \"serviceType\", header: \"Tipo de Serviço\" },\r\n        {\r\n          key: \"monthlyLimit\",\r\n          header: \"Limite Mensal\",\r\n          format: (value) => value > 0 ? `${value}x por mês` : \"Ilimitado\"\r\n        },\r\n        { key: \"notes\", header: \"Observações\" },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = limitsArray.map(limit => {\r\n        // Extrair informações do paciente\r\n        let patientName = \"N/A\";\r\n        if (limit.Person && limit.Person.fullName) {\r\n          patientName = limit.Person.fullName;\r\n        } else if (limit.person && limit.person.fullName) {\r\n          patientName = limit.person.fullName;\r\n        } else if (limit.personFullName) {\r\n          patientName = limit.personFullName;\r\n        } else if (limit.personId) {\r\n          // Se tivermos apenas o ID, podemos tentar buscar o nome em outro lugar\r\n          patientName = `ID: ${limit.personId}`;\r\n        }\r\n\r\n        // Extrair informações do convênio\r\n        let insuranceName = \"N/A\";\r\n        if (limit.Insurance && limit.Insurance.name) {\r\n          insuranceName = limit.Insurance.name;\r\n        } else if (limit.insurance && limit.insurance.name) {\r\n          insuranceName = limit.insurance.name;\r\n        } else if (limit.insuranceName) {\r\n          insuranceName = limit.insuranceName;\r\n        } else if (limit.insuranceId) {\r\n          insuranceName = `ID: ${limit.insuranceId}`;\r\n        }\r\n\r\n        // Extrair informações do tipo de serviço\r\n        let serviceTypeName = \"N/A\";\r\n        if (limit.ServiceType && limit.ServiceType.name) {\r\n          serviceTypeName = limit.ServiceType.name;\r\n        } else if (limit.serviceType && limit.serviceType.name) {\r\n          serviceTypeName = limit.serviceType.name;\r\n        } else if (limit.serviceTypeName) {\r\n          serviceTypeName = limit.serviceTypeName;\r\n        } else if (limit.serviceTypeId) {\r\n          serviceTypeName = `ID: ${limit.serviceTypeId}`;\r\n        }\r\n\r\n        // Extrair informações da empresa\r\n        let companyName = \"\";\r\n        if (limit.Insurance?.company?.name) {\r\n          companyName = limit.Insurance.company.name;\r\n        } else if (limit.insurance?.company?.name) {\r\n          companyName = limit.insurance.company.name;\r\n        }\r\n\r\n        return {\r\n          patient: patientName,\r\n          insurance: insuranceName,\r\n          company: companyName,\r\n          serviceType: serviceTypeName,\r\n          monthlyLimit: limit.monthlyLimit || 0,\r\n          notes: limit.notes || \"\",\r\n          createdAt: limit.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.personIds && filters.personIds.length > 0) {\r\n        subtitleParts.push(`Pacientes específicos: ${filters.personIds.length} selecionados`);\r\n      }\r\n      if (filters.insuranceId) {\r\n        subtitleParts.push(`Convênio: ${filters.insuranceName || filters.insuranceId}`);\r\n      }\r\n      if (filters.serviceTypeId) {\r\n        subtitleParts.push(`Tipo de Serviço: ${filters.serviceTypeName || filters.serviceTypeId}`);\r\n      }\r\n      if (filters.companyId) {\r\n        // Tentar encontrar o nome da empresa nos dados\r\n        const companyName = limitsArray.find(l =>\r\n          l.Insurance?.company?.id === filters.companyId ||\r\n          l.insurance?.company?.id === filters.companyId\r\n        )?.Insurance?.company?.name ||\r\n        limitsArray.find(l =>\r\n          l.Insurance?.company?.id === filters.companyId ||\r\n          l.insurance?.company?.id === filters.companyId\r\n        )?.insurance?.company?.name;\r\n\r\n        subtitleParts.push(`Empresa: ${companyName || filters.companyId}`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"limites-convenio\",\r\n        columns,\r\n        title: \"Lista de Limites de Convênio\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar limites de convênio:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\nexport default insuranceServiceLimitService;"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEA,0DAA0D;AAC1D,MAAM,uBAAuB,CAAC;IAC5B,IAAI,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,SAAS;QACrC,OAAO,EAAE;IACX;IAEA,OAAO,OAAO,GAAG,CAAC,CAAA;QAChB,sFAAsF;QACtF,OAAO;YACL,GAAG,KAAK;YACR,uGAAuG;YACvG,WAAW,MAAM,SAAS,IAAI,CAAC;YAC/B,aAAa,MAAM,WAAW,IAAI,CAAC;YACnC,QAAQ,MAAM,MAAM,IAAI,CAAC;YACzB,qCAAqC;YACrC,WAAW,MAAM,SAAS,IAAI,MAAM,SAAS,IAAI,CAAC;YAClD,aAAa,MAAM,WAAW,IAAI,MAAM,WAAW,IAAI,CAAC;YACxD,QAAQ,MAAM,MAAM,IAAI,MAAM,MAAM,IAAI,CAAC;QAC3C;IACF;AACF;AAEO,MAAM,+BAA+B;IAC1C,+CAA+C;IAC/C,cAAc,OAAO,UAAU,CAAC,CAAC;QAC/B,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG;YAEzG,mCAAmC;YACnC,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,UAAU,OAAO,MAAM,CAAC,YAAY;YACxC,IAAI,aAAa,OAAO,MAAM,CAAC,eAAe;YAC9C,IAAI,eAAe,OAAO,MAAM,CAAC,iBAAiB;YAClD,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAC1C,IAAI,eAAe,OAAO,MAAM,CAAC,iBAAiB;YAElD,qEAAqE;YACrE,IAAI,aAAa,UAAU,MAAM,GAAG,GAAG;gBACrC,uCAAuC;gBACvC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,YAAY;oBAAC;iBAAU;gBAEzE,+CAA+C;gBAC/C,eAAe,OAAO,CAAC,CAAC;oBACtB,2DAA2D;oBAC3D,OAAO,MAAM,CAAC,aAAa;gBAC7B;gBAEA,QAAQ,GAAG,CAAC,6CAA6C;YAC3D,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;YAEA,iDAAiD;YACjD,IAAI,WAAW;gBACb,gDAAgD;gBAChD,MAAM,iBAAiB,iBAAiB;oBAAC;oBAAO;iBAAO,CAAC,QAAQ,CAAC,cAAc,WAAW,MACtF,cAAc,WAAW,KACzB;gBAEJ,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,UAAU,UAAU,EAAE,gBAAgB;gBAEpF,2DAA2D;gBAC3D,OAAO,GAAG,CAAC,iBAAiB;YAC9B;YAEA,sDAAsD;YACtD,0DAA0D;YAE1D,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,OAAO,QAAQ,IAAI;YACtF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,OAAO,QAAQ,IAAI;YAE/E,QAAQ,GAAG,CAAC,oBAAoB,SAAS,IAAI;YAE7C,8DAA8D;YAC9D,oDAAoD;YACpD,MAAM,YAAY,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD,EAAE,SAAS,IAAI,EAAE,UAAU;gBAAC;aAAO;YAE/D,2EAA2E;YAC3E,IAAI,UAAU,MAAM,IAAI,UAAU,MAAM,CAAC,MAAM,GAAG,GAAG;gBACnD,OAAO;oBACL,QAAQ,qBAAqB,UAAU,MAAM;oBAC7C,OAAO,UAAU,KAAK;oBACtB,OAAO,UAAU,KAAK;gBACxB;YACF,OAAO;gBACL,kEAAkE;gBAClE,MAAM,kBAAkB,qBAAqB,SAAS,IAAI;gBAE1D,OAAO;oBACL,QAAQ;oBACR,OAAO,gBAAgB,MAAM;oBAC7B,OAAO,KAAK,IAAI,CAAC,gBAAgB,MAAM,GAAG;gBAC5C;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,2CAA2C;IAC3C,mBAAmB,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU;YAC7E,OAAO,qBAAqB,SAAS,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,oDAAoD;IACpD,4BAA4B,OAAO,UAAU;QAC3C,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,WAAW,EAAE,aAAa;YACtG,OAAO,qBAAqB,SAAS,IAAI;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,aAAa,OAAO;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,6BAA6B;YAC7D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,aAAa,OAAO,IAAI;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,0BAA0B,EAAE,IAAI,EAAE;YAClE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC5D,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,aAAa,OAAO;QAClB,IAAI;YACF,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,0BAA0B,EAAE,IAAI;YAClD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,sDAAsD;IACtD,uBAAuB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,mCAAmC;YACnE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;YAC1D,MAAM;QACR;IACF;IAEA;;;;;GAKC,GACD,uBAAuB,OAAO,SAAS,eAAe,MAAM;QAC1D,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,6BAA6B,YAAY,CAAC;gBAC/D,GAAG,OAAO;YAEZ;YAEA,+BAA+B;YAC/B,MAAM,cAAc,SAAS,MAAM,IAAI,EAAE;YAEzC,wCAAwC;YACxC,QAAQ,GAAG,CAAC,0CAA0C;YACtD,IAAI,YAAY,MAAM,GAAG,GAAG;gBAC1B,QAAQ,GAAG,CAAC,+BAA+B,KAAK,SAAS,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM;gBAChF,QAAQ,GAAG,CAAC,6BAA6B,OAAO,IAAI,CAAC,WAAW,CAAC,EAAE;YACrE;YAEA,iFAAiF;YACjF,qDAAqD;YACrD,MAAM,kBAAkB,YAAY,MAAM,GAAG,KAAK,CAChD,AAAC,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,cAAc,IAClF,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,aAAa,IACvF,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,eAAe,AAChG;YAEA,4EAA4E;YAC5E,IAAI,iBAAiB;gBACnB,QAAQ,GAAG,CAAC;gBAEZ,8BAA8B;gBAC9B,MAAM,YAAY;uBAAI,IAAI,IAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;iBAAG;gBACxF,MAAM,eAAe;uBAAI,IAAI,IAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,WAAW;iBAAG;gBACjG,MAAM,iBAAiB;uBAAI,IAAI,IAAI,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,aAAa;iBAAG;gBAEvG,QAAQ,GAAG,CAAC,4BAA4B;oBAAE;oBAAW;oBAAc;gBAAe;gBAElF,IAAI;oBACF,gDAAgD;oBAChD,IAAI,UAAU,MAAM,GAAG,GAAG;wBACxB,MAAM,kBAAkB,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,YAAY;4BAChD,QAAQ;gCAAE,KAAK,UAAU,IAAI,CAAC;4BAAK;wBACrC;wBACA,MAAM,UAAU,gBAAgB,IAAI,IAAI,EAAE;wBAE1C,2CAA2C;wBAC3C,MAAM,aAAa,MAAM,OAAO,CAAC,WAC7B,QAAQ,MAAM,CAAC,CAAC,KAAK;4BACnB,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG;4BACjB,OAAO;wBACT,GAAG,CAAC,KACJ,CAAC;wBAEL,oCAAoC;wBACpC,YAAY,OAAO,CAAC,CAAA;4BAClB,IAAI,MAAM,QAAQ,IAAI,UAAU,CAAC,MAAM,QAAQ,CAAC,EAAE;gCAChD,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,QAAQ,CAAC;4BAC3C;wBACF;oBACF;oBAEA,gDAAgD;oBAChD,IAAI,aAAa,MAAM,GAAG,GAAG;wBAC3B,MAAM,qBAAqB,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,eAAe;4BACtD,QAAQ;gCAAE,KAAK,aAAa,IAAI,CAAC;4BAAK;wBACxC;wBACA,MAAM,aAAa,mBAAmB,IAAI,IAAI,EAAE;wBAEhD,2CAA2C;wBAC3C,MAAM,gBAAgB,MAAM,OAAO,CAAC,cAChC,WAAW,MAAM,CAAC,CAAC,KAAK;4BACtB,GAAG,CAAC,UAAU,EAAE,CAAC,GAAG;4BACpB,OAAO;wBACT,GAAG,CAAC,KACJ,CAAC;wBAEL,oCAAoC;wBACpC,YAAY,OAAO,CAAC,CAAA;4BAClB,IAAI,MAAM,WAAW,IAAI,aAAa,CAAC,MAAM,WAAW,CAAC,EAAE;gCACzD,MAAM,SAAS,GAAG,aAAa,CAAC,MAAM,WAAW,CAAC;4BACpD;wBACF;oBACF;oBAEA,uDAAuD;oBACvD,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,MAAM,uBAAuB,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,kBAAkB;4BAC3D,QAAQ;gCAAE,KAAK,eAAe,IAAI,CAAC;4BAAK;wBAC1C;wBACA,MAAM,eAAe,qBAAqB,IAAI,IAAI,EAAE;wBAEpD,2CAA2C;wBAC3C,MAAM,kBAAkB,MAAM,OAAO,CAAC,gBAClC,aAAa,MAAM,CAAC,CAAC,KAAK;4BACxB,GAAG,CAAC,YAAY,EAAE,CAAC,GAAG;4BACtB,OAAO;wBACT,GAAG,CAAC,KACJ,CAAC;wBAEL,oCAAoC;wBACpC,YAAY,OAAO,CAAC,CAAA;4BAClB,IAAI,MAAM,aAAa,IAAI,eAAe,CAAC,MAAM,aAAa,CAAC,EAAE;gCAC/D,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,aAAa,CAAC;4BAC1D;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC;gBACd,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,qCAAqC;oBAEnD,iDAAiD;oBACjD,QAAQ,GAAG,CAAC;oBAEZ,kDAAkD;oBAClD,MAAM,cAAc,OAAO;wBACzB,IAAI;4BACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;4BAC/C,OAAO,SAAS,IAAI;wBACtB,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,GAAG,CAAC,CAAC,EAAE;4BAC9C,OAAO;wBACT;oBACF;oBAEA,MAAM,iBAAiB,OAAO;wBAC5B,IAAI;4BACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;4BAClD,OAAO,SAAS,IAAI;wBACtB,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,GAAG,CAAC,CAAC,EAAE;4BAChD,OAAO;wBACT;oBACF;oBAEA,MAAM,mBAAmB,OAAO;wBAC9B,IAAI;4BACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;4BACrD,OAAO,SAAS,IAAI;wBACtB,EAAE,OAAO,GAAG;4BACV,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;4BACvD,OAAO;wBACT;oBACF;oBAEA,gDAAgD;oBAChD,KAAK,MAAM,SAAS,YAAa;wBAC/B,8BAA8B;wBAC9B,IAAI,MAAM,QAAQ,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,cAAc,EAAE;4BAC7E,MAAM,SAAS,MAAM,YAAY,MAAM,QAAQ;4BAC/C,IAAI,QAAQ;gCACV,MAAM,MAAM,GAAG;gCACf,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,MAAM,QAAQ,CAAC,2BAA2B,CAAC;4BACnE;wBACF;wBAEA,gCAAgC;wBAChC,IAAI,MAAM,WAAW,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,SAAS,IAAI,CAAC,MAAM,aAAa,EAAE;4BACrF,MAAM,YAAY,MAAM,eAAe,MAAM,WAAW;4BACxD,IAAI,WAAW;gCACb,MAAM,SAAS,GAAG;gCAClB,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,WAAW,CAAC,2BAA2B,CAAC;4BACxE;wBACF;wBAEA,uCAAuC;wBACvC,IAAI,MAAM,aAAa,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,WAAW,IAAI,CAAC,MAAM,eAAe,EAAE;4BAC7F,MAAM,cAAc,MAAM,iBAAiB,MAAM,aAAa;4BAC9D,IAAI,aAAa;gCACf,MAAM,WAAW,GAAG;gCACpB,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,MAAM,aAAa,CAAC,2BAA2B,CAAC;4BACjF;wBACF;oBACF;gBACF;YACF;YAEA,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAW,QAAQ;gBAAW;gBACrC;oBAAE,KAAK;oBAAa,QAAQ;gBAAW;gBACvC;oBAAE,KAAK;oBAAW,QAAQ;gBAAU;gBACpC;oBAAE,KAAK;oBAAe,QAAQ;gBAAkB;gBAChD;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,QAAQ,IAAI,GAAG,MAAM,SAAS,CAAC,GAAG;gBACvD;gBACA;oBAAE,KAAK;oBAAS,QAAQ;gBAAc;gBACtC;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,YAAY,GAAG,CAAC,CAAA;gBACnC,kCAAkC;gBAClC,IAAI,cAAc;gBAClB,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,EAAE;oBACzC,cAAc,MAAM,MAAM,CAAC,QAAQ;gBACrC,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM,CAAC,QAAQ,EAAE;oBAChD,cAAc,MAAM,MAAM,CAAC,QAAQ;gBACrC,OAAO,IAAI,MAAM,cAAc,EAAE;oBAC/B,cAAc,MAAM,cAAc;gBACpC,OAAO,IAAI,MAAM,QAAQ,EAAE;oBACzB,uEAAuE;oBACvE,cAAc,CAAC,IAAI,EAAE,MAAM,QAAQ,EAAE;gBACvC;gBAEA,kCAAkC;gBAClC,IAAI,gBAAgB;gBACpB,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,EAAE;oBAC3C,gBAAgB,MAAM,SAAS,CAAC,IAAI;gBACtC,OAAO,IAAI,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,IAAI,EAAE;oBAClD,gBAAgB,MAAM,SAAS,CAAC,IAAI;gBACtC,OAAO,IAAI,MAAM,aAAa,EAAE;oBAC9B,gBAAgB,MAAM,aAAa;gBACrC,OAAO,IAAI,MAAM,WAAW,EAAE;oBAC5B,gBAAgB,CAAC,IAAI,EAAE,MAAM,WAAW,EAAE;gBAC5C;gBAEA,yCAAyC;gBACzC,IAAI,kBAAkB;gBACtB,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,IAAI,EAAE;oBAC/C,kBAAkB,MAAM,WAAW,CAAC,IAAI;gBAC1C,OAAO,IAAI,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,IAAI,EAAE;oBACtD,kBAAkB,MAAM,WAAW,CAAC,IAAI;gBAC1C,OAAO,IAAI,MAAM,eAAe,EAAE;oBAChC,kBAAkB,MAAM,eAAe;gBACzC,OAAO,IAAI,MAAM,aAAa,EAAE;oBAC9B,kBAAkB,CAAC,IAAI,EAAE,MAAM,aAAa,EAAE;gBAChD;gBAEA,iCAAiC;gBACjC,IAAI,cAAc;gBAClB,IAAI,MAAM,SAAS,EAAE,SAAS,MAAM;oBAClC,cAAc,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;gBAC5C,OAAO,IAAI,MAAM,SAAS,EAAE,SAAS,MAAM;oBACzC,cAAc,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI;gBAC5C;gBAEA,OAAO;oBACL,SAAS;oBACT,WAAW;oBACX,SAAS;oBACT,aAAa;oBACb,cAAc,MAAM,YAAY,IAAI;oBACpC,OAAO,MAAM,KAAK,IAAI;oBACtB,WAAW,MAAM,SAAS,IAAI;gBAChC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gBACrD,cAAc,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC;YACtF;YACA,IAAI,QAAQ,WAAW,EAAE;gBACvB,cAAc,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,aAAa,IAAI,QAAQ,WAAW,EAAE;YAChF;YACA,IAAI,QAAQ,aAAa,EAAE;gBACzB,cAAc,IAAI,CAAC,CAAC,iBAAiB,EAAE,QAAQ,eAAe,IAAI,QAAQ,aAAa,EAAE;YAC3F;YACA,IAAI,QAAQ,SAAS,EAAE;gBACrB,+CAA+C;gBAC/C,MAAM,cAAc,YAAY,IAAI,CAAC,CAAA,IACnC,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,IAC9C,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,GAC7C,WAAW,SAAS,QACvB,YAAY,IAAI,CAAC,CAAA,IACf,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,IAC9C,EAAE,SAAS,EAAE,SAAS,OAAO,QAAQ,SAAS,GAC7C,WAAW,SAAS;gBAEvB,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,eAAe,QAAQ,SAAS,EAAE;YACnE;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,0IAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;AACF;uCAEe"}}, {"offset": {"line": 3444, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3450, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/scheduler/services/serviceTypeService.js"], "sourcesContent": ["import { api } from \"@/utils/api\";\r\nimport { format as dateFormat } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\n\r\n\r\nexport const serviceTypeService = {\r\n  // Listar tipos de serviço com suporte a filtros\r\n  getServiceTypes: async ({ search, companyId, serviceTypeIds } = {}) => {\r\n    try {\r\n      const params = new URLSearchParams();\r\n      if (search) params.append('search', search);\r\n      if (companyId) params.append('companyId', companyId);\r\n\r\n      // Adicionar suporte para múltiplos IDs de tipos de serviço\r\n      if (serviceTypeIds && Array.isArray(serviceTypeIds) && serviceTypeIds.length > 0) {\r\n        serviceTypeIds.forEach(id => params.append('serviceTypeIds', id));\r\n      }\r\n\r\n      const response = await api.get(`/service-types?${params.toString()}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao buscar tipos de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Obter um tipo de serviço específico\r\n  getServiceType: async (id) => {\r\n    try {\r\n      const response = await api.get(`/service-types/${id}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao buscar tipo de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Criar um novo tipo de serviço\r\n  createServiceType: async (data) => {\r\n    try {\r\n      const response = await api.post('/service-types', data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(\"Erro ao criar tipo de serviço:\", error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Atualizar um tipo de serviço existente\r\n  updateServiceType: async (id, data) => {\r\n    try {\r\n      const response = await api.put(`/service-types/${id}`, data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Erro ao atualizar tipo de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Excluir um tipo de serviço\r\n  deleteServiceType: async (id) => {\r\n    try {\r\n      await api.delete(`/service-types/${id}`);\r\n      return true;\r\n    } catch (error) {\r\n      console.error(`Erro ao excluir tipo de serviço ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  /**\r\n * Exporta a lista de tipos de serviço com os filtros aplicados\r\n * @param {Object} filters - Filtros atuais (busca, companyId, etc)\r\n * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')\r\n * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n */\r\n  exportServiceTypes: async (filters, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados filtrados da API\r\n      const response = await serviceTypeService.getServiceTypes(filters);\r\n\r\n      // Extrair os dados dos tipos de serviço\r\n      const data = response?.serviceTypes || [];\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = dateFormat(new Date(), \"dd/MM/yyyy 'às' HH:mm\", { locale: ptBR });\r\n\r\n      // Definição das colunas com formatação\r\n      const columns = [\r\n        { key: \"name\", header: \"Nome do Serviço\" },\r\n        {\r\n          key: \"value\",\r\n          header: \"Valor\",\r\n          format: (value) => new Intl.NumberFormat(\"pt-BR\", {\r\n            style: \"currency\",\r\n            currency: \"BRL\",\r\n          }).format(value),\r\n          align: \"right\",\r\n          width: 25\r\n        },\r\n        {\r\n          key: \"companyName\",\r\n          header: \"Empresa\",\r\n          format: (value, item) => item.company ? item.company.name : \"N/A\"\r\n        },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" },\r\n      ];\r\n\r\n      // Preparar os dados para exportação\r\n      const preparedData = data.map(serviceType => {\r\n        return {\r\n          name: serviceType.name || \"\",\r\n          value: serviceType.value || 0,\r\n          companyName: serviceType.company ? serviceType.company.name : \"N/A\",\r\n          createdAt: serviceType.createdAt || \"\",\r\n        };\r\n      });\r\n\r\n      // Filtros aplicados para subtítulo\r\n      let subtitleParts = [];\r\n      if (filters.search) subtitleParts.push(`Busca: \"${filters.search}\"`);\r\n      if (filters.companyId) {\r\n        const companyName = data.length > 0 && data[0].company ? data[0].company.name : \"Selecionada\";\r\n        subtitleParts.push(`Empresa: ${companyName}`);\r\n      }\r\n      if (filters.serviceTypeIds && Array.isArray(filters.serviceTypeIds) && filters.serviceTypeIds.length > 0) {\r\n        subtitleParts.push(`Tipos de Serviço: ${filters.serviceTypeIds.length} selecionados`);\r\n      }\r\n\r\n      // Construir o subtítulo\r\n      let subtitle = `Exportado em: ${timestamp}`;\r\n      if (subtitleParts.length > 0) {\r\n        subtitle += ` | Filtros: ${subtitleParts.join(\", \")}`;\r\n      }\r\n\r\n      // Exportar os dados\r\n      return await exportService.exportData(preparedData, {\r\n        format: exportFormat,\r\n        filename: \"tipos-de-servico\",\r\n        columns,\r\n        title: \"Lista de Tipos de Serviço\",\r\n        subtitle\r\n      });\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar tipos de serviço:\", error);\r\n      return false;\r\n    }\r\n  }\r\n};\r\n\r\nexport default serviceTypeService;"], "names": [], "mappings": ";;;;AAAA;AAGA;AAFA;AACA;;;;;AAIO,MAAM,qBAAqB;IAChC,gDAAgD;IAChD,iBAAiB,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC;QAChE,IAAI;YACF,MAAM,SAAS,IAAI;YACnB,IAAI,QAAQ,OAAO,MAAM,CAAC,UAAU;YACpC,IAAI,WAAW,OAAO,MAAM,CAAC,aAAa;YAE1C,2DAA2D;YAC3D,IAAI,kBAAkB,MAAM,OAAO,CAAC,mBAAmB,eAAe,MAAM,GAAG,GAAG;gBAChF,eAAe,OAAO,CAAC,CAAA,KAAM,OAAO,MAAM,CAAC,kBAAkB;YAC/D;YAEA,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;YACpE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;YACrD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC,EAAE;YACvD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,mBAAmB,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,IAAI,CAAC,kBAAkB;YAClD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,mBAAmB,OAAO,IAAI;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,sHAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;YACvD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,6BAA6B;IAC7B,mBAAmB,OAAO;QACxB,IAAI;YACF,MAAM,sHAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,GAAG,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IACA;;;;;CAKD,GACC,oBAAoB,OAAO,SAAS,eAAe,MAAM;QACvD,IAAI;YACF,kCAAkC;YAClC,MAAM,WAAW,MAAM,mBAAmB,eAAe,CAAC;YAE1D,wCAAwC;YACxC,MAAM,OAAO,UAAU,gBAAgB,EAAE;YAEzC,mCAAmC;YACnC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,SAAU,AAAD,EAAE,IAAI,QAAQ,yBAAyB;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;YAEjF,uCAAuC;YACvC,MAAM,UAAU;gBACd;oBAAE,KAAK;oBAAQ,QAAQ;gBAAkB;gBACzC;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,QAAU,IAAI,KAAK,YAAY,CAAC,SAAS;4BAChD,OAAO;4BACP,UAAU;wBACZ,GAAG,MAAM,CAAC;oBACV,OAAO;oBACP,OAAO;gBACT;gBACA;oBACE,KAAK;oBACL,QAAQ;oBACR,QAAQ,CAAC,OAAO,OAAS,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,IAAI,GAAG;gBAC9D;gBACA;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oCAAoC;YACpC,MAAM,eAAe,KAAK,GAAG,CAAC,CAAA;gBAC5B,OAAO;oBACL,MAAM,YAAY,IAAI,IAAI;oBAC1B,OAAO,YAAY,KAAK,IAAI;oBAC5B,aAAa,YAAY,OAAO,GAAG,YAAY,OAAO,CAAC,IAAI,GAAG;oBAC9D,WAAW,YAAY,SAAS,IAAI;gBACtC;YACF;YAEA,mCAAmC;YACnC,IAAI,gBAAgB,EAAE;YACtB,IAAI,QAAQ,MAAM,EAAE,cAAc,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,CAAC,CAAC;YACnE,IAAI,QAAQ,SAAS,EAAE;gBACrB,MAAM,cAAc,KAAK,MAAM,GAAG,KAAK,IAAI,CAAC,EAAE,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,GAAG;gBAChF,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,aAAa;YAC9C;YACA,IAAI,QAAQ,cAAc,IAAI,MAAM,OAAO,CAAC,QAAQ,cAAc,KAAK,QAAQ,cAAc,CAAC,MAAM,GAAG,GAAG;gBACxG,cAAc,IAAI,CAAC,CAAC,kBAAkB,EAAE,QAAQ,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC;YACtF;YAEA,wBAAwB;YACxB,IAAI,WAAW,CAAC,cAAc,EAAE,WAAW;YAC3C,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,YAAY,CAAC,YAAY,EAAE,cAAc,IAAI,CAAC,OAAO;YACvD;YAEA,oBAAoB;YACpB,OAAO,MAAM,0IAAA,CAAA,gBAAa,CAAC,UAAU,CAAC,cAAc;gBAClD,QAAQ;gBACR,UAAU;gBACV;gBACA,OAAO;gBACP;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO;QACT;IACF;AACF;uCAEe"}}, {"offset": {"line": 3604, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3610, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/InsuranceLimitsPage/InsuranceLimitsPage.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect, useCallback } from \"react\";\r\nimport TutorialManager from \"@/components/tutorial/TutorialManager\";\r\nimport TutorialTriggerButton from \"@/components/tutorial/TutorialTriggerButton\";\r\nimport ModuleHeader, { FilterButton } from \"@/components/ui/ModuleHeader\";\r\nimport { ModuleSelect, ModuleInput, ModuleTable } from \"@/components/ui\";\r\nimport MultiSelect from \"@/components/ui/multi-select\";\r\nimport {\r\n  Plus,\r\n  Search,\r\n  Filter,\r\n  RefreshCw,\r\n  Edit,\r\n  Trash,\r\n  Shield,\r\n  CreditCard,\r\n  User,\r\n  Tag,\r\n  Clock\r\n} from \"lucide-react\";\r\nimport { insuranceServiceLimitService } from \"@/app/modules/people/services/insuranceServiceLimitService\";\r\nimport { insurancesService } from \"@/app/modules/people/services/insurancesService\";\r\nimport { personsService } from \"@/app/modules/people/services/personsService\";\r\nimport { serviceTypeService } from \"@/app/modules/scheduler/services/serviceTypeService\";\r\nimport { companyService } from \"@/app/modules/admin/services/companyService\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { useToast } from \"@/contexts/ToastContext\";\r\nimport ConfirmationDialog from \"@/components/ui/ConfirmationDialog\";\r\nimport InsuranceLimitFormModal from \"@/components/people/InsuranceLimitFormModal\";\r\nimport ExportMenu from \"@/components/ui/ExportMenu\";\r\n\r\n// Tutorial steps para a página de limites de convênio\r\nconst insuranceLimitsTutorialSteps = [\r\n  {\r\n    title: \"Limites de Convênio\",\r\n    content: \"Esta tela permite configurar os limites de utilização de serviços por convênio para cada paciente.\",\r\n    selector: \"h1\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Adicionar Novo Limite\",\r\n    content: \"Clique aqui para adicionar um novo limite de convênio.\",\r\n    selector: \"button:has(span:contains('Novo Limite'))\",\r\n    position: \"left\"\r\n  },\r\n  {\r\n    title: \"Filtrar Limites\",\r\n    content: \"Use esta barra de pesquisa para encontrar limites específicos pelo nome do paciente ou convênio.\",\r\n    selector: \"input[placeholder*='Buscar']\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Pacientes\",\r\n    content: \"Selecione um ou mais pacientes para filtrar os limites.\",\r\n    selector: \"div:has(> div > label:contains('Filtrar por Pacientes'))\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Convênio\",\r\n    content: \"Filtre os limites por convênio específico.\",\r\n    selector: \"select:nth-of-type(1)\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Filtrar por Tipo de Serviço\",\r\n    content: \"Filtre os limites por tipo de serviço específico.\",\r\n    selector: \"select:nth-of-type(2)\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Exportar Dados\",\r\n    content: \"Exporte a lista de limites em diferentes formatos usando este botão.\",\r\n    selector: \".export-button\",\r\n    position: \"bottom\"\r\n  },\r\n  {\r\n    title: \"Gerenciar Limites\",\r\n    content: \"Edite ou exclua limites existentes usando os botões de ação na tabela.\",\r\n    selector: \"table\",\r\n    position: \"top\"\r\n  }\r\n];\r\n\r\nconst InsuranceLimitsPage = () => {\r\n  const { user: currentUser } = useAuth();\r\n  const { addToast, toast_success, toast_error } = useToast();\r\n  const isAdmin = currentUser?.role === \"ADMIN\" || currentUser?.role === \"SYSTEM_ADMIN\";\r\n  const isSystemAdmin = currentUser?.role === \"SYSTEM_ADMIN\";\r\n  const [limits, setLimits] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [filters, setFilters] = useState({\r\n    insuranceId: \"\",\r\n    serviceTypeId: \"\",\r\n    companyId: \"\"\r\n  });\r\n  const [personsFilter, setPersonsFilter] = useState([]);\r\n  const [personOptions, setPersonOptions] = useState([]);\r\n  const [isLoadingPersonOptions, setIsLoadingPersonOptions] = useState(false);\r\n  const [persons, setPersons] = useState([]);\r\n  const [insurances, setInsurances] = useState([]);\r\n  const [serviceTypes, setServiceTypes] = useState([]);\r\n  const [companies, setCompanies] = useState([]);\r\n  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);\r\n  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);\r\n  const [selectedLimit, setSelectedLimit] = useState(null);\r\n  const [limitFormOpen, setLimitFormOpen] = useState(false);\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const [isLoadingFilters, setIsLoadingFilters] = useState(false);\r\n  // Estados para paginação\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [totalPages, setTotalPages] = useState(1);\r\n  const [totalItems, setTotalItems] = useState(0);\r\n  // Estado para ordenação\r\n  const [sortField, setSortField] = useState(\"patient\");\r\n  const [sortDirection, setSortDirection] = useState(\"asc\");\r\n\r\n  // Função para carregar opções de pacientes para o multi-select\r\n  const loadPersonOptions = useCallback(async () => {\r\n    setIsLoadingPersonOptions(true);\r\n    try {\r\n      // Carregar todos os pacientes para o multi-select (com limite maior)\r\n      const response = await personsService.getPersons({\r\n        limit: 100, // Limite maior para ter mais opções\r\n        active: true // Apenas pacientes ativos por padrão\r\n      });\r\n\r\n      // Extrair os pacientes da resposta, garantindo que temos um array válido\r\n      const personsArray = response?.persons || response?.people || [];\r\n\r\n      const options = personsArray.map(person => ({\r\n        value: person.id,\r\n        label: person.fullName,\r\n        // Guardar o nome para ordenação\r\n        sortName: person.fullName ? person.fullName.toLowerCase() : ''\r\n      })) || [];\r\n\r\n      // Ordenar as opções alfabeticamente pelo nome\r\n      const sortedOptions = options.sort((a, b) => a.sortName.localeCompare(b.sortName, 'pt-BR', { sensitivity: 'base' }));\r\n\r\n      console.log('Opções de pacientes ordenadas:', sortedOptions);\r\n      setPersonOptions(sortedOptions);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de pacientes:\", error);\r\n      setPersonOptions([]);\r\n    } finally {\r\n      setIsLoadingPersonOptions(false);\r\n    }\r\n  }, []);\r\n\r\n  // Função para carregar empresas (apenas para system_admin)\r\n  const loadCompanies = async () => {\r\n    if (!isSystemAdmin) return;\r\n\r\n    setIsLoadingCompanies(true);\r\n    try {\r\n      const response = await companyService.getCompaniesForSelect();\r\n      setCompanies(response);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar empresas:\", error);\r\n    } finally {\r\n      setIsLoadingCompanies(false);\r\n    }\r\n  };\r\n\r\n  // Carregar dados iniciais\r\n  useEffect(() => {\r\n    loadLimits();\r\n    loadFilterOptions();\r\n    loadPersonOptions();\r\n    // Carregar empresas se o usuário for system_admin\r\n    if (isSystemAdmin) {\r\n      loadCompanies();\r\n    }\r\n  }, [loadPersonOptions, isSystemAdmin]);\r\n\r\n  // Constante para itens por página\r\n  const ITEMS_PER_PAGE = 10;\r\n\r\n  // Carregar limites de convênio\r\n  const loadLimits = async (\r\n    page = currentPage,\r\n    searchQuery = search,\r\n    personIds = personsFilter,\r\n    filterOptions = filters,\r\n    sort = sortField || 'patient',\r\n    direction = sortDirection || 'asc'\r\n  ) => {\r\n    setIsLoading(true);\r\n    try {\r\n      // Garantir que a direção seja uma string válida\r\n      const validDirection = direction && ['asc', 'desc'].includes(direction.toLowerCase())\r\n        ? direction.toLowerCase()\r\n        : 'asc';\r\n\r\n      // Garantir que a página é um número\r\n      const pageNumber = parseInt(page, 10);\r\n\r\n      // Atualizar o estado da página atual\r\n      setCurrentPage(pageNumber);\r\n\r\n      // Buscar TODOS os limites de convênio (sem paginação no backend)\r\n      const response = await insuranceServiceLimitService.getAllLimits({\r\n        search: searchQuery || undefined,\r\n        personIds: personIds.length > 0 ? personIds : undefined,\r\n        ...filterOptions,\r\n        sortField: sort || 'patient',\r\n        sortDirection: validDirection\r\n      });\r\n\r\n      console.log('Parâmetros enviados para o serviço:', {\r\n        search: searchQuery || undefined,\r\n        personIds: personIds.length > 0 ? personIds : undefined,\r\n        ...filterOptions,\r\n        sortField: sort || 'patient',\r\n        sortDirection: validDirection\r\n      });\r\n\r\n      // Verificar se temos os dados no formato esperado\r\n      if (response && typeof response === 'object') {\r\n        // Extrair todos os limites\r\n        let allLimits = response.limits || [];\r\n\r\n        // A ordenação agora é feita no backend, não precisamos ordenar manualmente aqui\r\n        // Apenas registramos a ordenação para debug\r\n        console.log(`Ordenação aplicada: campo=${sort || 'patient'}, direção=${validDirection}`);\r\n\r\n        // Calcular o total de itens e páginas\r\n        const total = allLimits.length;\r\n        const pages = Math.ceil(total / ITEMS_PER_PAGE) || 1;\r\n\r\n        // Aplicar paginação manual no lado do cliente\r\n        const startIndex = (pageNumber - 1) * ITEMS_PER_PAGE;\r\n        const endIndex = startIndex + ITEMS_PER_PAGE;\r\n        const paginatedLimits = allLimits.slice(startIndex, endIndex);\r\n\r\n        // Verificar se a página atual é válida\r\n        if (pageNumber > 1 && paginatedLimits.length === 0 && allLimits.length > 0) {\r\n          // Se a página atual não tem itens, mas existem itens em outras páginas,\r\n          // voltar para a primeira página\r\n          console.log(`Página ${pageNumber} está vazia, voltando para a página 1`);\r\n          setCurrentPage(1);\r\n          const firstPageLimits = allLimits.slice(0, ITEMS_PER_PAGE);\r\n          setLimits(firstPageLimits);\r\n        } else {\r\n          // Atualizar o estado com os dados paginados manualmente\r\n          setLimits(paginatedLimits); // Apenas os 10 itens da página atual\r\n        }\r\n\r\n        setTotalItems(total);\r\n        setTotalPages(pages);\r\n\r\n        console.log(`Paginação manual: Página ${pageNumber}, exibindo ${paginatedLimits.length} itens (${startIndex+1}-${Math.min(endIndex, total)}) de ${total} total`);\r\n      } else {\r\n        console.error('Dados de limites inválidos:', response);\r\n        setLimits([]);\r\n        setTotalItems(0);\r\n        setTotalPages(1);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar limites de convênio:\", error);\r\n      toast_error(\"Erro ao carregar limites de convênio\");\r\n      setLimits([]);\r\n      setTotalItems(0);\r\n      setTotalPages(1);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Carregar opções para os filtros\r\n  const loadFilterOptions = async () => {\r\n    setIsLoadingFilters(true);\r\n    try {\r\n      // Carregar pessoas\r\n      try {\r\n        const personsData = await personsService.getPersons({ limit: 100 });\r\n        console.log('Dados de pessoas recebidos:', personsData);\r\n\r\n        // Garantir que temos um array válido\r\n        if (personsData && typeof personsData === 'object') {\r\n          // Verificar a estrutura dos dados retornados e extrair o array de pessoas\r\n          const personsArray = Array.isArray(personsData) ? personsData :\r\n                              personsData.persons ? personsData.persons :\r\n                              personsData.people ? personsData.people :\r\n                              personsData.data ? personsData.data : [];\r\n\r\n          console.log('Array de pessoas extraído:', personsArray);\r\n          // Garantir que estamos definindo um array\r\n          setPersons(Array.isArray(personsArray) ? personsArray : []);\r\n        } else {\r\n          console.error('Dados de pessoas inválidos:', personsData);\r\n          setPersons([]);\r\n        }\r\n      } catch (personError) {\r\n        console.error('Erro ao carregar pessoas:', personError);\r\n        setPersons([]);\r\n      }\r\n\r\n      // Carregar convênios\r\n      try {\r\n        const insurancesData = await insurancesService.getInsurances();\r\n        console.log('Dados de convênios recebidos:', insurancesData);\r\n\r\n        // Garantir que temos um array válido\r\n        if (insurancesData && typeof insurancesData === 'object') {\r\n          // Verificar a estrutura dos dados retornados e extrair o array de convênios\r\n          const insurancesArray = Array.isArray(insurancesData) ? insurancesData :\r\n                                 insurancesData.insurances ? insurancesData.insurances :\r\n                                 insurancesData.data ? insurancesData.data : [];\r\n\r\n          console.log('Array de convênios extraído:', insurancesArray);\r\n          // Garantir que estamos definindo um array\r\n          setInsurances(Array.isArray(insurancesArray) ? insurancesArray : []);\r\n        } else {\r\n          console.error('Dados de convênios inválidos:', insurancesData);\r\n          setInsurances([]);\r\n        }\r\n      } catch (insuranceError) {\r\n        console.error('Erro ao carregar convênios:', insuranceError);\r\n        setInsurances([]);\r\n      }\r\n\r\n      // Carregar tipos de serviço\r\n      try {\r\n        const serviceTypesData = await serviceTypeService.getServiceTypes();\r\n        console.log('Dados de tipos de serviço recebidos:', serviceTypesData);\r\n\r\n        // Garantir que temos um array válido\r\n        if (serviceTypesData && typeof serviceTypesData === 'object') {\r\n          // Verificar a estrutura dos dados retornados e extrair o array de tipos de serviço\r\n          const serviceTypesArray = Array.isArray(serviceTypesData) ? serviceTypesData :\r\n                                   serviceTypesData.serviceTypes ? serviceTypesData.serviceTypes :\r\n                                   serviceTypesData.data ? serviceTypesData.data : [];\r\n\r\n          console.log('Array de tipos de serviço extraído:', serviceTypesArray);\r\n          // Garantir que estamos definindo um array\r\n          setServiceTypes(Array.isArray(serviceTypesArray) ? serviceTypesArray : []);\r\n        } else {\r\n          console.error('Dados de tipos de serviço inválidos:', serviceTypesData);\r\n          setServiceTypes([]);\r\n        }\r\n      } catch (serviceTypeError) {\r\n        console.error('Erro ao carregar tipos de serviço:', serviceTypeError);\r\n        setServiceTypes([]);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar opções de filtro:\", error);\r\n      toast_error(\"Erro ao carregar opções de filtro\");\r\n    } finally {\r\n      setIsLoadingFilters(false);\r\n    }\r\n  };\r\n\r\n  // Manipuladores de eventos\r\n  const handleSearch = (e) => {\r\n    e.preventDefault();\r\n    loadLimits(1, search, personsFilter, filters, sortField, sortDirection);\r\n  };\r\n\r\n  const handleFilterChange = (field, value) => {\r\n    const newFilters = { ...filters, [field]: value };\r\n    setFilters(newFilters);\r\n    loadLimits(1, search, personsFilter, newFilters, sortField, sortDirection);\r\n  };\r\n\r\n  const handlePersonsFilterChange = (value) => {\r\n    setPersonsFilter(value);\r\n    loadLimits(1, search, value, filters, sortField, sortDirection);\r\n  };\r\n\r\n  const handleResetFilters = () => {\r\n    setSearch(\"\");\r\n    setPersonsFilter([]);\r\n    setFilters({\r\n      insuranceId: \"\",\r\n      serviceTypeId: \"\",\r\n      companyId: \"\"\r\n    });\r\n    loadLimits(1, \"\", [], {\r\n      insuranceId: \"\",\r\n      serviceTypeId: \"\",\r\n      companyId: \"\"\r\n    }, sortField, sortDirection);\r\n  };\r\n\r\n  // Função para lidar com a mudança de página\r\n  const handlePageChange = (page) => {\r\n    console.log(`Mudando para a página ${page}`);\r\n    // Chamamos loadLimits com a nova página\r\n    loadLimits(page, search, personsFilter, filters, sortField, sortDirection);\r\n  };\r\n\r\n  // Função para lidar com a mudança de ordenação\r\n  const handleSortChange = (field, direction) => {\r\n    console.log(`Alterando ordenação: campo=${field}, direção=${direction}`, {\r\n      tipoField: typeof field,\r\n      tipoDirection: typeof direction,\r\n      valorField: field,\r\n      valorDirection: direction\r\n    });\r\n\r\n    // Garantir que a direção seja uma string válida\r\n    const validDirection = direction && ['asc', 'desc'].includes(direction.toLowerCase())\r\n      ? direction.toLowerCase()\r\n      : 'asc';\r\n\r\n    console.log(`Direção normalizada: ${validDirection}`);\r\n\r\n    setSortField(field);\r\n    setSortDirection(validDirection);\r\n    loadLimits(currentPage, search, personsFilter, filters, field, validDirection);\r\n  };\r\n\r\n  const handleEditLimit = (limit) => {\r\n    setSelectedLimit(limit);\r\n    setLimitFormOpen(true);\r\n  };\r\n\r\n  const handleDeleteLimit = (limit) => {\r\n    setSelectedLimit(limit);\r\n    setConfirmationDialogOpen(true);\r\n  };\r\n\r\n  const confirmDeleteLimit = async () => {\r\n    try {\r\n      await insuranceServiceLimitService.deleteLimit(selectedLimit.id);\r\n      toast_success(\"Limite de convênio excluído com sucesso\");\r\n      loadLimits();\r\n      setConfirmationDialogOpen(false);\r\n    } catch (error) {\r\n      console.error(\"Erro ao excluir limite de convênio:\", error);\r\n      toast_error(\"Erro ao excluir limite de convênio\");\r\n    }\r\n  };\r\n\r\n  const handleExport = async (format) => {\r\n    setIsExporting(true);\r\n    try {\r\n      // Encontrar os nomes dos filtros selecionados para o subtítulo da exportação\r\n      let insuranceName, serviceTypeName;\r\n\r\n      if (filters.insuranceId) {\r\n        const selectedInsurance = insurances.find(i => i.id === filters.insuranceId);\r\n        insuranceName = selectedInsurance ? selectedInsurance.name : undefined;\r\n      }\r\n\r\n      if (filters.serviceTypeId) {\r\n        const selectedServiceType = serviceTypes.find(s => s.id === filters.serviceTypeId);\r\n        serviceTypeName = selectedServiceType ? selectedServiceType.name : undefined;\r\n      }\r\n\r\n      // Exportar usando os mesmos filtros da tabela atual\r\n      const success = await insuranceServiceLimitService.exportInsuranceLimits({\r\n        search: search || undefined,\r\n        personIds: personsFilter.length > 0 ? personsFilter : undefined,\r\n        insuranceId: filters.insuranceId || undefined,\r\n        insuranceName,\r\n        serviceTypeId: filters.serviceTypeId || undefined,\r\n        serviceTypeName,\r\n        companyId: filters.companyId || undefined,\r\n        sortField,\r\n        sortDirection\r\n      }, format);\r\n\r\n      if (success) {\r\n        toast_success(`Dados exportados com sucesso no formato ${format.toUpperCase()}`);\r\n      } else {\r\n        toast_error(\"Erro ao exportar dados\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar dados:\", error);\r\n      toast_error(\"Erro ao exportar dados\");\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Título e botões de exportar e adicionar */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h1 className=\"text-2xl font-bold text-slate-800 dark:text-white flex items-center\">\r\n          <CreditCard size={24} className=\"mr-2 text-orange-600 dark:text-orange-400\" />\r\n          Limites de Convênio\r\n        </h1>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n          {/* Botão de exportar */}\r\n          <ExportMenu\r\n            onExport={handleExport}\r\n            isExporting={isExporting}\r\n            disabled={isLoading || limits.length === 0}\r\n            className=\"text-orange-700 dark:text-orange-300\"\r\n          />\r\n\r\n          {/* Botão de adicionar */}\r\n          <button\r\n            onClick={() => {\r\n              setSelectedLimit(null);\r\n              setLimitFormOpen(true);\r\n            }}\r\n            className=\"flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-orange-500 to-amber-500 dark:from-orange-600 dark:to-amber-600 text-white rounded-lg hover:from-orange-600 hover:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 shadow-md transition-all\"\r\n          >\r\n            <Plus size={18} />\r\n            <span className=\"font-medium\">Novo Limite</span>\r\n          </button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Cabeçalho e filtros da página */}\r\n      <ModuleHeader\r\n        title=\"Filtros\"\r\n        icon={<Filter size={22} className=\"text-module-people-icon dark:text-module-people-icon-dark\" />}\r\n        description=\"Configure os limites de utilização de serviços por convênio para cada paciente. Utilize os filtros abaixo para encontrar limites específicos.\"\r\n        tutorialSteps={insuranceLimitsTutorialSteps}\r\n        tutorialName=\"insurance-limits-overview\"\r\n        moduleColor=\"people\"\r\n        filters={\r\n          <form\r\n            onSubmit={handleSearch}\r\n            className=\"flex flex-col gap-4\"\r\n          >\r\n            <div className=\"flex flex-col md:flex-row gap-4\">\r\n              <div className=\"flex-1 relative\">\r\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-5 w-5\" />\r\n                <ModuleInput\r\n                  moduleColor=\"people\"\r\n                  type=\"text\"\r\n                  placeholder=\"Buscar por nome do paciente ou convênio...\"\r\n                  value={search}\r\n                  onChange={(e) => setSearch(e.target.value)}\r\n                  className=\"w-full pl-10\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"flex flex-col sm:flex-row gap-2\">\r\n                {/* Filtro de empresa (apenas para system_admin) */}\r\n                {isSystemAdmin && (\r\n                  <div className=\"w-full sm:w-48\">\r\n                    <ModuleSelect\r\n                      moduleColor=\"people\"\r\n                      value={filters.companyId}\r\n                      onChange={(e) => handleFilterChange(\"companyId\", e.target.value)}\r\n                      placeholder=\"Empresa\"\r\n                      disabled={isLoadingCompanies}\r\n                    >\r\n                      <option value=\"\">Todas as empresas</option>\r\n                      {companies.map((company) => (\r\n                        <option key={company.id} value={company.id}>\r\n                          {company.name}\r\n                        </option>\r\n                      ))}\r\n                    </ModuleSelect>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Filtro de Convênio */}\r\n                <div className=\"w-full sm:w-48\">\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    value={filters.insuranceId}\r\n                    onChange={(e) => handleFilterChange(\"insuranceId\", e.target.value)}\r\n                    placeholder=\"Convênios\"\r\n                    disabled={isLoadingFilters}\r\n                  >\r\n                    <option value=\"\">Todos os Convênios</option>\r\n                    {Array.isArray(insurances) && insurances.map((insurance) => (\r\n                      <option key={insurance?.id} value={insurance?.id}>\r\n                        {insurance?.name || 'Sem nome'}\r\n                      </option>\r\n                    ))}\r\n                  </ModuleSelect>\r\n                </div>\r\n\r\n                {/* Filtro de Tipo de Serviço */}\r\n                <div className=\"w-full sm:w-48\">\r\n                  <ModuleSelect\r\n                    moduleColor=\"people\"\r\n                    value={filters.serviceTypeId}\r\n                    onChange={(e) => handleFilterChange(\"serviceTypeId\", e.target.value)}\r\n                    placeholder=\"Tipos de Serviço\"\r\n                    disabled={isLoadingFilters}\r\n                  >\r\n                    <option value=\"\">Todos os Tipos de Serviço</option>\r\n                    {Array.isArray(serviceTypes) && serviceTypes.map((serviceType) => (\r\n                      <option key={serviceType?.id} value={serviceType?.id}>\r\n                        {serviceType?.name || 'Sem nome'}\r\n                      </option>\r\n                    ))}\r\n                  </ModuleSelect>\r\n                </div>\r\n\r\n                <FilterButton type=\"submit\" moduleColor=\"people\" variant=\"primary\">\r\n                  <Filter size={16} className=\"sm:hidden\" />\r\n                  <span className=\"hidden sm:inline\">Filtrar</span>\r\n                </FilterButton>\r\n\r\n                <FilterButton\r\n                  type=\"button\"\r\n                  onClick={handleResetFilters}\r\n                  moduleColor=\"people\"\r\n                  variant=\"secondary\"\r\n                >\r\n                  <RefreshCw size={16} className=\"sm:hidden\" />\r\n                  <span className=\"hidden sm:inline\">Limpar</span>\r\n                </FilterButton>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Multi-select para filtrar por múltiplos pacientes */}\r\n            <div className=\"w-full\">\r\n              <MultiSelect\r\n                label=\"Filtrar por Pacientes\"\r\n                value={personsFilter}\r\n                onChange={handlePersonsFilterChange}\r\n                options={personOptions}\r\n                placeholder=\"Selecione um ou mais pacientes pelo nome...\"\r\n                loading={isLoadingPersonOptions}\r\n                moduleOverride=\"people\"\r\n              />\r\n            </div>\r\n          </form>\r\n      }\r\n      />\r\n\r\n      {/* Tabela de Limites de Convênio */}\r\n      <ModuleTable\r\n        moduleColor=\"people\"\r\n        columns={[\r\n          {\r\n            header: 'Paciente',\r\n            field: 'patient',\r\n            width: '25%',\r\n            // Função de acesso personalizada para ordenação\r\n            accessor: (limit) => limit?.Person?.fullName || limit?.person?.fullName || ''\r\n          },\r\n          {\r\n            header: 'Convênio',\r\n            field: 'insurance',\r\n            width: '20%',\r\n            accessor: (limit) => limit?.Insurance?.name || limit?.insurance?.name || ''\r\n          },\r\n          {\r\n            header: 'Tipo de Serviço',\r\n            field: 'serviceType',\r\n            width: '25%',\r\n            accessor: (limit) => limit?.ServiceType?.name || limit?.serviceType?.name || ''\r\n          },\r\n          { header: 'Limite Mensal', field: 'monthlyLimit', width: '15%' },\r\n          { header: 'Ações', field: 'actions', className: 'text-right', width: '15%', sortable: false }\r\n        ]}\r\n        data={limits} // Já contém apenas os 10 itens da página atual\r\n        isLoading={isLoading}\r\n        emptyMessage=\"Nenhum limite de convênio encontrado\"\r\n        emptyIcon={<Shield size={24} />}\r\n        tableId=\"people-insurance-limits-table\"\r\n        enableColumnToggle={true}\r\n        defaultSortField=\"patient\"\r\n        defaultSortDirection=\"asc\"\r\n        currentPage={currentPage}\r\n        totalPages={totalPages}\r\n        totalItems={totalItems}\r\n        onPageChange={handlePageChange}\r\n        onSort={handleSortChange}\r\n        sortField={sortField}\r\n        sortDirection={sortDirection}\r\n        showPagination={true}\r\n        itemsPerPage={ITEMS_PER_PAGE}\r\n        renderRow={(limit, index, moduleColors, visibleColumns) => (\r\n          <tr key={limit.id} className={moduleColors.hoverBg}>\r\n            {visibleColumns.includes('patient') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <div className=\"flex-shrink-0 h-10 w-10 bg-orange-100 dark:bg-orange-900/30 text-orange-600 dark:text-orange-400 rounded-full flex items-center justify-center\">\r\n                    <User size={20} />\r\n                  </div>\r\n                  <div className=\"ml-4\">\r\n                    <div className=\"text-sm font-medium text-neutral-900 dark:text-neutral-100\">\r\n                      {limit?.Person?.fullName || limit?.person?.fullName || \"N/A\"}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('insurance') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <CreditCard className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\" />\r\n                  <span className=\"text-neutral-700 dark:text-neutral-300 truncate\">\r\n                    {limit?.Insurance?.name || limit?.insurance?.name || \"N/A\"}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('serviceType') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <Tag className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\" />\r\n                  <span className=\"text-neutral-700 dark:text-neutral-300 truncate\">\r\n                    {limit?.ServiceType?.name || limit?.serviceType?.name || \"N/A\"}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('monthlyLimit') && (\r\n              <td className=\"px-4 py-4\">\r\n                <div className=\"flex items-center\">\r\n                  <Clock className=\"h-4 w-4 text-neutral-400 dark:text-neutral-500 mr-1 flex-shrink-0\" />\r\n                  <span className=\"text-neutral-700 dark:text-neutral-300\">\r\n                    {limit?.monthlyLimit > 0 ? `${limit.monthlyLimit}x por mês` : \"Ilimitado\"}\r\n                  </span>\r\n                </div>\r\n              </td>\r\n            )}\r\n\r\n            {visibleColumns.includes('actions') && (\r\n              <td className=\"px-4 py-4 text-right\">\r\n                <div className=\"flex items-center justify-end gap-2\">\r\n                  <button\r\n                    onClick={() => handleEditLimit(limit)}\r\n                    className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-orange-500 dark:hover:text-orange-400 transition-colors\"\r\n                    title=\"Editar\"\r\n                  >\r\n                    <Edit size={16} />\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleDeleteLimit(limit)}\r\n                    className=\"p-1 text-neutral-500 dark:text-neutral-400 hover:text-red-500 dark:hover:text-red-400 transition-colors\"\r\n                    title=\"Excluir\"\r\n                  >\r\n                    <Trash size={16} />\r\n                  </button>\r\n                </div>\r\n              </td>\r\n            )}\r\n          </tr>\r\n        )}\r\n      />\r\n\r\n      {/* Confirmation Dialog */}\r\n      <ConfirmationDialog\r\n        isOpen={confirmationDialogOpen}\r\n        onClose={() => setConfirmationDialogOpen(false)}\r\n        onConfirm={confirmDeleteLimit}\r\n        title=\"Excluir Limite de Convênio\"\r\n        message={`Tem certeza que deseja excluir o limite para ${selectedLimit?.Person?.fullName || selectedLimit?.person?.fullName || 'este paciente'} com o convênio ${selectedLimit?.Insurance?.name || selectedLimit?.insurance?.name || 'selecionado'}?`}\r\n        variant=\"danger\"\r\n        confirmText=\"Excluir\"\r\n        cancelText=\"Cancelar\"\r\n      />\r\n\r\n      {/* Limit Form Modal */}\r\n      {limitFormOpen && (\r\n        <InsuranceLimitFormModal\r\n          isOpen={limitFormOpen}\r\n          onClose={() => setLimitFormOpen(false)}\r\n          limit={selectedLimit}\r\n          onSuccess={() => {\r\n            setLimitFormOpen(false);\r\n            loadLimits();\r\n          }}\r\n        />\r\n      )}\r\n\r\n      {/* Gerenciador de tutorial */}\r\n      <TutorialManager />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InsuranceLimitsPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAcA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA;AAAA;AAAA;AAAA;AAFA;AAAA;AAEA;AAFA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;;;;;;;;;;;AAgCA,sDAAsD;AACtD,MAAM,+BAA+B;IACnC;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;IACA;QACE,OAAO;QACP,SAAS;QACT,UAAU;QACV,UAAU;IACZ;CACD;AAED,MAAM,sBAAsB;;IAC1B,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACpC,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD;IACxD,MAAM,UAAU,aAAa,SAAS,WAAW,aAAa,SAAS;IACvE,MAAM,gBAAgB,aAAa,SAAS;IAC5C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,aAAa;QACb,eAAe;QACf,WAAW;IACb;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IAC7C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,yBAAyB;IACzB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,wBAAwB;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,+DAA+D;IAC/D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACpC,0BAA0B;YAC1B,IAAI;gBACF,qEAAqE;gBACrE,MAAM,WAAW,MAAM,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAC/C,OAAO;oBACP,QAAQ,KAAK,qCAAqC;gBACpD;gBAEA,yEAAyE;gBACzE,MAAM,eAAe,UAAU,WAAW,UAAU,UAAU,EAAE;gBAEhE,MAAM,UAAU,aAAa,GAAG;0EAAC,CAAA,SAAU,CAAC;4BAC1C,OAAO,OAAO,EAAE;4BAChB,OAAO,OAAO,QAAQ;4BACtB,gCAAgC;4BAChC,UAAU,OAAO,QAAQ,GAAG,OAAO,QAAQ,CAAC,WAAW,KAAK;wBAC9D,CAAC;4EAAM,EAAE;gBAET,8CAA8C;gBAC9C,MAAM,gBAAgB,QAAQ,IAAI;wFAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,EAAE,SAAS;4BAAE,aAAa;wBAAO;;gBAEjH,QAAQ,GAAG,CAAC,kCAAkC;gBAC9C,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yCAAyC;gBACvD,iBAAiB,EAAE;YACrB,SAAU;gBACR,0BAA0B;YAC5B;QACF;6DAAG,EAAE;IAEL,2DAA2D;IAC3D,MAAM,gBAAgB;QACpB,IAAI,CAAC,eAAe;QAEpB,sBAAsB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,+JAAA,CAAA,iBAAc,CAAC,qBAAqB;YAC3D,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR;YACA;YACA;YACA,kDAAkD;YAClD,IAAI,eAAe;gBACjB;YACF;QACF;wCAAG;QAAC;QAAmB;KAAc;IAErC,kCAAkC;IAClC,MAAM,iBAAiB;IAEvB,+BAA+B;IAC/B,MAAM,aAAa,OACjB,OAAO,WAAW,EAClB,cAAc,MAAM,EACpB,YAAY,aAAa,EACzB,gBAAgB,OAAO,EACvB,OAAO,aAAa,SAAS,EAC7B,YAAY,iBAAiB,KAAK;QAElC,aAAa;QACb,IAAI;YACF,gDAAgD;YAChD,MAAM,iBAAiB,aAAa;gBAAC;gBAAO;aAAO,CAAC,QAAQ,CAAC,UAAU,WAAW,MAC9E,UAAU,WAAW,KACrB;YAEJ,oCAAoC;YACpC,MAAM,aAAa,SAAS,MAAM;YAElC,qCAAqC;YACrC,eAAe;YAEf,iEAAiE;YACjE,MAAM,WAAW,MAAM,8KAAA,CAAA,+BAA4B,CAAC,YAAY,CAAC;gBAC/D,QAAQ,eAAe;gBACvB,WAAW,UAAU,MAAM,GAAG,IAAI,YAAY;gBAC9C,GAAG,aAAa;gBAChB,WAAW,QAAQ;gBACnB,eAAe;YACjB;YAEA,QAAQ,GAAG,CAAC,uCAAuC;gBACjD,QAAQ,eAAe;gBACvB,WAAW,UAAU,MAAM,GAAG,IAAI,YAAY;gBAC9C,GAAG,aAAa;gBAChB,WAAW,QAAQ;gBACnB,eAAe;YACjB;YAEA,kDAAkD;YAClD,IAAI,YAAY,OAAO,aAAa,UAAU;gBAC5C,2BAA2B;gBAC3B,IAAI,YAAY,SAAS,MAAM,IAAI,EAAE;gBAErC,gFAAgF;gBAChF,4CAA4C;gBAC5C,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,QAAQ,UAAU,UAAU,EAAE,gBAAgB;gBAEvF,sCAAsC;gBACtC,MAAM,QAAQ,UAAU,MAAM;gBAC9B,MAAM,QAAQ,KAAK,IAAI,CAAC,QAAQ,mBAAmB;gBAEnD,8CAA8C;gBAC9C,MAAM,aAAa,CAAC,aAAa,CAAC,IAAI;gBACtC,MAAM,WAAW,aAAa;gBAC9B,MAAM,kBAAkB,UAAU,KAAK,CAAC,YAAY;gBAEpD,uCAAuC;gBACvC,IAAI,aAAa,KAAK,gBAAgB,MAAM,KAAK,KAAK,UAAU,MAAM,GAAG,GAAG;oBAC1E,wEAAwE;oBACxE,gCAAgC;oBAChC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,WAAW,qCAAqC,CAAC;oBACvE,eAAe;oBACf,MAAM,kBAAkB,UAAU,KAAK,CAAC,GAAG;oBAC3C,UAAU;gBACZ,OAAO;oBACL,wDAAwD;oBACxD,UAAU,kBAAkB,qCAAqC;gBACnE;gBAEA,cAAc;gBACd,cAAc;gBAEd,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,WAAW,WAAW,EAAE,gBAAgB,MAAM,CAAC,QAAQ,EAAE,aAAW,EAAE,CAAC,EAAE,KAAK,GAAG,CAAC,UAAU,OAAO,KAAK,EAAE,MAAM,MAAM,CAAC;YACjK,OAAO;gBACL,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,UAAU,EAAE;gBACZ,cAAc;gBACd,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,YAAY;YACZ,UAAU,EAAE;YACZ,cAAc;YACd,cAAc;QAChB,SAAU;YACR,aAAa;QACf;IACF;IAEA,kCAAkC;IAClC,MAAM,oBAAoB;QACxB,oBAAoB;QACpB,IAAI;YACF,mBAAmB;YACnB,IAAI;gBACF,MAAM,cAAc,MAAM,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAI;gBACjE,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,qCAAqC;gBACrC,IAAI,eAAe,OAAO,gBAAgB,UAAU;oBAClD,0EAA0E;oBAC1E,MAAM,eAAe,MAAM,OAAO,CAAC,eAAe,cAC9B,YAAY,OAAO,GAAG,YAAY,OAAO,GACzC,YAAY,MAAM,GAAG,YAAY,MAAM,GACvC,YAAY,IAAI,GAAG,YAAY,IAAI,GAAG,EAAE;oBAE5D,QAAQ,GAAG,CAAC,8BAA8B;oBAC1C,0CAA0C;oBAC1C,WAAW,MAAM,OAAO,CAAC,gBAAgB,eAAe,EAAE;gBAC5D,OAAO;oBACL,QAAQ,KAAK,CAAC,+BAA+B;oBAC7C,WAAW,EAAE;gBACf;YACF,EAAE,OAAO,aAAa;gBACpB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,WAAW,EAAE;YACf;YAEA,qBAAqB;YACrB,IAAI;gBACF,MAAM,iBAAiB,MAAM,mKAAA,CAAA,oBAAiB,CAAC,aAAa;gBAC5D,QAAQ,GAAG,CAAC,iCAAiC;gBAE7C,qCAAqC;gBACrC,IAAI,kBAAkB,OAAO,mBAAmB,UAAU;oBACxD,4EAA4E;oBAC5E,MAAM,kBAAkB,MAAM,OAAO,CAAC,kBAAkB,iBACjC,eAAe,UAAU,GAAG,eAAe,UAAU,GACrD,eAAe,IAAI,GAAG,eAAe,IAAI,GAAG,EAAE;oBAErE,QAAQ,GAAG,CAAC,gCAAgC;oBAC5C,0CAA0C;oBAC1C,cAAc,MAAM,OAAO,CAAC,mBAAmB,kBAAkB,EAAE;gBACrE,OAAO;oBACL,QAAQ,KAAK,CAAC,iCAAiC;oBAC/C,cAAc,EAAE;gBAClB;YACF,EAAE,OAAO,gBAAgB;gBACvB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,cAAc,EAAE;YAClB;YAEA,4BAA4B;YAC5B,IAAI;gBACF,MAAM,mBAAmB,MAAM,uKAAA,CAAA,qBAAkB,CAAC,eAAe;gBACjE,QAAQ,GAAG,CAAC,wCAAwC;gBAEpD,qCAAqC;gBACrC,IAAI,oBAAoB,OAAO,qBAAqB,UAAU;oBAC5D,mFAAmF;oBACnF,MAAM,oBAAoB,MAAM,OAAO,CAAC,oBAAoB,mBACnC,iBAAiB,YAAY,GAAG,iBAAiB,YAAY,GAC7D,iBAAiB,IAAI,GAAG,iBAAiB,IAAI,GAAG,EAAE;oBAE3E,QAAQ,GAAG,CAAC,uCAAuC;oBACnD,0CAA0C;oBAC1C,gBAAgB,MAAM,OAAO,CAAC,qBAAqB,oBAAoB,EAAE;gBAC3E,OAAO;oBACL,QAAQ,KAAK,CAAC,wCAAwC;oBACtD,gBAAgB,EAAE;gBACpB;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,KAAK,CAAC,sCAAsC;gBACpD,gBAAgB,EAAE;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,YAAY;QACd,SAAU;YACR,oBAAoB;QACtB;IACF;IAEA,2BAA2B;IAC3B,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,WAAW,GAAG,QAAQ,eAAe,SAAS,WAAW;IAC3D;IAEA,MAAM,qBAAqB,CAAC,OAAO;QACjC,MAAM,aAAa;YAAE,GAAG,OAAO;YAAE,CAAC,MAAM,EAAE;QAAM;QAChD,WAAW;QACX,WAAW,GAAG,QAAQ,eAAe,YAAY,WAAW;IAC9D;IAEA,MAAM,4BAA4B,CAAC;QACjC,iBAAiB;QACjB,WAAW,GAAG,QAAQ,OAAO,SAAS,WAAW;IACnD;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,iBAAiB,EAAE;QACnB,WAAW;YACT,aAAa;YACb,eAAe;YACf,WAAW;QACb;QACA,WAAW,GAAG,IAAI,EAAE,EAAE;YACpB,aAAa;YACb,eAAe;YACf,WAAW;QACb,GAAG,WAAW;IAChB;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM;QAC3C,wCAAwC;QACxC,WAAW,MAAM,QAAQ,eAAe,SAAS,WAAW;IAC9D;IAEA,+CAA+C;IAC/C,MAAM,mBAAmB,CAAC,OAAO;QAC/B,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,MAAM,UAAU,EAAE,WAAW,EAAE;YACvE,WAAW,OAAO;YAClB,eAAe,OAAO;YACtB,YAAY;YACZ,gBAAgB;QAClB;QAEA,gDAAgD;QAChD,MAAM,iBAAiB,aAAa;YAAC;YAAO;SAAO,CAAC,QAAQ,CAAC,UAAU,WAAW,MAC9E,UAAU,WAAW,KACrB;QAEJ,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,gBAAgB;QAEpD,aAAa;QACb,iBAAiB;QACjB,WAAW,aAAa,QAAQ,eAAe,SAAS,OAAO;IACjE;IAEA,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,0BAA0B;IAC5B;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,8KAAA,CAAA,+BAA4B,CAAC,WAAW,CAAC,cAAc,EAAE;YAC/D,cAAc;YACd;YACA,0BAA0B;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,YAAY;QACd;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,6EAA6E;YAC7E,IAAI,eAAe;YAEnB,IAAI,QAAQ,WAAW,EAAE;gBACvB,MAAM,oBAAoB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,WAAW;gBAC3E,gBAAgB,oBAAoB,kBAAkB,IAAI,GAAG;YAC/D;YAEA,IAAI,QAAQ,aAAa,EAAE;gBACzB,MAAM,sBAAsB,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ,aAAa;gBACjF,kBAAkB,sBAAsB,oBAAoB,IAAI,GAAG;YACrE;YAEA,oDAAoD;YACpD,MAAM,UAAU,MAAM,8KAAA,CAAA,+BAA4B,CAAC,qBAAqB,CAAC;gBACvE,QAAQ,UAAU;gBAClB,WAAW,cAAc,MAAM,GAAG,IAAI,gBAAgB;gBACtD,aAAa,QAAQ,WAAW,IAAI;gBACpC;gBACA,eAAe,QAAQ,aAAa,IAAI;gBACxC;gBACA,WAAW,QAAQ,SAAS,IAAI;gBAChC;gBACA;YACF,GAAG;YAEH,IAAI,SAAS;gBACX,cAAc,CAAC,wCAAwC,EAAE,OAAO,WAAW,IAAI;YACjF,OAAO;gBACL,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,YAAY;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAIA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,qNAAA,CAAA,aAAU;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAA8C;;;;;;;kCAIhF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,wIAAA,CAAA,UAAU;gCACT,UAAU;gCACV,aAAa;gCACb,UAAU,aAAa,OAAO,MAAM,KAAK;gCACzC,WAAU;;;;;;0CAIZ,6LAAC;gCACC,SAAS;oCACP,iBAAiB;oCACjB,iBAAiB;gCACnB;gCACA,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAMpC,6LAAC,0IAAA,CAAA,UAAY;gBACX,OAAM;gBACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;oBAAC,MAAM;oBAAI,WAAU;;;;;;gBAClC,aAAY;gBACZ,eAAe;gBACf,cAAa;gBACb,aAAY;gBACZ,uBACE,6LAAC;oBACC,UAAU;oBACV,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,mLAAA,CAAA,cAAW;4CACV,aAAY;4CACZ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;;;;;;;;;;;8CAId,6LAAC;oCAAI,WAAU;;wCAEZ,+BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO,QAAQ,SAAS;gDACxB,UAAU,CAAC,IAAM,mBAAmB,aAAa,EAAE,MAAM,CAAC,KAAK;gDAC/D,aAAY;gDACZ,UAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,UAAU,GAAG,CAAC,CAAC,wBACd,6LAAC;4DAAwB,OAAO,QAAQ,EAAE;sEACvC,QAAQ,IAAI;2DADF,QAAQ,EAAE;;;;;;;;;;;;;;;;sDAS/B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO,QAAQ,WAAW;gDAC1B,UAAU,CAAC,IAAM,mBAAmB,eAAe,EAAE,MAAM,CAAC,KAAK;gDACjE,aAAY;gDACZ,UAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,MAAM,OAAO,CAAC,eAAe,WAAW,GAAG,CAAC,CAAC,0BAC5C,6LAAC;4DAA2B,OAAO,WAAW;sEAC3C,WAAW,QAAQ;2DADT,WAAW;;;;;;;;;;;;;;;;sDAQ9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qLAAA,CAAA,eAAY;gDACX,aAAY;gDACZ,OAAO,QAAQ,aAAa;gDAC5B,UAAU,CAAC,IAAM,mBAAmB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gDACnE,aAAY;gDACZ,UAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,MAAM,OAAO,CAAC,iBAAiB,aAAa,GAAG,CAAC,CAAC,4BAChD,6LAAC;4DAA6B,OAAO,aAAa;sEAC/C,aAAa,QAAQ;2DADX,aAAa;;;;;;;;;;;;;;;;sDAOhC,6LAAC,0IAAA,CAAA,eAAY;4CAAC,MAAK;4CAAS,aAAY;4CAAS,SAAQ;;8DACvD,6LAAC,yMAAA,CAAA,SAAM;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC5B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;sDAGrC,6LAAC,0IAAA,CAAA,eAAY;4CACX,MAAK;4CACL,SAAS;4CACT,aAAY;4CACZ,SAAQ;;8DAER,6LAAC,mNAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAU;;;;;;8DAC/B,6LAAC;oDAAK,WAAU;8DAAmB;;;;;;;;;;;;;;;;;;;;;;;;sCAMzC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6IAAA,CAAA,UAAW;gCACV,OAAM;gCACN,OAAO;gCACP,UAAU;gCACV,SAAS;gCACT,aAAY;gCACZ,SAAS;gCACT,gBAAe;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6LAAC,mLAAA,CAAA,cAAW;gBACV,aAAY;gBACZ,SAAS;oBACP;wBACE,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,gDAAgD;wBAChD,UAAU,CAAC,QAAU,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;oBAC7E;oBACA;wBACE,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,UAAU,CAAC,QAAU,OAAO,WAAW,QAAQ,OAAO,WAAW,QAAQ;oBAC3E;oBACA;wBACE,QAAQ;wBACR,OAAO;wBACP,OAAO;wBACP,UAAU,CAAC,QAAU,OAAO,aAAa,QAAQ,OAAO,aAAa,QAAQ;oBAC/E;oBACA;wBAAE,QAAQ;wBAAiB,OAAO;wBAAgB,OAAO;oBAAM;oBAC/D;wBAAE,QAAQ;wBAAS,OAAO;wBAAW,WAAW;wBAAc,OAAO;wBAAO,UAAU;oBAAM;iBAC7F;gBACD,MAAM;gBACN,WAAW;gBACX,cAAa;gBACb,yBAAW,6LAAC,yMAAA,CAAA,SAAM;oBAAC,MAAM;;;;;;gBACzB,SAAQ;gBACR,oBAAoB;gBACpB,kBAAiB;gBACjB,sBAAqB;gBACrB,aAAa;gBACb,YAAY;gBACZ,YAAY;gBACZ,cAAc;gBACd,QAAQ;gBACR,WAAW;gBACX,eAAe;gBACf,gBAAgB;gBAChB,cAAc;gBACd,WAAW,CAAC,OAAO,OAAO,cAAc,+BACtC,6LAAC;wBAAkB,WAAW,aAAa,OAAO;;4BAC/C,eAAe,QAAQ,CAAC,4BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACZ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;4BAOhE,eAAe,QAAQ,CAAC,8BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAK,WAAU;sDACb,OAAO,WAAW,QAAQ,OAAO,WAAW,QAAQ;;;;;;;;;;;;;;;;;4BAM5D,eAAe,QAAQ,CAAC,gCACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDACb,OAAO,aAAa,QAAQ,OAAO,aAAa,QAAQ;;;;;;;;;;;;;;;;;4BAMhE,eAAe,QAAQ,CAAC,iCACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDACb,OAAO,eAAe,IAAI,GAAG,MAAM,YAAY,CAAC,SAAS,CAAC,GAAG;;;;;;;;;;;;;;;;;4BAMrE,eAAe,QAAQ,CAAC,4BACvB,6LAAC;gCAAG,WAAU;0CACZ,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,8MAAA,CAAA,OAAI;gDAAC,MAAM;;;;;;;;;;;sDAEd,6LAAC;4CACC,SAAS,IAAM,kBAAkB;4CACjC,WAAU;4CACV,OAAM;sDAEN,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;uBAhEd,MAAM,EAAE;;;;;;;;;;0BA0ErB,6LAAC,gJAAA,CAAA,UAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,WAAW;gBACX,OAAM;gBACN,SAAS,CAAC,6CAA6C,EAAE,eAAe,QAAQ,YAAY,eAAe,QAAQ,YAAY,gBAAgB,gBAAgB,EAAE,eAAe,WAAW,QAAQ,eAAe,WAAW,QAAQ,cAAc,CAAC,CAAC;gBACrP,SAAQ;gBACR,aAAY;gBACZ,YAAW;;;;;;YAIZ,+BACC,6LAAC,yJAAA,CAAA,UAAuB;gBACtB,QAAQ;gBACR,SAAS,IAAM,iBAAiB;gBAChC,OAAO;gBACP,WAAW;oBACT,iBAAiB;oBACjB;gBACF;;;;;;0BAKJ,6LAAC,mJAAA,CAAA,UAAe;;;;;;;;;;;AAGtB;GAnrBM;;QAC0B,iIAAA,CAAA,UAAO;QACY,kIAAA,CAAA,WAAQ;;;KAFrD;uCAqrBS"}}, {"offset": {"line": 4714, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4720, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/introduction/IntroductionPage.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport {\r\n  Users,\r\n  UserPlus,\r\n  CreditCard,\r\n  Shield,\r\n  Info,\r\n  Building,\r\n  Play,\r\n  Pause,\r\n  ArrowRight,\r\n  BarChart4,\r\n  PieChart,\r\n  LineChart,\r\n  Activity,\r\n  Mail,\r\n  Phone,\r\n  FileText,\r\n  User,\r\n  Calendar\r\n} from \"lucide-react\";\r\nimport { ModuleHeader } from \"@/components/ui\";\r\n\r\nconst IntroductionPage = () => {\r\n  const [isVideoPlaying, setIsVideoPlaying] = useState(false);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Título */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h1 className=\"text-2xl font-bold text-slate-800 dark:text-white flex items-center\">\r\n          <Info size={24} className=\"mr-2 text-orange-600 dark:text-orange-400\" />\r\n          Introdução\r\n        </h1>\r\n      </div>\r\n\r\n      {/* Main content */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl border border-module-people-border dark:border-gray-700 shadow-lg dark:shadow-black/30 overflow-hidden\">\r\n        {/* Header with gradient */}\r\n        <div className=\"bg-gradient-to-r from-orange-600 to-orange-400 dark:from-orange-700 dark:to-orange-600 px-6 py-4\">\r\n          <div className=\"flex items-center\">\r\n            <Users className=\"mr-3 text-white\" size={24} aria-hidden=\"true\" />\r\n            <h2 className=\"text-xl font-bold text-white\">Módulo de Pessoas</h2>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Introduction text and video */}\r\n        <div className=\"p-6\">\r\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8\">\r\n            <div>\r\n              <p className=\"text-gray-700 dark:text-gray-300 mb-6\">\r\n                Bem-vindo ao Módulo de Pessoas do High Tide Systems. Este módulo é o centro de gerenciamento de clientes, pacientes e convênios,\r\n                permitindo organizar todas as informações de forma eficiente.\r\n                Aqui você encontrará todas as ferramentas necessárias para gerenciar pessoas e seus relacionamentos.\r\n              </p>\r\n\r\n              {/* Key features */}\r\n              <div className=\"bg-orange-50 dark:bg-gray-700 rounded-lg p-4 border border-orange-200 dark:border-gray-600\">\r\n                <h3 className=\"font-semibold text-orange-800 dark:text-white mb-3 flex items-center\">\r\n                  <Activity className=\"mr-2 text-orange-600 dark:text-orange-300\" size={18} />\r\n                  Principais Recursos\r\n                </h3>\r\n                <ul className=\"space-y-2 text-sm text-gray-700 dark:text-gray-300\">\r\n                  <li className=\"flex items-start\">\r\n                    <span className=\"inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0\">1</span>\r\n                    <span>Cadastro completo de clientes e pacientes</span>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <span className=\"inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0\">2</span>\r\n                    <span>Gerenciamento de convênios e planos de saúde</span>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <span className=\"inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0\">3</span>\r\n                    <span>Controle de limites de serviços por convênio</span>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <span className=\"inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0\">4</span>\r\n                    <span>Relacionamento entre clientes e pacientes</span>\r\n                  </li>\r\n                  <li className=\"flex items-start\">\r\n                    <span className=\"inline-flex items-center justify-center w-5 h-5 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-2 flex-shrink-0\">5</span>\r\n                    <span>Gestão de documentos e informações de contato</span>\r\n                  </li>\r\n                </ul>\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              {/* Interactive demo/video placeholder */}\r\n              <div className=\"bg-gradient-to-r from-orange-700 to-orange-500 dark:from-orange-800 dark:to-orange-600 rounded-lg overflow-hidden shadow-lg h-80 relative\">\r\n                {isVideoPlaying ? (\r\n                  <div className=\"absolute inset-0 bg-black/80 flex items-center justify-center\">\r\n                    <button\r\n                      onClick={() => setIsVideoPlaying(false)}\r\n                      className=\"absolute top-4 right-4 bg-white/20 hover:bg-white/30 rounded-full p-2 text-white transition-colors\"\r\n                    >\r\n                      <Pause size={20} />\r\n                    </button>\r\n\r\n                    {/* Animation container */}\r\n                    <div className=\"w-full h-full flex items-center justify-center\">\r\n                      <div className=\"ai-video-content w-4/5 h-4/5 relative\">\r\n                        {/* People module overview animation */}\r\n                        <div className=\"absolute inset-0 flex flex-col items-center justify-center ai-slide\" data-slide=\"1\">\r\n                          <div className=\"text-white text-xl font-bold mb-6\">Módulo de Pessoas</div>\r\n                          <div className=\"flex space-x-8 mb-8\">\r\n                            <div className=\"flex flex-col items-center transition-all duration-500 hover:scale-110\">\r\n                              <div className=\"w-16 h-16 rounded-lg bg-orange-500/30 flex items-center justify-center mb-2\">\r\n                                <UserPlus size={32} className=\"text-orange-300\" />\r\n                              </div>\r\n                              <span className=\"text-orange-200 text-sm\">Clientes</span>\r\n                            </div>\r\n                            <div className=\"flex flex-col items-center transition-all duration-500 hover:scale-110\">\r\n                              <div className=\"w-16 h-16 rounded-lg bg-orange-500/30 flex items-center justify-center mb-2\">\r\n                                <Users size={32} className=\"text-orange-300\" />\r\n                              </div>\r\n                              <span className=\"text-orange-200 text-sm\">Pacientes</span>\r\n                            </div>\r\n                            <div className=\"flex flex-col items-center transition-all duration-500 hover:scale-110\">\r\n                              <div className=\"w-16 h-16 rounded-lg bg-orange-500/30 flex items-center justify-center mb-2\">\r\n                                <CreditCard size={32} className=\"text-orange-300\" />\r\n                              </div>\r\n                              <span className=\"text-orange-200 text-sm\">Convênios</span>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n\r\n                    <style jsx>{`\r\n                      .ai-video-content {\r\n                        position: relative;\r\n                        overflow: hidden;\r\n                      }\r\n                      .ai-slide {\r\n                        animation: fadeIn 0.5s ease-in-out;\r\n                      }\r\n                      @keyframes fadeIn {\r\n                        from { opacity: 0; transform: translateY(10px); }\r\n                        to { opacity: 1; transform: translateY(0); }\r\n                      }\r\n                    `}</style>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center p-6\">\r\n                    <button\r\n                      onClick={() => setIsVideoPlaying(true)}\r\n                      className=\"w-20 h-20 rounded-full bg-primary-500/20 flex items-center justify-center mx-auto mb-4 hover:bg-primary-500/30 transition-colors cursor-pointer\"\r\n                      aria-label=\"Iniciar demonstração\"\r\n                    >\r\n                      <Play size={36} className=\"text-primary-500 ml-1\" />\r\n                    </button>\r\n                    <p className=\"text-white text-sm mb-2\">Clique para iniciar a demonstração interativa</p>\r\n                    <p className=\"text-orange-200 text-xs\">Visualize as principais funcionalidades do módulo</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Section cards */}\r\n          <h3 className=\"text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-4 flex items-center\">\r\n            <Info className=\"mr-2 text-orange-500\" size={20} />\r\n            Seções do Módulo\r\n          </h3>\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mt-4\">\r\n            {/* Clients section */}\r\n            <div className=\"bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md\">\r\n              <div className=\"bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500\">\r\n                <div className=\"flex items-center\">\r\n                  <UserPlus className=\"mr-2 text-orange-600 dark:text-orange-300\" size={20} />\r\n                  <h3 className=\"font-semibold text-orange-800 dark:text-white\">Clientes</h3>\r\n                </div>\r\n              </div>\r\n              <div className=\"p-4\">\r\n                <div className=\"flex flex-col md:flex-row gap-4\">\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\r\n                      Gerencie o cadastro de clientes no sistema. Mantenha informações completas\r\n                      como dados pessoais, endereço, contatos e documentos.\r\n                    </p>\r\n                    <div className=\"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600\">\r\n                      <p className=\"text-xs text-slate-500 dark:text-slate-400\">\r\n                        <span className=\"font-semibold\">Funcionalidades:</span> Cadastro completo;\r\n                        Gestão de documentos; Histórico de atendimentos; Relacionamento com pacientes.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"md:w-1/3 flex items-center justify-center\">\r\n                    <div className=\"w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center\">\r\n                      <UserPlus size={32} className=\"text-orange-500 dark:text-orange-300\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Patients section */}\r\n            <div className=\"bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md\">\r\n              <div className=\"bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500\">\r\n                <div className=\"flex items-center\">\r\n                  <Users className=\"mr-2 text-orange-600 dark:text-orange-300\" size={20} />\r\n                  <h3 className=\"font-semibold text-orange-800 dark:text-white\">Pacientes</h3>\r\n                </div>\r\n              </div>\r\n              <div className=\"p-4\">\r\n                <div className=\"flex flex-col md:flex-row gap-4\">\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\r\n                      Cadastre e gerencie pacientes, vinculando-os aos clientes titulares.\r\n                      Mantenha um histórico completo de atendimentos e informações médicas.\r\n                    </p>\r\n                    <div className=\"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600\">\r\n                      <p className=\"text-xs text-slate-500 dark:text-slate-400\">\r\n                        <span className=\"font-semibold\">Funcionalidades:</span> Cadastro de pacientes;\r\n                        Vínculo com clientes; Histórico médico; Gestão de convênios.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"md:w-1/3 flex items-center justify-center\">\r\n                    <div className=\"w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center\">\r\n                      <Users size={32} className=\"text-orange-500 dark:text-orange-300\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Insurances section */}\r\n            <div className=\"bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md\">\r\n              <div className=\"bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500\">\r\n                <div className=\"flex items-center\">\r\n                  <CreditCard className=\"mr-2 text-orange-600 dark:text-orange-300\" size={20} />\r\n                  <h3 className=\"font-semibold text-orange-800 dark:text-white\">Convênios</h3>\r\n                </div>\r\n              </div>\r\n              <div className=\"p-4\">\r\n                <div className=\"flex flex-col md:flex-row gap-4\">\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\r\n                      Gerencie os convênios e planos de saúde disponíveis no sistema.\r\n                      Configure informações como operadora, tipo de plano e cobertura.\r\n                    </p>\r\n                    <div className=\"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600\">\r\n                      <p className=\"text-xs text-slate-500 dark:text-slate-400\">\r\n                        <span className=\"font-semibold\">Funcionalidades:</span> Cadastro de convênios;\r\n                        Configuração de planos; Vinculação com pacientes; Gestão de coberturas.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"md:w-1/3 flex items-center justify-center\">\r\n                    <div className=\"w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center\">\r\n                      <CreditCard size={32} className=\"text-orange-500 dark:text-orange-300\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n\r\n            {/* Insurance Limits section */}\r\n            <div className=\"bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 overflow-hidden shadow-md\">\r\n              <div className=\"bg-orange-100 dark:bg-gray-600 px-4 py-3 border-b border-orange-200 dark:border-gray-500\">\r\n                <div className=\"flex items-center\">\r\n                  <Shield className=\"mr-2 text-orange-600 dark:text-orange-300\" size={20} />\r\n                  <h3 className=\"font-semibold text-orange-800 dark:text-white\">Limites de Convênio</h3>\r\n                </div>\r\n              </div>\r\n              <div className=\"p-4\">\r\n                <div className=\"flex flex-col md:flex-row gap-4\">\r\n                  <div className=\"flex-1\">\r\n                    <p className=\"text-gray-600 dark:text-gray-300 text-sm\">\r\n                      Configure os limites de serviços disponíveis para cada convênio.\r\n                      Defina quantidades, valores e períodos de carência.\r\n                    </p>\r\n                    <div className=\"mt-3 bg-white dark:bg-gray-800 rounded p-3 border border-orange-200 dark:border-gray-600\">\r\n                      <p className=\"text-xs text-slate-500 dark:text-slate-400\">\r\n                        <span className=\"font-semibold\">Funcionalidades:</span> Configuração de limites;\r\n                        Definição de carências; Controle de utilização; Alertas de limite.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"md:w-1/3 flex items-center justify-center\">\r\n                    <div className=\"w-20 h-20 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center\">\r\n                      <Shield size={32} className=\"text-orange-500 dark:text-orange-300\" />\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Getting started section */}\r\n          <div className=\"mt-8 bg-orange-50 dark:bg-gray-700 rounded-lg border border-orange-200 dark:border-gray-600 p-6\">\r\n            <h3 className=\"text-lg font-semibold text-orange-800 dark:text-white mb-4 flex items-center\">\r\n              <Activity className=\"mr-2 text-orange-600 dark:text-orange-300\" size={20} />\r\n              Começando\r\n            </h3>\r\n            <p className=\"text-gray-600 dark:text-gray-300 mb-4\">\r\n              Para começar a utilizar o módulo de pessoas, recomendamos seguir estes passos:\r\n            </p>\r\n            <ol className=\"space-y-3 text-gray-600 dark:text-gray-300\">\r\n              <li className=\"flex items-start\">\r\n                <span className=\"inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0\">1</span>\r\n                <span>Cadastre os <strong>clientes</strong> com informações completas, incluindo documentos e contatos.</span>\r\n              </li>\r\n              <li className=\"flex items-start\">\r\n                <span className=\"inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0\">2</span>\r\n                <span>Registre os <strong>pacientes</strong> vinculados aos clientes, com seus dados pessoais e médicos.</span>\r\n              </li>\r\n              <li className=\"flex items-start\">\r\n                <span className=\"inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0\">3</span>\r\n                <span>Configure os <strong>convênios</strong> disponíveis no sistema.</span>\r\n              </li>\r\n              <li className=\"flex items-start\">\r\n                <span className=\"inline-flex items-center justify-center w-6 h-6 rounded-full bg-orange-100 dark:bg-orange-900 text-orange-600 dark:text-orange-300 mr-3 flex-shrink-0\">4</span>\r\n                <span>Defina os <strong>limites de convênio</strong> para cada tipo de serviço oferecido.</span>\r\n              </li>\r\n            </ol>\r\n            <div className=\"mt-6 flex justify-center\">\r\n              <button\r\n                onClick={() => window.location.href = '/dashboard/people/clients'}\r\n                className=\"px-5 py-2.5 bg-orange-600 hover:bg-orange-700 text-white rounded-lg shadow transition-colors flex items-center gap-2\"\r\n              >\r\n                <UserPlus size={18} />\r\n                Ir para Clientes\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default IntroductionPage;\r\n"], "names": [], "mappings": ";;;;;AAEA;AAqBA;AApBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;;;AAyBA,MAAM,mBAAmB;;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;;sCACZ,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;wBAA8C;;;;;;;;;;;;0BAM5E,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;oCAAkB,MAAM;oCAAI,eAAY;;;;;;8CACzD,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;;;;;;;;;;;;kCAKjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAwC;;;;;;0DAOrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,6MAAA,CAAA,WAAQ;gEAAC,WAAU;gEAA4C,MAAM;;;;;;4DAAM;;;;;;;kEAG9E,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAwJ;;;;;;kFACxK,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAwJ;;;;;;kFACxK,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAwJ;;;;;;kFACxK,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAwJ;;;;;;kFACxK,6LAAC;kFAAK;;;;;;;;;;;;0EAER,6LAAC;gEAAG,WAAU;;kFACZ,6LAAC;wEAAK,WAAU;kFAAwJ;;;;;;kFACxK,6LAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMd,6LAAC;kDAEC,cAAA,6LAAC;4CAAI,WAAU;sDACZ,+BACC,6LAAC;0FAAc;;kEACb,6LAAC;wDACC,SAAS,IAAM,kBAAkB;kGACvB;kEAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;4DAAC,MAAM;;;;;;;;;;;kEAIf,6LAAC;kGAAc;kEACb,cAAA,6LAAC;sGAAc;sEAEb,cAAA,6LAAC;gEAAoF,cAAW;0GAAjF;;kFACb,6LAAC;kHAAc;kFAAoC;;;;;;kFACnD,6LAAC;kHAAc;;0FACb,6LAAC;0HAAc;;kGACb,6LAAC;kIAAc;kGACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4FAAC,MAAM;4FAAI,WAAU;;;;;;;;;;;kGAEhC,6LAAC;kIAAe;kGAA0B;;;;;;;;;;;;0FAE5C,6LAAC;0HAAc;;kGACb,6LAAC;kIAAc;kGACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4FAAC,MAAM;4FAAI,WAAU;;;;;;;;;;;kGAE7B,6LAAC;kIAAe;kGAA0B;;;;;;;;;;;;0FAE5C,6LAAC;0HAAc;;kGACb,6LAAC;kIAAc;kGACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4FAAC,MAAM;4FAAI,WAAU;;;;;;;;;;;kGAElC,6LAAC;kIAAe;kGAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qEAsBtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,kBAAkB;wDACjC,WAAU;wDACV,cAAW;kEAEX,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;kEAE5B,6LAAC;wDAAE,WAAU;kEAA0B;;;;;;kEACvC,6LAAC;wDAAE,WAAU;kEAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQjD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;wCAAuB,MAAM;;;;;;oCAAM;;;;;;;0CAGrD,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;4DAA4C,MAAM;;;;;;sEACtE,6LAAC;4DAAG,WAAU;sEAAgD;;;;;;;;;;;;;;;;;0DAGlE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EAIxD,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;0FACX,6LAAC;gFAAK,WAAU;0FAAgB;;;;;;4EAAuB;;;;;;;;;;;;;;;;;;sEAK7D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,MAAM;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;4DAA4C,MAAM;;;;;;sEACnE,6LAAC;4DAAG,WAAU;sEAAgD;;;;;;;;;;;;;;;;;0DAGlE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EAIxD,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;0FACX,6LAAC;gFAAK,WAAU;0FAAgB;;;;;;4EAAuB;;;;;;;;;;;;;;;;;;sEAK7D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oEAAC,MAAM;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qNAAA,CAAA,aAAU;4DAAC,WAAU;4DAA4C,MAAM;;;;;;sEACxE,6LAAC;4DAAG,WAAU;sEAAgD;;;;;;;;;;;;;;;;;0DAGlE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EAIxD,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;0FACX,6LAAC;gFAAK,WAAU;0FAAgB;;;;;;4EAAuB;;;;;;;;;;;;;;;;;;sEAK7D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oEAAC,MAAM;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ1C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;4DAA4C,MAAM;;;;;;sEACpE,6LAAC;4DAAG,WAAU;sEAAgD;;;;;;;;;;;;;;;;;0DAGlE,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAE,WAAU;8EAA2C;;;;;;8EAIxD,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAE,WAAU;;0FACX,6LAAC;gFAAK,WAAU;0FAAgB;;;;;;4EAAuB;;;;;;;;;;;;;;;;;;sEAK7D,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oEAAC,MAAM;oEAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;gDAA4C,MAAM;;;;;;4CAAM;;;;;;;kDAG9E,6LAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAGrD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAwJ;;;;;;kEACxK,6LAAC;;4DAAK;0EAAY,6LAAC;0EAAO;;;;;;4DAAiB;;;;;;;;;;;;;0DAE7C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAwJ;;;;;;kEACxK,6LAAC;;4DAAK;0EAAY,6LAAC;0EAAO;;;;;;4DAAkB;;;;;;;;;;;;;0DAE9C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAwJ;;;;;;kEACxK,6LAAC;;4DAAK;0EAAa,6LAAC;0EAAO;;;;;;4DAAkB;;;;;;;;;;;;;0DAE/C,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAK,WAAU;kEAAwJ;;;;;;kEACxK,6LAAC;;4DAAK;0EAAU,6LAAC;0EAAO;;;;;;4DAA4B;;;;;;;;;;;;;;;;;;;kDAGxD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;4CACtC,WAAU;;8DAEV,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,MAAM;;;;;;gDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;GArTM;KAAA;uCAuTS"}}, {"offset": {"line": 5955, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5961, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/introduction/index.js"], "sourcesContent": ["// src/app/modules/people/introduction/index.js\r\nimport IntroductionPage from './IntroductionPage';\r\n\r\nexport { IntroductionPage };\r\n"], "names": [], "mappings": "AAAA,+CAA+C"}}, {"offset": {"line": 5968, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5983, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/services/peopleDashboardService.js"], "sourcesContent": ["// src/app/modules/people/services/peopleDashboardService.js\r\nimport { api } from \"@/utils/api\";\r\nimport { personsService } from \"./personsService\";\r\nimport { clientsService } from \"./clientsService\";\r\nimport { insurancesService } from \"./insurancesService\";\r\nimport { exportService } from \"@/app/services/exportService\";\r\n\r\nexport const peopleDashboardService = {\r\n  /**\r\n   * Get all dashboard data for people module\r\n   * @param {Object} params - Parameters for filtering dashboard data\r\n   * @returns {Promise<Object>} Dashboard data\r\n   */\r\n  getPeopleDashboardData: async (params = {}) => {\r\n    try {\r\n      // Diretamente usar o método de geração de dados, já que o endpoint não existe\r\n      return peopleDashboardService.generateDashboardDataFromServices(params);\r\n    } catch (error) {\r\n      console.error(\"Error generating people dashboard data:\", error);\r\n\r\n      // Retornar dados vazios em caso de erro\r\n      return {\r\n        stats: {\r\n          totalPersons: 0,\r\n          activePersons: 0,\r\n          totalClients: 0,\r\n          activeClients: 0,\r\n          totalInsurances: 0\r\n        },\r\n        growth: {\r\n          personsGrowth: 0,\r\n          clientsGrowth: 0,\r\n          insurancesGrowth: 0\r\n        },\r\n        genderDistribution: [],\r\n        topClients: [],\r\n        insuranceDistribution: [],\r\n        recentPersons: []\r\n      };\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Exporta os dados do dashboard de pessoas\r\n   * @param {Object} params - Parâmetros para filtrar os dados do dashboard\r\n   * @param {string} exportFormat - Formato da exportação ('xlsx', 'pdf' ou 'image')\r\n   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida\r\n   */\r\n  exportDashboardData: async (params = {}, exportFormat = \"xlsx\") => {\r\n    try {\r\n      // Obter os dados do dashboard\r\n      const dashboardData = await peopleDashboardService.getPeopleDashboardData(params);\r\n\r\n      // Timestamp atual para o subtítulo\r\n      const timestamp = new Date().toLocaleString('pt-BR');\r\n\r\n      // Período para o subtítulo\r\n      let periodText = 'Todos os períodos';\r\n      if (params.period) {\r\n        switch (params.period) {\r\n          case '7dias':\r\n            periodText = 'Últimos 7 dias';\r\n            break;\r\n          case '30dias':\r\n            periodText = 'Últimos 30 dias';\r\n            break;\r\n          case '3meses':\r\n            periodText = 'Últimos 3 meses';\r\n            break;\r\n          case '6meses':\r\n            periodText = 'Últimos 6 meses';\r\n            break;\r\n          case '1ano':\r\n            periodText = 'Último ano';\r\n            break;\r\n          default:\r\n            periodText = params.period;\r\n        }\r\n      }\r\n\r\n      // Subtítulo com timestamp e período\r\n      const subtitle = `Exportado em: ${timestamp} | Período: ${periodText}`;\r\n\r\n      // Definição das colunas para cada tabela\r\n      const statsColumns = [\r\n        { key: \"metric\", header: \"Métrica\" },\r\n        { key: \"value\", header: \"Valor\", align: \"right\" }\r\n      ];\r\n\r\n      const genderColumns = [\r\n        { key: \"name\", header: \"Gênero\" },\r\n        { key: \"value\", header: \"Quantidade\", align: \"right\" }\r\n      ];\r\n\r\n      const clientColumns = [\r\n        { key: \"name\", header: \"Cliente\" },\r\n        { key: \"count\", header: \"Pacientes\", align: \"right\" }\r\n      ];\r\n\r\n      const insuranceColumns = [\r\n        { key: \"name\", header: \"Status\" },\r\n        { key: \"value\", header: \"Quantidade\", align: \"right\" }\r\n      ];\r\n\r\n      const recentPersonsColumns = [\r\n        { key: \"fullName\", header: \"Nome\" },\r\n        { key: \"clientName\", header: \"Cliente\" },\r\n        { key: \"relationship\", header: \"Relação\" },\r\n        { key: \"createdAt\", header: \"Data de Cadastro\", type: \"date\" }\r\n      ];\r\n\r\n      // Preparar os dados de estatísticas para exportação\r\n      const statsData = [\r\n        { metric: \"Total de Pacientes\", value: dashboardData.stats.totalPersons },\r\n        { metric: \"Pacientes Ativos\", value: dashboardData.stats.activePersons },\r\n        { metric: \"Total de Clientes\", value: dashboardData.stats.totalClients },\r\n        { metric: \"Clientes Ativos\", value: dashboardData.stats.activeClients },\r\n        { metric: \"Total de Convênios\", value: dashboardData.stats.totalInsurances }\r\n      ];\r\n\r\n      // Exportar os dados em múltiplas tabelas\r\n      return await exportService.exportData(\r\n        {\r\n          estatisticas: statsData,\r\n          genero: dashboardData.genderDistribution,\r\n          clientes: dashboardData.topClients,\r\n          convenios: dashboardData.insuranceDistribution,\r\n          recentes: dashboardData.recentPersons\r\n        },\r\n        {\r\n          format: exportFormat,\r\n          filename: \"dashboard-pessoas\",\r\n          title: \"Dashboard de Pessoas\",\r\n          subtitle,\r\n          multiTable: true,\r\n          tables: [\r\n            { name: \"estatisticas\", title: \"Estatísticas Gerais\", columns: statsColumns },\r\n            { name: \"genero\", title: \"Distribuição por Gênero\", columns: genderColumns },\r\n            { name: \"clientes\", title: \"Clientes com Mais Pacientes\", columns: clientColumns },\r\n            { name: \"convenios\", title: \"Distribuição por Convênio\", columns: insuranceColumns },\r\n            { name: \"recentes\", title: \"Pacientes Recentes\", columns: recentPersonsColumns }\r\n          ]\r\n        }\r\n      );\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar dados do dashboard de pessoas:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  /**\r\n   * Generate dashboard data from existing services\r\n   * @param {Object} params - Parameters for filtering dashboard data\r\n   * @returns {Promise<Object>} Dashboard data\r\n   */\r\n  generateDashboardDataFromServices: async (params = {}) => {\r\n    try {\r\n      // Fetch data from existing services\r\n      const [personsResponse, clientsResponse, insurancesResponse] = await Promise.all([\r\n        personsService.getPersons({ limit: 1000 }),\r\n        clientsService.getClients({ limit: 1000 }),\r\n        insurancesService.getInsurances({})\r\n      ]);\r\n\r\n      // Extract data\r\n      const persons = personsResponse.persons || [];\r\n      const clients = clientsResponse.clients || [];\r\n\r\n      // Extrair convênios corretamente da resposta\r\n      console.log(\"Resposta do serviço de convênios:\", insurancesResponse);\r\n\r\n      let insurances = [];\r\n\r\n      // Se a resposta for um array, usar diretamente\r\n      if (Array.isArray(insurancesResponse)) {\r\n        insurances = insurancesResponse;\r\n      }\r\n      // Se for um objeto, procurar por propriedades que possam conter os convênios\r\n      else if (insurancesResponse && typeof insurancesResponse === 'object') {\r\n        // Verificar propriedades comuns\r\n        if (Array.isArray(insurancesResponse.insurances)) {\r\n          insurances = insurancesResponse.insurances;\r\n        } else if (Array.isArray(insurancesResponse.data)) {\r\n          insurances = insurancesResponse.data;\r\n        } else {\r\n          // Procurar por qualquer propriedade que seja um array\r\n          for (const key in insurancesResponse) {\r\n            if (Array.isArray(insurancesResponse[key])) {\r\n              insurances = insurancesResponse[key];\r\n              break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log(\"Convênios extraídos:\", insurances);\r\n\r\n      // Se ainda não tiver convênios, usar dados mockados para demonstração\r\n      if (!insurances || insurances.length === 0) {\r\n        console.log(\"Usando dados mockados para convênios\");\r\n        insurances = [\r\n          { id: '1', name: 'Unimed' },\r\n          { id: '2', name: 'Amil' },\r\n          { id: '3', name: 'SulAmérica' },\r\n          { id: '4', name: 'Bradesco Saúde' },\r\n          { id: '5', name: 'NotreDame Intermédica' }\r\n        ];\r\n      }\r\n\r\n      // Calculate stats\r\n      const stats = {\r\n        totalPersons: persons.length,\r\n        activePersons: persons.filter(person => person.active).length,\r\n        totalClients: clients.length,\r\n        activeClients: clients.filter(client => client.active).length,\r\n        totalInsurances: insurances.length\r\n      };\r\n\r\n      // Calculate growth (mock data for now)\r\n      const growth = {\r\n        personsGrowth: 5,\r\n        clientsGrowth: 3,\r\n        insurancesGrowth: 2\r\n      };\r\n\r\n      // Calculate gender distribution\r\n      const genderDistribution = calculateGenderDistribution(persons);\r\n\r\n      // Calculate top clients by persons count\r\n      const topClients = calculateTopClients(clients, persons);\r\n\r\n      // Calculate insurance distribution\r\n      const insuranceDistribution = calculateInsuranceDistribution(persons);\r\n\r\n      // Calculate recent persons\r\n      const recentPersons = getRecentPersons(persons, clients);\r\n\r\n      return {\r\n        stats,\r\n        growth,\r\n        genderDistribution,\r\n        topClients,\r\n        insuranceDistribution,\r\n        recentPersons\r\n      };\r\n    } catch (error) {\r\n      console.error(\"Error generating dashboard data:\", error);\r\n      return {\r\n        stats: {\r\n          totalPersons: 0,\r\n          activePersons: 0,\r\n          totalClients: 0,\r\n          activeClients: 0,\r\n          totalInsurances: 0\r\n        },\r\n        growth: {\r\n          personsGrowth: 0,\r\n          clientsGrowth: 0,\r\n          insurancesGrowth: 0\r\n        },\r\n        genderDistribution: [],\r\n        topClients: [],\r\n        insuranceDistribution: [],\r\n        recentPersons: []\r\n      };\r\n    }\r\n  }\r\n};\r\n\r\n/**\r\n * Calculate gender distribution from persons data\r\n * @param {Array} persons - List of persons\r\n * @returns {Array} Gender distribution data\r\n */\r\nfunction calculateGenderDistribution(persons) {\r\n  const genderCounts = {\r\n    M: 0,\r\n    F: 0,\r\n    O: 0\r\n  };\r\n\r\n  persons.forEach(person => {\r\n    if (person.gender && genderCounts.hasOwnProperty(person.gender)) {\r\n      genderCounts[person.gender]++;\r\n    } else {\r\n      genderCounts.O++;\r\n    }\r\n  });\r\n\r\n  return [\r\n    { name: 'Masculino', value: genderCounts.M },\r\n    { name: 'Feminino', value: genderCounts.F },\r\n    { name: 'Outro', value: genderCounts.O }\r\n  ];\r\n}\r\n\r\n/**\r\n * Calculate top clients by persons count\r\n * @param {Array} clients - List of clients\r\n * @param {Array} persons - List of persons\r\n * @returns {Array} Top clients data\r\n */\r\nfunction calculateTopClients(clients, persons) {\r\n  // Count persons per client\r\n  const clientPersonsCount = {};\r\n\r\n  persons.forEach(person => {\r\n    if (person.clientId) {\r\n      if (!clientPersonsCount[person.clientId]) {\r\n        clientPersonsCount[person.clientId] = 0;\r\n      }\r\n      clientPersonsCount[person.clientId]++;\r\n    }\r\n  });\r\n\r\n  // Map client IDs to client objects\r\n  const clientMap = {};\r\n  clients.forEach(client => {\r\n    clientMap[client.id] = client;\r\n  });\r\n\r\n  // Create array of clients with person count\r\n  const clientsWithCount = Object.keys(clientPersonsCount).map(clientId => {\r\n    const client = clientMap[clientId];\r\n    // Usar nome completo se disponível, caso contrário usar login ou \"Cliente Desconhecido\"\r\n    const clientName = client ? (client.fullName || client.login) : 'Cliente Desconhecido';\r\n\r\n    console.log('Client data:', client ? { id: client.id, fullName: client.fullName, login: client.login } : 'No client data');\r\n\r\n    return {\r\n      id: clientId,\r\n      name: clientName,\r\n      count: clientPersonsCount[clientId]\r\n    };\r\n  });\r\n\r\n  // Sort by count and take top 5\r\n  return clientsWithCount\r\n    .sort((a, b) => b.count - a.count)\r\n    .slice(0, 5);\r\n}\r\n\r\n/**\r\n * Calculate insurance distribution from persons data\r\n * @param {Array} persons - List of persons\r\n * @returns {Array} Insurance distribution data\r\n */\r\nfunction calculateInsuranceDistribution(persons) {\r\n  // Count persons with insurance\r\n  let withInsurance = 0;\r\n  let withoutInsurance = 0;\r\n\r\n  persons.forEach(person => {\r\n    if (person.personInsurances && person.personInsurances.length > 0) {\r\n      withInsurance++;\r\n    } else {\r\n      withoutInsurance++;\r\n    }\r\n  });\r\n\r\n  return [\r\n    { name: 'Com Convênio', value: withInsurance },\r\n    { name: 'Sem Convênio', value: withoutInsurance }\r\n  ];\r\n}\r\n\r\n/**\r\n * Get recent persons\r\n * @param {Array} persons - List of persons\r\n * @returns {Array} Recent persons\r\n */\r\nfunction getRecentPersons(persons, clients) {\r\n  // Map client IDs to client objects for quick lookup\r\n  const clientMap = {};\r\n  clients.forEach(client => {\r\n    clientMap[client.id] = client;\r\n  });\r\n\r\n  return persons\r\n    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))\r\n    .slice(0, 5)\r\n    .map(person => {\r\n      // Get client info if available\r\n      const client = person.clientPersons && person.clientPersons.length > 0\r\n        ? clientMap[person.clientPersons[0].clientId]\r\n        : null;\r\n\r\n      return {\r\n        id: person.id,\r\n        fullName: person.fullName,\r\n        email: person.email,\r\n        phone: person.phone,\r\n        createdAt: person.createdAt,\r\n        profileImageUrl: person.profileImageUrl,\r\n        clientId: person.clientId,\r\n        clientName: client ? (client.fullName || client.login) : 'Cliente Desconhecido',\r\n        relationship: person.relationship || 'Titular'\r\n      };\r\n    });\r\n}\r\n\r\nexport default peopleDashboardService;\r\n"], "names": [], "mappings": "AAAA,4DAA4D;;;;;AAC5D;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,yBAAyB;IACpC;;;;GAIC,GACD,wBAAwB,OAAO,SAAS,CAAC,CAAC;QACxC,IAAI;YACF,8EAA8E;YAC9E,OAAO,uBAAuB,iCAAiC,CAAC;QAClE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YAEzD,wCAAwC;YACxC,OAAO;gBACL,OAAO;oBACL,cAAc;oBACd,eAAe;oBACf,cAAc;oBACd,eAAe;oBACf,iBAAiB;gBACnB;gBACA,QAAQ;oBACN,eAAe;oBACf,eAAe;oBACf,kBAAkB;gBACpB;gBACA,oBAAoB,EAAE;gBACtB,YAAY,EAAE;gBACd,uBAAuB,EAAE;gBACzB,eAAe,EAAE;YACnB;QACF;IACF;IAEA;;;;;GAKC,GACD,qBAAqB,OAAO,SAAS,CAAC,CAAC,EAAE,eAAe,MAAM;QAC5D,IAAI;YACF,8BAA8B;YAC9B,MAAM,gBAAgB,MAAM,uBAAuB,sBAAsB,CAAC;YAE1E,mCAAmC;YACnC,MAAM,YAAY,IAAI,OAAO,cAAc,CAAC;YAE5C,2BAA2B;YAC3B,IAAI,aAAa;YACjB,IAAI,OAAO,MAAM,EAAE;gBACjB,OAAQ,OAAO,MAAM;oBACnB,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;oBACF,KAAK;wBACH,aAAa;wBACb;oBACF;wBACE,aAAa,OAAO,MAAM;gBAC9B;YACF;YAEA,oCAAoC;YACpC,MAAM,WAAW,CAAC,cAAc,EAAE,UAAU,YAAY,EAAE,YAAY;YAEtE,yCAAyC;YACzC,MAAM,eAAe;gBACnB;oBAAE,KAAK;oBAAU,QAAQ;gBAAU;gBACnC;oBAAE,KAAK;oBAAS,QAAQ;oBAAS,OAAO;gBAAQ;aACjD;YAED,MAAM,gBAAgB;gBACpB;oBAAE,KAAK;oBAAQ,QAAQ;gBAAS;gBAChC;oBAAE,KAAK;oBAAS,QAAQ;oBAAc,OAAO;gBAAQ;aACtD;YAED,MAAM,gBAAgB;gBACpB;oBAAE,KAAK;oBAAQ,QAAQ;gBAAU;gBACjC;oBAAE,KAAK;oBAAS,QAAQ;oBAAa,OAAO;gBAAQ;aACrD;YAED,MAAM,mBAAmB;gBACvB;oBAAE,KAAK;oBAAQ,QAAQ;gBAAS;gBAChC;oBAAE,KAAK;oBAAS,QAAQ;oBAAc,OAAO;gBAAQ;aACtD;YAED,MAAM,uBAAuB;gBAC3B;oBAAE,KAAK;oBAAY,QAAQ;gBAAO;gBAClC;oBAAE,KAAK;oBAAc,QAAQ;gBAAU;gBACvC;oBAAE,KAAK;oBAAgB,QAAQ;gBAAU;gBACzC;oBAAE,KAAK;oBAAa,QAAQ;oBAAoB,MAAM;gBAAO;aAC9D;YAED,oDAAoD;YACpD,MAAM,YAAY;gBAChB;oBAAE,QAAQ;oBAAsB,OAAO,cAAc,KAAK,CAAC,YAAY;gBAAC;gBACxE;oBAAE,QAAQ;oBAAoB,OAAO,cAAc,KAAK,CAAC,aAAa;gBAAC;gBACvE;oBAAE,QAAQ;oBAAqB,OAAO,cAAc,KAAK,CAAC,YAAY;gBAAC;gBACvE;oBAAE,QAAQ;oBAAmB,OAAO,cAAc,KAAK,CAAC,aAAa;gBAAC;gBACtE;oBAAE,QAAQ;oBAAsB,OAAO,cAAc,KAAK,CAAC,eAAe;gBAAC;aAC5E;YAED,yCAAyC;YACzC,OAAO,MAAM,0IAAA,CAAA,gBAAa,CAAC,UAAU,CACnC;gBACE,cAAc;gBACd,QAAQ,cAAc,kBAAkB;gBACxC,UAAU,cAAc,UAAU;gBAClC,WAAW,cAAc,qBAAqB;gBAC9C,UAAU,cAAc,aAAa;YACvC,GACA;gBACE,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP;gBACA,YAAY;gBACZ,QAAQ;oBACN;wBAAE,MAAM;wBAAgB,OAAO;wBAAuB,SAAS;oBAAa;oBAC5E;wBAAE,MAAM;wBAAU,OAAO;wBAA2B,SAAS;oBAAc;oBAC3E;wBAAE,MAAM;wBAAY,OAAO;wBAA+B,SAAS;oBAAc;oBACjF;wBAAE,MAAM;wBAAa,OAAO;wBAA6B,SAAS;oBAAiB;oBACnF;wBAAE,MAAM;wBAAY,OAAO;wBAAsB,SAAS;oBAAqB;iBAChF;YACH;QAEJ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,OAAO;QACT;IACF;IAEA;;;;GAIC,GACD,mCAAmC,OAAO,SAAS,CAAC,CAAC;QACnD,IAAI;YACF,oCAAoC;YACpC,MAAM,CAAC,iBAAiB,iBAAiB,mBAAmB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC/E,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAK;gBACxC,gKAAA,CAAA,iBAAc,CAAC,UAAU,CAAC;oBAAE,OAAO;gBAAK;gBACxC,mKAAA,CAAA,oBAAiB,CAAC,aAAa,CAAC,CAAC;aAClC;YAED,eAAe;YACf,MAAM,UAAU,gBAAgB,OAAO,IAAI,EAAE;YAC7C,MAAM,UAAU,gBAAgB,OAAO,IAAI,EAAE;YAE7C,6CAA6C;YAC7C,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,IAAI,aAAa,EAAE;YAEnB,+CAA+C;YAC/C,IAAI,MAAM,OAAO,CAAC,qBAAqB;gBACrC,aAAa;YACf,OAEK,IAAI,sBAAsB,OAAO,uBAAuB,UAAU;gBACrE,gCAAgC;gBAChC,IAAI,MAAM,OAAO,CAAC,mBAAmB,UAAU,GAAG;oBAChD,aAAa,mBAAmB,UAAU;gBAC5C,OAAO,IAAI,MAAM,OAAO,CAAC,mBAAmB,IAAI,GAAG;oBACjD,aAAa,mBAAmB,IAAI;gBACtC,OAAO;oBACL,sDAAsD;oBACtD,IAAK,MAAM,OAAO,mBAAoB;wBACpC,IAAI,MAAM,OAAO,CAAC,kBAAkB,CAAC,IAAI,GAAG;4BAC1C,aAAa,kBAAkB,CAAC,IAAI;4BACpC;wBACF;oBACF;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,sEAAsE;YACtE,IAAI,CAAC,cAAc,WAAW,MAAM,KAAK,GAAG;gBAC1C,QAAQ,GAAG,CAAC;gBACZ,aAAa;oBACX;wBAAE,IAAI;wBAAK,MAAM;oBAAS;oBAC1B;wBAAE,IAAI;wBAAK,MAAM;oBAAO;oBACxB;wBAAE,IAAI;wBAAK,MAAM;oBAAa;oBAC9B;wBAAE,IAAI;wBAAK,MAAM;oBAAiB;oBAClC;wBAAE,IAAI;wBAAK,MAAM;oBAAwB;iBAC1C;YACH;YAEA,kBAAkB;YAClB,MAAM,QAAQ;gBACZ,cAAc,QAAQ,MAAM;gBAC5B,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,EAAE,MAAM;gBAC7D,cAAc,QAAQ,MAAM;gBAC5B,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,MAAM,EAAE,MAAM;gBAC7D,iBAAiB,WAAW,MAAM;YACpC;YAEA,uCAAuC;YACvC,MAAM,SAAS;gBACb,eAAe;gBACf,eAAe;gBACf,kBAAkB;YACpB;YAEA,gCAAgC;YAChC,MAAM,qBAAqB,4BAA4B;YAEvD,yCAAyC;YACzC,MAAM,aAAa,oBAAoB,SAAS;YAEhD,mCAAmC;YACnC,MAAM,wBAAwB,+BAA+B;YAE7D,2BAA2B;YAC3B,MAAM,gBAAgB,iBAAiB,SAAS;YAEhD,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBACL,OAAO;oBACL,cAAc;oBACd,eAAe;oBACf,cAAc;oBACd,eAAe;oBACf,iBAAiB;gBACnB;gBACA,QAAQ;oBACN,eAAe;oBACf,eAAe;oBACf,kBAAkB;gBACpB;gBACA,oBAAoB,EAAE;gBACtB,YAAY,EAAE;gBACd,uBAAuB,EAAE;gBACzB,eAAe,EAAE;YACnB;QACF;IACF;AACF;AAEA;;;;CAIC,GACD,SAAS,4BAA4B,OAAO;IAC1C,MAAM,eAAe;QACnB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,QAAQ,OAAO,CAAC,CAAA;QACd,IAAI,OAAO,MAAM,IAAI,aAAa,cAAc,CAAC,OAAO,MAAM,GAAG;YAC/D,YAAY,CAAC,OAAO,MAAM,CAAC;QAC7B,OAAO;YACL,aAAa,CAAC;QAChB;IACF;IAEA,OAAO;QACL;YAAE,MAAM;YAAa,OAAO,aAAa,CAAC;QAAC;QAC3C;YAAE,MAAM;YAAY,OAAO,aAAa,CAAC;QAAC;QAC1C;YAAE,MAAM;YAAS,OAAO,aAAa,CAAC;QAAC;KACxC;AACH;AAEA;;;;;CAKC,GACD,SAAS,oBAAoB,OAAO,EAAE,OAAO;IAC3C,2BAA2B;IAC3B,MAAM,qBAAqB,CAAC;IAE5B,QAAQ,OAAO,CAAC,CAAA;QACd,IAAI,OAAO,QAAQ,EAAE;YACnB,IAAI,CAAC,kBAAkB,CAAC,OAAO,QAAQ,CAAC,EAAE;gBACxC,kBAAkB,CAAC,OAAO,QAAQ,CAAC,GAAG;YACxC;YACA,kBAAkB,CAAC,OAAO,QAAQ,CAAC;QACrC;IACF;IAEA,mCAAmC;IACnC,MAAM,YAAY,CAAC;IACnB,QAAQ,OAAO,CAAC,CAAA;QACd,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG;IACzB;IAEA,4CAA4C;IAC5C,MAAM,mBAAmB,OAAO,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAA;QAC3D,MAAM,SAAS,SAAS,CAAC,SAAS;QAClC,wFAAwF;QACxF,MAAM,aAAa,SAAU,OAAO,QAAQ,IAAI,OAAO,KAAK,GAAI;QAEhE,QAAQ,GAAG,CAAC,gBAAgB,SAAS;YAAE,IAAI,OAAO,EAAE;YAAE,UAAU,OAAO,QAAQ;YAAE,OAAO,OAAO,KAAK;QAAC,IAAI;QAEzG,OAAO;YACL,IAAI;YACJ,MAAM;YACN,OAAO,kBAAkB,CAAC,SAAS;QACrC;IACF;IAEA,+BAA+B;IAC/B,OAAO,iBACJ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAChC,KAAK,CAAC,GAAG;AACd;AAEA;;;;CAIC,GACD,SAAS,+BAA+B,OAAO;IAC7C,+BAA+B;IAC/B,IAAI,gBAAgB;IACpB,IAAI,mBAAmB;IAEvB,QAAQ,OAAO,CAAC,CAAA;QACd,IAAI,OAAO,gBAAgB,IAAI,OAAO,gBAAgB,CAAC,MAAM,GAAG,GAAG;YACjE;QACF,OAAO;YACL;QACF;IACF;IAEA,OAAO;QACL;YAAE,MAAM;YAAgB,OAAO;QAAc;QAC7C;YAAE,MAAM;YAAgB,OAAO;QAAiB;KACjD;AACH;AAEA;;;;CAIC,GACD,SAAS,iBAAiB,OAAO,EAAE,OAAO;IACxC,oDAAoD;IACpD,MAAM,YAAY,CAAC;IACnB,QAAQ,OAAO,CAAC,CAAA;QACd,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG;IACzB;IAEA,OAAO,QACJ,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,SAAS,GAC3D,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAA;QACH,+BAA+B;QAC/B,MAAM,SAAS,OAAO,aAAa,IAAI,OAAO,aAAa,CAAC,MAAM,GAAG,IACjE,SAAS,CAAC,OAAO,aAAa,CAAC,EAAE,CAAC,QAAQ,CAAC,GAC3C;QAEJ,OAAO;YACL,IAAI,OAAO,EAAE;YACb,UAAU,OAAO,QAAQ;YACzB,OAAO,OAAO,KAAK;YACnB,OAAO,OAAO,KAAK;YACnB,WAAW,OAAO,SAAS;YAC3B,iBAAiB,OAAO,eAAe;YACvC,UAAU,OAAO,QAAQ;YACzB,YAAY,SAAU,OAAO,QAAQ,IAAI,OAAO,KAAK,GAAI;YACzD,cAAc,OAAO,YAAY,IAAI;QACvC;IACF;AACJ;uCAEe"}}, {"offset": {"line": 6448, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 6454, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/dashboard/PeopleDashboard.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from \"react\";\r\nimport {\r\n  <PERSON><PERSON><PERSON>,\r\n  <PERSON>,\r\n  <PERSON><PERSON>hart,\r\n  Pie,\r\n  Cell,\r\n  XAxis,\r\n  YAxis,\r\n  CartesianGrid,\r\n  <PERSON>ltip,\r\n  Responsive<PERSON>ontaine<PERSON>,\r\n  <PERSON>\r\n} from \"recharts\";\r\nimport {\r\n  Users,\r\n  UserPlus,\r\n  CreditCard,\r\n  Shield,\r\n  TrendingUp,\r\n  TrendingDown,\r\n  User,\r\n  Mail,\r\n  Phone,\r\n  Calendar,\r\n  Clock,\r\n  LayoutDashboard,\r\n  Filter,\r\n  RefreshCw,\r\n  Bar<PERSON>hart2,\r\n  <PERSON><PERSON><PERSON> as PieChartIcon\r\n} from \"lucide-react\";\r\nimport { format } from \"date-fns\";\r\nimport { ptBR } from \"date-fns/locale\";\r\nimport { peopleDashboardService } from \"../services/peopleDashboardService\";\r\nimport { personsService } from \"../services/personsService\";\r\nimport { ModuleHeader, ModuleTable, ModuleFormGroup, ModuleSelect } from \"@/components/ui\";\r\nimport ExportMenu from \"@/components/ui/ExportMenu\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\n\r\n// Cores para os gráficos\r\nconst GENDER_COLORS = [\"#3B82F6\", \"#EC4899\", \"#8B5CF6\"];\r\nconst CLIENT_COLORS = [\"#F97316\", \"#FBBF24\", \"#10B981\", \"#3B82F6\", \"#8B5CF6\"];\r\nconst INSURANCE_COLORS = [\"#10B981\", \"#F97316\"];\r\n\r\nconst PeopleDashboard = () => {\r\n  // Estados para armazenar os dados\r\n  const [dashboardData, setDashboardData] = useState({\r\n    stats: {\r\n      totalPersons: 0,\r\n      activePersons: 0,\r\n      totalClients: 0,\r\n      activeClients: 0,\r\n      totalInsurances: 0\r\n    },\r\n    growth: {\r\n      personsGrowth: 0,\r\n      clientsGrowth: 0,\r\n      insurancesGrowth: 0\r\n    },\r\n    genderDistribution: [],\r\n    topClients: [],\r\n    insuranceDistribution: [],\r\n    recentPersons: []\r\n  });\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [period, setPeriod] = useState(\"30dias\");\r\n  const [chartType, setChartType] = useState(\"bar\"); // 'bar' ou 'pie'\r\n  const [isExporting, setIsExporting] = useState(false);\r\n  const { user } = useAuth();\r\n\r\n  // Função para carregar os dados do dashboard\r\n  const loadDashboardData = async (selectedPeriod = period) => {\r\n    setIsLoading(true);\r\n    try {\r\n      const data = await peopleDashboardService.getPeopleDashboardData({\r\n        period: selectedPeriod\r\n      });\r\n      setDashboardData(data);\r\n    } catch (error) {\r\n      console.error(\"Erro ao carregar dados do dashboard:\", error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Função para alterar o período e atualizar os dados\r\n  const handlePeriodChange = (e) => {\r\n    const newPeriod = e.target.value;\r\n    setPeriod(newPeriod);\r\n    loadDashboardData(newPeriod);\r\n  };\r\n\r\n  // Função para alternar o tipo de gráfico\r\n  const toggleChartType = (type) => {\r\n    setChartType(type);\r\n  };\r\n\r\n  // Função para exportar os dados do dashboard\r\n  const handleExport = async (format) => {\r\n    setIsExporting(true);\r\n    try {\r\n      await peopleDashboardService.exportDashboardData({ period }, format);\r\n    } catch (error) {\r\n      console.error(\"Erro ao exportar dados do dashboard:\", error);\r\n    } finally {\r\n      setIsExporting(false);\r\n    }\r\n  };\r\n\r\n  // Carregar dados quando o componente montar\r\n  useEffect(() => {\r\n    loadDashboardData();\r\n  }, []);\r\n\r\n  // Formatar data\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) return \"N/A\";\r\n    try {\r\n      return format(new Date(dateString), \"dd/MM/yyyy\", { locale: ptBR });\r\n    } catch (error) {\r\n      return \"Data inválida\";\r\n    }\r\n  };\r\n\r\n  // Componente personalizado para o tooltip dos gráficos\r\n  const CustomTooltip = ({ active, payload, label }) => {\r\n    if (active && payload && payload.length) {\r\n      return (\r\n        <div className=\"bg-white dark:bg-gray-800 p-3 border border-gray-200 dark:border-gray-700 rounded shadow-md\">\r\n          <p className=\"font-medium text-gray-800 dark:text-gray-200\">{label}</p>\r\n          {payload.map((entry, index) => (\r\n            <p key={index} style={{ color: entry.color }}>\r\n              {entry.name}: {entry.value}\r\n            </p>\r\n          ))}\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  // Extrair dados do estado\r\n  const { stats, growth, genderDistribution, topClients, insuranceDistribution, recentPersons } = dashboardData;\r\n\r\n  // Log para debug\r\n  console.log('Top Clients:', topClients);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Título e botão de exportação */}\r\n      <div className=\"flex justify-between items-center mb-4\">\r\n        <h1 className=\"text-2xl font-bold text-slate-800 dark:text-white flex items-center\">\r\n          <LayoutDashboard size={24} className=\"mr-2 text-orange-600 dark:text-orange-400\" />\r\n          Dashboard de Pessoas\r\n        </h1>\r\n\r\n        <ExportMenu\r\n          onExport={handleExport}\r\n          isExporting={isExporting}\r\n          disabled={isLoading}\r\n          className=\"text-orange-600 dark:text-orange-400\"\r\n        />\r\n      </div>\r\n\r\n      {/* Filtros */}\r\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl border border-module-people-border dark:border-gray-700 shadow-lg dark:shadow-black/30 p-4\">\r\n        <div className=\"flex flex-col md:flex-row gap-4\">\r\n          <div className=\"w-full md:w-64\">\r\n            <div className=\"flex items-center gap-2 text-gray-500 dark:text-gray-400 mb-2\">\r\n              <Calendar size={16} />\r\n              <span className=\"text-sm font-medium\">Período</span>\r\n            </div>\r\n            <div className=\"relative\">\r\n              <select\r\n                id=\"period\"\r\n                name=\"period\"\r\n                value={period}\r\n                onChange={handlePeriodChange}\r\n                className=\"w-full bg-gray-800 dark:bg-gray-800 text-white dark:text-white border border-gray-700 dark:border-gray-700 rounded-md py-2 px-3 appearance-none focus:outline-none focus:ring-2 focus:ring-people-500 focus:border-transparent\"\r\n              >\r\n                <option value=\"7dias\">Últimos 7 dias</option>\r\n                <option value=\"30dias\">Últimos 30 dias</option>\r\n                <option value=\"3meses\">Últimos 3 meses</option>\r\n                <option value=\"6meses\">Últimos 6 meses</option>\r\n                <option value=\"1ano\">Último ano</option>\r\n              </select>\r\n              <div className=\"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none\">\r\n                <svg className=\"h-5 w-5 text-gray-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\" aria-hidden=\"true\">\r\n                  <path fillRule=\"evenodd\" d=\"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\r\n                </svg>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Chart type toggle */}\r\n          <div className=\"flex items-end\">\r\n            <div className=\"flex border border-gray-700 dark:border-gray-700 rounded-md overflow-hidden\">\r\n              <button\r\n                className={`p-2 ${\r\n                  chartType === \"bar\"\r\n                  ? \"bg-people-500 text-white\"\r\n                  : \"bg-gray-800 dark:bg-gray-800 text-gray-400 dark:text-gray-400\"\r\n                }`}\r\n                onClick={() => toggleChartType(\"bar\")}\r\n                title=\"Gráfico de barras\"\r\n              >\r\n                <BarChart2 className=\"h-5 w-5\" />\r\n              </button>\r\n              <button\r\n                className={`p-2 ${\r\n                  chartType === \"pie\"\r\n                  ? \"bg-people-500 text-white\"\r\n                  : \"bg-gray-800 dark:bg-gray-800 text-gray-400 dark:text-gray-400\"\r\n                }`}\r\n                onClick={() => toggleChartType(\"pie\")}\r\n                title=\"Gráfico de pizza\"\r\n              >\r\n                <PieChartIcon className=\"h-5 w-5\" />\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Stats Cards */}\r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\">\r\n        {/* Pacientes Card */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-neutral-500 dark:text-neutral-400\">\r\n                Pacientes\r\n              </p>\r\n              <h3 className=\"text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2\">\r\n                {stats.totalPersons}\r\n              </h3>\r\n            </div>\r\n            <div className=\"h-12 w-12 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center\">\r\n              <Users className=\"h-6 w-6 text-orange-600 dark:text-orange-400\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4 flex items-center text-xs\">\r\n            <span\r\n              className={`flex items-center ${\r\n                growth.personsGrowth >= 0\r\n                  ? \"text-green-600 dark:text-green-400\"\r\n                  : \"text-red-600 dark:text-red-400\"\r\n              }`}\r\n            >\r\n              {growth.personsGrowth >= 0 ? (\r\n                <TrendingUp className=\"h-3 w-3 mr-1\" />\r\n              ) : (\r\n                <TrendingDown className=\"h-3 w-3 mr-1\" />\r\n              )}\r\n              {Math.abs(growth.personsGrowth)}%\r\n            </span>\r\n            <span className=\"text-neutral-500 dark:text-neutral-400 ml-2\">\r\n              desde o período anterior\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Pacientes Ativos Card */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-neutral-500 dark:text-neutral-400\">\r\n                Pacientes Ativos\r\n              </p>\r\n              <h3 className=\"text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2\">\r\n                {stats.activePersons}\r\n              </h3>\r\n            </div>\r\n            <div className=\"h-12 w-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center\">\r\n              <User className=\"h-6 w-6 text-green-600 dark:text-green-400\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4 flex items-center text-xs\">\r\n            <span className=\"text-neutral-500 dark:text-neutral-400\">\r\n              {stats.totalPersons > 0\r\n                ? `${Math.round((stats.activePersons / stats.totalPersons) * 100)}% do total`\r\n                : \"0% do total\"}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Clientes Card */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-neutral-500 dark:text-neutral-400\">\r\n                Clientes\r\n              </p>\r\n              <h3 className=\"text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2\">\r\n                {stats.totalClients}\r\n              </h3>\r\n            </div>\r\n            <div className=\"h-12 w-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center\">\r\n              <UserPlus className=\"h-6 w-6 text-blue-600 dark:text-blue-400\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4 flex items-center text-xs\">\r\n            <span\r\n              className={`flex items-center ${\r\n                growth.clientsGrowth >= 0\r\n                  ? \"text-green-600 dark:text-green-400\"\r\n                  : \"text-red-600 dark:text-red-400\"\r\n              }`}\r\n            >\r\n              {growth.clientsGrowth >= 0 ? (\r\n                <TrendingUp className=\"h-3 w-3 mr-1\" />\r\n              ) : (\r\n                <TrendingDown className=\"h-3 w-3 mr-1\" />\r\n              )}\r\n              {Math.abs(growth.clientsGrowth)}%\r\n            </span>\r\n            <span className=\"text-neutral-500 dark:text-neutral-400 ml-2\">\r\n              desde o período anterior\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Clientes Ativos Card */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-neutral-500 dark:text-neutral-400\">\r\n                Clientes Ativos\r\n              </p>\r\n              <h3 className=\"text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2\">\r\n                {stats.activeClients}\r\n              </h3>\r\n            </div>\r\n            <div className=\"h-12 w-12 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center\">\r\n              <UserPlus className=\"h-6 w-6 text-teal-600 dark:text-teal-400\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4 flex items-center text-xs\">\r\n            <span className=\"text-neutral-500 dark:text-neutral-400\">\r\n              {stats.totalClients > 0\r\n                ? `${Math.round((stats.activeClients / stats.totalClients) * 100)}% do total`\r\n                : \"0% do total\"}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Convênios Card */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex justify-between\">\r\n            <div>\r\n              <p className=\"text-sm font-medium text-neutral-500 dark:text-neutral-400\">\r\n                Convênios\r\n              </p>\r\n              <h3 className=\"text-3xl font-bold text-neutral-800 dark:text-neutral-100 mt-2\">\r\n                {stats.totalInsurances}\r\n              </h3>\r\n            </div>\r\n            <div className=\"h-12 w-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center\">\r\n              <CreditCard className=\"h-6 w-6 text-purple-600 dark:text-purple-400\" />\r\n            </div>\r\n          </div>\r\n          <div className=\"mt-4 flex items-center text-xs\">\r\n            <span\r\n              className={`flex items-center ${\r\n                growth.insurancesGrowth >= 0\r\n                  ? \"text-green-600 dark:text-green-400\"\r\n                  : \"text-red-600 dark:text-red-400\"\r\n              }`}\r\n            >\r\n              {growth.insurancesGrowth >= 0 ? (\r\n                <TrendingUp className=\"h-3 w-3 mr-1\" />\r\n              ) : (\r\n                <TrendingDown className=\"h-3 w-3 mr-1\" />\r\n              )}\r\n              {Math.abs(growth.insurancesGrowth)}%\r\n            </span>\r\n            <span className=\"text-neutral-500 dark:text-neutral-400 ml-2\">\r\n              desde o período anterior\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Charts Row */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\" id=\"gradedashboards\">\r\n        {/* Gender Distribution Chart */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center\">\r\n              <Users className=\"h-5 w-5 mr-2 text-people-500 dark:text-people-400\" />\r\n              Distribuição por Gênero\r\n            </h3>\r\n          </div>\r\n          <div className=\"h-80\">\r\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n              {chartType === 'pie' ? (\r\n                <PieChart>\r\n                  <Pie\r\n                    data={genderDistribution}\r\n                    cx=\"50%\"\r\n                    cy=\"50%\"\r\n                    innerRadius={60}\r\n                    outerRadius={90}\r\n                    fill=\"#8884d8\"\r\n                    paddingAngle={5}\r\n                    dataKey=\"value\"\r\n                    labelLine={true}\r\n                    label={({ name }) => name}\r\n                  >\r\n                    {genderDistribution.map((entry, index) => (\r\n                      <Cell key={`cell-${index}`} fill={GENDER_COLORS[index % GENDER_COLORS.length]} />\r\n                    ))}\r\n                  </Pie>\r\n                  <Tooltip content={<CustomTooltip />} />\r\n                  <Legend />\r\n                </PieChart>\r\n              ) : (\r\n                <BarChart\r\n                  data={genderDistribution}\r\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\r\n                >\r\n                  <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f3f4f6\" className=\"dark:stroke-gray-700\" />\r\n                  <XAxis\r\n                    dataKey=\"name\"\r\n                    tick={{ fontSize: 12, fill: \"#71717a\", className: \"dark:fill-gray-400\" }}\r\n                  />\r\n                  <YAxis\r\n                    tick={{ fontSize: 12, fill: \"#71717a\", className: \"dark:fill-gray-400\" }}\r\n                  />\r\n                  <Tooltip content={<CustomTooltip />} />\r\n                  <Bar dataKey=\"value\" name=\"Quantidade\">\r\n                    {genderDistribution.map((entry, index) => (\r\n                      <Cell key={`cell-${index}`} fill={GENDER_COLORS[index % GENDER_COLORS.length]} />\r\n                    ))}\r\n                  </Bar>\r\n                </BarChart>\r\n              )}\r\n            </ResponsiveContainer>\r\n          </div>\r\n\r\n          {chartType === \"pie\" && (\r\n            <div className=\"mt-4 grid grid-cols-1 gap-2\">\r\n              {genderDistribution.map((item, index) => (\r\n                <div key={index} className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center\">\r\n                    <div\r\n                      className=\"h-3 w-3 rounded-full mr-2\"\r\n                      style={{ backgroundColor: GENDER_COLORS[index % GENDER_COLORS.length] }}\r\n                    ></div>\r\n                    <span className=\"text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]\" title={item.name}>\r\n                      {item.name}\r\n                    </span>\r\n                  </div>\r\n                  <span className=\"text-sm font-medium text-neutral-800 dark:text-neutral-100\">\r\n                    {item.value}\r\n                  </span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Top Clients Chart */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center\">\r\n              <UserPlus className=\"h-5 w-5 mr-2 text-people-500 dark:text-people-400\" />\r\n              Clientes com Mais Pacientes\r\n            </h3>\r\n          </div>\r\n          <div className=\"h-80\">\r\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n              {chartType === 'bar' ? (\r\n                <BarChart\r\n                  data={topClients}\r\n                  margin={{ top: 5, right: 30, left: 20, bottom: 50 }}\r\n                >\r\n                  <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f3f4f6\" className=\"dark:stroke-gray-700\" />\r\n                  <XAxis\r\n                    dataKey=\"name\"\r\n                    tick={{ fontSize: 12, fill: \"#71717a\", className: \"dark:fill-gray-400\" }}\r\n                    angle={-45}\r\n                    textAnchor=\"end\"\r\n                    height={60}\r\n                  />\r\n                  <YAxis\r\n                    tick={{ fontSize: 12, fill: \"#71717a\", className: \"dark:fill-gray-400\" }}\r\n                  />\r\n                  <Tooltip content={<CustomTooltip />} />\r\n                  <Bar dataKey=\"count\" name=\"Pacientes\" fill=\"#f97316\">\r\n                    {topClients.map((_, index) => (\r\n                      <Cell key={`cell-${index}`} fill={CLIENT_COLORS[index % CLIENT_COLORS.length]} />\r\n                    ))}\r\n                  </Bar>\r\n                </BarChart>\r\n              ) : (\r\n                <PieChart>\r\n                  <Pie\r\n                    data={topClients}\r\n                    cx=\"50%\"\r\n                    cy=\"50%\"\r\n                    innerRadius={60}\r\n                    outerRadius={90}\r\n                    fill=\"#8884d8\"\r\n                    paddingAngle={5}\r\n                    dataKey=\"count\"\r\n                    nameKey=\"name\"\r\n                    label={({ name }) => name}\r\n                  >\r\n                    {topClients.map((entry, index) => (\r\n                      <Cell key={`cell-${index}`} fill={CLIENT_COLORS[index % CLIENT_COLORS.length]} />\r\n                    ))}\r\n                  </Pie>\r\n                  <Tooltip content={<CustomTooltip />} />\r\n                </PieChart>\r\n              )}\r\n            </ResponsiveContainer>\r\n          </div>\r\n\r\n          {chartType === \"pie\" && (\r\n            <div className=\"mt-4 grid grid-cols-1 gap-2\">\r\n              {topClients.map((client, index) => (\r\n                <div key={index} className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center\">\r\n                    <div\r\n                      className=\"h-3 w-3 rounded-full mr-2\"\r\n                      style={{ backgroundColor: CLIENT_COLORS[index % CLIENT_COLORS.length] }}\r\n                    ></div>\r\n                    <span className=\"text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]\" title={client.name}>\r\n                      {client.name}\r\n                    </span>\r\n                  </div>\r\n                  <span className=\"text-sm font-medium text-neutral-800 dark:text-neutral-100\">\r\n                    {client.count}\r\n                  </span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Insurance Distribution Chart */}\r\n        <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n          <div className=\"flex items-center justify-between mb-6\">\r\n            <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center\">\r\n              <CreditCard className=\"h-5 w-5 mr-2 text-people-500 dark:text-people-400\" />\r\n              Pacientes com Convênio\r\n            </h3>\r\n          </div>\r\n          <div className=\"h-80\">\r\n            <ResponsiveContainer width=\"100%\" height=\"100%\">\r\n              {chartType === 'pie' ? (\r\n                <PieChart>\r\n                  <Pie\r\n                    data={insuranceDistribution}\r\n                    cx=\"50%\"\r\n                    cy=\"50%\"\r\n                    innerRadius={60}\r\n                    outerRadius={90}\r\n                    fill=\"#8884d8\"\r\n                    paddingAngle={5}\r\n                    dataKey=\"value\"\r\n                    labelLine={true}\r\n                    label={({ name }) => name}\r\n                  >\r\n                    {insuranceDistribution.map((entry, index) => (\r\n                      <Cell key={`cell-${index}`} fill={INSURANCE_COLORS[index % INSURANCE_COLORS.length]} />\r\n                    ))}\r\n                  </Pie>\r\n                  <Tooltip content={<CustomTooltip />} />\r\n                </PieChart>\r\n              ) : (\r\n                <BarChart\r\n                  data={insuranceDistribution}\r\n                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}\r\n                >\r\n                  <CartesianGrid strokeDasharray=\"3 3\" stroke=\"#f3f4f6\" className=\"dark:stroke-gray-700\" />\r\n                  <XAxis\r\n                    dataKey=\"name\"\r\n                    tick={{ fontSize: 12, fill: \"#71717a\", className: \"dark:fill-gray-400\" }}\r\n                  />\r\n                  <YAxis\r\n                    tick={{ fontSize: 12, fill: \"#71717a\", className: \"dark:fill-gray-400\" }}\r\n                  />\r\n                  <Tooltip content={<CustomTooltip />} />\r\n                  <Bar dataKey=\"value\" name=\"Quantidade\">\r\n                    {insuranceDistribution.map((entry, index) => (\r\n                      <Cell key={`cell-${index}`} fill={INSURANCE_COLORS[index % INSURANCE_COLORS.length]} />\r\n                    ))}\r\n                  </Bar>\r\n                </BarChart>\r\n              )}\r\n            </ResponsiveContainer>\r\n          </div>\r\n\r\n          {chartType === \"pie\" && (\r\n            <div className=\"mt-4 grid grid-cols-1 gap-2\">\r\n              {insuranceDistribution.map((item, index) => (\r\n                <div key={index} className=\"flex items-center justify-between\">\r\n                  <div className=\"flex items-center\">\r\n                    <div\r\n                      className=\"h-3 w-3 rounded-full mr-2\"\r\n                      style={{ backgroundColor: INSURANCE_COLORS[index % INSURANCE_COLORS.length] }}\r\n                    ></div>\r\n                    <span className=\"text-sm text-neutral-700 dark:text-neutral-300 truncate max-w-[150px]\" title={item.name}>\r\n                      {item.name}\r\n                    </span>\r\n                  </div>\r\n                  <span className=\"text-sm font-medium text-neutral-800 dark:text-neutral-100\">\r\n                    {item.value}\r\n                  </span>\r\n                </div>\r\n              ))}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recent Persons Table */}\r\n      <div className=\"bg-white dark:bg-gray-800 p-6 rounded-xl shadow-md border border-people-border dark:border-people-border-dark\">\r\n        <div className=\"flex items-center justify-between mb-6\">\r\n          <h3 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-100 flex items-center\">\r\n            <Clock className=\"h-5 w-5 mr-2 text-people-500 dark:text-people-400\" />\r\n            Pacientes Recentes\r\n          </h3>\r\n        </div>\r\n        <ModuleTable\r\n          moduleColor=\"people\"\r\n          columns={[\r\n            { header: 'Nome', field: 'fullName', width: '25%' },\r\n            { header: 'Cliente', field: 'clientName', width: '25%' },\r\n            { header: 'Contato', field: 'contact', width: '25%' },\r\n            { header: 'Cadastro', field: 'createdAt', width: '15%' },\r\n            { header: 'Relação', field: 'relationship', width: '10%' }\r\n          ]}\r\n          data={recentPersons}\r\n          isLoading={isLoading}\r\n          emptyMessage=\"Nenhum paciente encontrado\"\r\n          emptyIcon={<User size={24} />}\r\n          tableId=\"recent-persons-table\"\r\n          defaultSortField=\"createdAt\"\r\n          defaultSortDirection=\"desc\"\r\n          renderRow={(person, _index, moduleColors, visibleColumns) => (\r\n            <tr key={person.id} className={moduleColors.hoverBg}>\r\n              {visibleColumns.includes('fullName') && (\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"flex items-center\">\r\n                    <div className=\"flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center overflow-hidden\">\r\n                      {person.profileImageUrl ? (\r\n                        <img\r\n                          src={personsService.getProfileImageUrl(person.id, person.profileImageUrl)}\r\n                          alt={person.fullName}\r\n                          className=\"h-10 w-10 rounded-full object-cover\"\r\n                        />\r\n                      ) : (\r\n                        <User className=\"h-5 w-5 text-gray-500 dark:text-gray-400\" />\r\n                      )}\r\n                    </div>\r\n                    <div className=\"ml-4\">\r\n                      <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                        {person.fullName}\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </td>\r\n              )}\r\n\r\n              {visibleColumns.includes('clientName') && (\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"text-sm text-gray-500 dark:text-gray-400 flex items-center\">\r\n                    <UserPlus className=\"h-4 w-4 mr-2 text-gray-400 dark:text-gray-500\" />\r\n                    {person.clientName}\r\n                  </div>\r\n                </td>\r\n              )}\r\n\r\n              {visibleColumns.includes('contact') && (\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                    {person.email && (\r\n                      <div className=\"flex items-center mb-1\">\r\n                        <Mail className=\"h-4 w-4 mr-2 text-gray-400 dark:text-gray-500\" />\r\n                        {person.email}\r\n                      </div>\r\n                    )}\r\n                    {person.phone && (\r\n                      <div className=\"flex items-center\">\r\n                        <Phone className=\"h-4 w-4 mr-2 text-gray-400 dark:text-gray-500\" />\r\n                        {person.phone}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </td>\r\n              )}\r\n\r\n              {visibleColumns.includes('createdAt') && (\r\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400\">\r\n                  {formatDate(person.createdAt)}\r\n                </td>\r\n              )}\r\n\r\n              {visibleColumns.includes('relationship') && (\r\n                <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                  <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                    {person.relationship}\r\n                  </div>\r\n                </td>\r\n              )}\r\n            </tr>\r\n          )}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default PeopleDashboard;\r\n"], "names": [], "mappings": ";;;;AAEA;AAkCA;AACA;AACA;AACA;AACA;AANA;AACA;AAnBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AAsBA;AAtBA;AAAA;;;AAhBA;;;;;;;;;;;AA0CA,yBAAyB;AACzB,MAAM,gBAAgB;IAAC;IAAW;IAAW;CAAU;AACvD,MAAM,gBAAgB;IAAC;IAAW;IAAW;IAAW;IAAW;CAAU;AAC7E,MAAM,mBAAmB;IAAC;IAAW;CAAU;AAE/C,MAAM,kBAAkB;;IACtB,kCAAkC;IAClC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD,OAAO;YACL,cAAc;YACd,eAAe;YACf,cAAc;YACd,eAAe;YACf,iBAAiB;QACnB;QACA,QAAQ;YACN,eAAe;YACf,eAAe;YACf,kBAAkB;QACpB;QACA,oBAAoB,EAAE;QACtB,YAAY,EAAE;QACd,uBAAuB,EAAE;QACzB,eAAe,EAAE;IACnB;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,iBAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEvB,6CAA6C;IAC7C,MAAM,oBAAoB,OAAO,iBAAiB,MAAM;QACtD,aAAa;QACb,IAAI;YACF,MAAM,OAAO,MAAM,wKAAA,CAAA,yBAAsB,CAAC,sBAAsB,CAAC;gBAC/D,QAAQ;YACV;YACA,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,aAAa;QACf;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAqB,CAAC;QAC1B,MAAM,YAAY,EAAE,MAAM,CAAC,KAAK;QAChC,UAAU;QACV,kBAAkB;IACpB;IAEA,yCAAyC;IACzC,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,6CAA6C;IAC7C,MAAM,eAAe,OAAO;QAC1B,eAAe;QACf,IAAI;YACF,MAAM,wKAAA,CAAA,yBAAsB,CAAC,mBAAmB,CAAC;gBAAE;YAAO,GAAG;QAC/D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,gBAAgB;IAChB,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,YAAY,OAAO;QACxB,IAAI;YACF,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,aAAa,cAAc;gBAAE,QAAQ,oJAAA,CAAA,OAAI;YAAC;QACnE,EAAE,OAAO,OAAO;YACd,OAAO;QACT;IACF;IAEA,uDAAuD;IACvD,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE;QAC/C,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;YACvC,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;oBAC5D,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,6LAAC;4BAAc,OAAO;gCAAE,OAAO,MAAM,KAAK;4BAAC;;gCACxC,MAAM,IAAI;gCAAC;gCAAG,MAAM,KAAK;;2BADpB;;;;;;;;;;;QAMhB;QACA,OAAO;IACT;IAEA,0BAA0B;IAC1B,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,kBAAkB,EAAE,UAAU,EAAE,qBAAqB,EAAE,aAAa,EAAE,GAAG;IAEhG,iBAAiB;IACjB,QAAQ,GAAG,CAAC,gBAAgB;IAE5B,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,+NAAA,CAAA,kBAAe;gCAAC,MAAM;gCAAI,WAAU;;;;;;4BAA8C;;;;;;;kCAIrF,6LAAC,wIAAA,CAAA,UAAU;wBACT,UAAU;wBACV,aAAa;wBACb,UAAU;wBACV,WAAU;;;;;;;;;;;;0BAKd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,MAAM;;;;;;sDAChB,6LAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAExC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,OAAO;4CACP,UAAU;4CACV,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAQ;;;;;;8DACtB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6LAAC;oDAAO,OAAM;8DAAO;;;;;;;;;;;;sDAEvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;gDAAwB,OAAM;gDAA6B,SAAQ;gDAAY,MAAK;gDAAe,eAAY;0DAC5H,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjK,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,IAAI,EACd,cAAc,QACZ,6BACA,iEACF;wCACF,SAAS,IAAM,gBAAgB;wCAC/B,OAAM;kDAEN,cAAA,6LAAC,mOAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6LAAC;wCACC,WAAW,CAAC,IAAI,EACd,cAAc,QACZ,6BACA,iEACF;wCACF,SAAS,IAAM,gBAAgB;wCAC/B,OAAM;kDAEN,cAAA,6LAAC,iNAAA,CAAA,WAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6D;;;;;;0DAG1E,6LAAC;gDAAG,WAAU;0DACX,MAAM,YAAY;;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,kBAAkB,EAC5B,OAAO,aAAa,IAAI,IACpB,uCACA,kCACJ;;4CAED,OAAO,aAAa,IAAI,kBACvB,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;qEAEtB,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAEzB,KAAK,GAAG,CAAC,OAAO,aAAa;4CAAE;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;kDAA8C;;;;;;;;;;;;;;;;;;kCAOlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6D;;;;;;0DAG1E,6LAAC;gDAAG,WAAU;0DACX,MAAM,aAAa;;;;;;;;;;;;kDAGxB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGpB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CACb,MAAM,YAAY,GAAG,IAClB,GAAG,KAAK,KAAK,CAAC,AAAC,MAAM,aAAa,GAAG,MAAM,YAAY,GAAI,KAAK,UAAU,CAAC,GAC3E;;;;;;;;;;;;;;;;;kCAMV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6D;;;;;;0DAG1E,6LAAC;gDAAG,WAAU;0DACX,MAAM,YAAY;;;;;;;;;;;;kDAGvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,kBAAkB,EAC5B,OAAO,aAAa,IAAI,IACpB,uCACA,kCACJ;;4CAED,OAAO,aAAa,IAAI,kBACvB,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;qEAEtB,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAEzB,KAAK,GAAG,CAAC,OAAO,aAAa;4CAAE;;;;;;;kDAElC,6LAAC;wCAAK,WAAU;kDAA8C;;;;;;;;;;;;;;;;;;kCAOlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6D;;;;;;0DAG1E,6LAAC;gDAAG,WAAU;0DACX,MAAM,aAAa;;;;;;;;;;;;kDAGxB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CACb,MAAM,YAAY,GAAG,IAClB,GAAG,KAAK,KAAK,CAAC,AAAC,MAAM,aAAa,GAAG,MAAM,YAAY,GAAI,KAAK,UAAU,CAAC,GAC3E;;;;;;;;;;;;;;;;;kCAMV,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAA6D;;;;;;0DAG1E,6LAAC;gDAAG,WAAU;0DACX,MAAM,eAAe;;;;;;;;;;;;kDAG1B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAW,CAAC,kBAAkB,EAC5B,OAAO,gBAAgB,IAAI,IACvB,uCACA,kCACJ;;4CAED,OAAO,gBAAgB,IAAI,kBAC1B,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;qEAEtB,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;4CAEzB,KAAK,GAAG,CAAC,OAAO,gBAAgB;4CAAE;;;;;;;kDAErC,6LAAC;wCAAK,WAAU;kDAA8C;;;;;;;;;;;;;;;;;;;;;;;;0BAQpE,6LAAC;gBAAI,WAAU;gBAAwC,IAAG;;kCAExD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAsD;;;;;;;;;;;;0CAI3E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAO;8CACtC,cAAc,sBACb,6LAAC,uJAAA,CAAA,WAAQ;;0DACP,6LAAC,kJAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,aAAa;gDACb,MAAK;gDACL,cAAc;gDACd,SAAQ;gDACR,WAAW;gDACX,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK;0DAEpB,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;uDAAlE,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0DAG9B,6LAAC,0JAAA,CAAA,UAAO;gDAAC,uBAAS,6LAAC;;;;;;;;;;0DACnB,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;6DAGT,6LAAC,uJAAA,CAAA,WAAQ;wCACP,MAAM;wCACN,QAAQ;4CAAE,KAAK;4CAAG,OAAO;4CAAI,MAAM;4CAAI,QAAQ;wCAAE;;0DAEjD,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;gDAAM,QAAO;gDAAU,WAAU;;;;;;0DAChE,6LAAC,wJAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,MAAM;oDAAE,UAAU;oDAAI,MAAM;oDAAW,WAAW;gDAAqB;;;;;;0DAEzE,6LAAC,wJAAA,CAAA,QAAK;gDACJ,MAAM;oDAAE,UAAU;oDAAI,MAAM;oDAAW,WAAW;gDAAqB;;;;;;0DAEzE,6LAAC,0JAAA,CAAA,UAAO;gDAAC,uBAAS,6LAAC;;;;;;;;;;0DACnB,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAQ,MAAK;0DACvB,mBAAmB,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;uDAAlE,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQrC,cAAc,uBACb,6LAAC;gCAAI,WAAU;0CACZ,mBAAmB,GAAG,CAAC,CAAC,MAAM,sBAC7B,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;wDAAC;;;;;;kEAExE,6LAAC;wDAAK,WAAU;wDAAwE,OAAO,KAAK,IAAI;kEACrG,KAAK,IAAI;;;;;;;;;;;;0DAGd,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;;uCAXL;;;;;;;;;;;;;;;;kCAoBlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,iNAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAsD;;;;;;;;;;;;0CAI9E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAO;8CACtC,cAAc,sBACb,6LAAC,uJAAA,CAAA,WAAQ;wCACP,MAAM;wCACN,QAAQ;4CAAE,KAAK;4CAAG,OAAO;4CAAI,MAAM;4CAAI,QAAQ;wCAAG;;0DAElD,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;gDAAM,QAAO;gDAAU,WAAU;;;;;;0DAChE,6LAAC,wJAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,MAAM;oDAAE,UAAU;oDAAI,MAAM;oDAAW,WAAW;gDAAqB;gDACvE,OAAO,CAAC;gDACR,YAAW;gDACX,QAAQ;;;;;;0DAEV,6LAAC,wJAAA,CAAA,QAAK;gDACJ,MAAM;oDAAE,UAAU;oDAAI,MAAM;oDAAW,WAAW;gDAAqB;;;;;;0DAEzE,6LAAC,0JAAA,CAAA,UAAO;gDAAC,uBAAS,6LAAC;;;;;;;;;;0DACnB,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAQ,MAAK;gDAAY,MAAK;0DACxC,WAAW,GAAG,CAAC,CAAC,GAAG,sBAClB,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;uDAAlE,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;6DAKhC,6LAAC,uJAAA,CAAA,WAAQ;;0DACP,6LAAC,kJAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,aAAa;gDACb,MAAK;gDACL,cAAc;gDACd,SAAQ;gDACR,SAAQ;gDACR,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK;0DAEpB,WAAW,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;uDAAlE,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0DAG9B,6LAAC,0JAAA,CAAA,UAAO;gDAAC,uBAAS,6LAAC;;;;;;;;;;;;;;;;;;;;;;;;;;4BAM1B,cAAc,uBACb,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,QAAQ,sBACvB,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,aAAa,CAAC,QAAQ,cAAc,MAAM,CAAC;wDAAC;;;;;;kEAExE,6LAAC;wDAAK,WAAU;wDAAwE,OAAO,OAAO,IAAI;kEACvG,OAAO,IAAI;;;;;;;;;;;;0DAGhB,6LAAC;gDAAK,WAAU;0DACb,OAAO,KAAK;;;;;;;uCAXP;;;;;;;;;;;;;;;;kCAoBlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;wCAAsD;;;;;;;;;;;;0CAIhF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAO,QAAO;8CACtC,cAAc,sBACb,6LAAC,uJAAA,CAAA,WAAQ;;0DACP,6LAAC,kJAAA,CAAA,MAAG;gDACF,MAAM;gDACN,IAAG;gDACH,IAAG;gDACH,aAAa;gDACb,aAAa;gDACb,MAAK;gDACL,cAAc;gDACd,SAAQ;gDACR,WAAW;gDACX,OAAO,CAAC,EAAE,IAAI,EAAE,GAAK;0DAEpB,sBAAsB,GAAG,CAAC,CAAC,OAAO,sBACjC,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,gBAAgB,CAAC,QAAQ,iBAAiB,MAAM,CAAC;uDAAxE,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;0DAG9B,6LAAC,0JAAA,CAAA,UAAO;gDAAC,uBAAS,6LAAC;;;;;;;;;;;;;;;6DAGrB,6LAAC,uJAAA,CAAA,WAAQ;wCACP,MAAM;wCACN,QAAQ;4CAAE,KAAK;4CAAG,OAAO;4CAAI,MAAM;4CAAI,QAAQ;wCAAE;;0DAEjD,6LAAC,gKAAA,CAAA,gBAAa;gDAAC,iBAAgB;gDAAM,QAAO;gDAAU,WAAU;;;;;;0DAChE,6LAAC,wJAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,MAAM;oDAAE,UAAU;oDAAI,MAAM;oDAAW,WAAW;gDAAqB;;;;;;0DAEzE,6LAAC,wJAAA,CAAA,QAAK;gDACJ,MAAM;oDAAE,UAAU;oDAAI,MAAM;oDAAW,WAAW;gDAAqB;;;;;;0DAEzE,6LAAC,0JAAA,CAAA,UAAO;gDAAC,uBAAS,6LAAC;;;;;;;;;;0DACnB,6LAAC,sJAAA,CAAA,MAAG;gDAAC,SAAQ;gDAAQ,MAAK;0DACvB,sBAAsB,GAAG,CAAC,CAAC,OAAO,sBACjC,6LAAC,uJAAA,CAAA,OAAI;wDAAuB,MAAM,gBAAgB,CAAC,QAAQ,iBAAiB,MAAM,CAAC;uDAAxE,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;4BAQrC,cAAc,uBACb,6LAAC;gCAAI,WAAU;0CACZ,sBAAsB,GAAG,CAAC,CAAC,MAAM,sBAChC,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,iBAAiB,gBAAgB,CAAC,QAAQ,iBAAiB,MAAM,CAAC;wDAAC;;;;;;kEAE9E,6LAAC;wDAAK,WAAU;wDAAwE,OAAO,KAAK,IAAI;kEACrG,KAAK,IAAI;;;;;;;;;;;;0DAGd,6LAAC;gDAAK,WAAU;0DACb,KAAK,KAAK;;;;;;;uCAXL;;;;;;;;;;;;;;;;;;;;;;0BAqBpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAsD;;;;;;;;;;;;kCAI3E,6LAAC,mLAAA,CAAA,cAAW;wBACV,aAAY;wBACZ,SAAS;4BACP;gCAAE,QAAQ;gCAAQ,OAAO;gCAAY,OAAO;4BAAM;4BAClD;gCAAE,QAAQ;gCAAW,OAAO;gCAAc,OAAO;4BAAM;4BACvD;gCAAE,QAAQ;gCAAW,OAAO;gCAAW,OAAO;4BAAM;4BACpD;gCAAE,QAAQ;gCAAY,OAAO;gCAAa,OAAO;4BAAM;4BACvD;gCAAE,QAAQ;gCAAW,OAAO;gCAAgB,OAAO;4BAAM;yBAC1D;wBACD,MAAM;wBACN,WAAW;wBACX,cAAa;wBACb,yBAAW,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;;;;;;wBACvB,SAAQ;wBACR,kBAAiB;wBACjB,sBAAqB;wBACrB,WAAW,CAAC,QAAQ,QAAQ,cAAc,+BACxC,6LAAC;gCAAmB,WAAW,aAAa,OAAO;;oCAChD,eAAe,QAAQ,CAAC,6BACvB,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,OAAO,eAAe,iBACrB,6LAAC;wDACC,KAAK,gKAAA,CAAA,iBAAc,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,OAAO,eAAe;wDACxE,KAAK,OAAO,QAAQ;wDACpB,WAAU;;;;;+EAGZ,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;8DAGpB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,OAAO,QAAQ;;;;;;;;;;;;;;;;;;;;;;oCAOzB,eAAe,QAAQ,CAAC,+BACvB,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDACnB,OAAO,UAAU;;;;;;;;;;;;oCAKvB,eAAe,QAAQ,CAAC,4BACvB,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,KAAK,kBACX,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDACf,OAAO,KAAK;;;;;;;gDAGhB,OAAO,KAAK,kBACX,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,OAAO,KAAK;;;;;;;;;;;;;;;;;;oCAOtB,eAAe,QAAQ,CAAC,8BACvB,6LAAC;wCAAG,WAAU;kDACX,WAAW,OAAO,SAAS;;;;;;oCAI/B,eAAe,QAAQ,CAAC,iCACvB,6LAAC;wCAAG,WAAU;kDACZ,cAAA,6LAAC;4CAAI,WAAU;sDACZ,OAAO,YAAY;;;;;;;;;;;;+BA7DnB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;AAuE9B;GA9pBM;;QAwBa,iIAAA,CAAA,UAAO;;;KAxBpB;uCAgqBS"}}, {"offset": {"line": 8229, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8235, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/dashboard/index.js"], "sourcesContent": ["// src/app/modules/people/dashboard/index.js\r\nimport PeopleDashboard from './PeopleDashboard';\r\n\r\nexport { PeopleDashboard };\r\n"], "names": [], "mappings": "AAAA,4CAA4C"}}, {"offset": {"line": 8242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8257, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/modules/people/index.js"], "sourcesContent": ["// src/app/modules/people/index.js\r\nimport PersonsPage from './PersonsPage/PersonsPage';\r\nimport ClientsPage from './ClientsPage/ClientsPage';\r\nimport InsuranceLimitsPage from './InsuranceLimitsPage/InsuranceLimitsPage';\r\nimport { IntroductionPage } from './introduction';\r\nimport { PeopleDashboard } from './dashboard';\r\nimport personsService from './services/personsService';\r\nimport clientsService from './services/clientsService';\r\nimport peopleDashboardService from './services/peopleDashboardService';\r\n\r\n// Export the components and services\r\nexport {\r\n  PersonsPage,\r\n  ClientsPage,\r\n  InsuranceLimitsPage,\r\n  IntroductionPage,\r\n  PeopleDashboard,\r\n  personsService,\r\n  clientsService,\r\n  peopleDashboardService\r\n};"], "names": [], "mappings": "AAAA,kCAAkC"}}, {"offset": {"line": 8271, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 8303, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/Programa%C3%A7%C3%A3o/high-tide-systems-frontend/src/app/dashboard/people/clients/page.js"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ClientsPage } from '@/app/modules/people';\r\n\r\nexport default function ClientsRoute() {\r\n  return <ClientsPage />;\r\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAIe,SAAS;IACtB,qBAAO,6LAAC,0MAAA,CAAA,cAAW;;;;;AACrB;KAFwB"}}, {"offset": {"line": 8325, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}