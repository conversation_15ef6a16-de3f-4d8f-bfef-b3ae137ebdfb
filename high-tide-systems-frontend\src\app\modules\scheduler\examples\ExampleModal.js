'use client';

import React, { useState } from 'react';
import { Calendar, Users, MapPin, Tag, Clock } from 'lucide-react';
import ModuleModal from '@/components/ui/ModuleModal';
import ModalButton from '@/components/ui/ModalButton';
import { ModuleSelect, ModuleFormGroup } from '@/components/ui';

const ExampleModal = ({ isOpen, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    date: '',
    time: '',
    location: '',
    provider: '',
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulação de envio de dados
    await new Promise(resolve => setTimeout(resolve, 1500));

    setIsLoading(false);
    onClose();
  };

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="scheduler"
        onClick={onClose}
        disabled={isLoading}
      >
        Cancelar
      </ModalButton>

      <ModalButton
        variant="primary"
        moduleColor="scheduler"
        onClick={handleSubmit}
        isLoading={isLoading}
      >
        Salvar
      </ModalButton>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title="Novo Agendamento"
      icon={<Calendar size={22} />}
      moduleColor="scheduler"
      size="md"
      animateExit={true}
      footer={modalFooter}
    >
      <form className="space-y-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Título
            </label>
            <input
              type="text"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:focus:ring-purple-400 bg-white dark:bg-gray-700 dark:text-gray-200"
              placeholder="Digite o título do agendamento"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Descrição
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={3}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:focus:ring-purple-400 bg-white dark:bg-gray-700 dark:text-gray-200"
              placeholder="Digite uma descrição (opcional)"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-1">
              <Clock size={16} />
              Data
            </label>
            <input
              type="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:focus:ring-purple-400 bg-white dark:bg-gray-700 dark:text-gray-200"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 flex items-center gap-1">
              <Clock size={16} />
              Horário
            </label>
            <input
              type="time"
              name="time"
              value={formData.time}
              onChange={handleChange}
              className="w-full rounded-md border border-gray-300 dark:border-gray-600 px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:focus:ring-purple-400 bg-white dark:bg-gray-700 dark:text-gray-200"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <ModuleFormGroup
            moduleColor="scheduler"
            label="Profissional"
            htmlFor="provider"
            icon={<Users size={16} />}
          >
            <ModuleSelect
              moduleColor="scheduler"
              id="provider"
              name="provider"
              value={formData.provider}
              onChange={handleChange}
              placeholder="Selecione um profissional"
            >
              <option value="">Selecione um profissional</option>
              <option value="1">Dr. João Silva</option>
              <option value="2">Dra. Maria Santos</option>
              <option value="3">Dr. Carlos Oliveira</option>
            </ModuleSelect>
          </ModuleFormGroup>

          <ModuleFormGroup
            moduleColor="scheduler"
            label="Local"
            htmlFor="location"
            icon={<MapPin size={16} />}
          >
            <ModuleSelect
              moduleColor="scheduler"
              id="location"
              name="location"
              value={formData.location}
              onChange={handleChange}
              placeholder="Selecione um local"
            >
              <option value="">Selecione um local</option>
              <option value="1">Consultório 101</option>
              <option value="2">Consultório 102</option>
              <option value="3">Sala de Reuniões</option>
            </ModuleSelect>
          </ModuleFormGroup>
        </div>
      </form>
    </ModuleModal>
  );
};

export default ExampleModal;
