// src/services/chat/chatService.js
const prisma = require('../../utils/prisma');
const { NotFoundError, ForbiddenError } = require('../../utils/errors');

/**
 * Cria uma nova conversa
 * @param {Object} data - Dados da conversa
 * @param {string} userId - ID do usuário que está criando a conversa
 * @returns {Promise<Object>} - Conversa criada
 */
const createConversation = async (data, userId) => {
  try {
    // Validar se todos os participantes são da mesma empresa
    const creator = await prisma.user.findUnique({
      where: { id: userId },
      select: { companyId: true, branchId: true, role: true }
    });

    // Verificar se o usuário existe
    if (!creator) {
      throw new ForbiddenError('Usuário não encontrado');
    }

    // System Admin pode criar conversas mesmo sem empresa associada
    const isSystemAdmin = creator.role === 'SYSTEM_ADMIN';

    // Para outros usuários, verificar se tem empresa associada
    if (!isSystemAdmin && !creator.companyId) {
      throw new ForbiddenError('Usuário não possui empresa associada');
    }

    // Se a conversa for restrita a uma unidade e o usuário não for system admin, verificar se todos os participantes são da mesma unidade
    if (!isSystemAdmin && data.branchId) {
      const participantsFromOtherBranches = await prisma.user.count({
        where: {
          id: { in: data.participantIds || [] },
          OR: [
            { branchId: { not: data.branchId } },
            { branchId: null }
          ]
        }
      });

      if (participantsFromOtherBranches > 0) {
        throw new ForbiddenError('Alguns participantes não pertencem à mesma unidade');
      }
    }

    // Verificar se já existe uma conversa individual entre os mesmos participantes
    if (data.type === 'INDIVIDUAL' && data.participantIds?.length === 1) {
      const existingConversation = await findIndividualConversation(userId, data.participantIds[0]);

      if (existingConversation) {
        return existingConversation;
      }
    }

    // Buscar a primeira empresa disponível para system admin
    let companyId = creator.companyId;

    // Se for system admin e não tiver empresa associada, buscar a primeira empresa disponível
    if (isSystemAdmin && !companyId) {
      const firstCompany = await prisma.company.findFirst({
        where: { active: true },
        select: { id: true }
      });

      if (!firstCompany) {
        throw new ForbiddenError('Não há empresas disponíveis para criar a conversa');
      }

      companyId = firstCompany.id;
    }

    // Criar a conversa
    const conversation = await prisma.conversation.create({
      data: {
        type: data.type,
        title: data.title,
        companyId: companyId, // Usar a empresa do usuário ou a primeira empresa disponível
        branchId: isSystemAdmin ? null : (data.branchId || creator.branchId),
        createdById: userId,
        participants: {
          create: [
            // Adicionar o criador como participante e admin
            {
              userId: userId,
              isAdmin: true
            },
            // Adicionar os outros participantes
            ...(data.participantIds || []).map(participantId => ({
              userId: participantId,
              isAdmin: false
            }))
          ]
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        }
      }
    });

    return conversation;
  } catch (error) {
    console.error('Error creating conversation:', error);
    throw error;
  }
};

/**
 * Busca uma conversa individual entre dois usuários
 * @param {string} userId1 - ID do primeiro usuário
 * @param {string} userId2 - ID do segundo usuário
 * @returns {Promise<Object|null>} - Conversa encontrada ou null
 */
const findIndividualConversation = async (userId1, userId2) => {
  try {
    // Buscar todas as conversas individuais do usuário 1
    const user1Conversations = await prisma.conversation.findMany({
      where: {
        type: 'INDIVIDUAL',
        participants: {
          some: {
            userId: userId1,
            leftAt: null
          }
        }
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        }
      }
    });

    // Filtrar as conversas que também têm o usuário 2 como participante
    const sharedConversation = user1Conversations.find(conversation => {
      return conversation.participants.some(participant =>
        participant.userId === userId2 && participant.leftAt === null
      );
    });

    return sharedConversation || null;
  } catch (error) {
    console.error('Error finding individual conversation:', error);
    throw error;
  }
};

/**
 * Busca todas as conversas de um usuário
 * @param {string} userId - ID do usuário
 * @param {Object} filters - Filtros de busca
 * @returns {Promise<Array>} - Lista de conversas
 */
const getUserConversations = async (userId, filters = {}) => {
  try {
    const { search, limit = 20, offset = 0, includeMessages = true } = filters;

    // Buscar o usuário para verificar a empresa e a role
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { companyId: true, role: true }
    });

    // Verificar se o usuário existe
    if (!user) {
      throw new ForbiddenError('Usuário não encontrado');
    }

    // System Admin pode acessar conversas mesmo sem empresa associada
    const isSystemAdmin = user.role === 'SYSTEM_ADMIN';

    // Para outros usuários, verificar se tem empresa associada
    if (!isSystemAdmin && !user.companyId) {
      throw new ForbiddenError('Usuário não possui empresa associada');
    }

    // Construir a query base
    const where = {
      participants: {
        some: {
          userId,
          leftAt: null
        }
      }
    };

    // Removendo a filtragem por empresa para permitir que usuários vejam todas as conversas em que são participantes
    // Usuários devem poder ver todas as conversas em que são participantes, independentemente da empresa
    // A restrição por empresa só deve ser aplicada ao criar novas conversas

    // Adicionar filtro de busca se fornecido
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        {
          participants: {
            some: {
              user: {
                fullName: { contains: search, mode: 'insensitive' }
              }
            }
          }
        }
      ];
    }

    // Buscar as conversas
    const conversations = await prisma.conversation.findMany({
      where,
      orderBy: {
        lastMessageAt: 'desc'
      },
      skip: offset,
      take: limit,
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        },
        ...(includeMessages ? {
          messages: {
            orderBy: {
              createdAt: 'desc'
            },
            take: 1,
            include: {
              sender: {
                select: {
                  id: true,
                  fullName: true
                }
              }
            }
          }
        } : {})
      }
    });

    // Contar o total de conversas
    const total = await prisma.conversation.count({ where });

    return {
      conversations,
      total,
      limit,
      offset
    };
  } catch (error) {
    console.error('Error getting user conversations:', error);
    throw error;
  }
};

/**
 * Busca uma conversa pelo ID
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário que está buscando a conversa
 * @returns {Promise<Object>} - Conversa encontrada
 */
const getConversationById = async (conversationId, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        }
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é participante da conversa
    const isParticipant = conversation.participants.some(
      participant => participant.userId === userId && participant.leftAt === null
    );

    if (!isParticipant) {
      throw new ForbiddenError('Você não é participante desta conversa');
    }

    return conversation;
  } catch (error) {
    console.error('Error getting conversation by ID:', error);
    throw error;
  }
};

/**
 * Adiciona um participante a uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} participantId - ID do usuário a ser adicionado
 * @param {string} userId - ID do usuário que está adicionando o participante
 * @returns {Promise<Object>} - Participante adicionado
 */
const addParticipant = async (conversationId, participantId, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: true
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é administrador da conversa
    const isAdmin = conversation.participants.some(
      participant => participant.userId === userId && participant.isAdmin && participant.leftAt === null
    );

    if (!isAdmin) {
      throw new ForbiddenError('Apenas administradores podem adicionar participantes');
    }

    // Verificar se a conversa é do tipo GROUP
    if (conversation.type !== 'GROUP') {
      throw new ForbiddenError('Apenas conversas em grupo podem ter participantes adicionados');
    }

    // Verificar se o participante já está na conversa
    const existingParticipant = conversation.participants.find(
      participant => participant.userId === participantId
    );

    if (existingParticipant) {
      // Se o participante saiu da conversa, reativá-lo
      if (existingParticipant.leftAt) {
        return prisma.conversationParticipant.update({
          where: { id: existingParticipant.id },
          data: { leftAt: null },
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        });
      }

      throw new ForbiddenError('Usuário já é participante desta conversa');
    }

    // Verificar se o participante é da mesma empresa
    const [conversationUser, newParticipant] = await Promise.all([
      prisma.user.findUnique({
        where: { id: userId },
        select: { companyId: true, branchId: true, role: true }
      }),
      prisma.user.findUnique({
        where: { id: participantId },
        select: { companyId: true, branchId: true }
      })
    ]);

    if (!newParticipant) {
      throw new NotFoundError('Usuário não encontrado');
    }

    // System Admin pode adicionar qualquer usuário
    const isSystemAdmin = conversationUser.role === 'SYSTEM_ADMIN';

    // Para outros usuários, verificar se o participante é da mesma empresa
    if (!isSystemAdmin && newParticipant.companyId !== conversationUser.companyId) {
      throw new ForbiddenError('Usuário não pertence à mesma empresa');
    }

    // Se a conversa for restrita a uma unidade e o usuário não for system admin, verificar se o participante é da mesma unidade
    if (!isSystemAdmin && conversation.branchId && newParticipant.branchId !== conversation.branchId) {
      throw new ForbiddenError('Usuário não pertence à mesma unidade');
    }

    // Adicionar o participante
    const participant = await prisma.conversationParticipant.create({
      data: {
        conversationId,
        userId: participantId,
        isAdmin: false
      },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true,
            profileImageUrl: true
          }
        }
      }
    });

    return participant;
  } catch (error) {
    console.error('Error adding participant:', error);
    throw error;
  }
};

/**
 * Remove um participante de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} participantId - ID do usuário a ser removido
 * @param {string} userId - ID do usuário que está removendo o participante
 * @returns {Promise<Object>} - Participante removido
 */
const removeParticipant = async (conversationId, participantId, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: true
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é administrador da conversa ou está removendo a si mesmo
    const isAdmin = conversation.participants.some(
      participant => participant.userId === userId && participant.isAdmin && participant.leftAt === null
    );
    const isSelfRemoval = userId === participantId;

    if (!isAdmin && !isSelfRemoval) {
      throw new ForbiddenError('Apenas administradores podem remover outros participantes');
    }

    // Verificar se a conversa é do tipo GROUP
    if (conversation.type !== 'GROUP' && !isSelfRemoval) {
      throw new ForbiddenError('Apenas conversas em grupo podem ter participantes removidos');
    }

    // Buscar o participante
    const participant = conversation.participants.find(
      participant => participant.userId === participantId && participant.leftAt === null
    );

    if (!participant) {
      throw new NotFoundError('Participante não encontrado ou já removido');
    }

    // Remover o participante (marcar como saído)
    const updatedParticipant = await prisma.conversationParticipant.update({
      where: { id: participant.id },
      data: { leftAt: new Date() },
      include: {
        user: {
          select: {
            id: true,
            fullName: true,
            email: true
          }
        }
      }
    });

    return updatedParticipant;
  } catch (error) {
    console.error('Error removing participant:', error);
    throw error;
  }
};

/**
 * Atualiza uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {Object} data - Dados a serem atualizados
 * @param {string} userId - ID do usuário que está atualizando a conversa
 * @returns {Promise<Object>} - Conversa atualizada
 */
const updateConversation = async (conversationId, data, userId) => {
  try {
    // Buscar a conversa
    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
      include: {
        participants: true
      }
    });

    if (!conversation) {
      throw new NotFoundError('Conversa não encontrada');
    }

    // Verificar se o usuário é administrador da conversa
    const isAdmin = conversation.participants.some(
      participant => participant.userId === userId && participant.isAdmin && participant.leftAt === null
    );

    if (!isAdmin) {
      throw new ForbiddenError('Apenas administradores podem atualizar a conversa');
    }

    // Atualizar a conversa
    const updatedConversation = await prisma.conversation.update({
      where: { id: conversationId },
      data: {
        title: data.title,
        // Não permitir alterar companyId ou branchId
      },
      include: {
        participants: {
          include: {
            user: {
              select: {
                id: true,
                fullName: true,
                email: true,
                profileImageUrl: true
              }
            }
          }
        }
      }
    });

    return updatedConversation;
  } catch (error) {
    console.error('Error updating conversation:', error);
    throw error;
  }
};

/**
 * Verifica se um usuário é participante de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário
 * @returns {Promise<boolean>} - True se o usuário é participante, false caso contrário
 */
const isConversationParticipant = async (conversationId, userId) => {
  try {
    const count = await prisma.conversationParticipant.count({
      where: {
        conversationId,
        userId,
        leftAt: null
      }
    });

    return count > 0;
  } catch (error) {
    console.error('Error checking if user is conversation participant:', error);
    throw error;
  }
};

/**
 * Busca um participante de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} userId - ID do usuário
 * @returns {Promise<Object|null>} - Participante encontrado ou null
 */
const getConversationParticipant = async (conversationId, userId) => {
  try {
    const participant = await prisma.conversationParticipant.findFirst({
      where: {
        conversationId,
        userId,
        leftAt: null
      }
    });

    return participant;
  } catch (error) {
    console.error('Error getting conversation participant:', error);
    throw error;
  }
};

/**
 * Atualiza a última mensagem lida por um participante
 * @param {string} participantId - ID do participante
 * @param {string} messageId - ID da mensagem
 * @returns {Promise<Object>} - Participante atualizado
 */
const updateLastReadMessage = async (participantId, messageId) => {
  try {
    const participant = await prisma.conversationParticipant.update({
      where: { id: participantId },
      data: { lastReadMessageId: messageId }
    });

    return participant;
  } catch (error) {
    console.error('Error updating last read message:', error);
    throw error;
  }
};

/**
 * Atualiza a última mensagem de uma conversa
 * @param {string} conversationId - ID da conversa
 * @param {string} messageId - ID da mensagem
 * @returns {Promise<Object>} - Conversa atualizada
 */
const updateLastMessage = async (conversationId, messageId) => {
  try {
    // Buscar a mensagem para obter o timestamp
    const message = await prisma.message.findUnique({
      where: { id: messageId },
      select: { createdAt: true }
    });

    if (!message) {
      throw new NotFoundError('Mensagem não encontrada');
    }

    // Atualizar a conversa
    const conversation = await prisma.conversation.update({
      where: { id: conversationId },
      data: { lastMessageAt: message.createdAt }
    });

    return conversation;
  } catch (error) {
    console.error('Error updating last message:', error);
    throw error;
  }
};

module.exports = {
  createConversation,
  getUserConversations,
  getConversationById,
  addParticipant,
  removeParticipant,
  updateConversation,
  isConversationParticipant,
  getConversationParticipant,
  updateLastReadMessage,
  updateLastMessage
};
