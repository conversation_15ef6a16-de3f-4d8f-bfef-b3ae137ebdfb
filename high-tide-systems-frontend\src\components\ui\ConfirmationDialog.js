"use client";

import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { AlertTriangle, X } from "lucide-react";
import { cn } from "@/lib/utils";

const ConfirmationDialog = ({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirmar ação",
  message = "Tem certeza que deseja continuar?",
  confirmText = "Confirmar",
  cancelText = "Cancelar",
  variant = "warning" // warning, danger, info
}) => {
  // Definir estilos com base na variante
  const getVariantStyles = () => {
    switch (variant) {
      case "danger":
        return {
          iconColor: "text-red-500 dark:text-red-400",
          iconBg: "bg-red-100 dark:bg-red-900/30",
          confirmBg: "bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-600",
        };
      case "info":
        return {
          iconColor: "text-blue-500 dark:text-blue-400",
          iconBg: "bg-blue-100 dark:bg-blue-900/30",
          confirmBg: "bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-600",
        };
      case "warning":
      default:
        return {
          iconColor: "text-amber-500 dark:text-amber-400",
          iconBg: "bg-amber-100 dark:bg-amber-900/30",
          confirmBg: "bg-amber-600 hover:bg-amber-700 dark:bg-amber-700 dark:hover:bg-amber-600",
        };
    }
  };

  const variantStyles = getVariantStyles();

  // Estado para controlar a montagem do componente no cliente
  const [mounted, setMounted] = useState(false);

  // Efeito para garantir que o portal só seja criado no lado do cliente
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Efeito para prevenir scroll quando o modal estiver aberto
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  if (!isOpen || !mounted) return null;

  // Usar createPortal para renderizar o modal no nível mais alto do DOM
  const modalContent = (
    <div className={cn(
      "fixed inset-0 z-[12000] flex items-center justify-center overflow-y-auto",
      "pointer-events-auto" // Garantir que os eventos de clique funcionem
    )} onClick={(e) => {
      e.stopPropagation();
    }}>
      {/* Overlay de fundo escuro */}
      <div
        className="fixed inset-0 bg-black/50"
        onClick={(e) => {
          e.stopPropagation(); // Impedir propagação do evento para o modal principal
          e.preventDefault(); // Adicionado preventDefault para garantir
          onClose();
        }}
      ></div>

      <div
        className="relative bg-white dark:bg-gray-800 rounded-xl shadow-xl dark:shadow-black/50 w-full max-w-xl z-[12050]"
        onClick={(e) => {
          e.stopPropagation(); // Impedir propagação do evento para o modal principal
        }}
      >
        <div className="absolute top-4 right-4">
          <button
            onClick={(e) => {
              e.stopPropagation(); // Impedir propagação do evento
              e.preventDefault(); // Adicionado preventDefault para garantir
              onClose();
            }}
            className="text-neutral-400 dark:text-gray-500 hover:text-neutral-600 dark:hover:text-gray-300 transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4 mb-4">
            <div className={`p-3 rounded-full ${variantStyles.iconBg}`}>
              <AlertTriangle className={`h-6 w-6 ${variantStyles.iconColor}`} />
            </div>
            <h3 className="text-xl font-semibold text-neutral-800 dark:text-white">{title}</h3>
          </div>

          <div className="text-neutral-600 dark:text-gray-300 mb-6 max-h-[60vh] overflow-y-auto pr-2 text-sm">
            {message.split('\n').map((line, index) => (
              <React.Fragment key={index}>
                {line.startsWith('•') ? (
                  <span className="block pl-2 py-0.5 text-primary-600 dark:text-primary-400 font-medium">{line}</span>
                ) : line.match(/^\d+\./) ? (
                  <span className="block pl-2 py-0.5 font-medium">{line}</span>
                ) : (
                  <span className={line.trim() === '' ? 'block py-1' : 'block py-0.5'}>{line}</span>
                )}
              </React.Fragment>
            ))}
          </div>

          <div className="flex justify-end gap-3">
            <button
              onClick={(e) => {
                e.stopPropagation(); // Impedir propagação do evento
                e.preventDefault(); // Adicionado preventDefault para garantir
                onClose();
              }}
              className="px-4 py-2 bg-white dark:bg-gray-700 border border-neutral-300 dark:border-gray-600 rounded-lg text-neutral-700 dark:text-gray-300 hover:bg-neutral-50 dark:hover:bg-gray-600 transition-colors"
            >
              {cancelText}
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation(); // Impedir propagação do evento
                e.preventDefault(); // Adicionado preventDefault para garantir
                onConfirm();
                onClose();
              }}
              className={`px-4 py-2 text-white rounded-lg transition-colors ${variantStyles.confirmBg}`}
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  // Renderizar o modal usando um portal para garantir que ele fique acima de tudo
  // Usamos um z-index maior que o do modal principal para garantir que ele fique por cima
  return createPortal(modalContent, document.body);
};

export default ConfirmationDialog;