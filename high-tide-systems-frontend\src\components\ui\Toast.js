'use client';

import React, { useEffect, useState, useRef } from 'react';
import { 
  CheckCircle, 
  AlertCircle, 
  AlertTriangle, 
  Info, 
  X 
} from 'lucide-react';
import { TOAST_TYPES } from '@/contexts/ToastContext';

// Configurações de ícones e cores por tipo de toast
const TOAST_CONFIG = {
  [TOAST_TYPES.SUCCESS]: {
    icon: CheckCircle,
    bgClass: 'bg-success-50 dark:bg-success-700/30 border-success-200 dark:border-success-700/50',
    iconClass: 'text-success-500 dark:text-success-300',
    textClass: 'text-success-800 dark:text-success-50',
    progressClass: 'bg-success-500 dark:bg-success-400',
  },
  [TOAST_TYPES.ERROR]: {
    icon: AlertCircle,
    bgClass: 'bg-error-50 dark:bg-error-700/30 border-error-200 dark:border-error-700/50',
    iconClass: 'text-error-500 dark:text-error-300',
    textClass: 'text-error-800 dark:text-error-50',
    progressClass: 'bg-error-500 dark:bg-error-400',
  },
  [TOAST_TYPES.WARNING]: {
    icon: AlertTriangle,
    bgClass: 'bg-warning-50 dark:bg-warning-700/30 border-warning-200 dark:border-warning-700/50',
    iconClass: 'text-warning-500 dark:text-warning-300',
    textClass: 'text-warning-800 dark:text-warning-50',
    progressClass: 'bg-warning-500 dark:bg-warning-400',
  },
  [TOAST_TYPES.INFO]: {
    icon: Info,
    bgClass: 'bg-primary-50 dark:bg-primary-700/30 border-primary-200 dark:border-primary-700/50',
    iconClass: 'text-primary-500 dark:text-primary-300',
    textClass: 'text-primary-800 dark:text-primary-50',
    progressClass: 'bg-primary-500 dark:bg-primary-400',
  },
};

export const Toast = ({ toast, onRemove }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [exiting, setExiting] = useState(false);
  const progressRef = useRef(null);
  const timerRef = useRef(null);
  
  // Configuração com base no tipo
  const config = TOAST_CONFIG[toast.type] || TOAST_CONFIG[TOAST_TYPES.INFO];
  const Icon = config.icon;
  
  // Efeito para animação de entrada
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 10);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Efeito para animação da barra de progresso usando CSS transitions
  useEffect(() => {
    if (toast.duration > 0 && progressRef.current) {
      // Configura a transição CSS para animação suave
      progressRef.current.style.transition = `width ${toast.duration}ms linear`;
      
      // Force reflow para garantir que a transição inicie corretamente
      void progressRef.current.getBoundingClientRect();
      
      // Inicia a animação
      progressRef.current.style.width = '0%';
      
      // Timer para remoção automática do toast
      timerRef.current = setTimeout(() => {
        handleClose();
      }, toast.duration);
    }
    
    return () => {
      if (timerRef.current) clearTimeout(timerRef.current);
    };
  }, [toast.duration]);
  
  // Função para fechar o toast
  const handleClose = () => {
    setExiting(true);
    setTimeout(() => {
      onRemove(toast.id);
    }, 300);
  };
  
  return (
    <div
      className={`
        max-w-md w-full rounded-lg border px-4 py-3 shadow-md dark:shadow-black/50 relative 
        transform transition-all duration-300 ease-out
        ${config.bgClass}
        ${isVisible && !exiting ? 'translate-y-0 opacity-100 scale-100' : 
          exiting ? 'translate-y-2 opacity-0 scale-95' : 'translate-y-2 opacity-0 scale-95'}
      `}
      style={{
        backdropFilter: 'blur(8px)',
        boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)'
      }}
      role="alert"
    >
      <div className="flex items-start">
        <div className={`flex-shrink-0 ${config.iconClass}`}>
          <Icon className="h-5 w-5" aria-hidden="true" />
        </div>
        <div className={`ml-3 flex-grow ${config.textClass}`}>
          {toast.title && <p className="text-sm font-semibold">{toast.title}</p>}
          <p className={`text-sm ${toast.title ? 'mt-0.5 font-normal' : 'font-medium'}`}>
            {toast.message}
          </p>
        </div>
        <div className="ml-4 flex-shrink-0 flex">
          <button
            className={`inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 focus:ring-primary-500 ${config.iconClass} hover:opacity-75 transition-opacity duration-150`}
            onClick={handleClose}
          >
            <span className="sr-only">Fechar</span>
            <X className="h-5 w-5" aria-hidden="true" />
          </button>
        </div>
      </div>
      
      {/* Barra de progresso */}
      {toast.duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gray-200/30 dark:bg-gray-700/40 rounded-b-lg overflow-hidden">
          <div
            ref={progressRef}
            className={`h-full ${config.progressClass}`}
            style={{ width: '100%' }}
          />
        </div>
      )}
    </div>
  );
};