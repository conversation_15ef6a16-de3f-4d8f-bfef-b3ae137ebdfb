'use client';

import React from 'react';
import { Toast } from '@/components/ui/Toast';
import { useToast } from '@/contexts/ToastContext';

export const ToastContainer = () => {
  const { toasts, removeToast } = useToast();

  if (toasts.length === 0) return null;

  return (
    <div className="fixed inset-0 flex flex-col items-end justify-start pt-20 px-4 pb-4 pointer-events-none space-y-3 z-[12000] max-h-screen overflow-hidden">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className="pointer-events-auto w-full max-w-sm transform transition-all duration-300"
        >
          <Toast toast={toast} onRemove={removeToast} />
        </div>
      ))}
    </div>
  );
};