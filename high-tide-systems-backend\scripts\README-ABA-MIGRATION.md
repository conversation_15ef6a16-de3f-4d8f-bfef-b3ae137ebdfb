# Migração do Módulo ABA+

Este script executa a migração do banco de dados para adicionar as tabelas e relacionamentos necessários para o módulo ABA+.

## Características

- Cria as tabelas para o módulo ABA+: Evaluation, Level, Skill, Score, Task, etc.
- Adiciona os enums necessários: EvaluationType, ScoreType
- Estabelece os relacionamentos entre as tabelas
- Executa a migração dentro do container Docker

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-aba-migration.js
```

### Opção 2: Executando os comandos diretamente no container Docker

```bash
docker exec -it high-tide-systems-api npx prisma migrate dev --name add_aba_module
docker exec -it high-tide-systems-api npx prisma generate
```

## Detalhes da Migração

A migração cria as seguintes tabelas:

1. **Evaluation**: Armazena as avaliações do módulo ABA+
   - Campos: id, type, name, observations, active, createdAt, updatedAt, deletedAt, companyId, createdById
   - Relacionamentos: company, createdBy, levels, skills, scores, tasks

2. **Level**: Armazena os níveis das avaliações
   - Campos: id, order, description, ageRange, createdAt, updatedAt, evaluationId
   - Relacionamentos: evaluation, tasks

3. **Skill**: Armazena as habilidades
   - Campos: id, code, order, description, active, createdAt, updatedAt, deletedAt, companyId
   - Relacionamentos: company, evaluations, tasks

4. **EvaluationSkill**: Tabela de relacionamento entre avaliações e habilidades
   - Campos: evaluationId, skillId
   - Relacionamentos: evaluation, skill

5. **Score**: Armazena as pontuações das avaliações
   - Campos: id, type, value, description, createdAt, updatedAt, evaluationId
   - Relacionamentos: evaluation

6. **Task**: Armazena as tarefas/testes das avaliações
   - Campos: id, order, name, milestone, item, question, example, criteria, objective, createdAt, updatedAt, evaluationId, skillId, levelId
   - Relacionamentos: evaluation, skill, level

## Verificação da Migração

Após a migração, você pode verificar se as tabelas foram criadas corretamente acessando o Prisma Studio:

```bash
docker exec -it high-tide-systems-studio npx prisma studio
```

Ou executando consultas SQL diretamente no banco de dados:

```bash
docker exec -it high-tide-systems-db psql -U admin -d hightidesystems -c "SELECT * FROM \"Evaluation\";"
```

## Notas Importantes

- Esta migração não adiciona dados iniciais. Se necessário, crie um script de seed separado para adicionar dados de exemplo.
- Certifique-se de que o container Docker `high-tide-systems-api` esteja em execução antes de executar a migração.
- Se ocorrer algum erro durante a migração, verifique os logs para identificar o problema.
