const fs = require('fs');
const Papa = require('papaparse');

console.log('🔧 Diagnóstico rápido do problema...\n');

// Verificar se arquivo existe
if (!fs.existsSync('funcionarios_schema_user.csv')) {
  console.error('❌ Arquivo funcionarios_schema_user.csv não encontrado');
  console.log('💡 Execute primeiro: node index.js');
  process.exit(1);
}

// Ler arquivo e verificar problema específico
const fileContent = fs.readFileSync('funcionarios_schema_user.csv', 'utf8');
const results = Papa.parse(fileContent, {
  header: true,
  skipEmptyLines: true,
  dynamicTyping: true // Manter como estava para reproduzir o erro
});

console.log(`📊 Total de registros no CSV: ${results.data.length}`);

// Encontrar registros problemáticos
const problematicRecords = [];
results.data.forEach((row, index) => {
  if (row.cpf && typeof row.cpf === 'number') {
    problematicRecords.push({
      linha: index + 1,
      nome: row.fullName,
      cpf: row.cpf,
      tipo: typeof row.cpf
    });
  }
});

if (problematicRecords.length > 0) {
  console.log(`\n⚠️  Encontrados ${problematicRecords.length} registros com CPF como número:`);
  problematicRecords.slice(0, 5).forEach(record => {
    console.log(`  Linha ${record.linha}: ${record.nome} - CPF: ${record.cpf} (${record.tipo})`);
  });
  
  if (problematicRecords.length > 5) {
    console.log(`  ... e mais ${problematicRecords.length - 5} registros`);
  }
} else {
  console.log('✅ Nenhum problema com CPF encontrado');
}

// Verificar outros campos problemáticos
const emailProblems = results.data.filter(row => !row.email || typeof row.email !== 'string');
const nameProblems = results.data.filter(row => !row.fullName || typeof row.fullName !== 'string');

if (emailProblems.length > 0) {
  console.log(`\n⚠️  ${emailProblems.length} registros com problemas de email`);
}

if (nameProblems.length > 0) {
  console.log(`\n⚠️  ${nameProblems.length} registros com problemas de nome`);
}

// Gerar versão corrigida do CSV
console.log('\n🔧 Gerando versão corrigida do CSV...');

const correctedData = results.data.map(row => {
  return {
    ...row,
    // Forçar CPF como string
    cpf: row.cpf ? row.cpf.toString() : null,
    // Garantir email como string
    email: row.email ? row.email.toString() : `${row.codigoOriginal}@empresa.com`,
    // Garantir nome como string
    fullName: row.fullName ? row.fullName.toString() : 'Nome não informado',
    // Garantir login como string
    login: row.login ? row.login.toString() : row.codigoOriginal?.toString(),
    // Normalizar outros campos
    phone: row.phone ? row.phone.toString() : null,
    city: row.city ? row.city.toString() : null,
    address: row.address ? row.address.toString() : null,
    state: row.state ? row.state.toString() : null,
    neighborhood: row.neighborhood ? row.neighborhood.toString() : null,
    postalCode: row.postalCode ? row.postalCode.toString() : null
  };
});

// Salvar versão corrigida
//const Papa = require('papaparse');
const correctedCSV = Papa.unparse(correctedData);
fs.writeFileSync('funcionarios_schema_user_fixed.csv', correctedCSV);

console.log('✅ Arquivo corrigido salvo como: funcionarios_schema_user_fixed.csv');

// Instruções
console.log('\n📋 Próximos passos:');
console.log('1. Faça backup do arquivo original:');
console.log('   copy funcionarios_schema_user.csv funcionarios_schema_user_backup.csv');
console.log('');
console.log('2. Substitua pelo arquivo corrigido:');
console.log('   copy funcionarios_schema_user_fixed.csv funcionarios_schema_user.csv');
console.log('');
console.log('3. Execute a migração novamente:');
console.log('   node migrate-existing-docker.js');

// Mostrar exemplo de registro corrigido
console.log('\n📄 Exemplo de registro corrigido:');
const sample = correctedData[0];
Object.entries(sample).slice(0, 8).forEach(([key, value]) => {
  console.log(`  ${key}: ${value} (${typeof value})`);
});