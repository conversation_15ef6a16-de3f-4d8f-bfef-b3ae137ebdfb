// src/routes/contactRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { ContactController, contactValidation } = require('../../controllers/contactController');

// All routes require authentication
router.use(authenticate);

// Contact CRUD routes
router.post('/', contactValidation, ContactController.create);
router.get('/', ContactController.list);
router.get('/:id', ContactController.get);
router.put('/:id', contactValidation, ContactController.update);
router.delete('/:id', ContactController.delete);

module.exports = router;