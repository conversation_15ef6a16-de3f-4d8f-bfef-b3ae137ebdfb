# Testes de Cache Redis

Esta pasta contém scripts para testar a implementação do Redis Cache na aplicação High Tide Systems.

## Arquivos de Teste

- **redis-basic.js**: Teste básico da conexão e operações do Redis
- **redis-simple.js**: Teste simplificado do Redis para verificação rápida
- **middleware.js**: Teste do middleware de cache para rotas
- **cache-simple.js**: Teste simplificado do serviço de cache

## Como Executar os Testes

Para executar os testes, você precisa ter o Redis rodando (via Docker) e as dependências instaladas.

```bash
# Dentro do container Docker
node tests/cache/redis-basic.js
```

## Notas Importantes

- Os testes devem ser executados dentro do container Docker para ter acesso ao Redis
- Alguns testes podem exigir autenticação, verifique o código antes de executar
- Os testes são independentes e podem ser executados em qualquer ordem
