# Script de Configuração de Horários de Trabalho

Este script configura os horários de trabalho para todos os profissionais cadastrados no sistema que possuem acesso ao módulo de Agendamento.

## Características

- Configura horários de trabalho de segunda a sexta-feira (dias 1 a 5)
- Define o horário de trabalho das 8h às 17h
- Configura intervalo de almoço das 12h às 13h
- Desativa horários existentes antes de criar novos
- Aplica a mesma configuração para todos os profissionais

## Como Executar

### Opção 1: Usando o script auxiliar

```bash
node scripts/run-working-hours-seed.js
```

### Opção 2: Executando diretamente o arquivo de seed

```bash
node prisma/seed-working-hours.js
```

### Opção 3: Executando dentro do container Docker

```bash
docker exec -it high-tide-systems-api node prisma/seed-working-hours.js
```

## Detalhes do Script

O script realiza as seguintes operações:

1. Busca todos os usuários que têm acesso ao módulo de Agendamento
2. Para cada profissional:
   - Verifica se já existem horários configurados
   - Desativa todos os horários existentes
   - Cria novos horários para cada dia útil (segunda a sexta)
   - Define o horário de trabalho das 8h às 17h
   - Configura intervalo de almoço das 12h às 13h

## Formato dos Horários

Os horários são armazenados em minutos desde a meia-noite:

- 8h = 480 minutos
- 12h = 720 minutos
- 13h = 780 minutos
- 17h = 1020 minutos

## Observações

- O script desativa todos os horários existentes antes de criar novos
- Todos os profissionais terão a mesma configuração de horário
- Os horários são configurados apenas para dias úteis (segunda a sexta-feira)
- O script não verifica conflitos com agendamentos existentes
