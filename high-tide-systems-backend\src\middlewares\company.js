/**
 * Middleware que garante o isolamento de dados entre empresas
 * Este middleware deve ser usado após o middleware de autenticação
 */
const companyMiddleware = async (req, res, next) => {
    try {
      // O usuário já deve estar autenticado e disponível em req.user
      // O middleware de autenticação deve ter sido executado antes deste
      
      // Se for um admin do sistema, não aplica restrições de empresa
      if (req.user.role === 'SYSTEM_ADMIN') {
        return next();
      }
  
      // Se o usuário não tiver uma empresa associada, retorna erro
      if (!req.user.companyId) {
        return res.status(403).json({ 
          message: 'Usuário não está associado a nenhuma empresa' 
        });
      }
  
      // Adiciona o companyId nas queries
      req.companyId = req.user.companyId;
      
      // Se houver um parâmetro companyId na rota, verifica se corresponde
      // à empresa do usuário (exceto para admins de sistema)
      if (req.params.companyId && req.params.companyId !== req.user.companyId) {
        return res.status(403).json({ 
          message: 'Acesso negado a recursos de outra empresa' 
        });
      }
  
      // Da mesma forma para companyId no corpo da requisição
      if (req.body && req.body.companyId && req.body.companyId !== req.user.companyId) {
        return res.status(403).json({ 
          message: 'Acesso negado a recursos de outra empresa' 
        });
      }
  
      next();
    } catch (error) {
      console.error('Erro no middleware de empresa:', error);
      return res.status(500).json({ message: 'Erro interno do servidor' });
    }
  };
  
  module.exports = companyMiddleware;