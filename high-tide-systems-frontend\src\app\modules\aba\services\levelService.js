// src/app/modules/aba/services/levelService.js
import { api } from "@/utils/api";

export const levelService = {
  // Get levels with optional filters
  getLevels: async (filters = {}) => {
    const { page = 1, limit = 10, search = "", evaluationId } = filters;

    try {
      const response = await api.get("/aba/levels", {
        params: {
          page,
          limit,
          search: search || undefined,
          evaluationId: evaluationId || undefined
        }
      });

      return {
        items: response.data.items || [],
        total: response.data.total || 0,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching levels:", error);
      throw error;
    }
  },

  // Get a single level by ID
  getLevel: async (id) => {
    try {
      const response = await api.get(`/aba/levels/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching level ${id}:`, error);
      throw error;
    }
  },

  // Create a new level
  createLevel: async (levelData) => {
    try {
      const response = await api.post("/aba/levels", levelData);
      return response.data;
    } catch (error) {
      console.error("Error creating level:", error);
      throw error;
    }
  },

  // Update an existing level
  updateLevel: async (id, levelData) => {
    try {
      const response = await api.put(`/aba/levels/${id}`, levelData);
      return response.data;
    } catch (error) {
      console.error(`Error updating level ${id}:`, error);
      throw error;
    }
  },

  // Delete a level
  deleteLevel: async (id) => {
    try {
      const response = await api.delete(`/aba/levels/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting level ${id}:`, error);
      throw error;
    }
  }
};

export default levelService;
