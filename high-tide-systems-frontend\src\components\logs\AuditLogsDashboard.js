"use client";

import React, { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>hart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { FileText, Users, Calendar, RefreshCw } from "lucide-react";
import { auditLogService } from "@/app/modules/admin";

const AuditLogsDashboard = () => {
  const [stats, setStats] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState(30); // Default: 30 days
  const [error, setError] = useState("");

  const COLORS = ["#0088FE", "#00C49F", "#FFBB28", "#FF8042", "#8884D8", "#FF6B6B"];
  
  useEffect(() => {
    fetchStats();
  }, [timeRange]);
  
  const fetchStats = async () => {
    setIsLoading(true);
    setError("");
    try {
      const response = await auditLogService.getStats(timeRange);
      setStats(response);
    } catch (error) {
      console.error("Error fetching audit log stats:", error);
      setError("Não foi possível carregar as estatísticas. Por favor, tente novamente mais tarde.");
    } finally {
      setIsLoading(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 text-red-600 rounded-lg">
        {error}
        <button 
          onClick={fetchStats}
          className="ml-4 px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700"
        >
          Tentar novamente
        </button>
      </div>
    );
  }
  
  if (!stats) {
    return null;
  }
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">Estatísticas de Auditoria</h2>
        <div className="flex items-center gap-3">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="px-3 py-2 border border-gray-300 rounded-md bg-white"
          >
            <option value={7}>Últimos 7 dias</option>
            <option value={30}>Últimos 30 dias</option>
            <option value={90}>Últimos 3 meses</option>
            <option value={365}>Último ano</option>
          </select>
          
          <button
            onClick={fetchStats}
            className="p-2 rounded-full hover:bg-gray-100"
            title="Atualizar"
          >
            <RefreshCw size={16} />
          </button>
        </div>
      </div>
      
      {/* Cards com contagens */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 rounded-full bg-blue-100">
              <FileText size={20} className="text-blue-600" />
            </div>
            <h3 className="text-lg font-medium">Total de Logs</h3>
          </div>
          <div className="text-3xl font-bold">
            {stats.actionCounts.reduce((sum, item) => sum + item.count, 0).toLocaleString()}
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 rounded-full bg-green-100">
              <Users size={20} className="text-green-600" />
            </div>
            <h3 className="text-lg font-medium">Usuários Ativos</h3>
          </div>
          <div className="text-3xl font-bold">
            {stats.activeUsers.length.toLocaleString()}
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 rounded-full bg-purple-100">
              <Calendar size={20} className="text-purple-600" />
            </div>
            <h3 className="text-lg font-medium">Último Dia</h3>
          </div>
          <div className="text-3xl font-bold">
            {stats.dailyTrend.length > 0 
              ? stats.dailyTrend[stats.dailyTrend.length - 1].count.toLocaleString() 
              : "0"}
          </div>
        </div>
      </div>
      
      {/* Gráficos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Tendência diária */}
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-4">Atividade Diária</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart
                data={stats.dailyTrend}
                margin={{ top: 5, right: 30, left: 20, bottom: 25 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={(date) => {
                    const parts = date.split('-');
                    return `${parts[2]}/${parts[1]}`;
                  }}
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: "#e5e7eb" }}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: "#e5e7eb" }}
                />
                <Tooltip 
                  formatter={(value) => [value, "Logs"]}
                  labelFormatter={(date) => {
                    const parts = date.split('-');
                    return `${parts[2]}/${parts[1]}/${parts[0]}`;
                  }}
                />
                <Line 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#FF9933" 
                  strokeWidth={2} 
                  dot={{ stroke: '#FF9933', strokeWidth: 2, r: 4 }}
                  activeDot={{ stroke: '#FF9933', strokeWidth: 2, r: 6 }}
                  name="Logs"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
        
        {/* Distribuição por ação */}
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-4">Logs por Ação</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={stats.actionCounts.slice(0, 6)} // Top 6 para não sobrecarregar
                  dataKey="count"
                  nameKey="action"
                  cx="50%"
                  cy="50%"
                  innerRadius={70}
                  outerRadius={100}
                  paddingAngle={2}
                  label={({ action, percent }) => `${action} (${(percent * 100).toFixed(0)}%)`}
                >
                  {stats.actionCounts.slice(0, 6).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip 
                  formatter={(value) => [`${value} logs`, "Quantidade"]}
                />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
        
        {/* Logs por entidade */}
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-4">Logs por Entidade</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={stats.entityCounts}
                layout="vertical"
                margin={{ top: 5, right: 30, left: 100, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                <XAxis 
                  type="number" 
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: "#e5e7eb" }}
                />
                <YAxis 
                  dataKey="entityType" 
                  type="category" 
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: "#e5e7eb" }}
                />
                <Tooltip formatter={(value) => [`${value} logs`, "Quantidade"]} />
                <Bar dataKey="count" fill="#8884d8" barSize={20} name="Logs" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
        
        {/* Usuários mais ativos */}
        <div className="bg-white p-4 rounded-xl shadow-sm border border-gray-100">
          <h3 className="text-lg font-medium mb-4">Usuários Mais Ativos</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={stats.activeUsers}
                margin={{ top: 5, right: 30, left: 20, bottom: 25 }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f3f4f6" />
                <XAxis 
                  dataKey="userName" 
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: "#e5e7eb" }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis
                  tick={{ fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: "#e5e7eb" }}
                />
                <Tooltip formatter={(value) => [`${value} logs`, "Atividade"]} />
                <Bar dataKey="count" fill="#FF9933" barSize={20} name="Logs" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditLogsDashboard;