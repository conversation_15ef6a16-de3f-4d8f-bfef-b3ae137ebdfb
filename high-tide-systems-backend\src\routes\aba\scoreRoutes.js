// src/routes/aba/scoreRoutes.js
const express = require('express');
const router = express.Router();
const { authenticate } = require('../../middlewares/auth');
const { ScoreController, createScoreValidation, updateScoreValidation } = require('../../controllers/aba/scoreController');

// Todas as rotas requerem autenticação
router.use(authenticate);

// Rotas CRUD para pontuações
router.post('/', createScoreValidation, ScoreController.create);
router.get('/', ScoreController.list);
router.get('/:id', ScoreController.get);
router.put('/:id', updateScoreValidation, ScoreController.update);
router.delete('/:id', ScoreController.delete);

module.exports = router;
