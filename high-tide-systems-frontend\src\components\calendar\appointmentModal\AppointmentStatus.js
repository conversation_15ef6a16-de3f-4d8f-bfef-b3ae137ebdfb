import React from 'react';
import { Clock } from 'lucide-react';

const AppointmentStatus = ({ formData, setFormData }) => {
  const inputBaseClasses =
    "w-full rounded-md border border-neutral-300 dark:border-neutral-600 px-3 py-2 text-base transition-colors duration-200 focus:border-primary-500 focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 bg-white dark:bg-gray-700 dark:text-gray-200";

  return (
    <div>
      <h3 className="text-sm font-semibold text-neutral-700 dark:text-neutral-300 mb-2 flex items-center gap-2">
        <Clock className="w-4 h-4" />
        Status do Agendamento
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm text-neutral-600 dark:text-neutral-400 mb-1">
            Status
          </label>
          <select
            value={formData.status || "PENDING"}
            onChange={(e) =>
              setFormData({ ...formData, status: e.target.value })
            }
            className={inputBaseClasses}
          >
            <option value="PENDING">Pendente</option>
            <option value="CONFIRMED">Confirmado</option>
            <option value="CANCELLED">Cancelado</option>
            <option value="COMPLETED">Concluído</option>
            <option value="NO_SHOW">Não Compareceu</option>
          </select>
        </div>
      </div>
    </div>
  );
};

export default AppointmentStatus;