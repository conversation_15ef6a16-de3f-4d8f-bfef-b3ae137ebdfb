const cron = require('node-cron');
const prisma = require('../utils/prisma');

class AppointmentStatusService {
  constructor() {
    // Schedule the task to run every 30 minutes (at minute 1 and 31 of every hour)
    this.job = cron.schedule('1,31 * * * *', this.updateAppointmentStatuses.bind(this), {
      scheduled: false
    });
  }

  /**
   * Start the appointment status updater
   */
  start() {
    this.job.start();

    // Calcular próxima execução (1 minuto após a hora cheia ou meia hora)
    const now = new Date();
    const nextRunTime = new Date(now);

    if (now.getMinutes() < 30) {
      nextRunTime.setMinutes(31, 0, 0);
    } else {
      nextRunTime.setHours(now.getHours() + 1, 1, 0, 0);
    }

    console.log('\n=== Appointment Status Updater Service ===');
    console.log('Service started successfully');
    console.log(`Schedule: Every 30 minutes (at minute 1 and 31 of every hour)`);
    console.log(`Next scheduled run: ${nextRunTime.toLocaleString()}`);
    console.log('==========================================\n');
  }

  /**
   * Stop the appointment status updater
   */
  stop() {
    this.job.stop();
    console.log('\n=== Appointment Status Updater Service ===');
    console.log('Service stopped successfully');
    console.log('==========================================\n');
  }

  /**
   * Update appointment statuses from PENDING to COMPLETED
   * when 30 minutes have passed after the appointment's end time
   * Only applies to appointments created for future dates
   */
  async updateAppointmentStatuses() {
    try {
      const now = new Date();
      const nextRunTime = new Date(now);

      // Calcular próxima execução (1 minuto após a hora cheia ou meia hora)
      if (now.getMinutes() < 30) {
        nextRunTime.setMinutes(31, 0, 0);
      } else {
        nextRunTime.setHours(now.getHours() + 1, 1, 0, 0);
      }

      console.log(`\n=== Appointment Status Update Service (${now.toLocaleString()}) ===`);
      console.log(`Next scheduled run: ${nextRunTime.toLocaleString()}`);

      // Verificar agendamentos que terminaram há pelo menos 30 minutos
      const thirtyMinutesAgo = new Date(now.getTime() - 30 * 60000); // 30 minutes ago

      // Otimização: Limitar a busca a agendamentos que terminaram nas últimas 24 horas
      // Isso evita verificar todos os agendamentos antigos a cada execução
      const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000); // 24 hours ago

      console.log(`Looking for PENDING appointments that ended between ${oneDayAgo.toLocaleString()} and ${thirtyMinutesAgo.toLocaleString()}`);

      // Find appointments that:
      // 1. Have status "PENDING"
      // 2. Have an end time that is at least 30 minutes in the past
      // 3. Have an end time that is at most 24 hours in the past (otimização)
      // 4. Were created for a future date (endDate > createdAt)
      const appointmentsToUpdate = await prisma.scheduling.findMany({
        where: {
          status: 'PENDING',
          endDate: {
            lt: thirtyMinutesAgo,
            gt: oneDayAgo // Otimização: Limitar a busca a agendamentos recentes
          },
          // Verificar se o agendamento foi criado para uma data futura
          // Não podemos usar Prisma.sql diretamente aqui, então vamos remover essa condição
          // e filtrar os resultados depois
        },
        select: {
          id: true,
          title: true,
          startDate: true,
          endDate: true,
          creatorId: true,
          companyId: true,
          createdAt: true // Adicionado para verificar a condição endDate > createdAt
        }
      });

      // Filtrar agendamentos que foram criados para uma data futura (endDate > createdAt)
      const filteredAppointments = appointmentsToUpdate.filter(appointment =>
        appointment.endDate > appointment.createdAt
      );

      if (filteredAppointments.length === 0) {
        console.log('No appointments need status update');
        return;
      }

      console.log(`Found ${filteredAppointments.length} appointments to update status from PENDING to COMPLETED`);

      // Log details of appointments being updated
      filteredAppointments.forEach(appointment => {
        console.log(`Appointment ID: ${appointment.id}, Title: ${appointment.title}, End Time: ${appointment.endDate.toISOString()}, Created At: ${appointment.createdAt.toISOString()}`);
      });

      // Update the appointments to COMPLETED
      const updateResult = await prisma.scheduling.updateMany({
        where: {
          id: {
            in: filteredAppointments.map(appointment => appointment.id)
          }
        },
        data: {
          status: 'COMPLETED'
        }
      });

      console.log(`Updated ${updateResult.count} appointments to COMPLETED status`);

      // Log the updates for auditing
      for (const appointment of filteredAppointments) {
        try {
          await prisma.auditLog.create({
            data: {
              userId: appointment.creatorId,
              action: 'AUTO_STATUS_UPDATE',
              entityType: 'Scheduling',
              entityId: appointment.id,
              details: {
                previousStatus: 'PENDING',
                newStatus: 'COMPLETED',
                reason: 'Automatic update 30 minutes after appointment end time'
              },
              companyId: appointment.companyId
            }
          });
        } catch (logError) {
          console.error(`Error creating audit log for appointment ${appointment.id}:`, logError);
        }
      }

      // Log final summary
      console.log(`\n=== Summary ===`);
      console.log(`- Total appointments checked: ${appointmentsToUpdate.length}`);
      console.log(`- Appointments updated: ${updateResult.count}`);
      console.log(`- Next scheduled run: ${nextRunTime.toLocaleString()}`);
      console.log(`=== End of Appointment Status Update ===\n`);
    } catch (error) {
      console.error('\n=== ERROR in Appointment Status Update Service ===');
      console.error(`Time: ${new Date().toLocaleString()}`);
      console.error(`Message: ${error.message}`);
      console.error(`Stack: ${error.stack}`);
      console.error('=================================================\n');
    }
  }
}

// Create a singleton instance
const appointmentStatusService = new AppointmentStatusService();

module.exports = appointmentStatusService;
