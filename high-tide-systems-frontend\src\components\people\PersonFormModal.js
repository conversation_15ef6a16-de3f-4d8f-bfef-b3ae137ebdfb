"use client";

import React, { useState, useEffect, useRef } from "react";
import { Loader2, Shield, FileText, Users, CreditCard, Check, AlertCircle, User } from "lucide-react";
import ModuleModal from "@/components/ui/ModuleModal";
import ModalButton from "@/components/ui/ModalButton";
import { personsService } from "@/app/modules/people/services/personsService";
import { format } from "date-fns";
import PersonInfoTab from "./PersonInfoTab";
import DocumentsTab from "./DocumentsTab";
import ContactsTab from "./ContactsTab";
import PersonInsurancesTab from "./PersonInsurancesTab";

const PersonFormModal = ({ isOpen, onClose, person, onSuccess, initialClientId }) => {
  const [activeTab, setActiveTab] = useState("info"); // "info", "documents", "contacts", "insurances"
  const [savedPersonId, setSavedPersonId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [availableTabs, setAvailableTabs] = useState({
    info: true,
    documents: false,
    contacts: false,
    insurances: false
  });
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [tempDocuments, setTempDocuments] = useState([]);
  const [tempContacts, setTempContacts] = useState([]);
  const profileImageUploadRef = useRef(null);
  const [tempInsurances, setTempInsurances] = useState([]);
  const [tempProfileImage, setTempProfileImage] = useState(null);
  const [formData, setFormData] = useState({
    fullName: "",
    cpf: "",
    birthDate: "",
    address: "",
    neighborhood: "",
    city: "",
    state: "",
    postalCode: "",
    phone: "",
    email: "",
    gender: "",
    notes: "",
    clientId: "",
    relationship: "",
    useClientEmail: false,
    useClientPhone: false,
  });
  const [errors, setErrors] = useState({});

  // Fetch clients and initialize form data when modal opens
  useEffect(() => {
    if (isOpen) {
      console.log('Modal aberto, inicializando dados do formulário');
      console.log('Dados da pessoa recebidos no modal:', person);
      initializeFormData();
    }
  }, [isOpen, person, initialClientId]);

  const initializeFormData = () => {
    // Reset form data
    let newFormData = {
      fullName: "",
      cpf: "",
      birthDate: "",
      address: "",
      neighborhood: "",
      city: "",
      state: "",
      postalCode: "",
      phone: "",
      email: "",
      gender: "",
      notes: "",
      clientId: "",
      relationship: "",
      useClientEmail: false,
      useClientPhone: false,
    };

    // If editing an existing person
    if (person) {
      let formattedCpf = "";
      if (person.cpf) {
        const cleanCpf = person.cpf.replace(/\D/g, "");
        formattedCpf = cleanCpf.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4");
      }

      // Formatar telefone
      let formattedPhone = "";
      if (person.phone) {
        const cleanPhone = person.phone.replace(/\D/g, "");
        formattedPhone = cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, "($1) $2-$3");
      }

      let birthDateFormatted = "";
      try {
        if (person.birthDate) {
          birthDateFormatted = format(new Date(person.birthDate), "yyyy-MM-dd");
        }
      } catch (e) {
        console.error("Error formatting date:", e);
      }

      // Formatar CEP
      let formattedPostalCode = "";
      if (person.postalCode) {
        const cleanPostalCode = person.postalCode.replace(/\D/g, "");
        formattedPostalCode = cleanPostalCode.replace(/(\d{5})(\d{3})/, "$1-$2");
      }

      newFormData = {
        fullName: person.fullName || "",
        cpf: formattedCpf || "",
        birthDate: birthDateFormatted,
        address: person.address || "",
        neighborhood: person.neighborhood || "",
        city: person.city || "",
        state: person.state || "",
        postalCode: formattedPostalCode || "",
        phone: formattedPhone || "",
        email: person.email || "",
        gender: person.gender || "",
        notes: person.notes || "",
        clientId: person.clientId || "",
        relationship: person.relationship || "",
        profileImageFullUrl: person.profileImageFullUrl || null,
        useClientEmail: person.useClientEmail || false,
        useClientPhone: person.useClientPhone || false,
      };

      // Log para depuração
      console.log('Dados da pessoa carregados:', person);
      console.log('URL da imagem de perfil:', person.profileImageFullUrl);

      // Set saved person ID for document and contact tabs
      setSavedPersonId(person.id);

      // Enable all tabs when editing an existing person
      setAvailableTabs({
        info: true,
        documents: true,
        contacts: true,
        insurances: true
      });
    }
    // If creating a new person with a pre-selected client
    else if (initialClientId) {
      newFormData.clientId = initialClientId;
      newFormData.relationship = "Titular";
    }

    setFormData(newFormData);
    setErrors({});

    // Default to info tab when opening modal
    setActiveTab("info");
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.fullName) {
      newErrors.fullName = "Nome completo é obrigatório";
    }

    if (formData.cpf) {
      const cleanCpf = formData.cpf.replace(/\D/g, "");
      if (cleanCpf.length !== 11) {
        newErrors.cpf = "CPF deve ter 11 dígitos";
      }
    }

    // Only validate email format if an email is provided (it's not required)
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Email inválido";
    }

    if (formData.clientId && !formData.relationship) {
      newErrors.relationship = "Informe o relacionamento com o cliente";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async (data) => {
    console.log('Validando formulário antes de salvar');
    if (!validateForm()) {
      console.log('Validação falhou, não será possível salvar');
      return false;
    }

    setIsLoading(true);
    console.log('Iniciando processo de salvamento dos dados');

    try {
      console.log('Preparando payload para envio');
      const payload = {
        fullName: data.fullName,
        cpf: data.cpf ? data.cpf.replace(/\D/g, "") : null,
        birthDate: data.birthDate || null,
        address: data.address || null,
        neighborhood: data.neighborhood || null,
        city: data.city || null,
        state: data.state || null,
        postalCode: data.postalCode || null,
        phone: data.phone ? data.phone.replace(/\D/g, "") : null,
        email: data.email || null,
        gender: data.gender || null,
        notes: data.notes || null,
        clientId: data.clientId || null,
        relationship: data.relationship || null,
        profileImageUrl: data.profileImageUrl || null,
        useClientEmail: data.useClientEmail || false,
        useClientPhone: data.useClientPhone || false,
      };

      console.log('Enviando dados para o backend:', payload);


      let savedPerson;

      if (person) {
        // Update existing person
        console.log(`Atualizando pessoa existente (ID: ${person.id})`);
        savedPerson = await personsService.updatePerson(person.id, payload);
        console.log('Pessoa atualizada com sucesso:', savedPerson);
      } else {
        // Create new person
        console.log('Criando nova pessoa');
        savedPerson = await personsService.createPerson(payload);
        console.log('Nova pessoa criada com sucesso:', savedPerson);
      }

      // Set the ID so we can use it for documents and contacts tabs
      if (savedPerson && savedPerson.id) {
        console.log(`Definindo ID da pessoa salva: ${savedPerson.id}`);
        setSavedPersonId(savedPerson.id);

        // Processar dados temporários após salvar a pessoa
        await processTemporaryData(savedPerson.id);
      }

      return true;
    } catch (error) {
      console.error("Erro ao salvar pessoa:", error);

      // Handle API validation errors
      if (error.response?.data?.errors) {
        const apiErrors = {};
        error.response.data.errors.forEach(err => {
          apiErrors[err.param] = err.msg;
        });
        setErrors(apiErrors);
      } else {
        setErrors({
          submit: error.response?.data?.message || "Erro ao salvar pessoa"
        });
      }

      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Método para processar os dados temporários após salvar a pessoa
  const processTemporaryData = async (personId) => {
    console.log('Processando dados temporários para a pessoa ID:', personId);

    try {
      // Processar foto de perfil
      if (tempProfileImage) {
        console.log('Processando foto de perfil temporária');
        await personsService.uploadProfileImage(personId, tempProfileImage);
        setTempProfileImage(null);
      }

      // Processar documentos temporários
      if (tempDocuments.length > 0) {
        console.log('Processando documentos temporários:', tempDocuments.length);
        for (const doc of tempDocuments) {
          await personsService.uploadDocument(personId, doc.file, doc.type);
        }
        setTempDocuments([]);
      }

      // Processar contatos temporários
      if (tempContacts.length > 0) {
        console.log('Processando contatos temporários:', tempContacts.length);
        for (const contact of tempContacts) {
          await personsService.createContact(personId, contact);
        }
        setTempContacts([]);
      }

      // Processar convênios temporários
      if (tempInsurances.length > 0) {
        console.log('Processando convênios temporários:', tempInsurances.length);
        for (const insurance of tempInsurances) {
          await personsService.addPersonInsurance(personId, insurance);
        }
        setTempInsurances([]);
      }

      console.log('Processamento de dados temporários concluído com sucesso');
    } catch (error) {
      console.error('Erro ao processar dados temporários:', error);
      // Exibir mensagem de erro
      if (window.showToast) {
        window.showToast({
          type: 'error',
          message: 'Erro ao processar alguns dados adicionais. Por favor, verifique e tente novamente.'
        });
      }
    }
  };

  const handleTabChange = (tab) => {
    // Limpar mensagens de erro ao mudar de aba
    setErrors({});
    setShowSuccessMessage(false);

    // Verificar se a aba está disponível
    if (!availableTabs[tab]) {
      // Se a aba não estiver disponível, mostrar mensagem
      if (window.showToast) {
        window.showToast({
          type: 'warning',
          message: 'Complete a etapa atual antes de avançar para a próxima'
        });
      }
      return;
    }

    // Permitir a navegação entre abas se estiverem disponíveis
    setActiveTab(tab);

    // Exibir uma mensagem informativa se a pessoa ainda não foi salva
    if ((tab === "documents" || tab === "contacts" || tab === "insurances") && !savedPersonId && !person) {
      console.log('Mudando para a aba', tab, 'sem ID de pessoa');
    }
  };

  // Função para avançar para a próxima aba
  const handleNextTab = async () => {
    if (activeTab === "documents") {
      // Avançar para a próxima aba - Contatos
      setAvailableTabs(prev => ({
        ...prev,
        contacts: true
      }));

      setActiveTab("contacts");

      if (window.showToast) {
        window.showToast({
          type: 'success',
          message: tempDocuments.length > 0
            ? `${tempDocuments.length} documento(s) adicionado(s) com sucesso`
            : "Você pode continuar sem adicionar documentos"
        });
      }
    } else if (activeTab === "contacts") {
      // Avançar para a próxima aba - Convênios
      setAvailableTabs(prev => ({
        ...prev,
        insurances: true
      }));

      setActiveTab("insurances");

      if (window.showToast) {
        window.showToast({
          type: 'success',
          message: tempContacts.length > 0
            ? `${tempContacts.length} contato(s) adicionado(s) com sucesso`
            : "Você pode continuar sem adicionar contatos"
        });
      }
    }
  };

  const handlePersonInfoSubmit = async () => {
    console.log('Iniciando salvamento dos dados da pessoa');

    try {
      // Verificar se há uma imagem para fazer upload
      if (profileImageUploadRef.current && profileImageUploadRef.current.hasSelectedFile && profileImageUploadRef.current.hasSelectedFile()) {
        console.log('Nova imagem detectada, fazendo upload antes de salvar');

        // Obter o ID da pessoa (existente ou recém-criada)
        const personId = person ? person.id : savedPersonId;

        if (personId) {
          try {
            // Fazer o upload da imagem
            const imageResponse = await profileImageUploadRef.current.uploadSelectedImage();
            console.log('Upload concluído com sucesso:', imageResponse);

            if (imageResponse && imageResponse.profileImageUrl) {
              // Atualizar o formData com a nova URL da imagem
              setFormData(prev => ({
                ...prev,
                profileImageUrl: imageResponse.profileImageUrl,
                profileImageFullUrl: imageResponse.fullImageUrl
              }));

              console.log('FormData atualizado com a nova URL da imagem:', imageResponse.profileImageUrl);
            }
          } catch (error) {
            console.error('Erro ao fazer upload da imagem:', error);
          }
        } else {
          console.log('Pessoa ainda não tem ID, o upload será feito após salvar');
        }
      } else {
        console.log('Nenhuma nova imagem para fazer upload');
      }

      // Se estiver editando uma pessoa existente
      if (person || savedPersonId) {
        // Salvar os dados da pessoa
        const success = await handleSave(formData);
        console.log('Resultado do salvamento:', success ? 'Sucesso' : 'Falha');

        if (success) {
          // Mostrar mensagem de sucesso
          setShowSuccessMessage(true);

          // Habilitar todas as abas após salvar com sucesso
          setAvailableTabs({
            info: true,
            documents: true,
            contacts: true,
            insurances: true
          });

          // Exibir toast de sucesso
          if (window.showToast) {
            window.showToast({
              type: 'success',
              message: 'Informações salvas com sucesso. Agora você pode adicionar documentos, contatos e convênios.'
            });
          }

          // Fechar o modal após salvar (apenas na edição)
          onSuccess();
        }
      } else {
        // Criando uma nova pessoa - fluxo de etapas
        if (!validateForm()) return;

        // Validar informações básicas e avançar para a próxima etapa
        // Atualizar as abas disponíveis
        setAvailableTabs(prev => ({
          ...prev,
          documents: true
        }));

        // Avançar para a próxima aba - Documentos
        setActiveTab("documents");

        if (window.showToast) {
          window.showToast({
            type: 'success',
            message: 'Informações básicas validadas com sucesso'
          });
        }
      }
    } catch (error) {
      console.error('Erro durante o processo de salvamento:', error);
      if (window.showToast) {
        window.showToast({
          type: 'error',
          message: error.response?.data?.message || 'Erro ao salvar informações'
        });
      }
    }
  };

  // Função para finalizar e criar a pessoa
  const handleFinish = async () => {
    console.log('Finalizando e criando a pessoa');

    try {
      setIsLoading(true);

      // Salvar os dados da pessoa
      const success = await handleSave(formData);
      console.log('Resultado do salvamento:', success ? 'Sucesso' : 'Falha');

      if (success) {
        // Exibir toast de sucesso
        if (window.showToast) {
          window.showToast({
            type: 'success',
            message: 'Paciente criado com sucesso!'
          });
        }

        // Fechar o modal após criar a pessoa com sucesso
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao finalizar criação da pessoa:', error);
      if (window.showToast) {
        window.showToast({
          type: 'error',
          message: error.response?.data?.message || 'Erro ao criar paciente'
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    console.log(`PersonFormModal.handleChange: Atualizando campo ${name} para ${value}`);

    // Log do estado atual antes da atualização
    console.log('Estado atual do formulário antes da atualização:', formData);

    setFormData((prev) => {
      const newState = { ...prev, [name]: value };
      console.log('Novo estado do formulário após atualização:', newState);
      return newState;
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  // Componente de rodapé com botões
  const modalFooter = (
    <div className="flex justify-between items-center">
      <div>
        {activeTab !== "info" && (
          <ModalButton
            variant="secondary"
            moduleColor="people"
            onClick={() => {
              // Definir a aba anterior com base na aba atual
              const prevTab = {
                documents: "info",
                contacts: "documents",
                insurances: "contacts"
              }[activeTab];
              setActiveTab(prevTab);
            }}
            disabled={isLoading}
          >
            Voltar
          </ModalButton>
        )}
      </div>
      <div className="flex gap-3">
        <ModalButton
          variant="secondary"
          moduleColor="people"
          onClick={onClose}
          disabled={isLoading}
        >
          Cancelar
        </ModalButton>

        {person || savedPersonId ? (
          // Usuário existente - botão Salvar
          <ModalButton
            variant="primary"
            moduleColor="people"
            onClick={handlePersonInfoSubmit}
            isLoading={isLoading}
          >
            Salvar
          </ModalButton>
        ) : (
          // Novo usuário - botões Continuar ou Criar
          activeTab === "info" ? (
            <ModalButton
              variant="primary"
              moduleColor="people"
              onClick={handlePersonInfoSubmit}
              isLoading={isLoading}
            >
              Continuar
            </ModalButton>
          ) : activeTab === "insurances" ? (
            <ModalButton
              variant="primary"
              moduleColor="people"
              onClick={handleFinish}
              isLoading={isLoading}
            >
              Criar Paciente
            </ModalButton>
          ) : (
            <ModalButton
              variant="primary"
              moduleColor="people"
              onClick={handleNextTab}
              isLoading={isLoading}
            >
              Continuar
            </ModalButton>
          )
        )}
      </div>
    </div>
  );

  return (
    <ModuleModal
      isOpen={isOpen}
      onClose={onClose}
      title={person ? "Editar Pessoa" : "Novo Paciente"}
      icon={<User size={22} />}
      moduleColor="people"
      size="lg"
      animateExit={true}
      footer={modalFooter}
    >

        {/* Tabs */}
        <div className="border-b border-neutral-200 dark:border-gray-700">
          <div className="flex">
            <button
              onClick={() => handleTabChange("info")}
              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "info"
                ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                : "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                }`}
            >
              <User size={16} />
              <span>Informações</span>
            </button>
            <button
              onClick={() => handleTabChange("documents")}
              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "documents"
                ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                : availableTabs.documents
                  ? "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                  : "text-neutral-400 dark:text-gray-500 cursor-not-allowed"
                }`}
            >
              <FileText size={16} />
              <span>Documentos</span>
            </button>
            <button
              onClick={() => handleTabChange("contacts")}
              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "contacts"
                ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                : availableTabs.contacts
                  ? "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                  : "text-neutral-400 dark:text-gray-500 cursor-not-allowed"
                }`}
            >
              <Users size={16} />
              <span>Contatos</span>
            </button>
            <button
              onClick={() => handleTabChange("insurances")}
              className={`flex items-center gap-2 px-5 py-3 text-sm font-medium transition-colors ${activeTab === "insurances"
                ? "border-b-2 border-primary-500 dark:border-primary-400 text-primary-600 dark:text-primary-400"
                : availableTabs.insurances
                  ? "text-neutral-600 dark:text-gray-300 hover:text-neutral-800 dark:hover:text-white hover:bg-neutral-50 dark:hover:bg-gray-700"
                  : "text-neutral-400 dark:text-gray-500 cursor-not-allowed"
                }`}
            >
              <CreditCard size={16} />
              <span>Convênios</span>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto p-6">
          {errors.submit && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/50 text-red-700 dark:text-red-400 rounded-lg">
              {errors.submit}
            </div>
          )}

          {showSuccessMessage && (
            <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800/50 text-green-700 dark:text-green-400 rounded-lg flex items-center gap-2">
              <Check size={18} />
              <span>Informações salvas com sucesso! Você pode continuar adicionando documentos, contatos e convênios.</span>
            </div>
          )}

          {!savedPersonId && !person && activeTab !== "info" && (
            <div className="mb-4 p-3 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800/50 text-amber-700 dark:text-amber-400 rounded-lg flex items-center gap-2">
              <AlertCircle size={18} />
              <span>Salve as informações básicas antes de adicionar {activeTab === "documents" ? "documentos" : activeTab === "contacts" ? "contatos" : "convênios"}.</span>
            </div>
          )}

          {activeTab === "info" && (
            <PersonInfoTab
              formData={formData}
              setFormData={setFormData}
              errors={errors}
              isLoading={isLoading}
              handleChange={handleChange}
              onSubmit={handlePersonInfoSubmit}
              personId={savedPersonId || person?.id}
              profileImageUploadRef={profileImageUploadRef}
              isCreating={!savedPersonId && !person}
              onSetTempProfileImage={(file) => {
                console.log('Definindo foto de perfil temporária:', file?.name);
                setTempProfileImage(file);
              }}
              tempProfileImage={tempProfileImage}
            />
          )}

          {activeTab === "documents" && (
            <DocumentsTab
              personId={savedPersonId || person?.id}
              onClose={() => handleTabChange("info")}
              isCreating={!savedPersonId && !person}
              onAddTempDocument={(doc) => {
                console.log('Adicionando documento temporário:', doc);
                setTempDocuments(prev => [...prev, doc]);
              }}
              tempDocuments={tempDocuments}
            />
          )}

          {activeTab === "contacts" && (
            <ContactsTab
              personId={savedPersonId || person?.id}
              onClose={() => handleTabChange("info")}
              isCreating={!savedPersonId && !person}
              onAddTempContact={(contact) => {
                console.log('Adicionando contato temporário:', contact);
                setTempContacts(prev => [...prev, contact]);
              }}
              tempContacts={tempContacts}
            />
          )}

          {activeTab === "insurances" && (
            <PersonInsurancesTab
              personId={savedPersonId || person?.id}
              onClose={() => handleTabChange("info")}
              isCreating={!savedPersonId && !person}
              onAddTempInsurance={(insurance) => {
                console.log('Adicionando convênio temporário:', insurance);
                setTempInsurances(prev => [...prev, insurance]);
              }}
              tempInsurances={tempInsurances}
            />
          )}
        </div>

    </ModuleModal>
  );
};

export default PersonFormModal;