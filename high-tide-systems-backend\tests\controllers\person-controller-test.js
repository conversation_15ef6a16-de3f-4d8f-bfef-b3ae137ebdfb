// tests/controllers/person-controller-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Dados para testes
const testPerson = {
  fullName: `Test Person ${Date.now()}`,
  cpf: `${Date.now()}`.substring(0, 11),
  birthDate: '1990-01-01',
  address: 'Test Address',
  neighborhood: 'Test Neighborhood',
  city: 'Test City',
  state: 'TS',
  postalCode: '12345-678',
  phone: '1234567890',
  email: `test_person_${Date.now()}@example.com`,
  gender: 'M',
  notes: 'Test notes'
};

// Variáveis para armazenar IDs criados durante os testes
let personId;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do controlador de pessoas...');
  
  try {
    // Teste 1: Listar pessoas
    console.log('\n1. Listando pessoas...');
    const listResponse = await api.get('/persons');
    
    if (listResponse.status === 200) {
      console.log(`✅ ${listResponse.data.length} pessoas encontradas`);
    } else {
      console.log('❌ Falha ao listar pessoas');
    }
    
    // Teste 2: Criar uma nova pessoa
    console.log('\n2. Criando pessoa...');
    const createResponse = await api.post('/persons', testPerson);
    
    if (createResponse.status === 201) {
      console.log('✅ Pessoa criada com sucesso!');
      personId = createResponse.data.id;
      console.log(`ID da pessoa: ${personId}`);
    } else {
      console.log('❌ Falha ao criar pessoa');
      return;
    }
    
    // Teste 3: Obter pessoa por ID
    console.log('\n3. Obtendo pessoa por ID...');
    const getResponse = await api.get(`/persons/${personId}`);
    
    if (getResponse.status === 200) {
      console.log('✅ Pessoa obtida com sucesso!');
      console.log(`Nome: ${getResponse.data.fullName}`);
      console.log(`Email: ${getResponse.data.email}`);
    } else {
      console.log('❌ Falha ao obter pessoa');
    }
    
    // Teste 4: Atualizar pessoa
    console.log('\n4. Atualizando pessoa...');
    const updateResponse = await api.put(`/persons/${personId}`, {
      fullName: `${testPerson.fullName} Updated`,
      notes: 'Updated notes'
    });
    
    if (updateResponse.status === 200) {
      console.log('✅ Pessoa atualizada com sucesso!');
      console.log(`Novo nome: ${updateResponse.data.fullName}`);
    } else {
      console.log('❌ Falha ao atualizar pessoa');
    }
    
    // Teste 5: Buscar pessoa por CPF
    console.log('\n5. Buscando pessoa por CPF...');
    const searchResponse = await api.get(`/persons/search?cpf=${testPerson.cpf}`);
    
    if (searchResponse.status === 200 && searchResponse.data.length > 0) {
      console.log('✅ Pessoa encontrada por CPF com sucesso!');
      console.log(`Nome: ${searchResponse.data[0].fullName}`);
    } else {
      console.log('❌ Falha ao buscar pessoa por CPF');
    }
    
    // Teste 6: Excluir pessoa
    console.log('\n6. Excluindo pessoa...');
    const deleteResponse = await api.delete(`/persons/${personId}`);
    
    if (deleteResponse.status === 200) {
      console.log('✅ Pessoa excluída com sucesso!');
    } else {
      console.log('❌ Falha ao excluir pessoa');
    }
    
    console.log('\n✅ Todos os testes do controlador de pessoas concluídos com sucesso!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
