'use client';

import React, { createContext, useContext, useMemo } from 'react';
import { useAuth } from './AuthContext';
import { hasPermission, hasModuleAccess, getAllPermissions } from '@/utils/permissionConfig';
import { clientHasPermission, clientHasModuleAccess, CLIENT_PERMISSIONS, CLIENT_MODULES } from '@/utils/clientPermissions';

const PermissionContext = createContext(null);

export function PermissionProvider({ children }) {
  const { user } = useAuth();

  const permissionUtils = useMemo(() => {
    return {
      // Verificar permissão específica
      can: (permissionId) => {
        if (!user) return false;

        // Handle client permissions
        if (user.isClient) {
          return clientHasPermission(permissionId);
        }

        // Regular user permissions
        return hasPermission(user, permissionId);
      },

      // Verificar acesso ao módulo
      hasModule: (moduleId) => {
        if (!user) return false;

        // System Admin tem acesso a todos os módulos
        if (user.role === 'SYSTEM_ADMIN') return true;

        // Handle client module access
        if (user.isClient) {
          return clientHasModuleAccess(moduleId);
        }

        // Regular user module access
        return user.modules?.includes(moduleId);
      },

      // Verificar qualquer permissão de um conjunto
      canAny: (permissionIds) => {
        if (!Array.isArray(permissionIds) || permissionIds.length === 0) return false;

        if (user?.isClient) {
          return permissionIds.some(id => clientHasPermission(id));
        }

        return permissionIds.some(id => hasPermission(user, id));
      },

      // Verificar todas as permissões de um conjunto
      canAll: (permissionIds) => {
        if (!Array.isArray(permissionIds) || permissionIds.length === 0) return false;

        if (user?.isClient) {
          return permissionIds.every(id => clientHasPermission(id));
        }

        return permissionIds.every(id => hasPermission(user, id));
      },

      // Obter todas as permissões do usuário
      getUserPermissions: () => {
        if (user?.isClient) {
          return CLIENT_PERMISSIONS;
        }
        return user?.permissions || [];
      },

      // Obter todos os módulos do usuário
      getUserModules: () => {
        if (user?.isClient) {
          return CLIENT_MODULES;
        }
        return user?.modules || [];
      },

      // Verificar se o usuário é admin
      isAdmin: () => user?.role === 'SYSTEM_ADMIN' || user?.role === 'COMPANY_ADMIN' || false,

      // Verificar se o usuário é cliente
      isClient: () => {
        // System Admin e Company Admin nunca devem ser tratados como clientes
        if (user?.role === 'SYSTEM_ADMIN' || user?.role === 'COMPANY_ADMIN') {
          return false;
        }
        // Verificar se o usuário tem a flag isClient
        return user?.isClient === true || user?.role === 'CLIENT' || false;
      }
    };
  }, [user]);

  return (
    <PermissionContext.Provider value={permissionUtils}>
      {children}
    </PermissionContext.Provider>
  );
}

export function usePermission() {
  const context = useContext(PermissionContext);

  if (context === null) {
    throw new Error('usePermission deve ser usado dentro de um PermissionProvider');
  }

  return context;
}