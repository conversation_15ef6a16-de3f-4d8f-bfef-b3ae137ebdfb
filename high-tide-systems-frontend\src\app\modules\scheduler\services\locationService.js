import { exportService } from "@/app/services/exportService";
import { api } from "@/utils/api";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";

export const locationService = {
  // Listar localizações com suporte a paginação e filtros
  getLocations: async ({ page = 1, limit = 10, search, active, branchId, companyId, locationIds } = {}) => {
    try {
      const params = new URLSearchParams();
      if (page) params.append('page', page);
      if (limit) params.append('limit', limit);
      if (search) params.append('search', search);
      if (active !== undefined) params.append('active', active);
      if (branchId) params.append('branchId', branchId);
      if (companyId) params.append('companyId', companyId);

      // Adicionar suporte para múltiplos IDs de localizações
      if (locationIds && Array.isArray(locationIds) && locationIds.length > 0) {
        locationIds.forEach(id => params.append('locationIds', id));
      }

      const response = await api.get(`/locations?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar localizações:", error);
      throw error;
    }
  },

  // Obter uma localização específica
  getLocation: async (id) => {
    try {
      const response = await api.get(`/locations/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar localização ${id}:`, error);
      throw error;
    }
  },

  // Criar uma nova localização
  createLocation: async (data) => {
    try {
      const response = await api.post('/locations', data);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar localização:", error);
      throw error;
    }
  },

  // Atualizar uma localização existente
  updateLocation: async (id, data) => {
    try {
      const response = await api.put(`/locations/${id}`, data);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar localização ${id}:`, error);
      throw error;
    }
  },

  // Alternar o status de uma localização (ativo/inativo)
  toggleLocationStatus: async (id) => {
    try {
      const response = await api.patch(`/locations/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao alterar status da localização ${id}:`, error);
      throw error;
    }
  },

  // Excluir uma localização
  deleteLocation: async (id) => {
    try {
      await api.delete(`/locations/${id}`);
      return true;
    } catch (error) {
      console.error(`Erro ao excluir localização ${id}:`, error);
      throw error;
    }
  },
  /**
 * Exporta a lista de localizações com os filtros aplicados
 * @param {Object} filters - Filtros atuais (busca, status, unidade, etc)
 * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
 * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
 */
  exportLocations: async (filters, exportFormat = "xlsx") => {
    try {
      // Obter os dados filtrados da API
      const response = await locationService.getLocations({
        ...filters,
        limit: 1000, // Aumentamos o limite para exportar mais dados
      });

      // Extrair os dados das localizações
      const data = response?.locations || [];

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "name", header: "Nome" },
        { key: "address", header: "Endereço" },
        { key: "phone", header: "Telefone", type: "phone" },
        {
          key: "branchName",
          header: "Unidade",
          format: (value, item) => item.branch ? item.branch.name : "Não definida"
        },
        {
          key: "active",
          header: "Status",
          format: (value) => value ? "Ativo" : "Inativo",
          align: "center",
          width: 20
        },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(location => {
        return {
          name: location.name || "",
          address: location.address || "",
          phone: location.phone || "",
          branchName: location.branch ? location.branch.name : "",
          active: location.active,
          createdAt: location.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.active !== undefined) {
        subtitleParts.push(`Status: ${filters.active ? "Ativos" : "Inativos"}`);
      }
      if (filters.branchId) {
        const branchName = data.length > 0 && data[0].branch ? data[0].branch.name : "Selecionada";
        subtitleParts.push(`Unidade: ${branchName}`);
      }
      if (filters.locationIds && Array.isArray(filters.locationIds) && filters.locationIds.length > 0) {
        subtitleParts.push(`Locais: ${filters.locationIds.length} selecionados`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "localizacoes",
        columns,
        title: "Lista de Localizações",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar localizações:", error);
      return false;
    }
  }
};

export default locationService;