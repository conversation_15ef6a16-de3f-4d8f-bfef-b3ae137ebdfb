/**
 * Middleware que verifica se o usuário é um administrador da empresa
 * Este middleware deve ser usado após o middleware de autenticação
 */
const companyAdminMiddleware = (req, res, next) => {
    // Verifica se o usuário está autenticado
    if (!req.user) {
      return res.status(401).json({ message: 'Autenticação necessária' });
    }
  
    // System Admin tem permissões de Company Admin
    if (req.user.role === 'SYSTEM_ADMIN') {
      return next();
    }
  
    // Verifica se o usuário é admin da empresa
    if (req.user.role !== 'COMPANY_ADMIN') {
      return res.status(403).json({ 
        message: 'Acesso restrito a administradores da empresa' 
      });
    }
  
    next();
  };
  
  module.exports = companyAdminMiddleware;