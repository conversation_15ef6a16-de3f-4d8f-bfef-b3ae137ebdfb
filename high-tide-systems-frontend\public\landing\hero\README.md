# Imagens do Slider da Landing Page

Este diretório contém as imagens utilizadas no slider da seção Hero da landing page.

## Recomendações para Imagens de Alta Qualidade

Para garantir que as imagens do slider tenham a melhor qualidade possível:

1. **Resolução ideal**: 1920x1080 pixels (16:9) ou 1600x900 pixels
   - Mantenha a proporção 16:9 para preencher adequadamente o slider
   - Resoluções maiores podem ser usadas, mas serão redimensionadas

2. **Formato de arquivo**:
   - PNG: Melhor para capturas de tela com texto nítido e interfaces
   - WebP: Melhor equilíbrio entre qualidade e tamanho de arquivo
   - JPG: Use apenas para fotos, com compressão de 85-90%

3. **Tamanho de arquivo**:
   - Ideal: 200KB - 500KB por imagem
   - Comprima as imagens usando ferramentas como TinyPNG, Squoosh ou ImageOptim
   - Imagens muito grandes podem afetar o desempenho da página

4. **Otimização para web**:
   - Remova metadados desnecessários
   - Use ferramentas de compressão específicas para web
   - Considere usar o formato WebP com fallback para PNG/JPG

5. **Capturas de tela**:
   - Faça capturas em tela cheia para mostrar o contexto completo
   - Use uma resolução de tela alta ao fazer as capturas
   - Considere usar ferramentas como Snagit ou ShareX para capturas de alta qualidade

6. **Edição**:
   - Ajuste o brilho e contraste para melhorar a legibilidade
   - Considere adicionar uma leve nitidez (sharpening) para melhorar detalhes
   - Mantenha as cores fiéis à interface original

## Nomes de Arquivo

Mantenha os nomes de arquivo consistentes com os definidos no componente Hero.js:

- `dashboard.png`: Visão geral do dashboard
- `calendar.png`: Tela de agendamento/calendário
- `patients.png`: Gestão de pacientes
- `financial.png`: Relatórios financeiros
- `mobile.png`: Versão responsiva/mobile

## Processo de Otimização Recomendado

1. Faça a captura de tela em alta resolução
2. Recorte para mostrar apenas o conteúdo relevante
3. Redimensione para 1920x1080 ou 1600x900 pixels
4. Aplique ajustes de nitidez se necessário
5. Salve como PNG
6. Comprima usando TinyPNG ou ferramenta similar
7. Coloque o arquivo neste diretório
