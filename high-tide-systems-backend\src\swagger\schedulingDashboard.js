/**
 * @swagger
 * /schedulings/dashboard:
 *   get:
 *     summary: Obtém dados estatísticos para o dashboard de agendamentos
 *     tags: [Agendamentos]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7days, 30days, 3months, 1year, next7days, next30days]
 *           default: 30days
 *         description: Período para filtrar os dados do dashboard. Valores com prefixo 'next' representam períodos futuros. Ignorado se startDate e endDate forem fornecidos.
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Data de início para filtrar os dados (formato ISO 8601). Se fornecido, endDate também deve ser fornecido.
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Data de fim para filtrar os dados (formato ISO 8601). Se fornecido, startDate também deve ser fornecido.
 *     responses:
 *       200:
 *         description: Dados do dashboard de agendamentos obtidos com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 stats:
 *                   type: object
 *                   properties:
 *                     totalAppointments:
 *                       type: integer
 *                       description: Total de agendamentos no período
 *                     completedAppointments:
 *                       type: integer
 *                       description: Total de agendamentos concluídos
 *                     cancelledAppointments:
 *                       type: integer
 *                       description: Total de agendamentos cancelados
 *                     noShowAppointments:
 *                       type: integer
 *                       description: Total de agendamentos onde o cliente não compareceu
 *                     pendingAppointments:
 *                       type: integer
 *                       description: Total de agendamentos pendentes
 *                     confirmedAppointments:
 *                       type: integer
 *                       description: Total de agendamentos confirmados
 *                 topClients:
 *                   type: array
 *                   description: Lista dos 5 clientes com mais agendamentos
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: ID do cliente
 *                       name:
 *                         type: string
 *                         description: Nome do cliente
 *                       count:
 *                         type: integer
 *                         description: Quantidade de agendamentos
 *                 topServices:
 *                   type: array
 *                   description: Lista dos 5 serviços mais agendados
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: ID do serviço
 *                       name:
 *                         type: string
 *                         description: Nome do serviço
 *                       count:
 *                         type: integer
 *                         description: Quantidade de agendamentos
 *                 topLocations:
 *                   type: array
 *                   description: Lista dos 5 locais mais agendados
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: ID do local
 *                       name:
 *                         type: string
 *                         description: Nome do local
 *                       count:
 *                         type: integer
 *                         description: Quantidade de agendamentos
 *                 statusDistribution:
 *                   type: array
 *                   description: Distribuição de agendamentos por status
 *                   items:
 *                     type: object
 *                     properties:
 *                       status:
 *                         type: string
 *                         description: Código do status
 *                       label:
 *                         type: string
 *                         description: Nome amigável do status
 *                       count:
 *                         type: integer
 *                         description: Quantidade de agendamentos com este status
 *                 appointmentTrends:
 *                   type: array
 *                   description: Tendência de agendamentos ao longo do tempo
 *                   items:
 *                     type: object
 *                     properties:
 *                       date:
 *                         type: string
 *                         description: Data (formato varia de acordo com o período)
 *                       count:
 *                         type: integer
 *                         description: Quantidade de agendamentos na data
 *       401:
 *         description: Não autorizado - Token inválido ou expirado
 *       403:
 *         description: Proibido - Usuário não tem permissão para acessar os dados
 *       500:
 *         description: Erro interno do servidor
 */