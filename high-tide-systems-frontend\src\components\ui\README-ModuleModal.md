# Componentes de Modal para Módulos

Este documento descreve o uso dos componentes `ModuleModal` e `ModalButton` para criar modais consistentes em todo o sistema.

## ModuleModal

O componente `ModuleModal` é um modal genérico que pode ser personalizado para diferentes módulos do sistema, mantendo a consistência visual.

### Propriedades

| Propriedade | Tipo | Padrão | Descrição |
|-------------|------|--------|-----------|
| isOpen | boolean | - | Se o modal está aberto |
| onClose | function | - | Função para fechar o modal |
| title | string | - | Título do modal |
| icon | ReactNode | - | Ícone do título (opcional) |
| children | ReactNode | - | Conteúdo do modal |
| size | string | 'md' | Tamanho do modal (sm, md, lg, xl, full) |
| moduleColor | string | 'default' | Cor do módulo (people, scheduler, admin, financial, default) |
| footer | ReactNode | - | Conteúdo do rodapé (opcional) |
| preventClose | boolean | false | Impede o fechamento do modal ao clicar fora ou no X |
| variant | string | 'default' | Variante do modal (default, info, success, warning, danger) |
| showCloseButton | boolean | true | Mostrar botão de fechar no cabeçalho |
| onInteractOutside | function | - | Função chamada quando o usuário interage fora do modal |

### Exemplo de Uso

```jsx
import ModuleModal from '@/components/ui/ModuleModal';
import { Calendar } from 'lucide-react';

const MyComponent = () => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <button onClick={() => setIsOpen(true)}>Abrir Modal</button>
      
      <ModuleModal
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        title="Título do Modal"
        icon={<Calendar size={22} />}
        moduleColor="scheduler"
        size="md"
        footer={<div>Conteúdo do rodapé</div>}
      >
        <div>Conteúdo do modal</div>
      </ModuleModal>
    </>
  );
};
```

## ModalButton

O componente `ModalButton` é um botão estilizado para uso em modais, com suporte para diferentes temas de módulos.

### Propriedades

| Propriedade | Tipo | Padrão | Descrição |
|-------------|------|--------|-----------|
| type | string | 'button' | Tipo do botão (button, submit, reset) |
| onClick | function | - | Função para lidar com cliques |
| children | ReactNode | - | Conteúdo do botão |
| variant | string | 'primary' | Variante do botão (primary, secondary, danger, success, warning) |
| moduleColor | string | 'default' | Cor do módulo (people, scheduler, admin, financial, default) |
| isLoading | boolean | false | Se o botão está em estado de carregamento |
| disabled | boolean | false | Se o botão está desabilitado |
| className | string | '' | Classes adicionais |

### Exemplo de Uso

```jsx
import ModalButton from '@/components/ui/ModalButton';

const ModalFooter = ({ onClose, onSave, isLoading }) => {
  return (
    <div className="flex justify-end gap-3">
      <ModalButton
        variant="secondary"
        moduleColor="scheduler"
        onClick={onClose}
        disabled={isLoading}
      >
        Cancelar
      </ModalButton>
      
      <ModalButton
        variant="primary"
        moduleColor="scheduler"
        onClick={onSave}
        isLoading={isLoading}
      >
        Salvar
      </ModalButton>
    </div>
  );
};
```

## Cores dos Módulos

Os componentes suportam diferentes temas de cores para cada módulo:

- **default**: Cores neutras padrão
- **people**: Tons de laranja/amber
- **scheduler**: Tons de roxo/violeta
- **admin**: Tons de vermelho/rosa
- **financial**: Tons de verde/esmeralda

## Variantes de Botões

Os botões suportam diferentes variantes:

- **primary**: Botão principal com gradiente de cores do módulo
- **secondary**: Botão secundário com borda e texto na cor do módulo
- **danger**: Botão vermelho para ações destrutivas
- **success**: Botão verde para ações de sucesso
- **warning**: Botão âmbar para ações de alerta

## Variantes de Modal

Os modais suportam diferentes variantes:

- **default**: Modal padrão sem ícone adicional
- **info**: Modal com ícone de informação
- **success**: Modal com ícone de sucesso
- **warning**: Modal com ícone de alerta
- **danger**: Modal com ícone de perigo
