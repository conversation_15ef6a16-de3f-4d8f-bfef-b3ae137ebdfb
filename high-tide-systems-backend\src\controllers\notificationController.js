const prisma = require('../utils/prisma');
const emailService = require('../services/emailService');
const reminderService = require('../services/reminderService');

class NotificationController {
  /**
   * Confirma um agendamento através do token enviado por email
   */
  static async confirmAppointment(req, res) {
    try {
      const { token } = req.params;

      // Decodifica o token
      const decodedToken = Buffer.from(token, 'base64').toString('ascii');

      // Token format: confirm-SCHEDULING_ID-TIMESTAMP
      const [action, schedulingId] = decodedToken.split('-');

      if (action !== 'confirm') {
        return res.status(400).json({ message: 'Token inválido' });
      }

      // Busca o agendamento
      const scheduling = await prisma.scheduling.findUnique({
        where: { id: schedulingId }
      });

      if (!scheduling) {
        return res.status(404).json({ message: 'Agendamento não encontrado' });
      }

      // Atualiza o status para confirmado
      await prisma.scheduling.update({
        where: { id: schedulingId },
        data: { status: 'CONFIRMED' }
      });

      // Redireciona para página de sucesso
      res.redirect(`${process.env.FRONTEND_URL}/appointment-confirmed`);
    } catch (error) {
      console.error('Erro ao confirmar agendamento:', error);
      res.status(500).json({ message: 'Erro ao processar confirmação' });
    }
  }

  /**
   * Cancela um agendamento através do token enviado por email
   */
  static async cancelAppointment(req, res) {
    try {
      const { token } = req.params;

      // Decodifica o token
      const decodedToken = Buffer.from(token, 'base64').toString('ascii');

      // Token format: cancel-SCHEDULING_ID-TIMESTAMP
      const [action, schedulingId] = decodedToken.split('-');

      if (action !== 'cancel') {
        return res.status(400).json({ message: 'Token inválido' });
      }

      // Busca o agendamento
      const scheduling = await prisma.scheduling.findUnique({
        where: { id: schedulingId }
      });

      if (!scheduling) {
        return res.status(404).json({ message: 'Agendamento não encontrado' });
      }

      // Verifica se o agendamento já foi cancelado
      if (scheduling.status === 'CANCELLED') {
        return res.redirect(`${process.env.FRONTEND_URL}/appointment-already-cancelled`);
      }

      // Atualiza o status para cancelado
      await prisma.scheduling.update({
        where: { id: schedulingId },
        data: { status: 'CANCELLED' }
      });

      // Redireciona para página de cancelamento
      res.redirect(`${process.env.FRONTEND_URL}/appointment-cancelled`);
    } catch (error) {
      console.error('Erro ao cancelar agendamento:', error);
      res.status(500).json({ message: 'Erro ao processar cancelamento' });
    }
  }

  /**
   * Testa o envio de email (apenas para desenvolvimento)
   */
  static async testEmail(req, res) {
    try {
      const { email, type } = req.body;

      if (!email) {
        return res.status(400).json({ message: 'Email é obrigatório' });
      }

      if (type === 'reminder') {
        // Gera um agendamento fictício para testar o lembrete
        const testData = {
          id: 'test-123',
          title: 'Consulta de Teste',
          description: 'Este é um email de teste do sistema',
          startDate: new Date(new Date().setDate(new Date().getDate() + 1)),
          endDate: new Date(new Date().setDate(new Date().getDate() + 1) + 3600000), // +1 hora
          status: 'PENDING'
        };

        const testClient = {
          fullName: 'Cliente Teste',
          email: email
        };

        const testProvider = {
          fullName: 'Dr. Teste'
        };

        const testServiceType = {
          name: 'Consulta de Rotina'
        };

        const testLocation = {
          name: 'Clínica Teste',
          address: 'Rua de Teste, 123'
        };

        const result = await emailService.sendReminderEmail(
          testData,
          testClient,
          testProvider,
          testServiceType,
          testLocation
        );

        return res.json(result);
      } else {
        // Email de novo agendamento (padrão)
        const testData = {
          id: 'test-123',
          title: 'Consulta de Teste',
          description: 'Este é um email de teste do sistema',
          startDate: new Date(),
          endDate: new Date(new Date().getTime() + 3600000), // +1 hora
          status: 'PENDING'
        };

        const testClient = {
          fullName: 'Cliente Teste',
          email: email
        };

        const testProvider = {
          fullName: 'Dr. Teste'
        };

        const testServiceType = {
          name: 'Consulta de Rotina'
        };

        const testLocation = {
          name: 'Clínica Teste',
          address: 'Rua de Teste, 123'
        };

        const result = await emailService.sendNewAppointmentEmail(
          testData,
          testClient,
          testProvider,
          testServiceType,
          testLocation
        );

        return res.json(result);
      }
    } catch (error) {
      console.error('Erro ao testar email:', error);
      res.status(500).json({ message: 'Erro ao enviar email de teste' });
    }
  }

  /**
   * Envia um lembrete manualmente para um agendamento específico
   */
  static async sendManualReminder(req, res) {
    try {
      const { id } = req.params;

      const result = await reminderService.sendReminderForScheduling(id);

      res.json(result);
    } catch (error) {
      console.error('Erro ao enviar lembrete manual:', error);
      res.status(500).json({ message: 'Erro ao enviar lembrete', error: error.message });
    }
  }

  /**
   * Lista todas as notificações do usuário
   */
  static async list(req, res) {
    try {
      const userId = req.user.id;
      const { page = 1, limit = 20 } = req.query;
      const skip = (page - 1) * limit;

      // Buscar notificações do usuário
      const notifications = await prisma.notification.findMany({
        where: {
          userId,
          OR: [
            { expiresAt: { gt: new Date() } },
            { expiresAt: null }
          ]
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: parseInt(skip),
        take: parseInt(limit),
      });

      // Contar total de notificações
      const total = await prisma.notification.count({
        where: {
          userId,
          OR: [
            { expiresAt: { gt: new Date() } },
            { expiresAt: null }
          ]
        },
      });

      res.json({
        data: notifications,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      });
    } catch (error) {
      console.error('Erro ao listar notificações:', error);
      res.status(500).json({ message: 'Erro ao listar notificações', error: error.message });
    }
  }

  /**
   * Retorna a contagem de notificações não lidas do usuário
   */
  static async getUnreadCount(req, res) {
    try {
      const userId = req.user.id;

      // Contar notificações não lidas
      const count = await prisma.notification.count({
        where: {
          userId,
          read: false,
          OR: [
            { expiresAt: { gt: new Date() } },
            { expiresAt: null }
          ]
        },
      });

      res.json({ count });
    } catch (error) {
      console.error('Erro ao contar notificações não lidas:', error);
      res.status(500).json({ message: 'Erro ao contar notificações não lidas', error: error.message });
    }
  }

  /**
   * Marca uma notificação como lida
   */
  static async markAsRead(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;

      // Verificar se a notificação existe e pertence ao usuário
      const notification = await prisma.notification.findFirst({
        where: {
          id,
          userId
        }
      });

      if (!notification) {
        return res.status(404).json({ message: 'Notificação não encontrada' });
      }

      // Atualizar para lida
      await prisma.notification.update({
        where: { id },
        data: { read: true }
      });

      res.json({ message: 'Notificação marcada como lida' });
    } catch (error) {
      console.error('Erro ao marcar notificação como lida:', error);
      res.status(500).json({ message: 'Erro ao marcar notificação como lida', error: error.message });
    }
  }

  /**
   * Marca todas as notificações do usuário como lidas
   */
  static async markAllAsRead(req, res) {
    try {
      const userId = req.user.id;

      // Atualizar todas as notificações não lidas do usuário
      await prisma.notification.updateMany({
        where: {
          userId,
          read: false
        },
        data: { read: true }
      });

      res.json({ message: 'Todas as notificações marcadas como lidas' });
    } catch (error) {
      console.error('Erro ao marcar todas notificações como lidas:', error);
      res.status(500).json({ message: 'Erro ao marcar todas notificações como lidas', error: error.message });
    }
  }
}

module.exports = {
  NotificationController
};