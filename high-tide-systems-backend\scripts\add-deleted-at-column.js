/**
 * <PERSON>ript para adicionar a coluna deletedAt às tabelas Profession e ProfessionGroup
 * 
 * Este script executa comandos SQL diretamente no banco de dados para adicionar a coluna
 * deletedAt às tabelas Profession e ProfessionGroup.
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Iniciando adição da coluna deletedAt às tabelas...');

    // Adicionar coluna deletedAt à tabela ProfessionGroup
    await prisma.$executeRawUnsafe(`
      ALTER TABLE "ProfessionGroup" 
      ADD COLUMN IF NOT EXISTS "deletedAt" TIMESTAMP(3);
    `);
    console.log('Coluna deletedAt adicionada à tabela ProfessionGroup');

    // Adicionar coluna deletedAt à tabela Profession
    await prisma.$executeRawUnsafe(`
      ALTER TABLE "Profession" 
      ADD COLUMN IF NOT EXISTS "deletedAt" TIMESTAMP(3);
    `);
    console.log('Coluna deletedAt adicionada à tabela Profession');

    // Criar índice para a coluna deletedAt na tabela ProfessionGroup
    await prisma.$executeRawUnsafe(`
      CREATE INDEX IF NOT EXISTS "ProfessionGroup_deletedAt_idx" 
      ON "ProfessionGroup"("deletedAt");
    `);
    console.log('Índice criado para a coluna deletedAt na tabela ProfessionGroup');

    // Criar índice para a coluna deletedAt na tabela Profession
    await prisma.$executeRawUnsafe(`
      CREATE INDEX IF NOT EXISTS "Profession_deletedAt_idx" 
      ON "Profession"("deletedAt");
    `);
    console.log('Índice criado para a coluna deletedAt na tabela Profession');

    console.log('Adição da coluna deletedAt concluída com sucesso!');
  } catch (error) {
    console.error('Erro ao adicionar coluna deletedAt:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
