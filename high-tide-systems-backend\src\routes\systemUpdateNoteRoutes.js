const express = require('express');
const router = express.Router();
const { authenticate } = require('../middlewares/auth');
const systemAdminMiddleware = require('../middlewares/systemAdmin');
const { SystemUpdateNoteController } = require('../controllers/systemUpdateNoteController');

// Get the latest update note (public route, no authentication required)
router.get('/latest', SystemUpdateNoteController.getLatest);

// Protected routes (require authentication)
router.get('/', authenticate, SystemUpdateNoteController.getAll);

// Protected routes (require system admin role)
router.post('/', authenticate, systemAdminMiddleware, SystemUpdateNoteController.create);
router.put('/:id', authenticate, systemAdminMiddleware, SystemUpdateNoteController.update);
router.delete('/:id', authenticate, systemAdminMiddleware, SystemUpdateNoteController.delete);

module.exports = router;
