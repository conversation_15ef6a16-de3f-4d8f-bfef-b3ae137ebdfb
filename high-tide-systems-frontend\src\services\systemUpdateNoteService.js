import { api } from '@/utils/api';

/**
 * Service for managing system update notes
 */
const systemUpdateNoteService = {
  /**
   * Get the latest update note
   * @returns {Promise} Promise with the latest update note
   */
  getLatest: async () => {
    try {
      const response = await api.get('/system-update-notes/latest');
      return response.data;
    } catch (error) {
      console.error('Error fetching latest update note:', error);
      throw error;
    }
  },

  /**
   * Create a new update note
   * @param {Object} data - The update note data
   * @param {string} data.content - The content of the update note
   * @returns {Promise} Promise with the created update note
   */
  create: async (data) => {
    try {
      const response = await api.post('/system-update-notes', data);
      return response.data;
    } catch (error) {
      console.error('Error creating update note:', error);
      throw error;
    }
  },

  /**
   * Update an existing update note
   * @param {string} id - The ID of the update note to update
   * @param {Object} data - The update note data
   * @param {string} data.content - The content of the update note
   * @returns {Promise} Promise with the updated update note
   */
  update: async (id, data) => {
    try {
      const response = await api.put(`/system-update-notes/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating update note:', error);
      throw error;
    }
  },

  /**
   * Delete an update note
   * @param {string} id - The ID of the update note to delete
   * @returns {Promise} Promise with the deleted update note
   */
  delete: async (id) => {
    try {
      const response = await api.delete(`/system-update-notes/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting update note:', error);
      throw error;
    }
  },

  /**
   * Get all update notes
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Number of items per page
   * @returns {Promise} Promise with the update notes
   */
  getAll: async (params = {}) => {
    try {
      const response = await api.get('/system-update-notes', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching update notes:', error);
      throw error;
    }
  }
};

export default systemUpdateNoteService;
