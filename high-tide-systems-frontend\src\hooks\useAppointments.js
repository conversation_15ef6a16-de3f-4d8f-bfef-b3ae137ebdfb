'use client';

import { useState, useEffect } from 'react';
import { appointmentService } from '@/app/modules/scheduler/index';

/**
 * Hook para gerenciar dados de agendamentos
 * @param {boolean} useMockData - Se deve usar dados falsos ou reais da API
 * @param {Object} filters - Filtros adicionais para a busca de agendamentos
 * @returns {Object} - { appointments, isLoading, error }
 */
export const useAppointments = (useMockData = false, filters = {}) => {
  const [appointments, setAppointments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        setIsLoading(true);
        setError(null);

        if (useMockData) {
          // Dados mockados
          const today = new Date();
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);

          const nextWeek = new Date(today);
          nextWeek.setDate(nextWeek.getDate() + 4);

          const mockAppointments = [
            {
              id: 1,
              title: "Consulta de Rotina",
              startDate: today.toISOString(),
              status: "CONFIRMED",
              person: { fullName: "Maria Silva" },
              provider: { fullName: "Dr. Carlos Mendes" },
              location: { name: "Consultório Principal" }
            },
            {
              id: 2,
              title: "Retorno - Exames",
              startDate: today.toISOString(),
              status: "PENDING",
              person: { fullName: "João Oliveira" },
              provider: { fullName: "Dra. Paula Santos" },
              location: { name: "Sala 205" }
            },
            {
              id: 3,
              title: "Fisioterapia",
              startDate: tomorrow.toISOString(),
              status: "CONFIRMED",
              person: { fullName: "Ana Costa" },
              provider: { fullName: "Fisio. Roberto Alves" },
              location: { name: "Centro de Reabilitação" }
            },
            {
              id: 4,
              title: "Avaliação Psicológica",
              startDate: tomorrow.toISOString(),
              status: "CONFIRMED",
              person: { fullName: "Pedro Souza" },
              provider: { fullName: "Psic. Márcia Lima" },
              location: { name: "Consultório 3" }
            },
            {
              id: 5,
              title: "Sessão de Acupuntura",
              startDate: nextWeek.toISOString(),
              status: "PENDING",
              person: { fullName: "Fernanda Dias" },
              provider: { fullName: "Dr. Akira Tanaka" },
              location: { name: "Sala de Terapias" }
            }
          ];

          setAppointments(processAppointments(mockAppointments));
        } else {
          // Chamada real à API
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const todayStr = today.toISOString();

          const twoWeeks = new Date(today);
          twoWeeks.setDate(twoWeeks.getDate() + 14);
          const twoWeeksStr = twoWeeks.toISOString();

          // Combinar os filtros padrão com os filtros personalizados
          const apiFilters = {
            startDate: todayStr,
            endDate: twoWeeksStr,
            limit: 8,
            page: 1,
            status: ['CONFIRMED', 'PENDING'], // Apenas agendamentos confirmados ou pendentes
            ...filters // Sobrescrever com filtros personalizados, se fornecidos
          };

          console.log('Buscando agendamentos com filtros:', apiFilters);
          const result = await appointmentService.getAppointments(apiFilters);

          // Ordenar por data de início (mais próximos primeiro)
          const sortedAppointments = result.appointments.sort((a, b) => {
            return new Date(a.startDate) - new Date(b.startDate);
          });

          setAppointments(processAppointments(sortedAppointments));
        }
      } catch (err) {
        console.error('Erro ao buscar agendamentos:', err);
        setError('Não foi possível carregar os agendamentos');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppointments();
  }, [useMockData, JSON.stringify(filters)]); // Adicionar filters como dependência

  // Função auxiliar para processar dados de agendamentos
  const processAppointments = (appointments) => {
    return appointments.map(apt => ({
      ...apt,
      providerName: apt.provider?.fullName || 'Médico não especificado',
      personName: apt.person?.fullName || apt.Person?.fullName || 'Paciente não especificado',
      locationName: apt.location?.name || 'Local não especificado',
    }));
  };

  return { appointments, isLoading, error };
};