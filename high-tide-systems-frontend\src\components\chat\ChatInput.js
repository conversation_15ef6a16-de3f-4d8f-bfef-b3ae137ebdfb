'use client';

import React, { useState } from 'react';
import { Send, Paperclip, Smile } from 'lucide-react';
import { useChat } from '@/contexts/ChatContext';

const ChatInput = ({ conversationId, compact = false }) => {
  const [message, setMessage] = useState('');
  const { sendMessage } = useChat();

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!message.trim()) return;

    sendMessage(conversationId, message.trim());
    setMessage('');
  };

  return (
    <form
      onSubmit={handleSubmit}
      className={`flex items-center gap-2 p-4 border-t border-orange-200 dark:border-orange-700 bg-white dark:bg-gray-800 ${compact ? 'py-3' : 'py-4'}`}
    >
      {!compact && (
        <button
          type="button"
          className="p-2 text-orange-500 hover:text-orange-600 dark:text-orange-400 dark:hover:text-orange-300 rounded-full hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors"
          aria-label="Anexar arquivo"
        >
          <Paperclip size={compact ? 16 : 18} />
        </button>
      )}

      <div className="flex-1 relative">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Digite sua mensagem..."
          className="w-full py-2.5 px-4 bg-orange-50 dark:bg-orange-900/20 rounded-full focus:outline-none focus:ring-2 focus:ring-orange-500 dark:focus:ring-orange-600 text-gray-900 dark:text-gray-100 border border-orange-200 dark:border-orange-800"
        />

        {!compact && (
          <button
            type="button"
            className="absolute right-4 top-1/2 transform -translate-y-1/2 text-orange-500 hover:text-orange-600 dark:text-orange-400 dark:hover:text-orange-300"
            aria-label="Inserir emoji"
          >
            <Smile size={18} />
          </button>
        )}
      </div>

      <button
        type="submit"
        disabled={!message.trim()}
        className={`p-2.5 ${message.trim() ? 'bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600 dark:from-orange-600 dark:to-amber-600 dark:hover:from-orange-700 dark:hover:to-amber-700 text-white shadow-sm' : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'} rounded-full transition-colors`}
        aria-label="Enviar mensagem"
      >
        <Send size={compact ? 16 : 18} />
      </button>
    </form>
  );
};

export default ChatInput;
