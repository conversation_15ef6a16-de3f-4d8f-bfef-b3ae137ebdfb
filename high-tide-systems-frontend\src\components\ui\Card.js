/**
 * Componentes de Card
 * 
 * Este arquivo contém componentes para cards consistentes em todo o sistema.
 * Todos os componentes seguem o sistema de design definido.
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * Componente de Card
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conteúdo do card
 * @param {string} props.className - Classes adicionais
 * @param {string} props.variant - Variante do card (default, outline, flat)
 * @param {string} props.moduleColor - Cor do módulo (people, scheduler, admin, financial)
 * @param {boolean} props.hoverable - Se o card deve ter efeito de hover
 * @param {boolean} props.clickable - Se o card deve ter estilo de clicável
 * @param {Function} props.onClick - Função para lidar com cliques
 */
export const Card = ({
  children,
  className,
  variant = 'default',
  moduleColor,
  hoverable = false,
  clickable = false,
  onClick,
  ...props
}) => {
  // Classes base para todos os cards
  const baseClasses = "rounded-lg overflow-hidden";
  
  // Classes para variantes
  const variantClasses = {
    default: "bg-white dark:bg-gray-800 shadow-sm dark:shadow-gray-900/30",
    outline: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700",
    flat: "bg-gray-50 dark:bg-gray-900",
  };
  
  // Classes para módulos
  const moduleClasses = moduleColor ? {
    people: "border-l-4 border-l-module-people-border dark:border-l-module-people-border",
    scheduler: "border-l-4 border-l-module-scheduler-border dark:border-l-module-scheduler-border",
    admin: "border-l-4 border-l-module-admin-border dark:border-l-module-admin-border",
    financial: "border-l-4 border-l-module-financial-border dark:border-l-module-financial-border",
  }[moduleColor] : "";
  
  // Classes para hover
  const hoverClasses = hoverable ? "transition-all duration-200 hover:shadow-md dark:hover:shadow-gray-900/50" : "";
  
  // Classes para clicável
  const clickableClasses = clickable ? "cursor-pointer active:scale-[0.99] transition-transform" : "";
  
  return (
    <div
      className={cn(
        baseClasses,
        variantClasses[variant] || variantClasses.default,
        moduleClasses,
        hoverClasses,
        clickableClasses,
        className
      )}
      onClick={onClick}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Componente de Cabeçalho do Card
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conteúdo do cabeçalho
 * @param {string} props.className - Classes adicionais
 */
export const CardHeader = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn(
        "px-6 py-4 border-b border-gray-200 dark:border-gray-700",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Componente de Título do Card
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conteúdo do título
 * @param {string} props.className - Classes adicionais
 */
export const CardTitle = ({
  children,
  className,
  ...props
}) => {
  return (
    <h3
      className={cn(
        "text-lg font-semibold text-gray-900 dark:text-white",
        className
      )}
      {...props}
    >
      {children}
    </h3>
  );
};

/**
 * Componente de Descrição do Card
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conteúdo da descrição
 * @param {string} props.className - Classes adicionais
 */
export const CardDescription = ({
  children,
  className,
  ...props
}) => {
  return (
    <p
      className={cn(
        "text-sm text-gray-500 dark:text-gray-400 mt-1",
        className
      )}
      {...props}
    >
      {children}
    </p>
  );
};

/**
 * Componente de Conteúdo do Card
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conteúdo do card
 * @param {string} props.className - Classes adicionais
 */
export const CardContent = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn(
        "px-6 py-4",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Componente de Rodapé do Card
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conteúdo do rodapé
 * @param {string} props.className - Classes adicionais
 */
export const CardFooter = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn(
        "px-6 py-4 bg-gray-50 dark:bg-gray-800/50 border-t border-gray-200 dark:border-gray-700",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * Componente de Imagem do Card
 * 
 * @param {Object} props
 * @param {string} props.src - URL da imagem
 * @param {string} props.alt - Texto alternativo da imagem
 * @param {string} props.className - Classes adicionais
 */
export const CardImage = ({
  src,
  alt = "",
  className,
  ...props
}) => {
  return (
    <div className="w-full">
      <img
        src={src}
        alt={alt}
        className={cn(
          "w-full h-auto object-cover",
          className
        )}
        {...props}
      />
    </div>
  );
};

/**
 * Componente de Ações do Card
 * 
 * @param {Object} props
 * @param {React.ReactNode} props.children - Conteúdo das ações
 * @param {string} props.className - Classes adicionais
 */
export const CardActions = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={cn(
        "flex items-center justify-end space-x-2",
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};
