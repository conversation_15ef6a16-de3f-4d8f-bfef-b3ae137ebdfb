"use client";

import React, { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Save, Loader2, Clock, AlertCircle } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import workingHoursService from '@/app/modules/scheduler/services/workingHoursService';
import WorkingHoursDetailsPage from '@/app/modules/scheduler/workingHours/WorkingHoursDetailsPage';
import { usePermissions } from '@/hooks/usePermissions';
import { useToast } from '@/contexts/ToastContext'; // Importando o hook de toast

const DAYS_OF_WEEK = [
  //{ value: '0', label: 'Domingo' },
  { value: '1', label: 'Segunda' },
  { value: '2', label: 'Terça' },
  { value: '3', label: 'Quarta' },
  { value: '4', label: 'Quinta' },
  { value: '5', label: 'Sexta' },
  { value: '6', label: 'S<PERSON>bado' },
];

const HOURS = Array.from({ length: 24 }, (_, i) => ({
  value: i,
  label: `${String(i).padStart(2, '0')}:00`,
}));

const WorkingHoursGrid = ({
  selectedProviders = [],
  canManage = false,
  onDayChange = () => {},
  onTimeGridsChange = () => {}
}) => {
  const { user } = useAuth();
  const { can } = usePermissions();

  // Obtendo as funções de toast
  const { toast_success, toast_error, toast_warning } = useToast();

  const [selectedDay, setSelectedDay] = useState('1'); // Segunda por padrão
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [users, setUsers] = useState([]);
  const [originalData, setOriginalData] = useState([]);
  const [timeGrids, setTimeGrids] = useState({});
  const [hasChanges, setHasChanges] = useState(false);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [errorMessages, setErrorMessages] = useState([]);
  const [conflictingSchedules, setConflictingSchedules] = useState({});

  // Estado para controlar a visualização de um único usuário
  const [selectedUser, setSelectedUser] = useState(null);

  // Estados para controlar o arrasto de seleção
  const [isDragging, setIsDragging] = useState(false);
  const [dragStartValue, setDragStartValue] = useState(null);
  const [currentDragUserId, setCurrentDragUserId] = useState(null);

  // Verificando todas as permissões necessárias
  const hasViewPermission = can('scheduling.working-hours.view');
  const hasManagePermission = can('scheduling.working-hours.manage');

  // Usando a verificação de permissão mais restritiva (componente E hook)
  const canEdit = canManage && hasManagePermission;

  useEffect(() => {
    if (!selectedUser) {
      loadTimeGrid();
    }
  }, [selectedDay, selectedUser]);

  // Efeito para filtrar usuários quando o filtro de seleção muda
  useEffect(() => {
    if (users.length > 0) {
      if (selectedProviders.length === 0) {
        setFilteredUsers(users);
      } else {
        const providerIds = selectedProviders.map(p => typeof p === 'string' ? p : p.value);
        setFilteredUsers(users.filter(user =>
          providerIds.includes(user.userId)
        ));
      }
    }
  }, [selectedProviders, users]);

  const loadTimeGrid = async () => {
    setIsLoading(true);
    setErrorMessages([]);
    setConflictingSchedules({});

    try {
      const data = await workingHoursService.getUsersTimeGrid(selectedDay);

      // Inicializa os grids para cada usuário
      const grids = {};
      data.users.forEach(user => {
        grids[user.userId] = [...user.timeGrid];
      });

      setUsers(data.users);

      // Aplicar filtro inicial se existir
      if (selectedProviders.length === 0) {
        setFilteredUsers(data.users);
      } else {
        const providerIds = selectedProviders.map(p => typeof p === 'string' ? p : p.value);
        setFilteredUsers(data.users.filter(user =>
          providerIds.includes(user.userId)
        ));
      }

      setTimeGrids(grids);
      onTimeGridsChange(grids);
      setOriginalData(JSON.stringify(grids));
      setHasChanges(false);

    } catch (error) {
      console.error("Erro ao carregar grade de horários:", error);
      setErrorMessages([`Falha ao carregar horários de trabalho: ${error.message || 'Erro desconhecido'}`]);

      // Exibe toast de erro
      toast_error({
        title: "Erro ao carregar horários",
        message: error.message || "Ocorreu um erro ao carregar os horários"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDayChange = (day) => {
    if (hasChanges) {
      if (confirm('Há alterações não salvas. Deseja continuar mesmo assim?')) {
        setSelectedDay(day);
        onDayChange(day);
        toast_warning("Alterações não salvas foram descartadas");
      }
    } else {
      setSelectedDay(day);
      onDayChange(day);
    }
  };

  // Função para iniciar o arrasto
  const handleMouseDown = (userId, hour) => {
    // Permite alterações apenas se o usuário tiver permissão para gerenciar
    if (!canEdit) {
      toast_error({
        title: "Permissão negada",
        message: "Você não tem permissão para editar horários"
      });
      return;
    }

    // Limpar mensagens de erro/conflitos ao fazer alterações
    setErrorMessages([]);
    setConflictingSchedules({});

    // Iniciar o arrasto
    setIsDragging(true);
    setCurrentDragUserId(userId);

    // Armazenar o valor inicial (se a célula estava selecionada ou não)
    // Vamos inverter este valor para todas as células durante o arrasto
    setDragStartValue(!timeGrids[userId][hour]);

    // Atualizar a célula atual
    toggleCellValue(userId, hour, !timeGrids[userId][hour]);

    // Adicionar event listeners globais para capturar o movimento e o fim do arrasto
    document.addEventListener('mouseup', handleMouseUp);
  };

  // Função para lidar com o movimento do mouse durante o arrasto
  const handleMouseOver = (userId, hour) => {
    // Só processar se estiver arrastando e for o mesmo usuário
    if (isDragging && userId === currentDragUserId) {
      toggleCellValue(userId, hour, dragStartValue);
    }
  };

  // Função para finalizar o arrasto
  const handleMouseUp = () => {
    setIsDragging(false);
    setCurrentDragUserId(null);
    setDragStartValue(null);

    // Remover event listeners globais
    document.removeEventListener('mouseup', handleMouseUp);
  };

  // Função auxiliar para alternar o valor de uma célula
  const toggleCellValue = (userId, hour, newValue) => {
    const newGrids = { ...timeGrids };

    // Verificar se estamos tornando um slot indisponível (true → false)
    // Se sim, remover quaisquer marcações de conflito que possam existir
    if (newGrids[userId][hour] === true && newValue === false) {
      if (conflictingSchedules[userId]) {
        const userConflicts = { ...conflictingSchedules };
        delete userConflicts[userId];
        setConflictingSchedules(userConflicts);
      }
    }

    newGrids[userId][hour] = newValue;
    setTimeGrids(newGrids);
    onTimeGridsChange(newGrids);

    const hasNewChanges = JSON.stringify(newGrids) !== originalData;
    setHasChanges(hasNewChanges);
  };

  // Função para alternar disponibilidade ao clicar na célula (mantida para compatibilidade)
  const handleCellClick = (userId, hour) => {
    // Esta função agora é apenas um wrapper para handleMouseDown
    // para manter compatibilidade com código existente
    handleMouseDown(userId, hour);
  };

  // Função para selecionar um usuário e mostrar sua visualização individual
  const handleUserSelect = (user) => {
    if (hasChanges) {
      if (confirm('Há alterações não salvas. Deseja continuar mesmo assim?')) {
        setSelectedUser(user);
        toast_warning("Alterações não salvas foram descartadas");
      }
    } else {
      setSelectedUser(user);
    }
  };

  // Função para voltar à visualização principal
  const handleBackToGrid = () => {
    setSelectedUser(null);
  };

  const handlePreviousDay = () => {
    const currentIndex = DAYS_OF_WEEK.findIndex(day => day.value === selectedDay);
    const newIndex = (currentIndex - 1 + DAYS_OF_WEEK.length) % DAYS_OF_WEEK.length;
    handleDayChange(DAYS_OF_WEEK[newIndex].value);
  };

  const handleNextDay = () => {
    const currentIndex = DAYS_OF_WEEK.findIndex(day => day.value === selectedDay);
    const newIndex = (currentIndex + 1) % DAYS_OF_WEEK.length;
    handleDayChange(DAYS_OF_WEEK[newIndex].value);
  };

  const saveChanges = async () => {
    // Verificar permissão antes de salvar
    if (!canEdit) {
      setErrorMessages([{
        type: 'error',
        message: 'Você não tem permissão para gerenciar horários de trabalho.'
      }]);

      toast_error({
        title: "Permissão negada",
        message: "Você não tem permissão para gerenciar horários de trabalho"
      });
      return;
    }

    setIsSaving(true);
    setErrorMessages([]);
    setConflictingSchedules({});

    try {
      const usersGrid = users.map(user => ({
        userId: user.userId,
        timeGrid: timeGrids[user.userId]
      }));

      const result = await workingHoursService.updateMultipleUsersGrid(selectedDay, usersGrid);

      if (result.overallSuccess) {
        // Sucesso total - todos os horários foram atualizados
        setOriginalData(JSON.stringify(timeGrids));
        setHasChanges(false);
        setErrorMessages([{
          type: 'success',
          message: 'Horários atualizados com sucesso'
        }]);

        // Exibe toast de sucesso
        toast_success({
          title: "Horários atualizados",
          message: "Todos os horários foram atualizados com sucesso"
        });
      } else {
        // Sucesso parcial ou falha - algum ou todos os horários não foram atualizados
        const errors = [];
        const conflicts = {};

        // Processar cada erro
        result.errors.forEach(err => {
          errors.push({
            type: 'error',
            message: `${getUserName(err.userId)}: ${err.message}`
          });

          // Se houver agendamentos conflitantes, armazená-los
          if (err.schedulings && err.schedulings.length > 0) {
            conflicts[err.userId] = err.schedulings.map(s => ({
              id: s.id,
              title: s.title || 'Agendamento',
              startDate: new Date(s.startDate),
              endDate: new Date(s.endDate),
              hour: new Date(s.startDate).getHours(),
            }));
          }
        });

        setErrorMessages([
          {
            type: 'warning',
            message: 'Alguns horários não puderam ser atualizados devido a conflitos com agendamentos existentes.'
          },
          ...errors
        ]);

        setConflictingSchedules(conflicts);

        // Exibe toast de aviso
        toast_warning({
          title: "Atualização parcial",
          message: "Alguns horários não puderam ser atualizados devido a conflitos"
        });

        // Atualizar o originalData apenas para os usuários que foram atualizados com sucesso
        const successfulUserIds = result.results.map(r => r.userId);
        const updatedGrids = { ...timeGrids };

        // Atualizar apenas o estado original dos usuários que tiveram sucesso
        if (successfulUserIds.length > 0) {
          const newOriginalData = JSON.stringify(updatedGrids);
          setOriginalData(newOriginalData);
          // Verificar se ainda há alterações pendentes
          setHasChanges(JSON.stringify(updatedGrids) !== newOriginalData);
        }
      }
    } catch (error) {
      setErrorMessages([{
        type: 'error',
        message: `Erro: Falha ao salvar alterações nos horários. ${error.message || 'Erro desconhecido'}`
      }]);

      // Exibe toast de erro
      toast_error({
        title: "Erro ao salvar",
        message: error.message || "Ocorreu um erro ao salvar os horários"
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Função auxiliar para obter o nome do usuário a partir do ID
  const getUserName = (userId) => {
    const user = users.find(u => u.userId === userId);
    return user ? user.userName : userId;
  };

  const renderDaySelector = () => (
    <div className="flex items-center gap-2 mb-6">
      <button
        className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors shadow-sm dark:shadow-black/20"
        onClick={handlePreviousDay}
        disabled={isLoading || isSaving}
      >
        <ChevronLeft className="h-4 w-4 text-gray-600 dark:text-gray-300" />
      </button>

      <select
        value={selectedDay}
        onChange={(e) => handleDayChange(e.target.value)}
        disabled={isLoading || isSaving}
        className="w-full max-w-xs border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 bg-white dark:bg-gray-700 dark:text-white shadow-sm focus:border-module-scheduler-border dark:focus:border-module-scheduler-border-dark focus:ring focus:ring-module-scheduler-bg dark:focus:ring-module-scheduler-bg-dark focus:ring-opacity-50 transition-all"
        id="bordaDiaDaSemana"
      >
        {DAYS_OF_WEEK.map(day => (
          <option key={day.value} value={day.value}>
            {day.label}
          </option>
        ))}
      </select>

      <button
        className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors shadow-sm dark:shadow-black/20"
        onClick={handleNextDay}
        disabled={isLoading || isSaving}
      >
        <ChevronRight className="h-4 w-4 text-gray-600 dark:text-gray-300" />
      </button>
    </div>
  );

  const renderErrorMessages = () => {
    if (errorMessages.length === 0) return null;

    return (
      <div className="mb-4">
        {errorMessages.map((error, index) => (
          <div
            key={index}
            className={`mb-2 p-3 rounded-md ${
              error.type === 'error'
                ? 'bg-error-50 dark:bg-red-900/20 border border-error-200 dark:border-red-800/50 text-error-800 dark:text-red-300'
                : error.type === 'success'
                ? 'bg-success-50 dark:bg-green-900/20 border border-success-200 dark:border-green-800/50 text-success-800 dark:text-green-300'
                : 'bg-warning-50 dark:bg-amber-900/20 border border-warning-200 dark:border-amber-800/50 text-warning-800 dark:text-amber-300'
            }`}
          >
            <div className="flex items-start">
              {error.type === 'error' && <AlertCircle className="h-5 w-5 text-error-500 dark:text-red-400 mr-2 flex-shrink-0 mt-0.5" />}
              {error.type === 'warning' && <AlertCircle className="h-5 w-5 text-warning-500 dark:text-amber-400 mr-2 flex-shrink-0 mt-0.5" />}
              <span>{error.message}</span>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderHoursTable = () => (
    <div className="overflow-x-auto pb-4">
      <table className="min-w-full border-collapse bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
        <thead>
          <tr className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-800">
            <th className="py-3 px-4 border-b border-r border-gray-200 dark:border-gray-700 text-left font-semibold text-gray-700 dark:text-gray-200 w-48">
              Profissional
            </th>
            {HOURS.map(hour => (
              <th
                key={hour.value}
                className="py-3 px-2 text-center border-b border-r border-gray-200 dark:border-gray-700 font-semibold text-gray-700 dark:text-gray-200 w-10"
              >
                {hour.value}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {filteredUsers.length > 0 ? (
            filteredUsers.map(user => (
              <tr key={user.userId} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <td
                  className="py-3 px-4 border-b border-r border-gray-200 dark:border-gray-700 text-sm font-medium text-gray-800 dark:text-gray-200 cursor-pointer hover:bg-module-scheduler-bg dark:hover:bg-module-scheduler-bg-dark transition-colors"
                  id="bordaNomeFuncionario"
                  onClick={() => handleUserSelect(user)}
                >
                  <div className="flex items-center">
                    <span className="text-module-scheduler-icon dark:text-module-scheduler-icon-dark hover:text-module-scheduler-text dark:hover:text-module-scheduler-text-dark font-medium">{user.userName}</span>
                  </div>
                </td>
                {HOURS.map(hour => {
                  // Verificar se existe conflito para esta célula
                  const hasConflict = conflictingSchedules[user.userId]?.some(
                    s => s.hour === hour.value
                  );

                  return (
                    <td
                      key={`${user.userId}-${hour.value}`}
                      className="py-1 px-1 border-b border-r border-gray-200 dark:border-gray-700 text-center cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                      id="bordaSelecaoHorario"
                      onMouseDown={() => handleMouseDown(user.userId, hour.value)}
                      onMouseOver={() => handleMouseOver(user.userId, hour.value)}
                      title={hasConflict ? "Existe um agendamento neste horário" : "Clique e arraste para selecionar múltiplos horários"}
                    >
                      <div
                        className={`w-full h-6 rounded flex items-center justify-center transition-all duration-200 ${
                          hasConflict
                            ? 'bg-error-200 dark:bg-red-900/50 hover:bg-error-300 dark:hover:bg-red-800/50 shadow-sm dark:shadow-black/20'
                            : timeGrids[user.userId]?.[hour.value]
                              ? 'bg-module-scheduler-border dark:bg-module-scheduler-border-dark hover:bg-module-scheduler-hover dark:hover:bg-module-scheduler-hover-dark shadow-sm dark:shadow-black/20'
                              : 'bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500'
                        }`}
                      >
                        {hasConflict ? '!' : timeGrids[user.userId]?.[hour.value] ? '' : ''}
                      </div>
                    </td>
                  );
                })}
              </tr>
            ))
          ) : (
            <tr>
              <td colSpan={HOURS.length + 1} className="py-8 text-center text-gray-500 dark:text-gray-400">
                {selectedProviders.length > 0
                  ? "Nenhum profissional encontrado com os filtros selecionados"
                  : "Nenhum profissional disponível"}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );

  const renderLoading = () => (
    <div className="flex flex-col items-center justify-center py-16">
      <div className="w-16 h-16 rounded-full bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark flex items-center justify-center shadow-md dark:shadow-black/30 mb-5">
        <Loader2 className="h-8 w-8 animate-spin text-module-scheduler-icon dark:text-module-scheduler-icon-dark" />
      </div>
      <p className="text-gray-700 dark:text-gray-300 font-medium">Carregando quadro de horários...</p>
    </div>
  );

  const renderLegend = () => (
    <div className="flex justify-center mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg py-3 px-5 border border-gray-200 dark:border-gray-600 shadow-sm dark:shadow-black/20">
      <div className="flex items-center gap-8 flex-wrap">
        <div className="flex items-center gap-2">
          <div className="w-5 h-5 rounded bg-module-scheduler-border dark:bg-module-scheduler-border-dark border border-module-scheduler-border dark:border-module-scheduler-border-dark shadow-sm dark:shadow-black/20"></div>
          <span className="text-sm text-gray-700 dark:text-gray-200 font-medium">Disponível</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-5 h-5 rounded bg-gray-100 dark:bg-gray-600 border border-gray-200 dark:border-gray-500 shadow-sm dark:shadow-black/20"></div>
          <span className="text-sm text-gray-700 dark:text-gray-200 font-medium">Indisponível</span>
        </div>
        {Object.keys(conflictingSchedules).length > 0 && (
          <div className="flex items-center gap-2">
            <div className="w-5 h-5 rounded bg-error-200 dark:bg-red-900/50 border border-error-300 dark:border-red-800 flex items-center justify-center text-xs shadow-sm dark:shadow-black/20">!</div>
            <span className="text-sm text-gray-700 dark:text-gray-200 font-medium">Conflito com agendamento</span>
          </div>
        )}
      </div>
    </div>
  );

  // Se um usuário estiver selecionado, exibe o WorkingHoursDetailsPage
  if (selectedUser) {
    return (
      <WorkingHoursDetailsPage
        userId={selectedUser.userId}
        userName={selectedUser.userName}
        onBack={handleBackToGrid}
        canManage={canEdit}
      />
    );
  }

  return (
    <div className="w-full space-y-6">
      {canEdit && (
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={saveChanges}
            disabled={isLoading || isSaving || !hasChanges}
            id="bordaSalvarHorarioTrabalho"
            className={`flex items-center gap-2 px-4 py-2 rounded-lg shadow dark:shadow-black/30 transition-all duration-200 ${
              hasChanges && !isLoading && !isSaving
                ? 'bg-module-scheduler-icon dark:bg-module-scheduler-icon-dark hover:bg-purple-700 dark:hover:bg-purple-600 text-white hover:shadow-lg dark:hover:shadow-black/50 transform hover:-translate-y-0.5'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
            }`}
          >
            {isSaving ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Save className="h-4 w-4" />
            )}
            Salvar Alterações
          </button>
        </div>
      )}

      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg dark:shadow-black/30 border border-gray-200 dark:border-gray-700">
        {renderDaySelector()}

        {/* Mostrar mensagens de erro ou sucesso */}
        {renderErrorMessages()}

        <div className="bg-module-scheduler-bg dark:bg-module-scheduler-bg-dark border border-module-scheduler-border dark:border-module-scheduler-border-dark rounded-lg p-3 mb-5">
          <div className="flex items-start gap-3">
            <Clock className="h-5 w-5 text-module-scheduler-icon dark:text-module-scheduler-icon-dark mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm text-module-scheduler-text dark:text-module-scheduler-text-dark font-medium mb-1">
                Como usar esta tela:
              </p>
              <ul className="text-sm text-gray-600 dark:text-gray-300 space-y-1">
                {canEdit ? (
                  <>
                    <li>• Clique nas células para alternar disponibilidade do profissional</li>
                    <li>• Clique e arraste para selecionar ou desselecionar múltiplos horários de uma vez</li>
                    <li>• Clique no nome do profissional para ver todos os dias de uma vez</li>
                  </>
                ) : (
                  <li>• Clique no nome do profissional para ver todos os dias de uma vez</li>
                )}
              </ul>
            </div>
          </div>
        </div>

        {isLoading ? renderLoading() : renderHoursTable()}

        {renderLegend()}
      </div>
    </div>
  );
};

export default WorkingHoursGrid;