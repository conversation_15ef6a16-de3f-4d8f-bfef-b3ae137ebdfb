// src/middlewares/permissionCheck.js
/**
 * Middleware que verifica se o usuário tem uma permissão específica
 * Este middleware deve ser usado após o middleware de autenticação
 */
const checkPermission = (permission) => {
  return (req, res, next) => {
    // Verifica se o usuário está autenticado
    if (!req.user) {
      return res.status(401).json({ message: 'Autenticação necessária' });
    }
  
    // System Admin tem todas as permissões
    if (req.user.role === 'SYSTEM_ADMIN') {
      return next();
    }
  
    // Verifica se o usuário tem a permissão específica
    if (!req.user.permissions.includes(permission)) {
      return res.status(403).json({ 
        message: 'Você não tem permissão para acessar este recurso' 
      });
    }
  
    next();
  };
};

module.exports = checkPermission;
