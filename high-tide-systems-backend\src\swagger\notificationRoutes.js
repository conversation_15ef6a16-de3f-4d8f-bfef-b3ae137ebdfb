// src/swagger/notificationRoutes.js

/**
 * @swagger
 * tags:
 *   name: Notificações
 *   description: Sistema de notificações e lembretes
 */

/**
 * @swagger
 * /notifications/confirm/{token}:
 *   get:
 *     summary: Confirma um agendamento
 *     description: |
 *       Confirma um agendamento através de um token enviado por email.
 *       Esta é uma rota pública, acessível por links em emails.
 *     tags: [Notificações]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Token de confirmação enviado por email
 *     responses:
 *       302:
 *         description: Redirecionamento para página de confirmação
 *         headers:
 *           Location:
 *             schema:
 *               type: string
 *             description: URL de redirecionamento
 *       400:
 *         description: Token inválido
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Token inválido"
 *       404:
 *         description: Agendamento não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Agendamento não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /notifications/cancel/{token}:
 *   get:
 *     summary: Cancela um agendamento
 *     description: |
 *       Cancela um agendamento através de um token enviado por email.
 *       Esta é uma rota pública, acessível por links em emails.
 *     tags: [Notificações]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Token de cancelamento enviado por email
 *     responses:
 *       302:
 *         description: Redirecionamento para página de cancelamento
 *         headers:
 *           Location:
 *             schema:
 *               type: string
 *             description: URL de redirecionamento
 *       400:
 *         description: Token inválido
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Token inválido"
 *       404:
 *         description: Agendamento não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Agendamento não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /notifications/test-email:
 *   post:
 *     summary: Testa o envio de email
 *     description: |
 *       Envia um email de teste para verificar a configuração de email.
 *       Útil para desenvolvimento e testes.
 *     tags: [Notificações]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email para envio do teste
 *               type:
 *                 type: string
 *                 enum: [reminder, appointment]
 *                 description: Tipo de email a ser enviado
 *                 default: appointment
 *     responses:
 *       200:
 *         description: Email enviado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 messageId:
 *                   type: string
 *                   example: "<************.5678@localhost>"
 *       400:
 *         description: Parâmetros inválidos
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Email é obrigatório"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */

/**
 * @swagger
 * /notifications/send-reminder/{id}:
 *   post:
 *     summary: Envia um lembrete manual
 *     description: |
 *       Envia um lembrete por email para um agendamento específico.
 *       Útil para enviar lembretes manualmente fora da programação automática.
 *     tags: [Notificações]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: ID do agendamento
 *     responses:
 *       200:
 *         description: Lembrete enviado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 messageId:
 *                   type: string
 *                   example: "<************.5678@localhost>"
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: Agendamento não encontrado
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *             example:
 *               message: "Agendamento não encontrado"
 *       500:
 *         $ref: '#/components/responses/ServerError'
 */