"use client";

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { getModuleRedirectionPath } from '@/utils/moduleRedirection';

export default function SchedulerModuleRoute() {
  const router = useRouter();

  useEffect(() => {
    // Obtém o caminho de redirecionamento com base nas preferências do usuário
    const redirectPath = getModuleRedirectionPath(
      'scheduler',
      '/dashboard/scheduler/introduction',
      '/dashboard/scheduler/calendar'
    );

    // Redireciona para o caminho determinado
    router.push(redirectPath);
  }, [router]);

  return <div>Carregando...</div>;
}