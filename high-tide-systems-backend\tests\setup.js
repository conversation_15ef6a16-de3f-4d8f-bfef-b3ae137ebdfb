// Carrega variáveis de ambiente do arquivo .env
require('dotenv').config();

// Configuração global para aumentar o timeout dos testes
jest.setTimeout(10000);

// Silenciar logs durante os testes (opcional)
// console.log = jest.fn();
// console.info = jest.fn();
// console.warn = jest.fn();
// console.error = jest.fn();

// Limpar mocks após cada teste
afterEach(() => {
  jest.clearAllMocks();
});

// Configuração global para o Prisma (se necessário)
// const { PrismaClient } = require('@prisma/client');
// const prisma = new PrismaClient();
// 
// // Limpar o banco de dados após todos os testes
// afterAll(async () => {
//   await prisma.$disconnect();
// });

// Configuração global para o Redis (se necessário)
// const cacheService = require('../src/services/cacheService');
// 
// // Inicializar o serviço de cache antes de todos os testes
// beforeAll(async () => {
//   await cacheService.initialize();
// });
// 
// // Fechar a conexão com o Redis após todos os testes
// afterAll(async () => {
//   await cacheService.close();
// });
