// src/app/modules/scheduler/index.js
import AppointmentCalendar from './calendar/AppointmentCalendar';
import WorkingHoursPage from './workingHours/WorkingHoursPage';
import { appointmentService } from './services/appointmentService';
import workingHoursService from './services/workingHoursService';
import ServiceTypePage from './ServiceTypePage/ServiceTypePage';
import serviceTypeService from './services/serviceTypeService';
import LocationsPage from './LocationsPage/LocationsPage';
import locationServiceService from './services/locationService';
import { IntroductionPage } from './introduction';
import { AppointmentsPage } from './AppointmentsPage';
import { AppointmentsReport } from './appointmentsReport';

// Export the components and services
export {
  AppointmentCalendar,
  WorkingHoursPage,
  appointmentService,
  workingHoursService,
  ServiceTypePage,
  serviceTypeService,
  LocationsPage,
  locationServiceService,
  IntroductionPage,
  AppointmentsPage,
  AppointmentsReport
};