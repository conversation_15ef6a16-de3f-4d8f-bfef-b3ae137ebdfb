// src/app/modules/aba/services/skillsService.js
import { api } from "@/utils/api";

export const skillsService = {
  // Get skills with optional filters
  getSkills: async (filters = {}) => {
    const { page = 1, limit = 10, search = "", active } = filters;

    try {
      console.log("skillsService.getSkills - Enviando requisição com parâmetros:", {
        page,
        limit,
        search: search || undefined,
        active: active === undefined ? undefined : active
      });

      const response = await api.get("/aba/skills", {
        params: {
          page,
          limit,
          search: search || undefined,
          active: active === undefined ? undefined : active
        }
      });

      console.log("skillsService.getSkills - Resposta da API:", response.data);

      // Verificar a estrutura da resposta
      const items = response.data.skills || response.data.items || [];
      const total = response.data.total || 0;

      console.log("skillsService.getSkills - Itens extraídos:", items);

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching skills:", error);
      throw error;
    }
  },

  // Get a single skill by ID
  getSkill: async (id) => {
    try {
      const response = await api.get(`/aba/skills/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching skill ${id}:`, error);
      throw error;
    }
  },

  // Create a new skill
  createSkill: async (skillData) => {
    try {
      const response = await api.post("/aba/skills", skillData);
      return response.data;
    } catch (error) {
      console.error("Error creating skill:", error);
      throw error;
    }
  },

  // Update an existing skill
  updateSkill: async (id, skillData) => {
    try {
      const response = await api.put(`/aba/skills/${id}`, skillData);
      return response.data;
    } catch (error) {
      console.error(`Error updating skill ${id}:`, error);
      throw error;
    }
  },

  // Toggle skill active status
  toggleSkillStatus: async (id) => {
    try {
      const response = await api.patch(`/aba/skills/${id}/status`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling status for skill ${id}:`, error);
      throw error;
    }
  },

  // Delete a skill
  deleteSkill: async (id) => {
    try {
      const response = await api.delete(`/aba/skills/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting skill ${id}:`, error);
      throw error;
    }
  }
};

export default skillsService;
