# Redis Cache Implementation

This document describes the Redis cache implementation in the High Tide Systems backend.

## Overview

Redis is used as a caching layer to improve performance and reduce database load. The implementation includes:

1. A centralized cache service
2. Middleware for caching API responses
3. Token blacklist for JWT authentication
4. Specific implementations for high-traffic endpoints

## Cache Service

The cache service (`src/services/cacheService.js`) provides a unified interface for interacting with Redis:

- Connection management
- Key generation
- CRUD operations for cached data
- TTL (Time-To-Live) management
- Pattern-based cache invalidation

## Cache Middleware

Two middleware functions are provided in `src/middlewares/cache.js`:

1. `cacheMiddleware`: Caches GET responses for specified routes
2. `clearCacheMiddleware`: Invalidates cache when data is modified (POST, PUT, DELETE)

## Implementation Areas

### Authentication

- JWT tokens are blacklisted in Redis when a user logs out
- Token validation checks the Redis blacklist
- Token TTL in Redis matches the token's expiration time

### Dashboard Data

- Admin dashboard data is cached with a 5-minute TTL
- Each dashboard component has its own cache key
- Cache is user and company-specific

### Reference Data

- Service types are cached with a 10-minute TTL
- Cache is invalidated when service types are created, updated, or deleted

### External API Calls

- CEP (postal code) lookups are cached for 7 days
- Reduces external API calls and improves response time

## Cache Keys

Cache keys follow a consistent pattern:

- `token:blacklist:{token}` - Blacklisted JWT tokens
- `dashboard:{component}:{userId}:{companyId}` - Dashboard data
- `service-types:list:{companyId}` - Service type listings
- `service-types:detail:{id}` - Individual service type details
- `cep:{cepNumber}` - CEP lookup results

## TTL Values

Different types of data have different TTL values:

- JWT Tokens: Matches token expiration time
- Dashboard Data: 5 minutes (300 seconds)
- Service Types: 10 minutes (600 seconds)
- CEP Data: 7 days (604,800 seconds)

## Configuration

Redis connection is configured via environment variables:

- `REDIS_URL`: Redis connection string (default: `redis://localhost:6379`)

## Future Improvements

- Implement cache compression for large responses
- Add cache analytics and monitoring
- Implement cache warming for frequently accessed data
- Add circuit breaker pattern for Redis connection failures
