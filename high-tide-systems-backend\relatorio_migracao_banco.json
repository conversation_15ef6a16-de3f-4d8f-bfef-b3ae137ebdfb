{"timestamp": "2025-05-26T16:40:25.801Z", "migration": {"companyId": "9f12a652-52ff-49ae-a930-fdc50a1decbf", "branchId": "d9a87d47-8a3c-459e-a812-33d12b692520", "defaultPassword": "TrocarSenha123!"}, "statistics": {"company": {"id": "9f12a652-52ff-49ae-a930-fdc50a1decbf", "name": "ABA+ Migrada", "tradingName": null, "legalName": null, "industry": null, "contactEmail": "<EMAIL>", "cnpj": "00.000.000/0001-00", "phone": "(11) 99999-9999", "privacyPolicyUrl": null, "termsOfServiceUrl": null, "phone2": null, "address": null, "city": null, "state": null, "postalCode": null, "website": null, "primaryColor": null, "secondaryColor": null, "description": null, "socialMedia": null, "businessHours": null, "active": true, "createdAt": "2025-05-26T16:34:18.809Z", "updatedAt": "2025-05-26T16:34:18.809Z", "defaultCurrency": "BRL", "deletedAt": null, "licenseValidUntil": null, "plan": null, "timeZone": "America/Sao_Paulo"}, "branch": {"id": "d9a87d47-8a3c-459e-a812-33d12b692520", "name": "Unidade Principal", "code": null, "description": null, "address": "Endereço a ser definido", "city": "Cidade a ser definida", "state": "Estado a ser definido", "postalCode": null, "phone": null, "email": null, "active": true, "isHeadquarters": true, "deletedAt": null, "createdAt": "2025-05-26T16:34:18.816Z", "updatedAt": "2025-05-26T16:34:18.816Z", "companyId": "9f12a652-52ff-49ae-a930-fdc50a1decbf", "neighborhood": null, "defaultWorkingHours": null}, "totalProfessions": 52, "totalUsers": 79, "activeUsers": 79, "inactiveUsers": 0, "admins": 35, "employees": 44, "usersWithProfession": 79, "usersWithoutProfession": 0}, "warnings": ["TODOS OS USUÁRIOS FORAM CRIADOS COM A SENHA PADRÃO: TrocarSenha123!", "As senhas DEVEM ser alteradas antes do uso em produção", "Usuários sem profissão mapeada precisam ser revisados", "Dados da empresa e filial são fictícios e precisam ser atualizados"], "nextSteps": ["Alterar senhas de todos os usuários", "Atualizar dados reais da empresa (CNPJ, endereço, contato)", "Atual<PERSON>r dados reais da filial", "Revisar usuários sem profissão", "Configurar módulos e permissões específicas", "Testar login de alguns usuários", "Configurar grupos de profissão se necessário"]}