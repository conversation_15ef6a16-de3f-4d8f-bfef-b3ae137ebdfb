'use client';

import React from 'react';
import { Activity } from 'lucide-react';
import ActionButton from './ActionButton';

export const QuickActionsCard = ({ actions, onActionClick }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-md dark:shadow-lg dark:shadow-black/20">
      <div className="p-6">
        <h3 className="text-xl font-bold text-gray-800 dark:text-gray-100 mb-6 flex items-center">
          <Activity className="mr-3 text-primary-500 dark:text-primary-400" size={22} aria-hidden="true" />
          Ações Rápidas
        </h3>
        
        <div className="grid grid-cols-1 gap-4">
          {actions.map((action, index) => (
            <ActionButton 
              key={index}
              action={action}
              index={index}
              onClick={onActionClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default QuickActionsCard;