const { authenticate: authMiddleware } = require('../../src/middlewares/auth');
const jwt = require('jsonwebtoken');

// Mock do módulo prisma
jest.mock('../../src/utils/prisma', () => ({
  user: {
    findUnique: jest.fn()
  }
}));

// Mock do módulo cacheService
jest.mock('../../src/services/cacheService', () => ({
  initialize: jest.fn().mockResolvedValue(true),
  get: jest.fn(),
  set: jest.fn(),
  delete: jest.fn(),
  clear: jest.fn(),
  close: jest.fn()
}));

// Importar o prisma mockado
const prisma = require('../../src/utils/prisma');

describe('Middleware de Autenticação', () => {
  // Configuração antes de cada teste
  let req, res, next;

  beforeEach(() => {
    // Resetar mocks
    jest.clearAllMocks();

    // Mock de req, res e next
    req = {
      headers: {},
      userId: null
    };

    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };

    next = jest.fn();
  });

  test('Deve rejeitar requisição sem token', async () => {
    // Executar o middleware
    await authMiddleware(req, res, next);

    // Verificar se a resposta foi correta
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ message: 'Token de autenticação não fornecido' });
    expect(next).not.toHaveBeenCalled();
  });

  test('Deve rejeitar token inválido', async () => {
    // Configurar requisição com token inválido
    req.headers.authorization = 'Bearer invalid_token';

    // Executar o middleware
    await authMiddleware(req, res, next);

    // Verificar se a resposta foi correta
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ message: 'Invalid token' });
    expect(next).not.toHaveBeenCalled();
  });

  test('Deve aceitar token de teste em ambiente de desenvolvimento', async () => {
    // Salvar o NODE_ENV original
    const originalNodeEnv = process.env.NODE_ENV;

    // Configurar ambiente de desenvolvimento
    process.env.NODE_ENV = 'development';

    // Configurar requisição com token de teste
    const testUserId = '00000000-0000-0000-0000-000000000001';
    req.headers.authorization = `Bearer TEST_TOKEN_${testUserId}`;

    // Mock do usuário retornado pelo prisma
    prisma.user.findUnique.mockResolvedValue({
      id: testUserId,
      email: '<EMAIL>',
      active: true
    });

    // Executar o middleware
    await authMiddleware(req, res, next);

    // Verificar se o middleware passou para o próximo handler
    expect(next).toHaveBeenCalled();

    // Restaurar o NODE_ENV original
    process.env.NODE_ENV = originalNodeEnv;
  });

  test('Deve aceitar token JWT válido', async () => {
    // Configurar um token JWT válido
    const userId = '********-1234-1234-1234-************';
    const token = jwt.sign({ userId }, process.env.JWT_SECRET || 'test_secret', { expiresIn: '1h' });
    req.headers.authorization = `Bearer ${token}`;

    // Mock do usuário retornado pelo prisma
    prisma.user.findUnique.mockResolvedValue({
      id: userId,
      email: '<EMAIL>',
      active: true
    });

    // Executar o middleware
    await authMiddleware(req, res, next);

    // Verificar se o middleware passou para o próximo handler
    expect(next).toHaveBeenCalled();
  });

  test('Deve rejeitar usuário inativo', async () => {
    // Configurar um token JWT válido
    const userId = '********-1234-1234-1234-************';
    const token = jwt.sign({ userId }, process.env.JWT_SECRET || 'test_secret', { expiresIn: '1h' });
    req.headers.authorization = `Bearer ${token}`;

    // Mock do usuário inativo retornado pelo prisma
    prisma.user.findUnique.mockResolvedValue({
      id: userId,
      email: '<EMAIL>',
      active: false
    });

    // Executar o middleware
    await authMiddleware(req, res, next);

    // Verificar se a resposta foi correta
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ message: 'User account is deactivated' });
    expect(next).not.toHaveBeenCalled();
  });

  test('Deve rejeitar usuário não encontrado', async () => {
    // Configurar um token JWT válido
    const userId = '********-1234-1234-1234-************';
    const token = jwt.sign({ userId }, process.env.JWT_SECRET || 'test_secret', { expiresIn: '1h' });
    req.headers.authorization = `Bearer ${token}`;

    // Mock do prisma retornando null (usuário não encontrado)
    prisma.user.findUnique.mockResolvedValue(null);

    // Executar o middleware
    await authMiddleware(req, res, next);

    // Verificar se a resposta foi correta
    expect(res.status).toHaveBeenCalledWith(401);
    expect(res.json).toHaveBeenCalledWith({ message: 'User not found' });
    expect(next).not.toHaveBeenCalled();
  });
});
