// src/app/modules/admin/services/professionsService.js
import { api } from "@/utils/api";
import { exportService } from "@/app/services/exportService";
import { format as dateFormat } from "date-fns";
import { ptBR } from "date-fns/locale";

/**
 * Serviço para gerenciar operações relacionadas a profissões
 */
export const professionsService = {
  /**
   * Obtém a lista de profissões com filtros
   * @param {Object} params - Parâmetros de filtro
   * @param {string} params.search - Termo de busca
   * @param {string} params.groupId - Filtro por grupo
   * @param {boolean} params.active - Filtro por status (ativo/inativo)
   * @param {string} params.companyId - Filtro por empresa
   * @param {Array} params.professionIds - Filtro por IDs específicos de profissões
   * @returns {Promise<Array>} Lista de profissões
   */
  async getProfessions(params = {}) {
    try {
      // Extrair professionIds do objeto params para tratamento especial
      const { professionIds, ...otherParams } = params;

      // Criar objeto de parâmetros base
      let queryParams = { ...otherParams };

      // Adicionar professionIds como parâmetros separados com notação de array
      if (professionIds && professionIds.length > 0) {
        // Garantir que professionIds seja um array
        const professionIdsArray = Array.isArray(professionIds) ? professionIds : [professionIds];

        // Adicionar cada ID como um parâmetro separado
        professionIdsArray.forEach((id, index) => {
          // Usar a notação de array para compatibilidade com a API
          queryParams[`professionIds[${index}]`] = id;
        });

        console.log("Filtrando por múltiplos IDs de profissões:", professionIdsArray);
      }

      // Log para debug
      console.log("Parâmetros de filtro para profissões:", queryParams);

      const response = await api.get("/professions", { params: queryParams });
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar profissões:", error);
      throw error;
    }
  },

  /**
   * Obtém uma profissão pelo ID
   * @param {string} professionId - ID da profissão
   * @returns {Promise<Object>} Dados da profissão
   */
  async getProfessionById(professionId) {
    try {
      const response = await api.get(`/professions/${professionId}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar profissão ${professionId}:`, error);
      throw error;
    }
  },

  /**
   * Cria uma nova profissão
   * @param {Object} professionData - Dados da profissão
   * @returns {Promise<Object>} Profissão criada
   */
  async createProfession(professionData) {
    try {
      const response = await api.post("/professions", professionData);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar profissão:", error);
      throw error;
    }
  },

  /**
   * Atualiza uma profissão existente
   * @param {string} professionId - ID da profissão
   * @param {Object} professionData - Dados atualizados da profissão
   * @returns {Promise<Object>} Profissão atualizada
   */
  async updateProfession(professionId, professionData) {
    try {
      const response = await api.put(`/professions/${professionId}`, professionData);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar profissão ${professionId}:`, error);
      throw error;
    }
  },

  /**
   * Exclui uma profissão
   * @param {string} professionId - ID da profissão
   * @returns {Promise<void>}
   */
  async deleteProfession(professionId) {
    try {
      await api.delete(`/professions/${professionId}`);
    } catch (error) {
      console.error(`Erro ao excluir profissão ${professionId}:`, error);
      throw error;
    }
  },

  /**
   * Obtém a lista de grupos de profissões
   * @param {Object} params - Parâmetros de filtro
   * @param {string} params.search - Termo de busca
   * @param {boolean} params.active - Filtro por status (ativo/inativo)
   * @param {string} params.companyId - Filtro por empresa
   * @param {Array} params.groupIds - Filtro por IDs específicos de grupos
   * @returns {Promise<Array>} Lista de grupos de profissões
   */
  async getProfessionGroups(params = {}) {
    try {
      // Extrair groupIds do objeto params para tratamento especial
      const { groupIds, ...otherParams } = params;

      // Criar objeto de parâmetros base
      let queryParams = { ...otherParams };

      // Adicionar groupIds como parâmetros separados com notação de array
      if (groupIds && groupIds.length > 0) {
        // Garantir que groupIds seja um array
        const groupIdsArray = Array.isArray(groupIds) ? groupIds : [groupIds];

        // Adicionar cada ID como um parâmetro separado
        groupIdsArray.forEach((id, index) => {
          // Usar a notação de array para compatibilidade com a API
          queryParams[`groupIds[${index}]`] = id;
        });

        console.log("Filtrando por múltiplos IDs de grupos:", groupIdsArray);
      }

      // Log para debug
      console.log("Parâmetros de filtro para grupos:", queryParams);

      const response = await api.get("/professions/groups", { params: queryParams });
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar grupos de profissões:", error);
      throw error;
    }
  },

  /**
   * Obtém um grupo de profissões pelo ID
   * @param {string} groupId - ID do grupo
   * @returns {Promise<Object>} Dados do grupo
   */
  async getProfessionGroupById(groupId) {
    try {
      const response = await api.get(`/professions/groups/${groupId}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar grupo de profissões ${groupId}:`, error);
      throw error;
    }
  },

  /**
   * Cria um novo grupo de profissões
   * @param {Object} groupData - Dados do grupo
   * @returns {Promise<Object>} Grupo criado
   */
  async createProfessionGroup(groupData) {
    try {
      const response = await api.post("/professions/groups", groupData);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar grupo de profissões:", error);
      throw error;
    }
  },

  /**
   * Atualiza um grupo de profissões existente
   * @param {string} groupId - ID do grupo
   * @param {Object} groupData - Dados atualizados do grupo
   * @returns {Promise<Object>} Grupo atualizado
   */
  async updateProfessionGroup(groupId, groupData) {
    try {
      const response = await api.put(`/professions/groups/${groupId}`, groupData);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar grupo de profissões ${groupId}:`, error);
      throw error;
    }
  },

  /**
   * Exclui um grupo de profissões
   * @param {string} groupId - ID do grupo
   * @returns {Promise<void>}
   */
  async deleteProfessionGroup(groupId) {
    try {
      await api.delete(`/professions/groups/${groupId}`);
    } catch (error) {
      console.error(`Erro ao excluir grupo de profissões ${groupId}:`, error);
      throw error;
    }
  },

  /**
   * Obtém a lista de usuários de uma profissão específica
   * @param {string} professionId - ID da profissão
   * @param {Object} params - Parâmetros de filtro
   * @param {boolean} params.active - Filtro por status (ativo/inativo)
   * @returns {Promise<Object>} Dados da profissão e lista de usuários
   */
  async getProfessionUsers(professionId, params = {}) {
    try {
      const response = await api.get(`/professions/${professionId}/users`, { params });
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar usuários da profissão ${professionId}:`, error);
      throw error;
    }
  },

  /**
   * Exporta a lista de profissões com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, status, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  async exportProfessions(filters, exportFormat = "xlsx") {
    try {
      // Obter os dados filtrados
      const data = await professionsService.getProfessions({
        ...filters,
      });

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "name", header: "Nome" },
        { key: "description", header: "Descrição" },
        { key: "groupName", header: "Grupo" },
        { key: "companyName", header: "Empresa" },
        {
          key: "active",
          header: "Status",
          format: (value) => value ? "Ativo" : "Inativo",
          align: "center",
          width: 20
        },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(profession => {
        return {
          name: profession.name || "",
          description: profession.description || "",
          groupName: profession.group?.name || "",
          companyName: profession.company?.name || "",
          active: profession.active,
          createdAt: profession.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.professionIds && filters.professionIds.length > 0) {
        subtitleParts.push(`Profissões específicas: ${filters.professionIds.length} selecionadas`);
      }
      if (filters.groupId) {
        subtitleParts.push(`Grupo: ${filters.groupName || filters.groupId}`);
      }
      if (filters.active !== undefined) {
        subtitleParts.push(`Status: ${filters.active ? "Ativas" : "Inativas"}`);
      }
      if (filters.companyId) {
        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "profissoes",
        columns,
        title: "Lista de Profissões",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar profissões:", error);
      return false;
    }
  },

  /**
   * Exporta a lista de grupos de profissões com os filtros aplicados
   * @param {Object} filters - Filtros atuais (busca, status, etc)
   * @param {string} exportFormat - Formato da exportação ('xlsx' ou 'pdf')
   * @returns {Promise<boolean>} - Indica se a exportação foi bem-sucedida
   */
  async exportProfessionGroups(filters, exportFormat = "xlsx") {
    try {
      // Obter os dados filtrados
      const data = await professionsService.getProfessionGroups({
        ...filters,
      });

      // Timestamp atual para o subtítulo
      const timestamp = dateFormat(new Date(), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });

      // Definição das colunas com formatação
      const columns = [
        { key: "name", header: "Nome" },
        { key: "description", header: "Descrição" },
        { key: "companyName", header: "Empresa" },
        { key: "professionsCount", header: "Nº de Profissões" },
        {
          key: "active",
          header: "Status",
          format: (value) => value ? "Ativo" : "Inativo",
          align: "center",
          width: 20
        },
        { key: "createdAt", header: "Data de Cadastro", type: "date" },
      ];

      // Preparar os dados para exportação
      const preparedData = data.map(group => {
        return {
          name: group.name || "",
          description: group.description || "",
          companyName: group.company?.name || "",
          professionsCount: group.professions?.length || 0,
          active: group.active,
          createdAt: group.createdAt || "",
        };
      });

      // Filtros aplicados para subtítulo
      let subtitleParts = [];
      if (filters.search) subtitleParts.push(`Busca: "${filters.search}"`);
      if (filters.groupIds && filters.groupIds.length > 0) {
        subtitleParts.push(`Grupos específicos: ${filters.groupIds.length} selecionados`);
      }
      if (filters.active !== undefined) {
        subtitleParts.push(`Status: ${filters.active ? "Ativos" : "Inativos"}`);
      }
      if (filters.companyId) {
        subtitleParts.push(`Empresa: ${filters.companyName || filters.companyId}`);
      }

      // Construir o subtítulo
      let subtitle = `Exportado em: ${timestamp}`;
      if (subtitleParts.length > 0) {
        subtitle += ` | Filtros: ${subtitleParts.join(", ")}`;
      }

      // Exportar os dados
      return await exportService.exportData(preparedData, {
        format: exportFormat,
        filename: "grupos-profissoes",
        columns,
        title: "Lista de Grupos de Profissões",
        subtitle
      });
    } catch (error) {
      console.error("Erro ao exportar grupos de profissões:", error);
      return false;
    }
  }
};
