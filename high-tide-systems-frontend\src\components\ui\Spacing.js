/**
 * Componentes de Espaçamento
 * 
 * Este arquivo contém componentes para espaçamento consistente em todo o sistema.
 * Todos os componentes seguem o sistema de design definido.
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * Componente para adicionar espaçamento vertical
 * 
 * @param {Object} props
 * @param {string|number} props.size - Tamanho do espaçamento (1-24)
 * @param {string} props.className - Classes adicionais
 */
export const VSpace = ({ size = 4, className, ...props }) => {
  // Mapear tamanhos para classes do Tailwind
  const sizeClasses = {
    0.5: 'h-0.5',
    1: 'h-1',
    2: 'h-2',
    3: 'h-3',
    4: 'h-4',
    5: 'h-5',
    6: 'h-6',
    8: 'h-8',
    10: 'h-10',
    12: 'h-12',
    16: 'h-16',
    20: 'h-20',
    24: 'h-24',
  };

  const sizeClass = sizeClasses[size] || 'h-4';

  return <div className={cn(sizeClass, className)} {...props} />;
};

/**
 * Componente para adicionar espaçamento horizontal
 * 
 * @param {Object} props
 * @param {string|number} props.size - Tamanho do espaçamento (1-24)
 * @param {string} props.className - Classes adicionais
 */
export const HSpace = ({ size = 4, className, ...props }) => {
  // Mapear tamanhos para classes do Tailwind
  const sizeClasses = {
    0.5: 'w-0.5',
    1: 'w-1',
    2: 'w-2',
    3: 'w-3',
    4: 'w-4',
    5: 'w-5',
    6: 'w-6',
    8: 'w-8',
    10: 'w-10',
    12: 'w-12',
    16: 'w-16',
    20: 'w-20',
    24: 'w-24',
  };

  const sizeClass = sizeClasses[size] || 'w-4';

  return <div className={cn(sizeClass, 'inline-block', className)} {...props} />;
};

/**
 * Componente para adicionar padding consistente
 * 
 * @param {Object} props
 * @param {string|number} props.p - Padding em todos os lados
 * @param {string|number} props.px - Padding horizontal
 * @param {string|number} props.py - Padding vertical
 * @param {string|number} props.pt - Padding top
 * @param {string|number} props.pr - Padding right
 * @param {string|number} props.pb - Padding bottom
 * @param {string|number} props.pl - Padding left
 * @param {string} props.className - Classes adicionais
 * @param {React.ReactNode} props.children - Conteúdo
 */
export const Padding = ({ 
  p, px, py, pt, pr, pb, pl, 
  className, 
  children,
  ...props 
}) => {
  // Mapear valores para classes do Tailwind
  const getPaddingClass = (value, prefix) => {
    if (value === undefined) return '';
    
    const paddingMap = {
      0: `${prefix}-0`,
      0.5: `${prefix}-0.5`,
      1: `${prefix}-1`,
      2: `${prefix}-2`,
      3: `${prefix}-3`,
      4: `${prefix}-4`,
      5: `${prefix}-5`,
      6: `${prefix}-6`,
      8: `${prefix}-8`,
      10: `${prefix}-10`,
      12: `${prefix}-12`,
      16: `${prefix}-16`,
      20: `${prefix}-20`,
      24: `${prefix}-24`,
    };
    
    return paddingMap[value] || '';
  };
  
  const paddingClasses = [
    getPaddingClass(p, 'p'),
    getPaddingClass(px, 'px'),
    getPaddingClass(py, 'py'),
    getPaddingClass(pt, 'pt'),
    getPaddingClass(pr, 'pr'),
    getPaddingClass(pb, 'pb'),
    getPaddingClass(pl, 'pl'),
  ].filter(Boolean).join(' ');
  
  return (
    <div className={cn(paddingClasses, className)} {...props}>
      {children}
    </div>
  );
};

/**
 * Componente para adicionar margin consistente
 * 
 * @param {Object} props
 * @param {string|number} props.m - Margin em todos os lados
 * @param {string|number} props.mx - Margin horizontal
 * @param {string|number} props.my - Margin vertical
 * @param {string|number} props.mt - Margin top
 * @param {string|number} props.mr - Margin right
 * @param {string|number} props.mb - Margin bottom
 * @param {string|number} props.ml - Margin left
 * @param {string} props.className - Classes adicionais
 * @param {React.ReactNode} props.children - Conteúdo
 */
export const Margin = ({ 
  m, mx, my, mt, mr, mb, ml, 
  className, 
  children,
  ...props 
}) => {
  // Mapear valores para classes do Tailwind
  const getMarginClass = (value, prefix) => {
    if (value === undefined) return '';
    
    const marginMap = {
      0: `${prefix}-0`,
      0.5: `${prefix}-0.5`,
      1: `${prefix}-1`,
      2: `${prefix}-2`,
      3: `${prefix}-3`,
      4: `${prefix}-4`,
      5: `${prefix}-5`,
      6: `${prefix}-6`,
      8: `${prefix}-8`,
      10: `${prefix}-10`,
      12: `${prefix}-12`,
      16: `${prefix}-16`,
      20: `${prefix}-20`,
      24: `${prefix}-24`,
    };
    
    return marginMap[value] || '';
  };
  
  const marginClasses = [
    getMarginClass(m, 'm'),
    getMarginClass(mx, 'mx'),
    getMarginClass(my, 'my'),
    getMarginClass(mt, 'mt'),
    getMarginClass(mr, 'mr'),
    getMarginClass(mb, 'mb'),
    getMarginClass(ml, 'ml'),
  ].filter(Boolean).join(' ');
  
  return (
    <div className={cn(marginClasses, className)} {...props}>
      {children}
    </div>
  );
};

/**
 * Componente para criar um container com largura máxima
 * 
 * @param {Object} props
 * @param {string} props.size - Tamanho do container (sm, md, lg, xl, 2xl, full)
 * @param {string} props.className - Classes adicionais
 * @param {React.ReactNode} props.children - Conteúdo
 */
export const Container = ({ 
  size = 'lg', 
  className, 
  children,
  ...props 
}) => {
  // Mapear tamanhos para classes do Tailwind
  const sizeClasses = {
    sm: 'max-w-screen-sm',
    md: 'max-w-screen-md',
    lg: 'max-w-screen-lg',
    xl: 'max-w-screen-xl',
    '2xl': 'max-w-screen-2xl',
    full: 'max-w-full',
  };
  
  const sizeClass = sizeClasses[size] || 'max-w-screen-lg';
  
  return (
    <div className={cn('w-full mx-auto px-4', sizeClass, className)} {...props}>
      {children}
    </div>
  );
};

/**
 * Componente para criar um grid responsivo
 * 
 * @param {Object} props
 * @param {number} props.cols - Número de colunas (1-12)
 * @param {number} props.colsSm - Número de colunas em telas pequenas
 * @param {number} props.colsMd - Número de colunas em telas médias
 * @param {number} props.colsLg - Número de colunas em telas grandes
 * @param {number} props.colsXl - Número de colunas em telas extra grandes
 * @param {number} props.gap - Tamanho do gap (1-12)
 * @param {string} props.className - Classes adicionais
 * @param {React.ReactNode} props.children - Conteúdo
 */
export const Grid = ({ 
  cols = 1, 
  colsSm, 
  colsMd, 
  colsLg, 
  colsXl, 
  gap = 4,
  className, 
  children,
  ...props 
}) => {
  // Mapear colunas para classes do Tailwind
  const getColsClass = (value, prefix) => {
    if (value === undefined) return '';
    
    const colsMap = {
      1: `${prefix}-1`,
      2: `${prefix}-2`,
      3: `${prefix}-3`,
      4: `${prefix}-4`,
      5: `${prefix}-5`,
      6: `${prefix}-6`,
      7: `${prefix}-7`,
      8: `${prefix}-8`,
      9: `${prefix}-9`,
      10: `${prefix}-10`,
      11: `${prefix}-11`,
      12: `${prefix}-12`,
    };
    
    return colsMap[value] || '';
  };
  
  // Mapear gap para classes do Tailwind
  const getGapClass = (value) => {
    if (value === undefined) return '';
    
    const gapMap = {
      0: 'gap-0',
      0.5: 'gap-0.5',
      1: 'gap-1',
      2: 'gap-2',
      3: 'gap-3',
      4: 'gap-4',
      5: 'gap-5',
      6: 'gap-6',
      8: 'gap-8',
      10: 'gap-10',
      12: 'gap-12',
      16: 'gap-16',
    };
    
    return gapMap[value] || '';
  };
  
  const colsClasses = [
    getColsClass(cols, 'grid-cols'),
    getColsClass(colsSm, 'sm:grid-cols'),
    getColsClass(colsMd, 'md:grid-cols'),
    getColsClass(colsLg, 'lg:grid-cols'),
    getColsClass(colsXl, 'xl:grid-cols'),
  ].filter(Boolean).join(' ');
  
  const gapClass = getGapClass(gap);
  
  return (
    <div className={cn('grid', colsClasses, gapClass, className)} {...props}>
      {children}
    </div>
  );
};
