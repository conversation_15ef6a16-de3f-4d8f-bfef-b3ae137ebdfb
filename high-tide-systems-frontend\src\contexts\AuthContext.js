'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { api } from '@/utils/api';
import { useRouter } from 'next/navigation';

const AuthContext = createContext({});

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Fetch user data from API
  const fetchUserData = async (token) => {
    try {
      api.defaults.headers.Authorization = `Bearer ${token}`;
      const response = await api.get('/auth/me');
      return response.data;
    } catch (error) {
      console.error('Error fetching user data:', error);
      localStorage.removeItem('token');
      delete api.defaults.headers.Authorization;
      return null;
    }
  };

  // Initialize auth on page load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const token = localStorage.getItem('token');
        if (token) {
          const userData = await fetchUserData(token);
          if (userData) {
            setUser(userData);
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem('token');
      } finally {
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (identifier, password, loginType = 'email') => {
    try {
      // Prepare login data based on login type
      const loginData = {
        password,
        loginType
      };

      // Set the appropriate field based on login type
      if (loginType === 'email') {
        loginData.email = identifier;
      } else {
        loginData.username = identifier;
      }

      const response = await api.post('/auth/login', loginData);
      const { token, user: userData } = response.data;

      localStorage.setItem('token', token);
      api.defaults.headers.Authorization = `Bearer ${token}`;

      // Set user data including role information
      setUser({
        ...userData,
        // Ensure role is properly set, defaulting to EMPLOYEE if not present
        role: userData.role || (userData.isClient ? 'CLIENT' : 'EMPLOYEE')
      });

      router.push('/dashboard');
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      const errorMessage = error.response?.data?.message || 'Erro ao fazer login';
      throw new Error(errorMessage);
    }
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('token');
    delete api.defaults.headers.Authorization;
    setUser(null);
    router.push('/login');
  };

  // Check if user has a specific role
  const hasRole = (role) => {
    if (!user) return false;
    if (Array.isArray(role)) {
      return role.includes(user.role);
    }
    return user.role === role;
  };

  // Check if user is a system admin
  const isSystemAdmin = () => {
    return hasRole('SYSTEM_ADMIN');
  };

  // Check if user is a company admin
  const isCompanyAdmin = () => {
    return hasRole('COMPANY_ADMIN');
  };

  // Check if user is an employee
  const isEmployee = () => {
    return hasRole('EMPLOYEE');
  };

  // Check if user belongs to a specific company
  const belongsToCompany = (companyId) => {
    if (!user || !user.companyId) return false;
    return user.companyId === companyId;
  };

  // Check if user has specific module
  const hasModule = (moduleId) => {
    if (!user || !user.modules) return false;
    return user.modules.includes(moduleId);
  };

  // Atualizar informações do usuário (ex: após atualização de perfil)
  const refreshUserData = async () => {
    try {
      const token = localStorage.getItem('token');
      if (token) {
        const userData = await fetchUserData(token);
        if (userData) {
          setUser(userData);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Error refreshing user data:', error);
      return false;
    }
  };

  // Obter o token atual do localStorage
  const getToken = () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('token');
    }
    return null;
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        token: getToken(),
        login,
        logout,
        loading,
        hasRole,
        isSystemAdmin,
        isCompanyAdmin,
        isEmployee,
        belongsToCompany,
        hasModule,
        refreshUserData
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};