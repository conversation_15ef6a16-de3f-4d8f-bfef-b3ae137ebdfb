'use client';

import React from 'react';
import { Moon, Sun } from 'lucide-react';

export const TimeIcon = () => {
  const hour = new Date().getHours();
  if (hour < 6) return <Moon className="text-indigo-400 dark:text-indigo-300" size={24} />;
  if (hour < 12) return <Sun className="text-amber-400 dark:text-amber-300" size={24} />;
  if (hour < 18) return <Sun className="text-orange-400 dark:text-orange-300" size={24} />;
  return <Moon className="text-indigo-400 dark:text-indigo-300" size={24} />;
};

export default TimeIcon;