import React from 'react';
import { Award } from 'lucide-react';
import ModuleSelect from '../../../components/ui/ModuleSelect';

const SpecialtySelector = ({ formData, setFormData, specialties }) => {
  return (
    <div>
      <label className="block text-sm font-semibold text-neutral-700 dark:text-neutral-300 flex items-center gap-2 mb-1">
        <Award className="w-4 h-4" />
        Especialidade
      </label>
      <ModuleSelect
        moduleColor="scheduler"
        value={formData.specialty}
        onChange={(e) =>
          setFormData({ ...formData, specialty: e.target.value })
        }
        placeholder="Selecione uma especialidade"
      >
        <option value="">Todas as especialidades</option>
        {specialties.map((specialty) => (
          <option key={specialty} value={specialty}>
            {specialty}
          </option>
        ))}
      </ModuleSelect>
    </div>
  );
};

export default SpecialtySelector;