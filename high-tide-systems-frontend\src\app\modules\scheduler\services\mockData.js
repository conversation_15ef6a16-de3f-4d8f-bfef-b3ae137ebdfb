// app/modules/scheduler/services/mockData.js
export const mockDoctors = [
    { value: '1', label: 'Dr. <PERSON>' },
    { value: '2', label: '<PERSON><PERSON><PERSON> <PERSON>' },
    { value: '3', label: '<PERSON><PERSON> <PERSON>' },
    { value: '4', label: '<PERSON><PERSON><PERSON>' },
    { value: '5', label: '<PERSON>. <PERSON>' }
  ];
  
  export const mockPatients = [
    { value: '1', label: '<PERSON>' },
    { value: '2', label: '<PERSON>' },
    { value: '3', label: '<PERSON>' },
    { value: '4', label: 'Fernanda <PERSON>' },
    { value: '5', label: '<PERSON>' },
    { value: '6', label: '<PERSON>' },
    { value: '7', label: '<PERSON>' },
    { value: '8', label: '<PERSON>' }
  ];
  
  export const mockAppointments = [
    {
      id: '1',
      title: '<PERSON>',
      startTime: '2025-01-28T09:00:00',
      endTime: '2025-01-28T10:00:00',
      doctorId: '1',
      patientId: '1',
      type: 'CONSULTATION',
      location: 'Sala 101',
      status: 'CONFIRMED',
      description: 'Consulta de rotina'
    },
    {
      id: '2',
      title: '<PERSON>',
      startTime: '2025-01-28T14:00:00',
      endTime: '2025-01-28T15:00:00',
      doctorId: '2',
      patientId: '2',
      type: 'RETURN',
      location: 'Sala 102',
      status: 'WAITING_CONFIRMATION',
      description: 'Retorno pós exames'
    },
    {
      id: '3',
      title: 'Roberto Almeida',
      startTime: '2025-01-29T10:00:00',
      endTime: '2025-01-29T11:00:00',
      doctorId: '3',
      patientId: '3',
      type: 'EXAM',
      location: 'Sala 103',
      status: 'CONFIRMED',
      description: 'Exame de sangue'
    }
  ];