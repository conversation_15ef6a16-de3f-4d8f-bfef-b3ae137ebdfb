const multer = require("multer");
const path = require("path");
const crypto = require("crypto");
const fs = require("fs");

// Caminho base para uploads
const UPLOAD_PATH = process.env.NODE_ENV === 'production'
  ? path.resolve("/tmp/uploads")
  : path.resolve("./uploads");

console.log('Caminho base para uploads:', UPLOAD_PATH);

// Garante que o diretório de uploads existe
const ensureUploadDirectoryExists = () => {
  const directories = [
    UPLOAD_PATH,
    path.join(UPLOAD_PATH, "profile-images")
  ];

  directories.forEach(dir => {
    console.log(`Verificando se o diretório existe: ${dir}`);
    if (!fs.existsSync(dir)) {
      console.log(`Criando diretório: ${dir}`);
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Diretório criado com sucesso: ${dir}`);
    } else {
      console.log(`Diretório já existe: ${dir}`);
    }
  });
};

// Cria os diretórios necessários
console.log('Garantindo que os diretórios de upload existam...');
ensureUploadDirectoryExists();
console.log('Diretórios de upload verificados/criados com sucesso');

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    console.log('Configurando destino para upload de arquivo');
    console.log('Req params:', req.params);
    console.log('Req user:', req.user ? 'Presente' : 'Ausente');

    // Usar caminho absoluto para o diretório de uploads
    const profileImagesPath = path.join(UPLOAD_PATH, "profile-images");
    console.log('Diretório de destino:', profileImagesPath);

    // Cria diretório específico para imagens de perfil se não existir
    try {
      if (!fs.existsSync(profileImagesPath)) {
        console.log('Diretório não existe, criando...');
        fs.mkdirSync(profileImagesPath, { recursive: true });
        console.log('Diretório criado com sucesso');
      } else {
        console.log('Diretório já existe');
      }

      // Verificar permissões de escrita
      fs.accessSync(profileImagesPath, fs.constants.W_OK);
      console.log('Diretório tem permissões de escrita');
    } catch (error) {
      console.error('Erro ao verificar/criar diretório:', error);
    }

    console.log('Definindo destino do arquivo:', profileImagesPath);
    cb(null, profileImagesPath);
  },
  filename: (req, file, cb) => {
    console.log('Gerando nome de arquivo');
    console.log('Arquivo original:', file.originalname);
    console.log('Params:', req.params);

    // Usar um nome previsível para facilitar o acesso posterior
    const personId = req.params.id || 'new';
    const timestamp = Date.now();
    const extension = path.extname(file.originalname) || '.jpg';

    // Nome do arquivo: profile-{personId}-{timestamp}.{extension}
    const filename = `profile-${personId}-${timestamp}${extension}`;
    console.log('Nome de arquivo gerado:', filename);

    cb(null, filename);
  },
});

const fileFilter = (req, file, cb) => {
  console.log('Verificando tipo de arquivo');
  console.log('Tipo MIME:', file.mimetype);

  // Aceita apenas arquivos de imagem
  if (file.mimetype.startsWith('image/')) {
    console.log('Arquivo é uma imagem, aceitando');
    cb(null, true);
  } else {
    console.log('Arquivo não é uma imagem, rejeitando');
    cb(new Error("Apenas imagens são permitidas"));
  }
};

console.log('Configurando middleware de upload de imagem de perfil');
const profileImageUpload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 2 * 1024 * 1024, // Limite de 2MB
  },
});
console.log('Middleware de upload de imagem de perfil configurado com sucesso');

module.exports = profileImageUpload;
