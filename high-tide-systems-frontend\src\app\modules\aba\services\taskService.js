// src/app/modules/aba/services/taskService.js
import { api } from "@/utils/api";

export const taskService = {
  // Get tasks with optional filters
  getTasks: async (filters = {}) => {
    const { page = 1, limit = 10, search = "", evaluationId, skillId, levelId } = filters;

    try {
      console.log("taskService.getTasks - Enviando requisição com parâmetros:", {
        page,
        limit,
        search: search || undefined,
        evaluationId: evaluationId || undefined,
        skillId: skillId || undefined,
        levelId: levelId || undefined
      });

      const response = await api.get("/aba/tasks", {
        params: {
          page,
          limit,
          search: search || undefined,
          evaluationId: evaluationId || undefined,
          skillId: skillId || undefined,
          levelId: levelId || undefined
        }
      });

      console.log("taskService.getTasks - Resposta da API:", response.data);

      // Verificar a estrutura da resposta
      const items = response.data.tasks || response.data.items || [];
      const total = response.data.total || 0;

      console.log("taskService.getTasks - Itens extraídos:", items);

      return {
        items: items,
        total: total,
        page: response.data.page || 1,
        limit: response.data.limit || 10,
        pages: response.data.pages || 1
      };
    } catch (error) {
      console.error("Error fetching tasks:", error);
      throw error;
    }
  },

  // Get a single task by ID
  getTask: async (id) => {
    try {
      const response = await api.get(`/aba/tasks/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching task ${id}:`, error);
      throw error;
    }
  },

  // Create a new task
  createTask: async (taskData) => {
    try {
      const response = await api.post("/aba/tasks", taskData);
      return response.data;
    } catch (error) {
      console.error("Error creating task:", error);
      throw error;
    }
  },

  // Update an existing task
  updateTask: async (id, taskData) => {
    try {
      const response = await api.put(`/aba/tasks/${id}`, taskData);
      return response.data;
    } catch (error) {
      console.error(`Error updating task ${id}:`, error);
      throw error;
    }
  },

  // Delete a task
  deleteTask: async (id) => {
    try {
      const response = await api.delete(`/aba/tasks/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting task ${id}:`, error);
      throw error;
    }
  }
};

export default taskService;
