// tests/controllers/user-controller-test.js
const axios = require('axios');
require('dotenv').config();

// Configuração
const API_URL = process.env.API_URL || 'http://localhost:5000';

// Token de teste para autenticação
const TEST_TOKEN = 'TEST_TOKEN_00000000-0000-0000-0000-000000000001';

// Configuração do cliente axios
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Authorization': `Bearer ${TEST_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

// Dados para testes
const testUser = {
  login: `test_user_${Date.now()}`,
  email: `test_user_${Date.now()}@example.com`,
  fullName: 'Test User',
  password: 'Test@123',
  role: 'EMPLOYEE',
  modules: ['BASIC']
};

// Variáveis para armazenar IDs criados durante os testes
let userId;

// Função principal de teste
async function runTests() {
  console.log('Iniciando testes do controlador de usuários...');
  
  try {
    // Teste 1: Listar usuários
    console.log('\n1. Listando usuários...');
    const listResponse = await api.get('/users');
    
    if (listResponse.status === 200) {
      console.log(`✅ ${listResponse.data.length} usuários encontrados`);
    } else {
      console.log('❌ Falha ao listar usuários');
    }
    
    // Teste 2: Criar um novo usuário
    console.log('\n2. Criando usuário...');
    const createResponse = await api.post('/users', testUser);
    
    if (createResponse.status === 201) {
      console.log('✅ Usuário criado com sucesso!');
      userId = createResponse.data.id;
      console.log(`ID do usuário: ${userId}`);
    } else {
      console.log('❌ Falha ao criar usuário');
      return;
    }
    
    // Teste 3: Obter usuário por ID
    console.log('\n3. Obtendo usuário por ID...');
    const getResponse = await api.get(`/users/${userId}`);
    
    if (getResponse.status === 200) {
      console.log('✅ Usuário obtido com sucesso!');
      console.log(`Nome: ${getResponse.data.fullName}`);
      console.log(`Email: ${getResponse.data.email}`);
    } else {
      console.log('❌ Falha ao obter usuário');
    }
    
    // Teste 4: Atualizar usuário
    console.log('\n4. Atualizando usuário...');
    const updateResponse = await api.put(`/users/${userId}`, {
      fullName: 'Test User Updated'
    });
    
    if (updateResponse.status === 200) {
      console.log('✅ Usuário atualizado com sucesso!');
      console.log(`Novo nome: ${updateResponse.data.fullName}`);
    } else {
      console.log('❌ Falha ao atualizar usuário');
    }
    
    // Teste 5: Atualizar módulos do usuário
    console.log('\n5. Atualizando módulos do usuário...');
    const updateModulesResponse = await api.patch(`/users/${userId}/modules`, {
      modules: ['BASIC', 'SCHEDULING']
    });
    
    if (updateModulesResponse.status === 200) {
      console.log('✅ Módulos do usuário atualizados com sucesso!');
      console.log(`Módulos: ${updateModulesResponse.data.modules.join(', ')}`);
    } else {
      console.log('❌ Falha ao atualizar módulos do usuário');
    }
    
    // Teste 6: Desativar usuário
    console.log('\n6. Desativando usuário...');
    const toggleStatusResponse = await api.patch(`/users/${userId}/status`, {
      active: false
    });
    
    if (toggleStatusResponse.status === 200) {
      console.log('✅ Status do usuário alterado com sucesso!');
      console.log(`Status: ${toggleStatusResponse.data.active ? 'Ativo' : 'Inativo'}`);
    } else {
      console.log('❌ Falha ao alterar status do usuário');
    }
    
    // Teste 7: Excluir usuário
    console.log('\n7. Excluindo usuário...');
    const deleteResponse = await api.delete(`/users/${userId}`);
    
    if (deleteResponse.status === 200) {
      console.log('✅ Usuário excluído com sucesso!');
    } else {
      console.log('❌ Falha ao excluir usuário');
    }
    
    console.log('\n✅ Todos os testes do controlador de usuários concluídos com sucesso!');
    
  } catch (error) {
    console.error('\n❌ Erro durante os testes:', error.message);
    if (error.response) {
      console.error('Detalhes do erro:', {
        status: error.response.status,
        data: error.response.data
      });
    }
  }
}

// Executar os testes
runTests();
