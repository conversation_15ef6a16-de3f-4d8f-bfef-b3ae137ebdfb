
-- Script para adicionar convênios de saúde para todas as empresas
-- Este script adiciona entre 4 a 10 convênios aleatórios para cada empresa

-- Função para gerar UUID v4 (se não existir)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'uuid_generate_v4') THEN
        CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
    END IF;
END
$$;

-- Adicionar convênios para cada empresa
DO $$
DECLARE
    company_record RECORD;
    insurance_name TEXT;
    insurance_exists BOOLEAN;
    num_insurances INTEGER;
    i INTEGER;
    random_index INTEGER;
    used_names TEXT[];
BEGIN
    -- Para cada empresa
    FOR company_record IN SELECT id, name FROM "Company" LOOP
        RAISE NOTICE 'Processando empresa: % (ID: %)', company_record.name, company_record.id;

        -- Determinar quantos convênios criar (entre 4 e 10)
        num_insurances := floor(random() * 7 + 4);
        RAISE NOTICE 'Criando % convênios para a empresa %', num_insurances, company_record.name;

        -- Inicializar array de nomes usados
        used_names := '{}';

        -- Criar os convênios
        FOR i IN 1..num_insurances LOOP
            -- Selecionar um nome de convênio aleatório
            random_index := floor(random() * 20 + 1);
            insurance_name := CASE random_index
                WHEN 1 THEN 'Amil'
                WHEN 2 THEN 'Bradesco Saúde'
                WHEN 3 THEN 'SulAmérica'
                WHEN 4 THEN 'Unimed'
                WHEN 5 THEN 'Porto Seguro Saúde'
                WHEN 6 THEN 'NotreDame Intermédica'
                WHEN 7 THEN 'Hapvida'
                WHEN 8 THEN 'Golden Cross'
                WHEN 9 THEN 'Prevent Senior'
                WHEN 10 THEN 'São Francisco Saúde'
                WHEN 11 THEN 'Mediservice'
                WHEN 12 THEN 'Omint'
                WHEN 13 THEN 'Care Plus'
                WHEN 14 THEN 'Allianz Saúde'
                WHEN 15 THEN 'Amil One'
                WHEN 16 THEN 'Assim Saúde'
                WHEN 17 THEN 'Biovida Saúde'
                WHEN 18 THEN 'Central Nacional Unimed'
                WHEN 19 THEN 'Economus'
                WHEN 20 THEN 'Gama Saúde'
                ELSE 'Convênio Padrão'
            END;

            -- Verificar se o convênio já existe para esta empresa
            SELECT EXISTS (
                SELECT 1 FROM "Insurance"
                WHERE name = insurance_name AND "companyId" = company_record.id
            ) INTO insurance_exists;

            -- Se não existir, criar o convênio
            IF NOT insurance_exists THEN
                INSERT INTO "Insurance" (id, name, "companyId")
                VALUES (uuid_generate_v4(), insurance_name, company_record.id);
                RAISE NOTICE '✅ Convênio criado: % para empresa %', insurance_name, company_record.name;
            ELSE
                RAISE NOTICE 'Convênio % já existe para a empresa %. Pulando...', insurance_name, company_record.name;
            END IF;
        END LOOP;
    END LOOP;

    RAISE NOTICE 'Criação de convênios concluída com sucesso!';
END $$;
