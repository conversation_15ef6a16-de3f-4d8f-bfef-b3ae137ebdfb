// src/app/modules/aba/services/curriculumProgramService.js
import { api } from "@/utils/api";
import { extractData, extractEntity } from "@/utils/apiResponseAdapter";

// Definir o serviço
const curriculumProgramService = {
  getProgramsByFolder: async (folderId, params = {}) => {
    try {
      const response = await api.get(`/aba/curriculum-folders/${folderId}/programs`, { params });
      return extractData(response.data, 'programs');
    } catch (error) {
      console.error(`Error fetching programs for folder ${folderId}:`, error);
      throw error;
    }
  },

  createProgram: async (folderId, programData) => {
    try {
      const response = await api.post(`/aba/curriculum-folders/${folderId}/programs`, programData);
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error creating program for folder ${folderId}:`, error);
      throw error;
    }
  },

  getProgram: async (programId) => {
    try {
      // Primeiro tenta buscar como um programa de catálogo
      try {
        const response = await api.get(`/aba/programs/${programId}`);
        return extractEntity(response.data);
      } catch (catalogError) {
        // Se não encontrar como programa de catálogo, tenta buscar como programa de pasta curricular
        if (catalogError.response && catalogError.response.status === 404) {
          console.log(`Program ${programId} not found in catalog, trying as curriculum folder program...`);

          // Precisamos encontrar a pasta curricular que contém este programa
          // Como não temos um endpoint direto, vamos ter que buscar em todas as pastas
          // Isso é uma solução temporária até termos um endpoint melhor

          // Buscar todas as pastas curriculares
          const foldersResponse = await api.get('/aba/curriculum-folders');
          const folders = foldersResponse.data.curriculumFolders || [];

          // Para cada pasta, verificar se contém o programa
          for (const folder of folders) {
            try {
              const folderProgramsResponse = await api.get(`/aba/curriculum-folders/${folder.id}/programs`);
              const folderPrograms = folderProgramsResponse.data.programs || [];

              // Procurar o programa na pasta
              const program = folderPrograms.find(p => p.id === programId);
              if (program) {
                return program;
              }
            } catch (folderError) {
              console.warn(`Error checking folder ${folder.id} for program ${programId}:`, folderError);
              // Continuar para a próxima pasta
            }
          }

          // Se chegou aqui, não encontrou o programa em nenhuma pasta
          throw new Error(`Program ${programId} not found in catalog or any curriculum folder`);
        }

        // Se for outro erro, propagar
        throw catalogError;
      }
    } catch (error) {
      console.error(`Error fetching program ${programId}:`, error);
      throw error;
    }
  },

  updateProgram: async (programId, programData) => {
    try {
      const response = await api.put(`/aba/programs/${programId}`, programData);
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error updating program ${programId}:`, error);
      throw error;
    }
  },

  deleteProgram: async (programId) => {
    try {
      const response = await api.delete(`/aba/programs/${programId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting program ${programId}:`, error);
      throw error;
    }
  },

  deleteCurriculumFolderProgram: async (folderId, programId) => {
    try {
      const response = await api.delete(`/aba/curriculum-folders/${folderId}/programs/${programId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting curriculum folder program ${programId}:`, error);
      throw error;
    }
  },

  updateCurriculumFolderProgram: async (folderId, programId, programData) => {
    try {
      const response = await api.put(`/aba/curriculum-folders/${folderId}/programs/${programId}`, programData);
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error updating curriculum folder program ${programId}:`, error);
      throw error;
    }
  },

  getProgramTypes: () => {
    return [
      { value: 'communication', label: 'Comunicação' },
      { value: 'social', label: 'Habilidades Sociais' },
      { value: 'autonomy', label: 'Autonomia' },
      { value: 'cognitive', label: 'Cognitivo' },
      { value: 'motor', label: 'Motor' },
      { value: 'academic', label: 'Acadêmico' },
      { value: 'behavior', label: 'Comportamento' },
      { value: 'other', label: 'Outro' }
    ];
  },

  getProgramTypeLabel: (type) => {
    const types = {
      'communication': 'Comunicação',
      'social': 'Habilidades Sociais',
      'autonomy': 'Autonomia',
      'cognitive': 'Cognitivo',
      'motor': 'Motor',
      'academic': 'Acadêmico',
      'behavior': 'Comportamento',
      'other': 'Outro'
    };
    return types[type] || type;
  },

  updateProgramStatus: async (programId, status, folderId = null) => {
    try {
      // Primeiro, obter os detalhes do programa atual
      let program;
      try {
        program = await curriculumProgramService.getProgram(programId);
      } catch (error) {
        console.error(`Error fetching program ${programId}:`, error);
        // Se o programa não for encontrado, criar um programa genérico
        if (error.response && error.response.status === 404) {
          console.warn(`Program with ID ${programId} not found, creating a generic program`);
          program = {
            id: programId,
            name: "Programa não encontrado",
            type: "other",
            status: status
          };
        } else {
          // Se for outro erro, propagar
          throw error;
        }
      }

      if (!program) {
        throw new Error(`Program with ID ${programId} not found`);
      }

      // Atualizar apenas o status do programa
      const programData = {
        ...program,
        status: status
      };

      // Verificar se é um programa de pasta curricular (tem curriculumFolderId)
      if (program.curriculumFolderId || folderId) {
        const folderIdToUse = program.curriculumFolderId || folderId;
        try {
          const response = await api.put(`/aba/curriculum-folders/${folderIdToUse}/programs/${programId}`, { status });
          return extractEntity(response.data);
        } catch (error) {
          console.warn(`Error updating curriculum folder program status: ${error.message}`);
          // Continuar com o método antigo como fallback
        }
      }

      // Método antigo como fallback
      try {
        const response = await api.put(`/aba/programs/${programId}`, programData);
        return extractEntity(response.data);
      } catch (error) {
        // Se o programa não for encontrado no backend, tentar atualizar localmente
        if (error.response && error.response.status === 404) {
          console.warn(`Program with ID ${programId} not found in backend, updating locally`);
          return programData;
        }
        throw error;
      }
    } catch (error) {
      console.error(`Error updating status for program ${programId}:`, error);
      throw error;
    }
  },

  addExistingProgram: async (folderId, programId, status = 'unallocated') => {
    try {
      // Usar o novo endpoint dedicado para adicionar programas existentes
      const response = await api.post(`/aba/curriculum-folders/${folderId}/programs/add`, {
        programId,
        status
      });
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error adding existing program ${programId} to folder ${folderId}:`, error);

      // Se o endpoint não estiver disponível ou retornar erro, tentar o método antigo como fallback
      if (error.response && (error.response.status === 404 || error.response.status === 500)) {
        console.warn('Falling back to legacy method for adding program');
        return curriculumProgramService.addExistingProgramLegacy(folderId, programId, status);
      }

      throw error;
    }
  },

  // Método legado para adicionar programas existentes (mantido para compatibilidade)
  addExistingProgramLegacy: async (folderId, programId, status = 'unallocated') => {
    try {
      // Primeiro, obter os detalhes do programa
      let program;
      try {
        program = await curriculumProgramService.getProgram(programId);
      } catch (error) {
        console.error(`Error fetching program ${programId}:`, error);
        // Se o programa não for encontrado, criar um programa genérico
        if (error.response && error.response.status === 404) {
          console.warn(`Program with ID ${programId} not found, creating a generic program`);
          program = {
            name: "Programa não encontrado",
            type: "other",
            protocol: null,
            skill: null,
            milestone: null,
            teachingType: null,
            targetsPerSession: 1,
            attemptsPerTarget: 1,
            teachingProcedure: "",
            instruction: "",
            objective: "",
            promptStep: "",
            correctionProcedure: "",
            learningCriteria: "",
            materials: "",
            notes: "Programa original não encontrado",
          };
        } else {
          // Se for outro erro, propagar
          throw error;
        }
      }

      if (!program) {
        throw new Error(`Program with ID ${programId} not found`);
      }

      // Criar um novo programa na pasta curricular com base no programa existente
      const programData = {
        name: program.name,
        type: program.type,
        protocol: program.protocol,
        skill: program.skill,
        milestone: program.milestone,
        teachingType: program.teachingType,
        targetsPerSession: program.targetsPerSession,
        attemptsPerTarget: program.attemptsPerTarget,
        teachingProcedure: program.teachingProcedure,
        instruction: program.instruction,
        objective: program.objective,
        promptStep: program.promptStep,
        correctionProcedure: program.correctionProcedure,
        learningCriteria: program.learningCriteria,
        materials: program.materials,
        notes: program.notes,
        status: status,
        originalProgramId: programId // Referência ao programa original
      };

      const response = await api.post(`/aba/curriculum-folders/${folderId}/programs`, programData);
      return extractEntity(response.data);
    } catch (error) {
      console.error(`Error adding existing program ${programId} to folder ${folderId} (legacy method):`, error);
      throw error;
    }
  }
};

export default curriculumProgramService;
