// src/routes/chat/conversationRoutes.js
const express = require('express');
const router = express.Router();
const conversationController = require('../../controllers/chat/conversationController');
const messageController = require('../../controllers/chat/messageController');
const { cacheMiddleware, clearCacheMiddleware } = require('../../middlewares/cache');

// Rotas de conversas
router.get('/', cacheMiddleware('chat:conversations', 60), conversationController.getUserConversations);
router.post('/', clearCacheMiddleware('chat:conversations'), conversationController.createConversation);
router.get('/:id', cacheMiddleware('chat:conversation', 60), conversationController.getConversationById);
router.put('/:id', clearCacheMiddleware('chat:conversation'), conversationController.updateConversation);

// Rotas de participantes
router.post('/:id/participants', clearCacheMiddleware('chat:conversation'), conversationController.addParticipant);
router.delete('/:id/participants/:participantId', clearCacheMiddleware('chat:conversation'), conversationController.removeParticipant);

// Rotas de mensagens de uma conversa
router.get('/:conversationId/messages', messageController.getConversationMessages);
router.post('/:conversationId/messages', messageController.createMessage);

module.exports = router;
